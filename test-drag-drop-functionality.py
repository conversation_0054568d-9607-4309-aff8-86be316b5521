#!/usr/bin/env python3
"""
Test Drag-Drop PDF Functionality in Chrome Extension
Tests the current state of PDF processing and identifies issues
"""

import os
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import <PERSON><PERSON>hai<PERSON>

def setup_chrome_driver():
    """Setup Chrome driver with extension loaded"""
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--allow-running-insecure-content")
    
    # Load extension
    extension_path = "/home/<USER>/W/cloudforge/accounting-chrome-extension/dist/dev"
    chrome_options.add_argument(f"--load-extension={extension_path}")
    
    # Use Chrome 135 binary
    chrome_binary = "/home/<USER>/W/cloudforge/accounting-chrome-extension/tests/selenium/chrome-135/chrome-linux64/chrome"
    chrome_options.binary_location = chrome_binary
    
    driver = webdriver.Chrome(options=chrome_options)
    return driver

def test_drag_drop_functionality():
    """Test drag-drop PDF functionality"""
    driver = setup_chrome_driver()
    
    try:
        print("🧪 DRAG-DROP PDF FUNCTIONALITY TEST")
        print("=" * 50)
        
        # Navigate to extension popup
        driver.get("chrome-extension://apbmmkbgaicmlnhebmbpkakfnlajgfij/popup.html")
        time.sleep(3)
        
        print("✅ Extension popup loaded")
        
        # Navigate to upload page
        try:
            upload_tab = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Upload') or contains(@class, 'upload')]"))
            )
            upload_tab.click()
            time.sleep(2)
            print("✅ Upload tab clicked")
        except Exception as e:
            print(f"⚠️ Could not find upload tab, checking current page: {e}")
        
        # Look for drag-drop area
        try:
            drag_drop_area = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//*[contains(@class, 'dropzone') or contains(text(), 'drag') or contains(text(), 'drop')]"))
            )
            print("✅ Drag-drop area found")
            print(f"📝 Drag-drop element: {drag_drop_area.tag_name} with class: {drag_drop_area.get_attribute('class')}")
        except Exception as e:
            print(f"❌ No drag-drop area found: {e}")
            
            # Check what's actually on the page
            page_source = driver.page_source
            print("📄 Current page content (first 1000 chars):")
            print(page_source[:1000])
            return False
        
        # Test file input element
        try:
            file_input = driver.find_element(By.XPATH, "//input[@type='file']")
            print("✅ File input element found")
            
            # Test with a sample PDF
            sample_pdf = "/home/<USER>/W/cloudforge/accounting-chrome-extension/data/samples/invoices/input/10026_M_11_24.pdf"
            if os.path.exists(sample_pdf):
                print(f"📄 Testing with sample PDF: {sample_pdf}")
                
                # Upload file
                file_input.send_keys(sample_pdf)
                time.sleep(3)
                
                print("✅ File uploaded via input element")
                
                # Check for processing indicators
                try:
                    processing_indicator = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'Processing') or contains(text(), 'Uploading') or contains(@class, 'progress')]"))
                    )
                    print("✅ Processing indicator found")
                except Exception:
                    print("⚠️ No processing indicator found")
                
                # Wait for processing to complete
                time.sleep(10)
                
                # Check for results
                try:
                    results_area = driver.find_element(By.XPATH, "//*[contains(text(), 'Invoice') or contains(text(), 'Result') or contains(@class, 'result')]")
                    print("✅ Results area found")
                except Exception:
                    print("⚠️ No results area found")
                
                # Check console for errors
                logs = driver.get_log('browser')
                errors = [log for log in logs if log['level'] == 'SEVERE']
                if errors:
                    print(f"❌ Console errors found: {len(errors)}")
                    for error in errors[:3]:  # Show first 3 errors
                        print(f"   - {error['message']}")
                else:
                    print("✅ No console errors")
                
            else:
                print(f"❌ Sample PDF not found: {sample_pdf}")
                return False
                
        except Exception as e:
            print(f"❌ File input element not found: {e}")
            return False
        
        print("\n🎯 DRAG-DROP TEST SUMMARY:")
        print("✅ Extension loads correctly")
        print("✅ Upload interface accessible")
        print("✅ File input element functional")
        print("✅ PDF processing initiated")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False
        
    finally:
        driver.quit()

def test_pdf_processing_pipeline():
    """Test the PDF processing pipeline specifically"""
    driver = setup_chrome_driver()
    
    try:
        print("\n🔧 PDF PROCESSING PIPELINE TEST")
        print("=" * 50)
        
        # Navigate to extension popup
        driver.get("chrome-extension://apbmmkbgaicmlnhebmbpkakfnlajgfij/popup.html")
        time.sleep(3)
        
        # Execute JavaScript to test PDF processing directly
        test_script = """
        // Test PDF processing capabilities
        const testResults = {
            pdfJsAvailable: typeof pdfjsLib !== 'undefined',
            tesseractAvailable: typeof Tesseract !== 'undefined',
            documentProcessingService: typeof documentProcessingService !== 'undefined',
            dragDropComponent: document.querySelector('[class*="dropzone"]') !== null,
            fileInput: document.querySelector('input[type="file"]') !== null
        };
        
        return testResults;
        """
        
        results = driver.execute_script(test_script)
        
        print("📊 PDF Processing Capabilities:")
        for key, value in results.items():
            status = "✅" if value else "❌"
            print(f"   {status} {key}: {value}")
        
        # Test actual PDF processing if possible
        if results.get('fileInput'):
            print("\n🧪 Testing actual PDF processing...")
            
            # Inject test PDF processing
            pdf_test_script = """
            // Test PDF processing with a mock file
            const testPDFProcessing = async () => {
                try {
                    // Check if DocumentProcessingService is available
                    if (typeof documentProcessingService !== 'undefined') {
                        console.log('DocumentProcessingService is available');
                        return { success: true, message: 'Service available' };
                    } else {
                        console.log('DocumentProcessingService not available');
                        return { success: false, message: 'Service not available' };
                    }
                } catch (error) {
                    console.error('PDF processing test error:', error);
                    return { success: false, message: error.message };
                }
            };
            
            return testPDFProcessing();
            """
            
            pdf_result = driver.execute_script(pdf_test_script)
            print(f"📄 PDF Processing Test: {pdf_result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Pipeline test failed: {e}")
        return False
        
    finally:
        driver.quit()

if __name__ == "__main__":
    print("🚀 Starting Chrome Extension PDF Processing Tests")
    print("=" * 60)
    
    # Test 1: Drag-Drop Functionality
    drag_drop_success = test_drag_drop_functionality()
    
    # Test 2: PDF Processing Pipeline
    pipeline_success = test_pdf_processing_pipeline()
    
    print("\n📊 FINAL TEST RESULTS:")
    print("=" * 30)
    print(f"✅ Drag-Drop Test: {'PASSED' if drag_drop_success else 'FAILED'}")
    print(f"✅ Pipeline Test: {'PASSED' if pipeline_success else 'FAILED'}")
    
    if drag_drop_success and pipeline_success:
        print("\n🎉 All tests passed! PDF processing is functional.")
    else:
        print("\n⚠️ Some tests failed. Issues identified for enhancement.")
