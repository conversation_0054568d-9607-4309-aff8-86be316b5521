# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production builds
dist/
build/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Coverage reports
coverage/
.nyc_output/

# Test results
test-results/
tests/e2e/test-results/
tests/e2e/reports/
tests/visual/screenshots/
tests/visual/reports/
tests/visual/venv/
tests/selenium/test_results.json

# Temporary files
tmp/
temp/
.tmp/

# Package manager files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
.pytest_cache/

# Chrome extension development
*.crx
*.pem
key.pem

# Backup files
*.bak
*.backup
*~

# Editor directories and files
.vscode/
!.vscode/extensions.json
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

.history/