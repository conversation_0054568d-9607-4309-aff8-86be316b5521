/**
 * Default Environment Variables
 *
 * Single source of truth for all default environment variables
 * used across the application. This eliminates the 280+ lines of
 * duplicate defaults found in multiple files.
 *
 * CONSOLIDATION: Replaces duplicate defaults in:
 * - src/utils/EnvLoader.js
 * - src/utils/ExtensionEnvironmentLoader.js
 * - src/services/EnvironmentConfigService.js
 * - src/services/ConfigurationSourceManager.js
 */

export const DEFAULT_ENV_VARS = {
  // API Keys
  DEEPSEEK_API_KEY: '***********************************',
  DEEPSEEK_API_URL: 'https://api.deepseek.com/v1',
  DEEPSEEK_MODEL: 'deepseek-chat',
  DEEPSEEK_MAX_TOKENS: '4000',
  DEEPSEEK_TEMPERATURE: '0.1',

  // OpenAI Configuration
  OPENAI_API_KEY: '',
  OPENAI_API_URL: 'https://api.openai.com/v1',
  OPENAI_MODEL: 'gpt-4',
  OPENAI_MAX_TOKENS: '4000',
  OPENAI_TEMPERATURE: '0.1',

  // Company Information
  COMPANY_NAME: 'MVAT Solutions',
  COMPANY_LEGAL_NAME: 'MVAT Solutions Sp. z o.o.',
  COMPANY_NIP: '*********0',
  COMPANY_REGON: '*********',
  COMPANY_KRS: '**********',

  // Company Address
  COMPANY_ADDRESS_STREET: 'ul. Przykładowa 123',
  COMPANY_ADDRESS_CITY: 'Warszawa',
  COMPANY_ADDRESS_POSTAL_CODE: '00-001',
  COMPANY_ADDRESS_COUNTRY: 'Poland',
  COMPANY_ADDRESS_COUNTRY_CODE: 'PL',

  // Company Contact
  COMPANY_EMAIL: '<EMAIL>',
  COMPANY_PHONE: '+48 ***********',
  COMPANY_WEBSITE: 'https://mvat-solutions.com',
  COMPANY_SUPPORT_EMAIL: '<EMAIL>',

  // Fakturownia Integration
  FAKTUROWNIA_API_TOKEN: '',
  FAKTUROWNIA_ACCOUNT_URL: '',
  FAKTUROWNIA_DEPARTMENT_ID: '',

  // Infakt Integration
  INFAKT_API_KEY: '',
  INFAKT_COMPANY_ID: '',

  // Application Configuration
  NODE_ENV: 'development',
  APP_VERSION: '1.0.0',
  DEBUG_MODE: 'true',
  LOG_LEVEL: 'info',

  // Feature Flags
  FEATURE_SUBSCRIPTION_SYSTEM: 'true',
  FEATURE_PAYMENT_PROCESSING: 'false',
  FEATURE_ENTERPRISE_FEATURES: 'false',
  FEATURE_RAG_SIMILARITY: 'true',
  FEATURE_DOCUMENT_LINKING: 'true',
  FEATURE_BULK_PROCESSING: 'true',
  FEATURE_EXPORT_FORMATS: 'true',

  // Subscription Configuration
  STARTER_TIER_INVOICE_LIMIT: '10',
  PROFESSIONAL_TIER_INVOICE_LIMIT: '500',
  BUSINESS_TIER_INVOICE_LIMIT: '2000',
  ENTERPRISE_TIER_INVOICE_LIMIT: '-1',

  // Subscription Pricing (in cents)
  PROFESSIONAL_TIER_PRICE_MONTHLY: '2900',
  BUSINESS_TIER_PRICE_MONTHLY: '9900',
  ENTERPRISE_TIER_PRICE_MONTHLY: '29900',

  // Processing Configuration
  OCR_DEFAULT_LANGUAGE: 'pol',
  OCR_CONFIDENCE_THRESHOLD: '0.7',
  PDF_MAX_PAGES: '50',
  PDF_MAX_SIZE_MB: '10',
  IMAGE_MAX_SIZE_MB: '5',
  BATCH_PROCESSING_SIZE: '5',
  PROCESSING_TIMEOUT_SECONDS: '30',

  // AI Processing
  AI_RETRY_ATTEMPTS: '3',
  AI_RETRY_DELAY_MS: '1000',
  AI_MAX_CONCURRENT_REQUESTS: '3',
  AI_RATE_LIMIT_PER_MINUTE: '60',

  // Cache Configuration
  CACHE_ENABLED: 'true',
  CACHE_TTL_HOURS: '24',
  CACHE_MAX_SIZE_MB: '100',
  EMBEDDING_CACHE_SIZE: '1000',

  // Security Configuration
  ENCRYPTION_ENABLED: 'true',
  SESSION_TIMEOUT_MINUTES: '60',
  MAX_LOGIN_ATTEMPTS: '5',
  PASSWORD_MIN_LENGTH: '8',

  // Localization
  DEFAULT_LANGUAGE: 'pl',
  DEFAULT_CURRENCY: 'PLN',
  DEFAULT_DATE_FORMAT: 'DD/MM/YYYY',
  DEFAULT_TIMEZONE: 'Europe/Warsaw',

  // UI Configuration
  DEFAULT_THEME: 'light',
  DEFAULT_GROUP_BY: 'month',
  ITEMS_PER_PAGE: '20',
  MAX_RECENT_FILES: '10',

  // Development Configuration
  DEV_MOCK_API_RESPONSES: 'false',
  DEV_SKIP_VALIDATION: 'false',
  DEV_ENABLE_PERFORMANCE_MONITORING: 'true',
  DEV_LOG_API_REQUESTS: 'true'
};

/**
 * Get default environment variables as object
 * @returns {Object} Default environment variables
 */
export function getDefaultEnvironmentVariables() {
  return { ...DEFAULT_ENV_VARS };
}

/**
 * Get default value for specific environment variable
 * @param {string} key - Environment variable key
 * @param {string} fallback - Fallback value if key not found
 * @returns {string} Default value
 */
export function getDefaultEnvVar(key, fallback = '') {
  return DEFAULT_ENV_VARS[key] || fallback;
}

/**
 * Check if environment variable key is known/valid
 * @param {string} key - Environment variable key
 * @returns {boolean} True if key is known
 */
export function isKnownEnvVar(key) {
  return key in DEFAULT_ENV_VARS;
}

/**
 * Get all API-related environment variables
 * @returns {Object} API environment variables
 */
export function getApiDefaults() {
  const apiKeys = {};
  Object.keys(DEFAULT_ENV_VARS).forEach(key => {
    if (key.includes('API_KEY') || key.includes('API_TOKEN') || key.includes('API_URL')) {
      apiKeys[key] = DEFAULT_ENV_VARS[key];
    }
  });
  return apiKeys;
}

/**
 * Get all company-related environment variables
 * @returns {Object} Company environment variables
 */
export function getCompanyDefaults() {
  const companyVars = {};
  Object.keys(DEFAULT_ENV_VARS).forEach(key => {
    if (key.startsWith('COMPANY_')) {
      companyVars[key] = DEFAULT_ENV_VARS[key];
    }
  });
  return companyVars;
}

/**
 * Get all feature flag environment variables
 * @returns {Object} Feature flag environment variables
 */
export function getFeatureDefaults() {
  const featureVars = {};
  Object.keys(DEFAULT_ENV_VARS).forEach(key => {
    if (key.startsWith('FEATURE_')) {
      featureVars[key] = DEFAULT_ENV_VARS[key];
    }
  });
  return featureVars;
}
