/**
 * File Configuration for MVAT Chrome Extension
 * Centralized configuration for file validation, security, and processing
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-01-27
 */

/**
 * Main file configuration object
 * Contains all settings for file validation, security, and processing
 */
export const FILE_CONFIG = {
  // Supported file types (extensions)
  SUPPORTED_TYPES: [
    'pdf',
    'jpg', 'jpeg', 'png', 'gif', 'bmp',
    'tiff', 'tif',
    'doc', 'docx',
    'xls', 'xlsx',
    'txt', 'csv'
  ],

  // File size limits (in bytes)
  MAX_FILE_SIZE: 15 * 1024 * 1024, // 15MB per file
  MAX_TOTAL_SIZE: 50 * 1024 * 1024, // 50MB total for all files
  MAX_FILES: 10, // Maximum number of files in one upload

  // File name constraints
  MAX_FILENAME_LENGTH: 255,
  MIN_FILENAME_LENGTH: 1,

  // MIME type mappings for validation
  MIME_TYPES: {
    // PDF files
    'pdf': ['application/pdf'],

    // Image files
    'jpg': ['image/jpeg'],
    'jpeg': ['image/jpeg'],
    'png': ['image/png'],
    'gif': ['image/gif'],
    'bmp': ['image/bmp', 'image/x-ms-bmp'],
    'tiff': ['image/tiff'],
    'tif': ['image/tiff'],

    // Microsoft Office documents
    'doc': ['application/msword'],
    'docx': [
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ],
    'xls': ['application/vnd.ms-excel'],
    'xlsx': [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ],

    // Text files
    'txt': ['text/plain'],
    'csv': ['text/csv', 'application/csv', 'text/comma-separated-values']
  },

  // File processing priorities
  PROCESSING_PRIORITY: {
    'pdf': 1, // Highest priority - main document type
    'jpg': 2, // High priority - common invoice format
    'jpeg': 2,
    'png': 2,
    'doc': 3, // Medium priority
    'docx': 3,
    'xls': 3,
    'xlsx': 3,
    'txt': 4, // Lower priority
    'csv': 4,
    'gif': 5, // Lowest priority
    'bmp': 5,
    'tiff': 5,
    'tif': 5
  },

  // Security settings
  SECURITY: {
    // Enable/disable security checks
    ENABLE_SIGNATURE_VALIDATION: true,
    ENABLE_MALWARE_DETECTION: true,
    ENABLE_CONTENT_VALIDATION: true,
    ENABLE_METADATA_CHECKS: true,

    // Security thresholds
    MAX_SUSPICIOUS_PATTERNS: 3,
    QUARANTINE_ON_THREAT: true,

    // Allowed file age (in days)
    MAX_FILE_AGE_DAYS: 365 * 5, // 5 years
    MIN_FILE_AGE_DAYS: -1 // Allow files with future dates (with warning)
  },

  // Processing settings
  PROCESSING: {
    // Chunk size for large file processing (in bytes)
    CHUNK_SIZE: 1024 * 1024, // 1MB chunks

    // Timeout settings (in milliseconds)
    VALIDATION_TIMEOUT: 30000, // 30 seconds
    PROCESSING_TIMEOUT: 300000, // 5 minutes

    // Retry settings
    MAX_RETRIES: 3,
    RETRY_DELAY: 1000, // 1 second

    // Memory limits
    MAX_MEMORY_USAGE: 100 * 1024 * 1024 // 100MB
  },

  // Error messages
  ERROR_MESSAGES: {
    // File type errors
    UNSUPPORTED_TYPE: 'File type not supported. Please upload PDF, image, or document files.',
    INVALID_EXTENSION: 'File must have a valid extension.',
    MIME_TYPE_MISMATCH: 'File content does not match the file extension.',

    // File size errors
    FILE_TOO_LARGE: 'File size exceeds the maximum limit of {maxSize}.',
    FILE_EMPTY: 'File is empty or corrupted.',
    TOTAL_SIZE_EXCEEDED: 'Total file size exceeds the limit of {maxTotalSize}.',
    TOO_MANY_FILES: 'Too many files. Maximum {maxFiles} files allowed.',

    // File name errors
    INVALID_FILENAME: 'File name contains invalid characters.',
    FILENAME_TOO_LONG: 'File name is too long. Maximum {maxLength} characters allowed.',
    FILENAME_EMPTY: 'File name cannot be empty.',

    // Security errors
    SECURITY_THREAT: 'File contains potential security threats and cannot be processed.',
    MALWARE_DETECTED: 'File appears to contain malicious content.',
    INVALID_SIGNATURE: 'File signature does not match the expected format.',
    SUSPICIOUS_CONTENT: 'File contains suspicious patterns.',

    // Content errors
    CORRUPTED_FILE: 'File appears to be corrupted or damaged.',
    INVALID_STRUCTURE: 'File structure is invalid or unsupported.',
    CONTENT_VALIDATION_FAILED: 'File content validation failed.',

    // Processing errors
    PROCESSING_FAILED: 'File processing failed. Please try again.',
    TIMEOUT_ERROR: 'File processing timed out. File may be too large or complex.',
    MEMORY_ERROR: 'Insufficient memory to process the file.',

    // General errors
    UNKNOWN_ERROR: 'An unknown error occurred while processing the file.',
    VALIDATION_ERROR: 'File validation failed.',
    NETWORK_ERROR: 'Network error occurred during file processing.'
  },

  // Warning messages
  WARNING_MESSAGES: {
    LARGE_FILE: 'Large file detected. Processing may take longer.',
    OLD_FILE: 'File is very old and may not process correctly.',
    FUTURE_DATE: 'File has a future modification date.',
    SUSPICIOUS_NAME: 'File name appears suspicious.',
    DUPLICATE_FILE: 'Duplicate file detected.',
    LOW_QUALITY: 'Image quality may be too low for optimal processing.',
    UNSUPPORTED_VERSION: 'File version may not be fully supported.'
  },

  // Success messages
  SUCCESS_MESSAGES: {
    VALIDATION_PASSED: 'File validation completed successfully.',
    UPLOAD_COMPLETE: 'File uploaded successfully.',
    PROCESSING_COMPLETE: 'File processing completed.',
    SECURITY_CLEARED: 'File passed all security checks.'
  },

  // File type specific settings
  TYPE_SPECIFIC: {
    pdf: {
      MAX_PAGES: 100,
      MIN_VERSION: 1.0,
      MAX_VERSION: 2.0,
      ALLOW_ENCRYPTED: false,
      ALLOW_FORMS: true,
      EXTRACT_TEXT: true,
      EXTRACT_IMAGES: true
    },

    images: {
      MIN_WIDTH: 100,
      MIN_HEIGHT: 100,
      MAX_WIDTH: 10000,
      MAX_HEIGHT: 10000,
      MIN_DPI: 72,
      RECOMMENDED_DPI: 300,
      SUPPORTED_COLOR_MODES: ['RGB', 'CMYK', 'Grayscale']
    },

    documents: {
      MAX_PAGES: 50,
      EXTRACT_TEXT: true,
      PRESERVE_FORMATTING: true,
      CONVERT_TO_PDF: false
    }
  },

  // Performance settings
  PERFORMANCE: {
    // Enable/disable performance optimizations
    ENABLE_CACHING: true,
    ENABLE_COMPRESSION: true,
    ENABLE_PARALLEL_PROCESSING: true,

    // Cache settings
    CACHE_SIZE: 50 * 1024 * 1024, // 50MB cache
    CACHE_TTL: 3600000, // 1 hour in milliseconds

    // Compression settings
    COMPRESSION_LEVEL: 6, // 0-9, higher = better compression but slower
    COMPRESSION_THRESHOLD: 1024 * 1024, // 1MB - compress files larger than this

    // Parallel processing
    MAX_CONCURRENT_FILES: 3,
    WORKER_POOL_SIZE: 2
  },

  // Development and debugging
  DEBUG: {
    ENABLE_LOGGING: true,
    LOG_LEVEL: 'info', // 'debug', 'info', 'warn', 'error'
    LOG_VALIDATION_DETAILS: false,
    LOG_SECURITY_CHECKS: false,
    LOG_PERFORMANCE_METRICS: true,
    SAVE_DEBUG_INFO: false
  }
};

/**
 * Get configuration for a specific file type
 * @param {string} fileType - File extension (e.g., 'pdf', 'jpg')
 * @returns {Object} Type-specific configuration
 */
export function getTypeConfig(fileType) {
  const type = fileType.toLowerCase();

  // Map file types to configuration categories
  const typeMapping = {
    'pdf': 'pdf',
    'jpg': 'images',
    'jpeg': 'images',
    'png': 'images',
    'gif': 'images',
    'bmp': 'images',
    'tiff': 'images',
    'tif': 'images',
    'doc': 'documents',
    'docx': 'documents',
    'xls': 'documents',
    'xlsx': 'documents',
    'txt': 'documents',
    'csv': 'documents'
  };

  const category = typeMapping[type];
  return category ? FILE_CONFIG.TYPE_SPECIFIC[category] : {};
}

/**
 * Check if a file type is supported
 * @param {string} fileType - File extension to check
 * @returns {boolean} True if supported
 */
export function isTypeSupported(fileType) {
  return FILE_CONFIG.SUPPORTED_TYPES.includes(fileType.toLowerCase());
}

/**
 * Get the processing priority for a file type
 * @param {string} fileType - File extension
 * @returns {number} Priority level (1 = highest, 5 = lowest)
 */
export function getProcessingPriority(fileType) {
  return FILE_CONFIG.PROCESSING_PRIORITY[fileType.toLowerCase()] || 5;
}

/**
 * Get expected MIME types for a file extension
 * @param {string} fileType - File extension
 * @returns {string[]} Array of expected MIME types
 */
export function getExpectedMimeTypes(fileType) {
  return FILE_CONFIG.MIME_TYPES[fileType.toLowerCase()] || [];
}

/**
 * Format file size for display
 * @param {number} bytes - File size in bytes
 * @returns {string} Formatted file size
 */
export function formatFileSize(bytes) {
  if (bytes === 0) { return '0 Bytes'; }

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Get error message with parameter substitution
 * @param {string} messageKey - Error message key
 * @param {Object} params - Parameters to substitute
 * @returns {string} Formatted error message
 */
export function getErrorMessage(messageKey, params = {}) {
  let message = FILE_CONFIG.ERROR_MESSAGES[messageKey] || FILE_CONFIG.ERROR_MESSAGES.UNKNOWN_ERROR;

  // Substitute parameters
  Object.keys(params).forEach(key => {
    message = message.replace(`{${key}}`, params[key]);
  });

  return message;
}

/**
 * Validate configuration integrity
 * @returns {Object} Validation result
 */
export function validateConfig() {
  const issues = [];

  // Check required properties
  const requiredProps = ['SUPPORTED_TYPES', 'MAX_FILE_SIZE', 'MAX_TOTAL_SIZE'];
  requiredProps.forEach(prop => {
    if (!FILE_CONFIG[prop]) {
      issues.push(`Missing required property: ${prop}`);
    }
  });

  // Check file size limits
  if (FILE_CONFIG.MAX_FILE_SIZE > FILE_CONFIG.MAX_TOTAL_SIZE) {
    issues.push('MAX_FILE_SIZE cannot be larger than MAX_TOTAL_SIZE');
  }

  // Check MIME type mappings
  FILE_CONFIG.SUPPORTED_TYPES.forEach(type => {
    if (!FILE_CONFIG.MIME_TYPES[type]) {
      issues.push(`Missing MIME type mapping for: ${type}`);
    }
  });

  return {
    isValid: issues.length === 0,
    issues
  };
}

// Export default configuration
export default FILE_CONFIG;
