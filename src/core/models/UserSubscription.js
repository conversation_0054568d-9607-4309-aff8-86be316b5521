/**
 * UserSubscription Model - User subscription state management
 * Tracks user's current subscription, billing cycle, and subscription lifecycle
 *
 * @fileoverview User subscription model for MVAT Chrome Extension
 * <AUTHOR> Development Team
 * @since 1.0.0
 */

import { SubscriptionTier } from './SubscriptionTier.js';
import { getTierById } from '../config/subscriptionTiers.js';

/**
 * Subscription status enumeration
 * @readonly
 * @enum {string}
 */
export const SUBSCRIPTION_STATUS = {
  TRIAL: 'trial',
  ACTIVE: 'active',
  EXPIRED: 'expired',
  CANCELLED: 'cancelled',
  SUSPENDED: 'suspended',
  PENDING: 'pending'
};

/**
 * Billing cycle enumeration
 * @readonly
 * @enum {string}
 */
export const BILLING_CYCLE = {
  MONTHLY: 'monthly',
  ANNUAL: 'annual'
};

/**
 * UserSubscription class for managing user subscription state and lifecycle
 *
 * @class UserSubscription
 * @description Handles user subscription tracking, billing cycles, and subscription management
 */
export class UserSubscription {
  /**
   * Create a UserSubscription instance
   *
   * @param {Object} data - Subscription data
   * @param {string} data.userId - Unique user identifier
   * @param {string} data.tierId - Subscription tier ID
   * @param {string} data.status - Subscription status
   * @param {string} data.billingCycle - Billing cycle (monthly/annual)
   * @param {string} data.startDate - Subscription start date (ISO string)
   * @param {string} data.endDate - Subscription end date (ISO string)
   * @param {string} data.nextBillingDate - Next billing date (ISO string)
   * @param {number} data.price - Current subscription price
   * @param {string} data.currency - Currency code
   * @param {Object} data.paymentMethod - Payment method information
   * @param {boolean} data.autoRenew - Whether subscription auto-renews
   * @param {string} data.cancellationReason - Reason for cancellation (if applicable)
   */
  constructor(data = {}) {
    // Core subscription properties
    this.userId = data.userId || '';
    this.tierId = data.tierId || 'starter';
    this.status = data.status || SUBSCRIPTION_STATUS.TRIAL;
    this.billingCycle = data.billingCycle || BILLING_CYCLE.MONTHLY;

    // Billing and dates
    this.startDate = data.startDate || new Date().toISOString();
    this.endDate = data.endDate || this._calculateEndDate(this.startDate, this.billingCycle);
    this.nextBillingDate = data.nextBillingDate || this.endDate;
    this.trialEndDate = data.trialEndDate || this._calculateTrialEndDate(this.startDate);

    // Pricing
    this.price = data.price !== undefined ? data.price : 0;
    this.currency = data.currency || 'EUR';
    this.originalPrice = data.originalPrice || this.price;
    this.discountPercent = data.discountPercent || 0;

    // Payment and renewal
    this.paymentMethod = data.paymentMethod || null;
    this.autoRenew = data.autoRenew !== undefined ? data.autoRenew : true;
    this.gracePeriodDays = data.gracePeriodDays || 7;

    // Subscription lifecycle
    this.cancellationDate = data.cancellationDate || null;
    this.cancellationReason = data.cancellationReason || null;
    this.suspensionDate = data.suspensionDate || null;
    this.suspensionReason = data.suspensionReason || null;

    // Metadata
    this.metadata = data.metadata || {};
    this.createdAt = data.createdAt || new Date().toISOString();
    this.updatedAt = data.updatedAt || new Date().toISOString();

    // Cached tier reference
    this._tier = null;
  }

  /**
   * Get the subscription tier object
   *
   * @returns {SubscriptionTier|null} - The subscription tier or null if not found
   */
  getTier() {
    if (!this._tier || this._tier.id !== this.tierId) {
      this._tier = getTierById(this.tierId);
    }
    return this._tier;
  }

  /**
   * Check if subscription is currently active
   *
   * @returns {boolean} - True if subscription is active
   */
  isActive() {
    return this.status === SUBSCRIPTION_STATUS.ACTIVE &&
           new Date() < new Date(this.endDate);
  }

  /**
   * Check if subscription is in trial period
   *
   * @returns {boolean} - True if in trial period
   */
  isTrial() {
    return this.status === SUBSCRIPTION_STATUS.TRIAL &&
           new Date() < new Date(this.trialEndDate);
  }

  /**
   * Check if subscription has expired
   *
   * @returns {boolean} - True if subscription has expired
   */
  isExpired() {
    return this.status === SUBSCRIPTION_STATUS.EXPIRED ||
           (this.status === SUBSCRIPTION_STATUS.ACTIVE && new Date() >= new Date(this.endDate));
  }

  /**
   * Check if subscription is cancelled
   *
   * @returns {boolean} - True if subscription is cancelled
   */
  isCancelled() {
    return this.status === SUBSCRIPTION_STATUS.CANCELLED;
  }

  /**
   * Check if subscription is suspended
   *
   * @returns {boolean} - True if subscription is suspended
   */
  isSuspended() {
    return this.status === SUBSCRIPTION_STATUS.SUSPENDED;
  }

  /**
   * Get days remaining in current billing cycle
   *
   * @returns {number} - Days remaining (0 if expired)
   */
  getDaysRemaining() {
    const now = new Date();
    const endDate = new Date(this.endDate);
    const diffTime = endDate - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  }

  /**
   * Get days remaining in trial period
   *
   * @returns {number} - Trial days remaining (0 if not in trial or expired)
   */
  getTrialDaysRemaining() {
    if (!this.isTrial()) { return 0; }
    const now = new Date();
    const trialEnd = new Date(this.trialEndDate);
    const diffTime = trialEnd - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  }

  /**
   * Check if subscription can be upgraded to a target tier
   *
   * @param {string} targetTierId - Target tier ID
   * @returns {boolean} - True if upgrade is possible
   */
  canUpgradeTo(targetTierId) {
    const currentTier = this.getTier();
    const targetTier = getTierById(targetTierId);

    if (!currentTier || !targetTier) { return false; }
    if (!this.isActive() && !this.isTrial()) { return false; }

    return currentTier.canUpgradeTo(targetTier);
  }

  /**
   * Check if subscription can be downgraded to a target tier
   *
   * @param {string} targetTierId - Target tier ID
   * @returns {boolean} - True if downgrade is possible
   */
  canDowngradeTo(targetTierId) {
    const currentTier = this.getTier();
    const targetTier = getTierById(targetTierId);

    if (!currentTier || !targetTier) { return false; }
    if (!this.isActive()) { return false; }

    return currentTier.canDowngradeTo(targetTier);
  }

  /**
   * Upgrade subscription to a new tier
   *
   * @param {string} targetTierId - Target tier ID
   * @param {Object} options - Upgrade options
   * @param {boolean} options.prorated - Whether to prorate the upgrade
   * @param {string} options.effectiveDate - When upgrade takes effect
   * @returns {Object} - Upgrade result with details
   */
  upgradeTo(targetTierId, options = {}) {
    if (!this.canUpgradeTo(targetTierId)) {
      return {
        success: false,
        error: 'Upgrade not allowed',
        details: 'Cannot upgrade to the specified tier'
      };
    }

    const targetTier = getTierById(targetTierId);
    const effectiveDate = options.effectiveDate || new Date().toISOString();
    const prorated = options.prorated !== undefined ? options.prorated : true;

    // Calculate new pricing
    const newPrice = this.billingCycle === BILLING_CYCLE.ANNUAL ?
      targetTier.annualPrice : targetTier.price;

    // Store previous tier for rollback
    const previousTierId = this.tierId;
    const previousPrice = this.price;

    // Update subscription
    this.tierId = targetTierId;
    this.price = newPrice;
    this.originalPrice = newPrice;
    this.updatedAt = new Date().toISOString();
    this._tier = null; // Clear cached tier

    return {
      success: true,
      previousTierId,
      previousPrice,
      newTierId: targetTierId,
      newPrice,
      effectiveDate,
      prorated
    };
  }

  /**
   * Downgrade subscription to a new tier
   *
   * @param {string} targetTierId - Target tier ID
   * @param {Object} options - Downgrade options
   * @param {string} options.effectiveDate - When downgrade takes effect
   * @returns {Object} - Downgrade result with details
   */
  downgradeTo(targetTierId, options = {}) {
    if (!this.canDowngradeTo(targetTierId)) {
      return {
        success: false,
        error: 'Downgrade not allowed',
        details: 'Cannot downgrade to the specified tier'
      };
    }

    const targetTier = getTierById(targetTierId);
    const effectiveDate = options.effectiveDate || this.nextBillingDate;

    // Calculate new pricing
    const newPrice = this.billingCycle === BILLING_CYCLE.ANNUAL ?
      targetTier.annualPrice : targetTier.price;

    // Store previous tier for rollback
    const previousTierId = this.tierId;
    const previousPrice = this.price;

    // Update subscription (effective at next billing cycle)
    this.tierId = targetTierId;
    this.price = newPrice;
    this.originalPrice = newPrice;
    this.updatedAt = new Date().toISOString();
    this._tier = null; // Clear cached tier

    return {
      success: true,
      previousTierId,
      previousPrice,
      newTierId: targetTierId,
      newPrice,
      effectiveDate
    };
  }

  /**
   * Cancel subscription
   *
   * @param {string} reason - Cancellation reason
   * @param {boolean} immediate - Whether to cancel immediately or at period end
   * @returns {Object} - Cancellation result
   */
  cancel(reason = 'User requested', immediate = false) {
    if (this.isCancelled()) {
      return {
        success: false,
        error: 'Already cancelled',
        details: 'Subscription is already cancelled'
      };
    }

    const cancellationDate = new Date().toISOString();

    this.cancellationDate = cancellationDate;
    this.cancellationReason = reason;
    this.autoRenew = false;
    this.updatedAt = new Date().toISOString();

    if (immediate) {
      this.status = SUBSCRIPTION_STATUS.CANCELLED;
      this.endDate = cancellationDate;
    } else {
      // Cancel at period end
      this.status = SUBSCRIPTION_STATUS.CANCELLED;
    }

    return {
      success: true,
      cancellationDate,
      reason,
      immediate,
      accessUntil: this.endDate
    };
  }

  /**
   * Reactivate cancelled subscription
   *
   * @returns {Object} - Reactivation result
   */
  reactivate() {
    if (!this.isCancelled()) {
      return {
        success: false,
        error: 'Not cancelled',
        details: 'Subscription is not cancelled'
      };
    }

    this.status = SUBSCRIPTION_STATUS.ACTIVE;
    this.autoRenew = true;
    this.cancellationDate = null;
    this.cancellationReason = null;
    this.updatedAt = new Date().toISOString();

    // Extend subscription if it has expired
    if (new Date() >= new Date(this.endDate)) {
      this.endDate = this._calculateEndDate(new Date().toISOString(), this.billingCycle);
      this.nextBillingDate = this.endDate;
    }

    return {
      success: true,
      reactivationDate: this.updatedAt,
      newEndDate: this.endDate
    };
  }

  /**
   * Renew subscription for next billing cycle
   *
   * @returns {Object} - Renewal result
   */
  renew() {
    if (!this.autoRenew) {
      return {
        success: false,
        error: 'Auto-renewal disabled',
        details: 'Subscription is set to not auto-renew'
      };
    }

    const previousEndDate = this.endDate;
    this.startDate = this.endDate;
    this.endDate = this._calculateEndDate(this.startDate, this.billingCycle);
    this.nextBillingDate = this.endDate;
    this.status = SUBSCRIPTION_STATUS.ACTIVE;
    this.updatedAt = new Date().toISOString();

    return {
      success: true,
      previousEndDate,
      newEndDate: this.endDate,
      renewalDate: this.updatedAt
    };
  }

  /**
   * Calculate end date based on start date and billing cycle
   *
   * @private
   * @param {string} startDate - Start date ISO string
   * @param {string} billingCycle - Billing cycle
   * @returns {string} - End date ISO string
   */
  _calculateEndDate(startDate, billingCycle) {
    const start = new Date(startDate);
    const end = new Date(start);

    if (billingCycle === BILLING_CYCLE.ANNUAL) {
      end.setFullYear(end.getFullYear() + 1);
    } else {
      end.setMonth(end.getMonth() + 1);
    }

    return end.toISOString();
  }

  /**
   * Calculate trial end date (14 days from start)
   *
   * @private
   * @param {string} startDate - Start date ISO string
   * @returns {string} - Trial end date ISO string
   */
  _calculateTrialEndDate(startDate) {
    const start = new Date(startDate);
    const trialEnd = new Date(start);
    trialEnd.setDate(trialEnd.getDate() + 14); // 14-day trial
    return trialEnd.toISOString();
  }

  /**
   * Get subscription summary for display
   *
   * @returns {Object} - Subscription summary
   */
  getSummary() {
    const tier = this.getTier();
    return {
      userId: this.userId,
      tier: tier ? tier.getComparisonData() : null,
      status: this.status,
      billingCycle: this.billingCycle,
      price: this.price,
      currency: this.currency,
      daysRemaining: this.getDaysRemaining(),
      trialDaysRemaining: this.getTrialDaysRemaining(),
      isActive: this.isActive(),
      isTrial: this.isTrial(),
      autoRenew: this.autoRenew,
      nextBillingDate: this.nextBillingDate,
      cancellationDate: this.cancellationDate
    };
  }

  /**
   * Convert subscription to JSON representation
   *
   * @returns {Object} - JSON representation of the subscription
   */
  toJSON() {
    return {
      userId: this.userId,
      tierId: this.tierId,
      status: this.status,
      billingCycle: this.billingCycle,
      startDate: this.startDate,
      endDate: this.endDate,
      nextBillingDate: this.nextBillingDate,
      trialEndDate: this.trialEndDate,
      price: this.price,
      currency: this.currency,
      originalPrice: this.originalPrice,
      discountPercent: this.discountPercent,
      paymentMethod: this.paymentMethod,
      autoRenew: this.autoRenew,
      gracePeriodDays: this.gracePeriodDays,
      cancellationDate: this.cancellationDate,
      cancellationReason: this.cancellationReason,
      suspensionDate: this.suspensionDate,
      suspensionReason: this.suspensionReason,
      metadata: this.metadata,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }

  /**
   * Create UserSubscription from JSON data
   *
   * @param {Object} json - JSON data
   * @returns {UserSubscription} - UserSubscription instance
   */
  static fromJSON(json) {
    return new UserSubscription(json);
  }

  /**
   * Update subscription properties
   *
   * @param {Object} updates - Properties to update
   */
  update(updates) {
    Object.keys(updates).forEach(key => {
      if (key !== 'userId' && key !== 'createdAt') { // Protect immutable fields
        this[key] = updates[key];
      }
    });
    this.updatedAt = new Date().toISOString();
    this._tier = null; // Clear cached tier
  }

  /**
   * Clone the subscription with optional modifications
   *
   * @param {Object} modifications - Properties to modify in the clone
   * @returns {UserSubscription} - New subscription instance
   */
  clone(modifications = {}) {
    const data = { ...this.toJSON(), ...modifications };
    return new UserSubscription(data);
  }

  /**
   * Get subscription identifier for display
   *
   * @returns {string} - Subscription identifier
   */
  getIdentifier() {
    return `${this.userId}-${this.tierId}` || 'Unknown Subscription';
  }
}

export default UserSubscription;
