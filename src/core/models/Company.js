/**
 * Company Model - Represents company data for buyers and sellers
 * Provides validation and formatting for company information
 */

export class Company {
  constructor(data = {}) {
    // Basic company information
    this.name = data.name || '';
    this.tax_no = data.tax_no || '';
    this.tax_no_kind = data.tax_no_kind || 'nip';
    this.regon = data.regon || '';
    this.krs = data.krs || '';

    // Address information
    this.street = data.street || '';
    this.city = data.city || '';
    this.post_code = data.post_code || '';
    this.country = data.country || 'PL';
    this.country_name = data.country_name || 'Poland';

    // Contact information
    this.email = data.email || '';
    this.phone = data.phone || '';
    this.website = data.website || '';

    // Banking information
    this.bank_account = data.bank_account || '';
    this.bank_name = data.bank_name || '';
    this.swift = data.swift || '';

    // Business information
    this.industry = data.industry || '';
    this.company_type = data.company_type || '';
    this.is_vat_payer = data.is_vat_payer !== undefined ? data.is_vat_payer : true;
    this.vat_exemption_reason = data.vat_exemption_reason || '';

    // Metadata
    this.created_at = data.created_at || new Date().toISOString();
    this.updated_at = data.updated_at || new Date().toISOString();
    this.verified = data.verified || false;
    this.verification_date = data.verification_date || null;
    this.verification_source = data.verification_source || '';

    // Internal flags
    this.is_buyer = data.is_buyer !== undefined ? data.is_buyer : false;
    this.is_seller = data.is_seller !== undefined ? data.is_seller : false;
    this.is_own_company = data.is_own_company !== undefined ? data.is_own_company : false;

    // Statistics
    this.transaction_count = data.transaction_count || 0;
    this.total_amount = data.total_amount || 0;
    this.last_transaction_date = data.last_transaction_date || null;
    this.average_transaction_amount = data.average_transaction_amount || 0;
  }

  /**
   * Convert company to JSON
   * @returns {Object} - JSON representation
   */
  toJSON() {
    return {
      name: this.name,
      tax_no: this.tax_no,
      tax_no_kind: this.tax_no_kind,
      regon: this.regon,
      krs: this.krs,
      street: this.street,
      city: this.city,
      post_code: this.post_code,
      country: this.country,
      country_name: this.country_name,
      email: this.email,
      phone: this.phone,
      website: this.website,
      bank_account: this.bank_account,
      bank_name: this.bank_name,
      swift: this.swift,
      industry: this.industry,
      company_type: this.company_type,
      is_vat_payer: this.is_vat_payer,
      vat_exemption_reason: this.vat_exemption_reason,
      created_at: this.created_at,
      updated_at: this.updated_at,
      verified: this.verified,
      verification_date: this.verification_date,
      verification_source: this.verification_source,
      is_buyer: this.is_buyer,
      is_seller: this.is_seller,
      is_own_company: this.is_own_company,
      transaction_count: this.transaction_count,
      total_amount: this.total_amount,
      last_transaction_date: this.last_transaction_date,
      average_transaction_amount: this.average_transaction_amount
    };
  }

  /**
   * Create company from JSON data
   * @param {Object} json - JSON data
   * @returns {Company} - Company instance
   */
  static fromJSON(json) {
    return new Company(json);
  }

  /**
   * Get company identifier (tax number or name)
   * @returns {string} - Company identifier
   */
  getIdentifier() {
    return this.tax_no || this.name || 'Unknown Company';
  }

  /**
   * Get formatted address
   * @returns {string} - Formatted address
   */
  getFormattedAddress() {
    const parts = [];

    if (this.street) { parts.push(this.street); }
    if (this.post_code && this.city) {
      parts.push(`${this.post_code} ${this.city}`);
    } else if (this.city) {
      parts.push(this.city);
    }
    if (this.country_name && this.country_name !== 'Poland') {
      parts.push(this.country_name);
    }

    return parts.join(', ');
  }

  /**
   * Get company summary for display
   * @returns {Object} - Company summary
   */
  getSummary() {
    return {
      name: this.name,
      tax_no: this.tax_no,
      city: this.city,
      country: this.country,
      email: this.email,
      phone: this.phone,
      verified: this.verified,
      transaction_count: this.transaction_count,
      total_amount: this.total_amount,
      is_own_company: this.is_own_company
    };
  }

  /**
   * Validate company data
   * @returns {Object} - Validation result
   */
  validate() {
    const errors = [];
    const warnings = [];

    // Required fields validation
    if (!this.name || this.name.trim() === '') {
      errors.push({
        field: 'name',
        message: 'Company name is required'
      });
    }

    // Tax number validation for Polish companies
    if (this.country === 'PL' && this.tax_no) {
      const cleanTaxNo = this.tax_no.replace(/\D/g, '');
      if (cleanTaxNo.length !== 10) {
        warnings.push({
          field: 'tax_no',
          message: 'Polish tax number (NIP) should be 10 digits'
        });
      }
    }

    // Postal code validation for Polish companies
    if (this.country === 'PL' && this.post_code) {
      const postCodePattern = /^\d{2}-\d{3}$/;
      if (!postCodePattern.test(this.post_code)) {
        warnings.push({
          field: 'post_code',
          message: 'Polish postal code should be in format XX-XXX'
        });
      }
    }

    // Email validation
    if (this.email) {
      const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailPattern.test(this.email)) {
        warnings.push({
          field: 'email',
          message: 'Invalid email format'
        });
      }
    }

    // Bank account validation (basic IBAN check)
    if (this.bank_account && this.bank_account.length > 0) {
      const cleanAccount = this.bank_account.replace(/\s/g, '');
      if (cleanAccount.length < 15) {
        warnings.push({
          field: 'bank_account',
          message: 'Bank account number seems too short'
        });
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Check if company is Polish
   * @returns {boolean} - Whether company is Polish
   */
  isPolish() {
    return this.country === 'PL';
  }

  /**
   * Check if company is EU-based
   * @returns {boolean} - Whether company is EU-based
   */
  isEU() {
    const euCountries = [
      'AT', 'BE', 'BG', 'HR', 'CY', 'CZ', 'DK', 'EE', 'FI', 'FR',
      'DE', 'GR', 'HU', 'IE', 'IT', 'LV', 'LT', 'LU', 'MT', 'NL',
      'PL', 'PT', 'RO', 'SK', 'SI', 'ES', 'SE'
    ];
    return euCountries.includes(this.country);
  }

  /**
   * Get tax number without country prefix
   * @returns {string} - Clean tax number
   */
  getCleanTaxNumber() {
    if (!this.tax_no) { return ''; }

    // Remove common prefixes
    let cleanTaxNo = this.tax_no.replace(/^(PL|DE|FR|IT|ES|NL|BE|AT|CZ|SK|HU|SI|HR|BG|RO|LT|LV|EE|FI|SE|DK|IE|LU|MT|CY|GR|PT)/, '');

    // Remove non-digits for Polish NIP
    if (this.isPolish()) {
      cleanTaxNo = cleanTaxNo.replace(/\D/g, '');
    }

    return cleanTaxNo;
  }

  /**
   * Format tax number for display
   * @returns {string} - Formatted tax number
   */
  getFormattedTaxNumber() {
    const cleanTaxNo = this.getCleanTaxNumber();

    if (this.isPolish() && cleanTaxNo.length === 10) {
      // Format Polish NIP: XXX-XXX-XX-XX
      return `${cleanTaxNo.substring(0, 3)}-${cleanTaxNo.substring(3, 6)}-${cleanTaxNo.substring(6, 8)}-${cleanTaxNo.substring(8, 10)}`;
    }

    return this.tax_no;
  }

  /**
   * Update company data
   * @param {Object} updates - Updates to apply
   * @returns {Company} - Updated company
   */
  update(updates) {
    Object.assign(this, updates);
    this.updated_at = new Date().toISOString();
    return this;
  }

  /**
   * Mark company as verified
   * @param {string} source - Verification source
   */
  markAsVerified(source = 'manual') {
    this.verified = true;
    this.verification_date = new Date().toISOString();
    this.verification_source = source;
    this.updated_at = new Date().toISOString();
  }

  /**
   * Add transaction statistics
   * @param {number} amount - Transaction amount
   * @param {string} date - Transaction date
   */
  addTransaction(amount, date = null) {
    this.transaction_count += 1;
    this.total_amount += amount;
    this.last_transaction_date = date || new Date().toISOString();
    this.average_transaction_amount = this.total_amount / this.transaction_count;
    this.updated_at = new Date().toISOString();
  }

  /**
   * Clone company
   * @returns {Company} - Cloned company
   */
  clone() {
    return new Company(this.toJSON());
  }

  /**
   * Compare with another company for similarity
   * @param {Company} otherCompany - Company to compare with
   * @returns {number} - Similarity score (0-1)
   */
  getSimilarityScore(otherCompany) {
    let score = 0;
    let factors = 0;

    // Tax number comparison (highest weight)
    if (this.tax_no && otherCompany.tax_no) {
      if (this.getCleanTaxNumber() === otherCompany.getCleanTaxNumber()) {
        score += 0.5;
      }
      factors += 0.5;
    }

    // Name comparison
    if (this.name && otherCompany.name) {
      const similarity = this.stringSimilarity(
        this.name.toLowerCase(),
        otherCompany.name.toLowerCase()
      );
      score += similarity * 0.3;
      factors += 0.3;
    }

    // Address comparison
    if (this.city && otherCompany.city) {
      if (this.city.toLowerCase() === otherCompany.city.toLowerCase()) {
        score += 0.1;
      }
      factors += 0.1;
    }

    // Email comparison
    if (this.email && otherCompany.email) {
      if (this.email.toLowerCase() === otherCompany.email.toLowerCase()) {
        score += 0.1;
      }
      factors += 0.1;
    }

    return factors > 0 ? score / factors : 0;
  }

  /**
   * Calculate string similarity using Levenshtein distance
   * @param {string} str1 - First string
   * @param {string} str2 - Second string
   * @returns {number} - Similarity score (0-1)
   */
  stringSimilarity(str1, str2) {
    if (str1 === str2) { return 1; }
    if (str1.length === 0 || str2.length === 0) { return 0; }

    const matrix = [];
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }

    const maxLength = Math.max(str1.length, str2.length);
    return (maxLength - matrix[str2.length][str1.length]) / maxLength;
  }
}

// Export for ES modules
export default Company;
