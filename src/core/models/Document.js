/**
 * Document Model - Base document data model
 * Provides structure and validation for document data
 */

export class Document {
  constructor(data = {}) {
    // Core document properties
    this.fileHash = data.fileHash || null;
    this.documentName = data.documentName || '';
    this.fileSize = data.fileSize || 0;
    this.fileType = data.fileType || '';

    // Analysis metadata
    this.analysisTimestamp = data.analysisTimestamp || new Date().toISOString();
    this.ocrUsed = data.ocrUsed || false;
    this.language = data.language || 'pl';
    this.documentType = data.documentType || 'vat';

    // Document content
    this.text = data.text || '';
    this.refinedText = data.refinedText || '';

    // Basic document fields
    this.kind = data.kind || data.documentType || 'vat';
    this.number = data.number || '';
    this.issue_date = data.issue_date || '';
    this.place = data.place || '';
    this.sell_date = data.sell_date || '';

    // Seller information
    this.seller_name = data.seller_name || '';
    this.seller_tax_no = data.seller_tax_no || '';
    this.seller_tax_no_kind = data.seller_tax_no_kind || 'nip';
    this.seller_bank_account = data.seller_bank_account || '';
    this.seller_bank = data.seller_bank || '';
    this.seller_post_code = data.seller_post_code || '';
    this.seller_city = data.seller_city || '';
    this.seller_street = data.seller_street || '';
    this.seller_country = data.seller_country || 'PL';
    this.seller_email = data.seller_email || '';
    this.seller_phone = data.seller_phone || '';

    // Buyer information
    this.buyer_name = data.buyer_name || '';
    this.buyer_tax_no = data.buyer_tax_no || '';
    this.buyer_tax_no_kind = data.buyer_tax_no_kind || 'nip';
    this.buyer_post_code = data.buyer_post_code || '';
    this.buyer_city = data.buyer_city || '';
    this.buyer_street = data.buyer_street || '';
    this.buyer_country = data.buyer_country || 'PL';
    this.buyer_email = data.buyer_email || '';
    this.buyer_phone = data.buyer_phone || '';
    this.buyer_company = data.buyer_company || '1';

    // Financial information
    this.total_net = data.total_net || '';
    this.total_vat = data.total_vat || '';
    this.total_gross = data.total_gross || '';
    this.currency = data.currency || 'PLN';

    // Payment information
    this.payment_type = data.payment_type || 'transfer';
    this.payment_to = data.payment_to || '';
    this.bank_account = data.bank_account || '';

    // Additional fields
    this.description = data.description || '';
    this.internal_note = data.internal_note || '';
    this.additional_info = data.additional_info || '';
    this.contract_reference = data.contract_reference || '';

    // Status and flags
    this.status = data.status || 'draft';
    this.paid = data.paid || '0';
    this.income = data.income || '0';
    this.split_payment = data.split_payment || '0';

    // Positions/line items
    this.positions = Array.isArray(data.positions) ? data.positions : [];

    // Metadata
    this.category_id = data.category_id || '';
    this.department_id = data.department_id || '';
    this.accounting_kind = data.accounting_kind || '';
    this.lang = data.lang || 'pl';

    // RAG and linking
    this.ragLinks = data.ragLinks || [];
    this.similarDocuments = data.similarDocuments || [];

    // Validation state
    this.validationResult = data.validationResult || null;
    this.lastValidated = data.lastValidated || null;
  }

  /**
   * Convert document to JSON
   * @returns {Object} - JSON representation
   */
  toJSON() {
    return {
      fileHash: this.fileHash,
      documentName: this.documentName,
      fileSize: this.fileSize,
      fileType: this.fileType,
      analysisTimestamp: this.analysisTimestamp,
      ocrUsed: this.ocrUsed,
      language: this.language,
      documentType: this.documentType,
      text: this.text,
      refinedText: this.refinedText,
      kind: this.kind,
      number: this.number,
      issue_date: this.issue_date,
      place: this.place,
      sell_date: this.sell_date,
      seller_name: this.seller_name,
      seller_tax_no: this.seller_tax_no,
      seller_tax_no_kind: this.seller_tax_no_kind,
      seller_bank_account: this.seller_bank_account,
      seller_bank: this.seller_bank,
      seller_post_code: this.seller_post_code,
      seller_city: this.seller_city,
      seller_street: this.seller_street,
      seller_country: this.seller_country,
      seller_email: this.seller_email,
      seller_phone: this.seller_phone,
      buyer_name: this.buyer_name,
      buyer_tax_no: this.buyer_tax_no,
      buyer_tax_no_kind: this.buyer_tax_no_kind,
      buyer_post_code: this.buyer_post_code,
      buyer_city: this.buyer_city,
      buyer_street: this.buyer_street,
      buyer_country: this.buyer_country,
      buyer_email: this.buyer_email,
      buyer_phone: this.buyer_phone,
      buyer_company: this.buyer_company,
      total_net: this.total_net,
      total_vat: this.total_vat,
      total_gross: this.total_gross,
      currency: this.currency,
      payment_type: this.payment_type,
      payment_to: this.payment_to,
      bank_account: this.bank_account,
      description: this.description,
      internal_note: this.internal_note,
      additional_info: this.additional_info,
      contract_reference: this.contract_reference,
      status: this.status,
      paid: this.paid,
      income: this.income,
      split_payment: this.split_payment,
      positions: this.positions,
      category_id: this.category_id,
      department_id: this.department_id,
      accounting_kind: this.accounting_kind,
      lang: this.lang,
      ragLinks: this.ragLinks,
      similarDocuments: this.similarDocuments,
      validationResult: this.validationResult,
      lastValidated: this.lastValidated
    };
  }

  /**
   * Create document from JSON data
   * @param {Object} json - JSON data
   * @returns {Document} - Document instance
   */
  static fromJSON(json) {
    return new Document(json);
  }

  /**
   * Get document summary for display
   * @returns {Object} - Document summary
   */
  getSummary() {
    return {
      fileHash: this.fileHash,
      documentName: this.documentName,
      documentType: this.documentType,
      number: this.number,
      issue_date: this.issue_date,
      seller_name: this.seller_name,
      buyer_name: this.buyer_name,
      total_gross: this.total_gross,
      currency: this.currency,
      status: this.status,
      analysisTimestamp: this.analysisTimestamp,
      positionCount: this.positions.length
    };
  }

  /**
   * Check if document is an invoice type
   * @returns {boolean} - Whether document is an invoice
   */
  isInvoice() {
    const invoiceTypes = [
      'vat', 'proforma', 'advance', 'final', 'correction', 'vat_mp',
      'invoice_other', 'vat_margin', 'vat_rr', 'wdt', 'wnt',
      'import_service', 'import_service_eu', 'import_products', 'export_products'
    ];
    return invoiceTypes.includes(this.kind.toLowerCase());
  }

  /**
   * Check if document has positions/line items
   * @returns {boolean} - Whether document has positions
   */
  hasPositions() {
    return this.positions && this.positions.length > 0;
  }

  /**
   * Get total amount as number
   * @returns {number} - Total gross amount
   */
  getTotalAmount() {
    return parseFloat(this.total_gross) || 0;
  }

  /**
   * Get formatted total amount
   * @returns {string} - Formatted total amount with currency
   */
  getFormattedTotal() {
    const amount = this.getTotalAmount();
    return `${amount.toFixed(2)} ${this.currency}`;
  }

  /**
   * Check if document is paid
   * @returns {boolean} - Whether document is paid
   */
  isPaid() {
    return this.paid === '1' || this.status === 'paid';
  }

  /**
   * Get document age in days
   * @returns {number} - Age in days
   */
  getAgeInDays() {
    if (!this.issue_date) { return 0; }

    const issueDate = new Date(this.issue_date);
    const today = new Date();
    const diffTime = Math.abs(today - issueDate);
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  /**
   * Check if document is overdue
   * @returns {boolean} - Whether document is overdue
   */
  isOverdue() {
    if (!this.payment_to || this.isPaid()) { return false; }

    const paymentDate = new Date(this.payment_to);
    const today = new Date();
    return today > paymentDate;
  }

  /**
   * Get days until payment due
   * @returns {number} - Days until due (negative if overdue)
   */
  getDaysUntilDue() {
    if (!this.payment_to) { return 0; }

    const paymentDate = new Date(this.payment_to);
    const today = new Date();
    const diffTime = paymentDate - today;
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  /**
   * Update document data
   * @param {Object} updates - Updates to apply
   * @returns {Document} - Updated document
   */
  update(updates) {
    Object.assign(this, updates);
    return this;
  }

  /**
   * Clone document
   * @returns {Document} - Cloned document
   */
  clone() {
    return new Document(this.toJSON());
  }

  /**
   * Validate document data
   * @param {ValidationService} validationService - Validation service
   * @returns {Object} - Validation result
   */
  validate(validationService) {
    if (!validationService) {
      throw new Error('ValidationService is required for document validation');
    }

    const result = validationService.validateDocument(this.toJSON());
    this.validationResult = result;
    this.lastValidated = new Date().toISOString();

    return result;
  }

  /**
   * Check if document is valid
   * @returns {boolean} - Whether document is valid
   */
  isValid() {
    return this.validationResult && this.validationResult.valid;
  }

  /**
   * Get validation errors
   * @returns {Array} - Array of validation errors
   */
  getValidationErrors() {
    return this.validationResult ? this.validationResult.errors : [];
  }

  /**
   * Get validation warnings
   * @returns {Array} - Array of validation warnings
   */
  getValidationWarnings() {
    return this.validationResult ? this.validationResult.warnings : [];
  }
}

// Export for ES modules
export default Document;

// Export to window object for non-module scripts
if (typeof window !== 'undefined') {
  window.Document = Document;
}

// ===== LOCAL TESTING (Node.js equivalent of Python's if __name__ == '__main__') =====
if (typeof require !== 'undefined' && require.main === module) {
  console.log('🧪 Running local tests for Document...');

  // Test core functionality
  console.log('✅ Test 1: Document creation');
  const doc = new Document({
    documentName: 'test-invoice.pdf',
    fileHash: 'abc123',
    seller_name: 'Test Company',
    buyer_name: 'Customer Company',
    total_gross: '1230.00',
    currency: 'PLN',
    issue_date: '2025-01-27',
    payment_to: '2025-02-27'
  });
  console.log('Document created:', doc.documentName);
  console.log('Total amount:', doc.getTotalAmount());

  console.log('✅ Test 2: Document type checking');
  console.log('Is invoice:', doc.isInvoice());
  console.log('Has positions:', doc.hasPositions());
  console.log('Is paid:', doc.isPaid());

  console.log('✅ Test 3: Date calculations');
  console.log('Age in days:', doc.getAgeInDays());
  console.log('Days until due:', doc.getDaysUntilDue());
  console.log('Is overdue:', doc.isOverdue());

  console.log('✅ Test 4: JSON serialization');
  const json = doc.toJSON();
  console.log('JSON keys count:', Object.keys(json).length);
  const restored = Document.fromJSON(json);
  console.log('Restored document name:', restored.documentName);

  console.log('✅ Test 5: Document summary');
  const summary = doc.getSummary();
  console.log('Summary keys:', Object.keys(summary));
  console.log('Formatted total:', doc.getFormattedTotal());

  console.log('✅ Test 6: Document operations');
  const cloned = doc.clone();
  console.log('Cloned document hash:', cloned.fileHash);

  doc.update({ status: 'paid', paid: '1' });
  console.log('Updated status:', doc.status);
  console.log('Now paid:', doc.isPaid());

  console.log('✅ All tests completed for Document');
}
