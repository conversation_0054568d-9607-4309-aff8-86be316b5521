/**
 * UsageTracker Model - Usage monitoring and limit enforcement
 * Tracks resource consumption, enforces limits, and manages monthly resets
 *
 * @fileoverview Usage tracking model for MVAT Chrome Extension
 * <AUTHOR> Development Team
 * @since 1.0.0
 */

/**
 * Resource type enumeration for usage tracking
 * @readonly
 * @enum {string}
 */
export const RESOURCE_TYPE = {
  MONTHLY_INVOICES: 'monthlyInvoices',
  AI_CALLS: 'aiCalls',
  STORAGE_GB: 'storageGB',
  API_CALLS: 'apiCalls',
  ACTIVE_USERS: 'activeUsers',
  INTEGRATIONS: 'integrations'
};

/**
 * Usage period enumeration
 * @readonly
 * @enum {string}
 */
export const USAGE_PERIOD = {
  MONTHLY: 'monthly',
  DAILY: 'daily',
  WEEKLY: 'weekly',
  ANNUAL: 'annual'
};

/**
 * UsageTracker class for monitoring resource consumption and enforcing limits
 *
 * @class UsageTracker
 * @description Handles usage tracking, limit enforcement, and monthly reset functionality
 */
export class UsageTracker {
  /**
   * Create a UsageTracker instance
   *
   * @param {Object} data - Usage tracker data
   * @param {string} data.userId - Unique user identifier
   * @param {string} data.tierId - Current subscription tier ID
   * @param {string} data.period - Usage tracking period
   * @param {string} data.periodStart - Period start date (ISO string)
   * @param {string} data.periodEnd - Period end date (ISO string)
   * @param {Object} data.usage - Current usage by resource type
   * @param {Object} data.limits - Current limits by resource type
   * @param {Array} data.history - Usage history entries
   * @param {Object} data.metadata - Additional metadata
   */
  constructor(data = {}) {
    // Core tracking properties
    this.userId = data.userId || '';
    this.tierId = data.tierId || 'starter';
    this.period = data.period || USAGE_PERIOD.MONTHLY;

    // Period management
    this.periodStart = data.periodStart || this._calculatePeriodStart();
    this.periodEnd = data.periodEnd || this._calculatePeriodEnd(this.periodStart);

    // Usage tracking
    this.usage = {
      [RESOURCE_TYPE.MONTHLY_INVOICES]: 0,
      [RESOURCE_TYPE.AI_CALLS]: 0,
      [RESOURCE_TYPE.STORAGE_GB]: 0,
      [RESOURCE_TYPE.API_CALLS]: 0,
      [RESOURCE_TYPE.ACTIVE_USERS]: 0,
      [RESOURCE_TYPE.INTEGRATIONS]: 0,
      ...data.usage
    };

    // Limits (copied from tier)
    this.limits = {
      [RESOURCE_TYPE.MONTHLY_INVOICES]: 0,
      [RESOURCE_TYPE.AI_CALLS]: 0,
      [RESOURCE_TYPE.STORAGE_GB]: 0.1,
      [RESOURCE_TYPE.API_CALLS]: 0,
      [RESOURCE_TYPE.ACTIVE_USERS]: 1,
      [RESOURCE_TYPE.INTEGRATIONS]: 0,
      ...data.limits
    };

    // Usage history and metadata
    this.history = data.history || [];
    this.metadata = data.metadata || {};

    // Timestamps
    this.createdAt = data.createdAt || new Date().toISOString();
    this.updatedAt = data.updatedAt || new Date().toISOString();
    this.lastResetAt = data.lastResetAt || this.periodStart;
  }

  /**
   * Increment usage for a specific resource type
   *
   * @param {string} resourceType - Type of resource to increment
   * @param {number} amount - Amount to increment (default: 1)
   * @param {Object} metadata - Additional metadata for the usage entry
   * @returns {Object} - Result with success status and details
   */
  incrementUsage(resourceType, amount = 1, metadata = {}) {
    if (!Object.values(RESOURCE_TYPE).includes(resourceType)) {
      return {
        success: false,
        error: 'Invalid resource type',
        resourceType,
        amount
      };
    }

    if (amount <= 0) {
      return {
        success: false,
        error: 'Amount must be positive',
        resourceType,
        amount
      };
    }

    // Check if increment would exceed limit
    const currentUsage = this.usage[resourceType] || 0;
    const limit = this.limits[resourceType] || 0;
    const newUsage = currentUsage + amount;

    if (limit > 0 && newUsage > limit) {
      return {
        success: false,
        error: 'Usage limit exceeded',
        resourceType,
        currentUsage,
        limit,
        requestedAmount: amount,
        excess: newUsage - limit
      };
    }

    // Increment usage
    this.usage[resourceType] = newUsage;
    this.updatedAt = new Date().toISOString();

    // Add to history
    this._addHistoryEntry({
      action: 'increment',
      resourceType,
      amount,
      previousUsage: currentUsage,
      newUsage,
      timestamp: this.updatedAt,
      metadata
    });

    return {
      success: true,
      resourceType,
      previousUsage: currentUsage,
      newUsage,
      remaining: limit > 0 ? Math.max(0, limit - newUsage) : Infinity
    };
  }

  /**
   * Check if usage is within limits for a specific resource
   *
   * @param {string} resourceType - Type of resource to check
   * @param {number} additionalAmount - Additional amount to check (default: 0)
   * @returns {Object} - Check result with details
   */
  checkLimit(resourceType, additionalAmount = 0) {
    if (!Object.values(RESOURCE_TYPE).includes(resourceType)) {
      return {
        isValid: false,
        error: 'Invalid resource type',
        resourceType
      };
    }

    const currentUsage = this.usage[resourceType] || 0;
    const limit = this.limits[resourceType] || 0;
    const totalUsage = currentUsage + additionalAmount;

    if (limit === 0) {
      // Unlimited
      return {
        isValid: true,
        resourceType,
        currentUsage,
        limit: 'unlimited',
        remaining: Infinity,
        percentage: 0
      };
    }

    const isValid = totalUsage <= limit;
    const remaining = Math.max(0, limit - totalUsage);
    const percentage = Math.round((totalUsage / limit) * 100);

    return {
      isValid,
      resourceType,
      currentUsage,
      limit,
      remaining,
      percentage,
      wouldExceed: !isValid,
      excess: isValid ? 0 : totalUsage - limit
    };
  }

  /**
   * Get all usage limits status
   *
   * @returns {Object} - Status for all resource types
   */
  getAllLimitsStatus() {
    const status = {};

    Object.values(RESOURCE_TYPE).forEach(resourceType => {
      status[resourceType] = this.checkLimit(resourceType);
    });

    return status;
  }

  /**
   * Check if any limits are exceeded
   *
   * @returns {boolean} - True if any limits are exceeded
   */
  hasExceededLimits() {
    return Object.values(this.getAllLimitsStatus()).some(status => !status.isValid);
  }

  /**
   * Get resources that are approaching limits (>80%)
   *
   * @param {number} threshold - Warning threshold percentage (default: 80)
   * @returns {Array} - Array of resources approaching limits
   */
  getApproachingLimits(threshold = 80) {
    const approaching = [];
    const allStatus = this.getAllLimitsStatus();

    Object.entries(allStatus).forEach(([resourceType, status]) => {
      if (status.isValid && status.limit !== 'unlimited' && status.percentage >= threshold) {
        approaching.push({
          resourceType,
          percentage: status.percentage,
          remaining: status.remaining,
          limit: status.limit
        });
      }
    });

    return approaching;
  }

  /**
   * Reset usage for the current period
   *
   * @param {Object} options - Reset options
   * @param {boolean} options.preserveHistory - Whether to preserve usage history
   * @returns {Object} - Reset result
   */
  resetPeriod(options = {}) {
    const preserveHistory = options.preserveHistory !== undefined ? options.preserveHistory : true;
    const resetDate = new Date().toISOString();

    // Store current usage in history before reset
    if (preserveHistory) {
      this._addHistoryEntry({
        action: 'period_reset',
        previousUsage: { ...this.usage },
        periodStart: this.periodStart,
        periodEnd: this.periodEnd,
        timestamp: resetDate,
        metadata: { resetType: 'automatic' }
      });
    }

    // Reset all usage counters
    Object.keys(this.usage).forEach(resourceType => {
      this.usage[resourceType] = 0;
    });

    // Update period dates
    this.periodStart = this._calculatePeriodStart();
    this.periodEnd = this._calculatePeriodEnd(this.periodStart);
    this.lastResetAt = resetDate;
    this.updatedAt = resetDate;

    return {
      success: true,
      resetDate,
      newPeriodStart: this.periodStart,
      newPeriodEnd: this.periodEnd,
      historyPreserved: preserveHistory
    };
  }

  /**
   * Check if period needs reset
   *
   * @returns {boolean} - True if period needs reset
   */
  needsPeriodReset() {
    const now = new Date();
    const periodEnd = new Date(this.periodEnd);
    return now > periodEnd;
  }

  /**
   * Get days until period reset
   *
   * @returns {number} - Days until reset
   */
  getDaysUntilReset() {
    const now = new Date();
    const periodEnd = new Date(this.periodEnd);
    const diffTime = periodEnd - now;
    return Math.max(0, Math.ceil(diffTime / (1000 * 60 * 60 * 24)));
  }

  /**
   * Get usage summary for display
   *
   * @returns {Object} - Usage summary
   */
  getUsageSummary() {
    const allStatus = this.getAllLimitsStatus();
    const resourcesWithUsage = Object.values(this.usage).filter(usage => usage > 0).length;

    return {
      userId: this.userId,
      tierId: this.tierId,
      period: this.period,
      periodStart: this.periodStart,
      periodEnd: this.periodEnd,
      daysUntilReset: this.getDaysUntilReset(),
      totalResources: Object.keys(this.usage).length,
      resourcesWithUsage,
      hasExceededLimits: this.hasExceededLimits(),
      approachingLimits: this.getApproachingLimits(),
      allStatus
    };
  }

  /**
   * Convert tracker to JSON representation
   *
   * @returns {Object} - JSON representation of the tracker
   */
  toJSON() {
    return {
      userId: this.userId,
      tierId: this.tierId,
      period: this.period,
      periodStart: this.periodStart,
      periodEnd: this.periodEnd,
      usage: this.usage,
      limits: this.limits,
      history: this.history,
      metadata: this.metadata,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      lastResetAt: this.lastResetAt
    };
  }

  /**
   * Create UsageTracker from JSON data
   *
   * @param {Object} json - JSON data
   * @returns {UsageTracker} - UsageTracker instance
   */
  static fromJSON(json) {
    return new UsageTracker(json);
  }

  /**
   * Update tracker properties
   *
   * @param {Object} updates - Properties to update
   */
  update(updates) {
    Object.keys(updates).forEach(key => {
      if (key !== 'userId' && key !== 'createdAt') { // Protect immutable fields
        this[key] = updates[key];
      }
    });
    this.updatedAt = new Date().toISOString();
  }

  /**
   * Clone the tracker with optional modifications
   *
   * @param {Object} modifications - Properties to modify in the clone
   * @returns {UsageTracker} - New tracker instance
   */
  clone(modifications = {}) {
    const data = { ...this.toJSON(), ...modifications };
    return new UsageTracker(data);
  }

  /**
   * Get tracker identifier for display
   *
   * @returns {string} - Tracker identifier
   */
  getIdentifier() {
    return `${this.userId}-${this.tierId}` || 'Unknown Tracker';
  }

  // Private helper methods

  /**
   * Add entry to usage history
   *
   * @private
   * @param {Object} entry - History entry
   */
  _addHistoryEntry(entry) {
    this.history.push({
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      ...entry
    });

    // Keep history size manageable (last 1000 entries)
    if (this.history.length > 1000) {
      this.history = this.history.slice(-1000);
    }
  }

  /**
   * Calculate period start date
   *
   * @private
   * @returns {string} - ISO date string
   */
  _calculatePeriodStart() {
    const now = new Date();

    switch (this.period) {
      case USAGE_PERIOD.MONTHLY:
        return new Date(now.getFullYear(), now.getMonth(), 1).toISOString();
      case USAGE_PERIOD.WEEKLY:
        const startOfWeek = new Date(now);
        startOfWeek.setDate(now.getDate() - now.getDay());
        startOfWeek.setHours(0, 0, 0, 0);
        return startOfWeek.toISOString();
      case USAGE_PERIOD.DAILY:
        return new Date(now.getFullYear(), now.getMonth(), now.getDate()).toISOString();
      case USAGE_PERIOD.ANNUAL:
        return new Date(now.getFullYear(), 0, 1).toISOString();
      default:
        return now.toISOString();
    }
  }

  /**
   * Calculate period end date
   *
   * @private
   * @param {string} startDate - Period start date
   * @returns {string} - ISO date string
   */
  _calculatePeriodEnd(startDate) {
    const start = new Date(startDate);

    switch (this.period) {
      case USAGE_PERIOD.MONTHLY:
        return new Date(start.getFullYear(), start.getMonth() + 1, 0, 23, 59, 59, 999).toISOString();
      case USAGE_PERIOD.WEEKLY:
        const endOfWeek = new Date(start);
        endOfWeek.setDate(start.getDate() + 6);
        endOfWeek.setHours(23, 59, 59, 999);
        return endOfWeek.toISOString();
      case USAGE_PERIOD.DAILY:
        const endOfDay = new Date(start);
        endOfDay.setHours(23, 59, 59, 999);
        return endOfDay.toISOString();
      case USAGE_PERIOD.ANNUAL:
        return new Date(start.getFullYear(), 11, 31, 23, 59, 59, 999).toISOString();
      default:
        const defaultEnd = new Date(start);
        defaultEnd.setMonth(defaultEnd.getMonth() + 1);
        return defaultEnd.toISOString();
    }
  }
}

export default UsageTracker;
