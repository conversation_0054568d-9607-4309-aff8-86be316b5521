/**
 * SubscriptionTier Model - Subscription tier management and validation
 * Provides structure for subscription tiers, feature validation, and usage limits
 *
 * @fileoverview Subscription tier model for MVAT Chrome Extension
 * <AUTHOR> Development Team
 * @since 1.0.0
 */

/**
 * SubscriptionTier class for managing subscription tiers and their features
 *
 * @class SubscriptionTier
 * @description Handles subscription tier definitions, feature validation, and usage limits
 */
export class SubscriptionTier {
  /**
   * Create a SubscriptionTier instance
   *
   * @param {Object} data - Tier configuration data
   * @param {string} data.id - Unique tier identifier
   * @param {string} data.name - Display name of the tier
   * @param {string} data.description - Tier description
   * @param {number} data.price - Monthly price in EUR
   * @param {number} data.annualPrice - Annual price in EUR (with discount)
   * @param {Object} data.limits - Usage limits for the tier
   * @param {Array<string>} data.features - Available features
   * @param {Array<string>} data.restrictions - Tier restrictions
   * @param {boolean} data.isActive - Whether tier is currently available
   */
  constructor(data = {}) {
    // Core tier properties
    this.id = data.id || '';
    this.name = data.name || '';
    this.description = data.description || '';
    this.price = data.price || 0;
    this.annualPrice = data.annualPrice || (data.price * 10); // 2 months free
    this.currency = data.currency || 'EUR';

    // Usage limits
    this.limits = {
      monthlyInvoices: data.limits?.monthlyInvoices !== undefined ? data.limits.monthlyInvoices : 0,
      aiCalls: data.limits?.aiCalls !== undefined ? data.limits.aiCalls : 0,
      storageGB: data.limits?.storageGB !== undefined ? data.limits.storageGB : 0.1,
      apiCalls: data.limits?.apiCalls !== undefined ? data.limits.apiCalls : 0,
      users: data.limits?.users !== undefined ? data.limits.users : 1,
      integrations: data.limits?.integrations !== undefined ? data.limits.integrations : 0
    };

    // Features and restrictions
    this.features = data.features || [];
    this.restrictions = data.restrictions || [];

    // Tier metadata
    this.isActive = data.isActive !== undefined ? data.isActive : true;
    this.sortOrder = data.sortOrder || 0;
    this.isPopular = data.isPopular || false;
    this.isEnterprise = data.isEnterprise || false;

    // Timestamps
    this.createdAt = data.createdAt || new Date().toISOString();
    this.updatedAt = data.updatedAt || new Date().toISOString();
  }

  /**
   * Check if a feature is available in this tier
   *
   * @param {string} featureName - Name of the feature to check
   * @returns {boolean} - True if feature is available
   */
  hasFeature(featureName) {
    return this.features.includes(featureName);
  }

  /**
   * Check if a restriction applies to this tier
   *
   * @param {string} restrictionName - Name of the restriction to check
   * @returns {boolean} - True if restriction applies
   */
  hasRestriction(restrictionName) {
    return this.restrictions.includes(restrictionName);
  }

  /**
   * Validate usage against tier limits
   *
   * @param {Object} usage - Current usage data
   * @param {number} usage.monthlyInvoices - Number of invoices processed this month
   * @param {number} usage.aiCalls - Number of AI calls made this month
   * @param {number} usage.storageUsed - Storage used in GB
   * @param {number} usage.apiCalls - Number of API calls made this month
   * @param {number} usage.activeUsers - Number of active users
   * @returns {Object} - Validation result with details
   */
  validateUsage(usage = {}) {
    const result = {
      isValid: true,
      violations: [],
      warnings: [],
      remainingLimits: {}
    };

    // Check each limit
    const checks = [
      { key: 'monthlyInvoices', label: 'Monthly Invoices' },
      { key: 'aiCalls', label: 'AI Calls' },
      { key: 'storageUsed', label: 'Storage (GB)', limitKey: 'storageGB' },
      { key: 'apiCalls', label: 'API Calls' },
      { key: 'activeUsers', label: 'Active Users', limitKey: 'users' }
    ];

    checks.forEach(check => {
      const usageKey = check.key;
      const limitKey = check.limitKey || check.key;
      const currentUsage = usage[usageKey] || 0;
      const limit = this.limits[limitKey] || 0;

      if (limit > 0) { // 0 means unlimited
        const remaining = limit - currentUsage;
        result.remainingLimits[usageKey] = Math.max(0, remaining);

        if (currentUsage > limit) {
          result.isValid = false;
          result.violations.push({
            type: usageKey,
            label: check.label,
            current: currentUsage,
            limit: limit,
            excess: currentUsage - limit
          });
        } else if (currentUsage >= limit * 0.8) { // 80% warning threshold
          result.warnings.push({
            type: usageKey,
            label: check.label,
            current: currentUsage,
            limit: limit,
            percentage: Math.round((currentUsage / limit) * 100)
          });
        }
      } else {
        // Unlimited - no violations possible
        result.remainingLimits[usageKey] = Infinity;
      }
    });

    return result;
  }

  /**
   * Get annual discount percentage
   *
   * @returns {number} - Discount percentage for annual billing
   */
  getAnnualDiscount() {
    if (this.price === 0) { return 0; }
    const monthlyTotal = this.price * 12;
    const discount = ((monthlyTotal - this.annualPrice) / monthlyTotal) * 100;
    return Math.round(discount);
  }

  /**
   * Check if this tier can be upgraded to another tier
   *
   * @param {SubscriptionTier} targetTier - Target tier to upgrade to
   * @returns {boolean} - True if upgrade is possible
   */
  canUpgradeTo(targetTier) {
    if (!targetTier || !targetTier.isActive) { return false; }
    return targetTier.price > this.price;
  }

  /**
   * Check if this tier can be downgraded to another tier
   *
   * @param {SubscriptionTier} targetTier - Target tier to downgrade to
   * @returns {boolean} - True if downgrade is possible
   */
  canDowngradeTo(targetTier) {
    if (!targetTier || !targetTier.isActive) { return false; }
    return targetTier.price < this.price;
  }

  /**
   * Get tier comparison data for UI display
   *
   * @returns {Object} - Formatted tier data for comparison
   */
  getComparisonData() {
    return {
      id: this.id,
      name: this.name,
      description: this.description,
      price: this.price,
      annualPrice: this.annualPrice,
      annualDiscount: this.getAnnualDiscount(),
      currency: this.currency,
      limits: { ...this.limits },
      features: [...this.features],
      restrictions: [...this.restrictions],
      isPopular: this.isPopular,
      isEnterprise: this.isEnterprise
    };
  }

  /**
   * Convert tier to JSON representation
   *
   * @returns {Object} - JSON representation of the tier
   */
  toJSON() {
    return {
      id: this.id,
      name: this.name,
      description: this.description,
      price: this.price,
      annualPrice: this.annualPrice,
      currency: this.currency,
      limits: this.limits,
      features: this.features,
      restrictions: this.restrictions,
      isActive: this.isActive,
      sortOrder: this.sortOrder,
      isPopular: this.isPopular,
      isEnterprise: this.isEnterprise,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }

  /**
   * Create SubscriptionTier from JSON data
   *
   * @param {Object} json - JSON data
   * @returns {SubscriptionTier} - SubscriptionTier instance
   */
  static fromJSON(json) {
    return new SubscriptionTier(json);
  }

  /**
   * Update tier properties
   *
   * @param {Object} updates - Properties to update
   */
  update(updates) {
    Object.keys(updates).forEach(key => {
      if (key !== 'id' && key !== 'createdAt') { // Protect immutable fields
        this[key] = updates[key];
      }
    });
    this.updatedAt = new Date().toISOString();
  }

  /**
   * Clone the tier with optional modifications
   *
   * @param {Object} modifications - Properties to modify in the clone
   * @returns {SubscriptionTier} - New tier instance
   */
  clone(modifications = {}) {
    const data = { ...this.toJSON(), ...modifications };
    return new SubscriptionTier(data);
  }

  /**
   * Get tier identifier for display
   *
   * @returns {string} - Tier identifier
   */
  getIdentifier() {
    return this.id || this.name || 'Unknown Tier';
  }

  /**
   * Check if tier is free
   *
   * @returns {boolean} - True if tier is free
   */
  isFree() {
    return this.price === 0;
  }

  /**
   * Get formatted price string
   *
   * @param {boolean} annual - Whether to show annual price
   * @returns {string} - Formatted price string
   */
  getFormattedPrice(annual = false) {
    const price = annual ? this.annualPrice : this.price;
    if (price === 0) { return 'Free'; }
    return `€${price}${annual ? '/year' : '/month'}`;
  }
}

export default SubscriptionTier;
