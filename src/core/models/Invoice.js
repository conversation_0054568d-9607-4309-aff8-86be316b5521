/**
 * Invoice Model - Extends Document model with invoice-specific functionality
 * Provides specialized methods for invoice handling and Fakturownia integration
 */

import Document from './Document.js';

export class Invoice extends Document {
  constructor(data = {}) {
    super(data);

    // Invoice-specific fields
    this.invoice_template_id = data.invoice_template_id || '';
    this.oid = data.oid || '';
    this.oid_unique = data.oid_unique || '';
    this.warehouse_id = data.warehouse_id || '';
    this.exchange_currency = data.exchange_currency || '';
    this.exchange_kind = data.exchange_kind || '';
    this.exchange_currency_rate = data.exchange_currency_rate || '';
    this.exchange_date = data.exchange_date || '';
    this.exchange_note = data.exchange_note || '';

    // VAT-specific fields
    this.gtu_codes = data.gtu_codes || '';
    this.procedure_designations = data.procedure_designations || '';
    this.reverse_charge = data.reverse_charge || '0';

    // Correction-specific fields
    this.invoice_id = data.invoice_id || '';
    this.from_invoice_id = data.from_invoice_id || '';
    this.corrected_content_before = data.corrected_content_before || '';
    this.corrected_content_after = data.corrected_content_after || '';

    // Fakturownia integration
    this.fakturownia_id = data.fakturownia_id || null;
    this.fakturownia_url = data.fakturownia_url || '';
    this.fakturownia_status = data.fakturownia_status || '';
    this.last_sync = data.last_sync || null;
    this.sync_errors = data.sync_errors || [];

    // Invoice workflow
    this.workflow_status = data.workflow_status || 'draft';
    this.approval_status = data.approval_status || 'pending';
    this.approved_by = data.approved_by || '';
    this.approved_at = data.approved_at || null;

    // File attachments
    this.fileData = data.fileData || null;
    this.attachments = data.attachments || [];
  }

  /**
   * Convert invoice to JSON
   * @returns {Object} - JSON representation
   */
  toJSON() {
    return {
      ...super.toJSON(),
      invoice_template_id: this.invoice_template_id,
      oid: this.oid,
      oid_unique: this.oid_unique,
      warehouse_id: this.warehouse_id,
      exchange_currency: this.exchange_currency,
      exchange_kind: this.exchange_kind,
      exchange_currency_rate: this.exchange_currency_rate,
      exchange_date: this.exchange_date,
      exchange_note: this.exchange_note,
      gtu_codes: this.gtu_codes,
      procedure_designations: this.procedure_designations,
      reverse_charge: this.reverse_charge,
      invoice_id: this.invoice_id,
      from_invoice_id: this.from_invoice_id,
      corrected_content_before: this.corrected_content_before,
      corrected_content_after: this.corrected_content_after,
      fakturownia_id: this.fakturownia_id,
      fakturownia_url: this.fakturownia_url,
      fakturownia_status: this.fakturownia_status,
      last_sync: this.last_sync,
      sync_errors: this.sync_errors,
      workflow_status: this.workflow_status,
      approval_status: this.approval_status,
      approved_by: this.approved_by,
      approved_at: this.approved_at,
      fileData: this.fileData,
      attachments: this.attachments
    };
  }

  /**
   * Create invoice from JSON data
   * @param {Object} json - JSON data
   * @returns {Invoice} - Invoice instance
   */
  static fromJSON(json) {
    return new Invoice(json);
  }

  /**
   * Get invoice summary for display
   * @returns {Object} - Invoice summary
   */
  getSummary() {
    return {
      ...super.getSummary(),
      invoice_id: this.invoice_id,
      from_invoice_id: this.from_invoice_id,
      fakturownia_id: this.fakturownia_id,
      workflow_status: this.workflow_status,
      approval_status: this.approval_status,
      last_sync: this.last_sync,
      isCorrection: this.isCorrection(),
      isAdvance: this.isAdvance(),
      isFinal: this.isFinal()
    };
  }

  /**
   * Check if invoice is a correction
   * @returns {boolean} - Whether invoice is a correction
   */
  isCorrection() {
    return this.kind === 'correction' || this.kind === 'correction_note';
  }

  /**
   * Check if invoice is an advance invoice
   * @returns {boolean} - Whether invoice is an advance
   */
  isAdvance() {
    return this.kind === 'advance';
  }

  /**
   * Check if invoice is a final invoice
   * @returns {boolean} - Whether invoice is a final
   */
  isFinal() {
    return this.kind === 'final';
  }

  /**
   * Check if invoice is a proforma
   * @returns {boolean} - Whether invoice is a proforma
   */
  isProforma() {
    return this.kind === 'proforma';
  }

  /**
   * Check if invoice uses split payment
   * @returns {boolean} - Whether invoice uses split payment
   */
  usesSplitPayment() {
    return this.split_payment === '1';
  }

  /**
   * Check if invoice uses reverse charge
   * @returns {boolean} - Whether invoice uses reverse charge
   */
  usesReverseCharge() {
    return this.reverse_charge === '1';
  }

  /**
   * Check if invoice is synced with Fakturownia
   * @returns {boolean} - Whether invoice is synced
   */
  isSyncedWithFakturownia() {
    return this.fakturownia_id !== null && this.fakturownia_id !== '';
  }

  /**
   * Check if invoice needs sync with Fakturownia
   * @returns {boolean} - Whether invoice needs sync
   */
  needsSync() {
    if (!this.isSyncedWithFakturownia()) { return true; }
    if (this.sync_errors.length > 0) { return true; }

    // Check if last sync is older than last modification
    if (this.last_sync && this.analysisTimestamp) {
      return new Date(this.analysisTimestamp) > new Date(this.last_sync);
    }

    return false;
  }

  /**
   * Get invoice status for display
   * @returns {Object} - Status information
   */
  getStatusInfo() {
    const status = {
      workflow: this.workflow_status,
      approval: this.approval_status,
      payment: this.isPaid() ? 'paid' : 'unpaid',
      sync: this.isSyncedWithFakturownia() ? 'synced' : 'not_synced',
      overdue: this.isOverdue()
    };

    // Determine overall status
    if (status.overdue) {
      status.overall = 'overdue';
      status.color = 'red';
    } else if (status.payment === 'paid') {
      status.overall = 'paid';
      status.color = 'green';
    } else if (status.approval === 'approved') {
      status.overall = 'approved';
      status.color = 'blue';
    } else if (status.workflow === 'draft') {
      status.overall = 'draft';
      status.color = 'gray';
    } else {
      status.overall = 'pending';
      status.color = 'yellow';
    }

    return status;
  }

  /**
   * Prepare invoice data for Fakturownia API
   * @returns {Object} - Fakturownia-compatible data
   */
  toFakturowniaFormat() {
    const data = {
      kind: this.kind,
      number: this.number,
      issue_date: this.issue_date,
      place: this.place,
      sell_date: this.sell_date || this.issue_date,

      // Seller
      seller_name: this.seller_name,
      seller_tax_no: this.seller_tax_no,
      seller_bank_account: this.seller_bank_account,
      seller_bank: this.seller_bank,
      seller_post_code: this.seller_post_code,
      seller_city: this.seller_city,
      seller_street: this.seller_street,
      seller_country: this.seller_country,
      seller_email: this.seller_email,

      // Buyer
      buyer_name: this.buyer_name,
      buyer_tax_no: this.buyer_tax_no,
      buyer_post_code: this.buyer_post_code,
      buyer_city: this.buyer_city,
      buyer_street: this.buyer_street,
      buyer_country: this.buyer_country,
      buyer_email: this.buyer_email,
      buyer_company: this.buyer_company,

      // Financial
      currency: this.currency,
      payment_type: this.payment_type,
      payment_to: this.payment_to,
      bank_account: this.bank_account,

      // Status
      status: this.status,
      paid: this.paid,
      income: this.income,

      // Additional
      description: this.description,
      internal_note: this.internal_note,
      additional_info: this.additional_info,

      // VAT specific
      split_payment: this.split_payment,
      gtu_codes: this.gtu_codes,
      procedure_designations: this.procedure_designations,
      reverse_charge: this.reverse_charge,

      // Language
      lang: this.lang
    };

    // Add correction-specific fields
    if (this.isCorrection()) {
      data.invoice_id = this.invoice_id;
      data.from_invoice_id = this.from_invoice_id;
      data.corrected_content_before = this.corrected_content_before;
      data.corrected_content_after = this.corrected_content_after;
    }

    // Add advance/final specific fields
    if (this.isAdvance() || this.isFinal()) {
      data.invoice_id = this.invoice_id;
    }

    if (this.isFinal()) {
      data.from_invoice_id = this.from_invoice_id;
    }

    // Add positions if present
    if (this.hasPositions()) {
      data.positions = this.positions.map(position => ({
        name: position.name,
        description: position.description || '',
        additional_info: position.additional_info || '',
        quantity: position.quantity,
        quantity_unit: position.quantity_unit || 'szt',
        price_net: position.price_net,
        tax: position.tax,
        price_gross: position.price_gross,
        total_price_net: position.total_price_net,
        total_price_gross: position.total_price_gross,
        tax_amount: position.tax_amount,
        code: position.code || '',
        discount: position.discount || '',
        discount_percent: position.discount_percent || '',
        product_id: position.product_id || '',
        kind: position.kind || ''
      }));
    }

    return data;
  }

  /**
   * Update sync status
   * @param {Object} syncResult - Sync result from Fakturownia
   */
  updateSyncStatus(syncResult) {
    if (syncResult.success) {
      this.fakturownia_id = syncResult.id;
      this.fakturownia_url = syncResult.url || '';
      this.fakturownia_status = syncResult.status || '';
      this.last_sync = new Date().toISOString();
      this.sync_errors = [];
    } else {
      this.sync_errors.push({
        timestamp: new Date().toISOString(),
        error: syncResult.error,
        details: syncResult.details || ''
      });
    }
  }

  /**
   * Mark invoice as approved
   * @param {string} approvedBy - Who approved the invoice
   */
  approve(approvedBy) {
    this.approval_status = 'approved';
    this.approved_by = approvedBy;
    this.approved_at = new Date().toISOString();
    this.workflow_status = 'approved';
  }

  /**
   * Mark invoice as rejected
   * @param {string} reason - Rejection reason
   */
  reject(reason) {
    this.approval_status = 'rejected';
    this.workflow_status = 'rejected';
    this.internal_note = (this.internal_note ? this.internal_note + '\n' : '') +
                        `Rejected: ${reason} (${new Date().toISOString()})`;
  }

  /**
   * Mark invoice as paid
   * @param {string} paymentDate - Payment date
   * @param {string} paymentMethod - Payment method
   */
  markAsPaid(paymentDate = null, paymentMethod = null) {
    this.paid = '1';
    this.status = 'paid';
    this.workflow_status = 'paid';

    if (paymentDate) {
      this.payment_to = paymentDate;
    }

    if (paymentMethod) {
      this.payment_type = paymentMethod;
    }
  }

  /**
   * Clone invoice
   * @returns {Invoice} - Cloned invoice
   */
  clone() {
    return new Invoice(this.toJSON());
  }

  /**
   * Create a correction invoice from this invoice
   * @param {Object} correctionData - Correction data
   * @returns {Invoice} - Correction invoice
   */
  createCorrection(correctionData) {
    const correction = this.clone();

    correction.kind = 'correction';
    correction.invoice_id = this.fakturownia_id || this.fileHash;
    correction.from_invoice_id = this.fakturownia_id || this.fileHash;
    correction.corrected_content_before = correctionData.before || '';
    correction.corrected_content_after = correctionData.after || '';
    correction.number = correctionData.number || `${this.number}-KOREKTA`;
    correction.issue_date = correctionData.issue_date || new Date().toISOString().split('T')[0];

    // Reset sync status for new correction
    correction.fakturownia_id = null;
    correction.fakturownia_url = '';
    correction.fakturownia_status = '';
    correction.last_sync = null;
    correction.sync_errors = [];
    correction.workflow_status = 'draft';
    correction.approval_status = 'pending';

    // Generate new file hash
    correction.fileHash = null;
    correction.analysisTimestamp = new Date().toISOString();

    return correction;
  }
}

// Export for ES modules
export default Invoice;
