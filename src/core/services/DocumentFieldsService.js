/**
 * DocumentFieldsService - Additional document field management
 * Extracted and refactored from core/prompt_generators/document_fields.js
 * Handles additional fields that complement AccountingFields
 */

import {
  ADDITIONAL_DOCUMENT_FIELDS,
  ADDITIONAL_FIELD_DESCRIPTIONS,
  ADDITI<PERSON>AL_FIELDS_WITH_FIXED_VALUES,
  DOCUMENT_TYPES_REQUIRING_ADDITIONAL_FIELDS,
  REQUIRED_ADDITIONAL_FIELDS
} from '../config/fieldDefinitions.js';

export class DocumentFieldsService {
  constructor() {
    this.additionalFields = ADDITIONAL_DOCUMENT_FIELDS;
    this.fieldDescriptions = ADDITIONAL_FIELD_DESCRIPTIONS;
    this.fieldsWithFixedValues = ADDITIONAL_FIELDS_WITH_FIXED_VALUES;
    this.applicableDocumentTypes = DOCUMENT_TYPES_REQUIRING_ADDITIONAL_FIELDS;
    this.requiredFields = REQUIRED_ADDITIONAL_FIELDS;
  }

  /**
   * Check if document type requires additional fields
   * @param {string} documentKind - The document kind
   * @returns {boolean} - Whether this document type needs additional fields
   */
  isApplicableForDocumentType(documentKind) {
    return this.applicableDocumentTypes.includes(documentKind);
  }

  /**
   * Get additional fields for a specific document kind
   * @param {string} documentKind - The document kind
   * @returns {Array} - Array of additional field names
   */
  getAdditionalFields(documentKind) {
    if (!this.isApplicableForDocumentType(documentKind)) {
      return [];
    }
    return [...this.additionalFields];
  }

  /**
   * Get field descriptions for additional fields
   * @param {string} documentKind - The document kind
   * @returns {Object} - Field descriptions for additional fields
   */
  getFieldDescriptions(documentKind) {
    if (!this.isApplicableForDocumentType(documentKind)) {
      return {};
    }

    const descriptions = {};
    for (const field of this.additionalFields) {
      descriptions[field] = this.fieldDescriptions[field];
    }
    return descriptions;
  }

  /**
   * Check if field has fixed values
   * @param {string} field - The field name
   * @returns {boolean} - Whether field has fixed values
   */
  hasFixedValues(field) {
    return this.fieldsWithFixedValues.hasOwnProperty(field);
  }

  /**
   * Get fixed values for a field
   * @param {string} field - The field name
   * @returns {Array} - Array of allowed values
   */
  getFixedValuesForField(field) {
    return this.fieldsWithFixedValues[field] || [];
  }

  /**
   * Generate field value with description and fixed values
   * @param {string} field - Field name
   * @param {string} description - Field description
   * @returns {string} - Formatted field value
   */
  generateFieldValue(field, description) {
    if (this.hasFixedValues(field)) {
      const fixedValues = this.getFixedValuesForField(field);
      return `(${description}) | [${fixedValues.join('|')}]`;
    }
    return `(${description})`;

  }

  /**
   * Generate expected JSON structure for additional fields
   * @param {string} documentKind - The document kind
   * @returns {Object} - Expected JSON structure with proper descriptions
   */
  generateExpectedJsonStructure(documentKind) {
    if (!this.isApplicableForDocumentType(documentKind)) {
      return {};
    }

    const structure = {};
    const descriptions = this.getFieldDescriptions(documentKind);

    // Process each additional field using proper logic
    for (const field of this.additionalFields) {
      const description = descriptions[field] || this.fieldDescriptions[field];

      if (description) {
        structure[field] = this.generateFieldValue(field, description);
      } else {
        // Fallback for fields without descriptions
        if (this.hasFixedValues(field)) {
          const allowedValues = this.getFixedValuesForField(field);
          structure[field] = `(${field}) | [${allowedValues.join('|')}]`;
        } else {
          structure[field] = `(${field})`;
        }
      }
    }

    return structure;
  }

  /**
   * Generate AI prompt for additional fields extraction
   * @param {string} documentKind - The document kind
   * @param {string} documentText - The document text to analyze
   * @param {string} detectedLang - The detected language of the document
   * @returns {Object} - Prompt configuration
   */
  generateAIPrompt(documentKind, documentText, detectedLang = 'pl') {
    if (!this.isApplicableForDocumentType(documentKind)) {
      return null;
    }

    const jsonStructure = JSON.stringify(this.generateExpectedJsonStructure(documentKind), null, 2);
    const descriptions = this.generateDescriptionListText(documentKind);

    const systemPrompt = `You are an expert in accounting document analysis specializing in additional document metadata extraction.

TASK: Extract additional document fields that complement basic accounting information.

CRITICAL INSTRUCTIONS:
1. Field names in JSON output must remain in English as defined in the structure
2. Field values should be extracted in the original document language (${detectedLang})
3. Extract only fields that you can find in the document
4. If you cannot find a value for a field, omit it in the response
5. Numeric values (total_net, total_vat, total_gross) should be numbers without currency symbols
6. Dates should be in YYYY-MM-DD format

ADDITIONAL FIELDS STRUCTURE:
${jsonStructure}

FIELD DESCRIPTIONS:
${descriptions}`;

    const prompt = `Extract additional document metadata from this ${documentKind} document:

${documentText}

Return the extracted additional fields in the exact JSON structure provided above.`;

    return {
      prompt,
      systemPrompt,
      options: {
        temperature: 0.1,
        max_tokens: 1500
      }
    };
  }

  /**
   * Generate a description list text for AI models for additional fields
   * @param {string} documentKind - The document kind
   * @returns {string} - Description list text for additional fields
   */
  generateDescriptionListText(documentKind) {
    if (!this.isApplicableForDocumentType(documentKind)) {
      return '';
    }

    const descriptions = this.getFieldDescriptions(documentKind);
    let text = `Additional fields for document type "${documentKind}":\n`;

    // Output all additional field descriptions
    for (const [field, description] of Object.entries(descriptions)) {
      text += `- "${field}": ${description}\n`;
    }

    return text;
  }

  /**
   * Validate and clean extracted additional fields
   * @param {Object} extractedData - Extracted data to validate
   * @param {string} documentKind - The document kind
   * @returns {Object} - Validation result with cleaned data
   */
  validateAndCleanData(extractedData, documentKind) {
    if (!this.isApplicableForDocumentType(documentKind)) {
      return { isValid: true, cleanedData: {}, errors: [] };
    }

    const cleanedData = {};
    const errors = [];

    // Validate and clean each field
    for (const field of this.additionalFields) {
      if (extractedData[field] !== undefined && extractedData[field] !== null && extractedData[field] !== '') {
        const value = extractedData[field];

        // Check fixed values first
        if (this.hasFixedValues(field)) {
          const allowedValues = this.getFixedValuesForField(field);
          if (allowedValues.includes(String(value))) {
            cleanedData[field] = String(value);
          } else {
            errors.push(`Invalid value for ${field}: ${value}. Allowed values: ${allowedValues.join(', ')}`);
          }
          continue;
        }

        switch (field) {
          case 'total_net':
          case 'total_vat':
          case 'total_gross':
            // Clean and validate numeric values
            const numericValue = this.cleanNumericValue(value);
            if (numericValue !== null) {
              cleanedData[field] = numericValue;
            } else {
              errors.push(`Invalid numeric value for ${field}: ${value}`);
            }
            break;

          case 'accounting_date':
            // Validate date format
            const cleanedDate = this.cleanDateValue(value);
            if (cleanedDate) {
              cleanedData[field] = cleanedDate;
            } else {
              errors.push(`Invalid date format for ${field}: ${value}`);
            }
            break;

          case 'seller_email':
          case 'buyer_email':
            // Basic email validation
            if (this.isValidEmail(value)) {
              cleanedData[field] = value.trim().toLowerCase();
            } else {
              errors.push(`Invalid email format for ${field}: ${value}`);
            }
            break;

          default:
            // For other fields, just trim and store
            cleanedData[field] = String(value).trim();
            break;
        }
      }
    }

    return {
      isValid: errors.length === 0,
      cleanedData,
      errors
    };
  }

  /**
   * Clean numeric value from string
   * @param {string|number} value - Value to clean
   * @returns {number|null} - Cleaned numeric value or null if invalid
   */
  cleanNumericValue(value) {
    if (typeof value === 'number') {
      return value;
    }

    if (typeof value === 'string') {
      // Remove currency symbols, spaces, and convert comma to dot
      const cleaned = value
        .replace(/[^\d,.-]/g, '')
        .replace(',', '.')
        .trim();

      const parsed = parseFloat(cleaned);
      return isNaN(parsed) ? null : parsed;
    }

    return null;
  }

  /**
   * Clean date value
   * @param {string} value - Date value to clean
   * @returns {string|null} - Cleaned date in YYYY-MM-DD format or null if invalid
   */
  cleanDateValue(value) {
    if (!value) { return null; }

    // Try to parse various date formats
    const dateStr = String(value).trim();

    // Try ISO format first
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
      return dateStr;
    }

    // Try other common formats
    const date = new Date(dateStr);
    if (!isNaN(date.getTime())) {
      return date.toISOString().split('T')[0];
    }

    return null;
  }

  /**
   * Basic email validation
   * @param {string} email - Email to validate
   * @returns {boolean} - Whether email is valid
   */
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Get required additional fields for a specific document kind
   * @param {string} documentKind - The document kind
   * @returns {Array} - Array of required additional field names
   */
  getRequiredFields(documentKind) {
    if (!this.isApplicableForDocumentType(documentKind)) {
      return [];
    }

    return this.requiredFields[documentKind] || this.requiredFields.default || [];
  }
}

// Create singleton instance
const documentFieldsService = new DocumentFieldsService();

// Export for ES modules
export default documentFieldsService;

// Export to window object for non-module scripts
if (typeof window !== 'undefined') {
  window.DocumentFieldsService = DocumentFieldsService;
  window.documentFieldsService = documentFieldsService;
}

// ===== LOCAL TESTING (Node.js equivalent of Python's if __name__ == '__main__') =====
if (typeof require !== 'undefined' && require.main === module) {
  console.log('🧪 Running local tests for DocumentFieldsService...');

  // Create test instance
  const service = new DocumentFieldsService();

  // Test core functionality
  console.log('✅ Test 1: Document type applicability');
  console.log('VAT document applicable:', service.isApplicableForDocumentType('vat'));
  console.log('Receipt document applicable:', service.isApplicableForDocumentType('receipt'));
  console.log('Unknown document applicable:', service.isApplicableForDocumentType('unknown'));

  console.log('✅ Test 2: Additional fields retrieval');
  const vatFields = service.getAdditionalFields('vat');
  console.log('VAT additional fields:', vatFields);
  console.log('VAT fields count:', vatFields.length);

  console.log('✅ Test 3: Field descriptions');
  const descriptions = service.getFieldDescriptions('vat');
  console.log('Field descriptions count:', Object.keys(descriptions).length);

  console.log('✅ Test 4: JSON structure generation');
  const structure = service.generateExpectedJsonStructure('vat');
  console.log('Generated structure fields:', Object.keys(structure));

  console.log('✅ Test 5: Data validation and cleaning');
  const testData = {
    total_net: '1,234.56 PLN',
    total_gross: '1,518.51',
    seller_email: '<EMAIL>',
    accounting_date: '2025-01-27'
  };
  const validation = service.validateAndCleanData(testData, 'vat');
  console.log('Validation result:', validation.isValid);
  console.log('Cleaned data:', validation.cleanedData);
  console.log('Errors:', validation.errors);

  console.log('✅ Test 6: Edge cases');
  console.log('Invalid email:', service.isValidEmail('invalid-email'));
  console.log('Valid email:', service.isValidEmail('<EMAIL>'));
  console.log('Clean numeric from string:', service.cleanNumericValue('1,234.56 PLN'));
  console.log('Clean date:', service.cleanDateValue('2025-01-27'));

  console.log('✅ All tests completed for DocumentFieldsService');
}
