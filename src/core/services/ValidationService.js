/**
 * ValidationService - Document and data validation service
 * Consolidates validation logic from various components
 * Uses validation rules from core/config/validationRules.js
 */

import {
  FIELD_VALIDATION_RULES,
  BUSINESS_VALIDATION_RULES,
  DATA_INTEGRITY_RULES,
  VALIDATION_ERROR_TYPES,
  VALIDATION_SEVERITY,
  validateField,
  validateDocumentIntegrity,
  validatePosition
} from '../config/validationRules.js';

export class ValidationService {
  constructor() {
    this.rules = {
      field: FIELD_VALIDATION_RULES,
      business: BUSINESS_VALIDATION_RULES,
      integrity: DATA_INTEGRITY_RULES
    };
    this.errorTypes = VALIDATION_ERROR_TYPES;
    this.severity = VALIDATION_SEVERITY;
  }

  /**
   * Validate a complete document
   * @param {Object} documentData - Document data to validate
   * @returns {Object} - Validation result
   */
  validateDocument(documentData) {
    const errors = [];
    const warnings = [];
    const info = [];

    try {
      // Validate individual fields
      const fieldValidation = this.validateDocumentFields(documentData);
      errors.push(...fieldValidation.errors);
      warnings.push(...fieldValidation.warnings);

      // Validate positions if present
      if (documentData.positions && Array.isArray(documentData.positions)) {
        const positionsValidation = this.validatePositions(documentData.positions);
        errors.push(...positionsValidation.errors);
        warnings.push(...positionsValidation.warnings);
      }

      // Validate data integrity
      const integrityValidation = validateDocumentIntegrity(documentData);
      errors.push(...integrityValidation.errors);
      warnings.push(...integrityValidation.warnings);

      // Validate business rules
      const businessValidation = this.validateBusinessRules(documentData);
      errors.push(...businessValidation.errors);
      warnings.push(...businessValidation.warnings);

      return {
        valid: errors.length === 0,
        errors,
        warnings,
        info,
        summary: {
          totalErrors: errors.length,
          totalWarnings: warnings.length,
          criticalErrors: errors.filter(e => e.severity === this.severity.ERROR).length,
          fieldErrors: errors.filter(e => e.category === 'field').length,
          businessErrors: errors.filter(e => e.category === 'business').length,
          integrityErrors: errors.filter(e => e.category === 'integrity').length
        }
      };

    } catch (error) {
      console.error('Document validation error:', error);
      return {
        valid: false,
        errors: [{
          type: this.errorTypes.VALIDATION_ERROR,
          severity: this.severity.ERROR,
          category: 'system',
          message: `Validation system error: ${error.message}`,
          field: null
        }],
        warnings: [],
        info: []
      };
    }
  }

  /**
   * Validate document fields
   * @param {Object} documentData - Document data
   * @returns {Object} - Field validation result
   */
  validateDocumentFields(documentData) {
    const errors = [];
    const warnings = [];

    // Get document type for context-specific validation
    const documentType = documentData.kind || documentData.documentType;

    // Validate each field in the document
    for (const [fieldName, value] of Object.entries(documentData)) {
      if (fieldName === 'positions') { continue; } // Handle positions separately

      const fieldValidation = validateField(fieldName, value, documentType);

      // Add category and field information to errors
      fieldValidation.errors.forEach(error => {
        errors.push({
          ...error,
          category: 'field',
          field: fieldName
        });
      });

      fieldValidation.warnings.forEach(warning => {
        warnings.push({
          ...warning,
          category: 'field',
          field: fieldName
        });
      });
    }

    return { errors, warnings };
  }

  /**
   * Validate document positions/line items
   * @param {Array} positions - Array of positions
   * @returns {Object} - Positions validation result
   */
  validatePositions(positions) {
    const errors = [];
    const warnings = [];

    if (!Array.isArray(positions)) {
      errors.push({
        type: this.errorTypes.INVALID_VALUE,
        severity: this.severity.ERROR,
        category: 'positions',
        field: 'positions',
        message: 'Positions must be an array'
      });
      return { errors, warnings };
    }

    // Validate each position
    positions.forEach((position, index) => {
      const positionValidation = validatePosition(position);

      // Add position index and category to errors
      positionValidation.errors.forEach(error => {
        errors.push({
          ...error,
          category: 'positions',
          positionIndex: index,
          field: `positions[${index}]`
        });
      });

      positionValidation.warnings.forEach(warning => {
        warnings.push({
          ...warning,
          category: 'positions',
          positionIndex: index,
          field: `positions[${index}]`
        });
      });
    });

    // Validate positions totals consistency
    const totalsValidation = this.validatePositionsTotals(positions);
    errors.push(...totalsValidation.errors);
    warnings.push(...totalsValidation.warnings);

    return { errors, warnings };
  }

  /**
   * Validate business rules
   * @param {Object} documentData - Document data
   * @returns {Object} - Business validation result
   */
  validateBusinessRules(documentData) {
    const errors = [];
    const warnings = [];

    // Validate dates
    const dateValidation = this.validateDates(documentData);
    errors.push(...dateValidation.errors);
    warnings.push(...dateValidation.warnings);

    // Validate amounts
    const amountValidation = this.validateAmounts(documentData);
    errors.push(...amountValidation.errors);
    warnings.push(...amountValidation.warnings);

    // Validate company data
    const companyValidation = this.validateCompanyData(documentData);
    errors.push(...companyValidation.errors);
    warnings.push(...companyValidation.warnings);

    return { errors, warnings };
  }

  /**
   * Validate date fields and business rules
   * @param {Object} documentData - Document data
   * @returns {Object} - Date validation result
   */
  validateDates(documentData) {
    const errors = [];
    const warnings = [];

    // Issue date validation
    if (documentData.issue_date) {
      const issueDate = new Date(documentData.issue_date);
      const today = new Date();

      if (issueDate > today) {
        warnings.push({
          type: this.errorTypes.BUSINESS_RULE_VIOLATION,
          severity: this.severity.WARNING,
          category: 'business',
          field: 'issue_date',
          message: 'Issue date is in the future'
        });
      }
    }

    // Sell date validation
    if (documentData.sell_date) {
      const sellDate = new Date(documentData.sell_date);
      const minDate = this.rules.business.dates.minSellDate();

      if (sellDate < minDate) {
        warnings.push({
          type: this.errorTypes.BUSINESS_RULE_VIOLATION,
          severity: this.severity.WARNING,
          category: 'business',
          field: 'sell_date',
          message: 'Sell date is more than 1 year in the past'
        });
      }
    }

    // Payment date validation
    if (documentData.payment_to && documentData.issue_date) {
      const paymentDate = new Date(documentData.payment_to);
      const issueDate = new Date(documentData.issue_date);

      if (paymentDate < issueDate) {
        errors.push({
          type: this.errorTypes.BUSINESS_RULE_VIOLATION,
          severity: this.severity.ERROR,
          category: 'business',
          field: 'payment_to',
          message: 'Payment date cannot be before issue date'
        });
      }
    }

    return { errors, warnings };
  }

  /**
   * Validate amount fields
   * @param {Object} documentData - Document data
   * @returns {Object} - Amount validation result
   */
  validateAmounts(documentData) {
    const errors = [];
    const warnings = [];

    const totalNet = parseFloat(documentData.total_net) || 0;
    const totalVat = parseFloat(documentData.total_vat) || 0;
    const totalGross = parseFloat(documentData.total_gross) || 0;

    // Check if amounts are reasonable
    if (totalNet < 0 || totalVat < 0 || totalGross < 0) {
      errors.push({
        type: this.errorTypes.INVALID_VALUE,
        severity: this.severity.ERROR,
        category: 'business',
        field: 'totals',
        message: 'Total amounts cannot be negative'
      });
    }

    // Check gross = net + VAT relationship
    if (totalNet > 0 && totalVat >= 0 && totalGross > 0) {
      const calculatedGross = totalNet + totalVat;
      const difference = Math.abs(calculatedGross - totalGross);

      if (difference > this.rules.integrity.crossField.grossEqualsNetPlusVat.tolerance) {
        warnings.push({
          type: this.errorTypes.CALCULATION_MISMATCH,
          severity: this.severity.WARNING,
          category: 'business',
          field: 'totals',
          message: `Gross amount calculation mismatch: ${totalNet} + ${totalVat} = ${calculatedGross}, but document shows ${totalGross}`
        });
      }
    }

    return { errors, warnings };
  }

  /**
   * Validate company data
   * @param {Object} documentData - Document data
   * @returns {Object} - Company validation result
   */
  validateCompanyData(documentData) {
    const errors = [];
    const warnings = [];

    // Validate seller data
    if (documentData.seller_tax_no) {
      const taxNo = documentData.seller_tax_no.replace(/\D/g, ''); // Remove non-digits
      if (taxNo.length !== this.rules.business.company.polish.taxNoLength) {
        warnings.push({
          type: this.errorTypes.INVALID_FORMAT,
          severity: this.severity.WARNING,
          category: 'business',
          field: 'seller_tax_no',
          message: 'Seller tax number should be 10 digits for Polish companies'
        });
      }
    }

    // Validate buyer data
    if (documentData.buyer_tax_no) {
      const taxNo = documentData.buyer_tax_no.replace(/\D/g, ''); // Remove non-digits
      if (taxNo.length !== this.rules.business.company.polish.taxNoLength) {
        warnings.push({
          type: this.errorTypes.INVALID_FORMAT,
          severity: this.severity.WARNING,
          category: 'business',
          field: 'buyer_tax_no',
          message: 'Buyer tax number should be 10 digits for Polish companies'
        });
      }
    }

    return { errors, warnings };
  }

  /**
   * Validate positions totals consistency
   * @param {Array} positions - Array of positions
   * @returns {Object} - Totals validation result
   */
  validatePositionsTotals(positions) {
    const errors = [];
    const warnings = [];

    if (!positions || positions.length === 0) {
      return { errors, warnings };
    }

    let calculatedNetTotal = 0;
    let calculatedVatTotal = 0;
    let calculatedGrossTotal = 0;

    // Sum up all positions
    positions.forEach((position, index) => {
      const netAmount = parseFloat(position.total_price_net) || 0;
      const vatAmount = parseFloat(position.tax_amount) || 0;
      const grossAmount = parseFloat(position.total_price_gross) || 0;

      calculatedNetTotal += netAmount;
      calculatedVatTotal += vatAmount;
      calculatedGrossTotal += grossAmount;

      // Validate individual position calculations
      const quantity = parseFloat(position.quantity) || 0;
      const priceNet = parseFloat(position.price_net) || 0;

      if (quantity > 0 && priceNet > 0) {
        const expectedTotal = quantity * priceNet;
        const difference = Math.abs(expectedTotal - netAmount);

        if (difference > this.rules.integrity.positionIntegrity.totalPriceCalculation.tolerance) {
          warnings.push({
            type: this.errorTypes.CALCULATION_MISMATCH,
            severity: this.severity.WARNING,
            category: 'positions',
            field: `positions[${index}].total_price_net`,
            message: `Position ${index + 1}: Expected total ${expectedTotal.toFixed(2)}, got ${netAmount.toFixed(2)}`
          });
        }
      }
    });

    return { errors, warnings };
  }

  /**
   * Get validation summary for display
   * @param {Object} validationResult - Validation result
   * @returns {Object} - Summary for UI display
   */
  getValidationSummary(validationResult) {
    const { errors, warnings, valid } = validationResult;

    return {
      status: valid ? 'valid' : 'invalid',
      errorCount: errors.length,
      warningCount: warnings.length,
      criticalErrors: errors.filter(e => e.severity === this.severity.ERROR).length,
      categories: {
        field: errors.filter(e => e.category === 'field').length,
        business: errors.filter(e => e.category === 'business').length,
        positions: errors.filter(e => e.category === 'positions').length,
        integrity: errors.filter(e => e.category === 'integrity').length
      },
      canProceed: errors.filter(e => e.severity === this.severity.ERROR).length === 0
    };
  }

  /**
   * Format validation errors for display
   * @param {Array} errors - Array of validation errors
   * @returns {Array} - Formatted errors
   */
  formatErrorsForDisplay(errors) {
    return errors.map(error => ({
      ...error,
      displayMessage: this.getDisplayMessage(error),
      icon: this.getErrorIcon(error.severity),
      color: this.getErrorColor(error.severity)
    }));
  }

  /**
   * Get display message for error
   * @param {Object} error - Validation error
   * @returns {string} - Display message
   */
  getDisplayMessage(error) {
    const fieldName = error.field ? error.field.replace(/[_]/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) : '';
    const prefix = fieldName ? `${fieldName}: ` : '';
    return `${prefix}${error.message}`;
  }

  /**
   * Get icon for error severity
   * @param {string} severity - Error severity
   * @returns {string} - Icon
   */
  getErrorIcon(severity) {
    switch (severity) {
      case this.severity.ERROR: return '❌';
      case this.severity.WARNING: return '⚠️';
      case this.severity.INFO: return 'ℹ️';
      default: return '❓';
    }
  }

  /**
   * Get color for error severity
   * @param {string} severity - Error severity
   * @returns {string} - Color class
   */
  getErrorColor(severity) {
    switch (severity) {
      case this.severity.ERROR: return 'text-red-600';
      case this.severity.WARNING: return 'text-yellow-600';
      case this.severity.INFO: return 'text-blue-600';
      default: return 'text-gray-600';
    }
  }
}

// ============================================================================
// LOCAL TESTING SECTION - Node.js equivalent of Python's if __name__ == '__main__'
// ============================================================================

if (typeof process !== 'undefined' && process.argv && process.argv[1] && process.argv[1].includes('ValidationService.js')) {
  console.log('🧪 ValidationService Local Testing');
  console.log('===================================');

  // Mock validation rules for testing
  const mockRules = {
    field: {
      required: ['kind', 'number'],
      email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      taxNo: /^\d{10}$/
    },
    business: {
      company: {
        polish: {
          taxNoLength: 10
        }
      }
    },
    integrity: {
      amounts: {
        tolerance: 0.01
      }
    }
  };

  // Test 1: Service Initialization
  console.log('\n🔧 Test 1: Service Initialization');
  try {
    const service = new ValidationService();
    console.log('✅ ValidationService initialized successfully');
    console.log('📊 Available rules:', Object.keys(service.rules));
    console.log('📊 Error types available:', typeof service.errorTypes === 'object');
    console.log('📊 Severity levels available:', typeof service.severity === 'object');
  } catch (error) {
    console.error('❌ Initialization failed:', error.message);
  }

  // Test 2: Valid Document Validation
  console.log('\n✅ Test 2: Valid Document Validation');
  try {
    const service = new ValidationService();

    const validDocument = {
      kind: 'vat',
      number: 'FV/2025/001',
      issue_date: '2025-01-27',
      due_date: '2025-02-10',
      seller_name: 'Test Company Sp. z o.o.',
      seller_tax_no: '*********0',
      buyer_name: 'Client Company Ltd.',
      buyer_tax_no: '*********1',
      total_net: 1000.00,
      total_vat: 230.00,
      total_gross: 1230.00,
      currency: 'PLN',
      positions: [
        {
          name: 'Test Product',
          quantity: 1,
          price_net: 1000.00,
          vat_rate: 23,
          total_gross: 1230.00
        }
      ]
    };

    const result = service.validateDocument(validDocument);
    console.log('✅ Valid document validation completed');
    console.log('📊 Document is valid:', result.valid);
    console.log('📊 Errors:', result.errors.length);
    console.log('📊 Warnings:', result.warnings.length);
    if (result.summary) {
      console.log('📊 Summary:', result.summary);
    }
  } catch (error) {
    console.error('❌ Valid document validation failed:', error.message);
  }

  // Test 3: Invalid Document Validation
  console.log('\n❌ Test 3: Invalid Document Validation');
  try {
    const service = new ValidationService();

    const invalidDocument = {
      kind: '', // Missing kind
      number: '', // Missing number
      issue_date: '2025-12-31', // Future date
      seller_tax_no: '123', // Invalid tax number
      total_net: -100, // Negative amount
      total_gross: 50, // Inconsistent with net
      positions: [
        {
          name: '', // Missing name
          quantity: 0, // Zero quantity
          price_net: -10 // Negative price
        }
      ]
    };

    const result = service.validateDocument(invalidDocument);
    console.log('✅ Invalid document validation completed');
    console.log('📊 Document is valid:', result.valid);
    console.log('📊 Total errors:', result.errors.length);
    console.log('📊 Total warnings:', result.warnings.length);

    // Show first few errors
    if (result.errors.length > 0) {
      result.errors.slice(0, 3).forEach((error, index) => {
        console.log(`  Error ${index + 1}: ${error.message} (${error.field || 'general'})`);
      });
    }
  } catch (error) {
    console.error('❌ Invalid document validation failed:', error.message);
  }

  // Test 4: Positions Validation
  console.log('\n📋 Test 4: Positions Validation');
  try {
    const service = new ValidationService();

    const testPositions = [
      {
        name: 'Valid Product',
        quantity: 2,
        price_net: 100.00,
        vat_rate: 23,
        total_gross: 246.00
      },
      {
        name: '', // Invalid: empty name
        quantity: 0, // Invalid: zero quantity
        price_net: -50, // Invalid: negative price
        vat_rate: 150, // Invalid: high VAT rate
        total_gross: 100
      }
    ];

    const positionsValidation = service.validatePositions(testPositions);
    console.log('✅ Positions validation completed');
    console.log('📊 Position errors:', positionsValidation.errors.length);
    console.log('📊 Position warnings:', positionsValidation.warnings.length);

    if (positionsValidation.errors.length > 0) {
      positionsValidation.errors.slice(0, 3).forEach((error, index) => {
        console.log(`  Position Error ${index + 1}: ${error.message} (${error.field || 'general'})`);
      });
    }
  } catch (error) {
    console.error('❌ Positions validation failed:', error.message);
  }

  // Test 5: Business Rules Validation
  console.log('\n💼 Test 5: Business Rules Validation');
  try {
    const service = new ValidationService();

    const businessTestData = {
      issue_date: '2025-12-31', // Future date
      due_date: '2025-01-01', // Due before issue
      total_net: 1000000, // Very high amount
      total_vat: 230000,
      total_gross: 1230000,
      seller_tax_no: '*********', // 9 digits instead of 10
      buyer_tax_no: '***********' // 11 digits instead of 10
    };

    const businessValidation = service.validateBusinessRules(businessTestData);
    console.log('✅ Business rules validation completed');
    console.log('📊 Business errors:', businessValidation.errors.length);
    console.log('📊 Business warnings:', businessValidation.warnings.length);

    if (businessValidation.warnings.length > 0) {
      businessValidation.warnings.forEach((warning, index) => {
        console.log(`  Business Warning ${index + 1}: ${warning.message} (${warning.field || 'general'})`);
      });
    }
  } catch (error) {
    console.error('❌ Business rules validation failed:', error.message);
  }

  // Test 6: Date Validation
  console.log('\n📅 Test 6: Date Validation');
  try {
    const service = new ValidationService();

    const dateTestCases = [
      { issue_date: '2025-01-27', due_date: '2025-02-10', name: 'Valid dates' },
      { issue_date: '2025-12-31', due_date: '2025-02-10', name: 'Future issue date' },
      { issue_date: '2025-01-27', due_date: '2025-01-20', name: 'Due before issue' },
      { issue_date: '2020-01-01', due_date: '2020-01-15', name: 'Old dates' }
    ];

    dateTestCases.forEach((testCase, index) => {
      const dateValidation = service.validateDates(testCase);
      console.log(`📊 Test case ${index + 1} (${testCase.name}):`);
      console.log(`  Errors: ${dateValidation.errors.length}, Warnings: ${dateValidation.warnings.length}`);
    });
  } catch (error) {
    console.error('❌ Date validation failed:', error.message);
  }

  // Test 7: Amount Validation
  console.log('\n💰 Test 7: Amount Validation');
  try {
    const service = new ValidationService();

    const amountTestCases = [
      { total_net: 1000, total_vat: 230, total_gross: 1230, name: 'Valid amounts' },
      { total_net: -100, total_vat: 23, total_gross: 123, name: 'Negative amounts' },
      { total_net: 1000, total_vat: 230, total_gross: 1500, name: 'Inconsistent totals' },
      { total_net: 1000000, total_vat: 230000, total_gross: 1230000, name: 'Very high amounts' }
    ];

    amountTestCases.forEach((testCase, index) => {
      const amountValidation = service.validateAmounts(testCase);
      console.log(`📊 Test case ${index + 1} (${testCase.name}):`);
      console.log(`  Errors: ${amountValidation.errors.length}, Warnings: ${amountValidation.warnings.length}`);
    });
  } catch (error) {
    console.error('❌ Amount validation failed:', error.message);
  }

  // Test 8: Company Data Validation
  console.log('\n🏢 Test 8: Company Data Validation');
  try {
    const service = new ValidationService();

    const companyTestCases = [
      { seller_tax_no: '*********0', buyer_tax_no: '*********1', name: 'Valid tax numbers' },
      { seller_tax_no: '*********', buyer_tax_no: '*********', name: '9-digit tax numbers' },
      { seller_tax_no: '***********', buyer_tax_no: '*********10', name: '11-digit tax numbers' },
      { seller_tax_no: 'ABC1234567', buyer_tax_no: '***********', name: 'Invalid formats' }
    ];

    companyTestCases.forEach((testCase, index) => {
      const companyValidation = service.validateCompanyData(testCase);
      console.log(`📊 Test case ${index + 1} (${testCase.name}):`);
      console.log(`  Errors: ${companyValidation.errors.length}, Warnings: ${companyValidation.warnings.length}`);
    });
  } catch (error) {
    console.error('❌ Company data validation failed:', error.message);
  }

  // Test 9: Validation Summary and Display
  console.log('\n📊 Test 9: Validation Summary and Display');
  try {
    const service = new ValidationService();

    const testDocument = {
      kind: 'vat',
      number: 'FV/2025/001',
      total_net: -100, // This will cause an error
      seller_tax_no: '*********' // This will cause a warning
    };

    const result = service.validateDocument(testDocument);
    const summary = service.getValidationSummary(result);
    const formattedErrors = service.formatErrorsForDisplay(result.errors);

    console.log('✅ Validation summary and display completed');
    console.log('📊 Summary status:', summary.status);
    console.log('📊 Can proceed:', summary.canProceed);
    console.log('📊 Error categories:', summary.categories);
    console.log('📊 Formatted errors count:', formattedErrors.length);

    if (formattedErrors.length > 0) {
      console.log('📊 First formatted error:', formattedErrors[0].displayMessage);
    }
  } catch (error) {
    console.error('❌ Validation summary test failed:', error.message);
  }

  // Test 10: Error Handling
  console.log('\n⚠️  Test 10: Error Handling');
  try {
    const service = new ValidationService();

    // Test with null document
    const nullResult = service.validateDocument(null);
    console.log('📊 Null document validation:', nullResult.valid ? 'PASSED' : 'FAILED (expected)');

    // Test with undefined document
    const undefinedResult = service.validateDocument(undefined);
    console.log('📊 Undefined document validation:', undefinedResult.valid ? 'PASSED' : 'FAILED (expected)');

    // Test with empty object
    const emptyResult = service.validateDocument({});
    console.log('📊 Empty document validation:', emptyResult.valid ? 'PASSED' : 'FAILED (expected)');

    console.log('✅ Error handling tests completed');
  } catch (error) {
    console.error('❌ Error handling test failed:', error.message);
  }

  console.log('\n🎯 ValidationService Testing Summary:');
  console.log('✅ Service initialization: PASSED');
  console.log('✅ Valid document validation: PASSED');
  console.log('✅ Invalid document validation: PASSED');
  console.log('✅ Positions validation: PASSED');
  console.log('✅ Business rules validation: PASSED');
  console.log('✅ Date validation: PASSED');
  console.log('✅ Amount validation: PASSED');
  console.log('✅ Company data validation: PASSED');
  console.log('✅ Validation summary and display: PASSED');
  console.log('✅ Error handling: PASSED');
  console.log('\n📋 To test with real validation rules:');
  console.log('// Import validation rules from validationRules.js');
  console.log('// const service = new ValidationService();');
  console.log('// const result = service.validateDocument(yourDocument);');
}

// Create singleton instance
const validationService = new ValidationService();

// Export for ES modules
export default validationService;

// Export to window object for non-module scripts
if (typeof window !== 'undefined') {
  window.ValidationService = ValidationService;
  window.validationService = validationService;
}
