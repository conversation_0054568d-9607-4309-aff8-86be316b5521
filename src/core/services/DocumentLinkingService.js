/**
 * DocumentLinkingService - Document relationship analysis and linking
 * Extracted and refactored from components/document_linking.js
 * <PERSON>les finding and analyzing relationships between documents
 */

import { StorageAPI } from '../../api/StorageAPI.js';
import { DeepSeekAPI } from '../../api/DeepSeekAPI.js';

// Document relationship types
export const RELATIONSHIP_TYPES = {
  SAME_COMPANY: 'same_company',
  SAME_CONTRACT: 'same_contract',
  INVOICE_CORRECTION: 'invoice_correction',
  RELATED_PURCHASE: 'related_purchase',
  RELATED_SALE: 'related_sale',
  SIMILAR_CONTENT: 'similar_content',
  UNKNOWN: 'unknown'
};

export class DocumentLinkingService {
  constructor() {
    this.storageAPI = new StorageAPI();
    this.deepSeekAPI = new DeepSeekAPI();
    this.relationshipTypes = RELATIONSHIP_TYPES;
  }

  /**
   * Find related documents for a given document
   * @param {Object} document - Document to find relationships for
   * @returns {Promise<Array>} - Array of related documents
   */
  async findRelatedDocuments(document) {
    try {
      // Get all stored documents
      const [invoices, documents] = await Promise.all([
        this.storageAPI.getInvoices(),
        this.storageAPI.getDocuments()
      ]);

      // Combine all documents for analysis
      const allDocuments = [...invoices, ...documents].filter(doc =>
        doc.fileHash !== document.fileHash // Exclude the current document
      );

      // Find related documents based on metadata
      const relatedDocs = allDocuments.map(doc => {
        const relationship = this.determineRelationship(document, doc);
        if (relationship.type !== this.relationshipTypes.UNKNOWN) {
          return {
            documentId: doc.fileHash,
            documentName: doc.documentName || doc.fileName,
            documentType: doc.documentType || doc.kind || 'Unknown',
            relationship: relationship
          };
        }
        return null;
      }).filter(Boolean);

      return relatedDocs;
    } catch (error) {
      console.error('Error finding related documents:', error);
      return [];
    }
  }

  /**
   * Determine the relationship between two documents
   * Enhanced from components/document_linking.js
   * @param {Object} doc1 - First document
   * @param {Object} doc2 - Second document
   * @returns {Object} - Relationship object
   */
  determineRelationship(doc1, doc2) {
    // Initialize with unknown relationship
    let relationship = {
      type: this.relationshipTypes.UNKNOWN,
      confidence: 0,
      reason: ''
    };

    // Check for same company (seller or buyer) - enhanced field mapping
    const seller1 = this.normalizeNip(doc1.seller_tax_no || doc1.sellerNip || doc1.nip);
    const seller2 = this.normalizeNip(doc2.seller_tax_no || doc2.sellerNip || doc2.nip);
    const buyer1 = this.normalizeNip(doc1.buyer_tax_no || doc1.buyerNip);
    const buyer2 = this.normalizeNip(doc2.buyer_tax_no || doc2.buyerNip);

    if (seller1 && seller2 && seller1 === seller2) {
      relationship = {
        type: this.relationshipTypes.SAME_COMPANY,
        confidence: 0.8,
        reason: 'Same seller NIP',
        companyName: doc1.seller_name || doc1.seller || doc2.seller_name || doc2.seller,
        companyNip: seller1
      };
    } else if (buyer1 && buyer2 && buyer1 === buyer2) {
      relationship = {
        type: this.relationshipTypes.SAME_COMPANY,
        confidence: 0.8,
        reason: 'Same buyer NIP',
        companyName: doc1.buyer_name || doc1.buyer || doc2.buyer_name || doc2.buyer,
        companyNip: buyer1
      };
    }

    // Check for same contract reference - enhanced contract detection
    if (doc1.contract_reference && doc2.contract_reference &&
        doc1.contract_reference === doc2.contract_reference) {
      relationship = {
        type: this.relationshipTypes.SAME_CONTRACT,
        confidence: 0.9,
        reason: 'Same contract reference',
        contractReference: doc1.contract_reference
      };
    } else if (doc1.contract && doc2.contract && doc1.contract.id === doc2.contract.id) {
      // Legacy contract structure support
      relationship = {
        type: this.relationshipTypes.SAME_CONTRACT,
        confidence: 0.9,
        reason: 'Same contract ID',
        contractId: doc1.contract.id,
        contractType: doc1.contract.type || doc2.contract.type
      };
    }

    // Check for invoice correction - enhanced correction detection
    if ((doc1.kind === 'correction' || doc1.documentType === 'Invoice Correction') &&
        doc1.invoice_id && doc2.number && doc1.invoice_id.includes(doc2.number)) {
      relationship = {
        type: this.relationshipTypes.INVOICE_CORRECTION,
        confidence: 0.95,
        reason: 'Correction references original invoice',
        originalInvoice: doc2.number
      };
    } else if (doc1.documentType === 'Invoice Correction' && doc1.description &&
               doc1.description.includes(doc2.documentName || doc2.fileName)) {
      // Legacy description-based correction detection
      relationship = {
        type: this.relationshipTypes.INVOICE_CORRECTION,
        confidence: 0.95,
        reason: 'Correction references original invoice',
        originalInvoice: doc2.documentName || doc2.fileName
      };
    }

    // Check for related purchase/sale (same transaction from different sides)
    if (seller1 && buyer1 && seller2 && buyer2) {
      if (seller1 === buyer2 && buyer1 === seller2) {
        // Same transaction from opposite sides
        const income1 = parseInt(doc1.income) || 0;
        const income2 = parseInt(doc2.income) || 0;

        if (income1 === 1 && income2 === 0) {
          relationship = {
            type: this.relationshipTypes.RELATED_SALE,
            confidence: 0.85,
            reason: 'Same transaction - our sale to their purchase',
            transactionParty: doc2.seller_name || doc2.seller
          };
        } else if (income1 === 0 && income2 === 1) {
          relationship = {
            type: this.relationshipTypes.RELATED_PURCHASE,
            confidence: 0.85,
            reason: 'Same transaction - our purchase to their sale',
            transactionParty: doc2.buyer_name || doc2.buyer
          };
        }
      }
    }

    // Check for similar content (amounts, dates)
    if (relationship.type === this.relationshipTypes.UNKNOWN) {
      const similarity = this.calculateContentSimilarity(doc1, doc2);
      if (similarity.score > 0.7) {
        relationship = {
          type: this.relationshipTypes.SIMILAR_CONTENT,
          confidence: similarity.score,
          reason: similarity.reason,
          similarityFactors: similarity.factors
        };
      }
    }

    return relationship;
  }

  /**
   * Calculate content similarity between two documents
   * @param {Object} doc1 - First document
   * @param {Object} doc2 - Second document
   * @returns {Object} - Similarity result
   */
  calculateContentSimilarity(doc1, doc2) {
    let score = 0;
    let factors = [];
    const weights = {
      amount: 0.3,
      date: 0.2,
      description: 0.3,
      positions: 0.2
    };

    // Amount similarity
    const amount1 = parseFloat(doc1.total_gross) || 0;
    const amount2 = parseFloat(doc2.total_gross) || 0;
    if (amount1 > 0 && amount2 > 0) {
      const amountDiff = Math.abs(amount1 - amount2) / Math.max(amount1, amount2);
      if (amountDiff < 0.1) {
        score += weights.amount;
        factors.push('Similar amounts');
      }
    }

    // Date proximity
    if (doc1.issue_date && doc2.issue_date) {
      const date1 = new Date(doc1.issue_date);
      const date2 = new Date(doc2.issue_date);
      const daysDiff = Math.abs(date1 - date2) / (1000 * 60 * 60 * 24);
      if (daysDiff <= 7) {
        score += weights.date * (1 - daysDiff / 7);
        factors.push('Similar dates');
      }
    }

    // Description similarity
    if (doc1.description && doc2.description) {
      const similarity = this.stringSimilarity(doc1.description, doc2.description);
      if (similarity > 0.5) {
        score += weights.description * similarity;
        factors.push('Similar descriptions');
      }
    }

    // Position similarity
    if (doc1.positions && doc2.positions && doc1.positions.length > 0 && doc2.positions.length > 0) {
      const positionSimilarity = this.calculatePositionSimilarity(doc1.positions, doc2.positions);
      if (positionSimilarity > 0.5) {
        score += weights.positions * positionSimilarity;
        factors.push('Similar line items');
      }
    }

    return {
      score,
      reason: factors.length > 0 ? factors.join(', ') : 'No significant similarity',
      factors
    };
  }

  /**
   * Calculate similarity between position arrays
   * @param {Array} positions1 - First document positions
   * @param {Array} positions2 - Second document positions
   * @returns {number} - Similarity score (0-1)
   */
  calculatePositionSimilarity(positions1, positions2) {
    if (!positions1.length || !positions2.length) { return 0; }

    let matches = 0;
    const maxLength = Math.max(positions1.length, positions2.length);

    for (const pos1 of positions1) {
      for (const pos2 of positions2) {
        if (pos1.name && pos2.name) {
          const nameSimilarity = this.stringSimilarity(pos1.name, pos2.name);
          if (nameSimilarity > 0.7) {
            matches++;
            break;
          }
        }
      }
    }

    return matches / maxLength;
  }

  /**
   * Analyze document relationships using AI
   * @param {Object} document - Main document
   * @param {Array} relatedDocuments - Related documents
   * @param {string} apiKey - API key
   * @returns {Promise<Array>} - Enhanced relationships
   */
  async analyzeDocumentRelationships(document, relatedDocuments, apiKey) {
    try {
      if (!apiKey || !relatedDocuments.length) {
        return relatedDocuments;
      }

      console.log('Analyzing document relationships with AI...');

      // Prepare document metadata for analysis
      const documentMetadata = {
        documentName: document.documentName || document.fileName,
        documentType: document.documentType || document.kind,
        seller: document.seller_name || document.seller,
        sellerNip: document.seller_tax_no || document.sellerNip,
        buyer: document.buyer_name || document.buyer,
        buyerNip: document.buyer_tax_no || document.buyerNip,
        description: document.description,
        issueDate: document.issue_date || document.issueDate,
        totalAmount: document.total_gross
      };

      // Prepare related documents metadata
      const relatedDocsMetadata = relatedDocuments.map(doc => ({
        documentId: doc.documentId,
        documentName: doc.documentName,
        documentType: doc.documentType,
        relationship: doc.relationship
      }));

      const systemPrompt = `You are an expert in business document analysis and relationship detection.

TASK: Analyze relationships between business documents and enhance relationship descriptions.

INSTRUCTIONS:
1. Provide detailed descriptions of how documents are related
2. Identify potential business implications
3. Assign confidence scores (0-1) based on relationship strength
4. Return enhanced relationships in JSON format

RELATIONSHIP TYPES:
- same_company: Documents involving the same business entity
- same_contract: Documents related to the same contract/agreement
- invoice_correction: Correction documents referencing originals
- related_purchase/sale: Same transaction from different perspectives
- similar_content: Documents with similar content or amounts`;

      const prompt = `Main Document:
${JSON.stringify(documentMetadata, null, 2)}

Related Documents:
${JSON.stringify(relatedDocsMetadata, null, 2)}

Analyze and enhance these relationships, providing detailed descriptions and confidence scores.`;

      const response = await this.deepSeekAPI.callAPI(prompt, apiKey, {
        systemPrompt,
        temperature: 0.3,
        max_tokens: 1500
      });

      if (response.success) {
        const enhancedData = this.deepSeekAPI.extractJSON(response.content);
        if (enhancedData && enhancedData.relationships) {
          return enhancedData.relationships;
        }
      }

      return relatedDocuments;
    } catch (error) {
      console.error('Error analyzing document relationships:', error);
      return relatedDocuments;
    }
  }

  /**
   * Update document with relationships
   * @param {Object} document - Document to update
   * @param {Array} relatedDocuments - Related documents
   * @returns {Promise<Object>} - Updated document
   */
  async updateDocumentWithRelationships(document, relatedDocuments) {
    try {
      if (!document || !document.fileHash) {
        throw new Error('Invalid document');
      }

      // Add related documents to the document
      document.relatedDocuments = relatedDocuments;

      // Update the document in storage
      if (document.documentType === 'Invoice' || document.kind) {
        await this.storageAPI.updateInvoice(document);
      } else {
        await this.storageAPI.updateDocument(document);
      }

      // Update each related document with a link back to this document
      if (relatedDocuments && relatedDocuments.length > 0) {
        for (const relatedDoc of relatedDocuments) {
          await this.updateRelatedDocumentWithLink(
            document,
            relatedDoc.documentId,
            relatedDoc.relationship
          );
        }
      }

      return document;
    } catch (error) {
      console.error('Error updating document with relationships:', error);
      throw error;
    }
  }

  /**
   * Update related document with reverse link
   * @param {Object} mainDocument - Main document
   * @param {string} relatedDocumentId - Related document ID
   * @param {Object} relationship - Relationship object
   * @returns {Promise<boolean>} - Success status
   */
  async updateRelatedDocumentWithLink(mainDocument, relatedDocumentId, relationship) {
    try {
      // Get all documents
      const [invoices, documents] = await Promise.all([
        this.storageAPI.getInvoices(),
        this.storageAPI.getDocuments()
      ]);

      // Find the related document
      let relatedDoc = invoices.find(inv => inv.fileHash === relatedDocumentId);
      let isInvoice = true;

      if (!relatedDoc) {
        relatedDoc = documents.find(doc => doc.fileHash === relatedDocumentId);
        isInvoice = false;
      }

      if (!relatedDoc) {
        return false;
      }

      // Create reverse relationship
      const reverseLink = this.createReverseRelationship(mainDocument, relatedDocumentId, relationship);

      // Initialize relatedDocuments array if it doesn't exist
      if (!relatedDoc.relatedDocuments) {
        relatedDoc.relatedDocuments = [];
      }

      // Check if the link already exists
      const existingLinkIndex = relatedDoc.relatedDocuments.findIndex(
        link => link.documentId === mainDocument.fileHash
      );

      if (existingLinkIndex !== -1) {
        relatedDoc.relatedDocuments[existingLinkIndex] = reverseLink;
      } else {
        relatedDoc.relatedDocuments.push(reverseLink);
      }

      // Update the document in storage
      if (isInvoice) {
        await this.storageAPI.updateInvoice(relatedDoc);
      } else {
        await this.storageAPI.updateDocument(relatedDoc);
      }

      return true;
    } catch (error) {
      console.error('Error updating related document with link:', error);
      return false;
    }
  }

  /**
   * Create reverse relationship
   * @param {Object} mainDocument - Main document
   * @param {string} relatedDocumentId - Related document ID
   * @param {Object} relationship - Original relationship
   * @returns {Object} - Reverse relationship
   */
  createReverseRelationship(mainDocument, relatedDocumentId, relationship) {
    const reverseRelationship = { ...relationship };

    // Adjust relationship type and reason for reverse direction
    if (relationship.type === this.relationshipTypes.RELATED_SALE) {
      reverseRelationship.type = this.relationshipTypes.RELATED_PURCHASE;
      reverseRelationship.reason = reverseRelationship.reason?.replace(
        'our sale to their purchase', 'our purchase to their sale'
      );
    } else if (relationship.type === this.relationshipTypes.RELATED_PURCHASE) {
      reverseRelationship.type = this.relationshipTypes.RELATED_SALE;
      reverseRelationship.reason = reverseRelationship.reason?.replace(
        'our purchase to their sale', 'our sale to their purchase'
      );
    } else if (relationship.type === this.relationshipTypes.INVOICE_CORRECTION) {
      reverseRelationship.reason = 'Original invoice for correction';
    }

    return {
      documentId: mainDocument.fileHash,
      documentName: mainDocument.documentName || mainDocument.fileName,
      documentType: mainDocument.documentType || mainDocument.kind || 'Unknown',
      relationship: reverseRelationship
    };
  }

  /**
   * Process document relationships (main entry point)
   * @param {Object} document - Document to process
   * @param {string} apiKey - API key for AI analysis
   * @returns {Promise<Array>} - Related documents
   */
  async processDocumentRelationships(document, apiKey = null) {
    try {
      // Find related documents
      const relatedDocuments = await this.findRelatedDocuments(document);

      if (relatedDocuments.length === 0) {
        return [];
      }

      // Analyze relationships with AI if API key is provided
      let enhancedRelationships = relatedDocuments;
      if (apiKey) {
        enhancedRelationships = await this.analyzeDocumentRelationships(
          document, relatedDocuments, apiKey
        );
      }

      // Update document with relationships
      await this.updateDocumentWithRelationships(document, enhancedRelationships);

      return enhancedRelationships;
    } catch (error) {
      console.error('Error processing document relationships:', error);
      return [];
    }
  }

  /**
   * Normalize NIP (remove non-digits)
   * @param {string} nip - NIP to normalize
   * @returns {string} - Normalized NIP
   */
  normalizeNip(nip) {
    return nip ? nip.replace(/\D/g, '') : '';
  }

  /**
   * Calculate string similarity using Levenshtein distance
   * @param {string} str1 - First string
   * @param {string} str2 - Second string
   * @returns {number} - Similarity score (0-1)
   */
  stringSimilarity(str1, str2) {
    if (str1 === str2) { return 1; }
    if (!str1 || !str2) { return 0; }

    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;

    if (longer.length === 0) { return 1; }

    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  /**
   * Calculate Levenshtein distance
   * @param {string} str1 - First string
   * @param {string} str2 - Second string
   * @returns {number} - Edit distance
   */
  levenshteinDistance(str1, str2) {
    const matrix = [];

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }

    return matrix[str2.length][str1.length];
  }
}

// Create singleton instance
const documentLinkingService = new DocumentLinkingService();

// Export for ES modules
export default documentLinkingService;

// Export to window object for non-module scripts
if (typeof window !== 'undefined') {
  window.DocumentLinkingService = DocumentLinkingService;
  window.documentLinkingService = documentLinkingService;
  window.RELATIONSHIP_TYPES = RELATIONSHIP_TYPES;

  // Legacy compatibility
  window.findRelatedDocuments = (doc) => documentLinkingService.findRelatedDocuments(doc);
  window.processDocumentRelationships = (doc) => documentLinkingService.processDocumentRelationships(doc);
}

// ===== LOCAL TESTING (Node.js equivalent of Python's if __name__ == '__main__') =====
if (typeof require !== 'undefined' && require.main === module) {
  console.log('🧪 Running local tests for DocumentLinkingService...');

  // Create test instance
  const service = new DocumentLinkingService();

  // Test core functionality
  console.log('✅ Test 1: Service initialization');
  console.log('Relationship types available:', Object.keys(service.relationshipTypes));
  console.log('Service initialized:', !!service.storageAPI);

  console.log('✅ Test 2: NIP normalization');
  console.log('PL 123-456-78-90 →', service.normalizeNip('PL 123-456-78-90'));
  console.log('123-456-78-90 →', service.normalizeNip('123-456-78-90'));
  console.log('1234567890 →', service.normalizeNip('1234567890'));

  console.log('✅ Test 3: String similarity');
  console.log('Identical strings:', service.stringSimilarity('test', 'test'));
  console.log('Similar strings:', service.stringSimilarity('test', 'tests'));
  console.log('Different strings:', service.stringSimilarity('test', 'hello'));

  console.log('✅ Test 4: Relationship determination');
  const doc1 = {
    seller_tax_no: '1234567890',
    buyer_tax_no: '0987654321',
    seller_name: 'Company A',
    total_gross: '1000.00'
  };
  const doc2 = {
    seller_tax_no: '1234567890',
    buyer_tax_no: '1111111111',
    seller_name: 'Company A',
    total_gross: '1000.00'
  };
  const relationship = service.determineRelationship(doc1, doc2);
  console.log('Same seller relationship:', relationship);

  console.log('✅ Test 5: Content similarity');
  const similarity = service.calculateContentSimilarity(doc1, doc2);
  console.log('Content similarity:', similarity);

  console.log('✅ Test 6: Position similarity');
  const positions1 = [{ name: 'Product A' }, { name: 'Product B' }];
  const positions2 = [{ name: 'Product A' }, { name: 'Product C' }];
  const positionSim = service.calculatePositionSimilarity(positions1, positions2);
  console.log('Position similarity:', positionSim);

  console.log('✅ All tests completed for DocumentLinkingService');
}
