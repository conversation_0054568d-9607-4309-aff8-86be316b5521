/**
 * LanguagesMapping - A class for managing language codes and mappings
 * between OCR language codes and document language codes.
 */
class LanguagesMapping {
  constructor() {}

  /**
     * Get a pipe-separated string of all supported languages for OCR
     * @returns {string} - Pipe-separated string of language codes (e.g., "eng|pol|deu|fra|...")
     */
  getOcrLanguageTypeFormat() {
    // OCR language codes from Tesseract
    const ocrLanguages = [
      'afr', 'amh', 'ara', 'asm', 'aze', 'aze_cyrl', 'bel', 'ben', 'bod', 'bos',
      'bul', 'cat', 'ceb', 'ces', 'chi_sim', 'chi_tra', 'chr', 'cos', 'cym', 'dan',
      'dan_frak', 'deu', 'deu_frak', 'div', 'dzo', 'ell', 'eng', 'enm', 'epo', 'equ',
      'est', 'eus', 'fao', 'fas', 'fil', 'fin', 'fra', 'frk', 'frm', 'gle',
      'glg', 'grc', 'guj', 'hat', 'heb', 'hin', 'hrv', 'hun', 'iku', 'ind',
      'isl', 'ita', 'ita_old', 'jav', 'jpn', 'kan', 'kat', 'kat_old', 'kaz', 'khm',
      'kir', 'kor', 'kur', 'lao', 'lat', 'lav', 'lit', 'ltz', 'mal', 'mar',
      'mkd', 'mlt', 'msa', 'mya', 'nep', 'nld', 'nor', 'oci', 'ori', 'pan',
      'pol', 'por', 'pus', 'ron', 'rus', 'san', 'sin', 'slk', 'slv', 'spa',
      'spa_old', 'sqi', 'srp', 'srp_latn', 'swa', 'swe', 'syr', 'tam', 'tel', 'tgk',
      'tgl', 'tha', 'tir', 'tur', 'uig', 'ukr', 'urd', 'uzb', 'uzb_cyrl', 'vie',
      'yid'
    ];

    return ocrLanguages.join('|');
  }

  /**
     * Get mapping between OCR language codes and document language codes
     * @returns {Object} - Mapping between OCR and document language codes
     */
  getLanguageMapping() {
    return {
      // Basic mappings between Tesseract OCR codes and document language codes
      'eng': 'en', // English
      'pol': 'pl', // Polish
      'deu': 'de', // German
      'fra': 'fr', // French
      'ces': 'cz', // Czech
      'rus': 'ru', // Russian
      'spa': 'es', // Spanish
      'ita': 'it', // Italian
      'nld': 'nl', // Dutch
      'hrv': 'hr', // Croatian
      'ara': 'ar', // Arabic
      'slk': 'sk', // Slovak
      'slv': 'sl', // Slovenian
      'ell': 'el', // Greek
      'est': 'et', // Estonian
      'chi_sim': 'cn', // Chinese Simplified
      'hun': 'hu', // Hungarian
      'tur': 'tr', // Turkish
      'fas': 'fa', // Persian

      // Additional mappings for completeness
      'bul': 'bg', // Bulgarian
      'dan': 'da', // Danish
      'fin': 'fi', // Finnish
      'heb': 'he', // Hebrew
      'hin': 'hi', // Hindi
      'ind': 'id', // Indonesian
      'jpn': 'ja', // Japanese
      'kor': 'ko', // Korean
      'lav': 'lv', // Latvian
      'lit': 'lt', // Lithuanian
      'nor': 'no', // Norwegian
      'por': 'pt', // Portuguese
      'ron': 'ro', // Romanian
      'srp': 'sr', // Serbian
      'swe': 'se', // Swedish
      'tha': 'th', // Thai
      'ukr': 'uk', // Ukrainian
      'vie': 'vi' // Vietnamese
    };
  }

  /**
     * Convert OCR language code to document language code
     * @param {string} ocrLang - OCR language code (e.g., 'eng', 'pol')
     * @returns {string} - Document language code (e.g., 'en', 'pl')
     */
  convertOcrToDocumentLanguage(ocrLang) {
    const mapping = this.getLanguageMapping();
    return mapping[ocrLang] || 'en'; // Default to English if mapping not found
  }

  /**
     * Get all supported OCR languages with their descriptions
     * @returns {Object} - Map of OCR language codes and their descriptions
     */
  getOcrLanguageDescriptions() {
    return {
      'afr': 'Afrikaans',
      'amh': 'Amharic',
      'ara': 'Arabic',
      'asm': 'Assamese',
      'aze': 'Azerbaijani',
      'aze_cyrl': 'Azerbaijani (Cyrillic)',
      'bel': 'Belarusian',
      'ben': 'Bengali',
      'bod': 'Tibetan',
      'bos': 'Bosnian',
      'bul': 'Bulgarian',
      'cat': 'Catalan',
      'ceb': 'Cebuano',
      'ces': 'Czech',
      'chi_sim': 'Chinese (Simplified)',
      'chi_tra': 'Chinese (Traditional)',
      'chr': 'Cherokee',
      'cos': 'Corsican',
      'cym': 'Welsh',
      'dan': 'Danish',
      'dan_frak': 'Danish (Fraktur)',
      'deu': 'German',
      'deu_frak': 'German (Fraktur)',
      'div': 'Dhivehi',
      'dzo': 'Dzongkha',
      'ell': 'Greek',
      'eng': 'English',
      'enm': 'English (Middle)',
      'epo': 'Esperanto',
      'equ': 'Math/Equation',
      'est': 'Estonian',
      'eus': 'Basque',
      'fao': 'Faroese',
      'fas': 'Persian',
      'fil': 'Filipino',
      'fin': 'Finnish',
      'fra': 'French',
      'frk': 'Frankish',
      'frm': 'French (Middle)',
      'gle': 'Irish',
      'glg': 'Galician',
      'grc': 'Greek (Ancient)',
      'guj': 'Gujarati',
      'hat': 'Haitian',
      'heb': 'Hebrew',
      'hin': 'Hindi',
      'hrv': 'Croatian',
      'hun': 'Hungarian',
      'iku': 'Inuktitut',
      'ind': 'Indonesian',
      'isl': 'Icelandic',
      'ita': 'Italian',
      'ita_old': 'Italian (Old)',
      'jav': 'Javanese',
      'jpn': 'Japanese',
      'kan': 'Kannada',
      'kat': 'Georgian',
      'kat_old': 'Georgian (Old)',
      'kaz': 'Kazakh',
      'khm': 'Khmer',
      'kir': 'Kyrgyz',
      'kor': 'Korean',
      'kur': 'Kurdish',
      'lao': 'Lao',
      'lat': 'Latin',
      'lav': 'Latvian',
      'lit': 'Lithuanian',
      'ltz': 'Luxembourgish',
      'mal': 'Malayalam',
      'mar': 'Marathi',
      'mkd': 'Macedonian',
      'mlt': 'Maltese',
      'msa': 'Malay',
      'mya': 'Burmese',
      'nep': 'Nepali',
      'nld': 'Dutch',
      'nor': 'Norwegian',
      'oci': 'Occitan',
      'ori': 'Oriya',
      'pan': 'Punjabi',
      'pol': 'Polish',
      'por': 'Portuguese',
      'pus': 'Pashto',
      'ron': 'Romanian',
      'rus': 'Russian',
      'san': 'Sanskrit',
      'sin': 'Sinhala',
      'slk': 'Slovak',
      'slv': 'Slovenian',
      'spa': 'Spanish',
      'spa_old': 'Spanish (Old)',
      'sqi': 'Albanian',
      'srp': 'Serbian',
      'srp_latn': 'Serbian (Latin)',
      'swa': 'Swahili',
      'swe': 'Swedish',
      'syr': 'Syriac',
      'tam': 'Tamil',
      'tel': 'Telugu',
      'tgk': 'Tajik',
      'tgl': 'Tagalog',
      'tha': 'Thai',
      'tir': 'Tigrinya',
      'tur': 'Turkish',
      'uig': 'Uyghur',
      'ukr': 'Ukrainian',
      'urd': 'Urdu',
      'uzb': 'Uzbek',
      'uzb_cyrl': 'Uzbek (Cyrillic)',
      'vie': 'Vietnamese',
      'yid': 'Yiddish'
    };
  }

  /**
     * Get common document languages for UI selection
     * @returns {Object} - Map of document language codes and their descriptions
     */
  getCommonDocumentLanguages() {
    return {
      'en': 'English',
      'pl': 'Polish',
      'de': 'German',
      'fr': 'French',
      'cz': 'Czech',
      'ru': 'Russian',
      'es': 'Spanish',
      'it': 'Italian',
      'nl': 'Dutch'
    };
  }

  /**
     * Detect the language of the text based on common words and patterns
     * @param {string} text - Text to analyze for language detection
     * @returns {string} - Detected language name (e.g., 'Polish', 'English')
     */
  detectLanguage(text) {
    // Common words and patterns in different languages
    const patterns = {
      'Polish': ['faktura', 'sprzedawca', 'nabywca', 'nip', 'razem', 'wartość', 'kwota', 'podatku', 'złotych', 'zł', 'netto', 'brutto', 'vat'],
      'English': ['invoice', 'seller', 'buyer', 'tax', 'total', 'amount', 'value', 'payment', 'due', 'date', 'net', 'gross', 'vat'],
      'German': ['rechnung', 'verkäufer', 'käufer', 'steuer', 'gesamt', 'betrag', 'wert', 'zahlung', 'fällig', 'datum', 'netto', 'brutto', 'mehrwertsteuer'],
      'French': ['facture', 'vendeur', 'acheteur', 'taxe', 'total', 'montant', 'valeur', 'paiement', 'échéance', 'date', 'net', 'brut', 'tva'],
      'Spanish': ['factura', 'vendedor', 'comprador', 'impuesto', 'total', 'importe', 'valor', 'pago', 'vencimiento', 'fecha', 'neto', 'bruto', 'iva'],
      'Italian': ['fattura', 'venditore', 'acquirente', 'imposta', 'totale', 'importo', 'valore', 'pagamento', 'scadenza', 'data', 'netto', 'lordo', 'iva']
    };

    // Convert text to lowercase for case-insensitive matching
    const lowerText = text.toLowerCase();

    // Count matches for each language
    const matches = {};
    for (const [language, words] of Object.entries(patterns)) {
      matches[language] = words.filter(word => lowerText.includes(word)).length;
    }

    // Find the language with the most matches
    let detectedLanguage = 'Polish'; // Default to Polish
    let maxMatches = 0;

    for (const [language, count] of Object.entries(matches)) {
      if (count > maxMatches) {
        maxMatches = count;
        detectedLanguage = language;
      }
    }

    return detectedLanguage;
  }

  /**
     * Detect language and return document language code
     * @param {string} text - Text to analyze for language detection
     * @returns {string} - Document language code (e.g., 'pl', 'en')
     */
  detectDocumentLanguageCode(text) {
    const detectedLanguage = this.detectLanguage(text);

    // Map language names to document codes
    const languageNameToCode = {
      'Polish': 'pl',
      'English': 'en',
      'German': 'de',
      'French': 'fr',
      'Spanish': 'es',
      'Italian': 'it'
    };

    return languageNameToCode[detectedLanguage] || 'pl'; // Default to Polish
  }
}

// Export to window object for non-module scripts (only in browser environment)
if (typeof window !== 'undefined') {
  window.LanguagesMapping = LanguagesMapping;
}

// Export as default for ES modules
export default LanguagesMapping;

// ===== LOCAL TESTING (Node.js equivalent of Python's if __name__ == '__main__') =====
if (typeof require !== 'undefined' && require.main === module) {
  console.log('🧪 Running local tests for LanguagesMapping...');

  // Create test instance
  const mapping = new LanguagesMapping();

  // Test core functionality
  console.log('✅ Test 1: Language detection');
  console.log('Polish text detection:', mapping.detectLanguage('Faktura VAT nr FV/2025/001'));
  console.log('English text detection:', mapping.detectLanguage('Invoice VAT no. INV/2025/001'));
  console.log('German text detection:', mapping.detectLanguage('Rechnung Nr. RE/2025/001'));

  console.log('✅ Test 2: Language code mapping');
  console.log('Polish code:', mapping.getLanguageCode('Polish'));
  console.log('English code:', mapping.getLanguageCode('English'));
  console.log('German code:', mapping.getLanguageCode('German'));

  console.log('✅ Test 3: Edge cases');
  console.log('Unknown language:', mapping.detectLanguage(''));
  console.log('Mixed text:', mapping.detectLanguage('Faktura Invoice Rechnung'));

  console.log('✅ All tests completed for LanguagesMapping');
}
