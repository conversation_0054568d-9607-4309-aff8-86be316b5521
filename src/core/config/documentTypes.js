/**
 * Document Types Configuration
 * Extracted from core/prompt_generators/document_types.js
 * Contains document type mappings and validation rules
 */

// Fakturownia document types with descriptions
export const FAKTUROWNIA_DOCUMENT_TYPES = {
  'vat': 'faktura VAT',
  'proforma': 'faktura Proforma',
  'bill': 'rachunek',
  'receipt': 'paragon',
  'advance': 'faktura zaliczkowa',
  'final': 'faktura końcowa',
  'correction': 'faktura korekta',
  'vat_mp': 'faktura MP',
  'invoice_other': 'inna faktura',
  'vat_margin': 'faktura marża',
  'kp': 'kasa przyjmie',
  'kw': 'kasa wyda',
  'estimate': 'zamówienie',
  'vat_rr': 'faktura RR',
  'correction_note': 'nota korygująca',
  'accounting_note': 'nota księgowa',
  'client_order': 'własny dokument nieksięgowy',
  'dw': 'dowód wewnętrzny',
  'wnt': 'Wewnątrzwspólnotowe Nabycie Towarów',
  'wdt': 'Wewnątrzwspólnotowa Dostawa Towarów',
  'import_service': 'import usług',
  'import_service_eu': 'import usług z UE',
  'import_products': 'import towarów - procedura uproszczona',
  'export_products': 'eksport towarów'
};

// Valid document types for validation
export const VALID_DOCUMENT_TYPES = [
  'vat', 'proforma', 'bill', 'receipt', 'advance', 'final', 'correction',
  'vat_mp', 'invoice_other', 'vat_margin', 'correction_note', 'accounting_note',
  'client_order', 'dw', 'wnt', 'wdt', 'import_service', 'import_service_eu',
  'import_products', 'export_products', 'kp', 'kw', 'estimate', 'vat_rr'
];

// Document type detection patterns for content analysis
export const DOCUMENT_TYPE_PATTERNS = {
  proforma: ['proforma', 'pro forma', 'pro-forma'],
  advance: ['zaliczk', 'advance'],
  final: ['końcow', 'final'],
  correction: ['korekt', 'correction', 'credit note'],
  vat_margin: ['marża', 'margin'],
  vat_mp: ['mp', 'mały podatnik'],
  vat_rr: [' rr', 'rolnik'],
  wdt: ['wdt', 'wewnątrzwspólnotowa dostawa'],
  wnt: ['wnt', 'wewnątrzwspólnotowe nabycie'],
  import_service: ['import usług', 'import of services'],
  import_service_eu: ['import usług', 'ue', 'eu'],
  import_products: ['import towarów', 'import of goods'],
  export_products: ['eksport', 'export'],
  bill: ['rachunek'],
  receipt: ['paragon'],
  correction_note: ['nota korygująca'],
  accounting_note: ['nota księgowa', 'obciążen'],
  estimate: ['zamówienie', 'order'],
  dw: ['dowód wewnętrzny'],
  client_order: ['własny dokument']
};

// Document types that require specific fields
export const DOCUMENT_TYPE_REQUIREMENTS = {
  correction: ['invoice_id', 'from_invoice_id', 'corrected_content_before', 'corrected_content_after'],
  advance: ['invoice_id'],
  final: ['from_invoice_id'],
  accounting_note: ['accounting_note_kind'],
  wnt: ['seller_country', 'buyer_country', 'reverse_charge'],
  wdt: ['seller_country', 'buyer_country'],
  import_service: ['seller_country', 'buyer_country', 'reverse_charge'],
  import_service_eu: ['seller_country', 'buyer_country', 'reverse_charge'],
  import_products: ['seller_country', 'buyer_country'],
  export_products: ['seller_country', 'buyer_country']
};

// Document types that typically have line items/positions
export const DOCUMENT_TYPES_WITH_POSITIONS = [
  'vat', 'proforma', 'bill', 'receipt', 'advance', 'final', 'correction',
  'vat_mp', 'invoice_other', 'vat_margin', 'wdt', 'wnt', 'import_service',
  'import_service_eu', 'import_products', 'export_products', 'estimate'
];

// Document types that are invoices (for classification purposes)
export const INVOICE_DOCUMENT_TYPES = [
  'vat', 'proforma', 'advance', 'final', 'correction', 'vat_mp',
  'invoice_other', 'vat_margin', 'vat_rr', 'wdt', 'wnt',
  'import_service', 'import_service_eu', 'import_products', 'export_products'
];

// Document types that are receipts/bills (for classification purposes)
export const RECEIPT_DOCUMENT_TYPES = [
  'bill', 'receipt', 'kp', 'kw'
];

// Document types that are notes (for classification purposes)
export const NOTE_DOCUMENT_TYPES = [
  'correction_note', 'accounting_note', 'dw'
];

// Document types that are orders/estimates (for classification purposes)
export const ORDER_DOCUMENT_TYPES = [
  'estimate', 'client_order'
];

// Default document type for fallback
export const DEFAULT_DOCUMENT_TYPE = 'vat';

// Document type categories for UI grouping
export const DOCUMENT_TYPE_CATEGORIES = {
  invoices: {
    label: 'Invoices',
    types: INVOICE_DOCUMENT_TYPES
  },
  receipts: {
    label: 'Receipts & Bills',
    types: RECEIPT_DOCUMENT_TYPES
  },
  notes: {
    label: 'Notes & Documents',
    types: NOTE_DOCUMENT_TYPES
  },
  orders: {
    label: 'Orders & Estimates',
    types: ORDER_DOCUMENT_TYPES
  }
};

/**
 * Map document type detected by AI to Fakturownia document type
 * @param {string} documentType - Document type detected by AI
 * @param {string} documentContent - Document content for additional analysis
 * @returns {string} - Fakturownia document type
 */
export function mapToFakturowniaDocumentType(documentType, documentContent = '') {
  const type = documentType.toLowerCase();
  const content = documentContent.toLowerCase();

  // Direct mapping for exact matches
  if (VALID_DOCUMENT_TYPES.includes(type)) {
    return type;
  }

  // Pattern-based mapping for content analysis
  if (type === 'invoice') {
    // Check for specific invoice types in the content
    for (const [docType, patterns] of Object.entries(DOCUMENT_TYPE_PATTERNS)) {
      if (patterns.some(pattern => content.includes(pattern))) {
        return docType;
      }
    }
    // Default to VAT invoice if no specific pattern found
    return 'vat';
  }

  // Fallback mappings for common variations
  const fallbackMappings = {
    'invoice': 'vat',
    'bill': 'bill',
    'receipt': 'receipt',
    'correction_note': 'correction_note',
    'accounting_note': 'accounting_note',
    'estimate': 'estimate',
    'order': 'estimate',
    'internal_document': 'dw',
    'client_order': 'client_order'
  };

  return fallbackMappings[type] || DEFAULT_DOCUMENT_TYPE;
}

/**
 * Check if document type has positions/line items
 * @param {string} documentType - Document type
 * @returns {boolean} - Whether the document type has positions
 */
export function documentTypeHasPositions(documentType) {
  return DOCUMENT_TYPES_WITH_POSITIONS.includes(documentType.toLowerCase());
}

/**
 * Get required fields for a document type
 * @param {string} documentType - Document type
 * @returns {Array} - Array of required field names
 */
export function getRequiredFieldsForDocumentType(documentType) {
  return DOCUMENT_TYPE_REQUIREMENTS[documentType.toLowerCase()] || [];
}

/**
 * Validate document type
 * @param {string} documentType - Document type to validate
 * @returns {boolean} - Whether the document type is valid
 */
export function isValidDocumentType(documentType) {
  return VALID_DOCUMENT_TYPES.includes(documentType.toLowerCase());
}

/**
 * Get document type category
 * @param {string} documentType - Document type
 * @returns {string} - Category name
 */
export function getDocumentTypeCategory(documentType) {
  const type = documentType.toLowerCase();

  for (const [category, config] of Object.entries(DOCUMENT_TYPE_CATEGORIES)) {
    if (config.types.includes(type)) {
      return category;
    }
  }

  return 'invoices'; // Default category
}

/**
 * Get human-readable description for document type
 * @param {string} documentType - Document type
 * @returns {string} - Human-readable description
 */
export function getDocumentTypeDescription(documentType) {
  return FAKTUROWNIA_DOCUMENT_TYPES[documentType.toLowerCase()] || documentType;
}

// Enhanced document type patterns for general business documents
// Merged from components/guessDocumentType.js
export const GENERAL_DOCUMENT_PATTERNS = {
  invoice: [
    'invoice', 'faktura', 'payment', 'płatność', 'zapłata', 'vat', 'tax', 'podatek'
  ],
  contract: [
    'contract', 'umowa', 'parties', 'strony', 'agree', 'zgadza'
  ],
  financial_report: [
    'financial', 'finansowy', 'report', 'raport', 'sprawozdanie',
    'balance', 'bilans', 'profit', 'zysk'
  ],
  agreement: [
    'agreement', 'porozumienie', 'terms', 'warunki'
  ],
  legal: [
    'legal', 'prawny', 'law', 'prawo', 'court', 'sąd'
  ],
  amendment: [
    'amendment', 'aneks', 'addendum', 'załącznik', 'supplement', 'uzupełnienie'
  ],
  notice: [
    'notice', 'zawiadomienie', 'notification', 'powiadomienie',
    'announcement', 'ogłoszenie'
  ],
  court_order: [
    'court order', 'nakaz sądowy', 'judgment', 'wyrok',
    'ruling', 'orzeczenie', 'verdict', 'werdykt'
  ],
  registration: [
    'registration', 'rejestracja', 'certificate of incorporation',
    'świadectwo rejestracji', 'business registry', 'rejestr przedsiębiorców'
  ],
  email: [
    'from:', 'to:', 'subject:', 'temat:', '@'
  ],
  tax_declaration: [
    'tax declaration', 'deklaracja podatkowa', 'tax return', 'zeznanie podatkowe',
    'pit', 'cit', 'vat', 'declaration', 'deklaracja'
  ],
  government_application: [
    'application', 'wniosek', 'government', 'rząd', 'ministry', 'ministerstwo',
    'agency', 'agencja'
  ],
  corporate_application: [
    'application', 'wniosek', 'corporate', 'korporacyjny', 'company', 'firma'
  ],
  resolution: [
    'resolution', 'uchwała', 'board resolution', 'uchwała zarządu',
    'shareholder resolution', 'uchwała wspólników'
  ]
};

/**
 * Guess document type from content using pattern matching
 * Merged from components/guessDocumentType.js
 * @param {string} content - Document content
 * @returns {string} - Guessed document type
 */
export function guessDocumentTypeWithPatterns(content) {
  const lowerContent = content.toLowerCase();

  // Check each pattern category
  for (const [docType, patterns] of Object.entries(GENERAL_DOCUMENT_PATTERNS)) {
    let matchCount = 0;
    let requiredMatches = 1;

    // For complex document types, require multiple pattern matches
    if (['invoice', 'contract', 'financial_report'].includes(docType)) {
      requiredMatches = 2;
    }

    for (const pattern of patterns) {
      if (lowerContent.includes(pattern)) {
        matchCount++;
        if (matchCount >= requiredMatches) {
          return docType;
        }
      }
    }
  }

  // Default to business document if no specific pattern found
  return 'business_document';
}

/**
 * Guess document type from content using AI
 * Merged from components/guessDocumentType.js
 * @param {string} content - Document content
 * @param {string} apiKey - DeepSeek API key
 * @param {Object} deepSeekAPI - DeepSeek API instance
 * @returns {Promise<string>} - Guessed document type
 */
export async function guessDocumentTypeWithAI(content, apiKey, deepSeekAPI = null) {
  try {
    if (!apiKey) {
      console.log('No DeepSeek API key provided, falling back to pattern matching');
      return guessDocumentTypeWithPatterns(content);
    }

    if (!deepSeekAPI) {
      console.warn('DeepSeek API instance not provided, falling back to pattern matching');
      return guessDocumentTypeWithPatterns(content);
    }

    // Prepare content sample for analysis
    const contentSample = content.substring(0, 2000);

    // Valid document types for AI response validation
    const validTypes = [
      'invoice', 'contract', 'financial_report', 'agreement', 'legal',
      'amendment', 'notice', 'court_order', 'registration', 'email',
      'tax_declaration', 'government_application', 'corporate_application',
      'resolution', 'business_document'
    ];

    const prompt = `Analyze the following document text and determine its type.
Respond with ONLY ONE of these document types:
${validTypes.map(type => `- ${type}`).join('\n')}

Document text sample:
${contentSample}`;

    const systemPrompt = 'You are an expert document analyst. Identify the document type from the provided text sample. Respond with ONLY the document type, nothing else.';

    // Call DeepSeek API
    const response = await deepSeekAPI.callAPI(prompt, apiKey, {
      temperature: 0.1,
      systemPrompt,
      max_tokens: 50
    });

    if (response.success) {
      const documentType = response.content.trim().toLowerCase();

      // Validate response
      if (validTypes.includes(documentType)) {
        console.log(`AI detected document type: ${documentType}`);
        return documentType;
      }
      // Try to extract valid type from response
      for (const type of validTypes) {
        if (response.content.toLowerCase().includes(type)) {
          console.log(`AI detected document type (extracted): ${type}`);
          return type;
        }
      }

      console.log('AI response did not contain valid document type, falling back to pattern matching');
      return guessDocumentTypeWithPatterns(content);

    }
    console.error('Error calling DeepSeek API:', response.error);
    return guessDocumentTypeWithPatterns(content);

  } catch (error) {
    console.error('Error in AI document type detection:', error);
    return guessDocumentTypeWithPatterns(content);
  }
}

/**
 * Main function to guess document type from content
 * Merged from components/guessDocumentType.js
 * @param {string} content - Document content
 * @param {Object} options - Options object
 * @param {string} options.apiKey - DeepSeek API key
 * @param {Object} options.deepSeekAPI - DeepSeek API instance
 * @param {boolean} options.useAI - Whether to use AI detection (default: true)
 * @returns {Promise<string>} - Guessed document type
 */
export async function guessDocumentType(content, options = {}) {
  try {
    const {
      apiKey = null,
      deepSeekAPI = null,
      useAI = true
    } = options;

    if (useAI && apiKey && deepSeekAPI) {
      // Use AI-based document type detection
      return await guessDocumentTypeWithAI(content, apiKey, deepSeekAPI);
    }
    // Fall back to pattern matching
    return guessDocumentTypeWithPatterns(content);

  } catch (error) {
    console.error('Error in document type detection:', error);
    return guessDocumentTypeWithPatterns(content);
  }
}

// ===== LOCAL TESTING (Node.js equivalent of Python's if __name__ == '__main__') =====
if (typeof process !== 'undefined' && process.argv && process.argv[1] && process.argv[1].includes('documentTypes.js')) {
  console.log('🧪 Running local tests for documentTypes...');

  // Test 1: Document type mapping
  console.log('✅ Test 1: Document type mapping');
  console.log('📝 VAT invoice mapping:', mapToFakturowniaDocumentType('invoice'));
  console.log('📝 Proforma mapping:', mapToFakturowniaDocumentType('proforma'));
  console.log('📝 Unknown type mapping:', mapToFakturowniaDocumentType('unknown'));

  // Test 2: Document type validation
  console.log('\n✅ Test 2: Document type validation');
  console.log('📝 VAT is valid:', isValidDocumentType('vat'));
  console.log('📝 Unknown is valid:', isValidDocumentType('unknown'));
  console.log('📝 Proforma is valid:', isValidDocumentType('proforma'));

  // Test 3: Position requirements
  console.log('\n✅ Test 3: Position requirements');
  console.log('📝 VAT has positions:', documentTypeHasPositions('vat'));
  console.log('📝 Receipt has positions:', documentTypeHasPositions('receipt'));
  console.log('📝 Note has positions:', documentTypeHasPositions('accounting_note'));

  // Test 4: Required fields
  console.log('\n✅ Test 4: Required fields');
  console.log('📝 Correction required fields:', getRequiredFieldsForDocumentType('correction'));
  console.log('📝 VAT required fields:', getRequiredFieldsForDocumentType('vat'));
  console.log('📝 WNT required fields:', getRequiredFieldsForDocumentType('wnt'));

  // Test 5: Document categories
  console.log('\n✅ Test 5: Document categories');
  console.log('📝 VAT category:', getDocumentTypeCategory('vat'));
  console.log('📝 Receipt category:', getDocumentTypeCategory('receipt'));
  console.log('📝 Note category:', getDocumentTypeCategory('accounting_note'));

  // Test 6: Document descriptions
  console.log('\n✅ Test 6: Document descriptions');
  console.log('📝 VAT description:', getDocumentTypeDescription('vat'));
  console.log('📝 Proforma description:', getDocumentTypeDescription('proforma'));
  console.log('📝 Unknown description:', getDocumentTypeDescription('unknown'));

  // Test 7: Pattern-based document type guessing
  console.log('\n✅ Test 7: Pattern-based document type guessing');
  const invoiceContent = 'FAKTURA VAT nr FV/2025/001 Total amount: 1230.00 PLN';
  const contractContent = 'CONTRACT between parties Company A and Company B agree to terms';
  const emailContent = 'From: <EMAIL> To: <EMAIL> Subject: Meeting';

  console.log('📝 Invoice content type:', guessDocumentTypeWithPatterns(invoiceContent));
  console.log('📝 Contract content type:', guessDocumentTypeWithPatterns(contractContent));
  console.log('📝 Email content type:', guessDocumentTypeWithPatterns(emailContent));

  // Test 8: Document type constants
  console.log('\n✅ Test 8: Document type constants');
  console.log('📝 Valid document types count:', VALID_DOCUMENT_TYPES.length);
  console.log('📝 Invoice types count:', INVOICE_DOCUMENT_TYPES.length);
  console.log('📝 Receipt types count:', RECEIPT_DOCUMENT_TYPES.length);
  console.log('📝 Default document type:', DEFAULT_DOCUMENT_TYPE);

  // Test 9: Content-based mapping
  console.log('\n✅ Test 9: Content-based mapping');
  const proformaContent = 'FAKTURA PROFORMA nr PRO/2025/001';
  const correctionContent = 'FAKTURA KOREKTA do faktury FV/2025/001';

  console.log('📝 Proforma content mapping:', mapToFakturowniaDocumentType('invoice', proformaContent));
  console.log('📝 Correction content mapping:', mapToFakturowniaDocumentType('invoice', correctionContent));

  // Test 10: Category structure
  console.log('\n✅ Test 10: Category structure');
  Object.entries(DOCUMENT_TYPE_CATEGORIES).forEach(([category, config]) => {
    console.log(`📝 Category ${category}: ${config.label} (${config.types.length} types)`);
  });

  console.log('\n🎉 All tests completed for documentTypes');
  console.log('📋 Document types configuration is working correctly');
}
