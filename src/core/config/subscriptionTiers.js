/**
 * Subscription Tiers Configuration
 * Defines all available subscription tiers with their features, limits, and pricing
 *
 * @fileoverview Static subscription tier configurations for MVAT Chrome Extension
 * <AUTHOR> Development Team
 * @since 1.0.0
 */

import { SubscriptionTier } from '../models/SubscriptionTier.js';

/**
 * Static subscription tier definitions
 * Based on business requirements from EPIC-B01
 */
export const TIER_DEFINITIONS = {
  STARTER: {
    id: 'starter',
    name: 'Starter',
    description: 'Perfect for freelancers and small businesses getting started with invoice automation',
    price: 0,
    annualPrice: 0,
    currency: 'EUR',
    limits: {
      monthlyInvoices: 10,
      aiCalls: 0,
      storageGB: 0.1, // 100MB
      apiCalls: 0,
      users: 1,
      integrations: 0
    },
    features: [
      'basic_pdf_processing',
      'local_storage',
      'manual_data_entry',
      'basic_table_view',
      'community_support',
      'export_json'
    ],
    restrictions: [
      'no_ai_extraction',
      'no_cloud_backup',
      'no_api_access',
      'no_integrations',
      'limited_support'
    ],
    isActive: true,
    sortOrder: 1,
    isPopular: false,
    isEnterprise: false
  },

  PROFESSIONAL: {
    id: 'professional',
    name: 'Professional',
    description: 'Advanced AI-powered processing for growing businesses',
    price: 29,
    annualPrice: 290, // 2 months free (12 * 29 = 348, discount = 58)
    currency: 'EUR',
    limits: {
      monthlyInvoices: 500,
      aiCalls: 1000,
      storageGB: 5,
      apiCalls: 0,
      users: 1,
      integrations: 3
    },
    features: [
      'ai_extraction',
      'advanced_pdf_processing',
      'ocr_processing',
      'cloud_backup',
      'email_support',
      'export_csv',
      'export_excel',
      'advanced_table_view',
      'usage_analytics',
      'data_validation'
    ],
    restrictions: [
      'no_api_access',
      'single_user',
      'limited_integrations'
    ],
    isActive: true,
    sortOrder: 2,
    isPopular: true,
    isEnterprise: false
  },

  BUSINESS: {
    id: 'business',
    name: 'Business',
    description: 'Complete solution for medium businesses with team collaboration',
    price: 99,
    annualPrice: 990, // 2 months free (12 * 99 = 1188, discount = 198)
    currency: 'EUR',
    limits: {
      monthlyInvoices: 2000,
      aiCalls: 5000,
      storageGB: 25,
      apiCalls: 10000,
      users: 5,
      integrations: 10
    },
    features: [
      'advanced_ai_extraction',
      'multi_provider_ai',
      'api_access',
      'priority_support',
      'multi_user',
      'team_collaboration',
      'advanced_analytics',
      'custom_fields',
      'bulk_processing',
      'webhook_notifications',
      'sso_integration',
      'audit_logs'
    ],
    restrictions: [
      'no_white_label',
      'standard_sla'
    ],
    isActive: true,
    sortOrder: 3,
    isPopular: false,
    isEnterprise: false
  },

  ENTERPRISE: {
    id: 'enterprise',
    name: 'Enterprise',
    description: 'Unlimited processing with premium support and customization',
    price: 299,
    annualPrice: 2990, // 2 months free (12 * 299 = 3588, discount = 598)
    currency: 'EUR',
    limits: {
      monthlyInvoices: 0, // Unlimited
      aiCalls: 0, // Unlimited
      storageGB: 0, // Unlimited
      apiCalls: 0, // Unlimited
      users: 0, // Unlimited
      integrations: 0 // Unlimited
    },
    features: [
      'unlimited_processing',
      'premium_ai_models',
      'dedicated_support',
      'custom_integrations',
      'white_label',
      'on_premise_deployment',
      'custom_workflows',
      'advanced_security',
      'compliance_reporting',
      'dedicated_account_manager',
      'custom_training',
      'priority_feature_requests'
    ],
    restrictions: [],
    isActive: true,
    sortOrder: 4,
    isPopular: false,
    isEnterprise: true
  }
};

/**
 * Feature definitions with descriptions
 */
export const FEATURE_DEFINITIONS = {
  // Basic Features
  'basic_pdf_processing': 'Extract text from PDF documents',
  'local_storage': 'Store data locally in browser',
  'manual_data_entry': 'Manual invoice data entry',
  'basic_table_view': 'Basic invoice table display',
  'community_support': 'Community forum support',
  'export_json': 'Export data in JSON format',

  // Professional Features
  'ai_extraction': 'AI-powered data extraction',
  'advanced_pdf_processing': 'Advanced PDF text extraction',
  'ocr_processing': 'OCR for scanned documents',
  'cloud_backup': 'Cloud data backup and sync',
  'email_support': 'Email customer support',
  'export_csv': 'Export data in CSV format',
  'export_excel': 'Export data in Excel format',
  'advanced_table_view': 'Advanced table with filtering and sorting',
  'usage_analytics': 'Usage statistics and analytics',
  'data_validation': 'Automatic data validation',

  // Business Features
  'advanced_ai_extraction': 'Advanced AI with higher accuracy',
  'multi_provider_ai': 'Multiple AI providers for redundancy',
  'api_access': 'REST API access',
  'priority_support': 'Priority customer support',
  'multi_user': 'Multiple user accounts',
  'team_collaboration': 'Team sharing and collaboration',
  'advanced_analytics': 'Detailed analytics and reporting',
  'custom_fields': 'Custom field definitions',
  'bulk_processing': 'Batch document processing',
  'webhook_notifications': 'Real-time webhook notifications',
  'sso_integration': 'Single Sign-On integration',
  'audit_logs': 'Comprehensive audit logging',

  // Enterprise Features
  'unlimited_processing': 'Unlimited document processing',
  'premium_ai_models': 'Access to premium AI models',
  'dedicated_support': '24/7 dedicated support',
  'custom_integrations': 'Custom integration development',
  'white_label': 'White-label branding options',
  'on_premise_deployment': 'On-premise deployment option',
  'custom_workflows': 'Custom workflow automation',
  'advanced_security': 'Enterprise-grade security',
  'compliance_reporting': 'Compliance and audit reporting',
  'dedicated_account_manager': 'Dedicated account manager',
  'custom_training': 'Custom training and onboarding',
  'priority_feature_requests': 'Priority feature development'
};

/**
 * Create SubscriptionTier instances from definitions
 *
 * @returns {Object} - Object with tier instances
 */
export function createTierInstances() {
  const tiers = {};

  Object.keys(TIER_DEFINITIONS).forEach(tierKey => {
    tiers[tierKey] = new SubscriptionTier(TIER_DEFINITIONS[tierKey]);
  });

  return tiers;
}

/**
 * Get all active tiers sorted by price
 *
 * @returns {Array<SubscriptionTier>} - Array of active tiers
 */
export function getActiveTiers() {
  const tiers = createTierInstances();
  return Object.values(tiers)
    .filter(tier => tier.isActive)
    .sort((a, b) => a.sortOrder - b.sortOrder);
}

/**
 * Get tier by ID
 *
 * @param {string} tierId - Tier identifier
 * @returns {SubscriptionTier|null} - Tier instance or null
 */
export function getTierById(tierId) {
  const tierKey = Object.keys(TIER_DEFINITIONS).find(
    key => TIER_DEFINITIONS[key].id === tierId
  );

  if (tierKey) {
    return new SubscriptionTier(TIER_DEFINITIONS[tierKey]);
  }

  return null;
}

/**
 * Get tier by name
 *
 * @param {string} tierName - Tier name
 * @returns {SubscriptionTier|null} - Tier instance or null
 */
export function getTierByName(tierName) {
  const tierKey = Object.keys(TIER_DEFINITIONS).find(
    key => TIER_DEFINITIONS[key].name.toLowerCase() === tierName.toLowerCase()
  );

  if (tierKey) {
    return new SubscriptionTier(TIER_DEFINITIONS[tierKey]);
  }

  return null;
}

/**
 * Get feature description
 *
 * @param {string} featureName - Feature name
 * @returns {string} - Feature description
 */
export function getFeatureDescription(featureName) {
  return FEATURE_DEFINITIONS[featureName] || 'Feature description not available';
}

/**
 * Get all available features
 *
 * @returns {Array<string>} - Array of all feature names
 */
export function getAllFeatures() {
  return Object.keys(FEATURE_DEFINITIONS);
}

/**
 * Get tier upgrade path
 *
 * @param {string} currentTierId - Current tier ID
 * @returns {Array<SubscriptionTier>} - Array of possible upgrade tiers
 */
export function getUpgradePath(currentTierId) {
  const currentTier = getTierById(currentTierId);
  if (!currentTier) { return []; }

  return getActiveTiers().filter(tier => currentTier.canUpgradeTo(tier));
}

/**
 * Get tier downgrade path
 *
 * @param {string} currentTierId - Current tier ID
 * @returns {Array<SubscriptionTier>} - Array of possible downgrade tiers
 */
export function getDowngradePath(currentTierId) {
  const currentTier = getTierById(currentTierId);
  if (!currentTier) { return []; }

  return getActiveTiers().filter(tier => currentTier.canDowngradeTo(tier));
}

// Export static tier instances for convenience
export const TIERS = createTierInstances();

export default {
  TIER_DEFINITIONS,
  FEATURE_DEFINITIONS,
  TIERS,
  createTierInstances,
  getActiveTiers,
  getTierById,
  getTierByName,
  getFeatureDescription,
  getAllFeatures,
  getUpgradePath,
  getDowngradePath
};
