/**
 * Validation Rules Configuration
 * Contains validation rules for document fields and data integrity checks
 */

// Field validation rules
export const FIELD_VALIDATION_RULES = {
  // Required fields for all documents
  required: {
    common: ['kind', 'number', 'issue_date', 'seller_name', 'buyer_name'],
    correction: ['invoice_id', 'from_invoice_id', 'corrected_content_before', 'corrected_content_after'],
    advance: ['invoice_id'],
    final: ['from_invoice_id'],
    accounting_note: ['accounting_note_kind']
  },

  // Field format validation
  format: {
    // Tax ID validation (Polish NIP format)
    tax_no: {
      pattern: /^\d{10}$/,
      message: 'Tax ID must be 10 digits'
    },

    // Postal code validation (Polish format)
    post_code: {
      pattern: /^\d{2}-\d{3}$/,
      message: 'Postal code must be in format XX-XXX'
    },

    // Email validation
    email: {
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      message: 'Invalid email format'
    },

    // Date validation (YYYY-MM-DD format)
    date: {
      pattern: /^\d{4}-\d{2}-\d{2}$/,
      message: 'Date must be in format YYYY-MM-DD'
    },

    // Currency validation
    currency: {
      pattern: /^[A-Z]{3}$/,
      message: 'Currency must be 3-letter code (e.g., PLN, EUR)'
    },

    // VAT rate validation
    vat_rate: {
      pattern: /^(23|8|5|0|zw|np)$/,
      message: 'VAT rate must be 23, 8, 5, 0, zw, or np'
    }
  },

  // Numeric field validation
  numeric: {
    fields: [
      'quantity', 'price_net', 'price_gross', 'total_price_net',
      'total_price_gross', 'tax_amount', 'total_net', 'total_vat', 'total_gross'
    ],
    rules: {
      min: 0,
      precision: 2,
      message: 'Must be a positive number with max 2 decimal places'
    }
  },

  // String length validation
  length: {
    number: { max: 50, message: 'Document number too long' },
    seller_name: { max: 200, message: 'Seller name too long' },
    buyer_name: { max: 200, message: 'Buyer name too long' },
    description: { max: 1000, message: 'Description too long' },
    internal_note: { max: 500, message: 'Internal note too long' }
  }
};

// Business logic validation rules
export const BUSINESS_VALIDATION_RULES = {
  // Total amount validation tolerance (in currency units)
  totalAmountTolerance: 0.1,

  // Position validation rules
  positions: {
    // Required fields for each position
    required: ['name', 'quantity', 'price_net', 'tax'],

    // Numeric fields that must be positive
    positiveNumeric: ['quantity', 'price_net', 'price_gross', 'total_price_net', 'total_price_gross'],

    // VAT calculation tolerance
    vatCalculationTolerance: 0.01
  },

  // Date validation rules
  dates: {
    // Issue date cannot be in the future
    maxIssueDate: () => new Date(),

    // Sell date cannot be more than 1 year in the past
    minSellDate: () => {
      const date = new Date();
      date.setFullYear(date.getFullYear() - 1);
      return date;
    },

    // Payment date cannot be in the past (for new invoices)
    minPaymentDate: () => new Date()
  },

  // Company validation rules
  company: {
    // Required fields for company validation
    required: ['name', 'tax_no'],

    // Polish company validation
    polish: {
      taxNoLength: 10,
      postCodePattern: /^\d{2}-\d{3}$/
    }
  }
};

// Data integrity validation rules
export const DATA_INTEGRITY_RULES = {
  // Cross-field validation rules
  crossField: {
    // Total amounts must match sum of positions
    totalAmountsMatch: {
      fields: ['total_net', 'total_vat', 'total_gross'],
      tolerance: 0.1
    },

    // Gross amount must equal net + VAT
    grossEqualsNetPlusVat: {
      tolerance: 0.01
    },

    // Payment date must be after issue date
    paymentAfterIssue: {
      fields: ['issue_date', 'payment_to']
    }
  },

  // Position-level validation
  positionIntegrity: {
    // Total price must equal quantity * unit price
    totalPriceCalculation: {
      tolerance: 0.01
    },

    // VAT amount calculation
    vatAmountCalculation: {
      tolerance: 0.01
    }
  }
};

// Validation error types
export const VALIDATION_ERROR_TYPES = {
  REQUIRED_FIELD_MISSING: 'required_field_missing',
  INVALID_FORMAT: 'invalid_format',
  INVALID_VALUE: 'invalid_value',
  BUSINESS_RULE_VIOLATION: 'business_rule_violation',
  DATA_INTEGRITY_ERROR: 'data_integrity_error',
  CALCULATION_MISMATCH: 'calculation_mismatch'
};

// Validation severity levels
export const VALIDATION_SEVERITY = {
  ERROR: 'error', // Blocks processing
  WARNING: 'warning', // Allows processing but shows warning
  INFO: 'info' // Informational only
};

/**
 * Validate a single field value
 * @param {string} fieldName - Name of the field
 * @param {any} value - Value to validate
 * @param {string} documentType - Document type for context
 * @returns {Object} - Validation result
 */
export function validateField(fieldName, value, documentType = null) {
  const errors = [];
  const warnings = [];

  // Check if field is required
  const requiredFields = FIELD_VALIDATION_RULES.required.common;
  const typeSpecificRequired = documentType ? FIELD_VALIDATION_RULES.required[documentType] : [];

  if ([...requiredFields, ...typeSpecificRequired].includes(fieldName)) {
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      errors.push({
        type: VALIDATION_ERROR_TYPES.REQUIRED_FIELD_MISSING,
        severity: VALIDATION_SEVERITY.ERROR,
        message: `${fieldName} is required`
      });
      return { valid: false, errors, warnings };
    }
  }

  // Skip further validation if value is empty and not required
  if (!value || (typeof value === 'string' && value.trim() === '')) {
    return { valid: true, errors, warnings };
  }

  // Format validation
  const formatRule = FIELD_VALIDATION_RULES.format[fieldName];
  if (formatRule && typeof value === 'string') {
    if (!formatRule.pattern.test(value)) {
      errors.push({
        type: VALIDATION_ERROR_TYPES.INVALID_FORMAT,
        severity: VALIDATION_SEVERITY.ERROR,
        message: formatRule.message
      });
    }
  }

  // Numeric validation
  if (FIELD_VALIDATION_RULES.numeric.fields.includes(fieldName)) {
    const numValue = parseFloat(value);
    if (isNaN(numValue)) {
      errors.push({
        type: VALIDATION_ERROR_TYPES.INVALID_VALUE,
        severity: VALIDATION_SEVERITY.ERROR,
        message: 'Must be a valid number'
      });
    } else if (numValue < FIELD_VALIDATION_RULES.numeric.rules.min) {
      errors.push({
        type: VALIDATION_ERROR_TYPES.INVALID_VALUE,
        severity: VALIDATION_SEVERITY.ERROR,
        message: FIELD_VALIDATION_RULES.numeric.rules.message
      });
    }
  }

  // Length validation
  const lengthRule = FIELD_VALIDATION_RULES.length[fieldName];
  if (lengthRule && typeof value === 'string') {
    if (value.length > lengthRule.max) {
      errors.push({
        type: VALIDATION_ERROR_TYPES.INVALID_VALUE,
        severity: VALIDATION_SEVERITY.ERROR,
        message: lengthRule.message
      });
    }
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Validate document data integrity
 * @param {Object} documentData - Document data to validate
 * @returns {Object} - Validation result
 */
export function validateDocumentIntegrity(documentData) {
  const errors = [];
  const warnings = [];

  // Validate total amounts match positions
  if (documentData.positions && documentData.positions.length > 0) {
    const positionsTotal = documentData.positions.reduce((sum, pos) => {
      return sum + (parseFloat(pos.total_price_net) || 0);
    }, 0);

    const documentTotal = parseFloat(documentData.total_net) || 0;
    const difference = Math.abs(positionsTotal - documentTotal);

    if (difference > DATA_INTEGRITY_RULES.crossField.totalAmountsMatch.tolerance) {
      errors.push({
        type: VALIDATION_ERROR_TYPES.CALCULATION_MISMATCH,
        severity: VALIDATION_SEVERITY.ERROR,
        message: `Total amount mismatch: positions sum ${positionsTotal}, document total ${documentTotal}`
      });
    }
  }

  // Validate gross = net + VAT
  const totalNet = parseFloat(documentData.total_net) || 0;
  const totalVat = parseFloat(documentData.total_vat) || 0;
  const totalGross = parseFloat(documentData.total_gross) || 0;

  if (totalNet > 0 && totalVat > 0 && totalGross > 0) {
    const calculatedGross = totalNet + totalVat;
    const difference = Math.abs(calculatedGross - totalGross);

    if (difference > DATA_INTEGRITY_RULES.crossField.grossEqualsNetPlusVat.tolerance) {
      warnings.push({
        type: VALIDATION_ERROR_TYPES.CALCULATION_MISMATCH,
        severity: VALIDATION_SEVERITY.WARNING,
        message: `Gross amount calculation: ${totalNet} + ${totalVat} = ${calculatedGross}, but document shows ${totalGross}`
      });
    }
  }

  // Validate dates
  if (documentData.issue_date && documentData.payment_to) {
    const issueDate = new Date(documentData.issue_date);
    const paymentDate = new Date(documentData.payment_to);

    if (paymentDate < issueDate) {
      errors.push({
        type: VALIDATION_ERROR_TYPES.BUSINESS_RULE_VIOLATION,
        severity: VALIDATION_SEVERITY.ERROR,
        message: 'Payment date cannot be before issue date'
      });
    }
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Validate position data
 * @param {Object} position - Position data to validate
 * @returns {Object} - Validation result
 */
export function validatePosition(position) {
  const errors = [];
  const warnings = [];

  // Check required fields
  for (const field of BUSINESS_VALIDATION_RULES.positions.required) {
    if (!position[field] || (typeof position[field] === 'string' && position[field].trim() === '')) {
      errors.push({
        type: VALIDATION_ERROR_TYPES.REQUIRED_FIELD_MISSING,
        severity: VALIDATION_SEVERITY.ERROR,
        message: `Position field ${field} is required`
      });
    }
  }

  // Validate numeric fields are positive
  for (const field of BUSINESS_VALIDATION_RULES.positions.positiveNumeric) {
    if (position[field] !== undefined) {
      const value = parseFloat(position[field]);
      if (isNaN(value) || value < 0) {
        errors.push({
          type: VALIDATION_ERROR_TYPES.INVALID_VALUE,
          severity: VALIDATION_SEVERITY.ERROR,
          message: `Position field ${field} must be a positive number`
        });
      }
    }
  }

  // Validate total price calculation
  const quantity = parseFloat(position.quantity) || 0;
  const priceNet = parseFloat(position.price_net) || 0;
  const totalPriceNet = parseFloat(position.total_price_net) || 0;

  if (quantity > 0 && priceNet > 0 && totalPriceNet > 0) {
    const calculatedTotal = quantity * priceNet;
    const difference = Math.abs(calculatedTotal - totalPriceNet);

    if (difference > DATA_INTEGRITY_RULES.positionIntegrity.totalPriceCalculation.tolerance) {
      warnings.push({
        type: VALIDATION_ERROR_TYPES.CALCULATION_MISMATCH,
        severity: VALIDATION_SEVERITY.WARNING,
        message: `Total price calculation: ${quantity} × ${priceNet} = ${calculatedTotal}, but position shows ${totalPriceNet}`
      });
    }
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings
  };
}
