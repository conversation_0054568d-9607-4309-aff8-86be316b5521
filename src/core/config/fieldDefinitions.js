/**
 * Enhanced Field Definitions Configuration for 90% Accuracy Target
 * Extracted from core/prompt_generators/accounting_fields.js
 * Contains all field definitions, document types, validation rules, and enhanced accuracy features
 *
 * Features:
 * - Multi-language support
 * - Confidence scoring
 * - Field importance levels
 * - Enhanced validation rules
 * - Pattern matching for 90% accuracy
 */

// ===== ENHANCED ACCURACY FEATURES =====

/**
 * Field importance levels for accuracy targeting
 */
export const FIELD_IMPORTANCE = {
  CRITICAL: 'critical',    // 95% accuracy target (amount, date, VAT)
  HIGH: 'high',           // 90% accuracy target (invoice number, company info)
  MEDIUM: 'medium',       // 85% accuracy target (address, description)
  LOW: 'low'             // 80% accuracy target (optional fields)
};

/**
 * Enhanced field extraction patterns for 90% accuracy
 */
export const ENHANCED_EXTRACTION_PATTERNS = {
  totalAmount: {
    importance: FIELD_IMPORTANCE.CRITICAL,
    patterns: [
      /(?:suma|total|razem|łącznie|lacznie|do\s*zapłaty|do\s*zaplaty)[\s:]*([0-9\s,.']+)/gi,
      /(?:brutto|gross)[\s:]*([0-9\s,.']+)/gi,
      /([0-9\s,.']+)[\s]*(?:zł|PLN|EUR|USD)/gi,
      /(?:kwota\s*brutto|total\s*gross)[\s:]*([0-9\s,.']+)/gi
    ],
    validation: {
      min: 0.01,
      max: 1000000,
      format: /^\d+([.,]\d{2})?$/
    }
  },

  invoiceNumber: {
    importance: FIELD_IMPORTANCE.HIGH,
    patterns: [
      /(?:faktura|invoice|nr|numer)[\s#:]*([A-Z0-9\/\-_]+)/gi,
      /([A-Z]{1,3}\/[0-9]{1,6}\/[0-9]{2,4})/gi,
      /([0-9]{1,6}\/[0-9]{2,4})/gi,
      /(?:number|nr\.?)[\s:]*([A-Z0-9\/\-_]+)/gi
    ],
    validation: {
      minLength: 3,
      maxLength: 50,
      format: /^[A-Z0-9\/\-_\s]+$/i
    }
  },

  issueDate: {
    importance: FIELD_IMPORTANCE.CRITICAL,
    patterns: [
      /(?:data\s*wystawienia|data\s*faktury|wystawiono|issued?)[\s:]*([0-9]{1,2}[.\-\/][0-9]{1,2}[.\-\/][0-9]{2,4})/gi,
      /([0-9]{1,2}[.\-\/][0-9]{1,2}[.\-\/][0-9]{2,4})/g,
      /([0-9]{4}[.\-\/][0-9]{1,2}[.\-\/][0-9]{1,2})/g
    ],
    validation: {
      format: ['DD.MM.YYYY', 'DD/MM/YYYY', 'DD-MM-YYYY', 'YYYY-MM-DD'],
      range: {
        min: '2020-01-01',
        max: '2030-12-31'
      }
    }
  },

  sellerTaxId: {
    importance: FIELD_IMPORTANCE.HIGH,
    patterns: [
      /(?:nip|tax\s*id|vat\s*id)[\s:]*([0-9\-\s]{10,15})/gi,
      /([0-9]{3}[\-\s]?[0-9]{3}[\-\s]?[0-9]{2}[\-\s]?[0-9]{2})/g
    ],
    validation: {
      format: /^[0-9\-\s]{10,15}$/,
      checksum: 'nip_algorithm'
    }
  }
};

/**
 * Confidence scoring system for 90% accuracy
 */
export const CONFIDENCE_SCORING = {
  THRESHOLDS: {
    [FIELD_IMPORTANCE.CRITICAL]: 85,
    [FIELD_IMPORTANCE.HIGH]: 80,
    [FIELD_IMPORTANCE.MEDIUM]: 75,
    [FIELD_IMPORTANCE.LOW]: 70
  },

  WEIGHTS: {
    pattern_match: 0.4,
    format_validation: 0.25,
    context_position: 0.2,
    cross_validation: 0.15
  },

  BONUSES: {
    multiple_pattern_match: 10,
    calculation_validation: 15,
    format_perfect_match: 10,
    context_high_relevance: 5
  }
};

/**
 * Language-specific field mappings for enhanced accuracy
 */
export const LANGUAGE_MAPPINGS = {
  polish: {
    totalAmount: ['suma brutto', 'kwota brutto', 'razem brutto', 'do zapłaty'],
    issueDate: ['data wystawienia', 'data faktury', 'wystawiono'],
    invoiceNumber: ['numer faktury', 'nr faktury', 'faktura nr'],
    sellerName: ['sprzedawca', 'nazwa sprzedawcy', 'firma'],
    buyerName: ['nabywca', 'nazwa nabywcy', 'klient'],
    vatAmount: ['kwota VAT', 'podatek VAT', 'VAT']
  },
  english: {
    totalAmount: ['total amount', 'total gross', 'amount due', 'total'],
    issueDate: ['issue date', 'invoice date', 'date issued'],
    invoiceNumber: ['invoice number', 'invoice no', 'number'],
    sellerName: ['seller name', 'company name', 'vendor'],
    buyerName: ['buyer name', 'customer name', 'client'],
    vatAmount: ['VAT amount', 'tax amount', 'VAT']
  }
};

// Common fields that appear in most accounting documents
export const COMMON_FIELDS = [
  'number',
  'issue_date',
  'place',
  'sell_date',
  'seller_name',
  'seller_tax_no',
  'seller_bank_account',
  'seller_bank',
  'seller_post_code',
  'seller_city',
  'seller_street',
  'seller_country',
  'buyer_name',
  'buyer_tax_no',
  'buyer_post_code',
  'buyer_city',
  'buyer_street',
  'buyer_country',
  'payment_type',
  'payment_to',
  'currency',
  'positions'
];

// Document kinds with their descriptions
export const DOCUMENT_KINDS = {
  'vat': 'VAT invoice',
  'proforma': 'Proforma invoice',
  'bill': 'Bill',
  'receipt': 'Receipt',
  'advance': 'Advance invoice',
  'final': 'Final invoice',
  'correction': 'Correction invoice',
  'vat_mp': 'MP invoice',
  'invoice_other': 'Other invoice',
  'vat_margin': 'Margin invoice',
  'kp': 'Cash receipt',
  'kw': 'Cash payment',
  'estimate': 'Estimate/Order',
  'vat_rr': 'RR invoice',
  'correction_note': 'Correction note',
  'accounting_note': 'Accounting note',
  'client_order': 'Client order document',
  'dw': 'Internal document',
  'wnt': 'Intra-community acquisition',
  'wdt': 'Intra-community supply',
  'import_service': 'Import of services',
  'import_service_eu': 'Import of services from EU',
  'import_products': 'Import of goods - simplified procedure',
  'export_products': 'Export of goods'
};

// Specific fields for each document kind
export const DOCUMENT_KIND_FIELDS = {
  // VAT Invoice specific fields
  'vat': [
    'income',
    'split_payment',
    'buyer_email',
    'seller_tax_no_kind',
    'buyer_tax_no_kind',
    'status',
    'paid',
    'gtu_codes',
    'procedure_designations'
  ],

  // Proforma Invoice specific fields
  'proforma': [
    'income',
    'buyer_email',
    'status',
    'oid'
  ],

  // Bill specific fields
  'bill': [
    'income',
    'buyer_email',
    'status',
    'paid'
  ],

  // Receipt specific fields
  'receipt': [
    'income',
    'status',
    'paid'
  ],

  // Advance Invoice specific fields
  'advance': [
    'income',
    'invoice_id',
    'split_payment',
    'buyer_email'
  ],

  // Final Invoice specific fields
  'final': [
    'income',
    'from_invoice_id',
    'split_payment',
    'buyer_email'
  ],

  // Correction Invoice specific fields
  'correction': [
    'income',
    'invoice_id',
    'from_invoice_id',
    'corrected_content_before',
    'corrected_content_after',
    'buyer_email'
  ],

  // VAT MP Invoice specific fields
  'vat_mp': [
    'income',
    'split_payment',
    'buyer_email'
  ],

  // Other Invoice specific fields
  'invoice_other': [
    'income',
    'buyer_email'
  ],

  // Margin Invoice specific fields
  'vat_margin': [
    'income',
    'buyer_email'
  ],

  // Cash Receipt specific fields
  'kp': [
    'income',
    'status',
    'paid'
  ],

  // Cash Payment specific fields
  'kw': [
    'income',
    'status',
    'paid'
  ],

  // Estimate specific fields
  'estimate': [
    'income',
    'buyer_email',
    'oid'
  ],

  // VAT RR Invoice specific fields
  'vat_rr': [
    'income',
    'split_payment',
    'buyer_email'
  ],

  // Correction Note specific fields
  'correction_note': [
    'invoice_id',
    'from_invoice_id',
    'corrected_content_before',
    'corrected_content_after'
  ],

  // Accounting Note specific fields
  'accounting_note': [
    'accounting_note_kind',
    'buyer_email'
  ],

  // Client Order specific fields
  'client_order': [
    'buyer_email',
    'oid',
    'oid_unique'
  ],

  // Internal Document specific fields
  'dw': [
    'description',
    'description_long'
  ],

  // Intra-community Acquisition specific fields
  'wnt': [
    'income',
    'buyer_email',
    'seller_country',
    'buyer_country',
    'reverse_charge'
  ],

  // Intra-community Supply specific fields
  'wdt': [
    'income',
    'buyer_email',
    'seller_country',
    'buyer_country'
  ],

  // Import Services specific fields
  'import_service': [
    'income',
    'seller_country',
    'buyer_country',
    'reverse_charge'
  ],

  // Import Services EU specific fields
  'import_service_eu': [
    'income',
    'seller_country',
    'buyer_country',
    'reverse_charge'
  ],

  // Import Products specific fields
  'import_products': [
    'income',
    'seller_country',
    'buyer_country'
  ],

  // Export Products specific fields
  'export_products': [
    'income',
    'seller_country',
    'buyer_country'
  ]
};

// Field descriptions with accounting expertise
export const FIELD_DESCRIPTIONS = {
  'number': 'invoice/document number following continuous numbering within fiscal year',
  'kind': 'type of accounting document according to VAT law',
  'income': 'accounting classification for revenue recognition: 1=income/sales revenue (company as seller), 0=expense/purchase cost (company as buyer)',
  'issue_date': 'document issue date (mandatory for VAT purposes)',
  'place': 'place of invoice issuance (legally required)',
  'sell_date': 'sale/service execution date (VAT obligation date)',
  'category_id': 'accounting category for automatic bookkeeping',
  'department_id': 'cost center/profit center',
  'accounting_kind': 'cost classification for accounting records and VAT settlements',
  'seller_name': 'full seller name according to business registry',
  'seller_tax_no': "seller's tax ID (without PL prefix, format: **********)",
  'seller_tax_no_kind': 'type of identification number (NIP/REGON/PESEL)',
  'seller_bank_account': "seller's bank account number (IBAN or domestic format)",
  'seller_bank': "name of bank holding seller's account",
  'seller_post_code': "seller's postal code (format XX-XXX)",
  'seller_city': "seller's city",
  'seller_street': "seller's address (street, number)",
  'seller_country': "seller's country code (PL for Poland, ISO 3166 format)",
  'seller_email': "seller's email address for correspondence",
  'seller_bdo_no': 'number in VAT White List (BDO)',
  'use_invoice_issuer': 'whether document is issued by authorized person',
  'invoice_issuer': 'details of person issuing the document',
  'client_id': 'buyer identifier in accounting system',
  'buyer_name': 'full buyer name according to registration documents',
  'buyer_tax_no': "buyer's tax ID (without PL prefix, format: **********)",
  'buyer_tax_no_kind': "buyer's identification number type",
  'disable_tax_no_validation': 'disable tax ID verification in VAT White List',
  'buyer_post_code': "buyer's postal code (format XX-XXX)",
  'buyer_city': "buyer's city",
  'buyer_street': "buyer's address (street, number)",
  'buyer_country': "buyer's country code (PL for Poland, ISO 3166 format)",
  'buyer_note': 'additional information about buyer',
  'buyer_email': "buyer's email",
  'recipient_id': 'recipient ID (client ID from system)',
  'recipient_name': 'recipient name',
  'recipient_street': 'recipient street',
  'recipient_post_code': 'recipient postal code',
  'recipient_city': 'recipient city',
  'recipient_country': 'recipient country',
  'additional_info': 'additional information about transaction',
  'additional_info_desc': 'description of additional information',
  'product_id': 'product identifier in system',
  'show_discount': 'whether to display discount information',
  'payment_type': 'payment method (transfer/cash/card/etc.)',
  'payment_to': 'payment deadline date',
  'payment_to_kind': 'payment deadline type (days/date/immediate)',
  'bank_account': 'bank account for payment',
  'bank_account_id': 'bank account ID in system',
  'currency': 'transaction currency (PLN/EUR/USD/etc.)',
  'lang': 'document language code',
  'exchange_currency': 'exchange rate currency',
  'exchange_kind': 'exchange rate type',
  'exchange_currency_rate': 'currency exchange rate',
  'exchange_date': 'exchange rate date',
  'internal_note': 'internal note (not printed on document)',
  'invoice_id': 'related invoice ID for corrections/advances',
  'from_invoice_id': 'source invoice ID for final invoices',
  'oid': 'order ID reference',
  'oid_unique': 'unique order identifier',
  'warehouse_id': 'warehouse identifier',
  'seller_person': 'seller contact person',
  'buyer_first_name': 'buyer first name',
  'buyer_company': 'buyer company flag',
  'description': 'document description',
  'description_footer': 'footer description',
  'description_long': 'detailed description',
  'invoice_template_id': 'template ID for document formatting',
  'description_long_footer': 'detailed footer description',
  'status': 'document status (draft/sent/paid/etc.)',
  'paid': 'payment status flag',
  'oid2': 'secondary order ID',
  'warehouse_id2': 'secondary warehouse ID',
  'exchange_note': 'exchange rate note',
  'accounting_note_kind': 'type of accounting note',
  'corrected_content_before': 'content before correction',
  'corrected_content_after': 'content after correction',
  'split_payment': 'split payment mechanism flag',
  'gtu_codes': 'GTU procedure codes for VAT',
  'procedure_designations': 'procedure designations for VAT',
  'reverse_charge': 'reverse charge mechanism flag',
  'positions': 'array of invoice line items/positions'
};

// Fields with fixed/predefined values
export const FIELDS_WITH_FIXED_VALUES = {
  'income': ['0', '1'],
  'use_invoice_issuer': ['0', '1'],
  'disable_tax_no_validation': ['0', '1'],
  'buyer_company': ['0', '1'],
  'paid': ['0', '1'],
  'split_payment': ['0', '1'],
  'reverse_charge': ['0', '1'],
  'show_discount': ['0', '1'],
  'status': ['draft', 'sent', 'paid', 'partial', 'overdue'],
  'payment_type': ['transfer', 'cash', 'card', 'blik', 'paypal', 'other'],
  'currency': ['PLN', 'EUR', 'USD', 'GBP', 'CHF'],
  'lang': ['pl', 'en', 'de', 'fr', 'es', 'it'],
  'seller_tax_no_kind': ['nip', 'regon', 'pesel', 'eu_vat_id'],
  'buyer_tax_no_kind': ['nip', 'regon', 'pesel', 'eu_vat_id'],
  'accounting_kind': ['cost', 'revenue', 'asset', 'liability', 'equity'],
  'accounting_note_kind': ['correction', 'adjustment', 'reclassification', 'other']
};

// Position/line item field definitions
export const POSITION_FIELDS = {
  'name': 'product/service name',
  'description': 'detailed description of product/service',
  'additional_info': 'additional information about position',
  'quantity': 'quantity of items',
  'quantity_unit': 'unit of measure (pcs/kg/m/etc.)',
  'price_net': 'net unit price (without VAT)',
  'tax': 'VAT rate percentage (23/8/5/0/zw/np)',
  'price_gross': 'gross unit price (with VAT)',
  'total_price_net': 'total net amount for position',
  'total_price_gross': 'total gross amount for position',
  'tax_amount': 'VAT amount for position',
  'code': 'product code/SKU',
  'discount': 'discount percentage',
  'discount_percent': 'discount as percentage',
  'product_id': 'product ID in system',
  'kind': 'position type (normal/correction/etc.)'
};

// VAT rates allowed in Poland
export const VAT_RATES = ['23', '8', '5', '0', 'zw', 'np'];

// Document types that typically have positions/line items
export const DOCUMENT_TYPES_WITH_POSITIONS = [
  'vat', 'proforma', 'bill', 'receipt', 'advance', 'final', 'correction',
  'vat_mp', 'invoice_other', 'vat_margin', 'wdt', 'wnt', 'import_service',
  'import_service_eu', 'import_products', 'export_products'
];

// Required fields for each document type
export const REQUIRED_FIELDS = {
  'common': ['kind', 'number', 'issue_date', 'seller_name', 'buyer_name'],
  'correction': ['invoice_id', 'from_invoice_id', 'corrected_content_before', 'corrected_content_after'],
  'advance': ['invoice_id'],
  'final': ['invoice_id'],
  'accounting_note': ['accounting_note_kind']
};

// Additional fields from DocumentFields (non-overlapping with AccountingFields)
export const ADDITIONAL_DOCUMENT_FIELDS = [
  'document_name',
  'total_net',
  'total_vat',
  'total_gross',
  'accounting_date',
  'contract_reference',
  'summary',
  'seller_phone',
  'buyer_phone',
  'income_type',
  'document_type',
  'ocr_used'
];

// Additional field descriptions (from DocumentFields)
export const ADDITIONAL_FIELD_DESCRIPTIONS = {
  'document_name': "full document name from header (e.g. 'VAT INVOICE', 'CORRECTION INVOICE')",
  'total_net': 'control sum of net values (VAT tax base)',
  'total_vat': 'control sum of VAT tax due (for JPK_VAT settlement)',
  'total_gross': 'control sum to pay (gross amount = net + VAT)',
  'accounting_date': 'accounting date for VAT records (may differ from issue date)',
  'contract_reference': 'contract/order number for linking with commercial documentation',
  'summary': 'synthetic description of document content for accounting purposes',
  'seller_phone': "seller's phone for document-related contact",
  'buyer_phone': "buyer's phone for contact",
  'income_type': 'income classification for tax and accounting purposes',
  'document_type': 'document type according to VAT classification',
  'ocr_used': 'technical information about data extraction method',
  'payment_to_kind': 'method of determining payment deadline (days/date/immediate)'
};

// Additional fields with fixed values (from DocumentFields)
export const ADDITIONAL_FIELDS_WITH_FIXED_VALUES = {
  'income_type': ['income', 'expense', 'other'],
  'payment_to_kind': ['days', 'date', 'immediate', 'on_delivery'],
  'ocr_used': ['true', 'false']
};

// Document types that require additional fields
export const DOCUMENT_TYPES_REQUIRING_ADDITIONAL_FIELDS = [
  'vat', 'proforma', 'bill', 'receipt', 'advance', 'final',
  'correction', 'vat_mp', 'invoice_other', 'vat_margin',
  'kp', 'kw', 'estimate', 'vat_rr', 'wdt', 'ue', 'accounting_note'
];

// Required additional fields by document type
export const REQUIRED_ADDITIONAL_FIELDS = {
  'vat': ['document_name', 'total_gross'],
  'proforma': ['document_name', 'total_gross'],
  'bill': ['document_name', 'total_gross'],
  'receipt': ['document_name', 'total_gross'],
  'default': ['document_name']
};

// ===== LOCAL TESTING (Node.js equivalent of Python's if __name__ == '__main__') =====
if (typeof require !== 'undefined' && require.main === module) {
  console.log('🧪 Running local tests for fieldDefinitions...');

  console.log('✅ Test 1: Document kinds validation');
  console.log('📝 Available document kinds:', Object.keys(DOCUMENT_KINDS).length);
  console.log('📝 Sample document kinds:', Object.keys(DOCUMENT_KINDS).slice(0, 5));

  console.log('\n✅ Test 2: Common fields structure');
  console.log('📝 Common fields count:', COMMON_FIELDS.length);
  console.log('📝 Common fields sample:', COMMON_FIELDS.slice(0, 5));

  console.log('\n✅ Test 3: Document kind specific fields');
  const vatFields = DOCUMENT_KIND_FIELDS.vat;
  console.log('📝 VAT invoice specific fields:', vatFields);

  const correctionFields = DOCUMENT_KIND_FIELDS.correction;
  console.log('📝 Correction invoice specific fields:', correctionFields);

  console.log('\n✅ Test 4: Field descriptions validation');
  const sampleFields = ['number', 'income', 'seller_name', 'buyer_name', 'total_gross'];
  sampleFields.forEach(field => {
    const description = FIELD_DESCRIPTIONS[field];
    console.log(`📝 ${field}: ${description ? description.substring(0, 50) + '...' : 'NO DESCRIPTION'}`);
  });

  console.log('\n✅ Test 5: Fields with fixed values');
  console.log('📝 Income values:', FIELDS_WITH_FIXED_VALUES.income);
  console.log('📝 Currency values:', FIELDS_WITH_FIXED_VALUES.currency);
  console.log('📝 Payment type values:', FIELDS_WITH_FIXED_VALUES.payment_type);

  console.log('\n✅ Test 6: Position fields validation');
  console.log('📝 Position fields count:', Object.keys(POSITION_FIELDS).length);
  console.log('📝 Position fields:', Object.keys(POSITION_FIELDS));

  console.log('\n✅ Test 7: VAT rates validation');
  console.log('📝 Available VAT rates:', VAT_RATES);

  console.log('\n✅ Test 8: Document types with positions');
  console.log('📝 Document types with positions count:', DOCUMENT_TYPES_WITH_POSITIONS.length);
  console.log('📝 Sample types with positions:', DOCUMENT_TYPES_WITH_POSITIONS.slice(0, 5));

  console.log('\n✅ Test 9: Required fields validation');
  console.log('📝 Common required fields:', REQUIRED_FIELDS.common);
  console.log('📝 Correction required fields:', REQUIRED_FIELDS.correction);

  console.log('\n✅ Test 10: Additional document fields');
  console.log('📝 Additional fields count:', ADDITIONAL_DOCUMENT_FIELDS.length);
  console.log('📝 Additional fields sample:', ADDITIONAL_DOCUMENT_FIELDS.slice(0, 5));

  console.log('\n✅ Test 11: Field integrity check');
  let missingDescriptions = [];
  COMMON_FIELDS.forEach(field => {
    if (!FIELD_DESCRIPTIONS[field] && !ADDITIONAL_FIELD_DESCRIPTIONS[field]) {
      missingDescriptions.push(field);
    }
  });

  if (missingDescriptions.length > 0) {
    console.log('⚠️  Fields missing descriptions:', missingDescriptions);
  } else {
    console.log('✅ All common fields have descriptions');
  }

  console.log('\n✅ Test 12: Document type coverage check');
  const allDocumentTypes = Object.keys(DOCUMENT_KINDS);
  const typesWithFields = Object.keys(DOCUMENT_KIND_FIELDS);
  const missingFieldDefinitions = allDocumentTypes.filter(type => !typesWithFields.includes(type));

  if (missingFieldDefinitions.length > 0) {
    console.log('⚠️  Document types missing field definitions:', missingFieldDefinitions);
  } else {
    console.log('✅ All document types have field definitions');
  }

  console.log('\n🎉 All tests completed for fieldDefinitions');
}

// ===== ENHANCED ACCURACY HELPER FUNCTIONS =====

/**
 * Get field extraction patterns for enhanced accuracy
 * @param {string} fieldName - Field name
 * @returns {Object|null} Field extraction patterns
 */
export function getEnhancedFieldPatterns(fieldName) {
  return ENHANCED_EXTRACTION_PATTERNS[fieldName] || null;
}

/**
 * Get confidence threshold for field
 * @param {string} fieldName - Field name
 * @returns {number} Confidence threshold
 */
export function getConfidenceThreshold(fieldName) {
  const patterns = getEnhancedFieldPatterns(fieldName);
  if (!patterns) return 70;

  return CONFIDENCE_SCORING.THRESHOLDS[patterns.importance] || 70;
}

/**
 * Get all fields by importance level
 * @param {string} importance - Importance level
 * @returns {Array} Array of field names
 */
export function getFieldsByImportance(importance) {
  return Object.entries(ENHANCED_EXTRACTION_PATTERNS)
    .filter(([_, definition]) => definition.importance === importance)
    .map(([name, _]) => name);
}

/**
 * Calculate confidence score for field extraction
 * @param {Object} extractionResult - Extraction result
 * @param {string} fieldName - Field name
 * @returns {number} Confidence score (0-100)
 */
export function calculateConfidenceScore(extractionResult, fieldName) {
  const {
    patternMatches = 0,
    formatValid = false,
    contextRelevant = false,
    crossValidated = false
  } = extractionResult;

  const weights = CONFIDENCE_SCORING.WEIGHTS;
  const bonuses = CONFIDENCE_SCORING.BONUSES;

  let score = 0;

  // Base scoring
  score += (patternMatches > 0 ? 100 : 0) * weights.pattern_match;
  score += (formatValid ? 100 : 0) * weights.format_validation;
  score += (contextRelevant ? 100 : 0) * weights.context_position;
  score += (crossValidated ? 100 : 0) * weights.cross_validation;

  // Bonus scoring
  if (patternMatches > 1) score += bonuses.multiple_pattern_match;
  if (formatValid) score += bonuses.format_perfect_match;
  if (contextRelevant) score += bonuses.context_high_relevance;

  return Math.min(100, Math.round(score));
}

/**
 * Validate field value according to enhanced rules
 * @param {string} fieldName - Field name
 * @param {any} value - Field value
 * @returns {Object} Validation result
 */
export function validateFieldValue(fieldName, value) {
  const patterns = getEnhancedFieldPatterns(fieldName);
  if (!patterns || !patterns.validation) {
    return { isValid: true, errors: [] };
  }

  const validation = patterns.validation;
  const errors = [];

  // Format validation
  if (validation.format && !validation.format.test(String(value))) {
    errors.push(`Invalid format for ${fieldName}`);
  }

  // Range validation
  if (validation.min !== undefined && Number(value) < validation.min) {
    errors.push(`Value too small for ${fieldName} (min: ${validation.min})`);
  }

  if (validation.max !== undefined && Number(value) > validation.max) {
    errors.push(`Value too large for ${fieldName} (max: ${validation.max})`);
  }

  // Length validation
  if (validation.minLength && String(value).length < validation.minLength) {
    errors.push(`Value too short for ${fieldName} (min: ${validation.minLength})`);
  }

  if (validation.maxLength && String(value).length > validation.maxLength) {
    errors.push(`Value too long for ${fieldName} (max: ${validation.maxLength})`);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Get language-specific field aliases
 * @param {string} fieldName - Field name
 * @param {string} language - Language code
 * @returns {Array} Array of aliases
 */
export function getFieldAliases(fieldName, language = 'polish') {
  const mappings = LANGUAGE_MAPPINGS[language];
  return mappings ? (mappings[fieldName] || []) : [];
}
