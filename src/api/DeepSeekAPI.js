/**
 * DeepSeekAPI - Unified DeepSeek API service following 2025 best practices
 * Consolidates all DeepSeek API interactions with proper error handling,
 * response parsing, and standardized interface
 *
 * This service provides:
 * - Unified API calling interface
 * - OpenAI client and fetch API fallback
 * - Standardized response handling
 * - JSON extraction utilities
 * - Error handling and logging
 */

import { processingLogger } from '../utils/ProcessingLogger.js';
import analyticsService from '../services/AnalyticsService.js';
import { ANALYTICS_EVENTS } from '../models/AnalyticsData.js';

// OpenAI will be dynamically imported when needed
let OpenAI = null;


// Main function to call DeepSeek API with standardized parameters (Legacy wrapper)
async function callDeepSeekAPI(prompt, apiKey, options = {}) {
  try {
    // Set default options
    const defaultOptions = {
      model: 'deepseek-chat',
      temperature: 0.1,
      max_tokens: 8192,
      responseFormat: null,
      systemPrompt: null
    };

    // Merge default options with provided options
    const mergedOptions = { ...defaultOptions, ...options };

    // Prepare messages array
    const messages = [];

    // Add system prompt if provided
    if (mergedOptions.systemPrompt) {
      messages.push({
        role: 'system',
        content: mergedOptions.systemPrompt
      });
    }

    // Add user prompt
    messages.push({
      role: 'user',
      content: prompt
    });

    // Prepare request body
    const requestBody = {
      model: mergedOptions.model,
      messages: messages,
      temperature: mergedOptions.temperature,
      max_tokens: mergedOptions.max_tokens
    };

    // Add response format if specified
    if (mergedOptions.responseFormat) {
      requestBody.response_format = mergedOptions.responseFormat;
    }

    // Make the API call
    const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify(requestBody)
    });

    // Handle HTTP errors
    if (!response.ok) {
      const errorText = await response.text();
      console.error('DeepSeek API error:', errorText);
      // addDebugLog(`DeepSeek API error: ${response.status} ${response.statusText}`);
      return { success: false, error: `API error: ${response.status} ${response.statusText}` };
    }

    // Parse the response
    const result = await response.json();

    // Validate response format
    if (!result.choices || !result.choices[0] || !result.choices[0].message || !result.choices[0].message.content) {
      return { success: false, error: 'Invalid response from DeepSeek API' };
    }

    // Return the content
    return {
      success: true,
      content: result.choices[0].message.content,
      rawResponse: result
    };
  } catch (error) {
    console.error('Error calling DeepSeek API:', error);
    // addDebugLog(`DeepSeek API error: ${error.message}`);
    return { success: false, error: error.message };
  }
}


/**
 * Process text with DeepSeek API - MVP function for analyzer.js compatibility
 * @param {string} text - Text to process
 * @param {string} preferredLanguage - Preferred language
 * @param {Object} languageMapping - Language mapping instance
 * @param {Object} accountingFields - Accounting fields instance
 * @param {string} apiKey - API key
 * @returns {Promise<Object>} - Processing result
 */
async function processTextWithDeepSeek(text, preferredLanguage, languageMapping, accountingFields, apiKey) {
  try {
    // Use the document analysis prompt generation method from AccountingFields
    const { prompt, systemPrompt, options } = accountingFields.generateDocumentAnalysisPrompt(text, languageMapping);

    // Call DeepSeek API with JSON response format
    const response = await callDeepSeekAPI(prompt, apiKey, {
      ...options,
      systemPrompt
    });

    if (response.success) {
      // Parse the response
      const parsedContent = this.extractJSON(response.content);

      if (parsedContent) {
        return {
          success: true,
          refinedText: parsedContent.refinedText || text,
          language: parsedContent.language || preferredLanguage,
          documentKind: parsedContent.documentKind || 'invoice_other'
        };
      }
    }

    // Fallback if parsing fails
    return {
      success: false,
      error: 'Failed to parse API response',
      refinedText: text,
      language: preferredLanguage,
      documentKind: 'invoice_other'
    };
  } catch (error) {
    console.error('DeepSeek Processing Error:', error);
    return {
      success: false,
      error: error.message,
      refinedText: text,
      language: preferredLanguage,
      documentKind: 'invoice_other'
    };
  }
}

/**
 * Extract fields based on document kind - MVP function for analyzer.js compatibility
 * @param {string} text - Document text
 * @param {string} kind - Document kind
 * @param {Object} languageMapping - Language mapping instance
 * @param {Object} accountingFields - Accounting fields instance
 * @param {string} apiKey - API key
 * @param {string} detectedLang - Detected language (optional)
 * @param {Object} companyInfo - Company information from chrome extension settings (optional)
 * @returns {Promise<string>} - Extracted fields as JSON string
 */
async function extractFieldsWithDeepSeek(text, kind, languageMapping, accountingFields, apiKey, detectedLang = null, companyInfo = null) {
  try {
    // Generate AI prompt using AccountingFields method with company info
    const prompt = accountingFields.generateDocumentMetadataPrompt(kind, detectedLang, companyInfo);

    // Call DeepSeek API with JSON response format
    const response = await callDeepSeekAPI(`${prompt}\n\nDocument text: "${text}"`, apiKey, {
      temperature: 0.1,
      systemPrompt: `You are an expert in extracting structured data from accounting documents into JSON. CRITICAL: Use exact English field names from the provided structure. Do not translate field names to any other language. Only field values should be in the document language (${detectedLang || 'original language'}).`,
      max_tokens: 8192,
      responseFormat: { type: 'json_object' }
    });

    if (response.success) {
      return response.content;
    }
    throw new Error(response.error || 'Failed to extract fields');

  } catch (error) {
    console.error('DeepSeek Field Extraction Error:', error);
    throw error;
  }
}


class DeepSeekAPI {
  constructor() {
    this.baseURL = 'https://api.deepseek.com/v1';
    this.defaultModel = 'deepseek-chat';

    // Default options for API calls
    this.defaultOptions = {
      model: this.defaultModel,
      temperature: 0.1,
      max_tokens: 8192,
      responseFormat: { type: 'json_object' }
    };
  }

  /**
     * Create OpenAI-compatible client for DeepSeek API
     * @param {string} apiKey - API key
     * @returns {Object|null} - OpenAI client or null if not available
     */
  createClient(apiKey) {
    if (typeof window === 'undefined' || !window.OpenAI) {
      console.warn('OpenAI client not available, will use fetch API instead');
      return null;
    }

    return new window.OpenAI({
      apiKey: apiKey,
      baseURL: this.baseURL,
      dangerouslyAllowBrowser: true
    });
  }

  /**
     * Main function to call DeepSeek API with standardized parameters
     * @param {string} prompt - User prompt
     * @param {string} apiKey - API key
     * @param {Object} options - API options
     * @returns {Object} - API response with success flag and content
     */
  async callAPI(prompt, apiKey, options = {}) {
    const uploadId = options.uploadId || null;

    try {
      if (uploadId) {
        processingLogger.info('deepseek_api', 'Starting DeepSeek API request', uploadId, {
          promptLength: prompt.length,
          model: options.model || this.defaultModel,
          temperature: options.temperature || this.defaultOptions.temperature,
          maxTokens: options.max_tokens || this.defaultOptions.max_tokens,
          hasSystemPrompt: !!options.systemPrompt
        });
        processingLogger.startTimer('deepseek_api', uploadId);
      }

      // Merge default options with provided options
      const mergedOptions = { ...this.defaultOptions, ...options };

      // Prepare messages array
      const messages = [];

      // Add system prompt if provided
      if (mergedOptions.systemPrompt) {
        messages.push({
          role: 'system',
          content: mergedOptions.systemPrompt
        });

        if (uploadId) {
          processingLogger.debug('deepseek_api', 'System prompt added to request', uploadId, {
            systemPromptLength: mergedOptions.systemPrompt.length
          });
        }
      }

      // Add user prompt
      messages.push({
        role: 'user',
        content: prompt
      });

      if (uploadId) {
        processingLogger.info('deepseek_api', 'Sending request to DeepSeek API', uploadId, {
          totalMessages: messages.length,
          totalTokensEstimate: this._estimateTokens(messages)
        });
      }

      // Try OpenAI client first, fallback to fetch
      const client = this.createClient(apiKey);
      let content;

      if (client) {
        if (uploadId) {
          processingLogger.debug('deepseek_api', 'Using OpenAI client for API call', uploadId);
        }
        content = await this._callWithOpenAIClient(client, messages, mergedOptions, uploadId);
      } else {
        if (uploadId) {
          processingLogger.debug('deepseek_api', 'Using fetch API for API call', uploadId);
        }
        content = await this._callWithFetch(apiKey, messages, mergedOptions, uploadId);
      }

      if (uploadId) {
        const duration = processingLogger.endTimer('deepseek_api', uploadId);

        // Enhanced logging with API interaction details
        processingLogger.logAPIInteraction(
          {
            prompt: prompt.substring(0, 200) + (prompt.length > 200 ? '...' : ''),
            model: mergedOptions.model,
            temperature: mergedOptions.temperature,
            maxTokens: mergedOptions.max_tokens,
            systemPrompt: options.systemPrompt ? options.systemPrompt.substring(0, 100) + '...' : null
          },
          {
            content: content.substring(0, 200) + (content.length > 200 ? '...' : ''),
            contentLength: content.length,
            model: mergedOptions.model
          },
          uploadId,
          {
            processingDurationMs: duration,
            estimatedTokens: this._estimateTokens(messages),
            requestMethod: client ? 'openai_client' : 'fetch_api'
          }
        );

        processingLogger.info('deepseek_api', 'DeepSeek API request completed successfully', uploadId, {
          responseLength: content.length,
          processingDurationMs: duration,
          model: mergedOptions.model
        });
      }

      // Record analytics event for successful AI analysis
      await analyticsService.recordEvent(ANALYTICS_EVENTS.AI_ANALYSIS_COMPLETED, {
        success: true,
        analysisTime: uploadId ? processingLogger.getTimerDuration('deepseek_api', uploadId) : 0,
        confidence: 85, // Default confidence for successful API calls
        analysisType: 'deepseek_analysis',
        apiProvider: 'deepseek',
        tokens: this._estimateTokens(messages)
      });

      return {
        success: true,
        content: content,
        model: mergedOptions.model
      };

    } catch (error) {
      if (uploadId) {
        processingLogger.endTimer('deepseek_api', uploadId);
        processingLogger.error('deepseek_api', 'DeepSeek API request failed', uploadId, {
          error: error.message,
          stack: error.stack,
          promptLength: prompt.length,
          apiKeyProvided: !!apiKey
        });
      }

      // Record analytics event for failed AI analysis
      await analyticsService.recordEvent(ANALYTICS_EVENTS.AI_ANALYSIS_COMPLETED, {
        success: false,
        analysisTime: uploadId ? processingLogger.getTimerDuration('deepseek_api', uploadId) : 0,
        analysisType: 'deepseek_analysis',
        apiProvider: 'deepseek',
        error: error.message
      });

      // Only log in debug mode to avoid test noise
      if (process.env.DEBUG) {
        console.error('DeepSeek API Error:', error);
      }
      return {
        success: false,
        error: error.message,
        content: null
      };
    }
  }

  /**
     * Estimate token count for messages (rough approximation)
     * @param {Array} messages - Messages array
     * @returns {number} - Estimated token count
     * @private
     */
  _estimateTokens(messages) {
    // Rough estimation: 1 token ≈ 4 characters for English text
    const totalChars = messages.reduce((sum, msg) => sum + msg.content.length, 0);
    return Math.ceil(totalChars / 4);
  }

  /**
     * Call API using OpenAI client
     * @param {Object} client - OpenAI client
     * @param {Array} messages - Messages array
     * @param {Object} options - API options
     * @param {string} uploadId - Upload tracking ID
     * @returns {string} - Response content
     * @private
     */
  async _callWithOpenAIClient(client, messages, options, uploadId = null) {
    const requestBody = {
      model: options.model,
      messages: messages,
      temperature: options.temperature,
      max_tokens: options.max_tokens
    };

    // Add response format if specified
    if (options.responseFormat) {
      requestBody.response_format = options.responseFormat;
    }

    const completion = await client.chat.completions.create(requestBody);
    return completion.choices[0].message.content;
  }

  /**
     * Call API using fetch (fallback)
     * @param {string} apiKey - API key
     * @param {Array} messages - Messages array
     * @param {Object} options - API options
     * @returns {string} - Response content
     * @private
     */
  async _callWithFetch(apiKey, messages, options) {
    const requestBody = {
      model: options.model,
      messages: messages,
      temperature: options.temperature,
      max_tokens: options.max_tokens
    };

    // Add response format if specified
    if (options.responseFormat) {
      requestBody.response_format = options.responseFormat;
    }

    const response = await fetch(`${this.baseURL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`DeepSeek API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  }

  /**
     * Extract JSON from API response content
     * @param {string} content - Response content
     * @returns {Object|null} - Parsed JSON or null if extraction failed
     */
  extractJSON(content) {
    try {
      // Try to parse the entire content as JSON first
      try {
        return JSON.parse(content);
      } catch (e) {
        // If that fails, try to extract JSON from markdown code blocks
        const jsonMatch = content.match(/```json\s*(\{[\s\S]*?\})\s*```/) ||
                    content.match(/```\s*(\{[\s\S]*?\})\s*```/) ||
                    content.match(/\{[\s\S]*?\}/);

        if (jsonMatch) {
          const jsonStr = jsonMatch[1] || jsonMatch[0];
          return JSON.parse(jsonStr);
        }

        throw new Error('No valid JSON found in response');
      }
    } catch (error) {
      // Only log in debug mode to avoid test noise
      if (process.env.DEBUG) {
        console.error('Error extracting JSON from content:', error);
      }
      return null;
    }
  }

  /**
     * Process text with DeepSeek API (OCR refinement, language detection, document type)
     * @param {string} text - OCR extracted text
     * @param {string} preferredLanguage - Preferred language
     * @param {Object} languageMapping - Language mapping instance
     * @param {Object} accountingFields - Accounting fields instance
     * @param {string} apiKey - API key
     * @returns {Object} - Processing result
     */
  async processText(text, preferredLanguage, languageMapping, accountingFields, apiKey) {
    try {
      // Generate prompt for document analysis using AccountingFields
      const { prompt, systemPrompt, options } = accountingFields.generateDocumentAnalysisPrompt(
        text, languageMapping
      );

      // Call API
      const response = await this.callAPI(prompt, apiKey, {
        ...options,
        systemPrompt
      });

      if (!response.success) {
        return { success: false, error: response.error };
      }

      // Parse response
      const parsedContent = this.extractJSON(response.content);
      if (!parsedContent) {
        return { success: false, error: 'Failed to parse API response' };
      }

      return {
        success: true,
        refinedText: parsedContent.refinedText || text,
        language: parsedContent.language || preferredLanguage,
        documentKind: parsedContent.documentKind || 'invoice_other'
      };

    } catch (error) {
      // Only log in debug mode to avoid test noise
      if (process.env.DEBUG) {
        console.error('DeepSeek Text Processing Error:', error);
      }
      return { success: false, error: error.message };
    }
  }

  /**
     * Extract fields from document
     * @param {string} text - Document text
     * @param {string} kind - Document kind
     * @param {Object} languageMapping - Language mapping instance
     * @param {Object} accountingFields - Accounting fields instance
     * @param {string} apiKey - API key
     * @param {string} detectedLang - Detected language (optional)
     * @param {Object} companyInfo - Company information from chrome extension settings (optional)
     * @returns {Object} - Extraction result
     */
  async extractFields(text, kind, languageMapping, accountingFields, apiKey, detectedLang = null, companyInfo = null) {
    try {
      // Generate prompt for metadata extraction using AccountingFields with company info
      const prompt = accountingFields.generateMetadataExtractionPrompt(kind, detectedLang, companyInfo);

      // Call API
      const response = await this.callAPI(`${prompt}\n\nText: "${text}"`, apiKey, {
        temperature: 0.1,
        max_tokens: 8192,
        responseFormat: { type: 'json_object' },
        systemPrompt: `You are an expert in extracting structured data from accounting documents into JSON. CRITICAL: Use exact English field names from the provided structure. Do not translate field names to any other language. Only field values should be in the document language (${detectedLang || 'original language'}).`
      });

      if (!response.success) {
        throw new Error(response.error);
      }

      return response.content;

    } catch (error) {
      // Only log in debug mode to avoid test noise
      if (process.env.DEBUG) {
        console.error('DeepSeek Field Extraction Error:', error);
      }
      throw error;
    }
  }

  /**
     * Extract structured invoice data using enhanced templates
     * @param {string} text - Document text
     * @param {Object} options - Extraction options
     * @returns {Object} - Extraction result
     */
  async extractStructuredInvoiceData(text, options = {}) {
    try {
      const {
        apiKey,
        language = 'pol',
        template = 'standard',
        companyInfo = null
      } = options;

      if (!apiKey) {
        throw new Error('API key is required for structured extraction');
      }

      // Import templates dynamically to avoid circular dependencies
      const { getTemplate } = await import('../templates/invoiceFieldTemplates.js');
      const extractionTemplate = getTemplate(template, language);

      // Generate structured extraction prompt
      const prompt = this.generateStructuredExtractionPrompt(text, extractionTemplate, language, companyInfo);

      // Call API with JSON response format
      const response = await this.callAPI(prompt, apiKey, {
        model: 'deepseek-chat',
        temperature: 0.1,
        max_tokens: 4096,
        response_format: { type: 'json_object' }
      });

      if (!response.success) {
        return { success: false, error: response.error };
      }

      // Parse and return structured data
      const extractedData = this.extractJSON(response.content);

      return {
        success: true,
        data: extractedData,
        template: template,
        language: language,
        model: response.model
      };

    } catch (error) {
      if (process.env.DEBUG) {
        console.error('DeepSeek Structured Extraction Error:', error);
      }
      return { success: false, error: error.message };
    }
  }

  /**
     * Generate structured extraction prompt
     * @param {string} text - Document text
     * @param {Object} template - Extraction template
     * @param {string} language - Language code
     * @param {Object} companyInfo - Company information
     * @returns {string} - Generated prompt
     */
  generateStructuredExtractionPrompt(text, template, language, companyInfo) {
    const languageNames = {
      'pol': 'Polish',
      'eng': 'English',
      'deu': 'German',
      'fra': 'French'
    };

    const languageName = languageNames[language] || 'Polish';
    const companyContext = companyInfo ? `\n\nCompany Context:\n${JSON.stringify(companyInfo, null, 2)}` : '';

    return `You are an expert in extracting structured data from ${languageName} invoices. Extract the following fields from the invoice text and return them as a JSON object.

CRITICAL INSTRUCTIONS:
1. Use EXACT English field names from the template below
2. Field values should be in the original document language (${languageName})
3. Return only valid JSON without any additional text
4. If a field is not found, use null as the value
5. For monetary amounts, extract numbers only (no currency symbols)
6. For dates, use ISO format (YYYY-MM-DD) when possible

REQUIRED FIELDS TEMPLATE:
${JSON.stringify(template.fields, null, 2)}

FIELD DESCRIPTIONS:
${template.descriptions.map(desc => `- ${desc.field}: ${desc.description}`).join('\n')}

VALIDATION RULES:
${template.validationRules.map(rule => `- ${rule.field}: ${rule.rule}`).join('\n')}
${companyContext}

INVOICE TEXT TO ANALYZE:
${text}

Return the extracted data as a valid JSON object:`;
  }

  /**
     * Extract positions from document
     * @param {string} text - Document text
     * @param {string} kind - Document kind
     * @param {Object} accountingFields - Accounting fields instance
     * @param {string} apiKey - API key
     * @param {string} detectedLang - Detected language (optional)
     * @returns {Object} - Extraction result
     */
  async extractPositions(text, kind, accountingFields, apiKey, detectedLang = null) {
    try {
      // Generate prompt for position extraction using AccountingFields
      const prompt = accountingFields.generatePositionExtractionPrompt(kind, detectedLang);

      // Call API
      const response = await this.callAPI(`${prompt}\n\nDocument text:\n${text}`, apiKey, {
        temperature: 0.1,
        max_tokens: 8192,
        responseFormat: { type: 'json_object' },
        systemPrompt: `You are an expert accounting document analyst specializing in extracting line items from invoices and financial documents. CRITICAL: Use exact English field names from the provided structure (name, quantity, tax, price_net, price_gross, total_price_net, total_price_gross, etc.). Do not translate field names to any other language. Field values should be extracted in the original document language (${detectedLang || 'original language'}).`
      });

      if (!response.success) {
        // Only log in debug mode to avoid test noise
        if (process.env.DEBUG) {
          console.error('Error extracting positions:', response.error);
        }
        return { positions: [] };
      }

      // Parse response
      const parsedContent = this.extractJSON(response.content);
      return parsedContent || { positions: [] };

    } catch (error) {
      // Only log in debug mode to avoid test noise
      if (process.env.DEBUG) {
        console.error('DeepSeek Position Extraction Error:', error);
      }
      return { positions: [] };
    }
  }

  /**
     * Extract table data from document
     * @param {string} text - Document text
     * @param {string} apiKey - API key
     * @returns {Object} - Table extraction result
     */
  async extractTableData(text, apiKey) {
    try {
      // Use DocumentFields for table extraction
      const documentFields = new window.DocumentFields();
      const { prompt, systemPrompt, options } = documentFields.generateTableExtractionPrompt(text);

      // Call API
      const response = await this.callAPI(prompt, apiKey, {
        ...options,
        systemPrompt
      });

      if (!response.success) {
        return { tables: [] };
      }

      // Parse response
      const parsedContent = this.extractJSON(response.content);
      return parsedContent || { tables: [] };

    } catch (error) {
      // Only log in debug mode to avoid test noise
      if (process.env.DEBUG) {
        console.error('DeepSeek Table Extraction Error:', error);
      }
      return { tables: [] };
    }
  }

  /**
     * Analyze document relationships
     * @param {Object} documentMetadata - Main document metadata
     * @param {Array} relatedDocsMetadata - Related documents metadata
     * @param {string} apiKey - API key
     * @returns {Object} - Relationship analysis result
     */
  async analyzeRelationships(documentMetadata, relatedDocsMetadata, apiKey) {
    try {
      // Import PromptGenerator dynamically
      const { PromptGenerator } = await import('../components/shared/utilities/PromptGenerator.js');
      const promptGenerator = new PromptGenerator();

      // Generate prompt for relationship analysis
      const { prompt, systemPrompt, options } = promptGenerator.generateRelationshipAnalysisPrompt(
        documentMetadata, relatedDocsMetadata
      );

      // Call API
      const response = await this.callAPI(prompt, apiKey, {
        ...options,
        systemPrompt
      });

      if (!response.success) {
        return relatedDocsMetadata; // Return original if analysis fails
      }

      // Parse response
      const enhancedRelationships = this.extractJSON(response.content);

      if (enhancedRelationships && enhancedRelationships.relationships) {
        return enhancedRelationships.relationships;
      }
      return relatedDocsMetadata;


    } catch (error) {
      // Only log in debug mode to avoid test noise
      if (process.env.DEBUG) {
        console.error('DeepSeek Relationship Analysis Error:', error);
      }
      return relatedDocsMetadata;
    }
  }

  /**
     * Perform domain-specific document analysis
     * @param {string} documentType - Document type
     * @param {string} text - Document text
     * @param {string} apiKey - API key
     * @param {boolean} addSummaryJson - Whether to add summary JSON
     * @returns {Object} - Analysis result
     */
  async analyzeDomainSpecific(documentType, text, apiKey, addSummaryJson = false) {
    try {
      // Import PromptGenerator dynamically
      const { PromptGenerator } = await import('../components/shared/utilities/PromptGenerator.js');
      const promptGenerator = new PromptGenerator();

      // Generate domain-specific prompt
      const { prompt, systemPrompt, options } = promptGenerator.generateDomainSpecificPrompt(
        documentType, text, addSummaryJson
      );

      // Call API
      const response = await this.callAPI(prompt, apiKey, {
        ...options,
        systemPrompt
      });

      if (!response.success) {
        return { success: false, error: response.error };
      }

      // Parse response
      const parsedContent = this.extractJSON(response.content);

      return {
        success: true,
        data: parsedContent
      };

    } catch (error) {
      // Only log in debug mode to avoid test noise
      if (process.env.DEBUG) {
        console.error('DeepSeek Domain-Specific Analysis Error:', error);
      }
      return { success: false, error: error.message };
    }
  }

  /**
     * Unified document analysis (legacy support)
     * @param {string} documentText - Document text
     * @param {string} fileName - File name
     * @param {string} apiKey - API key
     * @param {string} ragContext - RAG context (optional)
     * @param {string} detectedLang - Detected language (optional)
     * @returns {Object} - Analysis result
     */
  async analyzeDocument(documentText, fileName, apiKey, ragContext = '', detectedLang = null) {
    try {
      // Import PromptGenerator dynamically
      const { PromptGenerator } = await import('../components/shared/utilities/PromptGenerator.js');
      const promptGenerator = new PromptGenerator();

      // Generate unified analysis prompt
      const { prompt, systemPrompt, options } = promptGenerator.generateUnifiedAnalysisPrompt(
        documentText, fileName, ragContext, detectedLang
      );

      // Call API
      const response = await this.callAPI(prompt, apiKey, {
        ...options,
        systemPrompt
      });

      if (!response.success) {
        return { success: false, error: response.error };
      }

      // Parse response
      const parsedContent = this.extractJSON(response.content);

      if (!parsedContent) {
        return { success: false, error: 'Failed to parse API response' };
      }

      return {
        success: true,
        data: parsedContent
      };

    } catch (error) {
      // Only log in debug mode to avoid test noise
      if (process.env.DEBUG) {
        console.error('DeepSeek Unified Analysis Error:', error);
      }
      return { success: false, error: error.message };
    }
  }

  /**
     * Get default API options
     * @returns {Object} - Default options
     */
  getDefaultOptions() {
    return { ...this.defaultOptions };
  }

  /**
     * Set default model
     * @param {string} model - Model name
     */
  setDefaultModel(model) {
    this.defaultModel = model;
    this.defaultOptions.model = model;
  }

  /**
     * Set base URL
     * @param {string} url - Base URL
     */
  setBaseURL(url) {
    this.baseURL = url;
  }
}

// Export to window object for non-module scripts
if (typeof window !== 'undefined') {
  window.DeepSeekAPI = DeepSeekAPI;
}

// ===== LOCAL TESTING (Node.js equivalent of Python's if __name__ == '__main__') =====
if (typeof process !== 'undefined' && process.argv && process.argv[1] && process.argv[1].includes('DeepSeekAPI.js')) {
  (async function runTests() {
    console.log('🧪 Running local tests for DeepSeekAPI...');

    // Create test instance
    const api = new DeepSeekAPI();

    // Test data
    const samplePrompt = 'Analyze this Polish invoice text and extract key information in JSON format.';
    const sampleDocumentText = `
      FAKTURA VAT
      Numer: FV/2025/001
      Data wystawienia: 2025-01-27
      Sprzedawca: ABC Sp. z o.o.
      NIP: 1234567890
      Nabywca: XYZ Sp. z o.o.
      NIP: 0987654321
      Wartość netto: 1000.00 PLN
      VAT 23%: 230.00 PLN
      Wartość brutto: 1230.00 PLN
      `;

    console.log('✅ Test 1: API configuration');
    console.log('📝 Default options:', JSON.stringify(api.getDefaultOptions(), null, 2));
    console.log('📝 Base URL:', api.baseURL);
    console.log('📝 Default model:', api.defaultModel);

    console.log('\n✅ Test 2: JSON extraction from various formats');

    // Test JSON extraction with clean JSON
    const cleanJson = '{"documentType": "vat", "amount": 1230.00}';
    console.log('📝 Clean JSON extraction:', api.extractJSON(cleanJson));

    // Test JSON extraction from markdown
    const markdownJson = '```json\n{"documentType": "vat", "amount": 1230.00}\n```';
    console.log('📝 Markdown JSON extraction:', api.extractJSON(markdownJson));

    // Test JSON extraction from mixed content
    const mixedContent = 'Here is the analysis:\n{"documentType": "vat", "amount": 1230.00}\nEnd of analysis.';
    console.log('📝 Mixed content JSON extraction:', api.extractJSON(mixedContent));

    // Test invalid JSON
    const invalidJson = 'This is not JSON at all';
    console.log('📝 Invalid JSON extraction (should be null):', api.extractJSON(invalidJson));

    console.log('\n✅ Test 3: API call simulation (without actual API key)');
    try {
      // This will fail without API key, but tests the structure
      const mockResponse = await api.callAPI(samplePrompt, 'fake-api-key', {
        temperature: 0.1,
        max_tokens: 1000
      });
      console.log('📝 Mock API call result:', mockResponse.success ? 'Success' : 'Expected failure (no API key)');
    } catch (error) {
      console.log('📝 Expected failure without valid API key:', error.message);
    }

    console.log('\n✅ Test 4: Configuration methods');

    // Test model setting
    api.setDefaultModel('deepseek-coder');
    console.log('📝 Model changed to:', api.defaultModel);

    // Test URL setting
    api.setBaseURL('https://api.example.com/v1');
    console.log('📝 Base URL changed to:', api.baseURL);

    // Reset to defaults
    api.setDefaultModel('deepseek-chat');
    api.setBaseURL('https://api.deepseek.com/v1');

    console.log('\n✅ Test 5: Edge cases');

    // Test empty prompt
    try {
      const emptyResult = await api.callAPI('', 'fake-key');
      console.log('📝 Empty prompt handled:', emptyResult.success ? 'Success' : 'Expected failure (empty prompt)');
    } catch (error) {
      console.log('📝 Expected failure with empty prompt:', error.message);
    }

    // Test with environment variable API key (if available)
    if (process.env.DEEPSEEK_API_KEY) {
      console.log('\n🤖 Test 6: Real API call (API key found in environment)');
      try {
        const realResponse = await api.callAPI(
          'Extract key information from this text: "FAKTURA VAT Nr: 123/2025"',
          process.env.DEEPSEEK_API_KEY,
          {
            temperature: 0.1,
            max_tokens: 500,
            responseFormat: { type: 'json_object' }
          }
        );
        console.log('📝 Real API call success:', realResponse.success);
        if (realResponse.success) {
          console.log('📝 Response content (first 200 chars):',
            realResponse.content.substring(0, 200) + '...');
        }
      } catch (error) {
        console.log('❌ Real API call error:', error.message);
      }
    } else {
      console.log('\n⚠️  Test 6: Skipped real API call (no DEEPSEEK_API_KEY environment variable)');
      console.log('   To test real API calls, set: export DEEPSEEK_API_KEY=your_api_key');
    }

    console.log('\n🎉 All tests completed for DeepSeekAPI');
  })();
}

// ES Module exports
export { DeepSeekAPI };
export { callDeepSeekAPI, processTextWithDeepSeek, extractFieldsWithDeepSeek };

// Default export
export default DeepSeekAPI;

