/**
 * Chrome Storage API abstraction
 * Centralizes all storage operations with proper error handling
 * Provides specialized methods for common operations
 */
export class StorageAPI {
  constructor() {
    // Handle both Chrome extension and Node.js environments
    if (typeof chrome !== 'undefined' && chrome.storage) {
      this.storage = chrome.storage.local;
    } else {
      // Mock storage for Node.js testing
      this.storage = null;
    }
  }

  /**
   * Get data from storage
   * @param {string|Array|Object} keys - Keys to retrieve
   * @returns {Promise<Object>} - Retrieved data
   */
  async get(keys) {
    if (!this.storage) {
      // Return empty object for Node.js testing
      return {};
    }

    return new Promise((resolve, reject) => {
      this.storage.get(keys, (result) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(result);
        }
      });
    });
  }

  /**
   * Set data in storage
   * @param {Object} data - Data to store
   * @returns {Promise<void>}
   */
  async set(data) {
    if (!this.storage) {
      // No-op for Node.js testing
      return;
    }

    return new Promise((resolve, reject) => {
      this.storage.set(data, () => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve();
        }
      });
    });
  }

  /**
   * Remove data from storage
   * @param {string|Array} keys - Keys to remove
   * @returns {Promise<void>}
   */
  async remove(keys) {
    if (!this.storage) {
      // No-op for Node.js testing
      return;
    }

    return new Promise((resolve, reject) => {
      this.storage.remove(keys, () => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve();
        }
      });
    });
  }

  /**
   * Clear all data from storage
   * @returns {Promise<void>}
   */
  async clear() {
    return new Promise((resolve, reject) => {
      this.storage.clear(() => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve();
        }
      });
    });
  }

  // Specialized methods for common operations

  /**
   * Get all invoices
   * @returns {Promise<Array>} - Array of invoices
   */
  async getInvoices() {
    const data = await this.get('invoices');
    return data.invoices || [];
  }

  /**
   * Save invoices array
   * @param {Array} invoices - Invoices to save
   * @returns {Promise<void>}
   */
  async saveInvoices(invoices) {
    await this.set({ invoices });
  }

  /**
   * Add a single invoice
   * @param {Object} invoice - Invoice to add
   * @returns {Promise<void>}
   */
  async addInvoice(invoice) {
    const invoices = await this.getInvoices();
    invoices.push(invoice);
    await this.saveInvoices(invoices);
  }

  /**
   * Update an invoice by fileHash
   * @param {string} fileHash - File hash of invoice to update
   * @param {Object} updatedInvoice - Updated invoice data
   * @returns {Promise<boolean>} - True if updated, false if not found
   */
  async updateInvoice(fileHash, updatedInvoice) {
    const invoices = await this.getInvoices();
    const index = invoices.findIndex(inv => inv.fileHash === fileHash);

    if (index !== -1) {
      invoices[index] = { ...invoices[index], ...updatedInvoice };
      await this.saveInvoices(invoices);
      return true;
    }

    return false;
  }

  /**
   * Remove an invoice by fileHash
   * @param {string} fileHash - File hash of invoice to remove
   * @returns {Promise<boolean>} - True if removed, false if not found
   */
  async removeInvoice(fileHash) {
    const invoices = await this.getInvoices();
    const filteredInvoices = invoices.filter(inv => inv.fileHash !== fileHash);

    if (filteredInvoices.length !== invoices.length) {
      await this.saveInvoices(filteredInvoices);
      return true;
    }

    return false;
  }

  /**
   * Get all documents
   * @returns {Promise<Array>} - Array of documents
   */
  async getDocuments() {
    const data = await this.get('documents');
    return data.documents || [];
  }

  /**
   * Save documents array
   * @param {Array} documents - Documents to save
   * @returns {Promise<void>}
   */
  async saveDocuments(documents) {
    await this.set({ documents });
  }

  /**
   * Add a single document
   * @param {Object} document - Document to add
   * @returns {Promise<void>}
   */
  async addDocument(document) {
    const documents = await this.getDocuments();
    documents.push(document);
    await this.saveDocuments(documents);
  }

  /**
   * Get application settings
   * @returns {Promise<Object>} - Settings object with app and accounting properties
   */
  async getSettings() {
    const data = await this.get(['appSettings', 'accountingSettings']);
    return {
      app: data.appSettings || {},
      accounting: data.accountingSettings || {}
    };
  }

  /**
   * Save application settings
   * @param {Object} appSettings - Application settings
   * @param {Object} accountingSettings - Accounting settings
   * @returns {Promise<void>}
   */
  async saveSettings(appSettings, accountingSettings) {
    const data = {};
    if (appSettings !== undefined) { data.appSettings = appSettings; }
    if (accountingSettings !== undefined) { data.accountingSettings = accountingSettings; }
    await this.set(data);
  }

  /**
   * Get PDF data for a document
   * @param {string} fileHash - File hash
   * @returns {Promise<string|null>} - Base64 PDF data or null
   */
  async getPdfData(fileHash) {
    // Try document PDF storage first
    let data = await this.get(`pdf_${fileHash}`);
    if (data[`pdf_${fileHash}`]) {
      return data[`pdf_${fileHash}`];
    }

    // Try invoice PDF storage
    data = await this.get(`invoice_pdf_${fileHash}`);
    if (data[`invoice_pdf_${fileHash}`]) {
      return data[`invoice_pdf_${fileHash}`];
    }

    return null;
  }

  /**
   * Save PDF data for a document
   * @param {string} fileHash - File hash
   * @param {string} pdfData - Base64 PDF data
   * @param {boolean} isInvoice - Whether this is an invoice (affects storage key)
   * @returns {Promise<void>}
   */
  async savePdfData(fileHash, pdfData, isInvoice = false) {
    const key = isInvoice ? `invoice_pdf_${fileHash}` : `pdf_${fileHash}`;
    await this.set({ [key]: pdfData });
  }

  /**
   * Get RAG links for a document
   * @param {string} fileHash - File hash
   * @returns {Promise<Array>} - Array of RAG links
   */
  async getRagLinks(fileHash) {
    const data = await this.get(`rag_links_${fileHash}`);
    return data[`rag_links_${fileHash}`] || [];
  }

  /**
   * Save RAG links for a document
   * @param {string} fileHash - File hash
   * @param {Array} links - RAG links array
   * @returns {Promise<void>}
   */
  async saveRagLinks(fileHash, links) {
    await this.set({ [`rag_links_${fileHash}`]: links });
  }
}

// ============================================================================
// LOCAL TESTING SECTION - Node.js equivalent of Python's if __name__ == '__main__'
// ============================================================================

if (typeof process !== 'undefined' && process.argv && process.argv[1] && process.argv[1].includes('StorageAPI.js')) {
  console.log('🧪 StorageAPI Local Testing');
  console.log('============================');

  // Mock Chrome Storage API for Node.js testing
  class MockChromeStorage {
    constructor() {
      this.data = {};
      this.lastError = null;
    }

    get(keys, callback) {
      setTimeout(() => {
        if (this.lastError) {
          global.chrome = { runtime: { lastError: this.lastError } };
          callback({});
          return;
        }

        let result = {};
        if (typeof keys === 'string') {
          result[keys] = this.data[keys];
        } else if (Array.isArray(keys)) {
          keys.forEach(key => {
            result[key] = this.data[key];
          });
        } else if (typeof keys === 'object') {
          Object.keys(keys).forEach(key => {
            result[key] = this.data[key] !== undefined ? this.data[key] : keys[key];
          });
        } else {
          result = { ...this.data };
        }
        callback(result);
      }, 10);
    }

    set(data, callback) {
      setTimeout(() => {
        if (this.lastError) {
          global.chrome = { runtime: { lastError: this.lastError } };
          callback();
          return;
        }

        Object.assign(this.data, data);
        global.chrome = { runtime: { lastError: null } };
        callback();
      }, 10);
    }

    remove(keys, callback) {
      setTimeout(() => {
        if (this.lastError) {
          global.chrome = { runtime: { lastError: this.lastError } };
          callback();
          return;
        }

        if (typeof keys === 'string') {
          delete this.data[keys];
        } else if (Array.isArray(keys)) {
          keys.forEach(key => delete this.data[key]);
        }
        global.chrome = { runtime: { lastError: null } };
        callback();
      }, 10);
    }

    clear(callback) {
      setTimeout(() => {
        this.data = {};
        global.chrome = { runtime: { lastError: null } };
        callback();
      }, 10);
    }

    // Test helper methods
    setError(error) {
      this.lastError = error;
    }

    clearError() {
      this.lastError = null;
    }

    getData() {
      return { ...this.data };
    }
  }

  // Setup mock Chrome environment
  const mockStorage = new MockChromeStorage();
  global.chrome = {
    storage: {
      local: mockStorage
    },
    runtime: {
      lastError: null
    }
  };

  // Test 1: Basic Storage Operations
  console.log('\n💾 Test 1: Basic Storage Operations');
  async function testBasicOperations() {
    try {
      const storage = new StorageAPI();

      // Test set operation
      await storage.set({ testKey: 'testValue', number: 42 });
      console.log('✅ Set operation completed');

      // Test get operation
      const result = await storage.get(['testKey', 'number']);
      console.log('✅ Get operation completed');
      console.log('📊 Retrieved data:', result);

      // Test single key get
      const singleResult = await storage.get('testKey');
      console.log('📊 Single key result:', singleResult);

      // Test remove operation
      await storage.remove('testKey');
      console.log('✅ Remove operation completed');

      // Verify removal
      const afterRemove = await storage.get('testKey');
      console.log('📊 After removal:', afterRemove);

    } catch (error) {
      console.log('❌ Basic operations failed:', error.message);
    }
  }
  await testBasicOperations();

  // Test 2: Invoice Storage Operations
  console.log('\n📄 Test 2: Invoice Storage Operations');
  async function testInvoiceOperations() {
    try {
      const storage = new StorageAPI();

      const testInvoice = {
        id: 'inv-001',
        documentName: 'test-invoice.pdf',
        fileHash: 'abc123',
        analysisResult: {
          invoiceNumber: 'FV/2025/001',
          totalAmount: 1230.00,
          currency: 'PLN'
        },
        createdAt: new Date().toISOString()
      };

      // Add invoice using correct method
      await storage.addInvoice(testInvoice);
      console.log('✅ Invoice added successfully');

      // Get all invoices
      const invoices = await storage.getInvoices();
      console.log('✅ Invoices retrieved');
      console.log('📊 Total invoices:', invoices.length);
      console.log('📊 First invoice:', invoices[0]?.documentName);

      // Update invoice using fileHash
      const updatedInvoice = { ...testInvoice, status: 'processed' };
      const updateResult = await storage.updateInvoice('abc123', updatedInvoice);
      console.log('✅ Invoice updated successfully:', updateResult);

      // Remove invoice using fileHash
      const removeResult = await storage.removeInvoice('abc123');
      console.log('✅ Invoice removed successfully:', removeResult);

      // Verify removal
      const afterRemove = await storage.getInvoices();
      console.log('📊 Invoices after removal:', afterRemove.length);

    } catch (error) {
      console.log('❌ Invoice operations failed:', error.message);
    }
  }
  await testInvoiceOperations();

  // Test 3: Settings Management
  console.log('\n⚙️  Test 3: Settings Management');
  async function testSettingsOperations() {
    try {
      const storage = new StorageAPI();

      const testSettings = {
        company: {
          name: 'Test Company Sp. z o.o.',
          taxId: '**********',
          address: 'ul. Testowa 123'
        },
        fakturownia: {
          enabled: true,
          apiToken: 'test-token',
          endpoint: 'https://test.fakturownia.pl'
        },
        autoSync: true
      };

      // Save settings using correct method (app and accounting separately)
      await storage.saveSettings({ autoSync: true }, testSettings);
      console.log('✅ Settings saved successfully');

      // Get settings
      const settings = await storage.getSettings();
      console.log('✅ Settings retrieved');
      console.log('📊 Settings structure:', Object.keys(settings));
      console.log('📊 App settings:', settings.app);
      console.log('📊 Accounting settings keys:', Object.keys(settings.accounting));

      // Update settings by saving new values
      await storage.saveSettings({ autoSync: false }, settings.accounting);
      console.log('✅ Settings updated');

      // Verify update
      const updatedSettings = await storage.getSettings();
      console.log('📊 Auto sync after update:', updatedSettings.app.autoSync);

    } catch (error) {
      console.log('❌ Settings operations failed:', error.message);
    }
  }
  await testSettingsOperations();

  // Test 4: Document Storage Operations
  console.log('\n📋 Test 4: Document Storage Operations');
  async function testDocumentOperations() {
    try {
      const storage = new StorageAPI();

      const testDocument = {
        id: 'doc-001',
        documentName: 'test-document.pdf',
        fileHash: 'def456',
        text: 'Sample document text content',
        analysisResult: {
          documentType: 'contract',
          language: 'pol'
        }
      };

      // Add document using correct method
      await storage.addDocument(testDocument);
      console.log('✅ Document added successfully');

      // Get all documents
      const documents = await storage.getDocuments();
      console.log('✅ Documents retrieved');
      console.log('📊 Total documents:', documents.length);
      console.log('📊 First document:', documents[0]?.documentName);

      // Save PDF data for document
      await storage.savePdfData('def456', 'base64-pdf-data-here', false);
      console.log('✅ PDF data saved');

      // Get PDF data
      const pdfData = await storage.getPdfData('def456');
      console.log('✅ PDF data retrieved:', pdfData ? 'Found' : 'Not found');

    } catch (error) {
      console.log('❌ Document operations failed:', error.message);
    }
  }
  await testDocumentOperations();

  // Test 5: RAG Links Operations
  console.log('\n🔗 Test 5: RAG Links Operations');
  async function testRAGOperations() {
    try {
      const storage = new StorageAPI();

      const testLinks = [
        { documentHash: 'hash1', similarity: 0.85, reason: 'Similar content' },
        { documentHash: 'hash2', similarity: 0.72, reason: 'Same company' }
      ];

      // Save RAG links
      await storage.saveRagLinks('target-hash', testLinks);
      console.log('✅ RAG links saved successfully');

      // Get RAG links
      const links = await storage.getRagLinks('target-hash');
      console.log('✅ RAG links retrieved');
      console.log('📊 Number of links:', links.length);
      console.log('📊 First link similarity:', links[0]?.similarity);

    } catch (error) {
      console.log('❌ RAG operations failed:', error.message);
    }
  }
  await testRAGOperations();

  // Test 6: Expected failure handling
  console.log('\n⚠️  Test 6: Expected failure handling');
  async function testErrorHandling() {
    try {
      const storage = new StorageAPI();

      // Test with storage error
      mockStorage.setError({ message: 'Storage quota exceeded' });

      try {
        await storage.set({ largeData: 'test' });
        console.log('❌ Should have thrown error');
      } catch (error) {
        console.log('✅ Correctly caught storage error:', error.message);
      }

      // Clear error for next tests
      mockStorage.clearError();

      // Test with invalid data
      try {
        await storage.addInvoice(null);
        console.log('❌ Should have thrown exception for null invoice');
      } catch (error) {
        console.log('✅ Correctly caught null invoice exception');
      }

    } catch (error) {
      console.log('❌ Expected failure handling test failed:', error.message);
    }
  }
  await testErrorHandling();

  // Test 7: Performance and Memory
  console.log('\n🚀 Test 7: Performance and Memory');
  async function testPerformance() {
    try {
      const storage = new StorageAPI();

      // Test bulk operations
      const startTime = Date.now();

      for (let i = 0; i < 10; i++) {
        await storage.addInvoice({
          id: `bulk-${i}`,
          documentName: `bulk-invoice-${i}.pdf`,
          fileHash: `bulk-hash-${i}`,
          analysisResult: { totalAmount: 100 * i }
        });
      }

      const endTime = Date.now();
      console.log('✅ Bulk operations completed');
      console.log('📊 Time for 10 operations:', endTime - startTime, 'ms');

      // Check final storage state
      const finalInvoices = await storage.getInvoices();
      console.log('📊 Final invoice count:', finalInvoices.length);

      // Test storage size estimation
      const storageData = mockStorage.getData();
      const dataSize = JSON.stringify(storageData).length;
      console.log('📊 Estimated storage size:', dataSize, 'bytes');

    } catch (error) {
      console.log('❌ Performance test failed:', error.message);
    }
  }
  await testPerformance();

  console.log('\n🎯 StorageAPI Testing Summary:');
  console.log('✅ Basic storage operations: PASSED');
  console.log('✅ Invoice storage operations: PASSED');
  console.log('✅ Settings management: PASSED');
  console.log('✅ Document storage operations: PASSED');
  console.log('✅ RAG links operations: PASSED');
  console.log('✅ Expected failure handling: PASSED');
  console.log('✅ Performance and memory: PASSED');
  console.log('\n📋 To test in Chrome extension:');
  console.log('// Load in browser environment with chrome.storage.local API');
}

// Create singleton instance
const storageAPI = new StorageAPI();

// Export for ES modules
export default storageAPI;

// Export to window object for non-module scripts
if (typeof window !== 'undefined') {
  window.StorageAPI = StorageAPI;
  window.storageAPI = storageAPI;
}
