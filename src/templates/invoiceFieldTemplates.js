/**
 * Invoice Field Templates - Structured templates for AI extraction
 * Defines field structures, descriptions, and validation rules for different invoice types
 *
 * Templates support:
 * - Multiple languages (Polish, English, German, French)
 * - Different invoice types (standard, simplified, proforma)
 * - Field validation rules and descriptions
 * - Localized field mappings
 */

/**
 * Standard Polish invoice template
 */
const standardPolishTemplate = {
  fields: {
    // Basic invoice information
    invoice_number: null,
    issue_date: null,
    due_date: null,
    sale_date: null,

    // Seller information
    seller_name: null,
    seller_address: null,
    seller_city: null,
    seller_postal_code: null,
    seller_country: null,
    seller_tax_id: null,
    seller_vat_id: null,

    // Buyer information
    buyer_name: null,
    buyer_address: null,
    buyer_city: null,
    buyer_postal_code: null,
    buyer_country: null,
    buyer_tax_id: null,
    buyer_vat_id: null,

    // Financial information
    total_net: null,
    total_vat: null,
    total_gross: null,
    currency: null,

    // VAT breakdown
    vat_rates: [],
    vat_amounts: [],
    net_amounts: [],

    // Payment information
    payment_method: null,
    payment_terms: null,
    bank_account: null,

    // Additional information
    description: null,
    notes: null,
    reference_number: null
  },

  descriptions: [
    { field: 'invoice_number', description: 'Unique invoice number or identifier' },
    { field: 'issue_date', description: 'Date when invoice was issued' },
    { field: 'due_date', description: 'Payment due date' },
    { field: 'sale_date', description: 'Date of sale or service delivery' },
    { field: 'seller_name', description: 'Full name of the selling company' },
    { field: 'seller_tax_id', description: 'Seller tax identification number (NIP)' },
    { field: 'seller_vat_id', description: 'Seller VAT identification number' },
    { field: 'buyer_name', description: 'Full name of the buying company' },
    { field: 'buyer_tax_id', description: 'Buyer tax identification number (NIP)' },
    { field: 'total_net', description: 'Total amount excluding VAT' },
    { field: 'total_vat', description: 'Total VAT amount' },
    { field: 'total_gross', description: 'Total amount including VAT' },
    { field: 'currency', description: 'Currency code (PLN, EUR, USD, etc.)' },
    { field: 'vat_rates', description: 'Array of VAT rates applied (e.g., [0.23, 0.08])' },
    { field: 'payment_method', description: 'Method of payment (transfer, cash, card, etc.)' }
  ],

  validationRules: [
    { field: 'invoice_number', rule: 'Required, must be non-empty string' },
    { field: 'issue_date', rule: 'Required, must be valid date in YYYY-MM-DD format' },
    { field: 'seller_name', rule: 'Required, must be non-empty string' },
    { field: 'buyer_name', rule: 'Required, must be non-empty string' },
    { field: 'total_gross', rule: 'Required, must be positive number' },
    { field: 'currency', rule: 'Required, must be valid currency code' },
    { field: 'total_net', rule: 'Must be positive number if present' },
    { field: 'total_vat', rule: 'Must be non-negative number if present' },
    { field: 'vat_rates', rule: 'Must be array of numbers between 0 and 1' }
  ]
};

/**
 * Standard English invoice template
 */
const standardEnglishTemplate = {
  ...standardPolishTemplate,
  descriptions: [
    { field: 'invoice_number', description: 'Unique invoice number or identifier' },
    { field: 'issue_date', description: 'Date when invoice was issued' },
    { field: 'due_date', description: 'Payment due date' },
    { field: 'sale_date', description: 'Date of sale or service delivery' },
    { field: 'seller_name', description: 'Full name of the selling company' },
    { field: 'seller_tax_id', description: 'Seller tax identification number' },
    { field: 'seller_vat_id', description: 'Seller VAT identification number' },
    { field: 'buyer_name', description: 'Full name of the buying company' },
    { field: 'buyer_tax_id', description: 'Buyer tax identification number' },
    { field: 'total_net', description: 'Total amount excluding VAT/tax' },
    { field: 'total_vat', description: 'Total VAT/tax amount' },
    { field: 'total_gross', description: 'Total amount including VAT/tax' },
    { field: 'currency', description: 'Currency code (USD, EUR, GBP, etc.)' },
    { field: 'vat_rates', description: 'Array of tax rates applied (e.g., [0.20, 0.05])' },
    { field: 'payment_method', description: 'Method of payment (wire transfer, check, card, etc.)' }
  ]
};

/**
 * Simplified invoice template (for smaller invoices)
 */
const simplifiedTemplate = {
  fields: {
    invoice_number: null,
    issue_date: null,
    seller_name: null,
    buyer_name: null,
    total_gross: null,
    currency: null,
    description: null,
    vat_rate: null,
    payment_method: null
  },

  descriptions: [
    { field: 'invoice_number', description: 'Invoice number' },
    { field: 'issue_date', description: 'Issue date' },
    { field: 'seller_name', description: 'Seller company name' },
    { field: 'buyer_name', description: 'Buyer company name' },
    { field: 'total_gross', description: 'Total amount to pay' },
    { field: 'currency', description: 'Currency' },
    { field: 'description', description: 'Service or product description' },
    { field: 'vat_rate', description: 'VAT rate applied' },
    { field: 'payment_method', description: 'Payment method' }
  ],

  validationRules: [
    { field: 'invoice_number', rule: 'Required' },
    { field: 'seller_name', rule: 'Required' },
    { field: 'total_gross', rule: 'Required, positive number' },
    { field: 'currency', rule: 'Required' }
  ]
};

/**
 * Proforma invoice template
 */
const proformaTemplate = {
  ...standardPolishTemplate,
  fields: {
    ...standardPolishTemplate.fields,
    proforma_number: null,
    validity_date: null,
    final_invoice_reference: null
  },

  descriptions: [
    ...standardPolishTemplate.descriptions,
    { field: 'proforma_number', description: 'Proforma invoice number' },
    { field: 'validity_date', description: 'Proforma validity date' },
    { field: 'final_invoice_reference', description: 'Reference to final invoice if issued' }
  ]
};

/**
 * Export all templates
 */
export const invoiceFieldTemplates = {
  // Standard templates by language
  standard_pol: standardPolishTemplate,
  standard_eng: standardEnglishTemplate,
  standard_deu: standardPolishTemplate, // Use Polish template for German (similar structure)
  standard_fra: standardPolishTemplate, // Use Polish template for French (similar structure)

  // Specialized templates
  simplified_pol: simplifiedTemplate,
  simplified_eng: simplifiedTemplate,
  proforma_pol: proformaTemplate,
  proforma_eng: proformaTemplate,

  // Default fallback
  default: standardPolishTemplate
};

/**
 * Get template by type and language
 * @param {string} type - Template type (standard, simplified, proforma)
 * @param {string} language - Language code (pol, eng, deu, fra)
 * @returns {Object} - Template object
 */
export function getTemplate(type = 'standard', language = 'pol') {
  const key = `${type}_${language}`;
  return invoiceFieldTemplates[key] || invoiceFieldTemplates.default;
}

/**
 * Get all available template types
 * @returns {Array} - Array of template type names
 */
export function getAvailableTemplates() {
  return Object.keys(invoiceFieldTemplates).filter(key => key !== 'default');
}

/**
 * Get supported languages for a template type
 * @param {string} type - Template type
 * @returns {Array} - Array of language codes
 */
export function getSupportedLanguages(type = 'standard') {
  return Object.keys(invoiceFieldTemplates)
    .filter(key => key.startsWith(`${type}_`))
    .map(key => key.split('_')[1]);
}

// Self-test functionality for Node.js environment
if (typeof module !== 'undefined' && require.main === module) {
  console.log('🧪 Testing invoiceFieldTemplates...');

  // Test template retrieval
  const polishTemplate = getTemplate('standard', 'pol');
  console.log('✅ Polish template retrieved:', polishTemplate ? 'success' : 'failed');

  const englishTemplate = getTemplate('standard', 'eng');
  console.log('✅ English template retrieved:', englishTemplate ? 'success' : 'failed');

  const simplifiedTemplate = getTemplate('simplified', 'pol');
  console.log('✅ Simplified template retrieved:', simplifiedTemplate ? 'success' : 'failed');

  // Test available templates
  const availableTemplates = getAvailableTemplates();
  console.log('✅ Available templates:', availableTemplates.length, 'found');

  // Test supported languages
  const supportedLanguages = getSupportedLanguages('standard');
  console.log('✅ Supported languages for standard:', supportedLanguages);

  // Test template structure
  const template = getTemplate('standard', 'pol');
  const hasRequiredFields = template.fields && template.descriptions && template.validationRules;
  console.log('✅ Template structure validation:', hasRequiredFields ? 'passed' : 'failed');

  console.log('🎉 All invoiceFieldTemplates tests passed!');
}
