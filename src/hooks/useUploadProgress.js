import { useState, useCallback, useRef, useEffect } from 'react';

/**
 * Custom Hook for Upload Progress Management
 * Manages state and logic for file upload progress tracking
 *
 * Features:
 * - Multiple file progress tracking
 * - Real-time progress updates
 * - Cancel/retry functionality
 * - Performance monitoring
 * - Memory efficient state management
 * - Error handling and recovery
 */
export function useUploadProgress(options = {}) {
  const {
    maxFiles = 10,
    onProgress = null,
    onComplete = null,
    onError = null,
    onCancel = null,
    autoRetry = false,
    retryAttempts = 3,
    retryDelay = 1000
  } = options;

  // State management
  const [state, setState] = useState({
    files: [],
    overallProgress: 0,
    isActive: false,
    totalFiles: 0,
    completedFiles: 0,
    failedFiles: 0,
    cancelledFiles: 0
  });

  // Refs for cleanup and cancellation
  const abortControllersRef = useRef(new Map());
  const timeoutsRef = useRef(new Map());
  const retryCountsRef = useRef(new Map());

  // Generate unique file ID
  const generateFileId = useCallback(() => {
    return `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  // Initialize files for tracking
  const initializeFiles = useCallback((fileList) => {
    const files = Array.from(fileList).slice(0, maxFiles).map(file => ({
      id: generateFileId(),
      name: file.name,
      size: file.size,
      type: file.type,
      lastModified: file.lastModified,
      file: file,
      progress: 0,
      status: 'pending',
      stage: 'uploading',
      error: null,
      startTime: null,
      endTime: null,
      estimatedTime: null,
      validationResult: null,
      securityResult: null
    }));

    setState(prev => ({
      ...prev,
      files,
      totalFiles: files.length,
      completedFiles: 0,
      failedFiles: 0,
      cancelledFiles: 0,
      overallProgress: 0,
      isActive: false
    }));

    // Clear previous abort controllers and timeouts
    abortControllersRef.current.clear();
    timeoutsRef.current.clear();
    retryCountsRef.current.clear();

    return files;
  }, [maxFiles, generateFileId]);

  // Update individual file progress
  const updateFileProgress = useCallback((fileId, updates) => {
    setState(prev => {
      const updatedFiles = prev.files.map(file => {
        if (file.id === fileId) {
          const updatedFile = { ...file, ...updates };

          // Set timestamps
          if (updates.status === 'uploading' && !file.startTime) {
            updatedFile.startTime = Date.now();
          }
          if ((updates.status === 'complete' || updates.status === 'error') && !file.endTime) {
            updatedFile.endTime = Date.now();
          }

          return updatedFile;
        }
        return file;
      });

      // Calculate overall statistics
      const completedFiles = updatedFiles.filter(f => f.status === 'complete').length;
      const failedFiles = updatedFiles.filter(f => f.status === 'error').length;
      const cancelledFiles = updatedFiles.filter(f => f.status === 'cancelled').length;
      const processingFiles = updatedFiles.filter(f =>
        ['uploading', 'validating', 'processing'].includes(f.status)
      ).length;

      // Calculate overall progress
      const totalProgress = updatedFiles.reduce((sum, file) => sum + file.progress, 0);
      const overallProgress = updatedFiles.length > 0 ? totalProgress / updatedFiles.length : 0;

      const newState = {
        ...prev,
        files: updatedFiles,
        completedFiles,
        failedFiles,
        cancelledFiles,
        overallProgress,
        isActive: processingFiles > 0
      };

      // Trigger callbacks
      if (onProgress) {
        onProgress({
          fileId,
          file: updatedFiles.find(f => f.id === fileId),
          overallProgress,
          completedFiles,
          failedFiles,
          totalFiles: updatedFiles.length
        });
      }

      return newState;
    });
  }, [onProgress]);

  // Start processing files
  const startProcessing = useCallback(() => {
    setState(prev => ({ ...prev, isActive: true }));

    // Update all pending files to uploading status
    state.files.forEach(file => {
      if (file.status === 'pending') {
        updateFileProgress(file.id, { status: 'uploading', progress: 0 });
      }
    });
  }, [state.files, updateFileProgress]);

  // Cancel processing
  const cancelProcessing = useCallback((fileIds = null) => {
    const targetIds = fileIds || state.files.map(f => f.id);

    targetIds.forEach(fileId => {
      // Cancel abort controller
      const controller = abortControllersRef.current.get(fileId);
      if (controller) {
        controller.abort();
        abortControllersRef.current.delete(fileId);
      }

      // Clear timeouts
      const timeout = timeoutsRef.current.get(fileId);
      if (timeout) {
        clearTimeout(timeout);
        timeoutsRef.current.delete(timeout);
      }

      // Update file status
      updateFileProgress(fileId, {
        status: 'cancelled',
        error: 'Processing cancelled by user'
      });
    });

    if (onCancel) {
      onCancel(targetIds);
    }
  }, [state.files, updateFileProgress, onCancel]);

  // Retry failed files
  const retryFiles = useCallback((fileIds = null) => {
    const targetIds = fileIds || state.files
      .filter(f => f.status === 'error')
      .map(f => f.id);

    targetIds.forEach(fileId => {
      const currentRetries = retryCountsRef.current.get(fileId) || 0;

      if (currentRetries < retryAttempts) {
        retryCountsRef.current.set(fileId, currentRetries + 1);

        // Reset file state for retry
        updateFileProgress(fileId, {
          status: 'pending',
          progress: 0,
          error: null,
          startTime: null,
          endTime: null
        });

        // Delay retry if specified
        if (retryDelay > 0) {
          const timeout = setTimeout(() => {
            updateFileProgress(fileId, { status: 'uploading' });
            timeoutsRef.current.delete(fileId);
          }, retryDelay);

          timeoutsRef.current.set(fileId, timeout);
        } else {
          updateFileProgress(fileId, { status: 'uploading' });
        }
      }
    });
  }, [state.files, updateFileProgress, retryAttempts, retryDelay]);

  // Clear completed/failed files
  const clearFiles = useCallback((statusFilter = null) => {
    setState(prev => {
      let filteredFiles = prev.files;

      if (statusFilter) {
        filteredFiles = prev.files.filter(f => !statusFilter.includes(f.status));
      } else {
        filteredFiles = [];
      }

      return {
        ...prev,
        files: filteredFiles,
        totalFiles: filteredFiles.length,
        completedFiles: filteredFiles.filter(f => f.status === 'complete').length,
        failedFiles: filteredFiles.filter(f => f.status === 'error').length,
        cancelledFiles: filteredFiles.filter(f => f.status === 'cancelled').length,
        overallProgress: filteredFiles.length === 0 ? 0 : prev.overallProgress,
        isActive: filteredFiles.some(f => ['uploading', 'validating', 'processing'].includes(f.status))
      };
    });

    // Clear associated refs
    if (!statusFilter) {
      abortControllersRef.current.clear();
      timeoutsRef.current.clear();
      retryCountsRef.current.clear();
    }
  }, []);

  // Set abort controller for a file
  const setAbortController = useCallback((fileId, controller) => {
    abortControllersRef.current.set(fileId, controller);
  }, []);

  // Get file by ID
  const getFile = useCallback((fileId) => {
    return state.files.find(f => f.id === fileId);
  }, [state.files]);

  // Get files by status
  const getFilesByStatus = useCallback((status) => {
    return state.files.filter(f => f.status === status);
  }, [state.files]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Cancel all active operations
      abortControllersRef.current.forEach(controller => {
        controller.abort();
      });

      // Clear all timeouts
      timeoutsRef.current.forEach(timeout => {
        clearTimeout(timeout);
      });

      abortControllersRef.current.clear();
      timeoutsRef.current.clear();
      retryCountsRef.current.clear();
    };
  }, []);

  // Auto-complete detection
  useEffect(() => {
    const { files, isActive, completedFiles, failedFiles, totalFiles } = state;

    if (isActive && (completedFiles + failedFiles) === totalFiles && totalFiles > 0) {
      setState(prev => ({ ...prev, isActive: false }));

      if (onComplete) {
        onComplete({
          files,
          completedFiles,
          failedFiles,
          totalFiles,
          overallProgress: state.overallProgress
        });
      }
    }
  }, [state, onComplete]);

  return {
    // State
    files: state.files,
    overallProgress: state.overallProgress,
    isActive: state.isActive,
    totalFiles: state.totalFiles,
    completedFiles: state.completedFiles,
    failedFiles: state.failedFiles,
    cancelledFiles: state.cancelledFiles,

    // Actions
    initializeFiles,
    updateFileProgress,
    startProcessing,
    cancelProcessing,
    retryFiles,
    clearFiles,
    setAbortController,

    // Utilities
    getFile,
    getFilesByStatus
  };
}
