/**
 * DataExporter Utility Class
 * Handles export functionality for invoice data
 * Epic 5, Story 5.2, Task 5.2.1: Invoice Table Component - Create export functionality
 */
class DataExporter {
  /**
   * Export invoices to CSV format
   * @param {Array} invoices - Array of invoice objects
   * @param {string} filename - Optional filename (default: invoices_YYYY-MM-DD.csv)
   * @returns {Promise<void>}
   */
  static async exportToCSV(invoices, filename = null) {
    if (!invoices || invoices.length === 0) {
      throw new Error('No invoices to export');
    }

    const csvFilename = filename || `invoices_${new Date().toISOString().split('T')[0]}.csv`;

    // Define CSV headers
    const headers = [
      'Invoice Number',
      'Date',
      'Seller Name',
      'Seller Tax ID',
      'Buyer Name',
      'Buyer Tax ID',
      'Net Amount',
      'VAT Amount',
      'Gross Amount',
      'Currency',
      'Extraction Method',
      'File Name',
      'File Size (KB)',
      'Processed At'
    ];

    // Convert invoices to CSV rows
    const rows = invoices.map(invoice => [
      this.escapeCSVField(invoice.number || ''),
      this.escapeCSVField(invoice.date || ''),
      this.escapeCSVField(invoice.seller_name || ''),
      this.escapeCSVField(invoice.seller_tax_no || ''),
      this.escapeCSVField(invoice.buyer_name || ''),
      this.escapeCSVField(invoice.buyer_tax_no || ''),
      this.formatNumber(invoice.total_net),
      this.formatNumber(invoice.total_vat),
      this.formatNumber(invoice.total_gross),
      this.escapeCSVField(invoice.currency || 'PLN'),
      this.escapeCSVField(invoice.extractionMethod || ''),
      this.escapeCSVField(invoice.filename || ''),
      this.formatNumber((invoice.fileSize || 0) / 1024, 1),
      this.escapeCSVField(invoice.processedAt || '')
    ]);

    // Combine headers and rows
    const csvContent = [headers, ...rows]
      .map(row => row.join(','))
      .join('\n');

    // Create and download file
    await this.downloadFile(csvContent, csvFilename, 'text/csv');
  }

  /**
   * Export invoices to JSON format
   * @param {Array} invoices - Array of invoice objects
   * @param {string} filename - Optional filename (default: invoices_YYYY-MM-DD.json)
   * @returns {Promise<void>}
   */
  static async exportToJSON(invoices, filename = null) {
    if (!invoices || invoices.length === 0) {
      throw new Error('No invoices to export');
    }

    const jsonFilename = filename || `invoices_${new Date().toISOString().split('T')[0]}.json`;

    const exportData = {
      exportedAt: new Date().toISOString(),
      totalInvoices: invoices.length,
      invoices: invoices.map(invoice => ({
        id: invoice.id,
        number: invoice.number,
        date: invoice.date,
        seller: {
          name: invoice.seller_name,
          taxId: invoice.seller_tax_no,
          address: invoice.seller_address,
          bankAccount: invoice.seller_bank_account
        },
        buyer: {
          name: invoice.buyer_name,
          taxId: invoice.buyer_tax_no,
          address: invoice.buyer_address,
          email: invoice.buyer_email
        },
        amounts: {
          net: parseFloat(invoice.total_net) || 0,
          vat: parseFloat(invoice.total_vat) || 0,
          gross: parseFloat(invoice.total_gross) || 0,
          currency: invoice.currency || 'PLN'
        },
        processing: {
          extractionMethod: invoice.extractionMethod,
          filename: invoice.filename,
          fileSize: invoice.fileSize,
          processedAt: invoice.processedAt
        },
        positions: invoice.positions || []
      }))
    };

    const jsonContent = JSON.stringify(exportData, null, 2);
    await this.downloadFile(jsonContent, jsonFilename, 'application/json');
  }

  /**
   * Export summary statistics to CSV
   * @param {Array} invoices - Array of invoice objects
   * @param {string} groupBy - Grouping criteria
   * @param {string} filename - Optional filename
   * @returns {Promise<void>}
   */
  static async exportSummaryToCSV(invoices, groupBy = 'month', filename = null) {
    if (!invoices || invoices.length === 0) {
      throw new Error('No invoices to export');
    }

    const csvFilename = filename || `invoice_summary_${groupBy}_${new Date().toISOString().split('T')[0]}.csv`;

    // Group invoices
    const groups = this.groupInvoices(invoices, groupBy);

    // Define headers
    const headers = [
      'Period',
      'Invoice Count',
      'Total Net Amount',
      'Total VAT Amount',
      'Total Gross Amount',
      'Average Amount',
      'Currency'
    ];

    // Convert groups to CSV rows
    const rows = Object.entries(groups).map(([period, data]) => [
      this.escapeCSVField(period),
      data.count.toString(),
      this.formatNumber(data.totalNet),
      this.formatNumber(data.totalVat),
      this.formatNumber(data.totalGross),
      this.formatNumber(data.totalGross / data.count),
      this.escapeCSVField(data.currency || 'PLN')
    ]);

    // Combine headers and rows
    const csvContent = [headers, ...rows]
      .map(row => row.join(','))
      .join('\n');

    await this.downloadFile(csvContent, csvFilename, 'text/csv');
  }

  /**
   * Group invoices by specified criteria
   * @param {Array} invoices - Array of invoice objects
   * @param {string} groupBy - Grouping criteria
   * @returns {Object} Grouped invoice data
   */
  static groupInvoices(invoices, groupBy) {
    const groups = {};

    invoices.forEach(invoice => {
      const groupKey = this.getGroupKey(invoice, groupBy);
      if (!groups[groupKey]) {
        groups[groupKey] = {
          count: 0,
          totalNet: 0,
          totalVat: 0,
          totalGross: 0,
          currency: invoice.currency || 'PLN'
        };
      }

      groups[groupKey].count++;
      groups[groupKey].totalNet += parseFloat(invoice.total_net) || 0;
      groups[groupKey].totalVat += parseFloat(invoice.total_vat) || 0;
      groups[groupKey].totalGross += parseFloat(invoice.total_gross) || 0;
    });

    return groups;
  }

  /**
   * Get group key for invoice based on groupBy setting
   * @param {Object} invoice - Invoice object
   * @param {string} groupBy - Grouping criteria
   * @returns {string} Group key
   */
  static getGroupKey(invoice, groupBy) {
    const date = new Date(invoice.date || invoice.processedAt);

    switch (groupBy) {
      case 'year':
        return date.getFullYear().toString();
      case 'quarter':
        const quarter = Math.floor(date.getMonth() / 3) + 1;
        return `${date.getFullYear()} Q${quarter}`;
      case 'month':
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      case 'week':
        const weekNumber = this.getWeekNumber(date);
        return `${date.getFullYear()} W${weekNumber}`;
      case 'day':
        return date.toISOString().split('T')[0];
      default:
        return 'All';
    }
  }

  /**
   * Get week number for date
   * @param {Date} date - Date object
   * @returns {number} Week number
   */
  static getWeekNumber(date) {
    const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
    const dayNum = d.getUTCDay() || 7;
    d.setUTCDate(d.getUTCDate() + 4 - dayNum);
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
    return Math.ceil((((d - yearStart) / 86400000) + 1) / 7);
  }

  /**
   * Escape CSV field to handle commas, quotes, and newlines
   * @param {string} field - Field value
   * @returns {string} Escaped field
   */
  static escapeCSVField(field) {
    if (field === null || field === undefined) { return ''; }

    const stringField = String(field);

    // If field contains comma, quote, or newline, wrap in quotes and escape quotes
    if (stringField.includes(',') || stringField.includes('"') || stringField.includes('\n')) {
      return `"${stringField.replace(/"/g, '""')}"`;
    }

    return stringField;
  }

  /**
   * Format number for export
   * @param {number|string} value - Number value
   * @param {number} decimals - Number of decimal places
   * @returns {string} Formatted number
   */
  static formatNumber(value, decimals = 2) {
    const num = parseFloat(value) || 0;
    return num.toFixed(decimals);
  }

  /**
   * Download file using browser download API
   * @param {string} content - File content
   * @param {string} filename - File name
   * @param {string} mimeType - MIME type
   * @returns {Promise<void>}
   */
  static async downloadFile(content, filename, mimeType) {
    try {
      // Create blob
      const blob = new Blob([content], { type: mimeType });

      // Create download link
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;

      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up
      URL.revokeObjectURL(url);

      console.log(`✅ Successfully exported ${filename}`);
    } catch (error) {
      console.error('❌ Export failed:', error);
      throw new Error(`Failed to export file: ${error.message}`);
    }
  }
}

export default DataExporter;
