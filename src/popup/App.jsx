import React, { useState, useEffect } from 'react';
import { HashRouter as Router, Routes, Route, Navigate } from 'react-router-dom';

// Import components with fallbacks
import MainLayout from './components/Layout/MainLayout.jsx';
import UploadPage from './components/upload/UploadPage.jsx';
import TablePage from './components/tables/TablePage.jsx';
import AnalyticsPage from './components/analytics/AnalyticsPage.jsx';
import { SettingsPage } from '../components/features/settings/SettingsPage.jsx';

// Import hooks
import { useExtensionState } from './hooks/useExtensionState.js';
import { useSettings } from './hooks/useSettings.js';

// Import unified components
import { PageLoading } from '../components/ui/feedback/LoadingSpinner.jsx';

function App() {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Custom hooks for state management
  const {
    invoices,
    isProcessing,
    addInvoice,
    removeInvoice,
    clearInvoices
  } = useExtensionState();

  const {
    settings,
    updateSettings,
    resetSettings,
    isLoading: settingsLoading
  } = useSettings();

  // Initialize extension
  useEffect(() => {
    const initializeExtension = async () => {
      try {
        console.log('🚀 Initializing MVAT extension...');

        // Check if we're in a Chrome extension context
        const isExtensionContext = !!(
          typeof chrome !== 'undefined' &&
          chrome.runtime &&
          chrome.runtime.id
        );

        if (!isExtensionContext) {
          console.warn('⚠️ Not in Chrome extension context - running in demo mode');
        } else {
          // Test connection to background script
          try {
            await chrome.runtime.sendMessage({ type: 'PING' });
            console.log('✅ Background script connection established');
          } catch (err) {
            console.warn('⚠️ Background script not responding:', err);
          }
        }

        // Wait for settings to load with timeout
        let attempts = 0;
        const maxAttempts = 10;
        while (settingsLoading && attempts < maxAttempts) {
          await new Promise(resolve => setTimeout(resolve, 100));
          attempts++;
        }

        if (attempts >= maxAttempts) {
          console.warn('⚠️ Settings loading timeout, continuing with initialization');
        }

        console.log('✅ MVAT extension initialized successfully');
        setIsLoading(false);

      } catch (err) {
        console.error('❌ Failed to initialize MVAT extension:', err);
        setError(err.message);
        setIsLoading(false);
      }
    };

    // Initialize only once on mount
    initializeExtension();
  }, []); // Remove settingsLoading dependency to prevent infinite loops

  // Loading state
  if (isLoading) {
    return (
      <div className="popup-container">
        <PageLoading message="Loading MVAT..." />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="popup-container">
        <PageLoading
          error={error}
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  // Main application context
  const appContext = {
    // State
    invoices,
    isProcessing,
    settings,

    // Actions
    addInvoice,
    removeInvoice,
    clearInvoices,
    updateSettings,
    resetSettings,

    // Utilities
    setError
  };

  return (
    <Router>
      <div className="popup-container bg-white">
        <MainLayout context={appContext}>
          <Routes>
            {/* Default route - redirect to upload */}
            <Route path="/" element={<Navigate to="/upload" replace />} />

            {/* Main pages */}
            <Route
              path="/upload"
              element={<UploadPage context={appContext} />}
            />
            <Route
              path="/table"
              element={<TablePage context={appContext} />}
            />
            <Route
              path="/analytics"
              element={<AnalyticsPage />}
            />
            <Route
              path="/settings"
              element={<SettingsPage />}
            />

            {/* Fallback route */}
            <Route
              path="*"
              element={<Navigate to="/upload" replace />}
            />
          </Routes>
        </MainLayout>
      </div>
    </Router>
  );
}

export default App;
