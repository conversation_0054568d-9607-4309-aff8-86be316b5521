import React from 'react';
import { createRoot } from 'react-dom/client';
import App from './App.jsx';
import './styles/globals.css';
import { environmentConfig } from '../services/EnvironmentConfigService.js';

console.log('🔧 MVAT: main.jsx module loaded');
console.warn('🔧 MVAT: main.jsx module loaded (warning level)');

// Error boundary component
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    this.setState({
      error: error,
      errorInfo: errorInfo
    });

    // Log error for debugging
    console.error('MVAT Error Boundary:', error, errorInfo);

    // Send error to background script for logging
    if (chrome?.runtime?.sendMessage) {
      chrome.runtime.sendMessage({
        type: 'ERROR_LOG',
        error: {
          message: error.message,
          stack: error.stack,
          componentStack: errorInfo.componentStack
        }
      }).catch(err => {
        console.warn('Failed to send error to background script:', err);
      });
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
          <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6 text-center">
            <div className="text-red-500 text-4xl mb-4">⚠️</div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Something went wrong
            </h2>
            <p className="text-gray-600 mb-4">
              The MVAT extension encountered an unexpected error.
            </p>

            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="text-left mb-4 p-3 bg-gray-100 rounded text-sm">
                <summary className="cursor-pointer font-medium">Error Details</summary>
                <pre className="mt-2 text-xs overflow-auto">
                  {this.state.error.toString()}
                  {this.state.errorInfo.componentStack}
                </pre>
              </details>
            )}

            <div className="space-y-2">
              <button
                onClick={() => window.location.reload()}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition-colors"
              >
                Reload Extension
              </button>
              <button
                onClick={() => this.setState({ hasError: false, error: null, errorInfo: null })}
                className="w-full bg-gray-200 text-gray-800 py-2 px-4 rounded hover:bg-gray-300 transition-colors"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Initialize the React app
async function initializeApp() {
  console.log('🚀 MVAT Chrome Extension: Starting initialization...');
  console.log('🔧 MVAT: initializeApp function called');

  const container = document.getElementById('root');

  if (!container) {
    console.error('MVAT: Root container not found');
    return;
  }

  console.log('✅ MVAT: Root container found:', container);

  let envInitSuccess = false;
  try {
    // Initialize environment configuration with timeout
    console.log('🔧 MVAT: Starting environment configuration initialization...');
    console.log('🔧 MVAT: Environment config object:', typeof environmentConfig, environmentConfig);

    const initPromise = environmentConfig.initialize();
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Environment initialization timeout')), 3000);
    });

    console.log('🔧 MVAT: Racing initialization promise with timeout...');
    const initSuccess = await Promise.race([initPromise, timeoutPromise]);
    envInitSuccess = true;

    if (initSuccess) {
      console.log('✅ Environment configuration initialized');
    } else {
      console.warn('⚠️ Environment configuration initialized with warnings');
    }

    // Development mode: Log startup information
    if (isDevelopmentMode()) {
      console.warn('🔍 MVAT Extension: Development Mode - Startup Information');
      console.warn('📋 Extension Context:', JSON.stringify({
        isChrome: typeof chrome !== 'undefined',
        hasWindow: typeof window !== 'undefined',
        protocol: window.location.protocol,
        hostname: window.location.hostname,
        url: window.location.href
      }));

      try {
        console.warn('📋 Environment Variables Available:', JSON.stringify({
          total: Object.keys(environmentConfig.getAll()).length,
          apiKeysConfigured: !!environmentConfig.getApiKey('deepseek'),
          companyConfigured: !!environmentConfig.getCompanyInfo()?.name,
          featuresEnabled: Object.keys(environmentConfig.get('features', {})).length
        }));
      } catch (envError) {
        console.warn('⚠️ Could not access environment configuration for logging:', envError.message);
      }

      console.warn('🔍 MVAT Extension: End of startup information');
    }

  } catch (error) {
    console.error('❌ Failed to initialize environment configuration:', error);
    console.error('❌ Error details:', error.message, error.stack);
    console.warn('⚠️ Continuing with default configuration...');
    // Don't prevent app loading, just log the error and continue
  }

  console.log('🔧 MVAT: Environment initialization completed. Success:', envInitSuccess);

  // Force render even if environment config failed
  console.log('🔧 MVAT: Proceeding with React app rendering...');

  // Clear loading spinner
  console.log('🔧 MVAT: Clearing container content...');
  container.innerHTML = '';

  // Create React root and render app
  console.log('🔧 MVAT: Creating React root...');
  const root = createRoot(container);
  console.log('🔧 MVAT: React root created:', root);

  // Test with a simple component first
  console.log('🔧 MVAT: Rendering React app...');

  try {
    // Render React app (circular dependency fixed)
    root.render(
      <ErrorBoundary>
        <div className="popup-container" style={{ padding: '20px', background: 'white' }}>
          <div id="mvat-app-container">
            <App />
          </div>
        </div>
      </ErrorBoundary>
    );

    console.log('🎉 MVAT extension loaded successfully');
  } catch (renderError) {
    console.error('❌ Failed to render React app:', renderError);
    console.error('❌ Render error details:', renderError.message, renderError.stack);

    // Fallback: render a simple HTML structure
    container.innerHTML = `
      <div style="padding: 20px; font-family: Arial, sans-serif;">
        <h1>� MVAT Extension</h1>
        <p style="color: red;">React app failed to load. Error: ${renderError.message}</p>
        <p style="color: #666; font-size: 14px;">Please check the console for more details and try reloading the extension.</p>
      </div>
    `;
  }
}

/**
 * Check if we're in development mode
 * For Chrome extensions, we check if NODE_ENV is development or if we're in a dev build
 */
function isDevelopmentMode() {
  // Check environment variables first
  if (typeof window !== 'undefined' && window.__MVAT_ENV__) {
    return window.__MVAT_ENV__.NODE_ENV === 'development' ||
           window.__MVAT_ENV__.DEBUG_MODE === 'true';
  }

  // Fallback: check for development indicators
  return typeof window !== 'undefined' &&
         (window.location.protocol === 'file:' ||
          window.location.hostname === 'localhost' ||
          window.location.hostname === '127.0.0.1' ||
          // Chrome extension development mode indicators
          (window.location.protocol === 'chrome-extension:' &&
           (document.title.includes('MVAT') ||
            window.location.href.includes('popup.html'))));
}

// Wait for DOM to be ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeApp);
} else {
  initializeApp();
}

// Handle Chrome extension context invalidation
if (chrome?.runtime?.onConnect) {
  chrome.runtime.onConnect.addListener((port) => {
    port.onDisconnect.addListener(() => {
      if (chrome.runtime.lastError) {
        console.warn('MVAT: Extension context invalidated, reloading...');
        window.location.reload();
      }
    });
  });
}

// Export for testing
export { ErrorBoundary };
