/**
 * CSP-Compliant Error Handler for MVAT Chrome Extension
 * Handles errors and loading timeouts without inline scripts
 */

// Error handling for the extension
function handleError(event) {
  console.error('MVAT Error:', event.error);

  const root = document.getElementById('root');
  if (root && !root.querySelector('.error-fallback')) {
    showErrorFallback(root, 'Something went wrong', 'Failed to load MVAT extension');
  }
}

// Loading timeout handler
function handleLoadingTimeout() {
  const root = document.getElementById('root');

  // Check if React app has loaded by looking for React-rendered content
  if (!root?.hasChildNodes() || root.children.length === 0) {
    console.warn('MVAT: React app failed to load within timeout');
    showErrorFallback(root, 'Loading timeout', 'The extension is taking longer than expected to load');
  }
}

// Show error fallback UI
function showErrorFallback(root, title, message) {
  const errorDiv = document.createElement('div');
  errorDiv.className = 'error-fallback';

  const titleDiv = document.createElement('div');
  titleDiv.className = 'error-title';
  titleDiv.textContent = title;

  const messageDiv = document.createElement('div');
  messageDiv.className = 'error-message';
  messageDiv.textContent = message;

  const button = document.createElement('button');
  button.className = 'error-button';
  button.textContent = 'Reload Extension';
  button.addEventListener('click', () => {
    window.location.reload();
  });

  errorDiv.appendChild(titleDiv);
  errorDiv.appendChild(messageDiv);
  errorDiv.appendChild(button);

  root.innerHTML = '';
  root.appendChild(errorDiv);
}

// Set up event listeners
window.addEventListener('error', handleError);
window.addEventListener('unhandledrejection', (event) => {
  console.error('MVAT Unhandled Promise Rejection:', event.reason);
  handleError({ error: event.reason });
});

// Set up loading timeout
setTimeout(handleLoadingTimeout, 10000); // 10 second timeout

// Export for potential use by other modules
export { handleError, showErrorFallback };
