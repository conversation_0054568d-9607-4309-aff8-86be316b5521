import React from 'react';

/**
 * TableRow Component
 * Renders individual table rows with proper data formatting
 * Epic 3, Story 3.1, Task 3.1.1: Base Table Component
 */
function TableRow({
  invoice,
  columns,
  onSelect,
  onDelete,
  isSelected = false,
  className = ''
}) {
  const formatCellValue = (invoice, column) => {
    const value = invoice[column.key];

    switch (column.key) {
      case 'filename':
        return (
          <div className="flex items-center min-w-0">
            <span className="text-green-500 mr-2 flex-shrink-0">📄</span>
            <div className="min-w-0">
              <div className="font-medium text-sm truncate" title={invoice.filename}>
                {invoice.filename || 'Unknown'}
              </div>
              <div className="text-xs text-gray-500">
                {invoice.fileSize ? `${(invoice.fileSize / 1024).toFixed(1)} KB` : ''}
              </div>
            </div>
          </div>
        );

      case 'extractionMethod':
        const method = invoice.extractionMethod || 'unknown';
        const methodConfig = {
          pdf_text: { label: '📄 PDF Text', className: 'bg-green-100 text-green-800' },
          pdf_ocr: { label: '🔍 PDF OCR', className: 'bg-yellow-100 text-yellow-800' },
          ocr: { label: '👁️ OCR', className: 'bg-blue-100 text-blue-800' },
          unknown: { label: '❓ Unknown', className: 'bg-gray-100 text-gray-800' }
        };
        const config = methodConfig[method] || methodConfig.unknown;

        return (
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${config.className}`}>
            {config.label}
          </span>
        );

      case 'number':
        return (
          <span className="font-mono text-sm" title={value || 'No invoice number'}>
            {value || '-'}
          </span>
        );

      case 'date':
        if (!value) { return '-'; }
        try {
          return new Date(value).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
          });
        } catch {
          return value;
        }

      case 'seller_name':
      case 'buyer_name':
        return (
          <div className="max-w-32 truncate" title={value || 'Not specified'}>
            {value || '-'}
          </div>
        );

      case 'total_gross':
      case 'total_net':
      case 'total_vat':
        if (!value && value !== 0) { return '-'; }
        const amount = parseFloat(value);
        const currency = invoice.currency || 'PLN';
        return (
          <span className="font-medium" title={`${amount.toFixed(2)} ${currency}`}>
            {amount.toFixed(2)} {currency}
          </span>
        );

      case 'related_documents':
        const relatedCount = invoice.relationshipData?.similarDocuments?.length || 0;
        if (relatedCount === 0) {
          return (
            <span className="text-gray-400 text-sm" title="No related documents found">
              -
            </span>
          );
        }

        return (
          <div className="flex items-center">
            <span className="text-blue-600 text-sm font-medium" title={`${relatedCount} related document${relatedCount !== 1 ? 's' : ''} found`}>
              🔗 {relatedCount}
            </span>
          </div>
        );

      case 'actions':
        return (
          <div className="flex space-x-1">
            <button
              onClick={(e) => {
                e.stopPropagation();
                onSelect?.(invoice);
              }}
              className="text-blue-600 hover:text-blue-800 text-sm p-1 rounded hover:bg-blue-50 transition-colors"
              title="View details"
              aria-label={`View details for ${invoice.filename || 'invoice'}`}
            >
              👁️
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                onDelete?.(invoice.id);
              }}
              className="text-red-600 hover:text-red-800 text-sm p-1 rounded hover:bg-red-50 transition-colors"
              title="Delete invoice"
              aria-label={`Delete ${invoice.filename || 'invoice'}`}
            >
              🗑️
            </button>
          </div>
        );

      default:
        // Handle custom formatters if provided in column config
        if (column.formatter && typeof column.formatter === 'function') {
          return column.formatter(value, invoice);
        }

        // Default string representation
        if (value === null || value === undefined) { return '-'; }
        if (typeof value === 'boolean') { return value ? '✅' : '❌'; }
        if (typeof value === 'object') { return JSON.stringify(value); }
        return value.toString();
    }
  };

  const handleRowClick = () => {
    onSelect?.(invoice);
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleRowClick();
    }
  };

  return (
    <tr
      className={`table-row ${isSelected ? 'bg-blue-50 border-blue-200' : ''} ${className}`}
      onClick={handleRowClick}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="row"
      aria-selected={isSelected}
      aria-label={`Invoice ${invoice.number || invoice.filename || 'row'}`}
    >
      {columns.map((column) => (
        <td
          key={column.key}
          className={`table-cell ${column.cellClassName || ''}`}
          style={{ width: column.width }}
          role="gridcell"
        >
          {formatCellValue(invoice, column)}
        </td>
      ))}
    </tr>
  );
}

/**
 * TableRowSkeleton Component
 * Loading skeleton for table rows
 */
export function TableRowSkeleton({ columns, className = '' }) {
  return (
    <tr className={`table-row animate-pulse ${className}`}>
      {columns.map((column) => (
        <td key={column.key} className="table-cell">
          <div className="h-4 bg-gray-200 rounded w-3/4" />
        </td>
      ))}
    </tr>
  );
}

/**
 * EmptyTableRow Component
 * Displays when no data is available
 */
export function EmptyTableRow({ columns, message = 'No data available', className = '' }) {
  return (
    <tr className={`table-row ${className}`}>
      <td colSpan={columns.length} className="table-cell text-center py-8">
        <div className="text-gray-500">
          <div className="text-4xl mb-2">📊</div>
          <p>{message}</p>
        </div>
      </td>
    </tr>
  );
}

export default TableRow;
