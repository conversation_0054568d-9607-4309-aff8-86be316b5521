import React from 'react';

/**
 * TableHeader Component
 * Provides sortable column headers for tables
 * Epic 3, Story 3.1, Task 3.1.1: Base Table Component
 */
function TableHeader({
  columns,
  sortField,
  sortDirection,
  onSort,
  className = ''
}) {
  const getSortIcon = (columnKey) => {
    if (sortField !== columnKey) {
      return <span className="text-gray-400 ml-1">↕️</span>;
    }
    return (
      <span className="text-blue-600 ml-1">
        {sortDirection === 'asc' ? '↑' : '↓'}
      </span>
    );
  };

  const getSortAriaLabel = (column) => {
    if (sortField !== column.key) {
      return `Sort by ${column.label}`;
    }
    const direction = sortDirection === 'asc' ? 'descending' : 'ascending';
    return `Sort by ${column.label} ${direction}`;
  };

  return (
    <thead className={`table-header ${className}`}>
      <tr>
        {columns.map((column) => (
          <th
            key={column.key}
            className={`table-header-cell ${
              column.sortable !== false ? 'cursor-pointer hover:bg-gray-100' : ''
            } ${column.className || ''}`}
            onClick={column.sortable !== false ? () => onSort(column.key) : undefined}
            style={{ width: column.width }}
            aria-sort={
              sortField === column.key
                ? sortDirection === 'asc'
                  ? 'ascending'
                  : 'descending'
                : 'none'
            }
            aria-label={column.sortable !== false ? getSortAriaLabel(column) : column.label}
            role={column.sortable !== false ? 'columnheader button' : 'columnheader'}
            tabIndex={column.sortable !== false ? 0 : undefined}
            onKeyDown={
              column.sortable !== false
                ? (e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    onSort(column.key);
                  }
                }
                : undefined
            }
          >
            <div className="flex items-center justify-between">
              <span className="flex items-center">
                {column.icon && <span className="mr-2">{column.icon}</span>}
                {column.label}
              </span>
              {column.sortable !== false && getSortIcon(column.key)}
            </div>
          </th>
        ))}
      </tr>
    </thead>
  );
}

/**
 * Default column configuration for invoice tables
 */
export const defaultInvoiceColumns = [
  {
    key: 'filename',
    label: 'File',
    icon: '📄',
    sortable: true,
    width: '200px',
    className: 'min-w-0'
  },
  {
    key: 'extractionMethod',
    label: 'Method',
    icon: '🔍',
    sortable: true,
    width: '120px'
  },
  {
    key: 'number',
    label: 'Invoice #',
    icon: '🔢',
    sortable: true,
    width: '120px'
  },
  {
    key: 'date',
    label: 'Date',
    icon: '📅',
    sortable: true,
    width: '100px'
  },
  {
    key: 'seller_name',
    label: 'Seller',
    icon: '🏢',
    sortable: true,
    width: '150px',
    className: 'min-w-0'
  },
  {
    key: 'buyer_name',
    label: 'Buyer',
    icon: '🏪',
    sortable: true,
    width: '150px',
    className: 'min-w-0'
  },
  {
    key: 'total_gross',
    label: 'Total',
    icon: '💰',
    sortable: true,
    width: '120px'
  },
  {
    key: 'related_documents',
    label: 'Related',
    icon: '🔗',
    sortable: false,
    width: '100px'
  },
  {
    key: 'actions',
    label: 'Actions',
    icon: '⚙️',
    sortable: false,
    width: '100px'
  }
];

/**
 * Compact column configuration for smaller screens
 */
export const compactInvoiceColumns = [
  {
    key: 'filename',
    label: 'File',
    icon: '📄',
    sortable: true,
    className: 'min-w-0'
  },
  {
    key: 'number',
    label: 'Invoice #',
    icon: '🔢',
    sortable: true,
    width: '100px'
  },
  {
    key: 'total_gross',
    label: 'Total',
    icon: '💰',
    sortable: true,
    width: '100px'
  },
  {
    key: 'actions',
    label: 'Actions',
    icon: '⚙️',
    sortable: false,
    width: '80px'
  }
];

export default TableHeader;
