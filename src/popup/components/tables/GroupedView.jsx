import React, { useState, useMemo } from 'react';

/**
 * GroupedView Component
 * Displays invoices grouped by time periods with drill-down functionality
 * Epic 5, Story 5.2, Task 5.2.2: Grouping & Views
 */
function GroupedView({ invoices, groupBy, onInvoiceSelect, currency = 'PLN' }) {
  const [expandedGroups, setExpandedGroups] = useState(new Set());

  // Get week number helper function
  const getWeekNumber = (date) => {
    const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
    const dayNum = d.getUTCDay() || 7;
    d.setUTCDate(d.getUTCDate() + 4 - dayNum);
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
    return Math.ceil((((d - yearStart) / 86400000) + 1) / 7);
  };

  // Get group key for invoice based on groupBy setting
  const getGroupKey = (invoice, groupBy) => {
    const date = new Date(invoice.date || invoice.processedAt);

    switch (groupBy) {
      case 'year':
        return date.getFullYear().toString();
      case 'quarter':
        const quarter = Math.floor(date.getMonth() / 3) + 1;
        return `${date.getFullYear()} Q${quarter}`;
      case 'month':
        const monthNames = [
          'January', 'February', 'March', 'April', 'May', 'June',
          'July', 'August', 'September', 'October', 'November', 'December'
        ];
        return `${monthNames[date.getMonth()]} ${date.getFullYear()}`;
      case 'week':
        const weekNumber = getWeekNumber(date);
        return `Week ${weekNumber}, ${date.getFullYear()}`;
      case 'day':
        return date.toLocaleDateString('en-US', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
      default:
        return 'All Invoices';
    }
  };

  // Group invoices by the specified criteria
  const groupedInvoices = useMemo(() => {
    if (!invoices || invoices.length === 0) { return {}; }

    const groups = {};

    invoices.forEach(invoice => {
      const groupKey = getGroupKey(invoice, groupBy);
      if (!groups[groupKey]) {
        groups[groupKey] = {
          invoices: [],
          totalNet: 0,
          totalVat: 0,
          totalGross: 0,
          count: 0
        };
      }

      groups[groupKey].invoices.push(invoice);
      groups[groupKey].totalNet += parseFloat(invoice.total_net) || 0;
      groups[groupKey].totalVat += parseFloat(invoice.total_vat) || 0;
      groups[groupKey].totalGross += parseFloat(invoice.total_gross) || 0;
      groups[groupKey].count++;
    });

    return groups;
  }, [invoices, groupBy]);

  // Toggle group expansion
  const toggleGroup = (groupKey) => {
    const newExpanded = new Set(expandedGroups);
    if (newExpanded.has(groupKey)) {
      newExpanded.delete(groupKey);
    } else {
      newExpanded.add(groupKey);
    }
    setExpandedGroups(newExpanded);
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('pl-PL', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(amount);
  };

  // Get group icon based on groupBy
  const getGroupIcon = (groupBy) => {
    switch (groupBy) {
      case 'year': return '📅';
      case 'quarter': return '📊';
      case 'month': return '🗓️';
      case 'week': return '📆';
      case 'day': return '📋';
      default: return '📁';
    }
  };

  // Sort groups by key (most recent first)
  const sortedGroups = Object.entries(groupedInvoices).sort(([a], [b]) => {
    // For date-based grouping, sort by date
    if (groupBy === 'year') {
      return parseInt(b) - parseInt(a);
    }
    return b.localeCompare(a);
  });

  if (sortedGroups.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="text-4xl mb-2">📊</div>
        <p className="text-gray-500">No invoices to group</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center mb-4">
        <span className="text-lg mr-2">{getGroupIcon(groupBy)}</span>
        <h3 className="text-lg font-semibold text-gray-900">
          Grouped by {groupBy.charAt(0).toUpperCase() + groupBy.slice(1)}
        </h3>
        <span className="ml-2 text-sm text-gray-500">
          ({sortedGroups.length} groups)
        </span>
      </div>

      {sortedGroups.map(([groupKey, groupData]) => (
        <div key={groupKey} className="border border-gray-200 rounded-lg overflow-hidden">
          {/* Group Header */}
          <div
            className="bg-gray-50 px-4 py-3 cursor-pointer hover:bg-gray-100 transition-colors"
            onClick={() => toggleGroup(groupKey)}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <span className="mr-2">
                  {expandedGroups.has(groupKey) ? '▼' : '▶'}
                </span>
                <h4 className="font-medium text-gray-900">{groupKey}</h4>
                <span className="ml-2 text-sm text-gray-500">
                  ({groupData.count} invoices)
                </span>
              </div>

              <div className="flex items-center space-x-4 text-sm">
                <div className="text-gray-600">
                  Net: <span className="font-medium">{formatCurrency(groupData.totalNet)}</span>
                </div>
                <div className="text-gray-600">
                  VAT: <span className="font-medium">{formatCurrency(groupData.totalVat)}</span>
                </div>
                <div className="text-gray-900 font-semibold">
                  Total: {formatCurrency(groupData.totalGross)}
                </div>
              </div>
            </div>
          </div>

          {/* Group Content */}
          {expandedGroups.has(groupKey) && (
            <div className="bg-white">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                        Invoice
                      </th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                        Date
                      </th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                        Seller
                      </th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                        Amount
                      </th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {groupData.invoices.map((invoice) => (
                      <tr key={invoice.id} className="hover:bg-gray-50">
                        <td className="px-4 py-2 text-sm">
                          <div className="flex items-center">
                            <span className="text-blue-500 mr-2">📄</span>
                            <div>
                              <div className="font-medium text-gray-900">
                                {invoice.number || 'N/A'}
                              </div>
                              <div className="text-xs text-gray-500">
                                {invoice.filename}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-4 py-2 text-sm text-gray-500">
                          {new Date(invoice.date || invoice.processedAt).toLocaleDateString()}
                        </td>
                        <td className="px-4 py-2 text-sm text-gray-500">
                          {invoice.seller_name || 'Unknown'}
                        </td>
                        <td className="px-4 py-2 text-sm">
                          <div className="text-gray-900 font-medium">
                            {formatCurrency(parseFloat(invoice.total_gross) || 0)}
                          </div>
                          <div className="text-xs text-gray-500">
                            Net: {formatCurrency(parseFloat(invoice.total_net) || 0)}
                          </div>
                        </td>
                        <td className="px-4 py-2 text-sm">
                          <button
                            onClick={() => onInvoiceSelect && onInvoiceSelect(invoice)}
                            className="text-blue-600 hover:text-blue-800 text-sm"
                            title="View details"
                          >
                            👁️ View
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
}

export default GroupedView;
