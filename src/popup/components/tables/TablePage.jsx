import React, { useState, useMemo } from 'react';
import SummaryStats from './SummaryStats';
import GroupedView from './GroupedView';
import TableControls from './TableControls';
import TableHeader, { defaultInvoiceColumns, compactInvoiceColumns } from './TableHeader';
import TableRow, { TableRowSkeleton, EmptyTableRow } from './TableRow';
import DataExporter from '../../utils/DataExporter';
import { useTableState } from '../../hooks/useTableState';

function TablePage({ context }) {
  const [viewMode, setViewMode] = useState('table'); // 'table', 'grouped'
  const [groupBy, setGroupBy] = useState('month'); // 'year', 'quarter', 'month', 'week', 'day'
  const [showSummary, setShowSummary] = useState(true);
  const [selectedInvoice, setSelectedInvoice] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  // Use the new table state hook
  const tableState = useTableState(context.invoices || [], {
    initialSortField: 'processedAt',
    initialSortDirection: 'desc',
    initialPageSize: 50,
    searchFields: ['filename', 'number', 'seller_name', 'buyer_name', 'total_gross']
  });

  // Get processed invoices from table state
  const processedInvoices = tableState.filteredData;

  // Determine which columns to use based on screen size
  const columns = useMemo(() => {
    // You could add responsive logic here
    return defaultInvoiceColumns;
  }, []);

  // Handle invoice deletion
  const handleDelete = (invoiceId) => {
    if (confirm('Are you sure you want to delete this invoice?')) {
      context.removeInvoice(invoiceId);
    }
  };

  // Handle export functionality
  const handleExport = async (format = 'csv') => {
    try {
      setIsLoading(true);
      const dataToExport = tableState.sortedData; // Use all filtered/sorted data, not just current page

      if (format === 'csv') {
        await DataExporter.exportToCSV(dataToExport);
      } else if (format === 'json') {
        await DataExporter.exportToJSON(dataToExport);
      } else if (format === 'summary') {
        await DataExporter.exportSummaryToCSV(dataToExport, groupBy);
      }
    } catch (error) {
      console.error('Export failed:', error);
      alert(`Export failed: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle invoice selection
  const handleInvoiceSelect = (invoice) => {
    setSelectedInvoice(invoice);
    // Could open a modal or navigate to detail view
    alert(`Selected invoice: ${invoice.number || invoice.filename}`);
  };

  if (!context.invoices || context.invoices.length === 0) {
    return (
      <div className="p-6 h-full flex flex-col items-center justify-center text-center">
        <div className="text-6xl mb-4">📊</div>
        <h2 className="text-xl font-semibold text-gray-900 mb-2">No Invoices Yet</h2>
        <p className="text-gray-600 mb-4">
          Upload some invoices to see them in the table
        </p>
        <button
          onClick={() => window.location.hash = '#/upload'}
          className="btn btn-primary"
        >
          Upload Invoices
        </button>
      </div>
    );
  }

  return (
    <div className="p-6 h-full flex flex-col">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Invoice Management</h2>
            <p className="text-gray-600 text-sm">
              {tableState.pagination.totalItems} of {context.invoices.length} invoices
              {tableState.filterText && ' (filtered)'}
            </p>
          </div>

          <div className="flex space-x-2">
            {/* Export Dropdown */}
            <div className="relative group">
              <button className="btn btn-secondary btn-sm" disabled={isLoading}>
                {isLoading ? '⏳ Exporting...' : '📤 Export ▼'}
              </button>
              <div className="absolute right-0 mt-1 w-48 bg-white border border-gray-200 rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
                <button
                  onClick={() => handleExport('csv')}
                  className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  📄 Export as CSV
                </button>
                <button
                  onClick={() => handleExport('json')}
                  className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  📋 Export as JSON
                </button>
                <button
                  onClick={() => handleExport('summary')}
                  className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  📊 Export Summary
                </button>
              </div>
            </div>

            <button
              onClick={() => context.clearInvoices()}
              className="btn btn-danger btn-sm"
              title="Clear all invoices"
            >
              🗑️ Clear All
            </button>
          </div>
        </div>

        {/* View Controls */}
        <div className="flex flex-wrap items-center gap-4 mb-4 p-4 bg-gray-50 rounded-lg">
          {/* View Mode Toggle */}
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700">View:</label>
            <div className="flex bg-white border border-gray-300 rounded-md">
              <button
                onClick={() => setViewMode('table')}
                className={`px-3 py-1 text-sm rounded-l-md ${
                  viewMode === 'table'
                    ? 'bg-blue-500 text-white'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                📋 Table
              </button>
              <button
                onClick={() => setViewMode('grouped')}
                className={`px-3 py-1 text-sm rounded-r-md ${
                  viewMode === 'grouped'
                    ? 'bg-blue-500 text-white'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                📊 Grouped
              </button>
            </div>
          </div>

          {/* Group By Selector */}
          {viewMode === 'grouped' && (
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-gray-700">Group by:</label>
              <select
                value={groupBy}
                onChange={(e) => setGroupBy(e.target.value)}
                className="px-3 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="year">📅 Year</option>
                <option value="quarter">📊 Quarter</option>
                <option value="month">🗓️ Month</option>
                <option value="week">📆 Week</option>
                <option value="day">📋 Day</option>
              </select>
            </div>
          )}

          {/* Summary Toggle */}
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700">
              <input
                type="checkbox"
                checked={showSummary}
                onChange={(e) => setShowSummary(e.target.checked)}
                className="mr-2"
              />
              Show Summary
            </label>
          </div>
        </div>

        {/* Table Controls */}
        <TableControls
          filterText={tableState.filterText}
          onFilterChange={tableState.handleFilter}
          pagination={tableState.pagination}
          onPageChange={tableState.handlePageChange}
          onPageSizeChange={tableState.handlePageSizeChange}
          onReset={tableState.resetTable}
          showPagination={viewMode === 'table'}
        />
      </div>

      {/* Summary Statistics */}
      {showSummary && (
        <SummaryStats
          invoices={processedInvoices}
          groupBy={viewMode === 'grouped' ? groupBy : 'none'}
          currency="PLN"
        />
      )}

      {/* Content Area */}
      <div className="flex-1 overflow-hidden">
        {viewMode === 'table' ? (
          /* Enhanced Table View */
          <div className="overflow-x-auto overflow-y-auto h-full">
            <table className="table" role="grid" aria-label="Invoice data table">
              <TableHeader
                columns={columns}
                sortField={tableState.sortField}
                sortDirection={tableState.sortDirection}
                onSort={tableState.handleSort}
              />
              <tbody className="table-body" role="rowgroup">
                {isLoading ? (
                  // Loading skeleton
                  Array.from({ length: 5 }).map((_, index) => (
                    <TableRowSkeleton key={`skeleton-${index}`} columns={columns} />
                  ))
                ) : tableState.data.length > 0 ? (
                  // Data rows
                  tableState.data.map((invoice) => (
                    <TableRow
                      key={invoice.id}
                      invoice={invoice}
                      columns={columns}
                      onSelect={handleInvoiceSelect}
                      onDelete={handleDelete}
                      isSelected={selectedInvoice?.id === invoice.id}
                    />
                  ))
                ) : (
                  // Empty state
                  <EmptyTableRow
                    columns={columns}
                    message={tableState.filterText ? 'No invoices match your search' : 'No invoices available'}
                  />
                )}
              </tbody>
            </table>
          </div>
        ) : (
          /* Grouped View */
          <GroupedView
            invoices={processedInvoices}
            groupBy={groupBy}
            onInvoiceSelect={handleInvoiceSelect}
            currency="PLN"
          />
        )}
      </div>
    </div>
  );
}

export default TablePage;
