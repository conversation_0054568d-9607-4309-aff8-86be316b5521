import React, { useMemo } from 'react';

/**
 * SummaryStats Component
 * Displays summary statistics for invoice data
 * Epic 5, Story 5.2, Task 5.2.2: Grouping & Views
 */
function SummaryStats({ invoices, groupBy = 'none', currency = 'PLN' }) {
  // Get week number helper function
  const getWeekNumber = (date) => {
    const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
    const dayNum = d.getUTCDay() || 7;
    d.setUTCDate(d.getUTCDate() + 4 - dayNum);
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
    return Math.ceil((((d - yearStart) / 86400000) + 1) / 7);
  };

  // Get group key for invoice based on groupBy setting
  const getGroupKey = (invoice, groupBy) => {
    const date = new Date(invoice.date || invoice.processedAt);

    switch (groupBy) {
      case 'year':
        return date.getFullYear().toString();
      case 'quarter':
        const quarter = Math.floor(date.getMonth() / 3) + 1;
        return `${date.getFullYear()} Q${quarter}`;
      case 'month':
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      case 'week':
        const weekNumber = getWeekNumber(date);
        return `${date.getFullYear()} W${weekNumber}`;
      case 'day':
        return date.toISOString().split('T')[0];
      default:
        return 'All';
    }
  };

  // Calculate summary statistics
  const stats = useMemo(() => {
    if (!invoices || invoices.length === 0) {
      return {
        totalInvoices: 0,
        totalNet: 0,
        totalVat: 0,
        totalGross: 0,
        averageAmount: 0,
        currency: currency,
        groupedStats: {}
      };
    }

    // Basic totals
    const totalNet = invoices.reduce((sum, inv) => sum + (parseFloat(inv.total_net) || 0), 0);
    const totalVat = invoices.reduce((sum, inv) => sum + (parseFloat(inv.total_vat) || 0), 0);
    const totalGross = invoices.reduce((sum, inv) => sum + (parseFloat(inv.total_gross) || 0), 0);
    const averageAmount = totalGross / invoices.length;

    // Grouped statistics
    const groupedStats = {};
    if (groupBy !== 'none') {
      invoices.forEach(invoice => {
        const groupKey = getGroupKey(invoice, groupBy);
        if (!groupedStats[groupKey]) {
          groupedStats[groupKey] = {
            count: 0,
            totalNet: 0,
            totalVat: 0,
            totalGross: 0
          };
        }

        groupedStats[groupKey].count++;
        groupedStats[groupKey].totalNet += parseFloat(invoice.total_net) || 0;
        groupedStats[groupKey].totalVat += parseFloat(invoice.total_vat) || 0;
        groupedStats[groupKey].totalGross += parseFloat(invoice.total_gross) || 0;
      });
    }

    return {
      totalInvoices: invoices.length,
      totalNet,
      totalVat,
      totalGross,
      averageAmount,
      currency,
      groupedStats
    };
  }, [invoices, groupBy, currency]);

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('pl-PL', {
      style: 'currency',
      currency: stats.currency,
      minimumFractionDigits: 2
    }).format(amount);
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-4 mb-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">📊 Summary Statistics</h3>

      {/* Overall Statistics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-blue-50 rounded-lg p-3">
          <div className="text-sm font-medium text-blue-600">Total Invoices</div>
          <div className="text-2xl font-bold text-blue-900">{stats.totalInvoices}</div>
        </div>

        <div className="bg-green-50 rounded-lg p-3">
          <div className="text-sm font-medium text-green-600">Total Net</div>
          <div className="text-lg font-bold text-green-900">{formatCurrency(stats.totalNet)}</div>
        </div>

        <div className="bg-yellow-50 rounded-lg p-3">
          <div className="text-sm font-medium text-yellow-600">Total VAT</div>
          <div className="text-lg font-bold text-yellow-900">{formatCurrency(stats.totalVat)}</div>
        </div>

        <div className="bg-purple-50 rounded-lg p-3">
          <div className="text-sm font-medium text-purple-600">Total Gross</div>
          <div className="text-lg font-bold text-purple-900">{formatCurrency(stats.totalGross)}</div>
        </div>
      </div>

      {/* Average Amount */}
      <div className="mb-6">
        <div className="bg-gray-50 rounded-lg p-3 inline-block">
          <div className="text-sm font-medium text-gray-600">Average Amount</div>
          <div className="text-xl font-bold text-gray-900">{formatCurrency(stats.averageAmount)}</div>
        </div>
      </div>

      {/* Grouped Statistics */}
      {groupBy !== 'none' && Object.keys(stats.groupedStats).length > 0 && (
        <div>
          <h4 className="text-md font-semibold text-gray-900 mb-3">
            📈 Breakdown by {groupBy.charAt(0).toUpperCase() + groupBy.slice(1)}
          </h4>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Period
                  </th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Count
                  </th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Net Amount
                  </th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    VAT Amount
                  </th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Gross Amount
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {Object.entries(stats.groupedStats)
                  .sort(([a], [b]) => b.localeCompare(a))
                  .map(([period, data]) => (
                    <tr key={period} className="hover:bg-gray-50">
                      <td className="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900">
                        {period}
                      </td>
                      <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                        {data.count}
                      </td>
                      <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                        {formatCurrency(data.totalNet)}
                      </td>
                      <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                        {formatCurrency(data.totalVat)}
                      </td>
                      <td className="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900">
                        {formatCurrency(data.totalGross)}
                      </td>
                    </tr>
                  ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
}

export default SummaryStats;
