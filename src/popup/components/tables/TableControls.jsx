import React from 'react';

/**
 * TableControls Component
 * Provides filtering and pagination controls for tables
 * Epic 3, Story 3.1, Task 3.1.1: Base Table Component
 */
function TableControls({
  filterText,
  onFilterChange,
  pagination,
  onPageChange,
  onPageSizeChange,
  onReset,
  showPagination = true,
  showPageSize = true,
  showReset = true,
  className = ''
}) {
  const {
    totalItems,
    totalPages,
    currentPage,
    pageSize,
    startItem,
    endItem,
    hasNextPage,
    hasPrevPage
  } = pagination;

  const pageSizeOptions = [25, 50, 100, 200];

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Search and Reset Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        {/* Search Input */}
        <div className="flex-1 max-w-md">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span className="text-gray-400 text-sm">🔍</span>
            </div>
            <input
              type="text"
              placeholder="Search invoices..."
              value={filterText}
              onChange={(e) => onFilterChange(e.target.value)}
              className="form-input pl-10 pr-4"
              aria-label="Search invoices"
            />
            {filterText && (
              <button
                onClick={() => onFilterChange('')}
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                aria-label="Clear search"
              >
                ✕
              </button>
            )}
          </div>
        </div>

        {/* Reset Button */}
        {showReset && (
          <button
            onClick={onReset}
            className="btn btn-secondary btn-sm"
            title="Reset all filters and sorting"
          >
            🔄 Reset
          </button>
        )}
      </div>

      {/* Pagination Controls */}
      {showPagination && totalItems > 0 && (
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          {/* Results Info */}
          <div className="text-sm text-gray-700">
            Showing <span className="font-medium">{startItem}</span> to{' '}
            <span className="font-medium">{endItem}</span> of{' '}
            <span className="font-medium">{totalItems}</span> results
          </div>

          {/* Page Size Selector */}
          {showPageSize && (
            <div className="flex items-center space-x-2">
              <label className="text-sm text-gray-700">Show:</label>
              <select
                value={pageSize}
                onChange={(e) => onPageSizeChange(Number(e.target.value))}
                className="form-input py-1 px-2 text-sm w-auto"
                aria-label="Items per page"
              >
                {pageSizeOptions.map(size => (
                  <option key={size} value={size}>
                    {size}
                  </option>
                ))}
              </select>
              <span className="text-sm text-gray-700">per page</span>
            </div>
          )}

          {/* Pagination Buttons */}
          {totalPages > 1 && (
            <div className="flex items-center space-x-1">
              {/* First Page */}
              <button
                onClick={() => onPageChange(1)}
                disabled={!hasPrevPage}
                className="btn btn-secondary btn-sm disabled:opacity-50 disabled:cursor-not-allowed"
                title="First page"
                aria-label="Go to first page"
              >
                ⏮️
              </button>

              {/* Previous Page */}
              <button
                onClick={() => onPageChange(currentPage - 1)}
                disabled={!hasPrevPage}
                className="btn btn-secondary btn-sm disabled:opacity-50 disabled:cursor-not-allowed"
                title="Previous page"
                aria-label="Go to previous page"
              >
                ◀️
              </button>

              {/* Page Numbers */}
              <div className="flex items-center space-x-1">
                {getPageNumbers(currentPage, totalPages).map((pageNum, index) => (
                  pageNum === '...' ? (
                    <span key={`ellipsis-${index}`} className="px-2 py-1 text-gray-500">
                      ...
                    </span>
                  ) : (
                    <button
                      key={pageNum}
                      onClick={() => onPageChange(pageNum)}
                      className={`px-3 py-1 text-sm rounded-md transition-colors ${
                        pageNum === currentPage
                          ? 'bg-blue-600 text-white'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                      aria-label={`Go to page ${pageNum}`}
                      aria-current={pageNum === currentPage ? 'page' : undefined}
                    >
                      {pageNum}
                    </button>
                  )
                ))}
              </div>

              {/* Next Page */}
              <button
                onClick={() => onPageChange(currentPage + 1)}
                disabled={!hasNextPage}
                className="btn btn-secondary btn-sm disabled:opacity-50 disabled:cursor-not-allowed"
                title="Next page"
                aria-label="Go to next page"
              >
                ▶️
              </button>

              {/* Last Page */}
              <button
                onClick={() => onPageChange(totalPages)}
                disabled={!hasNextPage}
                className="btn btn-secondary btn-sm disabled:opacity-50 disabled:cursor-not-allowed"
                title="Last page"
                aria-label="Go to last page"
              >
                ⏭️
              </button>
            </div>
          )}
        </div>
      )}

      {/* No Results Message */}
      {totalItems === 0 && filterText && (
        <div className="text-center py-8">
          <div className="text-4xl mb-2">🔍</div>
          <p className="text-gray-500 mb-2">No results found for "{filterText}"</p>
          <button
            onClick={() => onFilterChange('')}
            className="btn btn-secondary btn-sm"
          >
            Clear search
          </button>
        </div>
      )}
    </div>
  );
}

/**
 * Generate page numbers for pagination with ellipsis
 * @param {number} currentPage - Current active page
 * @param {number} totalPages - Total number of pages
 * @returns {Array} Array of page numbers and ellipsis
 */
function getPageNumbers(currentPage, totalPages) {
  const pages = [];
  const maxVisiblePages = 5;

  if (totalPages <= maxVisiblePages) {
    // Show all pages if total is small
    for (let i = 1; i <= totalPages; i++) {
      pages.push(i);
    }
  } else {
    // Always show first page
    pages.push(1);

    if (currentPage > 3) {
      pages.push('...');
    }

    // Show pages around current page
    const start = Math.max(2, currentPage - 1);
    const end = Math.min(totalPages - 1, currentPage + 1);

    for (let i = start; i <= end; i++) {
      if (!pages.includes(i)) {
        pages.push(i);
      }
    }

    if (currentPage < totalPages - 2) {
      pages.push('...');
    }

    // Always show last page
    if (!pages.includes(totalPages)) {
      pages.push(totalPages);
    }
  }

  return pages;
}

export default TableControls;
