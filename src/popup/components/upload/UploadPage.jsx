import React, { useState, useCallback } from 'react';
import documentProcessingService from '../../services/DocumentProcessingService.js';
import DragDropUpload from './DragDropUpload.jsx';
import { useFileUpload } from '../../hooks/useFileUpload.js';
import EnhancedPipelineVisualization, { LAYOUT_MODES, VIEW_MODES } from '../../../components/features/pipeline/EnhancedPipelineVisualization.jsx';
import { usePipelineWindow } from '../../../components/features/pipeline/PipelineWindow.jsx';

// Import unified loading components
import { LoadingSpinner } from '../../../components/ui/feedback/LoadingSpinner.jsx';

function UploadPage({ context }) {
  const [error, setError] = useState(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [pipelineLayout, setPipelineLayout] = useState(LAYOUT_MODES.COMPACT);
  const [isRecentUploadsExpanded, setIsRecentUploadsExpanded] = useState(false);

  // Pipeline window management
  const { openWindow, closeWindow, isWindowOpen } = usePipelineWindow();

  // Use the custom file upload hook
  const {
    uploadFiles,
    isUploading,
    progress,
    currentFile: hookCurrentFile,
    currentStage,
    errors,
    clearErrors
  } = useFileUpload({
    maxFiles: 10,
    maxSize: 10 * 1024 * 1024, // 10MB
    onError: (errorMessage) => setError(errorMessage),
    onComplete: (result) => {
      console.log('Upload completed:', result);
    }
  });

  // Handle file upload with enhanced pipeline visualization
  const handleFilesSelected = useCallback(async (files) => {
    if (!files || files.length === 0) { return; }

    setError(null);
    clearErrors();

    // Set the current file for pipeline visualization
    const file = files[0]; // Process first file
    setSelectedFile(file);

    // File processor function for the upload hook
    const fileProcessor = async (file, options = {}) => {
      const { onProgress } = options;

      try {
        // Update progress callback
        const progressCallback = (progress) => {
          onProgress?.({
            progress: progress.progress || 0,
            stage: progress.stage || 'processing'
          });
        };

        // Process file with real document processing service
        const result = await documentProcessingService.processDocument(file, progressCallback);

        if (result.success) {
          // Add processed invoice data to context
          context.addInvoice(result.data);
          return result.data;
        }
        throw new Error(result.error);

      } catch (err) {
        console.error('File processing error:', err);
        throw err;
      }
    };

    // Use the upload hook to process files
    try {
      const result = await uploadFiles(files, fileProcessor);
      if (!result.success) {
        setError(result.error || 'Upload failed');
      }
    } catch (err) {
      setError(err.message || 'Upload failed');
    }
  }, [context, uploadFiles, clearErrors]);

  // Handle error from drag drop component
  const handleUploadError = useCallback((errorMessage) => {
    setError(errorMessage);
  }, []);

  // Handle pipeline step completion
  const handleStepComplete = useCallback((result) => {
    console.log('Pipeline step completed:', result);
  }, []);

  // Handle pipeline processing state change
  const handleProcessingChange = useCallback((processing) => {
    // Update processing state if needed
  }, []);

  return (
    <div className="h-full flex">
      {/* Left Side - Upload Area */}
      <div className="w-1/2 p-6 border-r border-gray-200 flex flex-col">
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <h2 className="text-xl font-semibold text-gray-900">Upload Invoices</h2>
            <div className="flex items-center space-x-1">
              <button
                onClick={() => setPipelineLayout(LAYOUT_MODES.FULL_SCREEN)}
                className="px-2 py-1 bg-purple-600 text-white rounded text-xs hover:bg-purple-700"
                title="Full Screen View"
              >
                🖥️ Full
              </button>
            </div>
          </div>
          <p className="text-gray-600 text-sm">
            Upload PDF or image files to extract invoice data using AI
          </p>
        </div>

        {/* Status Cards */}
        <div className="mb-4 grid grid-cols-2 gap-4">
          <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
            <div className="text-sm font-medium text-blue-800">Processed Files</div>
            <div className="text-lg font-semibold text-blue-900">{context.invoices?.length || 0}</div>
          </div>
          <div className="bg-green-50 p-3 rounded-lg border border-green-200">
            <div className="text-sm font-medium text-green-800">Status</div>
            <div className="text-lg font-semibold text-green-900">
              {isUploading ? 'Processing...' : 'Ready'}
            </div>
          </div>
        </div>

        {/* Upload Area */}
        <div className="flex-1 flex flex-col">
          <DragDropUpload
            onFilesSelected={handleFilesSelected}
            onError={handleUploadError}
            maxFiles={10}
            maxSize={10 * 1024 * 1024} // 10MB
            disabled={isUploading}
            className="flex-1"
          />

          {/* Processing Status */}
          {isUploading && (
            <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center space-x-2">
                <LoadingSpinner size="small" color="blue" />
                <div className="text-sm">
                  <div className="font-medium text-blue-900">
                    {hookCurrentFile ? `Processing ${hookCurrentFile}` : 'Processing...'}
                  </div>
                  <div className="text-blue-700">{currentStage || 'Preparing...'}</div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Error display */}
        {(error || errors.length > 0) && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <div className="flex items-center">
              <span className="text-red-500 mr-2">⚠️</span>
              <div className="flex-1">
                {error && <p className="text-sm text-red-700">{error}</p>}
                {errors.length > 0 && (
                  <div className="text-sm text-red-700">
                    {errors.map((err, index) => (
                      <p key={index}>{err}</p>
                    ))}
                  </div>
                )}
              </div>
            </div>
            <button
              onClick={() => {
                setError(null);
                clearErrors();
              }}
              className="mt-2 text-xs text-red-600 hover:text-red-800"
            >
              Dismiss
            </button>
          </div>
        )}

        {/* Recent uploads summary - collapsible, folded by default */}
        {context.invoices && context.invoices.length > 0 && (
          <div className={`mt-6 ${isRecentUploadsExpanded ? 'flex-1 flex flex-col min-h-0' : 'flex-shrink-0'}`}>
            <div
              className="flex items-center justify-between mb-3 cursor-pointer hover:bg-gray-50 p-2 rounded-md transition-colors"
              onClick={() => setIsRecentUploadsExpanded(!isRecentUploadsExpanded)}
            >
              <div className="flex items-center space-x-2">
                <span className={`text-gray-400 transition-transform duration-200 ${isRecentUploadsExpanded ? 'rotate-90' : ''}`}>
                  ▶
                </span>
                <h3 className="text-sm font-medium text-gray-900">Recent Uploads Summary</h3>
              </div>
              <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                {context.invoices.length} files
              </span>
            </div>

            {isRecentUploadsExpanded && (
              <div className="flex-1 space-y-2 overflow-y-auto extension-scroll">
                {context.invoices.map((invoice) => (
                  <div key={invoice.id} className="p-2 bg-white border border-gray-200 rounded-md hover:shadow-sm transition-shadow">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2 flex-1 min-w-0">
                        <span className="text-success-500 text-xs">✓</span>
                        <span className="font-medium text-gray-900 text-xs truncate">{invoice.filename}</span>
                      </div>
                      <div className="flex items-center space-x-2 flex-shrink-0">
                        <span className="text-xs text-gray-500">
                          {new Date(invoice.processedAt).toLocaleTimeString()}
                        </span>
                      </div>
                    </div>
                    <div className="text-xs text-gray-600 mt-1">
                      <div className="flex justify-between">
                        <span>Invoice: {invoice.number || 'N/A'}</span>
                        <span className="font-medium text-gray-900">
                          {invoice.total_gross ? `${invoice.total_gross} ${invoice.currency || 'PLN'}` : 'N/A'}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}


      </div>

      {/* Right Side - Recent Uploads with Enhanced File Details */}
      <div className="w-1/2 flex flex-col p-6">
        {/* Recent uploads - improved layout with better space utilization */}
        {context.invoices && context.invoices.length > 0 ? (
          <div className="flex-1 flex flex-col min-h-0">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Recent Uploads</h3>
              <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                {context.invoices.length} files
              </span>
            </div>
            <div className="flex-1 space-y-3 overflow-y-auto extension-scroll">
              {context.invoices.map((invoice) => (
                <div key={invoice.id} className="p-4 bg-white border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                  {/* File Header */}
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <span className="text-success-500 text-lg">✓</span>
                      <div>
                        <div className="font-medium text-gray-900 text-sm">{invoice.filename}</div>
                        <div className="text-xs text-gray-500">
                          {new Date(invoice.processedAt).toLocaleString()}
                        </div>
                      </div>
                    </div>
                    <div className="text-xs text-gray-400">
                      82.3 kB
                    </div>
                  </div>

                  {/* File Details */}
                  <div className="space-y-2 mb-3">
                    <div className="p-2 bg-blue-50 rounded border border-blue-200">
                      <div className="text-xs font-medium text-blue-800 mb-1">Validation Results</div>
                      <div className="text-xs text-blue-700">Status: ✅ Valid</div>
                    </div>

                    <div className="p-2 bg-purple-50 rounded border border-purple-200">
                      <div className="text-xs font-medium text-purple-800 mb-1">Security Scan</div>
                      <div className="text-xs text-purple-700">Risk Level: Unknown</div>
                      <div className="text-xs text-purple-700">Score: N/A</div>
                    </div>

                    <div className="p-2 bg-gray-50 rounded border border-gray-200">
                      <div className="text-xs font-medium text-gray-800 mb-1">File Information</div>
                      <div className="text-xs text-gray-600">Size: 82.3 kB</div>
                      <div className="text-xs text-gray-600">Type: application/pdf</div>
                      <div className="text-xs text-gray-600">Modified: {new Date(invoice.processedAt).toLocaleDateString()}</div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => {
                          // Stop processing
                          console.log('Stop processing:', invoice.id);
                        }}
                        className="px-2 py-1 text-xs bg-red-50 text-red-600 border border-red-200 rounded hover:bg-red-100 transition-colors"
                        title="Stop processing"
                      >
                        Stop
                      </button>
                      <button
                        onClick={() => {
                          // Delete file
                          console.log('Delete file:', invoice.id);
                        }}
                        className="px-2 py-1 text-xs bg-gray-50 text-gray-600 border border-gray-200 rounded hover:bg-gray-100 transition-colors"
                        title="Delete file"
                      >
                        Delete
                      </button>
                      <button
                        onClick={() => {
                          // Cancel processing
                          console.log('Cancel processing:', invoice.id);
                        }}
                        className="px-2 py-1 text-xs bg-yellow-50 text-yellow-600 border border-yellow-200 rounded hover:bg-yellow-100 transition-colors"
                        title="Cancel processing"
                      >
                        Cancel
                      </button>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => {
                          // Repeat processing
                          console.log('Repeat processing:', invoice.id);
                        }}
                        className="px-2 py-1 text-xs bg-blue-50 text-blue-600 border border-blue-200 rounded hover:bg-blue-100 transition-colors"
                        title="Repeat processing"
                      >
                        Repeat
                      </button>
                      <button
                        onClick={() => {
                          // Open pipeline in separate window for this file
                          const fileObj = { name: invoice.filename, id: invoice.id };
                          openWindow(fileObj, {
                            isProcessing: false,
                            onProcessingChange: (processing) => console.log('Processing:', processing),
                            onStepComplete: (result) => console.log('Step complete:', result),
                            onError: (error) => console.error('Pipeline error:', error),
                            autoRun: false
                          });
                        }}
                        className={`px-2 py-1 text-xs font-medium rounded transition-colors ${
                          isWindowOpen(invoice.filename)
                            ? 'text-success-600 bg-success-50 border border-success-200'
                            : 'text-primary-600 bg-primary-50 border border-primary-200 hover:bg-primary-100'
                        }`}
                        title={isWindowOpen(invoice.filename) ? 'Pipeline window is open' : 'Open processing pipeline'}
                      >
                        {isWindowOpen(invoice.filename) ? '✓ Pipeline' : 'Pipeline'}
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center text-gray-500">
              <div className="text-4xl mb-2">📄</div>
              <div className="text-sm">No files uploaded yet</div>
              <div className="text-xs text-gray-400 mt-1">Upload files on the left to see them here</div>
            </div>
          </div>
        )}
      </div>

    </div>
  );
}

export default UploadPage;
