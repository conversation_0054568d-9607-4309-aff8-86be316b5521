import React from 'react';

/**
 * Upload Error Boundary Component
 * Catches and handles errors in upload components gracefully
 *
 * Features:
 * - Error catching and logging
 * - User-friendly error display
 * - Recovery options
 * - Error reporting capabilities
 * - Accessibility compliance
 */
class UploadErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      errorId: `upload-error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    };
  }

  componentDidCatch(error, errorInfo) {
    // Log error details
    console.error('Upload Error Boundary caught an error:', error, errorInfo);

    this.setState({
      error,
      errorInfo
    });

    // Report error to monitoring service (if available)
    this.reportError(error, errorInfo);
  }

  reportError = (error, errorInfo) => {
    try {
      // In a real application, you would send this to your error reporting service
      const errorReport = {
        timestamp: new Date().toISOString(),
        errorId: this.state.errorId,
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        userAgent: navigator.userAgent,
        url: window.location.href,
        component: 'UploadErrorBoundary'
      };

      // Log to console for development
      console.error('Error Report:', errorReport);

      // In production, send to error reporting service
      // Example: errorReportingService.report(errorReport);

    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  };

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    });
  };

  handleReload = () => {
    window.location.reload();
  };

  copyErrorDetails = () => {
    const errorDetails = {
      errorId: this.state.errorId,
      timestamp: new Date().toISOString(),
      message: this.state.error?.message || 'Unknown error',
      stack: this.state.error?.stack || 'No stack trace available',
      componentStack: this.state.errorInfo?.componentStack || 'No component stack available'
    };

    const errorText = JSON.stringify(errorDetails, null, 2);

    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard.writeText(errorText).then(() => {
        alert('Error details copied to clipboard');
      }).catch(() => {
        // Fallback for older browsers
        this.fallbackCopyToClipboard(errorText);
      });
    } else {
      this.fallbackCopyToClipboard(errorText);
    }
  };

  fallbackCopyToClipboard = (text) => {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
      document.execCommand('copy');
      alert('Error details copied to clipboard');
    } catch (err) {
      console.error('Failed to copy error details:', err);
      alert('Failed to copy error details. Please manually copy from console.');
    }

    document.body.removeChild(textArea);
  };

  render() {
    if (this.state.hasError) {
      const { error, errorInfo, errorId } = this.state;
      const { fallback: CustomFallback } = this.props;

      // If a custom fallback component is provided, use it
      if (CustomFallback) {
        return (
          <CustomFallback
            error={error}
            errorInfo={errorInfo}
            errorId={errorId}
            onRetry={this.handleRetry}
            onReload={this.handleReload}
            onCopyError={this.copyErrorDetails}
          />
        );
      }

      // Default error UI
      return (
        <div
          className="min-h-[200px] flex items-center justify-center p-6 bg-red-50 border border-red-200 rounded-lg"
          role="alert"
          aria-labelledby="error-title"
          aria-describedby="error-description"
        >
          <div className="text-center max-w-md">
            {/* Error icon */}
            <div className="mx-auto w-16 h-16 flex items-center justify-center mb-4">
              <span className="text-4xl text-red-500" role="img" aria-label="Error">
                ⚠️
              </span>
            </div>

            {/* Error title */}
            <h2 id="error-title" className="text-lg font-semibold text-red-800 mb-2">
              Upload Component Error
            </h2>

            {/* Error description */}
            <p id="error-description" className="text-sm text-red-700 mb-4">
              Something went wrong with the file upload component. This might be due to a
              browser compatibility issue or a temporary problem.
            </p>

            {/* Error ID for support */}
            {errorId && (
              <p className="text-xs text-red-600 mb-4 font-mono">
                Error ID: {errorId}
              </p>
            )}

            {/* Action buttons */}
            <div className="space-y-2">
              <button
                onClick={this.handleRetry}
                className="w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors"
                aria-label="Retry upload component"
              >
                Try Again
              </button>

              <button
                onClick={this.handleReload}
                className="w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
                aria-label="Reload entire page"
              >
                Reload Page
              </button>

              {/* Developer options (only show in development) */}
              {process.env.NODE_ENV === 'development' && (
                <details className="mt-4 text-left">
                  <summary className="cursor-pointer text-xs text-red-600 hover:text-red-800">
                    Show Error Details (Development)
                  </summary>
                  <div className="mt-2 p-3 bg-red-100 rounded border text-xs">
                    <div className="mb-2">
                      <strong>Error:</strong>
                      <pre className="mt-1 text-red-800 whitespace-pre-wrap font-mono text-xs">
                        {error?.message || 'Unknown error'}
                      </pre>
                    </div>

                    {error?.stack && (
                      <div className="mb-2">
                        <strong>Stack Trace:</strong>
                        <pre className="mt-1 text-red-800 whitespace-pre-wrap font-mono text-xs max-h-32 overflow-y-auto">
                          {error.stack}
                        </pre>
                      </div>
                    )}

                    {errorInfo?.componentStack && (
                      <div className="mb-2">
                        <strong>Component Stack:</strong>
                        <pre className="mt-1 text-red-800 whitespace-pre-wrap font-mono text-xs max-h-32 overflow-y-auto">
                          {errorInfo.componentStack}
                        </pre>
                      </div>
                    )}

                    <button
                      onClick={this.copyErrorDetails}
                      className="mt-2 text-xs bg-red-200 text-red-800 px-2 py-1 rounded hover:bg-red-300"
                    >
                      Copy Error Details
                    </button>
                  </div>
                </details>
              )}
            </div>

            {/* Help text */}
            <p className="mt-4 text-xs text-red-600">
              If this problem persists, please try refreshing the page or contact support
              with the error ID above.
            </p>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default UploadErrorBoundary;
