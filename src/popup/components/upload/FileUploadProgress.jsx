import React from 'react';
import prettyBytes from 'pretty-bytes';

/**
 * File Upload Progress Component
 * Displays upload progress with visual indicators and status messages
 *
 * Features:
 * - Animated progress bar
 * - Stage-specific status messages
 * - File name display
 * - Estimated time remaining
 * - Accessible progress indicators
 */
function FileUploadProgress({
  progress = 0,
  fileName = '',
  stage = 'uploading',
  fileSize = 0,
  estimatedTime = null,
  className = ''
}) {
  // Stage configurations
  const stageConfig = {
    uploading: {
      icon: '📤',
      title: 'Uploading',
      description: 'Transferring file...',
      color: 'blue'
    },
    validating: {
      icon: '🔍',
      title: 'Validating',
      description: 'Checking file format and size...',
      color: 'yellow'
    },
    processing: {
      icon: '⚙️',
      title: 'Processing',
      description: 'Preparing file for analysis...',
      color: 'blue'
    },
    extracting: {
      icon: '📄',
      title: 'Extracting Text',
      description: 'Reading document content...',
      color: 'indigo'
    },
    ocr: {
      icon: '👁️',
      title: 'OCR Processing',
      description: 'Converting image to text...',
      color: 'purple'
    },
    analyzing: {
      icon: '🤖',
      title: 'AI Analysis',
      description: 'Extracting invoice data...',
      color: 'green'
    },
    complete: {
      icon: '✅',
      title: 'Complete',
      description: 'File processed successfully!',
      color: 'green'
    },
    error: {
      icon: '❌',
      title: 'Error',
      description: 'Processing failed',
      color: 'red'
    }
  };

  const currentStage = stageConfig[stage] || stageConfig.uploading;
  const progressPercentage = Math.min(Math.max(progress, 0), 100);

  // Format estimated time
  const formatEstimatedTime = (seconds) => {
    if (!seconds || seconds <= 0) { return ''; }

    if (seconds < 60) {
      return `${Math.round(seconds)}s remaining`;
    }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return `${minutes}m ${remainingSeconds}s remaining`;

  };

  // Get progress bar color classes
  const getProgressBarClasses = () => {
    const colorMap = {
      blue: 'bg-blue-600',
      yellow: 'bg-yellow-500',
      indigo: 'bg-indigo-600',
      purple: 'bg-purple-600',
      green: 'bg-green-600',
      red: 'bg-red-600'
    };

    return colorMap[currentStage.color] || 'bg-blue-600';
  };

  // Get spinner animation classes
  const getSpinnerClasses = () => {
    if (stage === 'complete') {
      return 'text-green-600';
    }
    if (stage === 'error') {
      return 'text-red-600';
    }
    return 'text-blue-600 animate-spin';
  };

  return (
    <div className={`w-full max-w-md mx-auto ${className}`}>
      {/* Progress header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          {/* Stage icon with animation */}
          <div className="flex-shrink-0">
            {stage === 'complete' || stage === 'error' ? (
              <span className="text-2xl" role="img" aria-label={currentStage.title}>
                {currentStage.icon}
              </span>
            ) : (
              <div className="relative">
                <div className={`w-8 h-8 rounded-full border-2 border-current ${getSpinnerClasses()}`}>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-sm" role="img" aria-label={currentStage.title}>
                      {currentStage.icon}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Stage info */}
          <div className="flex-1 min-w-0">
            <h4 className="text-sm font-medium text-gray-900 truncate">
              {currentStage.title}
            </h4>
            <p className="text-xs text-gray-600 truncate">
              {currentStage.description}
            </p>
          </div>
        </div>

        {/* Progress percentage */}
        <div className="flex-shrink-0 text-right">
          <span className="text-sm font-medium text-gray-900">
            {progressPercentage}%
          </span>
          {estimatedTime && (
            <p className="text-xs text-gray-500">
              {formatEstimatedTime(estimatedTime)}
            </p>
          )}
        </div>
      </div>

      {/* Progress bar */}
      <div className="mb-3">
        <div
          className="w-full bg-gray-200 rounded-full h-2 overflow-hidden"
          role="progressbar"
          aria-valuenow={progressPercentage}
          aria-valuemin="0"
          aria-valuemax="100"
          aria-label={`Upload progress: ${progressPercentage}%`}
        >
          <div
            className={`h-full rounded-full transition-all duration-300 ease-out ${getProgressBarClasses()}`}
            style={{
              width: `${progressPercentage}%`,
              transform: progressPercentage === 0 ? 'translateX(-100%)' : 'translateX(0)'
            }}
          />
        </div>
      </div>

      {/* File details */}
      {fileName && (
        <div className="space-y-1">
          <div className="flex items-center justify-between text-xs text-gray-600">
            <span className="truncate flex-1 mr-2" title={fileName}>
              📄 {fileName}
            </span>
            {fileSize > 0 && (
              <span className="flex-shrink-0">
                {prettyBytes(fileSize)}
              </span>
            )}
          </div>
        </div>
      )}

      {/* Stage-specific additional info */}
      {stage === 'ocr' && (
        <div className="mt-2 p-2 bg-purple-50 rounded-md">
          <p className="text-xs text-purple-700">
            💡 OCR processing may take longer for complex images or poor quality scans
          </p>
        </div>
      )}

      {stage === 'analyzing' && (
        <div className="mt-2 p-2 bg-green-50 rounded-md">
          <p className="text-xs text-green-700">
            🤖 AI is analyzing the document structure and extracting invoice data
          </p>
        </div>
      )}

      {stage === 'complete' && (
        <div className="mt-2 p-2 bg-green-50 rounded-md">
          <p className="text-xs text-green-700">
            🎉 File processed successfully! You can now view the extracted data.
          </p>
        </div>
      )}

      {stage === 'error' && (
        <div className="mt-2 p-2 bg-red-50 rounded-md">
          <p className="text-xs text-red-700">
            ⚠️ Processing failed. Please try again or contact support if the issue persists.
          </p>
        </div>
      )}

      {/* Accessibility: Screen reader announcements */}
      <div className="sr-only" aria-live="polite" aria-atomic="true">
        {stage !== 'complete' && stage !== 'error' && (
          `${currentStage.title}: ${progressPercentage}% complete. ${currentStage.description}`
        )}
        {stage === 'complete' && 'File upload and processing completed successfully.'}
        {stage === 'error' && 'File processing failed. Please try again.'}
      </div>
    </div>
  );
}

export default FileUploadProgress;
