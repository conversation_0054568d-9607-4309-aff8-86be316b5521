/**
 * Analytics Page Component
 *
 * Main analytics dashboard page providing comprehensive business intelligence
 * insights, performance metrics, and data visualizations.
 */

import React, { useState, useEffect } from 'react';
import MetricsCards, { DetailedMetrics, QuickStats } from './MetricsCards.jsx';
import AnalyticsCharts from './AnalyticsCharts.jsx';
import analyticsService from '../../../services/AnalyticsService.js';
import { formatDateLabel } from '../../../utils/chartUtils.js';

const AnalyticsPage = () => {
  const [dashboardData, setDashboardData] = useState(null);
  const [chartData, setChartData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(null);

  /**
   * Initialize analytics service and load data
   */
  useEffect(() => {
    initializeAnalytics();
  }, []);

  /**
   * Set up real-time updates
   */
  useEffect(() => {
    const interval = setInterval(() => {
      if (!refreshing) {
        refreshData();
      }
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, [refreshing]);

  /**
   * Initialize analytics service
   */
  const initializeAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔧 AnalyticsPage: Initializing analytics service...');

      const initialized = await analyticsService.initialize();

      if (initialized) {
        await loadAnalyticsData();
        console.log('✅ AnalyticsPage: Analytics initialized successfully');
      } else {
        throw new Error('Failed to initialize analytics service');
      }
    } catch (err) {
      console.error('❌ AnalyticsPage: Initialization failed:', err);
      setError('Failed to initialize analytics. Please try refreshing the page.');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Load analytics data
   */
  const loadAnalyticsData = async () => {
    try {
      console.log('📊 AnalyticsPage: Loading analytics data...');

      // Get dashboard data
      const dashboard = analyticsService.getDashboardData();
      setDashboardData(dashboard);

      // Get chart data
      const charts = analyticsService.getChartData();
      setChartData(charts);

      setLastUpdated(new Date());

      console.log('✅ AnalyticsPage: Analytics data loaded successfully');
      console.log('📊 Dashboard data:', dashboard);
      console.log('📈 Chart data:', charts);

    } catch (err) {
      console.error('❌ AnalyticsPage: Failed to load analytics data:', err);
      setError('Failed to load analytics data. Please try again.');
    }
  };

  /**
   * Refresh analytics data
   */
  const refreshData = async () => {
    try {
      setRefreshing(true);
      await loadAnalyticsData();
    } catch (err) {
      console.error('❌ AnalyticsPage: Failed to refresh data:', err);
    } finally {
      setRefreshing(false);
    }
  };

  /**
   * Handle manual refresh
   */
  const handleRefresh = async () => {
    await refreshData();
  };

  /**
   * Export analytics data
   */
  const handleExport = async () => {
    try {
      console.log('📤 AnalyticsPage: Exporting analytics data...');

      const exportData = await analyticsService.exportData('json');

      // Create download link
      const blob = new Blob([exportData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `mvat-analytics-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      console.log('✅ AnalyticsPage: Analytics data exported successfully');

    } catch (err) {
      console.error('❌ AnalyticsPage: Failed to export data:', err);
      setError('Failed to export analytics data. Please try again.');
    }
  };

  /**
   * Loading state
   */
  if (loading) {
    return (
      <div className="flex-1 p-6 bg-gray-50 min-h-screen">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4" />
              <p className="text-gray-600">Loading analytics dashboard...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  /**
   * Error state
   */
  if (error) {
    return (
      <div className="flex-1 p-6 bg-gray-50 min-h-screen">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="text-red-600 text-xl mb-4">⚠️</div>
              <p className="text-gray-900 font-medium mb-2">Analytics Error</p>
              <p className="text-gray-600 mb-4">{error}</p>
              <button
                onClick={initializeAnalytics}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 p-6 bg-gray-50 min-h-screen" data-testid="analytics-page">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h1>
            <p className="text-gray-600 mt-1">
              Business intelligence and performance insights
            </p>
          </div>

          <div className="flex items-center space-x-4">
            <QuickStats data={dashboardData} />

            <div className="flex items-center space-x-2">
              <button
                onClick={handleRefresh}
                disabled={refreshing}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors flex items-center space-x-2"
                data-testid="refresh-analytics"
              >
                <span className={refreshing ? 'animate-spin' : ''}>🔄</span>
                <span>{refreshing ? 'Refreshing...' : 'Refresh'}</span>
              </button>

              <button
                onClick={handleExport}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
                data-testid="export-analytics"
              >
                <span>📤</span>
                <span>Export</span>
              </button>
            </div>
          </div>
        </div>

        {/* Last updated info */}
        {lastUpdated && (
          <div className="text-sm text-gray-500">
            Last updated: {formatDateLabel(lastUpdated, 'full')} at {lastUpdated.toLocaleTimeString()}
          </div>
        )}

        {/* Key Metrics Cards */}
        <MetricsCards data={dashboardData} data-testid="metrics-cards" />

        {/* Charts Section */}
        <div data-testid="analytics-charts">
          <AnalyticsCharts chartData={chartData} />
        </div>

        {/* Detailed Metrics */}
        <div data-testid="detailed-metrics">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Detailed Metrics</h2>
          <DetailedMetrics data={dashboardData} />
        </div>

        {/* Footer */}
        <div className="text-center text-sm text-gray-500 py-4">
          Analytics data is updated in real-time as documents are processed
        </div>
      </div>
    </div>
  );
};

export default AnalyticsPage;
