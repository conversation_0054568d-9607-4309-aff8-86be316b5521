import React, { useState, useCallback } from 'react';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';

/**
 * ColumnCustomizer Component
 * Provides column visibility toggles and drag-and-drop reordering
 * Epic 3, Story 3.1, Task 3.1.3: Column Customization & Row Selection
 */
function ColumnCustomizer({
  columns,
  visibleColumns,
  onColumnsChange,
  onVisibilityChange,
  className = ''
}) {
  const [isOpen, setIsOpen] = useState(false);

  // Handle drag end for column reordering
  const handleDragEnd = useCallback((result) => {
    if (!result.destination) { return; }

    const items = Array.from(columns);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    onColumnsChange(items);
  }, [columns, onColumnsChange]);

  // Handle column visibility toggle
  const handleVisibilityToggle = useCallback((columnKey) => {
    const newVisibleColumns = new Set(visibleColumns);

    if (newVisibleColumns.has(columnKey)) {
      newVisibleColumns.delete(columnKey);
    } else {
      newVisibleColumns.add(columnKey);
    }

    onVisibilityChange(newVisibleColumns);
  }, [visibleColumns, onVisibilityChange]);

  // Reset to default columns
  const handleReset = useCallback(() => {
    const defaultVisible = new Set(columns.map(col => col.key));
    onVisibilityChange(defaultVisible);
  }, [columns, onVisibilityChange]);

  // Show/hide all columns
  const handleToggleAll = useCallback(() => {
    const allVisible = columns.every(col => visibleColumns.has(col.key));

    if (allVisible) {
      // Hide all except first column (usually filename or id)
      const firstColumn = columns[0];
      onVisibilityChange(new Set([firstColumn.key]));
    } else {
      // Show all columns
      onVisibilityChange(new Set(columns.map(col => col.key)));
    }
  }, [columns, visibleColumns, onVisibilityChange]);

  const visibleCount = visibleColumns.size;
  const totalCount = columns.length;

  return (
    <div className={`relative ${className}`}>
      {/* Toggle Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="btn btn-secondary btn-sm flex items-center space-x-2"
        title="Customize columns"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <span>⚙️</span>
        <span>Columns</span>
        <span className="text-xs bg-gray-200 px-1.5 py-0.5 rounded">
          {visibleCount}/{totalCount}
        </span>
        <span className={`transform transition-transform ${isOpen ? 'rotate-180' : ''}`}>
          ▼
        </span>
      </button>

      {/* Dropdown Panel */}
      {isOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
            aria-hidden="true"
          />

          {/* Panel */}
          <div className="absolute right-0 top-full mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-20">
            {/* Header */}
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-sm font-medium text-gray-900">
                  Customize Columns
                </h3>
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-gray-400 hover:text-gray-600"
                  aria-label="Close"
                >
                  ✕
                </button>
              </div>

              {/* Quick Actions */}
              <div className="flex space-x-2">
                <button
                  onClick={handleToggleAll}
                  className="btn btn-secondary btn-xs"
                >
                  {visibleCount === totalCount ? 'Hide All' : 'Show All'}
                </button>
                <button
                  onClick={handleReset}
                  className="btn btn-secondary btn-xs"
                >
                  Reset
                </button>
              </div>
            </div>

            {/* Column List */}
            <div className="p-4">
              <p className="text-xs text-gray-500 mb-3">
                Drag to reorder • Toggle to show/hide
              </p>

              <DragDropContext onDragEnd={handleDragEnd}>
                <Droppable droppableId="columns">
                  {(provided, snapshot) => (
                    <div
                      {...provided.droppableProps}
                      ref={provided.innerRef}
                      className={`space-y-2 ${
                        snapshot.isDraggingOver ? 'bg-blue-50' : ''
                      }`}
                    >
                      {columns.map((column, index) => (
                        <Draggable
                          key={column.key}
                          draggableId={column.key}
                          index={index}
                        >
                          {(provided, snapshot) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              className={`flex items-center space-x-3 p-2 rounded-md border ${
                                snapshot.isDragging
                                  ? 'bg-blue-100 border-blue-300 shadow-md'
                                  : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                              }`}
                            >
                              {/* Drag Handle */}
                              <div
                                {...provided.dragHandleProps}
                                className="text-gray-400 hover:text-gray-600 cursor-grab active:cursor-grabbing"
                                title="Drag to reorder"
                              >
                                ⋮⋮
                              </div>

                              {/* Visibility Toggle */}
                              <label className="flex items-center space-x-2 flex-1 cursor-pointer">
                                <input
                                  type="checkbox"
                                  checked={visibleColumns.has(column.key)}
                                  onChange={() => handleVisibilityToggle(column.key)}
                                  className="form-checkbox h-4 w-4 text-blue-600"
                                />
                                <div className="flex items-center space-x-2 flex-1">
                                  {column.icon && (
                                    <span className="text-sm">{column.icon}</span>
                                  )}
                                  <span className="text-sm font-medium text-gray-900">
                                    {column.label}
                                  </span>
                                </div>
                              </label>

                              {/* Column Info */}
                              {column.width && (
                                <span className="text-xs text-gray-500">
                                  {column.width}
                                </span>
                              )}
                            </div>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </DragDropContext>
            </div>

            {/* Footer */}
            <div className="p-4 border-t border-gray-200 bg-gray-50 rounded-b-lg">
              <div className="flex items-center justify-between text-xs text-gray-500">
                <span>
                  {visibleCount} of {totalCount} columns visible
                </span>
                <span>
                  Changes saved automatically
                </span>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
}

export default ColumnCustomizer;
