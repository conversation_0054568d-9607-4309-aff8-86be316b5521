import React, { useState } from 'react';

// Import unified loading components
import { InlineLoading } from '../../../components/ui/feedback/LoadingSpinner.jsx';

/**
 * BulkActions Component
 * Provides bulk operations for selected table rows
 * Epic 3, Story 3.1, Task 3.1.3: Column Customization & Row Selection
 */
function BulkActions({
  selectedCount = 0,
  selectedIds = [],
  selectedData = [],
  onBulkDelete,
  onBulkExport,
  onBulkProcess,
  onClearSelection,
  disabled = false,
  className = ''
}) {
  const [isConfirmingDelete, setIsConfirmingDelete] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  // Handle bulk delete with confirmation
  const handleBulkDelete = async () => {
    if (!isConfirmingDelete) {
      setIsConfirmingDelete(true);
      return;
    }

    try {
      setIsProcessing(true);
      await onBulkDelete(selectedIds, selectedData);
      setIsConfirmingDelete(false);
      onClearSelection?.();
    } catch (error) {
      console.error('Bulk delete failed:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle bulk export
  const handleBulkExport = async (format = 'csv') => {
    try {
      setIsProcessing(true);
      await onBulkExport(selectedIds, selectedData, format);
    } catch (error) {
      console.error('Bulk export failed:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle bulk process
  const handleBulkProcess = async () => {
    try {
      setIsProcessing(true);
      await onBulkProcess(selectedIds, selectedData);
    } catch (error) {
      console.error('Bulk process failed:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  // Cancel delete confirmation
  const cancelDelete = () => {
    setIsConfirmingDelete(false);
  };

  if (selectedCount === 0) { return null; }

  return (
    <div className={`bg-blue-50 border border-blue-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-center justify-between">
        {/* Selection Info */}
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full" />
            <span className="text-sm font-medium text-blue-900">
              {selectedCount} item{selectedCount !== 1 ? 's' : ''} selected
            </span>
          </div>

          <button
            onClick={onClearSelection}
            className="text-xs text-blue-600 hover:text-blue-800 underline"
            disabled={disabled || isProcessing}
          >
            Clear selection
          </button>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center space-x-2">
          {/* Export Actions */}
          <div className="relative group">
            <button
              className="btn btn-secondary btn-sm flex items-center space-x-1"
              disabled={disabled || isProcessing}
              title="Export selected items"
            >
              <span>📤</span>
              <span>Export</span>
              <span className="text-xs">▼</span>
            </button>

            {/* Export Dropdown */}
            <div className="absolute right-0 top-full mt-1 w-32 bg-white border border-gray-200 rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
              <button
                onClick={() => handleBulkExport('csv')}
                className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 flex items-center space-x-2"
                disabled={disabled || isProcessing}
              >
                <span>📊</span>
                <span>CSV</span>
              </button>
              <button
                onClick={() => handleBulkExport('json')}
                className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 flex items-center space-x-2"
                disabled={disabled || isProcessing}
              >
                <span>📄</span>
                <span>JSON</span>
              </button>
              <button
                onClick={() => handleBulkExport('excel')}
                className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 flex items-center space-x-2"
                disabled={disabled || isProcessing}
              >
                <span>📈</span>
                <span>Excel</span>
              </button>
            </div>
          </div>

          {/* Process Action */}
          {onBulkProcess && (
            <button
              onClick={handleBulkProcess}
              className="btn btn-primary btn-sm flex items-center space-x-1"
              disabled={disabled || isProcessing}
              title="Process selected items"
            >
              <span>⚡</span>
              <span>Process</span>
            </button>
          )}

          {/* Delete Action */}
          {onBulkDelete && (
            <div className="flex items-center space-x-1">
              {isConfirmingDelete ? (
                <>
                  <button
                    onClick={handleBulkDelete}
                    className="btn btn-danger btn-sm flex items-center space-x-1"
                    disabled={disabled || isProcessing}
                    title={`Confirm delete ${selectedCount} items`}
                  >
                    <span>✓</span>
                    <span>Confirm</span>
                  </button>
                  <button
                    onClick={cancelDelete}
                    className="btn btn-secondary btn-sm"
                    disabled={disabled || isProcessing}
                    title="Cancel delete"
                  >
                    ✕
                  </button>
                </>
              ) : (
                <button
                  onClick={handleBulkDelete}
                  className="btn btn-danger btn-sm flex items-center space-x-1"
                  disabled={disabled || isProcessing}
                  title={`Delete ${selectedCount} selected items`}
                >
                  <span>🗑️</span>
                  <span>Delete</span>
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Confirmation Message */}
      {isConfirmingDelete && (
        <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
          <div className="flex items-start space-x-2">
            <span className="text-red-500 text-sm">⚠️</span>
            <div className="flex-1">
              <p className="text-sm text-red-800 font-medium">
                Delete {selectedCount} selected item{selectedCount !== 1 ? 's' : ''}?
              </p>
              <p className="text-xs text-red-600 mt-1">
                This action cannot be undone.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Processing Indicator */}
      {isProcessing && (
        <div className="mt-3">
          <InlineLoading
            message="Processing..."
            size="small"
            color="blue"
            className="text-sm text-blue-700"
          />
        </div>
      )}
    </div>
  );
}

/**
 * BulkActionBar Component
 * Simplified bulk action bar for table header
 */
export function BulkActionBar({
  selectedCount = 0,
  onBulkExport,
  onBulkDelete,
  onClearSelection,
  className = ''
}) {
  if (selectedCount === 0) { return null; }

  return (
    <div className={`flex items-center justify-between bg-blue-50 px-4 py-2 border-b border-blue-200 ${className}`}>
      <div className="flex items-center space-x-2 text-sm text-blue-900">
        <span className="font-medium">{selectedCount} selected</span>
        <button
          onClick={onClearSelection}
          className="text-blue-600 hover:text-blue-800 underline"
        >
          Clear
        </button>
      </div>

      <div className="flex items-center space-x-2">
        {onBulkExport && (
          <button
            onClick={() => onBulkExport('csv')}
            className="btn btn-secondary btn-xs"
            title="Export selected"
          >
            📤 Export
          </button>
        )}
        {onBulkDelete && (
          <button
            onClick={onBulkDelete}
            className="btn btn-danger btn-xs"
            title="Delete selected"
          >
            🗑️ Delete
          </button>
        )}
      </div>
    </div>
  );
}

export default BulkActions;
