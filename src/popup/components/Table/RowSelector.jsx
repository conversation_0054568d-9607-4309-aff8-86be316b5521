import React from 'react';

/**
 * RowSelector Component
 * Provides checkbox-based row selection functionality
 * Epic 3, Story 3.1, Task 3.1.3: Column Customization & Row Selection
 */
function RowSelector({
  isSelected = false,
  isIndeterminate = false,
  onChange,
  disabled = false,
  ariaLabel,
  className = ''
}) {
  return (
    <div className={`flex items-center justify-center ${className}`}>
      <label className="relative flex items-center cursor-pointer">
        <input
          type="checkbox"
          checked={isSelected}
          ref={(input) => {
            if (input) {
              input.indeterminate = isIndeterminate;
            }
          }}
          onChange={onChange}
          disabled={disabled}
          className={`
            form-checkbox h-4 w-4 text-blue-600 border-gray-300 rounded
            focus:ring-blue-500 focus:ring-2 focus:ring-offset-2
            disabled:opacity-50 disabled:cursor-not-allowed
            ${isIndeterminate ? 'indeterminate' : ''}
          `}
          aria-label={ariaLabel}
        />
        <span className="sr-only">{ariaLabel}</span>
      </label>
    </div>
  );
}

/**
 * SelectAllCheckbox Component
 * Special checkbox for selecting all rows with indeterminate state
 */
export function SelectAllCheckbox({
  allSelected = false,
  someSelected = false,
  onSelectAll,
  totalCount = 0,
  selectedCount = 0,
  disabled = false,
  className = ''
}) {
  const isIndeterminate = someSelected && !allSelected;

  const handleChange = (e) => {
    onSelectAll(e.target.checked);
  };

  const getAriaLabel = () => {
    if (allSelected) {
      return `Deselect all ${totalCount} rows`;
    } else if (someSelected) {
      return `Select all ${totalCount} rows (${selectedCount} currently selected)`;
    }
    return `Select all ${totalCount} rows`;

  };

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <label className="relative flex items-center cursor-pointer group">
        <input
          type="checkbox"
          checked={allSelected}
          ref={(input) => {
            if (input) {
              input.indeterminate = isIndeterminate;
            }
          }}
          onChange={handleChange}
          disabled={disabled || totalCount === 0}
          className={`
            form-checkbox h-4 w-4 text-blue-600 border-gray-300 rounded
            focus:ring-blue-500 focus:ring-2 focus:ring-offset-2
            disabled:opacity-50 disabled:cursor-not-allowed
            group-hover:border-blue-400
            ${isIndeterminate ? 'indeterminate' : ''}
          `}
          aria-label={getAriaLabel()}
          title={getAriaLabel()}
        />
        <span className="sr-only">{getAriaLabel()}</span>
      </label>
    </div>
  );
}

/**
 * RowCheckbox Component
 * Individual row selection checkbox
 */
export function RowCheckbox({
  rowId,
  isSelected = false,
  onRowSelect,
  rowData,
  disabled = false,
  className = ''
}) {
  const handleChange = (e) => {
    // Pass the original event to preserve modifier keys for multi-select
    onRowSelect(rowId, e.nativeEvent);
  };

  const getAriaLabel = () => {
    const identifier = rowData?.filename || rowData?.number || rowData?.id || 'row';
    return isSelected ? `Deselect ${identifier}` : `Select ${identifier}`;
  };

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <label className="relative flex items-center cursor-pointer group">
        <input
          type="checkbox"
          checked={isSelected}
          onChange={handleChange}
          disabled={disabled}
          className={`
            form-checkbox h-4 w-4 text-blue-600 border-gray-300 rounded
            focus:ring-blue-500 focus:ring-2 focus:ring-offset-2
            disabled:opacity-50 disabled:cursor-not-allowed
            group-hover:border-blue-400
          `}
          aria-label={getAriaLabel()}
          title={getAriaLabel()}
        />
        <span className="sr-only">{getAriaLabel()}</span>
      </label>
    </div>
  );
}

/**
 * SelectionIndicator Component
 * Shows selection status and count
 */
export function SelectionIndicator({
  selectedCount = 0,
  totalCount = 0,
  onClearSelection,
  className = ''
}) {
  if (selectedCount === 0) { return null; }

  return (
    <div className={`flex items-center space-x-2 text-sm ${className}`}>
      <div className="flex items-center space-x-1">
        <span className="w-2 h-2 bg-blue-500 rounded-full" />
        <span className="font-medium text-blue-700">
          {selectedCount} of {totalCount} selected
        </span>
      </div>

      {onClearSelection && (
        <button
          onClick={onClearSelection}
          className="text-blue-600 hover:text-blue-800 underline"
          title="Clear selection"
        >
          Clear
        </button>
      )}
    </div>
  );
}

export default RowSelector;
