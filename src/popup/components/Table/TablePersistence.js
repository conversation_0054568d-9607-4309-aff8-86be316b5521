/**
 * TablePersistence Utility
 * Handles saving and loading table state to/from localStorage
 * Epic 3, Story 3.1, Task 3.1.3: Column Customization & Row Selection
 */

const DEFAULT_PERSISTENCE_KEY = 'mvat-table-persistence';

/**
 * Save table state to localStorage
 * @param {string} key - Storage key
 * @param {Object} state - State object to save
 */
export function saveTableState(key = DEFAULT_PERSISTENCE_KEY, state) {
  if (typeof localStorage === 'undefined') {
    console.warn('localStorage not available');
    return false;
  }

  try {
    const serializedState = JSON.stringify({
      ...state,
      timestamp: Date.now(),
      version: '1.0'
    });

    localStorage.setItem(key, serializedState);
    return true;
  } catch (error) {
    console.error('Failed to save table state:', error);
    return false;
  }
}

/**
 * Load table state from localStorage
 * @param {string} key - Storage key
 * @returns {Object|null} Loaded state or null if not found/invalid
 */
export function loadTableState(key = DEFAULT_PERSISTENCE_KEY) {
  if (typeof localStorage === 'undefined') {
    console.warn('localStorage not available');
    return null;
  }

  try {
    const serializedState = localStorage.getItem(key);
    if (!serializedState) { return null; }

    const state = JSON.parse(serializedState);

    // Validate state structure
    if (!state || typeof state !== 'object') {
      console.warn('Invalid table state format');
      return null;
    }

    // Check if state is too old (older than 30 days)
    const maxAge = 30 * 24 * 60 * 60 * 1000; // 30 days in milliseconds
    if (state.timestamp && (Date.now() - state.timestamp) > maxAge) {
      console.info('Table state expired, clearing');
      clearTableState(key);
      return null;
    }

    return state;
  } catch (error) {
    console.error('Failed to load table state:', error);
    return null;
  }
}

/**
 * Clear table state from localStorage
 * @param {string} key - Storage key
 */
export function clearTableState(key = DEFAULT_PERSISTENCE_KEY) {
  if (typeof localStorage === 'undefined') {
    console.warn('localStorage not available');
    return false;
  }

  try {
    localStorage.removeItem(key);
    return true;
  } catch (error) {
    console.error('Failed to clear table state:', error);
    return false;
  }
}

/**
 * Save column configuration
 * @param {Array} columns - Column configuration array
 * @param {Set} visibleColumns - Set of visible column keys
 * @param {string} key - Storage key
 */
export function saveColumnConfig(columns, visibleColumns, key = `${DEFAULT_PERSISTENCE_KEY}-columns`) {
  const config = {
    columns: columns.map(col => ({
      key: col.key,
      label: col.label,
      width: col.width,
      sortable: col.sortable
    })),
    visibleColumns: Array.from(visibleColumns),
    timestamp: Date.now()
  };

  return saveTableState(key, config);
}

/**
 * Load column configuration
 * @param {string} key - Storage key
 * @returns {Object|null} Column configuration or null
 */
export function loadColumnConfig(key = `${DEFAULT_PERSISTENCE_KEY}-columns`) {
  const config = loadTableState(key);

  if (!config || !config.columns || !config.visibleColumns) {
    return null;
  }

  return {
    columns: config.columns,
    visibleColumns: new Set(config.visibleColumns)
  };
}

/**
 * Save table view preferences
 * @param {Object} preferences - View preferences object
 * @param {string} key - Storage key
 */
export function saveViewPreferences(preferences, key = `${DEFAULT_PERSISTENCE_KEY}-view`) {
  const config = {
    ...preferences,
    timestamp: Date.now()
  };

  return saveTableState(key, config);
}

/**
 * Load table view preferences
 * @param {string} key - Storage key
 * @returns {Object|null} View preferences or null
 */
export function loadViewPreferences(key = `${DEFAULT_PERSISTENCE_KEY}-view`) {
  return loadTableState(key);
}

/**
 * Get storage usage information
 * @returns {Object} Storage usage stats
 */
export function getStorageInfo() {
  if (typeof localStorage === 'undefined') {
    return { available: false };
  }

  try {
    const keys = Object.keys(localStorage).filter(key =>
      key.startsWith(DEFAULT_PERSISTENCE_KEY)
    );

    let totalSize = 0;
    const items = keys.map(key => {
      const value = localStorage.getItem(key);
      const size = new Blob([value]).size;
      totalSize += size;

      return {
        key,
        size,
        sizeFormatted: formatBytes(size)
      };
    });

    return {
      available: true,
      itemCount: keys.length,
      totalSize,
      totalSizeFormatted: formatBytes(totalSize),
      items
    };
  } catch (error) {
    console.error('Failed to get storage info:', error);
    return { available: false, error: error.message };
  }
}

/**
 * Clear all table-related storage
 */
export function clearAllTableStorage() {
  if (typeof localStorage === 'undefined') {
    return false;
  }

  try {
    const keys = Object.keys(localStorage).filter(key =>
      key.startsWith(DEFAULT_PERSISTENCE_KEY)
    );

    keys.forEach(key => localStorage.removeItem(key));

    console.info(`Cleared ${keys.length} table storage items`);
    return true;
  } catch (error) {
    console.error('Failed to clear table storage:', error);
    return false;
  }
}

/**
 * Format bytes to human readable string
 * @param {number} bytes - Number of bytes
 * @returns {string} Formatted string
 */
function formatBytes(bytes) {
  if (bytes === 0) { return '0 Bytes'; }

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Hook for using table persistence
 * @param {string} key - Storage key
 * @returns {Object} Persistence utilities
 */
export function useTablePersistence(key = DEFAULT_PERSISTENCE_KEY) {
  const save = (state) => saveTableState(key, state);
  const load = () => loadTableState(key);
  const clear = () => clearTableState(key);

  return { save, load, clear };
}

export default {
  saveTableState,
  loadTableState,
  clearTableState,
  saveColumnConfig,
  loadColumnConfig,
  saveViewPreferences,
  loadViewPreferences,
  getStorageInfo,
  clearAllTableStorage,
  useTablePersistence
};
