import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

// Import unified loading components
import { InlineLoading } from '../../../components/ui/feedback/LoadingSpinner.jsx';

// Get version from environment variable (injected by Vite build process)
const getAppVersion = () => {
  // Try multiple sources for version
  if (import.meta.env.APP_VERSION) {
    return import.meta.env.APP_VERSION;
  }
  if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.getManifest) {
    return chrome.runtime.getManifest().version;
  }
  return '1.0.0'; // fallback
};

function MainLayout({ children, context }) {
  const location = useLocation();
  const navigate = useNavigate();

  const navigation = [
    {
      name: 'Upload',
      path: '/upload',
      icon: '📄',
      description: 'Upload and process invoices'
    },
    {
      name: 'Table',
      path: '/table',
      icon: '📊',
      description: 'View processed invoices'
    },
    {
      name: 'Analytics',
      path: '/analytics',
      icon: '📈',
      description: 'Business intelligence and performance insights'
    },
    {
      name: 'Settings',
      path: '/settings',
      icon: '⚙️',
      description: 'Configure extension settings'
    }
  ];

  const currentPath = location.pathname;

  return (
    <div className="mvat-app popup-container flex flex-col h-screen bg-gray-50">
      {/* Header */}
      <header className="mvat-header popup-header bg-white shadow-sm px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="text-xl">🧾</div>
            <div>
              <h1 className="text-lg font-semibold text-gray-900">MVAT</h1>
              <p className="text-xs text-gray-500">Multi-VAT Processor</p>
            </div>
          </div>

          {/* Status indicator */}
          <div className="flex items-center space-x-2">
            {context.isProcessing && (
              <InlineLoading
                message="Processing..."
                size="small"
                color="blue"
                className="text-xs"
              />
            )}

            <div className="w-2 h-2 bg-green-500 rounded-full" title="Extension active" />
          </div>
        </div>
      </header>

      {/* Navigation */}
      <nav className="bg-white border-b border-gray-200 px-4 py-2">
        <div className="flex space-x-1">
          {navigation.map((item) => {
            const isActive = currentPath === item.path;
            return (
              <button
                key={item.path}
                onClick={() => navigate(item.path)}
                className={`nav-tab ${isActive ? 'nav-tab-active' : 'nav-tab-inactive'}`}
                title={item.description}
              >
                <span className="mr-1">{item.icon}</span>
                {item.name}
              </button>
            );
          })}
        </div>
      </nav>

      {/* Main content */}
      <main className="flex-1 overflow-hidden">
        <div className="popup-content h-full">
          {children}
        </div>
      </main>

      {/* Footer */}
      <footer className="popup-footer bg-white border-t border-gray-200 px-4 py-3 min-h-[48px] flex-shrink-0">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <div className="flex items-center space-x-4">
            <span>Invoices: {context.invoices?.length || 0}</span>
            {context.settings?.company?.name && (
              <span>Company: {context.settings.company.name}</span>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <span>v{getAppVersion()}</span>
            <div className="w-2 h-2 bg-green-500 rounded-full" title="Pipeline ready" />
          </div>
        </div>
      </footer>
    </div>
  );
}

export default MainLayout;
