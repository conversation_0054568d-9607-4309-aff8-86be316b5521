/**
 * DocumentProcessingService - Simplified document processing for popup
 * Integrates PDF.js and Tesseract.js for real document processing
 */

import * as pdfjsLib from 'pdfjs-dist';
import { sandboxCommunicationService } from '../../services/SandboxCommunicationService.js';
import { enhancedDeepSeekAnalysis } from '../../services/EnhancedDeepSeekAnalysis.js';
import { DocumentRelationshipService } from '../../services/DocumentRelationshipService.js';
import { processingLogger } from '../../utils/ProcessingLogger.js';
import { consolidatedFileValidationService } from '../../services/ConsolidatedFileValidationService.js';
import { DocumentProcessingPipeline } from '../../services/DocumentProcessingPipeline.js';

// Configure PDF.js worker for Chrome extension
// Use local worker file to comply with CSP restrictions
if (typeof chrome !== 'undefined' && chrome.runtime) {
  pdfjsLib.GlobalWorkerOptions.workerSrc = chrome.runtime.getURL('assets/pdf.worker.min.js');
}

class DocumentProcessingService {
  constructor() {
    this.initialized = false;
    this.sandboxService = sandboxCommunicationService;
    this.relationshipService = new DocumentRelationshipService();
    this.pipeline = new DocumentProcessingPipeline();
  }

  /**
   * Initialize the service
   */
  async initialize() {
    if (this.initialized) { return; }

    try {
      // Initialize sandbox communication service
      await this.sandboxService.initialize();

      // Initialize Tesseract in sandbox with default language
      await this.sandboxService.initializeTesseract('pol+eng');

      this.initialized = true;
      console.log('DocumentProcessingService initialized with sandbox');
    } catch (error) {
      console.error('Failed to initialize DocumentProcessingService:', error);
      throw error;
    }
  }

  /**
   * Process a document file using multi-step pipeline
   * @param {File} file - The file to process
   * @param {Function} progressCallback - Progress callback function
   * @param {Object} options - Processing options
   * @returns {Promise<Object>} - Processing result
   */
  async processDocument(file, progressCallback = null, options = {}) {
    try {
      await this.initialize();

      const {
        apiKey = null,
        enableAI = true,
        language = 'pol',
        template = 'standard',
        companyInfo = null
      } = options;

      console.log('🚀 Using multi-step document processing pipeline...');

      // Process through the new multi-step pipeline
      const pipelineResult = await this.pipeline.processDocument(file, {
        progressCallback,
        apiKey,
        language,
        companyInfo,
        enableAI
      });

      if (pipelineResult.success) {
        console.log(`✅ Multi-step pipeline completed with ${pipelineResult.finalResult?.accuracyScore || 0}% accuracy`);

        // Convert pipeline result to expected format
        const finalData = {
          id: `INV-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
          filename: file.name,
          fileSize: file.size,
          fileType: file.type,
          processedAt: new Date().toISOString(),
          extractionMethod: 'multi_step_pipeline',
          uploadId: pipelineResult.uploadId,
          processingTimeMs: pipelineResult.processingTimeMs,

          // Extract data from pipeline result
          ...this.extractInvoiceDataFromPipeline(pipelineResult.finalResult),

          // Additional pipeline metadata
          pipelineSteps: pipelineResult.stepResults,
          stepTimings: pipelineResult.stepTimings,
          accuracyScore: pipelineResult.finalResult?.accuracyScore || 0,
          ai_enhanced: true
        };

        return {
          success: true,
          data: finalData
        };
      }
      throw new Error(pipelineResult.error || 'Multi-step pipeline failed');


    } catch (error) {
      console.error('❌ Multi-step pipeline processing failed:', error);

      // Fallback to legacy processing
      console.log('⚠️ Falling back to legacy processing method...');
      return await this.processDocumentLegacy(file, progressCallback, options);
    }
  }

  /**
   * Extract invoice data from pipeline result
   * @param {Object} pipelineResult - Pipeline processing result
   * @returns {Object} - Invoice data
   */
  extractInvoiceDataFromPipeline(pipelineResult) {
    if (!pipelineResult) {
      return this.getDefaultInvoiceData();
    }

    // Try to extract from various pipeline steps
    const extractedFields = pipelineResult.extractedFields || {};
    const deepseekData = pipelineResult.deepseekAnalysis || {};
    const mappedFields = pipelineResult.mappedFields || {};

    return {
      number: extractedFields.invoice_number ||
              deepseekData.invoice_number ||
              mappedFields.invoice_number ||
              `FV/${new Date().getFullYear()}/${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,

      date: extractedFields.invoice_date ||
            deepseekData.invoice_date ||
            mappedFields.invoice_date ||
            new Date().toISOString().split('T')[0],

      seller_name: extractedFields.seller_name ||
                   deepseekData.seller_name ||
                   mappedFields.seller_name ||
                   'Unknown Seller',

      buyer_name: extractedFields.buyer_name ||
                  deepseekData.buyer_name ||
                  mappedFields.buyer_name ||
                  'Unknown Buyer',

      total_net: this.parseAmount(extractedFields.total_net || deepseekData.total_net || mappedFields.total_net),
      total_vat: this.parseAmount(extractedFields.total_vat || deepseekData.total_vat || mappedFields.total_vat),
      total_gross: this.parseAmount(extractedFields.total_gross || deepseekData.total_gross || mappedFields.total_gross),

      currency: extractedFields.currency ||
                deepseekData.currency ||
                mappedFields.currency ||
                'PLN',

      status: 'processed',
      document_type: pipelineResult.documentType || 'invoice',
      confidence_score: pipelineResult.confidence || 0
    };
  }

  /**
   * Parse amount from string or number
   * @param {*} amount - Amount to parse
   * @returns {number} - Parsed amount
   */
  parseAmount(amount) {
    if (typeof amount === 'number') { return amount; }
    if (typeof amount === 'string') {
      const parsed = parseFloat(amount.replace(/[^\d.,]/g, '').replace(',', '.'));
      return isNaN(parsed) ? 0 : parsed;
    }
    return 0;
  }

  /**
   * Get default invoice data
   * @returns {Object} - Default invoice data
   */
  getDefaultInvoiceData() {
    return {
      number: `FV/${new Date().getFullYear()}/${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
      date: new Date().toISOString().split('T')[0],
      seller_name: 'Unknown Seller',
      buyer_name: 'Unknown Buyer',
      total_net: 0,
      total_vat: 0,
      total_gross: 0,
      currency: 'PLN',
      status: 'processed',
      document_type: 'invoice',
      confidence_score: 0
    };
  }

  /**
   * Legacy document processing method (fallback)
   * @param {File} file - The file to process
   * @param {Function} progressCallback - Progress callback function
   * @param {Object} options - Processing options
   * @returns {Promise<Object>} - Processing result
   */
  async processDocumentLegacy(file, progressCallback = null, options = {}) {
    const uploadId = processingLogger.generateUploadId();
    const startTime = performance.now();

    try {
      const {
        apiKey = null,
        enableAI = true,
        language = 'pol',
        template = 'standard',
        companyInfo = null
      } = options;

      // Log processing start
      processingLogger.logProcessingStart(uploadId, file.name, file.type, file.size);

      if (progressCallback) { progressCallback({ stage: 'starting', progress: 0 }); }

      // Validate file
      const validation = await this.validateFile(file);
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '));
      }

      if (progressCallback) { progressCallback({ stage: 'extracting', progress: 20 }); }

      let extractedText = '';
      let method = 'unknown';

      if (file.type === 'application/pdf') {
        // Process PDF file
        const result = await this.processPDF(file, progressCallback);
        extractedText = result.text;
        method = result.method;
      } else if (file.type.startsWith('image/')) {
        // Process image file
        const result = await this.processImage(file, progressCallback);
        extractedText = result.text;
        method = result.method || 'ocr';
      } else {
        throw new Error(`Unsupported file type: ${file.type}`);
      }

      if (progressCallback) { progressCallback({ stage: 'analyzing', progress: 60 }); }

      let invoiceData = {};

      // Use basic extraction
      console.log('📝 Using basic invoice extraction (legacy fallback)...');
      invoiceData = this.extractInvoiceData(extractedText);

      if (progressCallback) { progressCallback({ stage: 'complete', progress: 100 }); }

      const totalTime = performance.now() - startTime;
      const finalData = {
        id: `INV-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        filename: file.name,
        fileSize: file.size,
        fileType: file.type,
        processedAt: new Date().toISOString(),
        extractionMethod: method,
        extractedText,
        uploadId,
        processingTimeMs: totalTime,
        ...invoiceData
      };

      // Log processing completion
      processingLogger.logProcessingComplete(uploadId, finalData, totalTime);

      return {
        success: true,
        data: finalData
      };
    } catch (error) {
      console.error('Legacy document processing failed:', error);
      processingLogger.logProcessingError(uploadId, 'document_processing', error);
      return {
        success: false,
        error: error.message,
        data: null,
        uploadId
      };
    }
  }

  /**
   * Process PDF file using basic PDF.js
   * @param {File} file - PDF file
   * @param {Function} progressCallback - Progress callback
   * @returns {Promise<Object>} - Extraction result
   */
  async processPDF(file, progressCallback = null) {
    try {
      if (progressCallback) { progressCallback({ stage: 'pdf_loading', progress: 30 }); }

      const arrayBuffer = await file.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);

      // Load PDF document
      const loadingTask = pdfjsLib.getDocument({ data: uint8Array });
      const pdf = await loadingTask.promise;

      if (progressCallback) { progressCallback({ stage: 'pdf_extracting', progress: 50 }); }

      let fullText = '';
      const numPages = pdf.numPages;

      // Extract text from all pages
      for (let pageNum = 1; pageNum <= Math.min(numPages, 5); pageNum++) { // Limit to first 5 pages
        const page = await pdf.getPage(pageNum);
        const textContent = await page.getTextContent();

        const pageText = textContent.items
          .map(item => item.str)
          .join(' ');

        fullText += pageText + '\n';

        // Clean up page
        page.cleanup();

        if (progressCallback) {
          const progress = 50 + ((pageNum / Math.min(numPages, 5)) * 20);
          progressCallback({ stage: 'pdf_extracting', progress });
        }
      }

      // Check if we got meaningful text
      if (fullText.trim().length < 50 || !this.containsInvoiceKeywords(fullText)) {
        console.log('PDF text extraction insufficient, trying OCR fallback...');

        if (progressCallback) { progressCallback({ stage: 'ocr_fallback', progress: 70 }); }

        const ocrResult = await this.performOCRFallback(file, progressCallback);
        if (ocrResult && ocrResult.text.length > fullText.length) {
          return { text: ocrResult.text, method: 'pdf_ocr' };
        }
      }

      return {
        text: fullText.trim(),
        method: 'pdf_text'
      };
    } catch (error) {
      console.error('PDF processing failed:', error);
      throw new Error(`PDF processing failed: ${error.message}`);
    }
  }

  /**
   * Perform OCR fallback for PDF files
   * @param {File} file - PDF file
   * @param {Function} progressCallback - Progress callback
   * @returns {Promise<Object>} - OCR result
   */
  async performOCRFallback(file, progressCallback = null) {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);

      // Load PDF document for OCR
      const loadingTask = pdfjsLib.getDocument({ data: uint8Array });
      const pdf = await loadingTask.promise;

      // Render first page to canvas for OCR
      const page = await pdf.getPage(1);
      const viewport = page.getViewport({ scale: 2.0 });

      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d');
      canvas.height = viewport.height;
      canvas.width = viewport.width;

      await page.render({ canvasContext: context, viewport }).promise;

      // Convert canvas to data URL for sandbox processing
      const imageData = this.sandboxService.canvasToDataURL(canvas);

      // Perform OCR in sandbox
      const ocrResult = await this.sandboxService.processOCR(imageData, {
        language: 'pol+eng',
        confidence: 60
      });

      // Clean up
      page.cleanup();

      return { text: ocrResult.text, method: 'pdf_ocr' };
    } catch (error) {
      console.error('OCR fallback failed:', error);
      return null;
    }
  }

  /**
   * Process image file using sandbox OCR processing
   * @param {File} file - Image file
   * @param {Function} progressCallback - Progress callback
   * @returns {Promise<Object>} - Extraction result
   */
  async processImage(file, progressCallback = null) {
    try {
      console.log(`🔍 Processing image file: ${file.name} (${file.type})`);

      if (progressCallback) { progressCallback({ stage: 'ocr_sandbox', progress: 20 }); }

      // Convert file to data URL for sandbox processing
      const imageData = await this.sandboxService.fileToDataURL(file);

      if (progressCallback) { progressCallback({ stage: 'ocr_sandbox', progress: 40 }); }

      // Perform OCR in sandbox
      const result = await this.sandboxService.processOCR(imageData, {
        language: 'pol+eng',
        confidence: 60,
        preprocessImage: true
      });

      if (progressCallback) { progressCallback({ stage: 'ocr_sandbox', progress: 80 }); }

      console.log(`✅ OCR processing successful: ${result.text.length} characters extracted`);
      console.log(`📊 Confidence: ${result.confidence}%`);

      return {
        text: result.text,
        method: 'ocr_sandbox',
        confidence: result.confidence,
        language: result.language
      };
    } catch (error) {
      console.error('❌ Sandbox image processing failed:', error);
      throw new Error(`Image processing failed: ${error.message}`);
    }
  }

  /**
   * Legacy image processing method (fallback)
   * @param {File} file - Image file
   * @param {Function} progressCallback - Progress callback
   * @returns {Promise<Object>} - Extraction result
   */
  async processImageLegacy(file, progressCallback = null) {
    try {
      if (progressCallback) { progressCallback({ stage: 'ocr_sandbox', progress: 40 }); }

      // Convert file to data URL for sandbox processing
      const imageData = await this.sandboxService.fileToDataURL(file);

      // Perform OCR in sandbox
      const result = await this.sandboxService.processOCR(imageData, {
        language: 'pol+eng',
        confidence: 60
      });

      if (progressCallback) { progressCallback({ stage: 'ocr_sandbox', progress: 80 }); }

      return {
        text: result.text,
        method: 'ocr_sandbox',
        confidence: result.confidence || 0
      };
    } catch (error) {
      console.error('Sandbox image processing failed:', error);
      throw new Error(`Sandbox image processing failed: ${error.message}`);
    }
  }

  /**
   * Map enhanced analysis to invoice data format
   * @param {Object} enhancedAnalysis - Enhanced analysis result
   * @returns {Object} - Invoice data
   */
  mapEnhancedAnalysisToInvoiceData(enhancedAnalysis) {
    const { metadata, classification, businessIntelligence } = enhancedAnalysis;

    // Extract transaction patterns from metadata
    const transactionPatterns = metadata.transactionPatterns || {};
    const amounts = transactionPatterns.amounts || {};

    // Extract company relationships
    const companyRelationships = metadata.companyRelationships || {};

    return {
      number: this.extractValueFromMetadata(metadata, 'invoiceNumber') ||
              `FV/${new Date().getFullYear()}/${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
      date: this.extractValueFromMetadata(metadata, 'invoiceDate') ||
            new Date().toISOString().split('T')[0],
      seller_name: companyRelationships.vendor || 'Unknown Seller',
      buyer_name: companyRelationships.customer || 'Unknown Buyer',
      total_net: amounts.net || 0,
      total_vat: amounts.tax || 0,
      total_gross: amounts.gross || 0,
      currency: transactionPatterns.currency || 'PLN',
      status: 'processed',
      document_type: classification.documentType || 'unknown',
      confidence_score: enhancedAnalysis.confidenceScore?.overall || 0,
      business_context: classification.businessContext || 'unknown',
      industry: classification.industry || 'unknown',
      language: classification.language || 'unknown',
      ai_enhanced: true
    };
  }

  /**
   * Extract specific value from metadata
   * @param {Object} metadata - Metadata object
   * @param {string} key - Key to extract
   * @returns {*} - Extracted value
   */
  extractValueFromMetadata(metadata, key) {
    // Try to find the value in various metadata sections
    if (metadata[key]) { return metadata[key]; }

    // Check in nested objects
    for (const section of Object.values(metadata)) {
      if (typeof section === 'object' && section[key]) {
        return section[key];
      }
    }

    return null;
  }

  /**
   * Extract invoice data from text
   * @param {string} text - Extracted text
   * @returns {Object} - Invoice data
   */
  extractInvoiceData(text) {
    const data = {
      number: this.extractInvoiceNumber(text),
      date: this.extractDate(text),
      seller_name: this.extractSellerName(text),
      buyer_name: this.extractBuyerName(text),
      total_net: this.extractAmount(text, 'net'),
      total_vat: this.extractAmount(text, 'vat'),
      total_gross: this.extractAmount(text, 'gross'),
      currency: this.extractCurrency(text),
      status: 'processed',
      ai_enhanced: false
    };

    return data;
  }

  /**
   * Extract invoice number from text
   * @param {string} text - Text to search
   * @returns {string} - Invoice number
   */
  extractInvoiceNumber(text) {
    const patterns = [
      /(?:nr|no\.?)\s*:?\s*([A-Z0-9\/\-]+)/i,
      /([0-9]+\/[A-Z]+\/[0-9]+\/[0-9]+\/[A-Z]+)/i,
      /([A-Z]{2,3}\/\d{4}\/\d+)/i,
      /(\d{4}\/\d+\/[A-Z]+)/i,
      /(\d+\/[A-Z]\/\d+\/\d+\/[A-Z]+)/i
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match && match[1] && match[1] !== 'VAT' && match[1].length > 3) {
        return match[1];
      }
    }

    return `FV/${new Date().getFullYear()}/${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`;
  }

  /**
   * Extract date from text
   * @param {string} text - Text to search
   * @returns {string} - Date in YYYY-MM-DD format
   */
  extractDate(text) {
    const patterns = [
      /(\d{4}-\d{2}-\d{2})/,
      /(\d{2}[-\.\/]\d{2}[-\.\/]\d{4})/,
      /(\d{2}[-\.\/]\d{2}[-\.\/]\d{2})/
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match) {
        const dateStr = match[1];
        try {
          const date = new Date(dateStr);
          if (!isNaN(date.getTime())) {
            return date.toISOString().split('T')[0];
          }
        } catch (e) {
          // Continue to next pattern
        }
      }
    }

    return new Date().toISOString().split('T')[0];
  }

  /**
   * Extract seller name from text
   * @param {string} text - Text to search
   * @returns {string} - Seller name
   */
  extractSellerName(text) {
    const patterns = [
      /(?:sprzedawca|seller|from):\s*([^\n\r]+)/i,
      /(?:wystawca|issued by):\s*([^\n\r]+)/i
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match) { return match[1].trim(); }
    }

    return 'Unknown Seller';
  }

  /**
   * Extract buyer name from text
   * @param {string} text - Text to search
   * @returns {string} - Buyer name
   */
  extractBuyerName(text) {
    const patterns = [
      /(?:nabywca|buyer|to):\s*([^\n\r]+)/i,
      /(?:klient|client):\s*([^\n\r]+)/i
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match) { return match[1].trim(); }
    }

    return 'Unknown Buyer';
  }

  /**
   * Extract amount from text
   * @param {string} text - Text to search
   * @param {string} type - Amount type (net, vat, gross)
   * @returns {number} - Amount
   */
  extractAmount(text, type) {
    const patterns = {
      net: [
        /(?:razem\s+netto|total\s+net)[:\s]*([0-9,.\s]+)\s*PLN/i,
        /(?:netto|net)[:\s]*([0-9,.\s]+)\s*PLN/i,
        /([0-9,.\s]+)\s*PLN[^0-9]*netto/i
      ],
      vat: [
        /(?:VAT\s*23%)[:\s]*([0-9,.\s]+)\s*PLN/i,
        /(?:vat|podatek)[:\s]*([0-9,.\s]+)\s*PLN/i,
        /([0-9,.\s]+)\s*PLN[^0-9]*VAT/i
      ],
      gross: [
        /(?:razem\s+brutto|total\s+gross|do\s+zapłaty)[:\s]*([0-9,.\s]+)\s*PLN/i,
        /(?:brutto|gross)[:\s]*([0-9,.\s]+)\s*PLN/i,
        /([0-9,.\s]+)\s*PLN[^0-9]*$/i
      ]
    };

    const typePatterns = patterns[type] || patterns.gross;

    for (const pattern of typePatterns) {
      const match = text.match(pattern);
      if (match) {
        // Handle Polish number formatting: 1,234.56 or 1 234,56
        let amountStr = match[1].trim();

        // Remove spaces used as thousand separators
        amountStr = amountStr.replace(/\s+/g, '');

        // Handle Polish decimal comma vs dot
        if (amountStr.includes(',') && amountStr.includes('.')) {
          // Both comma and dot - assume comma is thousands separator
          amountStr = amountStr.replace(/,/g, '').replace('.', '.');
        } else if (amountStr.includes(',') && !amountStr.includes('.')) {
          // Only comma - check if it's decimal separator
          const parts = amountStr.split(',');
          if (parts.length === 2 && parts[1].length <= 2) {
            // Likely decimal separator
            amountStr = amountStr.replace(',', '.');
          } else {
            // Likely thousands separator
            amountStr = amountStr.replace(/,/g, '');
          }
        }

        const amount = parseFloat(amountStr);
        if (!isNaN(amount) && amount > 0) {
          return amount;
        }
      }
    }

    return 0; // Return 0 instead of random amount
  }

  /**
   * Extract currency from text
   * @param {string} text - Text to search
   * @returns {string} - Currency code
   */
  extractCurrency(text) {
    const currencies = ['PLN', 'EUR', 'USD', 'GBP'];

    for (const currency of currencies) {
      if (text.includes(currency)) { return currency; }
    }

    return 'PLN'; // Default to PLN
  }

  /**
   * Check if text contains invoice keywords
   * @param {string} text - Text to check
   * @returns {boolean} - Whether text contains invoice keywords
   */
  containsInvoiceKeywords(text) {
    const keywords = [
      'faktura', 'vat', 'nip', 'sprzedawca', 'nabywca', 'kwota', 'suma', 'razem',
      'invoice', 'tax', 'seller', 'buyer', 'amount', 'total', 'payment'
    ];

    const lowerText = text.toLowerCase();
    const foundKeywords = keywords.filter(keyword => lowerText.includes(keyword));

    return foundKeywords.length >= 3;
  }

  /**
   * Validate file before processing using consolidated validation service
   * @param {File} file - File to validate
   * @returns {Promise<Object>} - Validation result
   */
  async validateFile(file) {
    try {
      // Use consolidated validation service with document processing specific options
      const validation = await consolidatedFileValidationService.validateFile(file, {
        maxFileSize: 10 * 1024 * 1024, // 10MB
        allowedMimeTypes: ['application/pdf', 'image/jpeg', 'image/png'],
        enableSecurityScanning: true,
        enableContentValidation: true
      });

      return validation;
    } catch (error) {
      console.error('File validation error:', error);
      return {
        isValid: false,
        errors: [`Validation error: ${error.message}`],
        warnings: []
      };
    }
  }

  /**
   * Cleanup resources
   */
  async cleanup() {
    if (this.sandboxService) {
      await this.sandboxService.terminateWorker();
      this.sandboxService.destroy();
    }
    this.initialized = false;
  }
}

// Create singleton instance
const documentProcessingService = new DocumentProcessingService();

export { DocumentProcessingService };
export default documentProcessingService;
