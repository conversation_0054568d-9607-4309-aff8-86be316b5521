import { useState, useMemo, useCallback, useEffect } from 'react';

/**
 * useTableState Hook
 * Enhanced table state management with multi-selection and persistence
 * Epic 3, Story 3.1, Task 3.1.3: Column Customization & Row Selection
 */
export function useTableState(initialData = [], options = {}) {
  const {
    initialSortField = 'processedAt',
    initialSortDirection = 'desc',
    initialPageSize = 50,
    initialFilterText = '',
    searchFields = ['filename', 'number', 'seller_name', 'buyer_name'],
    persistenceKey = 'mvat-table-state',
    enablePersistence = true,
    enableMultiSelect = true
  } = options;

  // Load persisted state
  const loadPersistedState = useCallback(() => {
    if (!enablePersistence || typeof localStorage === 'undefined') {
      return {};
    }

    try {
      const saved = localStorage.getItem(persistenceKey);
      return saved ? JSON.parse(saved) : {};
    } catch (error) {
      console.warn('Failed to load persisted table state:', error);
      return {};
    }
  }, [persistenceKey, enablePersistence]);

  const persistedState = loadPersistedState();

  // State management
  const [sortField, setSortField] = useState(persistedState.sortField || initialSortField);
  const [sortDirection, setSortDirection] = useState(persistedState.sortDirection || initialSortDirection);
  const [filterText, setFilterText] = useState(initialFilterText);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(persistedState.pageSize || initialPageSize);

  // Multi-selection state
  const [selectedRows, setSelectedRows] = useState(new Set());
  const [lastSelectedIndex, setLastSelectedIndex] = useState(null);

  // Filter data based on search text
  const filteredData = useMemo(() => {
    if (!filterText || !initialData) { return initialData; }

    const searchTerm = filterText.toLowerCase();
    return initialData.filter(item => {
      return searchFields.some(field => {
        const value = item[field];
        return value && value.toString().toLowerCase().includes(searchTerm);
      });
    });
  }, [initialData, filterText, searchFields]);

  // Sort filtered data
  const sortedData = useMemo(() => {
    if (!filteredData || !sortField) { return filteredData; }

    return [...filteredData].sort((a, b) => {
      let aValue = a[sortField];
      let bValue = b[sortField];

      // Handle different data types
      if (sortField === 'processedAt' || sortField === 'date') {
        aValue = new Date(aValue || 0);
        bValue = new Date(bValue || 0);
      } else if (sortField === 'total_gross' || sortField === 'total_net' || sortField === 'total_vat') {
        aValue = parseFloat(aValue) || 0;
        bValue = parseFloat(bValue) || 0;
      } else if (sortField === 'fileSize') {
        aValue = parseInt(aValue) || 0;
        bValue = parseInt(bValue) || 0;
      } else {
        // String comparison
        aValue = (aValue || '').toString().toLowerCase();
        bValue = (bValue || '').toString().toLowerCase();
      }

      if (aValue < bValue) { return sortDirection === 'asc' ? -1 : 1; }
      if (aValue > bValue) { return sortDirection === 'asc' ? 1 : -1; }
      return 0;
    });
  }, [filteredData, sortField, sortDirection]);

  // Paginate sorted data
  const paginatedData = useMemo(() => {
    if (!sortedData) { return []; }

    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return sortedData.slice(startIndex, endIndex);
  }, [sortedData, currentPage, pageSize]);

  // Calculate pagination info
  const paginationInfo = useMemo(() => {
    const totalItems = sortedData?.length || 0;
    const totalPages = Math.ceil(totalItems / pageSize);
    const startItem = totalItems === 0 ? 0 : (currentPage - 1) * pageSize + 1;
    const endItem = Math.min(currentPage * pageSize, totalItems);

    return {
      totalItems,
      totalPages,
      currentPage,
      pageSize,
      startItem,
      endItem,
      hasNextPage: currentPage < totalPages,
      hasPrevPage: currentPage > 1
    };
  }, [sortedData, currentPage, pageSize]);

  // Persist state changes
  useEffect(() => {
    if (!enablePersistence || typeof localStorage === 'undefined') { return; }

    const stateToSave = {
      sortField,
      sortDirection,
      pageSize
    };

    try {
      localStorage.setItem(persistenceKey, JSON.stringify(stateToSave));
    } catch (error) {
      console.warn('Failed to persist table state:', error);
    }
  }, [sortField, sortDirection, pageSize, persistenceKey, enablePersistence]);

  // Clear selection when data changes
  useEffect(() => {
    setSelectedRows(new Set());
    setLastSelectedIndex(null);
  }, [initialData]);

  // Action handlers
  const handleSort = useCallback((field) => {
    if (sortField === field) {
      setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
    setCurrentPage(1); // Reset to first page when sorting
  }, [sortField]);

  const handleFilter = useCallback((text) => {
    setFilterText(text);
    setCurrentPage(1); // Reset to first page when filtering
  }, []);

  const handlePageChange = useCallback((page) => {
    setCurrentPage(Math.max(1, Math.min(page, paginationInfo.totalPages)));
  }, [paginationInfo.totalPages]);

  const handlePageSizeChange = useCallback((size) => {
    setPageSize(size);
    setCurrentPage(1); // Reset to first page when changing page size
  }, []);

  const goToFirstPage = useCallback(() => handlePageChange(1), [handlePageChange]);
  const goToLastPage = useCallback(() => handlePageChange(paginationInfo.totalPages), [handlePageChange, paginationInfo.totalPages]);
  const goToNextPage = useCallback(() => handlePageChange(currentPage + 1), [handlePageChange, currentPage]);
  const goToPrevPage = useCallback(() => handlePageChange(currentPage - 1), [handlePageChange, currentPage]);

  // Reset all filters and pagination
  const resetTable = useCallback(() => {
    setFilterText('');
    setSortField(initialSortField);
    setSortDirection(initialSortDirection);
    setCurrentPage(1);
    setPageSize(initialPageSize);
    setSelectedRows(new Set());
    setLastSelectedIndex(null);
  }, [initialSortField, initialSortDirection, initialPageSize]);

  // Multi-selection handlers
  const handleRowSelect = useCallback((rowId, event = {}) => {
    if (!enableMultiSelect) {
      setSelectedRows(new Set([rowId]));
      return;
    }

    const { ctrlKey, metaKey, shiftKey } = event;
    const isMultiSelectKey = ctrlKey || metaKey;

    setSelectedRows(prev => {
      const newSelection = new Set(prev);
      const rowIndex = sortedData.findIndex(item => item.id === rowId);

      if (shiftKey && lastSelectedIndex !== null) {
        // Range selection
        const start = Math.min(lastSelectedIndex, rowIndex);
        const end = Math.max(lastSelectedIndex, rowIndex);

        for (let i = start; i <= end; i++) {
          if (sortedData[i]) {
            newSelection.add(sortedData[i].id);
          }
        }
      } else if (isMultiSelectKey) {
        // Toggle selection
        if (newSelection.has(rowId)) {
          newSelection.delete(rowId);
        } else {
          newSelection.add(rowId);
        }
      } else {
        // Single selection
        newSelection.clear();
        newSelection.add(rowId);
      }

      return newSelection;
    });

    setLastSelectedIndex(sortedData.findIndex(item => item.id === rowId));
  }, [enableMultiSelect, sortedData, lastSelectedIndex]);

  const handleSelectAll = useCallback((checked) => {
    if (checked) {
      const allIds = new Set(paginatedData.map(item => item.id));
      setSelectedRows(allIds);
    } else {
      setSelectedRows(new Set());
    }
    setLastSelectedIndex(null);
  }, [paginatedData]);

  const clearSelection = useCallback(() => {
    setSelectedRows(new Set());
    setLastSelectedIndex(null);
  }, []);

  // Selection info
  const selectionInfo = useMemo(() => {
    const selectedCount = selectedRows.size;
    const totalCount = paginatedData.length;
    const allSelected = selectedCount > 0 && selectedCount === totalCount;
    const someSelected = selectedCount > 0 && selectedCount < totalCount;

    return {
      selectedCount,
      totalCount,
      allSelected,
      someSelected,
      hasSelection: selectedCount > 0,
      selectedIds: Array.from(selectedRows)
    };
  }, [selectedRows, paginatedData]);

  return {
    // Data
    data: paginatedData,
    filteredData,
    sortedData,

    // State
    sortField,
    sortDirection,
    filterText,
    currentPage,
    pageSize,

    // Selection state
    selectedRows,
    selectionInfo,

    // Pagination info
    pagination: paginationInfo,

    // Actions
    handleSort,
    handleFilter,
    handlePageChange,
    handlePageSizeChange,
    goToFirstPage,
    goToLastPage,
    goToNextPage,
    goToPrevPage,
    resetTable,

    // Selection actions
    handleRowSelect,
    handleSelectAll,
    clearSelection,

    // Setters for direct control
    setSortField,
    setSortDirection,
    setFilterText,
    setCurrentPage,
    setPageSize
  };
}

export default useTableState;
