import { useState, useCallback, useRef } from 'react';
import { consolidatedFileValidationService } from '../../services/ConsolidatedFileValidationService.js';
import { estimateFileProcessingTime } from '../../utils/ProgressTracker.js';

/**
 * Custom Hook for File Upload Logic
 * Manages file upload state, validation, and processing
 *
 * Features:
 * - File validation and error handling
 * - Upload progress tracking
 * - Processing state management
 * - Cancellation support
 * - Memory efficient handling
 * - Performance monitoring
 */
export function useFileUpload(options = {}) {
  const {
    maxFiles = 10,
    maxSize = 10 * 1024 * 1024, // 10MB
    maxTotalSize = 50 * 1024 * 1024, // 50MB
    acceptedTypes = ['application/pdf', 'image/jpeg', 'image/png'],
    onProgress = null,
    onComplete = null,
    onError = null,
    autoProcess = true
  } = options;

  // State management
  const [state, setState] = useState({
    files: [],
    isUploading: false,
    isProcessing: false,
    progress: 0,
    currentFile: null,
    currentStage: null,
    errors: [],
    warnings: [],
    results: [],
    estimatedTime: null,
    startTime: null
  });

  // Refs for cancellation and cleanup
  const abortControllerRef = useRef(null);
  const timeoutRef = useRef(null);

  // Reset state
  const reset = useCallback(() => {
    // Cancel any ongoing operations
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    setState({
      files: [],
      isUploading: false,
      isProcessing: false,
      progress: 0,
      currentFile: null,
      currentStage: null,
      errors: [],
      warnings: [],
      results: [],
      estimatedTime: null,
      startTime: null
    });
  }, []);

  // Update progress with stage information
  const updateProgress = useCallback((progress, stage = null, fileName = null) => {
    setState(prev => ({
      ...prev,
      progress: Math.min(Math.max(progress, 0), 100),
      currentStage: stage || prev.currentStage,
      currentFile: fileName || prev.currentFile
    }));

    // Call external progress callback
    if (onProgress) {
      onProgress({
        progress,
        stage,
        fileName,
        isUploading: state.isUploading,
        isProcessing: state.isProcessing
      });
    }
  }, [onProgress, state.isUploading, state.isProcessing]);

  // Validate files before processing
  const validateFileList = useCallback(async (files) => {
    const validation = await consolidatedFileValidationService.validateFiles(files, {
      maxFiles,
      maxFileSize: maxSize,
      maxTotalSize,
      allowedMimeTypes: acceptedTypes
    });

    setState(prev => ({
      ...prev,
      errors: validation.errors,
      warnings: validation.warnings
    }));

    return validation;
  }, [maxFiles, maxSize, maxTotalSize, acceptedTypes]);

  // Process a single file
  const processSingleFile = useCallback(async (file, index, total, processor) => {
    if (!processor || typeof processor !== 'function') {
      throw new Error('File processor function is required');
    }

    const fileName = file.name;
    const baseProgress = (index / total) * 100;
    const fileProgressRange = 100 / total;

    try {
      // Update state for current file
      updateProgress(baseProgress, 'processing', fileName);

      // Estimate processing time
      const estimatedTime = estimateFileProcessingTime(file);
      setState(prev => ({ ...prev, estimatedTime }));

      // Create abort controller for this file
      const controller = new AbortController();
      abortControllerRef.current = controller;

      // Process file with progress tracking
      const result = await processor(file, {
        signal: controller.signal,
        onProgress: (fileProgress) => {
          const totalProgress = baseProgress + (fileProgress / 100) * fileProgressRange;
          updateProgress(totalProgress, fileProgress.stage || 'processing', fileName);
        }
      });

      // Update final progress for this file
      updateProgress(baseProgress + fileProgressRange, 'complete', fileName);

      return {
        file,
        fileName,
        result,
        success: true,
        error: null
      };

    } catch (error) {
      if (error.name === 'AbortError') {
        throw error; // Re-throw abort errors to handle cancellation
      }

      console.error(`Error processing file ${fileName}:`, error);

      return {
        file,
        fileName,
        result: null,
        success: false,
        error: error.message || 'Unknown processing error'
      };
    }
  }, [updateProgress]);

  // Main upload function
  const uploadFiles = useCallback(async (files, processor) => {
    if (!files || files.length === 0) {
      const error = 'No files provided for upload';
      setState(prev => ({ ...prev, errors: [error] }));
      onError?.(error);
      return { success: false, error };
    }

    // Validate files first
    const validation = await validateFileList(files);
    if (!validation.isValid) {
      const error = `File validation failed: ${validation.errors.join(', ')}`;
      onError?.(error);
      return { success: false, error, validation };
    }

    // Reset previous state and start upload
    const startTime = Date.now();
    setState(prev => ({
      ...prev,
      files: Array.from(files),
      isUploading: true,
      isProcessing: autoProcess,
      progress: 0,
      currentFile: null,
      currentStage: 'uploading',
      errors: [],
      results: [],
      startTime
    }));

    try {
      const results = [];
      const fileArray = Array.from(files);

      // Process files sequentially to avoid overwhelming the system
      for (let i = 0; i < fileArray.length; i++) {
        const file = fileArray[i];

        // Check if operation was cancelled
        if (abortControllerRef.current?.signal.aborted) {
          throw new Error('Upload cancelled by user');
        }

        const result = await processSingleFile(file, i, fileArray.length, processor);
        results.push(result);

        // Update results state
        setState(prev => ({ ...prev, results: [...prev.results, result] }));
      }

      // Calculate final statistics
      const successful = results.filter(r => r.success).length;
      const failed = results.filter(r => !r.success).length;
      const processingTime = Date.now() - startTime;

      // Update final state
      setState(prev => ({
        ...prev,
        isUploading: false,
        isProcessing: false,
        progress: 100,
        currentStage: 'complete',
        currentFile: null
      }));

      const finalResult = {
        success: failed === 0,
        total: fileArray.length,
        successful,
        failed,
        results,
        processingTime,
        validation
      };

      // Call completion callback
      onComplete?.(finalResult);

      return finalResult;

    } catch (error) {
      const errorMessage = error.message || 'Upload failed';

      setState(prev => ({
        ...prev,
        isUploading: false,
        isProcessing: false,
        currentStage: 'error',
        errors: [...prev.errors, errorMessage]
      }));

      onError?.(errorMessage);

      return {
        success: false,
        error: errorMessage,
        results: state.results
      };
    }
  }, [validateFileList, processSingleFile, autoProcess, onComplete, onError, state.results]);

  // Cancel upload
  const cancelUpload = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    setState(prev => ({
      ...prev,
      isUploading: false,
      isProcessing: false,
      currentStage: 'cancelled',
      errors: [...prev.errors, 'Upload cancelled by user']
    }));
  }, []);

  // Remove file from list (before upload)
  const removeFile = useCallback((index) => {
    setState(prev => ({
      ...prev,
      files: prev.files.filter((_, i) => i !== index)
    }));
  }, []);

  // Clear errors
  const clearErrors = useCallback(() => {
    setState(prev => ({ ...prev, errors: [] }));
  }, []);

  // Clear warnings
  const clearWarnings = useCallback(() => {
    setState(prev => ({ ...prev, warnings: [] }));
  }, []);

  // Get upload statistics
  const getStatistics = useCallback(() => {
    const { results, startTime } = state;
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    const processingTime = startTime ? Date.now() - startTime : 0;

    return {
      total: results.length,
      successful,
      failed,
      successRate: results.length > 0 ? (successful / results.length) * 100 : 0,
      processingTime,
      averageTimePerFile: results.length > 0 ? processingTime / results.length : 0
    };
  }, [state]);

  // Cleanup on unmount
  const cleanup = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  }, []);

  return {
    // State
    ...state,

    // Actions
    uploadFiles,
    cancelUpload,
    removeFile,
    reset,
    clearErrors,
    clearWarnings,

    // Utilities
    validateFileList,
    getStatistics,
    cleanup,

    // Computed properties
    hasErrors: state.errors.length > 0,
    hasWarnings: state.warnings.length > 0,
    isActive: state.isUploading || state.isProcessing,
    canUpload: state.files.length > 0 && !state.isUploading && !state.isProcessing
  };
}
