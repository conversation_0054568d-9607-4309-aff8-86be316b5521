import { useState, useEffect, useCallback } from 'react';

/**
 * Custom hook for managing extension state (invoices, processing status)
 */
export function useExtensionState() {
  const [invoices, setInvoices] = useState([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Load invoices from storage
  const loadInvoices = useCallback(async () => {
    try {
      setIsLoading(true);

      if (chrome?.storage?.local) {
        const result = await chrome.storage.local.get(['invoices']);
        setInvoices(result.invoices || []);
      } else {
        // Fallback for development/testing
        const stored = localStorage.getItem('mvat_invoices');
        setInvoices(stored ? JSON.parse(stored) : []);
      }
    } catch (err) {
      console.error('Failed to load invoices:', err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Save invoices to storage
  const saveInvoices = useCallback(async (newInvoices) => {
    try {
      if (chrome?.storage?.local) {
        await chrome.storage.local.set({ invoices: newInvoices });
      } else {
        // Fallback for development/testing
        localStorage.setItem('mvat_invoices', JSON.stringify(newInvoices));
      }
    } catch (err) {
      console.error('Failed to save invoices:', err);
      setError(err.message);
    }
  }, []);

  // Add new invoice
  const addInvoice = useCallback(async (invoice) => {
    try {
      setInvoices(currentInvoices => {
        const newInvoices = [invoice, ...currentInvoices];
        // Save asynchronously without dependency
        saveInvoices(newInvoices).catch(err => {
          console.error('Failed to save invoices:', err);
          setError(err.message);
        });
        return newInvoices;
      });
    } catch (err) {
      console.error('Failed to add invoice:', err);
      setError(err.message);
    }
  }, [saveInvoices]);

  // Remove invoice
  const removeInvoice = useCallback(async (invoiceId) => {
    try {
      setInvoices(currentInvoices => {
        const newInvoices = currentInvoices.filter(inv => inv.id !== invoiceId);
        // Save asynchronously without dependency
        saveInvoices(newInvoices).catch(err => {
          console.error('Failed to save invoices:', err);
          setError(err.message);
        });
        return newInvoices;
      });
    } catch (err) {
      console.error('Failed to remove invoice:', err);
      setError(err.message);
    }
  }, [saveInvoices]);

  // Update invoice
  const updateInvoice = useCallback(async (invoiceId, updates) => {
    try {
      setInvoices(currentInvoices => {
        const newInvoices = currentInvoices.map(inv =>
          inv.id === invoiceId ? { ...inv, ...updates } : inv
        );
        // Save asynchronously without dependency
        saveInvoices(newInvoices).catch(err => {
          console.error('Failed to save invoices:', err);
          setError(err.message);
        });
        return newInvoices;
      });
    } catch (err) {
      console.error('Failed to update invoice:', err);
      setError(err.message);
    }
  }, [saveInvoices]);

  // Clear all invoices
  const clearInvoices = useCallback(async () => {
    try {
      setInvoices([]);
      await saveInvoices([]);
    } catch (err) {
      console.error('Failed to clear invoices:', err);
      setError(err.message);
    }
  }, [saveInvoices]);

  // Listen for storage changes
  useEffect(() => {
    const handleStorageChange = (changes, namespace) => {
      if (namespace === 'local' && changes.invoices) {
        setInvoices(changes.invoices.newValue || []);
      }
    };

    if (chrome?.storage?.onChanged) {
      chrome.storage.onChanged.addListener(handleStorageChange);
      return () => {
        chrome.storage.onChanged.removeListener(handleStorageChange);
      };
    }
  }, []);

  // Load invoices on mount
  useEffect(() => {
    loadInvoices();
  }, [loadInvoices]);

  return {
    invoices,
    isProcessing,
    isLoading,
    error,
    setIsProcessing,
    setError,
    addInvoice,
    removeInvoice,
    updateInvoice,
    clearInvoices,
    reloadInvoices: loadInvoices
  };
}
