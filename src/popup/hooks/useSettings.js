import { useState, useEffect, useCallback } from 'react';
import { settingsService } from '../../services/SettingsService.js';
import { environmentConfig } from '../../services/EnvironmentConfigService.js';
import { configurationSourceManager } from '../../services/ConfigurationSourceManager.js';

/**
 * Default settings structure
 */
const DEFAULT_SETTINGS = {
  company: {
    name: '',
    taxId: '',
    address: '',
    email: '',
    phone: '',
    logo: ''
  },
  display: {
    groupBy: 'month',
    dateFormat: 'DD/MM/YYYY',
    currency: 'PLN',
    language: 'pl',
    theme: 'light'
  },
  processing: {
    ocrLanguage: 'pol',
    aiProvider: 'deepseek',
    autoProcess: true,
    cacheEnabled: true
  },
  apiKeys: {
    deepseek: '',
    openai: '',
    fakturownia: '',
    infakt: ''
  }
};

/**
 * Custom hook for managing extension settings
 */
export function useSettings() {
  const [settings, setSettings] = useState(DEFAULT_SETTINGS);
  const [environmentConfiguration, setEnvironmentConfiguration] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Load settings from storage
  const loadSettings = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Load regular settings
      const loadedSettings = await settingsService.loadSettings();
      setSettings(loadedSettings);

      // Load environment configuration
      try {
        if (!environmentConfig.isLoaded) {
          await environmentConfig.initialize();
        }
        const envConfig = environmentConfig.getAll(true); // masked by default
        setEnvironmentConfiguration(envConfig);
        console.log('✅ useSettings: Environment configuration loaded');
      } catch (envError) {
        console.warn('⚠️ useSettings: Failed to load environment configuration:', envError.message);
        setEnvironmentConfiguration({});
      }

    } catch (err) {
      console.error('Failed to load settings:', err);
      setError(err.message);
      setSettings(DEFAULT_SETTINGS);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Save settings to storage
  const saveSettings = useCallback(async (newSettings) => {
    try {
      await settingsService.saveSettings(newSettings);
      setSettings(newSettings);
    } catch (err) {
      console.error('Failed to save settings:', err);
      setError(err.message);
      throw err;
    }
  }, []);

  // Update settings
  const updateSettings = useCallback(async (updates) => {
    try {
      setSettings(currentSettings => {
        const newSettings = {
          company: { ...currentSettings.company, ...updates.company },
          display: { ...currentSettings.display, ...updates.display },
          processing: { ...currentSettings.processing, ...updates.processing }
        };

        // Save settings asynchronously without dependency
        settingsService.saveSettings(newSettings).catch(err => {
          console.error('Failed to save settings:', err);
          setError(err.message);
        });

        return newSettings;
      });
    } catch (err) {
      console.error('Failed to update settings:', err);
      setError(err.message);
      throw err;
    }
  }, []);

  // Update specific setting section
  const updateSettingSection = useCallback(async (section, updates) => {
    try {
      const newSettings = await settingsService.updateSection(section, updates);
      setSettings(newSettings);
    } catch (err) {
      console.error(`Failed to update ${section} settings:`, err);
      setError(err.message);
      throw err;
    }
  }, []);

  // Update API keys with validation
  const updateApiKeys = useCallback(async (apiKeys, validateConnections = false) => {
    try {
      const result = await settingsService.updateApiKeys(apiKeys, validateConnections);

      if (result.success) {
        // Reload settings to get updated values
        await loadSettings();
      }

      return result;
    } catch (err) {
      console.error('Failed to update API keys:', err);
      setError(err.message);
      throw err;
    }
  }, [loadSettings]);

  // Clear all API keys
  const clearApiKeys = useCallback(async () => {
    try {
      await settingsService.clearApiKeys();
      await loadSettings();
    } catch (err) {
      console.error('Failed to clear API keys:', err);
      setError(err.message);
      throw err;
    }
  }, [loadSettings]);

  // Reset settings to defaults
  const resetSettings = useCallback(async () => {
    try {
      await settingsService.resetToDefaults();
      setSettings(DEFAULT_SETTINGS);
    } catch (err) {
      console.error('Failed to reset settings:', err);
      setError(err.message);
      throw err;
    }
  }, []);

  // Export settings
  const exportSettings = useCallback(async () => {
    try {
      return await settingsService.exportSettings();
    } catch (err) {
      console.error('Failed to export settings:', err);
      setError(err.message);
      throw err;
    }
  }, []);

  // Import settings
  const importSettings = useCallback(async (importedSettings) => {
    try {
      const result = await settingsService.importSettings(importedSettings);
      if (result.success) {
        await loadSettings();
      }
      return result;
    } catch (err) {
      console.error('Failed to import settings:', err);
      setError(err.message);
      throw err;
    }
  }, [loadSettings]);

  // Get specific setting value
  const getSetting = useCallback((section, key, defaultValue = null) => {
    return settings[section]?.[key] ?? defaultValue;
  }, [settings]);

  // Load environment configuration from specific source
  const loadEnvironmentConfiguration = useCallback(async (sourceId = null) => {
    try {
      setIsLoading(true);
      setError(null);

      if (sourceId) {
        // Load from specific source using ConfigurationSourceManager
        configurationSourceManager.setCurrentSource(sourceId);
        const config = await configurationSourceManager.loadConfiguration();
        setEnvironmentConfiguration(config);
        console.log(`✅ useSettings: Environment configuration loaded from ${sourceId}`);
      } else {
        // Load from default EnvironmentConfigService
        if (!environmentConfig.isLoaded) {
          await environmentConfig.initialize();
        }
        const envConfig = environmentConfig.getAll(true); // masked by default
        setEnvironmentConfiguration(envConfig);
        console.log('✅ useSettings: Environment configuration loaded from default service');
      }

      return true;
    } catch (err) {
      console.error('Failed to load environment configuration:', err);
      setError(err.message);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Listen for storage changes
  useEffect(() => {
    const handleStorageChange = (changes, namespace) => {
      if (namespace === 'local' && changes.settings) {
        const newSettings = changes.settings.newValue || DEFAULT_SETTINGS;

        // Merge with defaults
        const mergedSettings = {
          company: { ...DEFAULT_SETTINGS.company, ...newSettings.company },
          display: { ...DEFAULT_SETTINGS.display, ...newSettings.display },
          processing: { ...DEFAULT_SETTINGS.processing, ...newSettings.processing }
        };

        setSettings(mergedSettings);
      }
    };

    if (chrome?.storage?.onChanged) {
      chrome.storage.onChanged.addListener(handleStorageChange);
      return () => {
        chrome.storage.onChanged.removeListener(handleStorageChange);
      };
    }
  }, []);

  // Load settings on mount
  useEffect(() => {
    loadSettings();
  }, [loadSettings]);

  return {
    settings,
    environmentConfiguration,
    isLoading,
    error,
    updateSettings,
    updateSettingSection,
    updateApiKeys,
    clearApiKeys,
    resetSettings,
    exportSettings,
    importSettings,
    getSetting,
    loadEnvironmentConfiguration,
    reloadSettings: loadSettings,
    DEFAULT_SETTINGS
  };
}
