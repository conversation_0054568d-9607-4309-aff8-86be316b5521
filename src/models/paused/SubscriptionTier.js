/**
 * 🎯 SUBSCRIPTION TIER MODEL
 *
 * Defines subscription tiers with features, limits, and pricing for the MVAT Chrome Extension.
 * Supports four tiers: STARTER (free), PROFESSIONAL, BUSINESS, and ENTERPRISE.
 *
 * @fileoverview Subscription tier management and feature validation
 * @version 1.4.0
 * @since 2025-06-15
 */

/**
 * Subscription tier definitions and feature management
 *
 * @class SubscriptionTier
 * @description Manages subscription tiers, features, and usage limits for monetization system
 *
 * @example
 * // Check if user has access to a feature
 * const tier = SubscriptionTier.PROFESSIONAL;
 * if (tier.hasFeature('AI_ANALYSIS')) {
 *   // User can access AI analysis
 * }
 *
 * @example
 * // Get remaining usage for a tier
 * const remaining = tier.getRemainingUsage('DOCUMENTS_PER_MONTH', currentUsage);
 * if (remaining > 0) {
 *   // User can process more documents
 * }
 */
class SubscriptionTier {
  /**
   * Creates a subscription tier instance
   *
   * @param {Object} config - Tier configuration
   * @param {string} config.id - Unique tier identifier
   * @param {string} config.name - Display name
   * @param {string} config.description - Tier description
   * @param {number} config.price - Monthly price in EUR
   * @param {Object} config.features - Available features
   * @param {Object} config.limits - Usage limits
   * @param {Array<string>} config.benefits - Key benefits list
   */
  constructor(config) {
    this.id = config.id;
    this.name = config.name;
    this.description = config.description;
    this.price = config.price;
    this.features = Object.freeze(config.features);
    this.limits = Object.freeze(config.limits);
    this.benefits = Object.freeze([...config.benefits]);

    // Freeze the instance to prevent modifications
    Object.freeze(this);
  }

  /**
   * Check if tier has access to a specific feature
   *
   * @param {string} featureName - Feature to check
   * @returns {boolean} True if feature is available
   *
   * @example
   * tier.hasFeature('AI_ANALYSIS') // true/false
   */
  hasFeature(featureName) {
    return Boolean(this.features[featureName]);
  }

  /**
   * Check if user can perform an action based on current usage
   *
   * @param {string} limitType - Type of limit to check
   * @param {number} currentUsage - Current usage count
   * @returns {boolean} True if action is allowed
   *
   * @example
   * tier.canPerformAction('DOCUMENTS_PER_MONTH', 45) // true/false
   */
  canPerformAction(limitType, currentUsage) {
    const limit = this.limits[limitType];
    if (limit === -1) { return true; } // Unlimited
    return currentUsage < limit;
  }

  /**
   * Get remaining usage for a specific limit
   *
   * @param {string} limitType - Type of limit to check
   * @param {number} currentUsage - Current usage count
   * @returns {number} Remaining usage (-1 for unlimited)
   *
   * @example
   * tier.getRemainingUsage('API_CALLS_PER_MONTH', 1500) // 500
   */
  getRemainingUsage(limitType, currentUsage) {
    const limit = this.limits[limitType];
    if (limit === -1) { return -1; } // Unlimited
    return Math.max(0, limit - currentUsage);
  }

  /**
   * Get usage percentage for a specific limit
   *
   * @param {string} limitType - Type of limit to check
   * @param {number} currentUsage - Current usage count
   * @returns {number} Usage percentage (0-100, or -1 for unlimited)
   */
  getUsagePercentage(limitType, currentUsage) {
    const limit = this.limits[limitType];
    if (limit === -1) { return -1; } // Unlimited
    return Math.min(100, (currentUsage / limit) * 100);
  }

  /**
   * Check if user is approaching usage limits
   *
   * @param {string} limitType - Type of limit to check
   * @param {number} currentUsage - Current usage count
   * @param {number} threshold - Warning threshold percentage (default: 80)
   * @returns {boolean} True if approaching limit
   */
  isApproachingLimit(limitType, currentUsage, threshold = 80) {
    const percentage = this.getUsagePercentage(limitType, currentUsage);
    return percentage !== -1 && percentage >= threshold;
  }

  /**
   * Get recommended upgrade tier based on usage patterns
   *
   * @param {Object} usage - Current usage statistics
   * @returns {SubscriptionTier|null} Recommended tier or null if no upgrade needed
   */
  getRecommendedUpgrade(usage) {
    const tiers = [SubscriptionTier.STARTER, SubscriptionTier.PROFESSIONAL,
      SubscriptionTier.BUSINESS, SubscriptionTier.ENTERPRISE];

    const currentIndex = tiers.findIndex(tier => tier.id === this.id);

    // Check if current tier limits are being exceeded
    for (const [limitType, currentUsage] of Object.entries(usage)) {
      if (!this.canPerformAction(limitType, currentUsage)) {
        // Find next tier that can handle this usage
        for (let i = currentIndex + 1; i < tiers.length; i++) {
          if (tiers[i].canPerformAction(limitType, currentUsage)) {
            return tiers[i];
          }
        }
      }
    }

    return null; // No upgrade needed
  }

  /**
   * Compare this tier with another tier
   *
   * @param {SubscriptionTier} otherTier - Tier to compare with
   * @returns {number} -1 if lower, 0 if equal, 1 if higher
   */
  compareTo(otherTier) {
    const tiers = [SubscriptionTier.STARTER, SubscriptionTier.PROFESSIONAL,
      SubscriptionTier.BUSINESS, SubscriptionTier.ENTERPRISE];

    const thisIndex = tiers.findIndex(tier => tier.id === this.id);
    const otherIndex = tiers.findIndex(tier => tier.id === otherTier.id);

    return Math.sign(thisIndex - otherIndex);
  }

  /**
   * Get tier as JSON object
   *
   * @returns {Object} Tier configuration as plain object
   */
  toJSON() {
    return {
      id: this.id,
      name: this.name,
      description: this.description,
      price: this.price,
      features: { ...this.features },
      limits: { ...this.limits },
      benefits: [...this.benefits]
    };
  }

  /**
   * Get tier display information
   *
   * @returns {Object} Display-friendly tier information
   */
  getDisplayInfo() {
    return {
      name: this.name,
      price: this.price === 0 ? 'Free' : `€${this.price}/month`,
      description: this.description,
      benefits: this.benefits,
      isPopular: this.id === 'PROFESSIONAL',
      isEnterprise: this.id === 'ENTERPRISE'
    };
  }
}

// Static tier definitions
SubscriptionTier.STARTER = new SubscriptionTier({
  id: 'STARTER',
  name: 'Starter',
  description: 'Perfect for freelancers and small-scale invoice processing',
  price: 0,
  features: {
    DOCUMENT_UPLOAD: true,
    BASIC_OCR: true,
    PDF_PROCESSING: true,
    TABLE_DISPLAY: true,
    BASIC_EXPORT: true,
    AI_ANALYSIS: false,
    ADVANCED_EXPORT: false,
    BULK_PROCESSING: false,
    API_ACCESS: false,
    PRIORITY_SUPPORT: false,
    CUSTOM_INTEGRATIONS: false,
    ADVANCED_ANALYTICS: false
  },
  limits: {
    DOCUMENTS_PER_MONTH: 10,
    API_CALLS_PER_MONTH: 100,
    STORAGE_MB: 50,
    EXPORT_FORMATS: 1, // CSV only
    CONCURRENT_UPLOADS: 1
  },
  benefits: [
    '10 documents per month',
    'Basic OCR processing',
    'PDF text extraction',
    'CSV export',
    'Community support'
  ]
});

SubscriptionTier.PROFESSIONAL = new SubscriptionTier({
  id: 'PROFESSIONAL',
  name: 'Professional',
  description: 'Ideal for small businesses with regular invoice processing needs',
  price: 29,
  features: {
    DOCUMENT_UPLOAD: true,
    BASIC_OCR: true,
    PDF_PROCESSING: true,
    TABLE_DISPLAY: true,
    BASIC_EXPORT: true,
    AI_ANALYSIS: true,
    ADVANCED_EXPORT: true,
    BULK_PROCESSING: true,
    API_ACCESS: false,
    PRIORITY_SUPPORT: false,
    CUSTOM_INTEGRATIONS: false,
    ADVANCED_ANALYTICS: true
  },
  limits: {
    DOCUMENTS_PER_MONTH: 100,
    API_CALLS_PER_MONTH: 2000,
    STORAGE_MB: 500,
    EXPORT_FORMATS: 3, // CSV, Excel, JSON
    CONCURRENT_UPLOADS: 3
  },
  benefits: [
    '100 documents per month',
    'AI-powered analysis',
    'Advanced export formats',
    'Bulk processing',
    'Analytics dashboard',
    'Email support'
  ]
});

SubscriptionTier.BUSINESS = new SubscriptionTier({
  id: 'BUSINESS',
  name: 'Business',
  description: 'Designed for growing businesses with high-volume processing',
  price: 99,
  features: {
    DOCUMENT_UPLOAD: true,
    BASIC_OCR: true,
    PDF_PROCESSING: true,
    TABLE_DISPLAY: true,
    BASIC_EXPORT: true,
    AI_ANALYSIS: true,
    ADVANCED_EXPORT: true,
    BULK_PROCESSING: true,
    API_ACCESS: true,
    PRIORITY_SUPPORT: true,
    CUSTOM_INTEGRATIONS: false,
    ADVANCED_ANALYTICS: true
  },
  limits: {
    DOCUMENTS_PER_MONTH: 500,
    API_CALLS_PER_MONTH: 10000,
    STORAGE_MB: 2000,
    EXPORT_FORMATS: 5, // CSV, Excel, JSON, XML, PDF
    CONCURRENT_UPLOADS: 5
  },
  benefits: [
    '500 documents per month',
    'API access',
    'Priority support',
    'Advanced analytics',
    'Multiple export formats',
    'Team collaboration'
  ]
});

SubscriptionTier.ENTERPRISE = new SubscriptionTier({
  id: 'ENTERPRISE',
  name: 'Enterprise',
  description: 'Complete solution for large organizations with unlimited processing',
  price: 299,
  features: {
    DOCUMENT_UPLOAD: true,
    BASIC_OCR: true,
    PDF_PROCESSING: true,
    TABLE_DISPLAY: true,
    BASIC_EXPORT: true,
    AI_ANALYSIS: true,
    ADVANCED_EXPORT: true,
    BULK_PROCESSING: true,
    API_ACCESS: true,
    PRIORITY_SUPPORT: true,
    CUSTOM_INTEGRATIONS: true,
    ADVANCED_ANALYTICS: true
  },
  limits: {
    DOCUMENTS_PER_MONTH: -1, // Unlimited
    API_CALLS_PER_MONTH: -1, // Unlimited
    STORAGE_MB: -1, // Unlimited
    EXPORT_FORMATS: -1, // All formats
    CONCURRENT_UPLOADS: 10
  },
  benefits: [
    'Unlimited documents',
    'Custom integrations',
    'Dedicated support',
    'SLA guarantees',
    'On-premise deployment',
    'Custom features'
  ]
});

// Utility methods
SubscriptionTier.getAllTiers = () => [
  SubscriptionTier.STARTER,
  SubscriptionTier.PROFESSIONAL,
  SubscriptionTier.BUSINESS,
  SubscriptionTier.ENTERPRISE
];

SubscriptionTier.getTierById = (id) => {
  const tiers = SubscriptionTier.getAllTiers();
  return tiers.find(tier => tier.id === id) || null;
};

SubscriptionTier.getDefaultTier = () => SubscriptionTier.STARTER;

export default SubscriptionTier;
