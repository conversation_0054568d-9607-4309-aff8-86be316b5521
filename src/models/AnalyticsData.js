/**
 * Analytics Data Models
 *
 * Defines data structures and models for analytics and business intelligence features.
 * Provides standardized interfaces for analytics data collection, processing, and visualization.
 */

/**
 * Document Processing Metrics
 */
export class DocumentMetrics {
  constructor(data = {}) {
    this.totalDocuments = data.totalDocuments || 0;
    this.successfulProcessing = data.successfulProcessing || 0;
    this.failedProcessing = data.failedProcessing || 0;
    this.averageProcessingTime = data.averageProcessingTime || 0;
    this.totalProcessingTime = data.totalProcessingTime || 0;
    this.documentTypes = data.documentTypes || {};
    this.processingErrors = data.processingErrors || [];
    this.lastUpdated = data.lastUpdated || new Date().toISOString();
  }

  get successRate() {
    return this.totalDocuments > 0 ? (this.successfulProcessing / this.totalDocuments) * 100 : 0;
  }

  get failureRate() {
    return this.totalDocuments > 0 ? (this.failedProcessing / this.totalDocuments) * 100 : 0;
  }

  addDocument(success, processingTime, documentType, error = null) {
    this.totalDocuments++;
    this.totalProcessingTime += processingTime;
    this.averageProcessingTime = this.totalProcessingTime / this.totalDocuments;

    if (success) {
      this.successfulProcessing++;
    } else {
      this.failedProcessing++;
      if (error) {
        this.processingErrors.push({
          timestamp: new Date().toISOString(),
          error: error.message || error,
          documentType
        });
      }
    }

    if (documentType) {
      this.documentTypes[documentType] = (this.documentTypes[documentType] || 0) + 1;
    }

    this.lastUpdated = new Date().toISOString();
  }
}

/**
 * AI Analysis Performance Metrics
 */
export class AIMetrics {
  constructor(data = {}) {
    this.totalAnalyses = data.totalAnalyses || 0;
    this.successfulAnalyses = data.successfulAnalyses || 0;
    this.failedAnalyses = data.failedAnalyses || 0;
    this.averageAnalysisTime = data.averageAnalysisTime || 0;
    this.totalAnalysisTime = data.totalAnalysisTime || 0;
    this.confidenceScores = data.confidenceScores || [];
    this.apiUsage = data.apiUsage || {
      deepseek: { calls: 0, tokens: 0, cost: 0 }
    };
    this.analysisTypes = data.analysisTypes || {};
    this.lastUpdated = data.lastUpdated || new Date().toISOString();
  }

  get successRate() {
    return this.totalAnalyses > 0 ? (this.successfulAnalyses / this.totalAnalyses) * 100 : 0;
  }

  get averageConfidence() {
    return this.confidenceScores.length > 0
      ? this.confidenceScores.reduce((sum, score) => sum + score, 0) / this.confidenceScores.length
      : 0;
  }

  addAnalysis(success, analysisTime, confidence, analysisType, apiProvider = 'deepseek', tokens = 0) {
    this.totalAnalyses++;
    this.totalAnalysisTime += analysisTime;
    this.averageAnalysisTime = this.totalAnalysisTime / this.totalAnalyses;

    if (success) {
      this.successfulAnalyses++;
      if (confidence !== null && confidence !== undefined) {
        this.confidenceScores.push(confidence);
      }
    } else {
      this.failedAnalyses++;
    }

    if (analysisType) {
      this.analysisTypes[analysisType] = (this.analysisTypes[analysisType] || 0) + 1;
    }

    if (this.apiUsage[apiProvider]) {
      this.apiUsage[apiProvider].calls++;
      this.apiUsage[apiProvider].tokens += tokens;
    }

    this.lastUpdated = new Date().toISOString();
  }
}

/**
 * Business Intelligence Metrics
 */
export class BusinessMetrics {
  constructor(data = {}) {
    this.totalValue = data.totalValue || 0;
    this.documentCount = data.documentCount || 0;
    this.averageValue = data.averageValue || 0;
    this.valueByMonth = data.valueByMonth || {};
    this.valueByVendor = data.valueByVendor || {};
    this.valueByCategory = data.valueByCategory || {};
    this.trends = data.trends || [];
    this.insights = data.insights || [];
    this.lastUpdated = data.lastUpdated || new Date().toISOString();
  }

  addDocument(value, vendor, category, date) {
    this.totalValue += value;
    this.documentCount++;
    this.averageValue = this.totalValue / this.documentCount;

    // Group by month
    const monthKey = new Date(date).toISOString().substring(0, 7); // YYYY-MM
    this.valueByMonth[monthKey] = (this.valueByMonth[monthKey] || 0) + value;

    // Group by vendor
    if (vendor) {
      this.valueByVendor[vendor] = (this.valueByVendor[vendor] || 0) + value;
    }

    // Group by category
    if (category) {
      this.valueByCategory[category] = (this.valueByCategory[category] || 0) + value;
    }

    this.lastUpdated = new Date().toISOString();
  }

  addInsight(insight) {
    this.insights.push({
      timestamp: new Date().toISOString(),
      ...insight
    });
  }
}

/**
 * System Performance Metrics
 */
export class SystemMetrics {
  constructor(data = {}) {
    this.uptime = data.uptime || 0;
    this.memoryUsage = data.memoryUsage || [];
    this.errorRate = data.errorRate || 0;
    this.responseTime = data.responseTime || [];
    this.activeUsers = data.activeUsers || 0;
    this.sessionDuration = data.sessionDuration || [];
    this.featureUsage = data.featureUsage || {};
    this.lastUpdated = data.lastUpdated || new Date().toISOString();
  }

  recordMemoryUsage(usage) {
    this.memoryUsage.push({
      timestamp: new Date().toISOString(),
      usage
    });

    // Keep only last 100 entries
    if (this.memoryUsage.length > 100) {
      this.memoryUsage = this.memoryUsage.slice(-100);
    }
  }

  recordResponseTime(time) {
    this.responseTime.push({
      timestamp: new Date().toISOString(),
      time
    });

    // Keep only last 100 entries
    if (this.responseTime.length > 100) {
      this.responseTime = this.responseTime.slice(-100);
    }
  }

  recordFeatureUsage(feature) {
    this.featureUsage[feature] = (this.featureUsage[feature] || 0) + 1;
    this.lastUpdated = new Date().toISOString();
  }
}

/**
 * Combined Analytics Data
 */
export class AnalyticsData {
  constructor(data = {}) {
    this.documents = new DocumentMetrics(data.documents);
    this.ai = new AIMetrics(data.ai);
    this.business = new BusinessMetrics(data.business);
    this.system = new SystemMetrics(data.system);
    this.createdAt = data.createdAt || new Date().toISOString();
    this.lastUpdated = data.lastUpdated || new Date().toISOString();
  }

  /**
   * Export analytics data for reporting
   */
  export() {
    return {
      documents: { ...this.documents },
      ai: { ...this.ai },
      business: { ...this.business },
      system: { ...this.system },
      createdAt: this.createdAt,
      lastUpdated: this.lastUpdated,
      exportedAt: new Date().toISOString()
    };
  }

  /**
   * Import analytics data from backup
   */
  static import(data) {
    return new AnalyticsData(data);
  }

  /**
   * Get summary statistics
   */
  getSummary() {
    return {
      totalDocuments: this.documents.totalDocuments,
      successRate: this.documents.successRate,
      totalValue: this.business.totalValue,
      averageValue: this.business.averageValue,
      aiSuccessRate: this.ai.successRate,
      averageConfidence: this.ai.averageConfidence,
      lastUpdated: this.lastUpdated
    };
  }

  /**
   * Update timestamp
   */
  touch() {
    this.lastUpdated = new Date().toISOString();
  }
}

/**
 * Analytics Event Types
 */
export const ANALYTICS_EVENTS = {
  DOCUMENT_UPLOADED: 'document_uploaded',
  DOCUMENT_PROCESSED: 'document_processed',
  AI_ANALYSIS_STARTED: 'ai_analysis_started',
  AI_ANALYSIS_COMPLETED: 'ai_analysis_completed',
  RAG_SIMILARITY_CALCULATED: 'rag_similarity_calculated',
  EXPORT_GENERATED: 'export_generated',
  FEATURE_USED: 'feature_used',
  ERROR_OCCURRED: 'error_occurred'
};

/**
 * Default analytics configuration
 */
export const ANALYTICS_CONFIG = {
  retentionDays: 90,
  maxEvents: 10000,
  aggregationInterval: 3600000, // 1 hour in milliseconds
  enableRealTimeUpdates: true,
  enableBusinessIntelligence: true
};
