/**
 * Embedding Cache Utility
 *
 * Optimized caching layer for document embeddings with LRU eviction,
 * compression, and persistent storage support for RAG-based analysis.
 *
 * <AUTHOR> Agent
 * @version 1.0.0
 * @since 2025-01-28
 */

import { ProcessingLogger } from './ProcessingLogger.js';

export class EmbeddingCache {
  constructor(options = {}) {
    this.logger = new ProcessingLogger('EmbeddingCache');

    // Configuration
    this.config = {
      maxSize: options.maxSize || 1000,
      maxMemoryMB: options.maxMemoryMB || 100,
      ttlMinutes: options.ttlMinutes || 60 * 24, // 24 hours default
      compressionEnabled: options.compressionEnabled !== false,
      persistentStorage: options.persistentStorage !== false,
      storageKey: options.storageKey || 'mvat_embedding_cache',
      ...options
    };

    // Cache storage
    this.cache = new Map();
    this.accessOrder = new Map(); // For LRU tracking
    this.sizeTracker = 0; // Approximate memory usage in bytes

    // Statistics
    this.stats = {
      hits: 0,
      misses: 0,
      evictions: 0,
      compressions: 0,
      persistentLoads: 0,
      persistentSaves: 0,
      totalSize: 0,
      memoryUsage: 0
    };

    // Initialize cache
    this.initialize();
  }

  /**
   * Initialize cache from persistent storage
   * @private
   */
  async initialize() {
    if (!this.config.persistentStorage) { return; }

    try {
      const stored = await this.loadFromStorage();
      if (stored) {
        this.cache = new Map(stored.entries);
        this.accessOrder = new Map(stored.accessOrder);
        this.sizeTracker = stored.sizeTracker || 0;
        this.stats.persistentLoads++;

        this.logger.log('📋 Cache initialized from persistent storage', {
          entries: this.cache.size,
          memoryUsage: this.formatBytes(this.sizeTracker)
        });
      }
    } catch (error) {
      this.logger.warn('⚠️ Failed to load cache from storage', { error: error.message });
    }
  }

  /**
   * Get embedding from cache
   * @param {string} text - Document text
   * @param {Object} options - Generation options
   * @returns {Promise<Object|null>} Cached embedding or null
   */
  async get(text, options = {}) {
    const key = this.generateCacheKey(text, options);

    if (!this.cache.has(key)) {
      this.stats.misses++;
      return null;
    }

    const entry = this.cache.get(key);

    // Check TTL
    if (this.isExpired(entry)) {
      this.cache.delete(key);
      this.accessOrder.delete(key);
      this.updateSizeTracker(-entry.size);
      this.stats.misses++;
      return null;
    }

    // Update access order for LRU
    this.accessOrder.set(key, Date.now());
    this.stats.hits++;

    // Decompress if needed
    const embedding = this.config.compressionEnabled && entry.compressed
      ? this.decompress(entry.data)
      : entry.data;

    this.logger.log('📋 Cache hit', {
      key: key.substring(0, 16) + '...',
      compressed: entry.compressed,
      age: Date.now() - entry.timestamp
    });

    return {
      ...embedding,
      cacheKey: key,
      cacheHit: true,
      cacheAge: Date.now() - entry.timestamp
    };
  }

  /**
   * Store embedding in cache
   * @param {string} text - Document text
   * @param {Object} options - Generation options
   * @param {Object} embedding - Embedding to cache
   * @returns {Promise<void>}
   */
  async set(text, options, embedding) {
    const key = this.generateCacheKey(text, options);
    const timestamp = Date.now();

    // Prepare data for storage
    let data = { ...embedding };
    let compressed = false;

    // Compress if enabled and beneficial
    if (this.config.compressionEnabled) {
      const compressedData = this.compress(data);
      if (compressedData.length < JSON.stringify(data).length * 0.8) {
        data = compressedData;
        compressed = true;
        this.stats.compressions++;
      }
    }

    const entry = {
      data,
      compressed,
      timestamp,
      size: this.estimateSize(data),
      ttl: timestamp + (this.config.ttlMinutes * 60 * 1000)
    };

    // Check if we need to evict entries
    await this.ensureCapacity(entry.size);

    // Store entry
    this.cache.set(key, entry);
    this.accessOrder.set(key, timestamp);
    this.updateSizeTracker(entry.size);

    this.logger.log('💾 Embedding cached', {
      key: key.substring(0, 16) + '...',
      size: this.formatBytes(entry.size),
      compressed,
      totalEntries: this.cache.size
    });

    // Persist to storage if enabled
    if (this.config.persistentStorage) {
      await this.saveToStorage();
    }
  }

  /**
   * Generate cache key from text and options
   * @private
   */
  generateCacheKey(text, options) {
    const textHash = this.hash(text);
    const optionsHash = this.hash(JSON.stringify(this.normalizeOptions(options)));
    return `${textHash}_${optionsHash}`;
  }

  /**
   * Normalize options for consistent caching
   * @private
   */
  normalizeOptions(options) {
    const normalized = {};

    // Include only cache-relevant options
    const relevantKeys = ['model', 'documentType', 'dimensions'];
    for (const key of relevantKeys) {
      if (options[key] !== undefined) {
        normalized[key] = options[key];
      }
    }

    return normalized;
  }

  /**
   * Check if cache entry is expired
   * @private
   */
  isExpired(entry) {
    return Date.now() > entry.ttl;
  }

  /**
   * Ensure cache capacity before adding new entry
   * @private
   */
  async ensureCapacity(newEntrySize) {
    const maxSizeBytes = this.config.maxMemoryMB * 1024 * 1024;

    // Check size limit
    while (this.cache.size >= this.config.maxSize ||
           this.sizeTracker + newEntrySize > maxSizeBytes) {

      if (this.cache.size === 0) { break; } // Safety check

      await this.evictLRU();
    }
  }

  /**
   * Evict least recently used entry
   * @private
   */
  async evictLRU() {
    if (this.accessOrder.size === 0) { return; }

    // Find LRU entry
    let oldestKey = null;
    let oldestTime = Infinity;

    for (const [key, time] of this.accessOrder) {
      if (time < oldestTime) {
        oldestTime = time;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      const entry = this.cache.get(oldestKey);
      this.cache.delete(oldestKey);
      this.accessOrder.delete(oldestKey);
      this.updateSizeTracker(-entry.size);
      this.stats.evictions++;

      this.logger.log('🗑️ Cache entry evicted (LRU)', {
        key: oldestKey.substring(0, 16) + '...',
        age: Date.now() - oldestTime,
        size: this.formatBytes(entry.size)
      });
    }
  }

  /**
   * Compress data using simple JSON compression
   * @private
   */
  compress(data) {
    try {
      const jsonString = JSON.stringify(data);

      // Simple compression: remove whitespace and use shorter keys
      const compressed = jsonString
        .replace(/\s+/g, '')
        .replace(/"vector":/g, '"v":')
        .replace(/"metadata":/g, '"m":')
        .replace(/"confidence":/g, '"c":')
        .replace(/"method":/g, '"mt":')
        .replace(/"timestamp":/g, '"t":');

      return compressed;
    } catch (error) {
      this.logger.warn('⚠️ Compression failed', { error: error.message });
      return JSON.stringify(data);
    }
  }

  /**
   * Decompress data
   * @private
   */
  decompress(compressedData) {
    try {
      // Reverse compression
      const decompressed = compressedData
        .replace(/"v":/g, '"vector":')
        .replace(/"m":/g, '"metadata":')
        .replace(/"c":/g, '"confidence":')
        .replace(/"mt":/g, '"method":')
        .replace(/"t":/g, '"timestamp":');

      return JSON.parse(decompressed);
    } catch (error) {
      this.logger.warn('⚠️ Decompression failed', { error: error.message });
      return typeof compressedData === 'string' ? JSON.parse(compressedData) : compressedData;
    }
  }

  /**
   * Estimate size of data in bytes
   * @private
   */
  estimateSize(data) {
    try {
      return new Blob([typeof data === 'string' ? data : JSON.stringify(data)]).size;
    } catch (error) {
      // Fallback estimation
      return JSON.stringify(data).length * 2;
    }
  }

  /**
   * Update size tracker
   * @private
   */
  updateSizeTracker(delta) {
    this.sizeTracker = Math.max(0, this.sizeTracker + delta);
    this.stats.totalSize = this.cache.size;
    this.stats.memoryUsage = this.sizeTracker;
  }

  /**
   * Hash function for cache keys
   * @private
   */
  hash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Format bytes for display
   * @private
   */
  formatBytes(bytes) {
    if (bytes === 0) { return '0 B'; }
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Load cache from persistent storage
   * @private
   */
  async loadFromStorage() {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      // Chrome extension storage
      const result = await chrome.storage.local.get([this.config.storageKey]);
      return result[this.config.storageKey];
    } else if (typeof localStorage !== 'undefined') {
      // Browser localStorage
      const stored = localStorage.getItem(this.config.storageKey);
      return stored ? JSON.parse(stored) : null;
    }
    return null;
  }

  /**
   * Save cache to persistent storage
   * @private
   */
  async saveToStorage() {
    try {
      const data = {
        entries: Array.from(this.cache.entries()),
        accessOrder: Array.from(this.accessOrder.entries()),
        sizeTracker: this.sizeTracker,
        timestamp: Date.now()
      };

      if (typeof chrome !== 'undefined' && chrome.storage) {
        // Chrome extension storage
        await chrome.storage.local.set({ [this.config.storageKey]: data });
      } else if (typeof localStorage !== 'undefined') {
        // Browser localStorage
        localStorage.setItem(this.config.storageKey, JSON.stringify(data));
      }

      this.stats.persistentSaves++;
    } catch (error) {
      this.logger.warn('⚠️ Failed to save cache to storage', { error: error.message });
    }
  }

  /**
   * Clear all cache entries
   */
  async clear() {
    this.cache.clear();
    this.accessOrder.clear();
    this.sizeTracker = 0;
    this.stats = {
      hits: 0,
      misses: 0,
      evictions: 0,
      compressions: 0,
      persistentLoads: 0,
      persistentSaves: 0,
      totalSize: 0,
      memoryUsage: 0
    };

    // Clear persistent storage
    if (this.config.persistentStorage) {
      try {
        if (typeof chrome !== 'undefined' && chrome.storage) {
          await chrome.storage.local.remove([this.config.storageKey]);
        } else if (typeof localStorage !== 'undefined') {
          localStorage.removeItem(this.config.storageKey);
        }
      } catch (error) {
        this.logger.warn('⚠️ Failed to clear persistent storage', { error: error.message });
      }
    }

    this.logger.log('🧹 Cache cleared completely');
  }

  /**
   * Get cache statistics
   */
  getStats() {
    const totalRequests = this.stats.hits + this.stats.misses;

    return {
      ...this.stats,
      hitRate: totalRequests > 0 ? this.stats.hits / totalRequests : 0,
      memoryUsageFormatted: this.formatBytes(this.stats.memoryUsage),
      maxMemoryFormatted: this.formatBytes(this.config.maxMemoryMB * 1024 * 1024),
      utilizationPercent: this.config.maxSize > 0 ? (this.stats.totalSize / this.config.maxSize) * 100 : 0
    };
  }

  /**
   * Get cache configuration
   */
  getConfig() {
    return { ...this.config };
  }

  /**
   * Update cache configuration
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    this.logger.log('⚙️ Cache configuration updated', newConfig);
  }

  /**
   * Cleanup expired entries
   */
  async cleanup() {
    const before = this.cache.size;
    const now = Date.now();

    for (const [key, entry] of this.cache) {
      if (now > entry.ttl) {
        this.cache.delete(key);
        this.accessOrder.delete(key);
        this.updateSizeTracker(-entry.size);
      }
    }

    const cleaned = before - this.cache.size;
    if (cleaned > 0) {
      this.logger.log('🧹 Expired entries cleaned', {
        cleaned,
        remaining: this.cache.size
      });
    }

    return cleaned;
  }
}
