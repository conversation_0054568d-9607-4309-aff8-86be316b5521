/**
 * imageUtils - Image processing utilities for OCR preprocessing
 * Implements image enhancement and manipulation functions
 *
 * Features:
 * - Image loading from files
 * - Canvas creation and manipulation
 * - Contrast enhancement
 * - Noise reduction
 * - Image sharpening
 * - Format conversion
 */

export const imageUtils = {
  /**
   * Load image from file
   * @param {File} file - Image file to load
   * @returns {Promise<HTMLImageElement>} - Loaded image element
   */
  async loadImageFromFile(file) {
    return new Promise((resolve, reject) => {
      const img = new Image();

      img.onload = () => {
        URL.revokeObjectURL(img.src);
        resolve(img);
      };

      img.onerror = () => {
        URL.revokeObjectURL(img.src);
        reject(new Error(`Failed to load image: ${file.name}`));
      };

      img.src = URL.createObjectURL(file);
    });
  },

  /**
   * Create canvas from image
   * @param {HTMLImageElement} image - Source image
   * @returns {HTMLCanvasElement} - Canvas with image drawn
   */
  createCanvasFromImage(image) {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    canvas.width = image.naturalWidth || image.width;
    canvas.height = image.naturalHeight || image.height;

    ctx.drawImage(image, 0, 0);

    return canvas;
  },

  /**
   * Enhance image contrast
   * @param {ImageData} imageData - Image data to process
   * @param {number} factor - Contrast factor (1.0 = no change)
   */
  enhanceContrast(imageData, factor = 1.2) {
    const data = imageData.data;

    for (let i = 0; i < data.length; i += 4) {
      // Apply contrast to RGB channels
      data[i] = Math.min(255, Math.max(0, (data[i] - 128) * factor + 128)); // Red
      data[i + 1] = Math.min(255, Math.max(0, (data[i + 1] - 128) * factor + 128)); // Green
      data[i + 2] = Math.min(255, Math.max(0, (data[i + 2] - 128) * factor + 128)); // Blue
      // Alpha channel (i + 3) remains unchanged
    }
  },

  /**
   * Reduce image noise using simple averaging
   * @param {ImageData} imageData - Image data to process
   */
  reduceNoise(imageData) {
    const data = imageData.data;
    const width = imageData.width;
    const height = imageData.height;
    const temp = new Uint8ClampedArray(data);

    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        const idx = (y * width + x) * 4;

        // Average with neighboring pixels for RGB channels
        for (let c = 0; c < 3; c++) {
          const sum = temp[idx + c] +
                     temp[idx - 4 + c] + temp[idx + 4 + c] + // Left, Right
                     temp[idx - width * 4 + c] + temp[idx + width * 4 + c]; // Up, Down
          data[idx + c] = sum / 5;
        }
      }
    }
  },

  /**
   * Sharpen image using convolution kernel
   * @param {ImageData} imageData - Image data to process
   */
  sharpenImage(imageData) {
    const data = imageData.data;
    const width = imageData.width;
    const height = imageData.height;
    const temp = new Uint8ClampedArray(data);

    // Sharpening kernel
    const kernel = [
      0, -1, 0,
      -1, 5, -1,
      0, -1, 0
    ];

    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        const idx = (y * width + x) * 4;

        // Apply kernel to RGB channels
        for (let c = 0; c < 3; c++) {
          let sum = 0;

          for (let ky = -1; ky <= 1; ky++) {
            for (let kx = -1; kx <= 1; kx++) {
              const kernelIdx = (ky + 1) * 3 + (kx + 1);
              const pixelIdx = ((y + ky) * width + (x + kx)) * 4 + c;
              sum += temp[pixelIdx] * kernel[kernelIdx];
            }
          }

          data[idx + c] = Math.min(255, Math.max(0, sum));
        }
      }
    }
  },

  /**
   * Convert image to grayscale
   * @param {ImageData} imageData - Image data to process
   */
  convertToGrayscale(imageData) {
    const data = imageData.data;

    for (let i = 0; i < data.length; i += 4) {
      // Calculate grayscale value using luminance formula
      const gray = Math.round(0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2]);

      data[i] = gray; // Red
      data[i + 1] = gray; // Green
      data[i + 2] = gray; // Blue
      // Alpha channel remains unchanged
    }
  },

  /**
   * Adjust image brightness
   * @param {ImageData} imageData - Image data to process
   * @param {number} adjustment - Brightness adjustment (-255 to 255)
   */
  adjustBrightness(imageData, adjustment = 0) {
    const data = imageData.data;

    for (let i = 0; i < data.length; i += 4) {
      data[i] = Math.min(255, Math.max(0, data[i] + adjustment)); // Red
      data[i + 1] = Math.min(255, Math.max(0, data[i + 1] + adjustment)); // Green
      data[i + 2] = Math.min(255, Math.max(0, data[i + 2] + adjustment)); // Blue
    }
  },

  /**
   * Apply threshold to create binary image
   * @param {ImageData} imageData - Image data to process
   * @param {number} threshold - Threshold value (0-255)
   */
  applyThreshold(imageData, threshold = 128) {
    const data = imageData.data;

    for (let i = 0; i < data.length; i += 4) {
      // Convert to grayscale first
      const gray = Math.round(0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2]);

      // Apply threshold
      const value = gray > threshold ? 255 : 0;

      data[i] = value; // Red
      data[i + 1] = value; // Green
      data[i + 2] = value; // Blue
    }
  },

  /**
   * Resize image maintaining aspect ratio
   * @param {HTMLCanvasElement} canvas - Source canvas
   * @param {number} maxWidth - Maximum width
   * @param {number} maxHeight - Maximum height
   * @returns {HTMLCanvasElement} - Resized canvas
   */
  resizeImage(canvas, maxWidth, maxHeight) {
    const { width, height } = canvas;

    // Calculate new dimensions
    let newWidth = width;
    let newHeight = height;

    if (width > maxWidth) {
      newWidth = maxWidth;
      newHeight = (height * maxWidth) / width;
    }

    if (newHeight > maxHeight) {
      newHeight = maxHeight;
      newWidth = (newWidth * maxHeight) / newHeight;
    }

    // Create new canvas with resized dimensions
    const resizedCanvas = document.createElement('canvas');
    const ctx = resizedCanvas.getContext('2d');

    resizedCanvas.width = newWidth;
    resizedCanvas.height = newHeight;

    ctx.drawImage(canvas, 0, 0, newWidth, newHeight);

    return resizedCanvas;
  },

  /**
   * Get image statistics
   * @param {ImageData} imageData - Image data to analyze
   * @returns {Object} - Image statistics
   */
  getImageStats(imageData) {
    const data = imageData.data;
    let minR = 255, maxR = 0, sumR = 0;
    let minG = 255, maxG = 0, sumG = 0;
    let minB = 255, maxB = 0, sumB = 0;
    const pixelCount = data.length / 4;

    for (let i = 0; i < data.length; i += 4) {
      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];

      minR = Math.min(minR, r);
      maxR = Math.max(maxR, r);
      sumR += r;

      minG = Math.min(minG, g);
      maxG = Math.max(maxG, g);
      sumG += g;

      minB = Math.min(minB, b);
      maxB = Math.max(maxB, b);
      sumB += b;
    }

    return {
      width: imageData.width,
      height: imageData.height,
      pixelCount,
      red: { min: minR, max: maxR, avg: sumR / pixelCount },
      green: { min: minG, max: maxG, avg: sumG / pixelCount },
      blue: { min: minB, max: maxB, avg: sumB / pixelCount }
    };
  },

  /**
   * Convert canvas to blob
   * @param {HTMLCanvasElement} canvas - Canvas to convert
   * @param {string} type - MIME type (default: 'image/png')
   * @param {number} quality - Quality for JPEG (0-1)
   * @returns {Promise<Blob>} - Canvas as blob
   */
  canvasToBlob(canvas, type = 'image/png', quality = 0.92) {
    return new Promise((resolve) => {
      canvas.toBlob(resolve, type, quality);
    });
  }
};

// Export to window object for non-module scripts
if (typeof window !== 'undefined') {
  window.imageUtils = imageUtils;
}

// Local test function
function testImageUtils() {
  console.log('=== imageUtils Local Test ===');

  try {
    // Test 1: Canvas creation
    console.log('Test 1: Canvas creation');
    const testCanvas = document.createElement('canvas');
    testCanvas.width = 100;
    testCanvas.height = 100;
    console.log('✓ Canvas created:', testCanvas.width, 'x', testCanvas.height);

    // Test 2: Image data manipulation
    console.log('\nTest 2: Image data manipulation');
    const ctx = testCanvas.getContext('2d');
    ctx.fillStyle = 'rgb(128, 128, 128)';
    ctx.fillRect(0, 0, 100, 100);

    const imageData = ctx.getImageData(0, 0, 100, 100);
    const originalStats = imageUtils.getImageStats(imageData);
    console.log('✓ Original stats:', originalStats.red.avg.toFixed(1));

    // Test contrast enhancement
    imageUtils.enhanceContrast(imageData, 1.5);
    const enhancedStats = imageUtils.getImageStats(imageData);
    console.log('✓ Enhanced stats:', enhancedStats.red.avg.toFixed(1));

    console.log('\n✅ All imageUtils tests passed!');
    return true;

  } catch (error) {
    console.error('❌ imageUtils test failed:', error);
    return false;
  }
}

// Run test if in browser environment
if (typeof window !== 'undefined' && window.location) {
  // Uncomment to run test
  // testImageUtils();
}
