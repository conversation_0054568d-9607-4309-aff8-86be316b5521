/**
 * Data Formatting Utilities
 * Consolidated data formatting functions from across the codebase
 *
 * Consolidates:
 * - Date formatting from DateGrouping.js
 * - Number formatting utilities
 * - Data transformation functions
 * - Display formatting utilities
 */

import { format, parseISO, isValid, startOfDay, endOfDay } from 'date-fns';

/**
 * Format date for display
 * @param {Date|string|number} date - Date to format
 * @param {Object} options - Formatting options
 * @returns {string} - Formatted date
 */
export function formatDate(date, options = {}) {
  const {
    format: dateFormat = 'yyyy-MM-dd',
    locale = null,
    fallback = 'Invalid date'
  } = options;

  let dateObj;

  // Convert input to Date object
  if (typeof date === 'string') {
    dateObj = parseISO(date);
  } else if (typeof date === 'number') {
    dateObj = new Date(date);
  } else if (date instanceof Date) {
    dateObj = date;
  } else {
    return fallback;
  }

  // Validate date
  if (!isValid(dateObj)) {
    return fallback;
  }

  try {
    return format(dateObj, dateFormat, locale ? { locale } : undefined);
  } catch (error) {
    return fallback;
  }
}

/**
 * Format date range
 * @param {Date|string} startDate - Start date
 * @param {Date|string} endDate - End date
 * @param {Object} options - Formatting options
 * @returns {string} - Formatted date range
 */
export function formatDateRange(startDate, endDate, options = {}) {
  const {
    separator = ' - ',
    sameFormat = 'yyyy-MM-dd',
    differentFormat = 'yyyy-MM-dd',
    fallback = 'Invalid date range'
  } = options;

  const formattedStart = formatDate(startDate, { format: sameFormat, fallback: null });
  const formattedEnd = formatDate(endDate, { format: differentFormat, fallback: null });

  if (!formattedStart || !formattedEnd) {
    return fallback;
  }

  return `${formattedStart}${separator}${formattedEnd}`;
}

/**
 * Format relative date (e.g., "2 days ago", "in 3 hours")
 * @param {Date|string|number} date - Date to format
 * @param {Object} options - Formatting options
 * @returns {string} - Relative date string
 */
export function formatRelativeDate(date, options = {}) {
  const {
    baseDate = new Date(),
    fallback = 'Invalid date'
  } = options;

  let dateObj;
  let baseDateObj;

  // Convert inputs to Date objects
  try {
    if (typeof date === 'string') {
      dateObj = parseISO(date);
    } else if (typeof date === 'number') {
      dateObj = new Date(date);
    } else if (date instanceof Date) {
      dateObj = date;
    } else {
      return fallback;
    }

    if (typeof baseDate === 'string') {
      baseDateObj = parseISO(baseDate);
    } else if (typeof baseDate === 'number') {
      baseDateObj = new Date(baseDate);
    } else {
      baseDateObj = baseDate;
    }

    if (!isValid(dateObj) || !isValid(baseDateObj)) {
      return fallback;
    }
  } catch (error) {
    return fallback;
  }

  const diffMs = dateObj.getTime() - baseDateObj.getTime();
  const diffSeconds = Math.floor(diffMs / 1000);
  const diffMinutes = Math.floor(diffSeconds / 60);
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);

  const isPast = diffMs < 0;
  const absSeconds = Math.abs(diffSeconds);
  const absMinutes = Math.abs(diffMinutes);
  const absHours = Math.abs(diffHours);
  const absDays = Math.abs(diffDays);

  if (absSeconds < 60) {
    return isPast ? 'just now' : 'in a moment';
  } else if (absMinutes < 60) {
    const unit = absMinutes === 1 ? 'minute' : 'minutes';
    return isPast ? `${absMinutes} ${unit} ago` : `in ${absMinutes} ${unit}`;
  } else if (absHours < 24) {
    const unit = absHours === 1 ? 'hour' : 'hours';
    return isPast ? `${absHours} ${unit} ago` : `in ${absHours} ${unit}`;
  } else if (absDays < 30) {
    const unit = absDays === 1 ? 'day' : 'days';
    return isPast ? `${absDays} ${unit} ago` : `in ${absDays} ${unit}`;
  }
  // For longer periods, just show the formatted date
  return formatDate(dateObj, { format: 'yyyy-MM-dd' });

}

/**
 * Format data for table display
 * @param {Array} data - Array of data objects
 * @param {Object} options - Formatting options
 * @returns {Array} - Formatted data array
 */
export function formatTableData(data, options = {}) {
  const {
    columns = [],
    dateFormat = 'yyyy-MM-dd',
    currencyFormat = { currency: 'PLN', locale: 'pl-PL' },
    numberFormat = { locale: 'pl-PL' }
  } = options;

  if (!Array.isArray(data)) {
    return [];
  }

  return data.map(row => {
    const formattedRow = { ...row };

    columns.forEach(column => {
      const { key, type, format: columnFormat } = column;
      const value = row[key];

      if (value === null || value === undefined) {
        formattedRow[key] = '';
        return;
      }

      switch (type) {
        case 'date':
          formattedRow[key] = formatDate(value, {
            format: columnFormat || dateFormat
          });
          break;

        case 'currency':
          formattedRow[key] = formatCurrency(value, {
            ...currencyFormat,
            ...columnFormat
          });
          break;

        case 'number':
          formattedRow[key] = formatNumber(value, {
            ...numberFormat,
            ...columnFormat
          });
          break;

        case 'percentage':
          formattedRow[key] = formatPercentage(value, columnFormat);
          break;

        case 'text':
          formattedRow[key] = String(value);
          break;

        default:
          // Keep original value
          break;
      }
    });

    return formattedRow;
  });
}

/**
 * Format summary statistics
 * @param {Array} data - Data array
 * @param {Object} options - Formatting options
 * @returns {Object} - Formatted summary statistics
 */
export function formatSummaryStats(data, options = {}) {
  const {
    fields = [],
    currencyFormat = { currency: 'PLN', locale: 'pl-PL' },
    numberFormat = { locale: 'pl-PL' }
  } = options;

  if (!Array.isArray(data) || data.length === 0) {
    return {};
  }

  const stats = {};

  fields.forEach(field => {
    const { key, type, operations = ['sum', 'avg', 'count'] } = field;
    const values = data.map(item => item[key]).filter(val =>
      val !== null && val !== undefined && !isNaN(val)
    );

    if (values.length === 0) {
      stats[key] = { count: 0 };
      return;
    }

    const fieldStats = {};

    if (operations.includes('count')) {
      fieldStats.count = values.length;
    }

    if (operations.includes('sum')) {
      const sum = values.reduce((acc, val) => acc + Number(val), 0);
      fieldStats.sum = type === 'currency'
        ? formatCurrency(sum, currencyFormat)
        : formatNumber(sum, numberFormat);
      fieldStats.sumRaw = sum;
    }

    if (operations.includes('avg')) {
      const avg = values.reduce((acc, val) => acc + Number(val), 0) / values.length;
      fieldStats.avg = type === 'currency'
        ? formatCurrency(avg, currencyFormat)
        : formatNumber(avg, numberFormat);
      fieldStats.avgRaw = avg;
    }

    if (operations.includes('min')) {
      const min = Math.min(...values.map(Number));
      fieldStats.min = type === 'currency'
        ? formatCurrency(min, currencyFormat)
        : formatNumber(min, numberFormat);
      fieldStats.minRaw = min;
    }

    if (operations.includes('max')) {
      const max = Math.max(...values.map(Number));
      fieldStats.max = type === 'currency'
        ? formatCurrency(max, currencyFormat)
        : formatNumber(max, numberFormat);
      fieldStats.maxRaw = max;
    }

    stats[key] = fieldStats;
  });

  return stats;
}

/**
 * Format data for chart display
 * @param {Array} data - Data array
 * @param {Object} options - Chart formatting options
 * @returns {Object} - Formatted chart data
 */
export function formatChartData(data, options = {}) {
  const {
    xField = 'date',
    yField = 'value',
    groupField = null,
    xFormat = null,
    yFormat = null,
    sortBy = xField,
    sortOrder = 'asc'
  } = options;

  if (!Array.isArray(data)) {
    return { labels: [], datasets: [] };
  }

  // Sort data
  const sortedData = [...data].sort((a, b) => {
    const aVal = a[sortBy];
    const bVal = b[sortBy];

    if (sortOrder === 'desc') {
      return bVal > aVal ? 1 : -1;
    }
    return aVal > bVal ? 1 : -1;

  });

  if (!groupField) {
    // Simple chart data
    const labels = sortedData.map(item => {
      const value = item[xField];
      return xFormat ? formatValue(value, xFormat) : value;
    });

    const values = sortedData.map(item => {
      const value = item[yField];
      return yFormat ? formatValue(value, yFormat) : value;
    });

    return {
      labels,
      datasets: [{
        data: values,
        rawData: sortedData
      }]
    };
  }
  // Grouped chart data
  const groups = {};

  sortedData.forEach(item => {
    const group = item[groupField];
    if (!groups[group]) {
      groups[group] = [];
    }
    groups[group].push(item);
  });

  const labels = [...new Set(sortedData.map(item => {
    const value = item[xField];
    return xFormat ? formatValue(value, xFormat) : value;
  }))];

  const datasets = Object.keys(groups).map(groupName => ({
    label: groupName,
    data: labels.map(label => {
      const item = groups[groupName].find(item => {
        const value = item[xField];
        const formattedValue = xFormat ? formatValue(value, xFormat) : value;
        return formattedValue === label;
      });

      if (item) {
        const value = item[yField];
        return yFormat ? formatValue(value, yFormat) : value;
      }
      return 0;
    }),
    rawData: groups[groupName]
  }));

  return { labels, datasets };

}

/**
 * Helper function to format a value based on format type
 * @param {any} value - Value to format
 * @param {Object} formatOptions - Format options
 * @returns {any} - Formatted value
 */
function formatValue(value, formatOptions) {
  const { type, ...options } = formatOptions;

  switch (type) {
    case 'date':
      return formatDate(value, options);
    case 'currency':
      return formatCurrency(value, options);
    case 'number':
      return formatNumber(value, options);
    case 'percentage':
      return formatPercentage(value, options);
    default:
      return value;
  }
}

// Re-export currency and number formatters for convenience
export { formatCurrency, formatNumber, formatPercentage } from '../string/formatters.js';
