/**
 * File Formatting Utilities
 * Consolidated file formatting functions from across the codebase
 *
 * Consolidates:
 * - formatFileSize from pdfUtils.js
 * - File size formatting from other utilities
 * - File type detection and formatting
 * - File metadata formatting
 */

/**
 * Format file size for display
 * Consolidated from multiple implementations across the codebase
 * @param {number} bytes - File size in bytes
 * @param {Object} options - Formatting options
 * @returns {string} - Formatted file size
 */
export function formatFileSize(bytes, options = {}) {
  const {
    decimals = 2,
    binary = false, // Use 1024 vs 1000
    units = null // Custom units array
  } = options;

  if (bytes === 0) { return '0 Bytes'; }
  if (bytes < 0) { return 'Invalid size'; }

  const k = binary ? 1024 : 1000;
  const defaultUnits = binary
    ? ['Bytes', 'KiB', 'MiB', 'GiB', 'TiB', 'PiB']
    : ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB'];

  const sizes = units || defaultUnits;
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  if (i >= sizes.length) {
    return `${(bytes / Math.pow(k, sizes.length - 1)).toFixed(decimals)} ${sizes[sizes.length - 1]}`;
  }

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(decimals))} ${sizes[i]}`;
}

/**
 * Format file type for display
 * @param {string} mimeType - MIME type
 * @param {string} fileName - File name (optional)
 * @returns {string} - Human-readable file type
 */
export function formatFileType(mimeType, fileName = '') {
  const typeMap = {
    'application/pdf': 'PDF Document',
    'image/jpeg': 'JPEG Image',
    'image/jpg': 'JPEG Image',
    'image/png': 'PNG Image',
    'image/gif': 'GIF Image',
    'image/tiff': 'TIFF Image',
    'image/bmp': 'BMP Image',
    'image/webp': 'WebP Image',
    'text/plain': 'Text File',
    'text/csv': 'CSV File',
    'application/json': 'JSON File',
    'application/xml': 'XML File',
    'application/zip': 'ZIP Archive',
    'application/x-rar-compressed': 'RAR Archive'
  };

  // Try MIME type first
  if (typeMap[mimeType]) {
    return typeMap[mimeType];
  }

  // Fallback to file extension
  if (fileName) {
    const extension = fileName.toLowerCase().split('.').pop();
    const extensionMap = {
      'pdf': 'PDF Document',
      'jpg': 'JPEG Image',
      'jpeg': 'JPEG Image',
      'png': 'PNG Image',
      'gif': 'GIF Image',
      'tiff': 'TIFF Image',
      'tif': 'TIFF Image',
      'bmp': 'BMP Image',
      'webp': 'WebP Image',
      'txt': 'Text File',
      'csv': 'CSV File',
      'json': 'JSON File',
      'xml': 'XML File',
      'zip': 'ZIP Archive',
      'rar': 'RAR Archive'
    };

    if (extensionMap[extension]) {
      return extensionMap[extension];
    }
  }

  return mimeType || 'Unknown File Type';
}

/**
 * Format file metadata for display
 * @param {File} file - File object
 * @param {Object} options - Formatting options
 * @returns {Object} - Formatted file metadata
 */
export function formatFileMetadata(file, options = {}) {
  const {
    includeSize = true,
    includeType = true,
    includeLastModified = true,
    sizeOptions = {}
  } = options;

  const metadata = {
    name: file.name
  };

  if (includeSize) {
    metadata.size = formatFileSize(file.size, sizeOptions);
    metadata.sizeBytes = file.size;
  }

  if (includeType) {
    metadata.type = formatFileType(file.type, file.name);
    metadata.mimeType = file.type;
  }

  if (includeLastModified && file.lastModified) {
    metadata.lastModified = new Date(file.lastModified).toLocaleString();
    metadata.lastModifiedTimestamp = file.lastModified;
  }

  return metadata;
}

/**
 * Format processing time estimate
 * Consolidated from multiple processing time estimation functions
 * @param {number} timeMs - Time in milliseconds
 * @param {Object} options - Formatting options
 * @returns {string} - Formatted time estimate
 */
export function formatProcessingTime(timeMs, options = {}) {
  const {
    precision = 'auto', // 'auto', 'seconds', 'minutes'
    showUnits = true
  } = options;

  if (timeMs < 0) { return 'Invalid time'; }
  if (timeMs === 0) { return showUnits ? '0 seconds' : '0'; }

  const seconds = timeMs / 1000;
  const minutes = seconds / 60;
  const hours = minutes / 60;

  if (precision === 'auto') {
    if (seconds < 60) {
      const rounded = Math.round(seconds * 10) / 10;
      return showUnits ? `${rounded} second${rounded !== 1 ? 's' : ''}` : rounded.toString();
    } else if (minutes < 60) {
      const rounded = Math.round(minutes * 10) / 10;
      return showUnits ? `${rounded} minute${rounded !== 1 ? 's' : ''}` : rounded.toString();
    }
    const rounded = Math.round(hours * 10) / 10;
    return showUnits ? `${rounded} hour${rounded !== 1 ? 's' : ''}` : rounded.toString();

  } else if (precision === 'seconds') {
    const rounded = Math.round(seconds * 10) / 10;
    return showUnits ? `${rounded} second${rounded !== 1 ? 's' : ''}` : rounded.toString();
  } else if (precision === 'minutes') {
    const rounded = Math.round(minutes * 10) / 10;
    return showUnits ? `${rounded} minute${rounded !== 1 ? 's' : ''}` : rounded.toString();
  }

  return timeMs.toString();
}

/**
 * Format file upload progress
 * @param {number} loaded - Bytes loaded
 * @param {number} total - Total bytes
 * @param {Object} options - Formatting options
 * @returns {Object} - Formatted progress information
 */
export function formatUploadProgress(loaded, total, options = {}) {
  const {
    includePercentage = true,
    includeSize = true,
    includeSpeed = false,
    startTime = null
  } = options;

  const progress = {
    loaded,
    total,
    percentage: total > 0 ? Math.round((loaded / total) * 100) : 0
  };

  if (includePercentage) {
    progress.percentageText = `${progress.percentage}%`;
  }

  if (includeSize) {
    progress.loadedText = formatFileSize(loaded);
    progress.totalText = formatFileSize(total);
    progress.sizeText = `${progress.loadedText} / ${progress.totalText}`;
  }

  if (includeSpeed && startTime) {
    const elapsedMs = Date.now() - startTime;
    const elapsedSeconds = elapsedMs / 1000;

    if (elapsedSeconds > 0) {
      const bytesPerSecond = loaded / elapsedSeconds;
      progress.speed = formatFileSize(bytesPerSecond) + '/s';

      // Estimate remaining time
      const remainingBytes = total - loaded;
      const remainingSeconds = remainingBytes / bytesPerSecond;
      progress.estimatedTimeRemaining = formatProcessingTime(remainingSeconds * 1000);
    }
  }

  return progress;
}

/**
 * Format file validation errors
 * @param {Array} errors - Array of validation errors
 * @param {Object} options - Formatting options
 * @returns {string} - Formatted error message
 */
export function formatValidationErrors(errors, options = {}) {
  const {
    separator = '; ',
    maxErrors = 5,
    includeCount = true
  } = options;

  if (!errors || !Array.isArray(errors) || errors.length === 0) {
    return '';
  }

  const displayErrors = errors.slice(0, maxErrors);
  let message = displayErrors.join(separator);

  if (errors.length > maxErrors) {
    const remaining = errors.length - maxErrors;
    message += `${separator}and ${remaining} more error${remaining !== 1 ? 's' : ''}`;
  }

  if (includeCount && errors.length > 1) {
    message = `${errors.length} errors: ${message}`;
  }

  return message;
}

/**
 * Format file extension
 * @param {string} fileName - File name
 * @param {Object} options - Formatting options
 * @returns {string} - Formatted extension
 */
export function formatFileExtension(fileName, options = {}) {
  const {
    includeDot = true,
    uppercase = false,
    lowercase = true
  } = options;

  if (!fileName || typeof fileName !== 'string') {
    return '';
  }

  const parts = fileName.split('.');
  if (parts.length < 2) {
    return '';
  }

  let extension = parts.pop();

  if (lowercase) {
    extension = extension.toLowerCase();
  } else if (uppercase) {
    extension = extension.toUpperCase();
  }

  return includeDot ? `.${extension}` : extension;
}
