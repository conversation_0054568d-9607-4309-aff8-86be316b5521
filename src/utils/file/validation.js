/**
 * File Validation Utilities
 * Consolidated file validation functions from across the codebase
 *
 * Consolidates:
 * - isPDFFile from pdfUtils.js
 * - validatePDFFile from pdfUtils.js
 * - File validation from various components
 * - MIME type validation
 * - File size validation
 */

/**
 * Check if a file is a valid PDF
 * Consolidated from pdfUtils.js
 * @param {File} file - File to check
 * @returns {boolean} - Whether file is a valid PDF
 */
export function isPDFFile(file) {
  if (!file) { return false; }

  // Check MIME type
  if (file.type === 'application/pdf') { return true; }

  // Check file extension as fallback
  const fileName = file.name.toLowerCase();
  return fileName.endsWith('.pdf');
}

/**
 * Check if a file is a valid image
 * @param {File} file - File to check
 * @param {Array} allowedTypes - Allowed image MIME types
 * @returns {boolean} - Whether file is a valid image
 */
export function isImageFile(file, allowedTypes = null) {
  if (!file) { return false; }

  const defaultImageTypes = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/tiff',
    'image/bmp',
    'image/webp'
  ];

  const types = allowedTypes || defaultImageTypes;

  // Check MIME type
  if (types.includes(file.type)) { return true; }

  // Check file extension as fallback
  const fileName = file.name.toLowerCase();
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.tiff', '.tif', '.bmp', '.webp'];

  return imageExtensions.some(ext => fileName.endsWith(ext));
}

/**
 * Validate file size
 * @param {File} file - File to validate
 * @param {Object} options - Validation options
 * @returns {Object} - Validation result
 */
export function validateFileSize(file, options = {}) {
  const {
    maxSize = 10 * 1024 * 1024, // 10MB default
    minSize = 1024, // 1KB default
    unit = 'bytes' // 'bytes', 'kb', 'mb', 'gb'
  } = options;

  const errors = [];
  const warnings = [];

  if (!file) {
    errors.push('No file provided');
    return { isValid: false, errors, warnings };
  }

  // Convert size limits based on unit
  let maxBytes = maxSize;
  let minBytes = minSize;

  switch (unit.toLowerCase()) {
    case 'kb':
      maxBytes = maxSize * 1024;
      minBytes = minSize * 1024;
      break;
    case 'mb':
      maxBytes = maxSize * 1024 * 1024;
      minBytes = minSize * 1024 * 1024;
      break;
    case 'gb':
      maxBytes = maxSize * 1024 * 1024 * 1024;
      minBytes = minSize * 1024 * 1024 * 1024;
      break;
  }

  // Size validation
  if (file.size > maxBytes) {
    const maxSizeFormatted = formatSizeForDisplay(maxBytes);
    const fileSizeFormatted = formatSizeForDisplay(file.size);
    errors.push(`File size (${fileSizeFormatted}) exceeds maximum allowed size (${maxSizeFormatted})`);
  }

  if (file.size < minBytes) {
    const minSizeFormatted = formatSizeForDisplay(minBytes);
    const fileSizeFormatted = formatSizeForDisplay(file.size);
    errors.push(`File size (${fileSizeFormatted}) is below minimum required size (${minSizeFormatted})`);
  }

  // Warnings for large files
  const warningThreshold = maxBytes * 0.8; // 80% of max size
  if (file.size > warningThreshold && file.size <= maxBytes) {
    warnings.push('File is quite large and may take longer to process');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    fileSize: file.size,
    maxSize: maxBytes,
    minSize: minBytes
  };
}

/**
 * Validate file type
 * @param {File} file - File to validate
 * @param {Object} options - Validation options
 * @returns {Object} - Validation result
 */
export function validateFileType(file, options = {}) {
  const {
    allowedTypes = [],
    allowedExtensions = [],
    strictMimeCheck = false
  } = options;

  const errors = [];
  const warnings = [];

  if (!file) {
    errors.push('No file provided');
    return { isValid: false, errors, warnings };
  }

  // MIME type validation
  if (allowedTypes.length > 0) {
    if (!allowedTypes.includes(file.type)) {
      if (strictMimeCheck) {
        errors.push(`Invalid file type: ${file.type}. Allowed types: ${allowedTypes.join(', ')}`);
      } else {
        warnings.push(`File MIME type (${file.type}) not in preferred list`);
      }
    }
  }

  // Extension validation
  if (allowedExtensions.length > 0) {
    const fileName = file.name.toLowerCase();
    const hasValidExtension = allowedExtensions.some(ext =>
      fileName.endsWith(ext.toLowerCase())
    );

    if (!hasValidExtension) {
      errors.push(`Invalid file extension. Allowed extensions: ${allowedExtensions.join(', ')}`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    fileType: file.type,
    fileName: file.name
  };
}

/**
 * Comprehensive file validation
 * Consolidated from validatePDFFile and other validation functions
 * @param {File} file - File to validate
 * @param {Object} options - Validation options
 * @returns {Object} - Comprehensive validation result
 */
export function validateFile(file, options = {}) {
  const {
    maxSize = 10 * 1024 * 1024, // 10MB
    minSize = 1024, // 1KB
    allowedTypes = [],
    allowedExtensions = [],
    strictMimeCheck = false,
    customValidators = []
  } = options;

  const errors = [];
  const warnings = [];

  // Basic file check
  if (!file) {
    errors.push('No file provided');
    return { isValid: false, errors, warnings };
  }

  // Size validation
  const sizeValidation = validateFileSize(file, { maxSize, minSize });
  errors.push(...sizeValidation.errors);
  warnings.push(...sizeValidation.warnings);

  // Type validation
  const typeValidation = validateFileType(file, {
    allowedTypes,
    allowedExtensions,
    strictMimeCheck
  });
  errors.push(...typeValidation.errors);
  warnings.push(...typeValidation.warnings);

  // Custom validators
  for (const validator of customValidators) {
    try {
      const result = validator(file);
      if (result.errors) { errors.push(...result.errors); }
      if (result.warnings) { warnings.push(...result.warnings); }
    } catch (error) {
      errors.push(`Custom validation failed: ${error.message}`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    file: {
      name: file.name,
      size: file.size,
      type: file.type,
      lastModified: file.lastModified
    }
  };
}

/**
 * Validate multiple files
 * @param {FileList|Array} files - Files to validate
 * @param {Object} options - Validation options
 * @returns {Object} - Validation results for all files
 */
export function validateFiles(files, options = {}) {
  const {
    maxFiles = 10,
    totalMaxSize = 50 * 1024 * 1024, // 50MB total
    ...fileOptions
  } = options;

  const results = [];
  const errors = [];
  const warnings = [];
  let totalSize = 0;

  // Convert FileList to Array if needed
  const fileArray = Array.from(files);

  // Check file count
  if (fileArray.length > maxFiles) {
    errors.push(`Too many files: ${fileArray.length}. Maximum allowed: ${maxFiles}`);
  }

  // Validate each file
  for (let i = 0; i < fileArray.length; i++) {
    const file = fileArray[i];
    const fileResult = validateFile(file, fileOptions);

    results.push({
      index: i,
      file: file.name,
      ...fileResult
    });

    totalSize += file.size;

    // Collect errors and warnings
    if (fileResult.errors.length > 0) {
      errors.push(`File "${file.name}": ${fileResult.errors.join(', ')}`);
    }
    if (fileResult.warnings.length > 0) {
      warnings.push(`File "${file.name}": ${fileResult.warnings.join(', ')}`);
    }
  }

  // Check total size
  if (totalSize > totalMaxSize) {
    const totalSizeFormatted = formatSizeForDisplay(totalSize);
    const maxSizeFormatted = formatSizeForDisplay(totalMaxSize);
    errors.push(`Total file size (${totalSizeFormatted}) exceeds maximum allowed (${maxSizeFormatted})`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    results,
    summary: {
      totalFiles: fileArray.length,
      totalSize,
      validFiles: results.filter(r => r.isValid).length,
      invalidFiles: results.filter(r => !r.isValid).length
    }
  };
}

/**
 * Helper function to format file size for display
 * @param {number} bytes - Size in bytes
 * @returns {string} - Formatted size
 */
function formatSizeForDisplay(bytes) {
  if (bytes === 0) { return '0 Bytes'; }

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Create a PDF-specific validator
 * @param {Object} options - PDF validation options
 * @returns {Function} - PDF validator function
 */
export function createPDFValidator(options = {}) {
  const {
    maxSize = 10 * 1024 * 1024, // 10MB
    minSize = 1024, // 1KB
    strictValidation = true
  } = options;

  return function validatePDF(file) {
    const errors = [];
    const warnings = [];

    // PDF-specific type check
    if (!isPDFFile(file)) {
      errors.push('File is not a valid PDF');
    }

    // Size validation
    const sizeValidation = validateFileSize(file, { maxSize, minSize });
    errors.push(...sizeValidation.errors);
    warnings.push(...sizeValidation.warnings);

    // Additional PDF-specific checks
    if (strictValidation) {
      // Check for proper PDF MIME type
      if (file.type !== 'application/pdf') {
        warnings.push('File does not have proper PDF MIME type');
      }

      // Check file name
      if (!file.name.toLowerCase().endsWith('.pdf')) {
        warnings.push('File does not have .pdf extension');
      }
    }

    return { errors, warnings };
  };
}

/**
 * Create an image-specific validator
 * @param {Object} options - Image validation options
 * @returns {Function} - Image validator function
 */
export function createImageValidator(options = {}) {
  const {
    maxSize = 5 * 1024 * 1024, // 5MB
    minSize = 1024, // 1KB
    allowedTypes = ['image/jpeg', 'image/png', 'image/tiff'],
    maxDimensions = null // { width: 4000, height: 4000 }
  } = options;

  return function validateImage(file) {
    const errors = [];
    const warnings = [];

    // Image-specific type check
    if (!isImageFile(file, allowedTypes)) {
      errors.push(`File is not a valid image. Allowed types: ${allowedTypes.join(', ')}`);
    }

    // Size validation
    const sizeValidation = validateFileSize(file, { maxSize, minSize });
    errors.push(...sizeValidation.errors);
    warnings.push(...sizeValidation.warnings);

    // Note: Dimension validation would require loading the image
    // This could be added as an async validator if needed
    if (maxDimensions) {
      warnings.push('Image dimension validation not implemented in sync validator');
    }

    return { errors, warnings };
  };
}
