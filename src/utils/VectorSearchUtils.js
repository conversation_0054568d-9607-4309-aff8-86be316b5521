/**
 * Vector Search Utilities
 *
 * Advanced utility functions for vector search operations, similarity calculations,
 * and performance optimization for large-scale document collections.
 *
 * <AUTHOR> Agent
 * @version 1.0.0
 * @since 2025-06-14
 */

/**
 * Calculate cosine similarity between two vectors
 * @param {Array} vector1 - First vector
 * @param {Array} vector2 - Second vector
 * @returns {number} Cosine similarity score (0-1)
 */
export function cosineSimilarity(vector1, vector2) {
  if (!vector1 || !vector2 || vector1.length !== vector2.length) {
    return 0;
  }

  let dotProduct = 0;
  let magnitude1 = 0;
  let magnitude2 = 0;

  for (let i = 0; i < vector1.length; i++) {
    dotProduct += vector1[i] * vector2[i];
    magnitude1 += vector1[i] * vector1[i];
    magnitude2 += vector2[i] * vector2[i];
  }

  magnitude1 = Math.sqrt(magnitude1);
  magnitude2 = Math.sqrt(magnitude2);

  if (magnitude1 === 0 || magnitude2 === 0) {
    return 0;
  }

  return dotProduct / (magnitude1 * magnitude2);
}

/**
 * Calculate Euclidean distance between two vectors
 * @param {Array} vector1 - First vector
 * @param {Array} vector2 - Second vector
 * @returns {number} Euclidean distance
 */
export function euclideanDistance(vector1, vector2) {
  if (!vector1 || !vector2 || vector1.length !== vector2.length) {
    return Infinity;
  }

  let sum = 0;
  for (let i = 0; i < vector1.length; i++) {
    const diff = vector1[i] - vector2[i];
    sum += diff * diff;
  }

  return Math.sqrt(sum);
}

/**
 * Calculate dot product of two vectors
 * @param {Array} vector1 - First vector
 * @param {Array} vector2 - Second vector
 * @returns {number} Dot product
 */
export function dotProduct(vector1, vector2) {
  if (!vector1 || !vector2 || vector1.length !== vector2.length) {
    return 0;
  }

  let product = 0;
  for (let i = 0; i < vector1.length; i++) {
    product += vector1[i] * vector2[i];
  }

  return product;
}

/**
 * Normalize a vector to unit length
 * @param {Array} vector - Input vector
 * @returns {Array} Normalized vector
 */
export function normalizeVector(vector) {
  if (!vector || vector.length === 0) {
    return [];
  }

  const magnitude = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0));

  if (magnitude === 0) {
    return vector.slice(); // Return copy of zero vector
  }

  return vector.map(val => val / magnitude);
}

/**
 * Validate vector format and content
 * @param {Array} vector - Vector to validate
 * @returns {boolean} True if valid
 */
export function validateVector(vector) {
  if (!Array.isArray(vector) || vector.length === 0) {
    return false;
  }

  return vector.every(val => typeof val === 'number' && !isNaN(val) && isFinite(val));
}

/**
 * Find K nearest neighbors using brute force search
 * @param {Array} queryVector - Query vector
 * @param {Array} vectors - Array of {id, vector, metadata} objects
 * @param {number} k - Number of neighbors to find
 * @param {string} metric - Distance metric ('cosine', 'euclidean', 'dot')
 * @returns {Array} K nearest neighbors with distances
 */
export function findKNearestNeighbors(queryVector, vectors, k = 10, metric = 'cosine') {
  if (!validateVector(queryVector) || !vectors || vectors.length === 0) {
    return [];
  }

  const similarities = [];
  const normalizedQuery = metric === 'cosine' ? normalizeVector(queryVector) : queryVector;

  for (const vectorData of vectors) {
    if (!validateVector(vectorData.vector)) {
      continue;
    }

    let similarity;
    const targetVector = metric === 'cosine' ? normalizeVector(vectorData.vector) : vectorData.vector;

    switch (metric) {
      case 'cosine': {
        similarity = cosineSimilarity(normalizedQuery, targetVector);
        break;
      }
      case 'euclidean': {
        const distance = euclideanDistance(normalizedQuery, targetVector);
        similarity = 1 / (1 + distance); // Convert distance to similarity
        break;
      }
      case 'dot': {
        similarity = dotProduct(normalizedQuery, targetVector);
        break;
      }
      default: {
        similarity = cosineSimilarity(normalizedQuery, targetVector);
      }
    }

    similarities.push({
      id: vectorData.id,
      similarity,
      distance: 1 - similarity,
      metadata: vectorData.metadata,
      vector: vectorData.vector
    });
  }

  // Sort by similarity (descending) and return top k
  return similarities
    .sort((a, b) => b.similarity - a.similarity)
    .slice(0, k);
}

/**
 * Calculate vector centroid from multiple vectors
 * @param {Array} vectors - Array of vectors
 * @returns {Array} Centroid vector
 */
export function calculateCentroid(vectors) {
  if (!vectors || vectors.length === 0) {
    return [];
  }

  const dimension = vectors[0].length;
  const centroid = new Array(dimension).fill(0);

  for (const vector of vectors) {
    if (vector.length !== dimension) {
      continue; // Skip vectors with different dimensions
    }

    for (let i = 0; i < dimension; i++) {
      centroid[i] += vector[i];
    }
  }

  // Average the values
  for (let i = 0; i < dimension; i++) {
    centroid[i] /= vectors.length;
  }

  return centroid;
}

/**
 * Perform K-means clustering on vectors
 * @param {Array} vectors - Array of {id, vector, metadata} objects
 * @param {number} k - Number of clusters
 * @param {number} maxIterations - Maximum iterations
 * @returns {Object} Clustering result with assignments and centroids
 */
export function kMeansClustering(vectors, k = 5, maxIterations = 100) {
  if (!vectors || vectors.length === 0 || k <= 0) {
    return { clusters: [], centroids: [], assignments: new Map() };
  }

  const dimension = vectors[0].vector.length;

  // Initialize centroids randomly
  let centroids = [];
  for (let i = 0; i < k; i++) {
    const randomVector = vectors[Math.floor(Math.random() * vectors.length)].vector;
    centroids.push([...randomVector]); // Copy vector
  }

  let assignments = new Map();
  let converged = false;
  let iteration = 0;

  while (!converged && iteration < maxIterations) {
    const newAssignments = new Map();

    // Assign each vector to nearest centroid
    for (const vectorData of vectors) {
      let bestCluster = 0;
      let bestSimilarity = -1;

      for (let c = 0; c < k; c++) {
        const similarity = cosineSimilarity(vectorData.vector, centroids[c]);
        if (similarity > bestSimilarity) {
          bestSimilarity = similarity;
          bestCluster = c;
        }
      }

      newAssignments.set(vectorData.id, bestCluster);
    }

    // Check for convergence
    converged = true;
    for (const [id, cluster] of newAssignments) {
      if (assignments.get(id) !== cluster) {
        converged = false;
        break;
      }
    }

    assignments = newAssignments;

    // Update centroids
    if (!converged) {
      const clusterVectors = Array.from({ length: k }, () => []);

      for (const vectorData of vectors) {
        const cluster = assignments.get(vectorData.id);
        clusterVectors[cluster].push(vectorData.vector);
      }

      for (let c = 0; c < k; c++) {
        if (clusterVectors[c].length > 0) {
          centroids[c] = calculateCentroid(clusterVectors[c]);
        }
      }
    }

    iteration++;
  }

  // Organize results
  const clusters = Array.from({ length: k }, () => []);
  for (const vectorData of vectors) {
    const cluster = assignments.get(vectorData.id);
    clusters[cluster].push(vectorData);
  }

  return {
    clusters,
    centroids,
    assignments,
    iterations: iteration,
    converged
  };
}

/**
 * Calculate silhouette score for clustering quality
 * @param {Array} vectors - Array of {id, vector, metadata} objects
 * @param {Map} assignments - Cluster assignments
 * @param {Array} centroids - Cluster centroids
 * @returns {number} Average silhouette score
 */
export function calculateSilhouetteScore(vectors, assignments, centroids) {
  if (!vectors || vectors.length === 0 || !assignments || !centroids) {
    return 0;
  }

  let totalScore = 0;
  let validPoints = 0;

  for (const vectorData of vectors) {
    const cluster = assignments.get(vectorData.id);
    const sameClusterVectors = vectors.filter(v => assignments.get(v.id) === cluster);

    if (sameClusterVectors.length <= 1) {
      continue; // Skip singleton clusters
    }

    // Calculate average distance to same cluster
    let intraClusterDistance = 0;
    for (const other of sameClusterVectors) {
      if (other.id !== vectorData.id) {
        intraClusterDistance += 1 - cosineSimilarity(vectorData.vector, other.vector);
      }
    }
    intraClusterDistance /= (sameClusterVectors.length - 1);

    // Calculate minimum average distance to other clusters
    let minInterClusterDistance = Infinity;
    for (let c = 0; c < centroids.length; c++) {
      if (c === cluster) {
        continue;
      }

      const otherClusterVectors = vectors.filter(v => assignments.get(v.id) === c);
      if (otherClusterVectors.length === 0) {
        continue;
      }

      let interClusterDistance = 0;
      for (const other of otherClusterVectors) {
        interClusterDistance += 1 - cosineSimilarity(vectorData.vector, other.vector);
      }
      interClusterDistance /= otherClusterVectors.length;

      minInterClusterDistance = Math.min(minInterClusterDistance, interClusterDistance);
    }

    if (minInterClusterDistance !== Infinity) {
      const silhouette = (minInterClusterDistance - intraClusterDistance) /
                        Math.max(intraClusterDistance, minInterClusterDistance);
      totalScore += silhouette;
      validPoints++;
    }
  }

  return validPoints > 0 ? totalScore / validPoints : 0;
}
