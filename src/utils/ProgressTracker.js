/**
 * Progress Tracker Utility
 * Provides progress calculation and tracking functionality
 *
 * Features:
 * - Multi-stage progress calculation
 * - Time estimation algorithms
 * - Performance monitoring
 * - Memory efficient tracking
 * - Configurable stage weights
 * - Real-time progress updates
 */

/**
 * Default stage configurations for file processing
 */
const DEFAULT_STAGES = {
  uploading: { weight: 0.2, name: 'Uploading', description: 'Transferring file...' },
  validating: { weight: 0.1, name: 'Validating', description: 'Checking file format and security...' },
  processing: { weight: 0.3, name: 'Processing', description: 'Preparing for analysis...' },
  extracting: { weight: 0.2, name: 'Extracting', description: 'Reading document content...' },
  analyzing: { weight: 0.2, name: 'Analyzing', description: 'Extracting structured data...' }
};

/**
 * Progress Tracker Class
 * Manages progress calculation for multi-stage operations
 */
class ProgressTracker {
  constructor(stages = DEFAULT_STAGES) {
    this.stages = stages;
    this.stageOrder = Object.keys(stages);
    this.currentStage = 0;
    this.stageProgress = 0;
    this.startTime = null;
    this.stageStartTimes = {};
    this.stageDurations = {};
    this.estimatedTotalTime = null;
  }

  /**
   * Start tracking progress
   */
  start() {
    this.startTime = Date.now();
    this.currentStage = 0;
    this.stageProgress = 0;
    this.stageStartTimes = {};
    this.stageDurations = {};
    this.estimatedTotalTime = null;

    // Start first stage
    if (this.stageOrder.length > 0) {
      this.stageStartTimes[this.stageOrder[0]] = Date.now();
    }
  }

  /**
   * Update progress for current stage
   * @param {number} progress - Progress percentage (0-100) for current stage
   */
  updateStageProgress(progress) {
    this.stageProgress = Math.min(Math.max(progress, 0), 100);
    return this.calculateOverallProgress();
  }

  /**
   * Move to next stage
   * @param {string} stageName - Optional stage name to move to
   */
  nextStage(stageName = null) {
    const currentStageName = this.stageOrder[this.currentStage];

    // Record completion time for current stage
    if (currentStageName && this.stageStartTimes[currentStageName]) {
      this.stageDurations[currentStageName] = Date.now() - this.stageStartTimes[currentStageName];
    }

    // Move to next stage
    if (stageName) {
      const stageIndex = this.stageOrder.indexOf(stageName);
      if (stageIndex !== -1) {
        this.currentStage = stageIndex;
      }
    } else {
      this.currentStage = Math.min(this.currentStage + 1, this.stageOrder.length - 1);
    }

    // Start timing for new stage
    const newStageName = this.stageOrder[this.currentStage];
    if (newStageName) {
      this.stageStartTimes[newStageName] = Date.now();
    }

    this.stageProgress = 0;
    return this.calculateOverallProgress();
  }

  /**
   * Calculate overall progress percentage
   * @returns {number} Overall progress (0-100)
   */
  calculateOverallProgress() {
    let totalProgress = 0;

    // Add completed stages
    for (let i = 0; i < this.currentStage; i++) {
      const stageName = this.stageOrder[i];
      totalProgress += (this.stages[stageName]?.weight || 0) * 100;
    }

    // Add current stage progress
    if (this.currentStage < this.stageOrder.length) {
      const currentStageName = this.stageOrder[this.currentStage];
      const stageWeight = this.stages[currentStageName]?.weight || 0;
      totalProgress += stageWeight * this.stageProgress;
    }

    return Math.min(Math.max(totalProgress, 0), 100);
  }

  /**
   * Get current stage information
   * @returns {Object} Current stage details
   */
  getCurrentStage() {
    if (this.currentStage >= this.stageOrder.length) {
      return {
        name: 'complete',
        displayName: 'Complete',
        description: 'Processing completed',
        progress: 100,
        weight: 0
      };
    }

    const stageName = this.stageOrder[this.currentStage];
    const stage = this.stages[stageName];

    return {
      name: stageName,
      displayName: stage?.name || stageName,
      description: stage?.description || '',
      progress: this.stageProgress,
      weight: stage?.weight || 0
    };
  }

  /**
   * Estimate remaining time based on completed stages
   * @returns {number|null} Estimated remaining time in seconds
   */
  estimateRemainingTime() {
    if (!this.startTime || this.currentStage === 0) {
      return null;
    }

    const elapsedTime = (Date.now() - this.startTime) / 1000;
    const overallProgress = this.calculateOverallProgress();

    if (overallProgress <= 0) {
      return null;
    }

    // Calculate estimated total time
    const estimatedTotal = (elapsedTime / overallProgress) * 100;
    const remainingTime = estimatedTotal - elapsedTime;

    return Math.max(remainingTime, 0);
  }

  /**
   * Get performance metrics
   * @returns {Object} Performance data
   */
  getPerformanceMetrics() {
    const currentTime = Date.now();
    const totalElapsed = this.startTime ? (currentTime - this.startTime) / 1000 : 0;
    const overallProgress = this.calculateOverallProgress();

    return {
      totalElapsedTime: totalElapsed,
      overallProgress,
      estimatedRemainingTime: this.estimateRemainingTime(),
      stageDurations: { ...this.stageDurations },
      currentStage: this.getCurrentStage(),
      averageStageTime: this.getAverageStageTime(),
      progressRate: overallProgress > 0 ? overallProgress / totalElapsed : 0
    };
  }

  /**
   * Calculate average time per stage
   * @returns {number} Average stage duration in seconds
   */
  getAverageStageTime() {
    const completedStages = Object.values(this.stageDurations);
    if (completedStages.length === 0) {
      return 0;
    }

    const totalTime = completedStages.reduce((sum, duration) => sum + duration, 0);
    return (totalTime / completedStages.length) / 1000; // Convert to seconds
  }

  /**
   * Check if processing is complete
   * @returns {boolean} True if all stages completed
   */
  isComplete() {
    return this.currentStage >= this.stageOrder.length - 1 && this.stageProgress >= 100;
  }

  /**
   * Reset tracker to initial state
   */
  reset() {
    this.currentStage = 0;
    this.stageProgress = 0;
    this.startTime = null;
    this.stageStartTimes = {};
    this.stageDurations = {};
    this.estimatedTotalTime = null;
  }

  /**
   * Create a progress update callback
   * @param {Function} onUpdate - Callback function to receive progress updates
   * @returns {Function} Progress update function
   */
  createProgressCallback(onUpdate) {
    return (progress, stageName = null) => {
      if (stageName && stageName !== this.stageOrder[this.currentStage]) {
        this.nextStage(stageName);
      }

      const overallProgress = this.updateStageProgress(progress);
      const metrics = this.getPerformanceMetrics();

      if (onUpdate) {
        onUpdate({
          overallProgress,
          stageProgress: progress,
          currentStage: this.getCurrentStage(),
          metrics,
          isComplete: this.isComplete()
        });
      }

      return overallProgress;
    };
  }
}

/**
 * Utility functions for progress calculation
 */

/**
 * Calculate file processing time estimate based on file characteristics
 * @param {File} file - File object
 * @returns {number} Estimated processing time in seconds
 */
export function estimateFileProcessingTime(file) {
  if (!file) { return 0; }

  const sizeInMB = file.size / (1024 * 1024);
  const fileType = file.type || '';

  // Base time estimates (seconds per MB)
  const timeEstimates = {
    'application/pdf': 2, // PDF processing
    'image/jpeg': 5, // OCR processing
    'image/png': 5, // OCR processing
    'image/tiff': 8, // Complex OCR
    default: 3
  };

  const baseTimePerMB = timeEstimates[fileType] || timeEstimates.default;
  const estimatedTime = Math.max(sizeInMB * baseTimePerMB, 1); // Minimum 1 second

  // Add complexity factors
  let complexityMultiplier = 1;

  if (fileType.startsWith('image/')) {
    complexityMultiplier = 1.5; // OCR is more complex
  }

  if (sizeInMB > 5) {
    complexityMultiplier *= 1.2; // Large files take proportionally longer
  }

  return Math.round(estimatedTime * complexityMultiplier);
}

/**
 * Create a progress tracker for multiple files
 * @param {Array} files - Array of file objects
 * @param {Object} options - Configuration options
 * @returns {Object} Multi-file progress tracker
 */
export function createMultiFileProgressTracker(files, options = {}) {
  const { onProgress, stages = DEFAULT_STAGES } = options;

  const fileTrackers = files.map(file => ({
    file,
    tracker: new ProgressTracker(stages),
    estimatedTime: estimateFileProcessingTime(file),
    completed: false
  }));

  let overallProgress = 0;

  const updateOverallProgress = () => {
    const totalProgress = fileTrackers.reduce((sum, { tracker }) => {
      return sum + tracker.calculateOverallProgress();
    }, 0);

    overallProgress = fileTrackers.length > 0 ? totalProgress / fileTrackers.length : 0;

    if (onProgress) {
      onProgress({
        overallProgress,
        fileProgress: fileTrackers.map(({ file, tracker }) => ({
          file,
          progress: tracker.calculateOverallProgress(),
          stage: tracker.getCurrentStage(),
          metrics: tracker.getPerformanceMetrics()
        })),
        completedFiles: fileTrackers.filter(f => f.completed).length,
        totalFiles: fileTrackers.length
      });
    }

    return overallProgress;
  };

  return {
    fileTrackers,
    updateOverallProgress,
    getOverallProgress: () => overallProgress,
    getFileTracker: (fileIndex) => fileTrackers[fileIndex]?.tracker,
    markFileComplete: (fileIndex) => {
      if (fileTrackers[fileIndex]) {
        fileTrackers[fileIndex].completed = true;
        updateOverallProgress();
      }
    }
  };
}

export default ProgressTracker;
export { DEFAULT_STAGES };
