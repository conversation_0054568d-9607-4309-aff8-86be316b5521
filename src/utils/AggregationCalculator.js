/**
 * AggregationCalculator - Statistical calculation utilities for invoice data
 * Provides efficient aggregation functions with proper error handling
 * Supports various statistical operations and edge cases
 *
 * Epic: EPIC-003 - Data Display & Visualization
 * Story: STORY-3.2 - Grouping & Aggregation
 * Task: TASK-3.2.1 - Grouping Logic
 */

export class AggregationCalculator {
  constructor() {
    this.precision = 2; // Decimal places for results
    this.supportedOperations = ['sum', 'count', 'average', 'min', 'max', 'median', 'mode', 'variance', 'stddev'];
  }

  /**
   * Configure aggregation calculator
   * @param {Object} config - Configuration options
   */
  configure(config = {}) {
    if (config.precision !== undefined) {
      this.precision = config.precision;
    }
  }

  /**
   * Calculate aggregation for a set of values
   * @param {Array} values - Array of numeric values
   * @param {string} operation - Aggregation operation
   * @returns {number|null} - Calculated result
   */
  calculate(values, operation) {
    if (!Array.isArray(values)) {
      throw new Error('Values must be an array');
    }

    if (!this.supportedOperations.includes(operation)) {
      throw new Error(`Unsupported operation: ${operation}`);
    }

    // Filter out non-numeric values
    const numericValues = values
      .map(val => parseFloat(val))
      .filter(val => !isNaN(val));

    if (numericValues.length === 0) {
      return null;
    }

    switch (operation) {
      case 'sum':
        return this.roundResult(this.calculateSum(numericValues));
      case 'count':
        return numericValues.length;
      case 'average':
        return this.roundResult(this.calculateAverage(numericValues));
      case 'min':
        return this.roundResult(this.calculateMin(numericValues));
      case 'max':
        return this.roundResult(this.calculateMax(numericValues));
      case 'median':
        return this.roundResult(this.calculateMedian(numericValues));
      case 'mode':
        return this.calculateMode(numericValues);
      case 'variance':
        return this.roundResult(this.calculateVariance(numericValues));
      case 'stddev':
        return this.roundResult(this.calculateStandardDeviation(numericValues));
      default:
        throw new Error(`Operation ${operation} not implemented`);
    }
  }

  /**
   * Calculate sum of values
   * @param {Array} values - Numeric values
   * @returns {number} - Sum
   */
  calculateSum(values) {
    return values.reduce((sum, value) => sum + value, 0);
  }

  /**
   * Calculate average of values
   * @param {Array} values - Numeric values
   * @returns {number} - Average
   */
  calculateAverage(values) {
    if (values.length === 0) { return 0; }
    return this.calculateSum(values) / values.length;
  }

  /**
   * Calculate minimum value
   * @param {Array} values - Numeric values
   * @returns {number} - Minimum
   */
  calculateMin(values) {
    return Math.min(...values);
  }

  /**
   * Calculate maximum value
   * @param {Array} values - Numeric values
   * @returns {number} - Maximum
   */
  calculateMax(values) {
    return Math.max(...values);
  }

  /**
   * Calculate median value
   * @param {Array} values - Numeric values
   * @returns {number} - Median
   */
  calculateMedian(values) {
    if (values.length === 0) { return 0; }

    const sorted = [...values].sort((a, b) => a - b);
    const middle = Math.floor(sorted.length / 2);

    if (sorted.length % 2 === 0) {
      return (sorted[middle - 1] + sorted[middle]) / 2;
    }
    return sorted[middle];

  }

  /**
   * Calculate mode (most frequent value)
   * @param {Array} values - Numeric values
   * @returns {number|Array} - Mode value(s)
   */
  calculateMode(values) {
    if (values.length === 0) { return null; }

    const frequency = {};
    let maxFreq = 0;

    // Count frequencies
    for (const value of values) {
      frequency[value] = (frequency[value] || 0) + 1;
      maxFreq = Math.max(maxFreq, frequency[value]);
    }

    // Find all values with maximum frequency
    const modes = Object.keys(frequency)
      .filter(key => frequency[key] === maxFreq)
      .map(key => parseFloat(key));

    // Return single mode or array of modes
    return modes.length === 1 ? modes[0] : modes;
  }

  /**
   * Calculate variance
   * @param {Array} values - Numeric values
   * @returns {number} - Variance
   */
  calculateVariance(values) {
    if (values.length === 0) { return 0; }
    if (values.length === 1) { return 0; }

    const mean = this.calculateAverage(values);
    const squaredDifferences = values.map(value => Math.pow(value - mean, 2));

    return this.calculateSum(squaredDifferences) / (values.length - 1); // Sample variance
  }

  /**
   * Calculate standard deviation
   * @param {Array} values - Numeric values
   * @returns {number} - Standard deviation
   */
  calculateStandardDeviation(values) {
    return Math.sqrt(this.calculateVariance(values));
  }

  /**
   * Calculate multiple aggregations at once
   * @param {Array} values - Numeric values
   * @param {Array} operations - Array of operations to perform
   * @returns {Object} - Object with operation results
   */
  calculateMultiple(values, operations) {
    const results = {};

    for (const operation of operations) {
      try {
        results[operation] = this.calculate(values, operation);
      } catch (error) {
        results[operation] = null;
        console.warn(`Failed to calculate ${operation}:`, error.message);
      }
    }

    return results;
  }

  /**
   * Calculate percentiles
   * @param {Array} values - Numeric values
   * @param {Array} percentiles - Array of percentile values (0-100)
   * @returns {Object} - Percentile results
   */
  calculatePercentiles(values, percentiles = [25, 50, 75, 90, 95, 99]) {
    if (!Array.isArray(values) || values.length === 0) {
      return {};
    }

    const numericValues = values
      .map(val => parseFloat(val))
      .filter(val => !isNaN(val))
      .sort((a, b) => a - b);

    if (numericValues.length === 0) {
      return {};
    }

    const results = {};

    for (const percentile of percentiles) {
      if (percentile < 0 || percentile > 100) {
        continue;
      }

      const index = (percentile / 100) * (numericValues.length - 1);

      if (Number.isInteger(index)) {
        results[`p${percentile}`] = this.roundResult(numericValues[index]);
      } else {
        const lower = Math.floor(index);
        const upper = Math.ceil(index);
        const weight = index - lower;

        results[`p${percentile}`] = this.roundResult(
          numericValues[lower] * (1 - weight) + numericValues[upper] * weight
        );
      }
    }

    return results;
  }

  /**
   * Calculate quartiles (Q1, Q2, Q3)
   * @param {Array} values - Numeric values
   * @returns {Object} - Quartile results
   */
  calculateQuartiles(values) {
    const percentiles = this.calculatePercentiles(values, [25, 50, 75]);

    return {
      q1: percentiles.p25,
      q2: percentiles.p50, // Same as median
      q3: percentiles.p75,
      iqr: percentiles.p75 - percentiles.p25 // Interquartile range
    };
  }

  /**
   * Calculate summary statistics
   * @param {Array} values - Numeric values
   * @returns {Object} - Complete statistical summary
   */
  calculateSummary(values) {
    if (!Array.isArray(values) || values.length === 0) {
      return this.getEmptySummary();
    }

    const numericValues = values
      .map(val => parseFloat(val))
      .filter(val => !isNaN(val));

    if (numericValues.length === 0) {
      return this.getEmptySummary();
    }

    const basicStats = this.calculateMultiple(numericValues, [
      'count', 'sum', 'average', 'min', 'max', 'median', 'variance', 'stddev'
    ]);

    const quartiles = this.calculateQuartiles(numericValues);
    const percentiles = this.calculatePercentiles(numericValues);

    return {
      ...basicStats,
      ...quartiles,
      ...percentiles,
      range: basicStats.max - basicStats.min,
      coefficientOfVariation: basicStats.average !== 0 ? basicStats.stddev / basicStats.average : 0
    };
  }

  /**
   * Get empty summary for edge cases
   * @returns {Object} - Empty summary object
   */
  getEmptySummary() {
    return {
      count: 0,
      sum: 0,
      average: 0,
      min: 0,
      max: 0,
      median: 0,
      variance: 0,
      stddev: 0,
      q1: 0,
      q2: 0,
      q3: 0,
      iqr: 0,
      range: 0,
      coefficientOfVariation: 0
    };
  }

  /**
   * Round result to configured precision
   * @param {number} value - Value to round
   * @returns {number} - Rounded value
   */
  roundResult(value) {
    if (typeof value !== 'number' || isNaN(value)) {
      return 0;
    }

    return Math.round(value * Math.pow(10, this.precision)) / Math.pow(10, this.precision);
  }

  /**
   * Validate aggregation inputs
   * @param {Array} values - Values to validate
   * @param {string} operation - Operation to validate
   * @returns {Object} - Validation result
   */
  validateInputs(values, operation) {
    const errors = [];

    if (!Array.isArray(values)) {
      errors.push('Values must be an array');
    }

    if (typeof operation !== 'string') {
      errors.push('Operation must be a string');
    }

    if (operation && !this.supportedOperations.includes(operation)) {
      errors.push(`Unsupported operation: ${operation}`);
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Get supported operations
   * @returns {Array} - Array of supported operation names
   */
  getSupportedOperations() {
    return [...this.supportedOperations];
  }
}
