/**
 * PDF Performance Utilities
 * Supporting utilities for PDF processing performance optimization
 *
 * Features:
 * - Memory management and monitoring
 * - Performance benchmarking
 * - Resource optimization
 * - Batch processing utilities
 * - Error recovery mechanisms
 */

/**
 * Performance monitor for PDF processing operations
 */
export class PDFPerformanceMonitor {
  constructor() {
    this.metrics = new Map();
    this.thresholds = {
      memoryWarning: 50 * 1024 * 1024, // 50MB
      memoryCritical: 100 * 1024 * 1024, // 100MB
      processingTimeWarning: 30000, // 30 seconds
      processingTimeCritical: 60000 // 60 seconds
    };
  }

  /**
   * Start monitoring a PDF processing operation
   * @param {string} operationId - Unique operation identifier
   * @param {Object} options - Monitoring options
   * @returns {Object} - Monitor instance
   */
  startMonitoring(operationId, options = {}) {
    const monitor = {
      operationId,
      startTime: Date.now(),
      startMemory: this.getCurrentMemoryUsage(),
      checkpoints: [],
      warnings: [],
      options: {
        trackMemory: options.trackMemory !== false,
        trackTime: options.trackTime !== false,
        interval: options.interval || 1000,
        ...options
      }
    };

    this.metrics.set(operationId, monitor);

    // Start periodic monitoring if enabled
    if (monitor.options.trackMemory || monitor.options.trackTime) {
      monitor.intervalId = setInterval(() => {
        this.recordCheckpoint(operationId);
      }, monitor.options.interval);
    }

    return {
      checkpoint: (label) => this.recordCheckpoint(operationId, label),
      warning: (message) => this.addWarning(operationId, message),
      stop: () => this.stopMonitoring(operationId),
      getMetrics: () => this.getMetrics(operationId)
    };
  }

  /**
   * Record a checkpoint during processing
   * @param {string} operationId - Operation identifier
   * @param {string} label - Optional checkpoint label
   */
  recordCheckpoint(operationId, label = null) {
    const monitor = this.metrics.get(operationId);
    if (!monitor) { return; }

    const checkpoint = {
      timestamp: Date.now(),
      elapsed: Date.now() - monitor.startTime,
      memory: this.getCurrentMemoryUsage(),
      label
    };

    monitor.checkpoints.push(checkpoint);

    // Check thresholds
    this.checkThresholds(operationId, checkpoint);
  }

  /**
   * Add a warning to the monitoring session
   * @param {string} operationId - Operation identifier
   * @param {string} message - Warning message
   */
  addWarning(operationId, message) {
    const monitor = this.metrics.get(operationId);
    if (!monitor) { return; }

    monitor.warnings.push({
      timestamp: Date.now(),
      message
    });
  }

  /**
   * Stop monitoring an operation
   * @param {string} operationId - Operation identifier
   * @returns {Object} - Final metrics
   */
  stopMonitoring(operationId) {
    const monitor = this.metrics.get(operationId);
    if (!monitor) { return null; }

    // Clear interval if running
    if (monitor.intervalId) {
      clearInterval(monitor.intervalId);
    }

    // Record final checkpoint
    this.recordCheckpoint(operationId, 'final');

    const finalMetrics = this.calculateFinalMetrics(monitor);

    // Keep metrics for a short time for analysis
    setTimeout(() => {
      this.metrics.delete(operationId);
    }, 60000); // 1 minute

    return finalMetrics;
  }

  /**
   * Get current metrics for an operation
   * @param {string} operationId - Operation identifier
   * @returns {Object} - Current metrics
   */
  getMetrics(operationId) {
    const monitor = this.metrics.get(operationId);
    if (!monitor) { return null; }

    return {
      operationId,
      elapsed: Date.now() - monitor.startTime,
      checkpoints: monitor.checkpoints.length,
      warnings: monitor.warnings.length,
      currentMemory: this.getCurrentMemoryUsage(),
      memoryDelta: this.getCurrentMemoryUsage() - monitor.startMemory,
      peakMemory: this.getPeakMemory(monitor)
    };
  }

  /**
   * Check performance thresholds
   * @param {string} operationId - Operation identifier
   * @param {Object} checkpoint - Current checkpoint
   */
  checkThresholds(operationId, checkpoint) {
    const monitor = this.metrics.get(operationId);
    if (!monitor) { return; }

    // Check memory thresholds
    if (checkpoint.memory > this.thresholds.memoryCritical) {
      this.addWarning(operationId, `Critical memory usage: ${this.formatMemory(checkpoint.memory)}`);
    } else if (checkpoint.memory > this.thresholds.memoryWarning) {
      this.addWarning(operationId, `High memory usage: ${this.formatMemory(checkpoint.memory)}`);
    }

    // Check processing time thresholds
    if (checkpoint.elapsed > this.thresholds.processingTimeCritical) {
      this.addWarning(operationId, `Critical processing time: ${this.formatTime(checkpoint.elapsed)}`);
    } else if (checkpoint.elapsed > this.thresholds.processingTimeWarning) {
      this.addWarning(operationId, `Long processing time: ${this.formatTime(checkpoint.elapsed)}`);
    }
  }

  /**
   * Calculate final metrics for a completed operation
   * @param {Object} monitor - Monitor data
   * @returns {Object} - Final metrics
   */
  calculateFinalMetrics(monitor) {
    const totalTime = Date.now() - monitor.startTime;
    const peakMemory = this.getPeakMemory(monitor);
    const memoryDelta = this.getCurrentMemoryUsage() - monitor.startMemory;

    return {
      operationId: monitor.operationId,
      totalTime,
      totalTimeFormatted: this.formatTime(totalTime),
      startMemory: monitor.startMemory,
      peakMemory,
      memoryDelta,
      peakMemoryFormatted: this.formatMemory(peakMemory),
      memoryDeltaFormatted: this.formatMemory(memoryDelta),
      checkpoints: monitor.checkpoints.length,
      warnings: monitor.warnings,
      performance: this.assessPerformance(totalTime, peakMemory, monitor.warnings.length)
    };
  }

  /**
   * Get peak memory usage from monitor
   * @param {Object} monitor - Monitor data
   * @returns {number} - Peak memory usage
   */
  getPeakMemory(monitor) {
    if (!monitor.checkpoints.length) { return monitor.startMemory; }

    return Math.max(
      monitor.startMemory,
      ...monitor.checkpoints.map(cp => cp.memory)
    );
  }

  /**
   * Assess overall performance
   * @param {number} totalTime - Total processing time
   * @param {number} peakMemory - Peak memory usage
   * @param {number} warningCount - Number of warnings
   * @returns {Object} - Performance assessment
   */
  assessPerformance(totalTime, peakMemory, warningCount) {
    let score = 100;
    const issues = [];

    // Time penalty
    if (totalTime > this.thresholds.processingTimeCritical) {
      score -= 30;
      issues.push('Very slow processing');
    } else if (totalTime > this.thresholds.processingTimeWarning) {
      score -= 15;
      issues.push('Slow processing');
    }

    // Memory penalty
    if (peakMemory > this.thresholds.memoryCritical) {
      score -= 25;
      issues.push('High memory usage');
    } else if (peakMemory > this.thresholds.memoryWarning) {
      score -= 10;
      issues.push('Moderate memory usage');
    }

    // Warning penalty
    score -= warningCount * 5;
    if (warningCount > 0) {
      issues.push(`${warningCount} warnings`);
    }

    score = Math.max(0, score);

    let rating;
    if (score >= 90) { rating = 'Excellent'; } else if (score >= 75) { rating = 'Good'; } else if (score >= 60) { rating = 'Fair'; } else if (score >= 40) { rating = 'Poor'; } else { rating = 'Critical'; }

    return {
      score,
      rating,
      issues
    };
  }

  /**
   * Get current memory usage
   * @returns {number} - Memory usage in bytes
   */
  getCurrentMemoryUsage() {
    if (typeof performance !== 'undefined' && performance.memory) {
      return performance.memory.usedJSHeapSize;
    }
    return 0;
  }

  /**
   * Format memory size for display
   * @param {number} bytes - Memory in bytes
   * @returns {string} - Formatted memory size
   */
  formatMemory(bytes) {
    if (bytes === 0) { return '0 B'; }

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  }

  /**
   * Format time duration for display
   * @param {number} ms - Time in milliseconds
   * @returns {string} - Formatted time
   */
  formatTime(ms) {
    if (ms < 1000) { return `${ms}ms`; }
    if (ms < 60000) { return `${(ms / 1000).toFixed(1)}s`; }
    return `${Math.floor(ms / 60000)}m ${Math.floor((ms % 60000) / 1000)}s`;
  }
}

/**
 * Memory optimization utilities
 */
export class MemoryOptimizer {
  constructor() {
    this.gcAvailable = typeof window !== 'undefined' && window.gc;
    this.cleanupCallbacks = new Set();
  }

  /**
   * Execute function with memory optimization
   * @param {Function} fn - Function to execute
   * @param {Object} options - Optimization options
   * @returns {Promise} - Function result
   */
  async withOptimization(fn, options = {}) {
    const {
      forceGC = true,
      monitorMemory = true,
      cleanupInterval = 5000
    } = options;

    let cleanupTimer;

    try {
      // Force garbage collection before starting
      if (forceGC && this.gcAvailable) {
        window.gc();
      }

      // Start periodic cleanup if requested
      if (cleanupInterval > 0) {
        cleanupTimer = setInterval(() => {
          this.performCleanup();
        }, cleanupInterval);
      }

      // Execute the function
      const result = await fn();

      return result;

    } finally {
      // Clean up
      if (cleanupTimer) {
        clearInterval(cleanupTimer);
      }

      // Final garbage collection
      if (forceGC && this.gcAvailable) {
        window.gc();
      }
    }
  }

  /**
   * Register a cleanup callback
   * @param {Function} callback - Cleanup function
   */
  registerCleanup(callback) {
    this.cleanupCallbacks.add(callback);
  }

  /**
   * Unregister a cleanup callback
   * @param {Function} callback - Cleanup function
   */
  unregisterCleanup(callback) {
    this.cleanupCallbacks.delete(callback);
  }

  /**
   * Perform cleanup operations
   */
  performCleanup() {
    // Execute all registered cleanup callbacks
    for (const callback of this.cleanupCallbacks) {
      try {
        callback();
      } catch (error) {
        console.warn('Cleanup callback failed:', error);
      }
    }

    // Force garbage collection if available
    if (this.gcAvailable) {
      window.gc();
    }
  }

  /**
   * Check if memory optimization is available
   * @returns {boolean} - Whether optimization features are available
   */
  isOptimizationAvailable() {
    return {
      gc: this.gcAvailable,
      memoryAPI: typeof performance !== 'undefined' && !!performance.memory,
      webWorkers: typeof Worker !== 'undefined'
    };
  }
}

/**
 * Batch processing utilities for multiple PDFs
 */
export class BatchProcessor {
  constructor(options = {}) {
    this.config = {
      maxConcurrent: options.maxConcurrent || 2,
      batchSize: options.batchSize || 5,
      retryAttempts: options.retryAttempts || 3,
      retryDelay: options.retryDelay || 1000,
      ...options
    };

    this.queue = [];
    this.active = new Map();
    this.completed = [];
    this.failed = [];
  }

  /**
   * Add files to the processing queue
   * @param {Array} files - Files to process
   * @param {Function} processor - Processing function
   * @param {Object} options - Processing options
   */
  addFiles(files, processor, options = {}) {
    for (const file of files) {
      const task = {
        id: this.generateTaskId(),
        file,
        processor,
        options,
        attempts: 0,
        status: 'queued',
        addedAt: Date.now()
      };

      this.queue.push(task);
    }
  }

  /**
   * Start batch processing
   * @param {Object} options - Processing options
   * @returns {Promise} - Processing completion promise
   */
  async startProcessing(options = {}) {
    const {
      onProgress = null,
      onTaskComplete = null,
      onTaskError = null
    } = options;

    return new Promise((resolve, reject) => {
      const processNext = async () => {
        // Check if we can start more tasks
        while (this.active.size < this.config.maxConcurrent && this.queue.length > 0) {
          const task = this.queue.shift();
          this.processTask(task, {
            onComplete: (result) => {
              this.active.delete(task.id);
              this.completed.push({ ...task, result, completedAt: Date.now() });

              if (onTaskComplete) { onTaskComplete(task, result); }
              if (onProgress) { this.reportProgress(onProgress); }

              processNext();
            },
            onError: (error) => {
              this.active.delete(task.id);

              if (task.attempts < this.config.retryAttempts) {
                // Retry the task
                task.attempts++;
                task.status = 'retrying';
                setTimeout(() => {
                  this.queue.unshift(task);
                  processNext();
                }, this.config.retryDelay * task.attempts);
              } else {
                // Task failed permanently
                this.failed.push({ ...task, error, failedAt: Date.now() });
                if (onTaskError) { onTaskError(task, error); }
                if (onProgress) { this.reportProgress(onProgress); }
                processNext();
              }
            }
          });
        }

        // Check if all tasks are complete
        if (this.queue.length === 0 && this.active.size === 0) {
          resolve({
            completed: this.completed,
            failed: this.failed,
            totalProcessed: this.completed.length + this.failed.length
          });
        }
      };

      processNext();
    });
  }

  /**
   * Process a single task
   * @param {Object} task - Task to process
   * @param {Object} callbacks - Completion callbacks
   */
  async processTask(task, callbacks) {
    task.status = 'processing';
    task.startedAt = Date.now();
    this.active.set(task.id, task);

    try {
      const result = await task.processor(task.file, task.options);
      callbacks.onComplete(result);
    } catch (error) {
      callbacks.onError(error);
    }
  }

  /**
   * Report processing progress
   * @param {Function} onProgress - Progress callback
   */
  reportProgress(onProgress) {
    const total = this.completed.length + this.failed.length + this.queue.length + this.active.size;
    const processed = this.completed.length + this.failed.length;

    onProgress({
      total,
      processed,
      active: this.active.size,
      queued: this.queue.length,
      completed: this.completed.length,
      failed: this.failed.length,
      progress: total > 0 ? (processed / total) * 100 : 0
    });
  }

  /**
   * Generate a unique task ID
   * @returns {string} - Unique task ID
   */
  generateTaskId() {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get current processing status
   * @returns {Object} - Current status
   */
  getStatus() {
    return {
      queued: this.queue.length,
      active: this.active.size,
      completed: this.completed.length,
      failed: this.failed.length,
      activeTasks: Array.from(this.active.values()).map(task => ({
        id: task.id,
        fileName: task.file.name,
        status: task.status,
        attempts: task.attempts
      }))
    };
  }

  /**
   * Cancel all pending tasks
   */
  cancelAll() {
    this.queue.length = 0;
    // Note: Active tasks will complete naturally
  }
}

// Create singleton instances
export const performanceMonitor = new PDFPerformanceMonitor();
export const memoryOptimizer = new MemoryOptimizer();

// Utility functions
export function createBatchProcessor(options) {
  return new BatchProcessor(options);
}

export function estimateProcessingTime(fileSize, numPages = 1) {
  // Base time: 1 second
  // File size factor: 1 second per MB
  // Page factor: 0.5 seconds per page
  const baseTime = 1000;
  const sizeTime = (fileSize / (1024 * 1024)) * 1000;
  const pageTime = numPages * 500;

  return baseTime + sizeTime + pageTime;
}

export function shouldUseWorker(fileSize, numPages = 1) {
  // Use worker for files larger than 5MB or more than 20 pages
  return fileSize > 5 * 1024 * 1024 || numPages > 20;
}

export function optimizeForLargeFiles(fileSize) {
  return {
    renderScale: fileSize > 10 * 1024 * 1024 ? 1.0 : 2.0,
    maxImageSize: fileSize > 20 * 1024 * 1024 ? 512 * 512 : 1024 * 1024,
    disableFontFace: fileSize > 50 * 1024 * 1024,
    enableStreaming: fileSize > 10 * 1024 * 1024
  };
}
