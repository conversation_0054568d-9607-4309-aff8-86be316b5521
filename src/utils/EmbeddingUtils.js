/**
 * Embedding Utilities
 *
 * Utility functions for document embedding operations, vector manipulation,
 * and similarity calculations for RAG-based document analysis.
 *
 * <AUTHOR> Agent
 * @version 1.0.0
 * @since 2025-06-03
 */

/**
 * Normalize a vector to unit length
 * @param {Array<number>} vector - Input vector
 * @returns {Array<number>} Normalized vector
 */
export function normalizeVector(vector) {
  const magnitude = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0));

  if (magnitude === 0) {
    return vector.slice(); // Return copy of zero vector
  }

  return vector.map(val => val / magnitude);
}

/**
 * Calculate dot product of two vectors
 * @param {Array<number>} vector1 - First vector
 * @param {Array<number>} vector2 - Second vector
 * @returns {number} Dot product
 */
export function dotProduct(vector1, vector2) {
  if (vector1.length !== vector2.length) {
    throw new Error('Vector dimensions must match');
  }

  return vector1.reduce((sum, val, index) => sum + val * vector2[index], 0);
}

/**
 * Calculate Euclidean distance between two vectors
 * @param {Array<number>} vector1 - First vector
 * @param {Array<number>} vector2 - Second vector
 * @returns {number} Euclidean distance
 */
export function euclideanDistance(vector1, vector2) {
  if (vector1.length !== vector2.length) {
    throw new Error('Vector dimensions must match');
  }

  const sumSquaredDiffs = vector1.reduce((sum, val, index) => {
    const diff = val - vector2[index];
    return sum + diff * diff;
  }, 0);

  return Math.sqrt(sumSquaredDiffs);
}

/**
 * Calculate Manhattan distance between two vectors
 * @param {Array<number>} vector1 - First vector
 * @param {Array<number>} vector2 - Second vector
 * @returns {number} Manhattan distance
 */
export function manhattanDistance(vector1, vector2) {
  if (vector1.length !== vector2.length) {
    throw new Error('Vector dimensions must match');
  }

  return vector1.reduce((sum, val, index) => sum + Math.abs(val - vector2[index]), 0);
}

/**
 * Calculate cosine similarity between two vectors
 * @param {Array<number>} vector1 - First vector
 * @param {Array<number>} vector2 - Second vector
 * @returns {number} Cosine similarity (-1 to 1)
 */
export function cosineSimilarity(vector1, vector2) {
  if (vector1.length !== vector2.length) {
    throw new Error('Vector dimensions must match');
  }

  const dot = dotProduct(vector1, vector2);
  const magnitude1 = Math.sqrt(dotProduct(vector1, vector1));
  const magnitude2 = Math.sqrt(dotProduct(vector2, vector2));

  if (magnitude1 === 0 || magnitude2 === 0) {
    return 0;
  }

  return dot / (magnitude1 * magnitude2);
}

/**
 * Calculate Jaccard similarity for binary vectors
 * @param {Array<number>} vector1 - First binary vector
 * @param {Array<number>} vector2 - Second binary vector
 * @returns {number} Jaccard similarity (0 to 1)
 */
export function jaccardSimilarity(vector1, vector2) {
  if (vector1.length !== vector2.length) {
    throw new Error('Vector dimensions must match');
  }

  let intersection = 0;
  let union = 0;

  for (let i = 0; i < vector1.length; i++) {
    const val1 = vector1[i] > 0 ? 1 : 0;
    const val2 = vector2[i] > 0 ? 1 : 0;

    if (val1 === 1 && val2 === 1) {
      intersection++;
    }

    if (val1 === 1 || val2 === 1) {
      union++;
    }
  }

  return union === 0 ? 0 : intersection / union;
}

/**
 * Create a zero vector of specified dimension
 * @param {number} dimension - Vector dimension
 * @returns {Array<number>} Zero vector
 */
export function createZeroVector(dimension) {
  return new Array(dimension).fill(0);
}

/**
 * Create a random vector of specified dimension
 * @param {number} dimension - Vector dimension
 * @param {number} min - Minimum value (default: -1)
 * @param {number} max - Maximum value (default: 1)
 * @returns {Array<number>} Random vector
 */
export function createRandomVector(dimension, min = -1, max = 1) {
  return Array.from({ length: dimension }, () => Math.random() * (max - min) + min);
}

/**
 * Add two vectors element-wise
 * @param {Array<number>} vector1 - First vector
 * @param {Array<number>} vector2 - Second vector
 * @returns {Array<number>} Sum vector
 */
export function addVectors(vector1, vector2) {
  if (vector1.length !== vector2.length) {
    throw new Error('Vector dimensions must match');
  }

  return vector1.map((val, index) => val + vector2[index]);
}

/**
 * Subtract two vectors element-wise
 * @param {Array<number>} vector1 - First vector
 * @param {Array<number>} vector2 - Second vector
 * @returns {Array<number>} Difference vector
 */
export function subtractVectors(vector1, vector2) {
  if (vector1.length !== vector2.length) {
    throw new Error('Vector dimensions must match');
  }

  return vector1.map((val, index) => val - vector2[index]);
}

/**
 * Multiply vector by scalar
 * @param {Array<number>} vector - Input vector
 * @param {number} scalar - Scalar multiplier
 * @returns {Array<number>} Scaled vector
 */
export function scaleVector(vector, scalar) {
  return vector.map(val => val * scalar);
}

/**
 * Calculate vector magnitude (L2 norm)
 * @param {Array<number>} vector - Input vector
 * @returns {number} Vector magnitude
 */
export function vectorMagnitude(vector) {
  return Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0));
}

/**
 * Calculate average of multiple vectors
 * @param {Array<Array<number>>} vectors - Array of vectors
 * @returns {Array<number>} Average vector
 */
export function averageVectors(vectors) {
  if (vectors.length === 0) {
    throw new Error('Cannot average empty vector array');
  }

  const dimension = vectors[0].length;
  const sum = createZeroVector(dimension);

  for (const vector of vectors) {
    if (vector.length !== dimension) {
      throw new Error('All vectors must have same dimension');
    }

    for (let i = 0; i < dimension; i++) {
      sum[i] += vector[i];
    }
  }

  return sum.map(val => val / vectors.length);
}

/**
 * Find k-nearest neighbors using cosine similarity
 * @param {Array<number>} targetVector - Target vector
 * @param {Array<Array<number>>} vectors - Array of candidate vectors
 * @param {number} k - Number of neighbors to find
 * @returns {Array<Object>} Array of {index, similarity} objects
 */
export function findKNearestNeighbors(targetVector, vectors, k = 5) {
  const similarities = vectors.map((vector, index) => ({
    index,
    similarity: cosineSimilarity(targetVector, vector)
  }));

  // Sort by similarity (descending) and take top k
  return similarities
    .sort((a, b) => b.similarity - a.similarity)
    .slice(0, k);
}

/**
 * Validate vector format and values
 * @param {Array<number>} vector - Vector to validate
 * @param {number} expectedDimension - Expected dimension (optional)
 * @returns {boolean} True if valid
 */
export function validateVector(vector, expectedDimension = null) {
  if (!Array.isArray(vector)) {
    return false;
  }

  if (expectedDimension !== null && vector.length !== expectedDimension) {
    return false;
  }

  return vector.every(val => typeof val === 'number' && !isNaN(val) && isFinite(val));
}

/**
 * Convert embedding object to vector array
 * @param {Object} embedding - Embedding object with vector property
 * @returns {Array<number>} Vector array
 */
export function extractVector(embedding) {
  if (!embedding || !embedding.vector) {
    throw new Error('Invalid embedding object');
  }

  if (!validateVector(embedding.vector)) {
    throw new Error('Invalid vector in embedding');
  }

  return embedding.vector;
}

/**
 * Calculate similarity threshold based on vector statistics
 * @param {Array<Array<number>>} vectors - Array of vectors
 * @param {number} percentile - Percentile for threshold (0-100)
 * @returns {number} Calculated threshold
 */
export function calculateSimilarityThreshold(vectors, percentile = 75) {
  if (vectors.length < 2) {
    return 0.7; // Default threshold
  }

  const similarities = [];

  // Calculate all pairwise similarities
  for (let i = 0; i < vectors.length; i++) {
    for (let j = i + 1; j < vectors.length; j++) {
      similarities.push(cosineSimilarity(vectors[i], vectors[j]));
    }
  }

  // Sort and find percentile
  similarities.sort((a, b) => a - b);
  const index = Math.floor((percentile / 100) * similarities.length);

  return similarities[Math.min(index, similarities.length - 1)];
}

/**
 * Compress vector using dimensionality reduction (simple PCA-like)
 * @param {Array<number>} vector - Input vector
 * @param {number} targetDimension - Target dimension
 * @returns {Array<number>} Compressed vector
 */
export function compressVector(vector, targetDimension) {
  if (targetDimension >= vector.length) {
    return vector.slice(); // Return copy if no compression needed
  }

  const step = vector.length / targetDimension;
  const compressed = [];

  for (let i = 0; i < targetDimension; i++) {
    const start = Math.floor(i * step);
    const end = Math.floor((i + 1) * step);

    // Average values in this segment
    let sum = 0;
    for (let j = start; j < end; j++) {
      sum += vector[j];
    }

    compressed.push(sum / (end - start));
  }

  return compressed;
}
