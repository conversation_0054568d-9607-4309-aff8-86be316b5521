/**
 * Polish NIP (Tax ID) Validator
 * Validates and formats Polish NIP numbers according to official rules
 */

/**
 * NIP validation weights for checksum calculation
 */
const NIP_WEIGHTS = [6, 5, 7, 2, 3, 4, 5, 6, 7];

/**
 * Clean NIP number by removing all non-digit characters
 * @param {string} nip - Raw NIP input
 * @returns {string} - Cleaned NIP with only digits
 */
export function cleanNip(nip) {
  if (!nip || typeof nip !== 'string') {
    return '';
  }
  return nip.replace(/\D/g, '');
}

/**
 * Format NIP number with standard Polish formatting (XXX-XXX-XX-XX)
 * @param {string} nip - Clean NIP number (10 digits)
 * @returns {string} - Formatted NIP or original if invalid
 */
export function formatNip(nip) {
  const cleaned = cleanNip(nip);

  if (cleaned.length !== 10) {
    return nip; // Return original if not 10 digits
  }

  return `${cleaned.slice(0, 3)}-${cleaned.slice(3, 6)}-${cleaned.slice(6, 8)}-${cleaned.slice(8, 10)}`;
}

/**
 * Calculate NIP checksum using official algorithm
 * @param {string} nip - Clean 10-digit NIP number
 * @returns {number} - Calculated checksum (0-9)
 */
function calculateNipChecksum(nip) {
  if (nip.length !== 10) {
    throw new Error('NIP must be exactly 10 digits');
  }

  let sum = 0;
  for (let i = 0; i < 9; i++) {
    sum += parseInt(nip[i]) * NIP_WEIGHTS[i];
  }

  const remainder = sum % 11;
  return remainder < 10 ? remainder : 0;
}

/**
 * Validate Polish NIP number
 * @param {string} nip - NIP number to validate
 * @returns {Object} - Validation result with success, error, and formatted NIP
 */
export function validateNip(nip) {
  const result = {
    valid: false,
    error: null,
    formatted: null,
    cleaned: null
  };

  if (!nip || typeof nip !== 'string') {
    result.error = 'NIP is required';
    return result;
  }

  const cleaned = cleanNip(nip);
  result.cleaned = cleaned;

  // Check length
  if (cleaned.length === 0) {
    result.error = 'NIP cannot be empty';
    return result;
  }

  if (cleaned.length !== 10) {
    result.error = 'NIP must contain exactly 10 digits';
    return result;
  }

  // Check for invalid patterns
  if (/^0+$/.test(cleaned)) {
    result.error = 'NIP cannot consist only of zeros';
    return result;
  }

  if (/^(.)\1{9}$/.test(cleaned)) {
    result.error = 'NIP cannot consist of the same digit repeated';
    return result;
  }

  // Validate checksum
  try {
    const expectedChecksum = calculateNipChecksum(cleaned);
    const actualChecksum = parseInt(cleaned[9]);

    if (expectedChecksum !== actualChecksum) {
      result.error = `Invalid NIP checksum. Expected: ${expectedChecksum}, got: ${actualChecksum}`;
      return result;
    }

    // All validations passed
    result.valid = true;
    result.formatted = formatNip(cleaned);

  } catch (error) {
    result.error = `NIP validation error: ${error.message}`;
  }

  return result;
}

/**
 * Check if NIP is valid (simple boolean check)
 * @param {string} nip - NIP number to check
 * @returns {boolean} - True if valid, false otherwise
 */
export function isValidNip(nip) {
  return validateNip(nip).valid;
}

/**
 * Generate example valid NIP for testing/demo purposes
 * @returns {string} - Valid formatted NIP
 */
export function generateExampleNip() {
  // Use a known valid NIP pattern for demonstration
  const baseDigits = '123456780'; // First 9 digits
  const checksum = calculateNipChecksum(baseDigits + '0');
  const fullNip = baseDigits + checksum.toString();
  return formatNip(fullNip);
}

/**
 * Get NIP validation rules for display to users
 * @returns {Array} - Array of validation rule descriptions
 */
export function getNipValidationRules() {
  return [
    'Must contain exactly 10 digits',
    'Cannot consist only of zeros',
    'Cannot consist of the same digit repeated',
    'Must pass official checksum validation',
    'Format: XXX-XXX-XX-XX (dashes optional)'
  ];
}

// Self-test function for development
if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
  console.log('🧪 NIP Validator Self-Test');

  // Test cases
  const testCases = [
    { nip: '123-456-78-90', expected: false, description: 'Invalid checksum' },
    { nip: '1234567890', expected: false, description: 'Invalid checksum (no dashes)' },
    { nip: '0000000000', expected: false, description: 'All zeros' },
    { nip: '1111111111', expected: false, description: 'Same digit repeated' },
    { nip: '12345678', expected: false, description: 'Too short' },
    { nip: '123456789012', expected: false, description: 'Too long' },
    { nip: '', expected: false, description: 'Empty string' },
    { nip: 'abc-def-gh-ij', expected: false, description: 'Non-numeric' }
  ];

  let passed = 0;
  let total = testCases.length;

  testCases.forEach(({ nip, expected, description }) => {
    const result = validateNip(nip);
    const success = result.valid === expected;

    if (success) {
      passed++;
      console.log(`✅ ${description}: ${nip} -> ${result.valid}`);
    } else {
      console.log(`❌ ${description}: ${nip} -> ${result.valid} (expected ${expected})`);
      console.log(`   Error: ${result.error}`);
    }
  });

  console.log(`🧪 NIP Validator Tests: ${passed}/${total} passed`);

  // Test a valid NIP
  const exampleNip = generateExampleNip();
  const exampleResult = validateNip(exampleNip);
  console.log(`📝 Example valid NIP: ${exampleNip} -> ${exampleResult.valid}`);
}
