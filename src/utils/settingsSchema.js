/**
 * Settings Schema Validation
 * Defines and validates the structure of settings data
 */

/**
 * Default settings configuration
 */
export const DEFAULT_SETTINGS = {
  company: {
    name: '',
    taxId: '',
    address: '',
    email: '',
    phone: '',
    logo: ''
  },
  display: {
    groupBy: 'month',
    dateFormat: 'DD/MM/YYYY',
    currency: 'PLN',
    language: 'pl',
    theme: 'light'
  },
  processing: {
    ocrLanguage: 'pol',
    aiProvider: 'deepseek',
    autoProcess: true,
    cacheEnabled: true
  },
  apiKeys: {
    deepseek: '',
    openai: '',
    fakturownia: '',
    infakt: ''
  }
};

/**
 * Settings validation schema
 */
export const SETTINGS_SCHEMA = {
  company: {
    type: 'object',
    required: false,
    properties: {
      name: { type: 'string', maxLength: 200 },
      taxId: { type: 'string', maxLength: 50 },
      address: { type: 'string', maxLength: 500 },
      email: { type: 'string', format: 'email', maxLength: 100 },
      phone: { type: 'string', maxLength: 20 },
      logo: { type: 'string', maxLength: 100000 } // Base64 encoded image
    }
  },
  display: {
    type: 'object',
    required: true,
    properties: {
      groupBy: {
        type: 'string',
        enum: ['year', 'quarter', 'month', 'week'],
        default: 'month'
      },
      dateFormat: {
        type: 'string',
        enum: ['DD/MM/YYYY', 'MM/DD/YYYY', 'YYYY-MM-DD'],
        default: 'DD/MM/YYYY'
      },
      currency: {
        type: 'string',
        enum: ['PLN', 'EUR', 'USD', 'GBP'],
        default: 'PLN'
      },
      language: {
        type: 'string',
        enum: ['pl', 'en'],
        default: 'pl'
      },
      theme: {
        type: 'string',
        enum: ['light', 'dark', 'auto'],
        default: 'light'
      }
    }
  },
  processing: {
    type: 'object',
    required: true,
    properties: {
      ocrLanguage: {
        type: 'string',
        enum: ['pol', 'eng', 'auto'],
        default: 'pol'
      },
      aiProvider: {
        type: 'string',
        enum: ['deepseek', 'openai'],
        default: 'deepseek'
      },
      autoProcess: {
        type: 'boolean',
        default: true
      },
      cacheEnabled: {
        type: 'boolean',
        default: true
      }
    }
  },
  apiKeys: {
    type: 'object',
    required: false,
    properties: {
      deepseek: { type: 'string', maxLength: 200 },
      openai: { type: 'string', maxLength: 200 },
      fakturownia: { type: 'string', maxLength: 200 },
      infakt: { type: 'string', maxLength: 200 }
    }
  }
};

/**
 * Validate settings object against schema
 * @param {Object} settings - Settings to validate
 * @returns {Object} Validation result
 */
export function validateSettings(settings) {
  const errors = [];
  const warnings = [];

  if (!settings || typeof settings !== 'object') {
    return {
      valid: false,
      errors: ['Settings must be an object'],
      warnings: []
    };
  }

  // Validate each section
  for (const [sectionName, sectionSchema] of Object.entries(SETTINGS_SCHEMA)) {
    const sectionData = settings[sectionName];

    // Check if required section exists
    if (sectionSchema.required && !sectionData) {
      errors.push(`Missing required section: ${sectionName}`);
      continue;
    }

    // Skip validation if section doesn't exist and is not required
    if (!sectionData) {
      continue;
    }

    // Validate section type
    if (sectionSchema.type === 'object' && typeof sectionData !== 'object') {
      errors.push(`Section ${sectionName} must be an object`);
      continue;
    }

    // Validate properties
    if (sectionSchema.properties) {
      for (const [propName, propSchema] of Object.entries(sectionSchema.properties)) {
        const propValue = sectionData[propName];
        const propErrors = validateProperty(propValue, propSchema, `${sectionName}.${propName}`);
        errors.push(...propErrors);
      }
    }
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Validate individual property
 * @param {*} value - Property value
 * @param {Object} schema - Property schema
 * @param {string} path - Property path for error messages
 * @returns {Array} Array of error messages
 */
function validateProperty(value, schema, path) {
  const errors = [];

  // Skip validation if value is undefined/null and not required
  if (value == null) {
    return errors;
  }

  // Type validation
  if (schema.type) {
    const actualType = typeof value;
    if (actualType !== schema.type) {
      errors.push(`${path}: expected ${schema.type}, got ${actualType}`);
      return errors; // Skip further validation if type is wrong
    }
  }

  // String validations
  if (schema.type === 'string') {
    if (schema.maxLength && value.length > schema.maxLength) {
      errors.push(`${path}: exceeds maximum length of ${schema.maxLength}`);
    }

    if (schema.enum && !schema.enum.includes(value)) {
      errors.push(`${path}: must be one of [${schema.enum.join(', ')}]`);
    }

    if (schema.format === 'email' && value && !isValidEmail(value)) {
      errors.push(`${path}: invalid email format`);
    }
  }

  return errors;
}

/**
 * Sanitize settings by removing invalid properties and applying defaults
 * @param {Object} settings - Settings to sanitize
 * @returns {Object} Sanitized settings
 */
export function sanitizeSettings(settings) {
  const sanitized = {};

  for (const [sectionName, sectionSchema] of Object.entries(SETTINGS_SCHEMA)) {
    const sectionData = settings[sectionName] || {};
    sanitized[sectionName] = {};

    if (sectionSchema.properties) {
      for (const [propName, propSchema] of Object.entries(sectionSchema.properties)) {
        const propValue = sectionData[propName];

        if (propValue != null) {
          // Use existing value if valid
          const propErrors = validateProperty(propValue, propSchema, `${sectionName}.${propName}`);
          if (propErrors.length === 0) {
            sanitized[sectionName][propName] = propValue;
          } else if (propSchema.default != null) {
            sanitized[sectionName][propName] = propSchema.default;
          }
        } else if (propSchema.default != null) {
          // Use default value
          sanitized[sectionName][propName] = propSchema.default;
        }
      }
    }
  }

  return sanitized;
}

/**
 * Merge settings with defaults
 * @param {Object} settings - User settings
 * @param {Object} defaults - Default settings
 * @returns {Object} Merged settings
 */
export function mergeWithDefaults(settings, defaults = DEFAULT_SETTINGS) {
  const merged = { ...defaults };

  for (const [sectionName, sectionData] of Object.entries(settings)) {
    if (merged[sectionName] && typeof merged[sectionName] === 'object') {
      merged[sectionName] = { ...merged[sectionName], ...sectionData };
    } else {
      merged[sectionName] = sectionData;
    }
  }

  return merged;
}

/**
 * Simple email validation
 * @param {string} email - Email to validate
 * @returns {boolean} True if valid email format
 */
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Get settings migration path for version updates
 * @param {string} fromVersion - Source version
 * @param {string} toVersion - Target version
 * @returns {Array} Array of migration functions
 */
export function getSettingsMigrations(fromVersion, toVersion) {
  // Future: implement version-specific migrations
  return [];
}
