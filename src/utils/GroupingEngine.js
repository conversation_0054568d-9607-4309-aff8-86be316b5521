/**
 * GroupingEngine - Core grouping and aggregation logic for invoice data
 * Provides efficient grouping algorithms with O(n log n) complexity
 * Supports multiple currencies and configurable grouping options
 *
 * Epic: EPIC-003 - Data Display & Visualization
 * Story: STORY-3.2 - Grouping & Aggregation
 * Task: TASK-3.2.1 - Grouping Logic
 */

import { ProcessingLogger } from './ProcessingLogger.js';
import { DateGrouping } from './DateGrouping.js';
import { AggregationCalculator } from './AggregationCalculator.js';

export class GroupingEngine {
  constructor() {
    this.logger = new ProcessingLogger();
    this.dateGrouping = new DateGrouping();
    this.aggregationCalculator = new AggregationCalculator();
    this.cache = new Map(); // Cache for expensive calculations
    this.cacheMaxAge = 5 * 60 * 1000; // 5 minutes
    this.supportedGroupTypes = ['year', 'quarter', 'month', 'week', 'day', 'custom'];
    this.supportedAggregations = ['sum', 'count', 'average', 'min', 'max', 'median'];
  }

  /**
   * Configure the grouping engine
   * @param {Object} config - Configuration options
   */
  configure(config = {}) {
    this.logger.configure(config.logging || {});
    this.dateGrouping.configure(config.dateGrouping || {});
    this.aggregationCalculator.configure(config.aggregation || {});

    if (config.cacheMaxAge) {
      this.cacheMaxAge = config.cacheMaxAge;
    }
  }

  /**
   * Group invoices by specified criteria with aggregation
   * @param {Array} invoices - Array of invoice objects
   * @param {Object} options - Grouping options
   * @returns {Object} - Grouped and aggregated data
   */
  async groupInvoices(invoices, options = {}) {
    const startTime = performance.now();
    const operationId = this.generateOperationId();

    try {
      this.logger.info('grouping_start', 'Starting invoice grouping operation', operationId, {
        invoiceCount: invoices.length,
        options
      });

      // Validate inputs
      const validation = this.validateInputs(invoices, options);
      if (!validation.valid) {
        throw new Error(`Invalid inputs: ${validation.errors.join(', ')}`);
      }

      // Check cache first
      const cacheKey = this.generateCacheKey(invoices, options);
      const cachedResult = this.getCachedResult(cacheKey);
      if (cachedResult) {
        this.logger.info('grouping_cache_hit', 'Using cached grouping result', operationId, {
          cacheKey: cacheKey.substring(0, 16) + '...'
        });
        return cachedResult;
      }

      // Perform grouping
      const groupedData = await this.performGrouping(invoices, options, operationId);

      // Cache the result
      this.setCachedResult(cacheKey, groupedData);

      const duration = performance.now() - startTime;
      this.logger.info('grouping_complete', 'Invoice grouping completed successfully', operationId, {
        duration: `${duration.toFixed(2)}ms`,
        groupCount: Object.keys(groupedData.groups).length,
        totalInvoices: groupedData.summary.totalInvoices
      });

      return groupedData;

    } catch (error) {
      const duration = performance.now() - startTime;
      this.logger.error('grouping_error', 'Invoice grouping failed', operationId, {
        error: error.message,
        duration: `${duration.toFixed(2)}ms`,
        invoiceCount: invoices.length
      });
      throw error;
    }
  }

  /**
   * Perform the actual grouping operation
   * @param {Array} invoices - Array of invoice objects
   * @param {Object} options - Grouping options
   * @param {string} operationId - Operation identifier for logging
   * @returns {Object} - Grouped data
   */
  async performGrouping(invoices, options, operationId) {
    const {
      groupBy = 'month',
      aggregations = ['sum', 'count', 'average'],
      dateField = 'issue_date',
      currencyHandling = 'separate',
      fiscalYearStart = 1, // January
      customDateRanges = null,
      includeEmpty = false,
      sortBy = 'date',
      sortDirection = 'desc'
    } = options;

    this.logger.debug('grouping_config', 'Grouping configuration', operationId, {
      groupBy,
      aggregations,
      dateField,
      currencyHandling,
      fiscalYearStart
    });

    // Step 1: Prepare and validate invoice data
    const preparedInvoices = this.prepareInvoiceData(invoices, dateField, operationId);

    // Step 2: Create groups based on criteria
    const groups = this.createGroups(preparedInvoices, groupBy, {
      fiscalYearStart,
      customDateRanges,
      dateField
    }, operationId);

    // Step 3: Calculate aggregations for each group
    const aggregatedGroups = this.calculateGroupAggregations(groups, aggregations, currencyHandling, operationId);

    // Step 4: Generate summary statistics
    const summary = this.generateSummary(aggregatedGroups, preparedInvoices, operationId);

    // Step 5: Sort groups
    const sortedGroups = this.sortGroups(aggregatedGroups, sortBy, sortDirection, operationId);

    return {
      groups: sortedGroups,
      summary,
      metadata: {
        groupBy,
        aggregations,
        dateField,
        currencyHandling,
        fiscalYearStart,
        totalGroups: Object.keys(sortedGroups).length,
        totalInvoices: preparedInvoices.length,
        generatedAt: new Date().toISOString(),
        operationId
      }
    };
  }

  /**
   * Prepare invoice data for grouping
   * @param {Array} invoices - Raw invoice data
   * @param {string} dateField - Field to use for date-based grouping
   * @param {string} operationId - Operation identifier
   * @returns {Array} - Prepared invoice data
   */
  prepareInvoiceData(invoices, dateField, operationId) {
    this.logger.debug('data_preparation', 'Preparing invoice data for grouping', operationId, {
      originalCount: invoices.length,
      dateField
    });

    const prepared = invoices
      .filter(invoice => this.isValidInvoice(invoice, dateField))
      .map(invoice => this.normalizeInvoice(invoice, dateField));

    this.logger.debug('data_preparation_complete', 'Invoice data preparation completed', operationId, {
      originalCount: invoices.length,
      preparedCount: prepared.length,
      filteredOut: invoices.length - prepared.length
    });

    return prepared;
  }

  /**
   * Check if invoice is valid for grouping
   * @param {Object} invoice - Invoice object
   * @param {string} dateField - Date field to validate
   * @returns {boolean} - Whether invoice is valid
   */
  isValidInvoice(invoice, dateField) {
    if (!invoice || typeof invoice !== 'object') { return false; }

    // Must have a valid date
    const dateValue = invoice[dateField];
    if (!dateValue) { return false; }

    const date = new Date(dateValue);
    if (isNaN(date.getTime())) { return false; }

    // Must have at least one numeric field for aggregation
    const numericFields = ['total_net', 'total_vat', 'total_gross'];
    const hasNumericData = numericFields.some(field => {
      const value = parseFloat(invoice[field]);
      return !isNaN(value);
    });

    return hasNumericData;
  }

  /**
   * Normalize invoice data for consistent processing
   * @param {Object} invoice - Raw invoice object
   * @param {string} dateField - Date field to normalize
   * @returns {Object} - Normalized invoice object
   */
  normalizeInvoice(invoice, dateField) {
    const normalized = { ...invoice };

    // Normalize date
    normalized.groupingDate = new Date(invoice[dateField]);

    // Normalize numeric fields
    normalized.total_net = parseFloat(invoice.total_net) || 0;
    normalized.total_vat = parseFloat(invoice.total_vat) || 0;
    normalized.total_gross = parseFloat(invoice.total_gross) || 0;

    // Normalize currency
    normalized.currency = (invoice.currency || 'PLN').toUpperCase();

    // Add grouping metadata
    normalized.groupingMetadata = {
      originalDateField: dateField,
      originalDateValue: invoice[dateField],
      normalizedAt: new Date().toISOString()
    };

    return normalized;
  }

  /**
   * Generate unique operation ID for tracking
   * @returns {string} - Unique operation identifier
   */
  generateOperationId() {
    return `grp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Validate inputs for grouping operation
   * @param {Array} invoices - Invoice array to validate
   * @param {Object} options - Options to validate
   * @returns {Object} - Validation result
   */
  validateInputs(invoices, options) {
    const errors = [];

    if (!Array.isArray(invoices)) {
      errors.push('Invoices must be an array');
    }

    if (invoices.length === 0) {
      errors.push('Invoice array cannot be empty');
    }

    if (options.groupBy && !this.supportedGroupTypes.includes(options.groupBy)) {
      errors.push(`Unsupported groupBy type: ${options.groupBy}`);
    }

    if (options.aggregations && !Array.isArray(options.aggregations)) {
      errors.push('Aggregations must be an array');
    }

    if (options.aggregations) {
      const invalidAggregations = options.aggregations.filter(agg =>
        !this.supportedAggregations.includes(agg)
      );
      if (invalidAggregations.length > 0) {
        errors.push(`Unsupported aggregations: ${invalidAggregations.join(', ')}`);
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Generate cache key for grouping operation
   * @param {Array} invoices - Invoice data
   * @param {Object} options - Grouping options
   * @returns {string} - Cache key
   */
  generateCacheKey(invoices, options) {
    const invoiceHash = this.hashInvoices(invoices);
    const optionsHash = this.hashObject(options);
    return `grouping_${invoiceHash}_${optionsHash}`;
  }

  /**
   * Generate hash for invoice array
   * @param {Array} invoices - Invoice array
   * @returns {string} - Hash string
   */
  hashInvoices(invoices) {
    const key = invoices.map(inv => `${inv.id || inv.filename}_${inv.issue_date}_${inv.total_gross}`).join('|');
    return this.simpleHash(key);
  }

  /**
   * Generate hash for object
   * @param {Object} obj - Object to hash
   * @returns {string} - Hash string
   */
  hashObject(obj) {
    return this.simpleHash(JSON.stringify(obj));
  }

  /**
   * Simple hash function
   * @param {string} str - String to hash
   * @returns {string} - Hash result
   */
  simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Get cached result if available and not expired
   * @param {string} cacheKey - Cache key
   * @returns {Object|null} - Cached result or null
   */
  getCachedResult(cacheKey) {
    const cached = this.cache.get(cacheKey);
    if (!cached) { return null; }

    const now = Date.now();
    if (now - cached.timestamp > this.cacheMaxAge) {
      this.cache.delete(cacheKey);
      return null;
    }

    return cached.data;
  }

  /**
   * Set cached result
   * @param {string} cacheKey - Cache key
   * @param {Object} data - Data to cache
   */
  setCachedResult(cacheKey, data) {
    this.cache.set(cacheKey, {
      data: JSON.parse(JSON.stringify(data)), // Deep copy
      timestamp: Date.now()
    });

    // Clean up old cache entries
    this.cleanupCache();
  }

  /**
   * Clean up expired cache entries
   */
  cleanupCache() {
    const now = Date.now();
    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp > this.cacheMaxAge) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Create groups based on grouping criteria
   * @param {Array} invoices - Prepared invoice data
   * @param {string} groupBy - Grouping criteria
   * @param {Object} options - Grouping options
   * @param {string} operationId - Operation identifier
   * @returns {Object} - Groups object
   */
  createGroups(invoices, groupBy, options, operationId) {
    this.logger.debug('group_creation', 'Creating groups', operationId, {
      groupBy,
      invoiceCount: invoices.length
    });

    const groups = {};

    for (const invoice of invoices) {
      const groupKey = this.getGroupKey(invoice, groupBy, options);

      if (!groups[groupKey]) {
        groups[groupKey] = {
          key: groupKey,
          invoices: [],
          metadata: this.createGroupMetadata(groupKey, groupBy, options)
        };
      }

      groups[groupKey].invoices.push(invoice);
    }

    this.logger.debug('group_creation_complete', 'Group creation completed', operationId, {
      groupCount: Object.keys(groups).length,
      averageGroupSize: invoices.length / Object.keys(groups).length
    });

    return groups;
  }

  /**
   * Get group key for an invoice
   * @param {Object} invoice - Invoice object
   * @param {string} groupBy - Grouping criteria
   * @param {Object} options - Grouping options
   * @returns {string} - Group key
   */
  getGroupKey(invoice, groupBy, options) {
    const date = invoice.groupingDate;

    switch (groupBy) {
      case 'year':
        return this.dateGrouping.getYearKey(date, options.fiscalYearStart);
      case 'quarter':
        return this.dateGrouping.getQuarterKey(date, options.fiscalYearStart);
      case 'month':
        return this.dateGrouping.getMonthKey(date);
      case 'week':
        return this.dateGrouping.getWeekKey(date);
      case 'day':
        return this.dateGrouping.getDayKey(date);
      case 'custom':
        return this.dateGrouping.getCustomKey(date, options.customDateRanges);
      default:
        throw new Error(`Unsupported groupBy type: ${groupBy}`);
    }
  }

  /**
   * Create metadata for a group
   * @param {string} groupKey - Group key
   * @param {string} groupBy - Grouping criteria
   * @param {Object} options - Grouping options
   * @returns {Object} - Group metadata
   */
  createGroupMetadata(groupKey, groupBy, options) {
    return {
      groupKey,
      groupBy,
      displayName: this.formatGroupDisplayName(groupKey, groupBy),
      sortOrder: this.getGroupSortOrder(groupKey, groupBy),
      dateRange: this.getGroupDateRange(groupKey, groupBy, options),
      createdAt: new Date().toISOString()
    };
  }

  /**
   * Format group display name
   * @param {string} groupKey - Group key
   * @param {string} groupBy - Grouping criteria
   * @returns {string} - Formatted display name
   */
  formatGroupDisplayName(groupKey, groupBy) {
    switch (groupBy) {
      case 'year':
        return `Year ${groupKey}`;
      case 'quarter':
        const [year, quarter] = groupKey.split('-Q');
        return `Q${quarter} ${year}`;
      case 'month':
        const [yearMonth, month] = groupKey.split('-');
        const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
          'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        return `${monthNames[parseInt(month) - 1]} ${yearMonth}`;
      case 'week':
        const [weekYear, week] = groupKey.split('-W');
        return `Week ${week}, ${weekYear}`;
      case 'day':
        return new Date(groupKey).toLocaleDateString();
      default:
        return groupKey;
    }
  }

  /**
   * Get sort order for group
   * @param {string} groupKey - Group key
   * @param {string} groupBy - Grouping criteria
   * @returns {number} - Sort order value
   */
  getGroupSortOrder(groupKey, groupBy) {
    switch (groupBy) {
      case 'year':
        return parseInt(groupKey);
      case 'quarter':
        const [qYear, qQuarter] = groupKey.split('-Q');
        return parseInt(qYear) * 10 + parseInt(qQuarter);
      case 'month':
        return groupKey.replace('-', '');
      case 'week':
        const [wYear, wWeek] = groupKey.split('-W');
        return parseInt(wYear) * 100 + parseInt(wWeek);
      case 'day':
        return new Date(groupKey).getTime();
      default:
        return 0;
    }
  }

  /**
   * Get date range for group
   * @param {string} groupKey - Group key
   * @param {string} groupBy - Grouping criteria
   * @param {Object} options - Grouping options
   * @returns {Object} - Date range object
   */
  getGroupDateRange(groupKey, groupBy, options) {
    return this.dateGrouping.getDateRangeForGroup(groupKey, groupBy, options);
  }

  /**
   * Calculate aggregations for each group
   * @param {Object} groups - Groups object
   * @param {Array} aggregations - Aggregation types to calculate
   * @param {string} currencyHandling - How to handle multiple currencies
   * @param {string} operationId - Operation identifier
   * @returns {Object} - Groups with aggregations
   */
  calculateGroupAggregations(groups, aggregations, currencyHandling, operationId) {
    this.logger.debug('aggregation_start', 'Starting aggregation calculations', operationId, {
      groupCount: Object.keys(groups).length,
      aggregations,
      currencyHandling
    });

    const aggregatedGroups = {};

    for (const [groupKey, group] of Object.entries(groups)) {
      aggregatedGroups[groupKey] = {
        ...group,
        aggregations: this.calculateGroupAggregation(group.invoices, aggregations, currencyHandling),
        statistics: this.calculateGroupStatistics(group.invoices)
      };
    }

    this.logger.debug('aggregation_complete', 'Aggregation calculations completed', operationId, {
      groupCount: Object.keys(aggregatedGroups).length
    });

    return aggregatedGroups;
  }

  /**
   * Calculate aggregation for a single group
   * @param {Array} invoices - Invoices in the group
   * @param {Array} aggregations - Aggregation types
   * @param {string} currencyHandling - Currency handling strategy
   * @returns {Object} - Aggregation results
   */
  calculateGroupAggregation(invoices, aggregations, currencyHandling) {
    const result = {};
    const fields = ['total_net', 'total_vat', 'total_gross'];

    // Group by currency if needed
    const currencyGroups = currencyHandling === 'separate'
      ? this.groupByCurrency(invoices)
      : { 'ALL': invoices };

    for (const [currency, currencyInvoices] of Object.entries(currencyGroups)) {
      result[currency] = {};

      for (const field of fields) {
        result[currency][field] = {};
        const values = currencyInvoices.map(inv => inv[field]).filter(val => !isNaN(val));

        for (const aggregation of aggregations) {
          result[currency][field][aggregation] = this.aggregationCalculator.calculate(
            values,
            aggregation
          );
        }
      }

      // Add count and additional metrics
      result[currency].count = currencyInvoices.length;
      result[currency].invoiceIds = currencyInvoices.map(inv => inv.id || inv.filename);
    }

    return result;
  }

  /**
   * Group invoices by currency
   * @param {Array} invoices - Invoice array
   * @returns {Object} - Invoices grouped by currency
   */
  groupByCurrency(invoices) {
    const groups = {};

    for (const invoice of invoices) {
      const currency = invoice.currency || 'PLN';
      if (!groups[currency]) {
        groups[currency] = [];
      }
      groups[currency].push(invoice);
    }

    return groups;
  }

  /**
   * Calculate additional statistics for a group
   * @param {Array} invoices - Invoices in the group
   * @returns {Object} - Statistics object
   */
  calculateGroupStatistics(invoices) {
    const dateRange = this.getInvoiceDateRange(invoices);
    const currencies = [...new Set(invoices.map(inv => inv.currency))];

    return {
      invoiceCount: invoices.length,
      dateRange,
      currencies,
      firstInvoiceDate: dateRange.start,
      lastInvoiceDate: dateRange.end,
      daySpan: Math.ceil((dateRange.end - dateRange.start) / (1000 * 60 * 60 * 24))
    };
  }

  /**
   * Get date range for invoices
   * @param {Array} invoices - Invoice array
   * @returns {Object} - Date range object
   */
  getInvoiceDateRange(invoices) {
    if (invoices.length === 0) {
      return { start: null, end: null };
    }

    const dates = invoices.map(inv => inv.groupingDate).sort((a, b) => a - b);
    return {
      start: dates[0],
      end: dates[dates.length - 1]
    };
  }

  /**
   * Generate summary statistics for all groups
   * @param {Object} aggregatedGroups - Groups with aggregations
   * @param {Array} preparedInvoices - All prepared invoices
   * @param {string} operationId - Operation identifier
   * @returns {Object} - Summary object
   */
  generateSummary(aggregatedGroups, preparedInvoices, operationId) {
    this.logger.debug('summary_generation', 'Generating summary statistics', operationId, {
      groupCount: Object.keys(aggregatedGroups).length,
      totalInvoices: preparedInvoices.length
    });

    const summary = {
      totalInvoices: preparedInvoices.length,
      totalGroups: Object.keys(aggregatedGroups).length,
      currencies: [...new Set(preparedInvoices.map(inv => inv.currency))],
      dateRange: this.getInvoiceDateRange(preparedInvoices),
      overallTotals: this.calculateOverallTotals(preparedInvoices),
      groupSizes: this.calculateGroupSizes(aggregatedGroups),
      generatedAt: new Date().toISOString()
    };

    this.logger.debug('summary_complete', 'Summary generation completed', operationId, {
      totalInvoices: summary.totalInvoices,
      totalGroups: summary.totalGroups,
      currencies: summary.currencies.length
    });

    return summary;
  }

  /**
   * Calculate overall totals across all invoices
   * @param {Array} invoices - All invoices
   * @returns {Object} - Overall totals by currency
   */
  calculateOverallTotals(invoices) {
    const totals = {};
    const fields = ['total_net', 'total_vat', 'total_gross'];

    const currencyGroups = this.groupByCurrency(invoices);

    for (const [currency, currencyInvoices] of Object.entries(currencyGroups)) {
      totals[currency] = {};

      for (const field of fields) {
        const values = currencyInvoices.map(inv => inv[field]).filter(val => !isNaN(val));
        totals[currency][field] = {
          sum: this.aggregationCalculator.calculate(values, 'sum'),
          average: this.aggregationCalculator.calculate(values, 'average'),
          count: values.length
        };
      }
    }

    return totals;
  }

  /**
   * Calculate group size statistics
   * @param {Object} groups - Groups object
   * @returns {Object} - Group size statistics
   */
  calculateGroupSizes(groups) {
    const sizes = Object.values(groups).map(group => group.invoices.length);

    return {
      min: Math.min(...sizes),
      max: Math.max(...sizes),
      average: this.aggregationCalculator.calculate(sizes, 'average'),
      median: this.aggregationCalculator.calculate(sizes, 'median'),
      total: sizes.reduce((sum, size) => sum + size, 0)
    };
  }

  /**
   * Sort groups by specified criteria
   * @param {Object} groups - Groups to sort
   * @param {string} sortBy - Sort criteria
   * @param {string} sortDirection - Sort direction (asc/desc)
   * @param {string} operationId - Operation identifier
   * @returns {Object} - Sorted groups
   */
  sortGroups(groups, sortBy, sortDirection, operationId) {
    this.logger.debug('group_sorting', 'Sorting groups', operationId, {
      sortBy,
      sortDirection,
      groupCount: Object.keys(groups).length
    });

    const sortedEntries = Object.entries(groups).sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'date':
          comparison = a[1].metadata.sortOrder - b[1].metadata.sortOrder;
          break;
        case 'count':
          comparison = a[1].invoices.length - b[1].invoices.length;
          break;
        case 'total':
          const aTotal = this.getGroupTotal(a[1]);
          const bTotal = this.getGroupTotal(b[1]);
          comparison = aTotal - bTotal;
          break;
        case 'name':
          comparison = a[1].metadata.displayName.localeCompare(b[1].metadata.displayName);
          break;
        default:
          comparison = a[1].metadata.sortOrder - b[1].metadata.sortOrder;
      }

      return sortDirection === 'desc' ? -comparison : comparison;
    });

    const sortedGroups = {};
    for (const [key, group] of sortedEntries) {
      sortedGroups[key] = group;
    }

    this.logger.debug('group_sorting_complete', 'Group sorting completed', operationId, {
      sortBy,
      sortDirection
    });

    return sortedGroups;
  }

  /**
   * Get total value for a group (for sorting)
   * @param {Object} group - Group object
   * @returns {number} - Total value
   */
  getGroupTotal(group) {
    if (!group.aggregations) { return 0; }

    // Use first currency's total_gross sum as default
    const currencies = Object.keys(group.aggregations);
    if (currencies.length === 0) { return 0; }

    const firstCurrency = currencies[0];
    return group.aggregations[firstCurrency]?.total_gross?.sum || 0;
  }
}
