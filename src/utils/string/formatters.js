/**
 * String Formatting Utilities
 * Consolidated string formatting functions from across the codebase
 *
 * Consolidates:
 * - OCR text cleaning from ocrUtils.js
 * - String formatting from various components
 * - Text normalization utilities
 * - Currency and number formatting
 */

/**
 * Clean OCR text by removing artifacts and normalizing
 * Consolidated from ocrUtils.js
 * @param {string} text - Raw OCR text
 * @param {Object} options - Cleaning options
 * @returns {string} - Cleaned text
 */
export function cleanOCRText(text, options = {}) {
  const {
    removeExtraSpaces = true,
    fixPunctuation = true,
    removeSpecialChars = false,
    normalizeLineBreaks = true
  } = options;

  if (!text || typeof text !== 'string') {
    return '';
  }

  let cleaned = text;

  // Remove common OCR artifacts
  cleaned = cleaned.replace(/[^\w\s\-.,;:!?()[\]{}'"€$£¥₹₽ąćęłńóśźżĄĆĘŁŃÓŚŹŻ]/g, ' ');

  // Normalize whitespace
  if (removeExtraSpaces) {
    cleaned = cleaned.replace(/\s+/g, ' ');
  }

  // Normalize line breaks
  if (normalizeLineBreaks) {
    cleaned = cleaned.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
    cleaned = cleaned.replace(/\n+/g, '\n');
  }

  // Fix punctuation spacing
  if (fixPunctuation) {
    cleaned = cleaned.replace(/\s+([,.;:!?])/g, '$1');
    cleaned = cleaned.replace(/([,.;:!?])\s*/g, '$1 ');
  }

  // Remove special characters if requested
  if (removeSpecialChars) {
    cleaned = cleaned.replace(/[^\w\s\-.,;:!?()]/g, '');
  }

  return cleaned.trim();
}

/**
 * Format currency amount
 * @param {number} amount - Amount to format
 * @param {Object} options - Formatting options
 * @returns {string} - Formatted currency
 */
export function formatCurrency(amount, options = {}) {
  const {
    currency = 'PLN',
    locale = 'pl-PL',
    minimumFractionDigits = 2,
    maximumFractionDigits = 2,
    style = 'currency'
  } = options;

  if (typeof amount !== 'number' || isNaN(amount)) {
    return '0,00 ' + currency;
  }

  try {
    return new Intl.NumberFormat(locale, {
      style,
      currency,
      minimumFractionDigits,
      maximumFractionDigits
    }).format(amount);
  } catch (error) {
    // Fallback formatting
    const formatted = amount.toFixed(maximumFractionDigits);
    return `${formatted} ${currency}`;
  }
}

/**
 * Format percentage
 * @param {number} value - Value to format as percentage
 * @param {Object} options - Formatting options
 * @returns {string} - Formatted percentage
 */
export function formatPercentage(value, options = {}) {
  const {
    decimals = 1,
    includeSymbol = true,
    locale = 'pl-PL'
  } = options;

  if (typeof value !== 'number' || isNaN(value)) {
    return includeSymbol ? '0%' : '0';
  }

  try {
    const formatted = new Intl.NumberFormat(locale, {
      style: 'percent',
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    }).format(value / 100);

    return formatted;
  } catch (error) {
    // Fallback formatting
    const formatted = (value).toFixed(decimals);
    return includeSymbol ? `${formatted}%` : formatted;
  }
}

/**
 * Format number with locale-specific formatting
 * @param {number} number - Number to format
 * @param {Object} options - Formatting options
 * @returns {string} - Formatted number
 */
export function formatNumber(number, options = {}) {
  const {
    locale = 'pl-PL',
    minimumFractionDigits = 0,
    maximumFractionDigits = 2,
    useGrouping = true
  } = options;

  if (typeof number !== 'number' || isNaN(number)) {
    return '0';
  }

  try {
    return new Intl.NumberFormat(locale, {
      minimumFractionDigits,
      maximumFractionDigits,
      useGrouping
    }).format(number);
  } catch (error) {
    // Fallback formatting
    return number.toFixed(maximumFractionDigits);
  }
}

/**
 * Capitalize first letter of each word
 * @param {string} text - Text to capitalize
 * @param {Object} options - Capitalization options
 * @returns {string} - Capitalized text
 */
export function capitalizeWords(text, options = {}) {
  const {
    preserveCase = false, // Whether to preserve existing case for other letters
    skipWords = ['and', 'or', 'the', 'a', 'an', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']
  } = options;

  if (!text || typeof text !== 'string') {
    return '';
  }

  return text.split(' ').map((word, index) => {
    if (index > 0 && skipWords.includes(word.toLowerCase())) {
      return preserveCase ? word : word.toLowerCase();
    }

    if (preserveCase) {
      return word.charAt(0).toUpperCase() + word.slice(1);
    }
    return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();

  }).join(' ');
}

/**
 * Truncate text with ellipsis
 * @param {string} text - Text to truncate
 * @param {Object} options - Truncation options
 * @returns {string} - Truncated text
 */
export function truncateText(text, options = {}) {
  const {
    maxLength = 100,
    ellipsis = '...',
    breakOnWord = true
  } = options;

  if (!text || typeof text !== 'string') {
    return '';
  }

  if (text.length <= maxLength) {
    return text;
  }

  let truncated = text.substring(0, maxLength - ellipsis.length);

  if (breakOnWord) {
    const lastSpace = truncated.lastIndexOf(' ');
    if (lastSpace > 0) {
      truncated = truncated.substring(0, lastSpace);
    }
  }

  return truncated + ellipsis;
}

/**
 * Remove diacritics (accents) from text
 * @param {string} text - Text to normalize
 * @returns {string} - Text without diacritics
 */
export function removeDiacritics(text) {
  if (!text || typeof text !== 'string') {
    return '';
  }

  return text.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
}

/**
 * Slugify text for URLs
 * @param {string} text - Text to slugify
 * @param {Object} options - Slugify options
 * @returns {string} - Slugified text
 */
export function slugify(text, options = {}) {
  const {
    separator = '-',
    lowercase = true,
    removeDiacritics: shouldRemoveDiacritics = true
  } = options;

  if (!text || typeof text !== 'string') {
    return '';
  }

  let slug = text;

  // Remove diacritics if requested
  if (shouldRemoveDiacritics) {
    slug = removeDiacritics(slug);
  }

  // Convert to lowercase if requested
  if (lowercase) {
    slug = slug.toLowerCase();
  }

  // Replace non-alphanumeric characters with separator
  slug = slug.replace(/[^a-zA-Z0-9]/g, separator);

  // Remove multiple separators
  slug = slug.replace(new RegExp(`${separator}+`, 'g'), separator);

  // Remove leading and trailing separators
  slug = slug.replace(new RegExp(`^${separator}+|${separator}+$`, 'g'), '');

  return slug;
}

/**
 * Extract initials from name
 * @param {string} name - Full name
 * @param {Object} options - Options for initials
 * @returns {string} - Initials
 */
export function extractInitials(name, options = {}) {
  const {
    maxInitials = 2,
    uppercase = true,
    includeDots = false
  } = options;

  if (!name || typeof name !== 'string') {
    return '';
  }

  const words = name.trim().split(/\s+/);
  const initials = words
    .slice(0, maxInitials)
    .map(word => word.charAt(0))
    .join(includeDots ? '.' : '');

  return uppercase ? initials.toUpperCase() : initials;
}

/**
 * Format phone number
 * @param {string} phone - Phone number to format
 * @param {Object} options - Formatting options
 * @returns {string} - Formatted phone number
 */
export function formatPhoneNumber(phone, options = {}) {
  const {
    countryCode = '+48',
    format = 'international' // 'international', 'national', 'compact'
  } = options;

  if (!phone || typeof phone !== 'string') {
    return '';
  }

  // Remove all non-digit characters
  const digits = phone.replace(/\D/g, '');

  if (digits.length === 0) {
    return '';
  }

  // Polish phone number formatting
  if (format === 'international' && digits.length === 9) {
    return `${countryCode} ${digits.substring(0, 3)} ${digits.substring(3, 6)} ${digits.substring(6)}`;
  } else if (format === 'national' && digits.length === 9) {
    return `${digits.substring(0, 3)} ${digits.substring(3, 6)} ${digits.substring(6)}`;
  } else if (format === 'compact') {
    return digits;
  }

  // Return as-is if format not recognized
  return phone;
}

/**
 * Mask sensitive information
 * @param {string} text - Text to mask
 * @param {Object} options - Masking options
 * @returns {string} - Masked text
 */
export function maskSensitiveInfo(text, options = {}) {
  const {
    maskChar = '*',
    visibleStart = 2,
    visibleEnd = 2,
    minLength = 4
  } = options;

  if (!text || typeof text !== 'string') {
    return '';
  }

  if (text.length < minLength) {
    return maskChar.repeat(text.length);
  }

  const start = text.substring(0, visibleStart);
  const end = text.substring(text.length - visibleEnd);
  const middle = maskChar.repeat(text.length - visibleStart - visibleEnd);

  return start + middle + end;
}

/**
 * Validate and format email
 * @param {string} email - Email to format
 * @param {Object} options - Formatting options
 * @returns {Object} - Validation and formatting result
 */
export function formatEmail(email, options = {}) {
  const {
    lowercase = true,
    trim = true
  } = options;

  if (!email || typeof email !== 'string') {
    return { isValid: false, formatted: '', error: 'No email provided' };
  }

  let formatted = email;

  if (trim) {
    formatted = formatted.trim();
  }

  if (lowercase) {
    formatted = formatted.toLowerCase();
  }

  // Basic email validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  const isValid = emailRegex.test(formatted);

  return {
    isValid,
    formatted: isValid ? formatted : email,
    error: isValid ? null : 'Invalid email format'
  };
}
