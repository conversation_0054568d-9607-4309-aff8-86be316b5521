/**
 * UploadTracker - UUID generation and tracking for document uploads
 * Provides unique identifiers and metadata tracking for upload sessions
 */

export class UploadTracker {
  constructor() {
    this.activeUploads = new Map(); // Track active uploads
    this.completedUploads = new Map(); // Track completed uploads
    this.uploadMetadata = new Map(); // Store upload metadata
  }

  /**
   * Generate a unique UUID for upload tracking
   * @returns {string} - Unique UUID
   */
  generateUploadId() {
    // Generate UUID v4 format: xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * Generate a short ID for display purposes
   * @param {string} uploadId - Full upload UUID
   * @returns {string} - Short ID (first 8 characters)
   */
  getShortId(uploadId) {
    return uploadId.substring(0, 8);
  }

  /**
   * Start tracking a new upload
   * @param {File} file - File being uploaded
   * @param {Object} options - Upload options
   * @returns {string} - Upload UUID
   */
  startUpload(file, options = {}) {
    const uploadId = this.generateUploadId();
    const timestamp = new Date().toISOString();

    const uploadInfo = {
      uploadId,
      file: {
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified
      },
      startTime: timestamp,
      status: 'started',
      stages: [],
      options,
      metadata: {
        userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'Node.js',
        timestamp,
        sessionId: this.getSessionId()
      }
    };

    this.activeUploads.set(uploadId, uploadInfo);
    this.uploadMetadata.set(uploadId, uploadInfo);

    return uploadId;
  }

  /**
   * Update upload stage
   * @param {string} uploadId - Upload UUID
   * @param {string} stage - Current processing stage
   * @param {Object} data - Stage-specific data
   */
  updateStage(uploadId, stage, data = {}) {
    const upload = this.activeUploads.get(uploadId);
    if (!upload) {
      console.warn(`Upload ${uploadId} not found for stage update`);
      return;
    }

    const stageInfo = {
      stage,
      timestamp: new Date().toISOString(),
      ...data
    };

    upload.stages.push(stageInfo);
    upload.currentStage = stage;
    upload.lastUpdated = stageInfo.timestamp;

    // Update metadata
    this.uploadMetadata.set(uploadId, upload);
  }

  /**
   * Mark upload as completed
   * @param {string} uploadId - Upload UUID
   * @param {Object} result - Processing result
   */
  completeUpload(uploadId, result = {}) {
    const upload = this.activeUploads.get(uploadId);
    if (!upload) {
      console.warn(`Upload ${uploadId} not found for completion`);
      return;
    }

    const completionTime = new Date().toISOString();
    const startTime = new Date(upload.startTime);
    const endTime = new Date(completionTime);
    const totalDuration = endTime - startTime;

    upload.status = result.success ? 'completed' : 'failed';
    upload.endTime = completionTime;
    upload.totalDurationMs = totalDuration;
    upload.result = result;

    // Move to completed uploads
    this.completedUploads.set(uploadId, upload);
    this.activeUploads.delete(uploadId);

    // Update metadata
    this.uploadMetadata.set(uploadId, upload);
  }

  /**
   * Mark upload as failed
   * @param {string} uploadId - Upload UUID
   * @param {Error|string} error - Error information
   */
  failUpload(uploadId, error) {
    const upload = this.activeUploads.get(uploadId);
    if (!upload) {
      console.warn(`Upload ${uploadId} not found for failure`);
      return;
    }

    const failureTime = new Date().toISOString();
    const startTime = new Date(upload.startTime);
    const endTime = new Date(failureTime);
    const totalDuration = endTime - startTime;

    upload.status = 'failed';
    upload.endTime = failureTime;
    upload.totalDurationMs = totalDuration;
    upload.error = {
      message: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: failureTime
    };

    // Move to completed uploads
    this.completedUploads.set(uploadId, upload);
    this.activeUploads.delete(uploadId);

    // Update metadata
    this.uploadMetadata.set(uploadId, upload);
  }

  /**
   * Get upload information
   * @param {string} uploadId - Upload UUID
   * @returns {Object|null} - Upload information
   */
  getUpload(uploadId) {
    return this.uploadMetadata.get(uploadId) || null;
  }

  /**
   * Get all active uploads
   * @returns {Array} - Array of active upload information
   */
  getActiveUploads() {
    return Array.from(this.activeUploads.values());
  }

  /**
   * Get all completed uploads
   * @returns {Array} - Array of completed upload information
   */
  getCompletedUploads() {
    return Array.from(this.completedUploads.values());
  }

  /**
   * Get upload statistics
   * @returns {Object} - Upload statistics
   */
  getStatistics() {
    const active = this.activeUploads.size;
    const completed = this.completedUploads.size;
    const total = active + completed;

    const completedUploads = Array.from(this.completedUploads.values());
    const successful = completedUploads.filter(u => u.status === 'completed').length;
    const failed = completedUploads.filter(u => u.status === 'failed').length;

    const averageDuration = completedUploads.length > 0
      ? completedUploads.reduce((sum, u) => sum + (u.totalDurationMs || 0), 0) / completedUploads.length
      : 0;

    return {
      active,
      completed,
      total,
      successful,
      failed,
      successRate: completed > 0 ? (successful / completed * 100).toFixed(1) : 0,
      averageDurationMs: averageDuration,
      averageDurationFormatted: this.formatDuration(averageDuration)
    };
  }

  /**
   * Format duration in human-readable format
   * @param {number} durationMs - Duration in milliseconds
   * @returns {string} - Formatted duration
   */
  formatDuration(durationMs) {
    if (durationMs < 1000) {
      return `${Math.round(durationMs)}ms`;
    } else if (durationMs < 60000) {
      return `${(durationMs / 1000).toFixed(1)}s`;
    }
    const minutes = Math.floor(durationMs / 60000);
    const seconds = Math.floor((durationMs % 60000) / 1000);
    return `${minutes}m ${seconds}s`;

  }

  /**
   * Get or create session ID
   * @returns {string} - Session ID
   */
  getSessionId() {
    if (!this.sessionId) {
      this.sessionId = `session-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    }
    return this.sessionId;
  }

  /**
   * Clean up old uploads (memory management)
   * @param {number} maxAge - Maximum age in milliseconds (default: 1 hour)
   */
  cleanup(maxAge = 3600000) {
    const cutoffTime = Date.now() - maxAge;

    // Clean completed uploads older than maxAge
    for (const [uploadId, upload] of this.completedUploads.entries()) {
      const uploadTime = new Date(upload.startTime).getTime();
      if (uploadTime < cutoffTime) {
        this.completedUploads.delete(uploadId);
        this.uploadMetadata.delete(uploadId);
      }
    }

    // Clean orphaned metadata
    for (const [uploadId, metadata] of this.uploadMetadata.entries()) {
      const uploadTime = new Date(metadata.startTime).getTime();
      if (uploadTime < cutoffTime && !this.activeUploads.has(uploadId) && !this.completedUploads.has(uploadId)) {
        this.uploadMetadata.delete(uploadId);
      }
    }
  }

  /**
   * Export upload data for analysis
   * @param {string} uploadId - Upload UUID (optional, exports all if not provided)
   * @returns {Object} - Export data
   */
  exportData(uploadId = null) {
    if (uploadId) {
      return this.getUpload(uploadId);
    }

    return {
      statistics: this.getStatistics(),
      activeUploads: this.getActiveUploads(),
      completedUploads: this.getCompletedUploads(),
      sessionId: this.getSessionId(),
      exportTime: new Date().toISOString()
    };
  }

  /**
   * Clear all tracking data
   */
  clearAll() {
    this.activeUploads.clear();
    this.completedUploads.clear();
    this.uploadMetadata.clear();
    this.sessionId = null;
  }
}

// Create singleton instance
export const uploadTracker = new UploadTracker();

// Export for testing
export default UploadTracker;
