/**
 * Environment Variable Loader for Chrome Extensions
 *
 * Unified environment variable loader that consolidates functionality from
 * multiple environment loading systems. Provides dynamic loading of environment
 * variables with multiple fallback strategies for Chrome extension context.
 *
 * CONSOLIDATION: Replaces circular dependencies between:
 * - EnvLoader.js (this file)
 * - ExtensionEnvironmentLoader.js (merged functionality)
 * - EnvironmentConfigService.js (simplified)
 * - ConfigurationSourceManager.js (source selection only)
 *
 * ASSIGNMENT-062: Environment Loading System Consolidation
 * Epic: EPIC-006 - Code Consolidation & Architecture Cleanup
 */

import { getDefaultEnvironmentVariables, isKnownEnvVar } from '../config/defaultEnvironment.js';

/**
 * Environment Variable Loader Class
 * Handles dynamic loading and parsing of environment variables
 */
export class EnvLoader {
  constructor() {
    this.envVars = {};
    this.isLoaded = false;
    this.loadPromise = null;
  }

  /**
   * Initialize and load environment variables
   * @returns {Promise<Object>} Loaded environment variables
   */
  async initialize() {
    if (this.loadPromise) {
      return this.loadPromise;
    }

    this.loadPromise = this._loadEnvironmentVariables();
    return this.loadPromise;
  }

  /**
   * Load environment variables (alias for initialize)
   * @returns {Promise<Object>} Loaded environment variables
   */
  async load() {
    return this.initialize();
  }

  /**
   * Load environment variables from various sources
   * @private
   * @returns {Promise<Object>} Environment variables
   */
  async _loadEnvironmentVariables() {
    try {
      console.log('🔧 EnvLoader: Loading environment variables...');
      console.log('🔧 EnvLoader: Environment context:', {
        isChrome: typeof chrome !== 'undefined',
        hasWindow: typeof window !== 'undefined',
        hasImportMeta: typeof import.meta !== 'undefined',
        hostname: typeof window !== 'undefined' ? window.location.hostname : 'unknown'
      });

      // Try multiple loading strategies
      let envVars = {};

      // Strategy 1: Try to load from injected build-time variables
      console.log('🔧 EnvLoader: Strategy 1 - Build-time injection');
      envVars = await this._loadFromBuildTimeInjection();

      if (Object.keys(envVars).length === 0) {
        // Strategy 2: Try to load from Chrome storage
        console.log('🔧 EnvLoader: Strategy 2 - Chrome storage');
        envVars = await this._loadFromChromeStorage();
      }

      if (Object.keys(envVars).length === 0) {
        // Strategy 3: Try to load from fetch (if available)
        console.log('🔧 EnvLoader: Strategy 3 - Development server fetch');
        envVars = await this._loadFromFetch();
      }

      if (Object.keys(envVars).length === 0) {
        // Strategy 4: Try Chrome extension specific loading (merged functionality)
        console.log('🔧 EnvLoader: Strategy 4 - Chrome extension specific loading');
        envVars = await this._loadFromChromeExtension();
      }

      if (Object.keys(envVars).length === 0) {
        // Strategy 5: Use default values
        console.log('🔧 EnvLoader: Strategy 5 - Default fallback values');
        envVars = getDefaultEnvironmentVariables();
        console.warn('⚠️ Using default environment variables');
      }

      this.envVars = envVars;
      this.isLoaded = true;

      console.log('✅ EnvLoader: Environment variables loaded successfully');
      console.log('📊 EnvLoader: Loaded', Object.keys(envVars).length, 'variables');

      // Development mode: Log summary only (reduced spam)
      if (this._isDevelopmentMode()) {
        console.log('🔍 EnvLoader: Development Mode - Variables Summary');
        console.log('📊 Loaded variables:', {
          total: Object.keys(envVars).length,
          apiKeys: Object.keys(envVars).filter(k => k.includes('API_KEY')).length,
          company: Object.keys(envVars).filter(k => k.includes('COMPANY')).length,
          features: Object.keys(envVars).filter(k => k.includes('FEATURE')).length
        });
      }

      return envVars;

    } catch (error) {
      console.error('❌ EnvLoader: Failed to load environment variables:', error);
      this.envVars = this._getDefaultEnvironmentVariables();
      this.isLoaded = true;
      return this.envVars;
    }
  }

  /**
   * Load from build-time injected variables (Vite)
   * @private
   * @returns {Promise<Object>} Environment variables
   */
  async _loadFromBuildTimeInjection() {
    try {
      console.log('🔧 EnvLoader: Loading from Vite build-time injection');

      const envVars = {};

      // Strategy 1: Check for Vite-injected environment object and assign to window
      if (typeof window !== 'undefined') {
        // Check if we have the Vite-defined environment object
        let envObject = null;

        // Try to get the environment object from Vite's define replacement
        try {
          // This will be replaced by Vite with the actual environment object
          envObject = __MVAT_ENV_OBJECT__;
        } catch (e) {
          // Fallback: __MVAT_ENV_OBJECT__ not defined, try other methods
          console.log('🔧 EnvLoader: __MVAT_ENV_OBJECT__ not available, trying alternatives');
        }

        if (envObject) {
          console.log('🔧 EnvLoader: Found environment object, assigning to window.__MVAT_ENV__');
          window.__MVAT_ENV__ = envObject;
          console.log(`✅ EnvLoader: Assigned ${Object.keys(envObject).length} variables to window.__MVAT_ENV__`);

          // Copy all environment variables
          Object.keys(envObject).forEach(key => {
            if (this._isKnownEnvVar(key)) {
              envVars[key] = envObject[key];
            }
          });

          if (Object.keys(envVars).length > 0) {
            console.log(`✅ EnvLoader: Loaded ${Object.keys(envVars).length} variables from environment object`);
            console.log('🔧 EnvLoader: Available variables:', Object.keys(envVars).join(', '));
            return envVars;
          }
        }
      }

      // Strategy 2: Check for global environment variables (already assigned)
      if (typeof window !== 'undefined' && window.__MVAT_ENV__) {
        console.log('🔧 EnvLoader: Loading from existing window.__MVAT_ENV__');
        const globalEnv = window.__MVAT_ENV__;

        // Copy all environment variables
        Object.keys(globalEnv).forEach(key => {
          if (this._isKnownEnvVar(key)) {
            envVars[key] = globalEnv[key];
          }
        });

        if (Object.keys(envVars).length > 0) {
          console.log(`✅ EnvLoader: Loaded ${Object.keys(envVars).length} variables from window.__MVAT_ENV__`);
          console.log('🔧 EnvLoader: Available variables:', Object.keys(envVars).join(', '));
          return envVars;
        }
      }

      // Strategy 3: Check if Vite injected environment variables via import.meta.env
      if (typeof import.meta !== 'undefined' && import.meta.env) {
        console.log('🔧 EnvLoader: Checking import.meta.env for environment variables');

        const metaEnv = import.meta.env;
        console.log('🔧 EnvLoader: Available import.meta.env keys:', Object.keys(metaEnv));

        // Extract MVAT-specific environment variables
        Object.keys(metaEnv).forEach(key => {
          if (key.startsWith('VITE_') || this._isKnownEnvVar(key)) {
            // Remove VITE_ prefix if present
            const cleanKey = key.startsWith('VITE_') ? key.substring(5) : key;
            if (this._isKnownEnvVar(cleanKey)) {
              envVars[cleanKey] = metaEnv[key];
            }
          }
        });

        if (Object.keys(envVars).length > 0) {
          console.log(`✅ EnvLoader: Loaded ${Object.keys(envVars).length} variables from import.meta.env`);
          console.log('🔧 EnvLoader: Available variables:', Object.keys(envVars).join(', '));
          return envVars;
        }
      }

      console.log('⚠️ EnvLoader: No environment variables found in build-time injection');
      return {};
    } catch (error) {
      console.warn('⚠️ EnvLoader: Build-time injection not available:', error.message);
      return {};
    }
  }

  /**
   * Load from Chrome storage
   * @private
   * @returns {Promise<Object>} Environment variables
   */
  async _loadFromChromeStorage() {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
        console.log('🔧 EnvLoader: Loading from Chrome storage');

        const result = await chrome.storage.local.get(['mvat_env_vars']);
        if (result.mvat_env_vars) {
          console.log('✅ EnvLoader: Loaded from Chrome storage');
          return result.mvat_env_vars;
        }
      }
      return {};
    } catch (error) {
      console.warn('⚠️ EnvLoader: Chrome storage not available:', error.message);
      return {};
    }
  }

  /**
   * Load from fetch (development mode)
   * @private
   * @returns {Promise<Object>} Environment variables
   */
  async _loadFromFetch() {
    try {
      // Only try in development mode
      if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
        console.log('🔧 EnvLoader: Attempting to load from development server');

        const response = await fetch('/api/env');
        if (response.ok) {
          const envVars = await response.json();
          console.log('✅ EnvLoader: Loaded from development server');
          return envVars;
        }
      }
      return {};
    } catch (error) {
      console.warn('⚠️ EnvLoader: Development server not available:', error.message);
      return {};
    }
  }

  /**
   * Load environment variables from Chrome extension context (merged functionality)
   * @private
   * @returns {Promise<Object>} Environment variables
   */
  async _loadFromChromeExtension() {
    try {
      console.log('🔧 EnvLoader: Chrome extension context loading');
      console.log('🔧 EnvLoader: Context:', {
        isChrome: typeof chrome !== 'undefined',
        hasWindow: typeof window !== 'undefined',
        protocol: typeof window !== 'undefined' ? window.location.protocol : 'unknown',
        hostname: typeof window !== 'undefined' ? window.location.hostname : 'unknown'
      });

      let envVars = {};

      // Sub-strategy 1: Try to load from Vite-injected global variable
      console.log('🔧 EnvLoader: Chrome extension sub-strategy 1 - Vite global injection');
      envVars = await this._loadFromViteGlobal();

      if (Object.keys(envVars).length === 0) {
        // Sub-strategy 2: Try to load from import.meta.env
        console.log('🔧 EnvLoader: Chrome extension sub-strategy 2 - import.meta.env');
        envVars = await this._loadFromImportMeta();
      }

      if (Object.keys(envVars).length === 0) {
        // Sub-strategy 3: Try to load from Chrome storage
        console.log('🔧 EnvLoader: Chrome extension sub-strategy 3 - Chrome storage');
        envVars = await this._loadFromChromeStorageExtension();
      }

      if (Object.keys(envVars).length > 0) {
        console.log(`✅ EnvLoader: Loaded ${Object.keys(envVars).length} variables from Chrome extension context`);

        // Development mode: Log summary only (reduced spam)
        if (this._isDevelopmentMode()) {
          console.log('🔍 EnvLoader: Chrome Extension Variables Summary');
          console.log('📊 Chrome extension variables:', {
            total: Object.keys(envVars).length,
            apiKeys: Object.keys(envVars).filter(k => k.includes('API_KEY')).length
          });
        }
      }

      return envVars;

    } catch (error) {
      console.warn('⚠️ EnvLoader: Chrome extension loading failed:', error.message);
      return {};
    }
  }

  /**
   * Check if a key is a known environment variable
   * @private
   * @param {string} key - Environment variable key
   * @returns {boolean} True if known
   */
  _isKnownEnvVar(key) {
    // Use the centralized function from defaultEnvironment.js
    return isKnownEnvVar(key);
  }

  /**
   * Load from Vite-injected global variable (Chrome extension specific)
   * @private
   * @returns {Promise<Object>} Environment variables
   */
  async _loadFromViteGlobal() {
    try {
      console.log('🔧 EnvLoader: Checking for Vite-injected globals');

      const envVars = {};

      // Check for the Vite-defined environment object
      if (typeof window !== 'undefined') {
        // Try to access the Vite-injected environment object
        let envObject = null;

        try {
          // This should be replaced by Vite with the actual environment object
          if (typeof __MVAT_ENV_OBJECT__ !== 'undefined') {
            envObject = __MVAT_ENV_OBJECT__;
            console.log('🔧 EnvLoader: Found __MVAT_ENV_OBJECT__');
          }
        } catch (e) {
          console.log('🔧 EnvLoader: __MVAT_ENV_OBJECT__ not available');
        }

        // If we have the environment object, use it
        if (envObject && typeof envObject === 'object') {
          console.log('🔧 EnvLoader: Processing environment object');
          Object.keys(envObject).forEach(key => {
            if (this._isKnownEnvVar(key)) {
              envVars[key] = envObject[key];
            }
          });

          if (Object.keys(envVars).length > 0) {
            console.log(`✅ EnvLoader: Loaded ${Object.keys(envVars).length} variables from Vite global`);
            return envVars;
          }
        }

        // Check if environment variables were already assigned to window
        if (window.__MVAT_ENV__) {
          console.log('🔧 EnvLoader: Found existing window.__MVAT_ENV__');
          const globalEnv = window.__MVAT_ENV__;
          Object.keys(globalEnv).forEach(key => {
            if (this._isKnownEnvVar(key)) {
              envVars[key] = globalEnv[key];
            }
          });

          if (Object.keys(envVars).length > 0) {
            console.log(`✅ EnvLoader: Loaded ${Object.keys(envVars).length} variables from window.__MVAT_ENV__`);
            return envVars;
          }
        }
      }

      console.log('⚠️ EnvLoader: No Vite global variables found');
      return {};
    } catch (error) {
      console.warn('⚠️ EnvLoader: Vite global loading failed:', error.message);
      return {};
    }
  }

  /**
   * Load from import.meta.env (Chrome extension specific)
   * @private
   * @returns {Promise<Object>} Environment variables
   */
  async _loadFromImportMeta() {
    try {
      if (typeof import.meta !== 'undefined' && import.meta.env) {
        console.log('🔧 EnvLoader: Loading from import.meta.env');

        const envVars = {};
        const metaEnv = import.meta.env;

        Object.keys(metaEnv).forEach(key => {
          // Handle both VITE_ prefixed and direct environment variables
          if (key.startsWith('VITE_') || this._isKnownEnvVar(key)) {
            const cleanKey = key.startsWith('VITE_') ? key.substring(5) : key;
            if (this._isKnownEnvVar(cleanKey)) {
              envVars[cleanKey] = metaEnv[key];
            }
          }
        });

        if (Object.keys(envVars).length > 0) {
          console.log(`✅ EnvLoader: Loaded ${Object.keys(envVars).length} variables from import.meta.env`);
          return envVars;
        }
      }

      console.log('⚠️ EnvLoader: import.meta.env not available or empty');
      return {};
    } catch (error) {
      console.warn('⚠️ EnvLoader: import.meta.env loading failed:', error.message);
      return {};
    }
  }

  /**
   * Load from Chrome storage (Chrome extension specific)
   * @private
   * @returns {Promise<Object>} Environment variables
   */
  async _loadFromChromeStorageExtension() {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
        console.log('🔧 EnvLoader: Loading from Chrome storage');

        const result = await chrome.storage.local.get(['mvat_env_vars']);
        if (result.mvat_env_vars) {
          console.log('✅ EnvLoader: Loaded from Chrome storage');
          return result.mvat_env_vars;
        }
      }
      return {};
    } catch (error) {
      console.warn('⚠️ EnvLoader: Chrome storage not available:', error.message);
      return {};
    }
  }

  // REMOVED: _getDefaultEnvironmentVariables() method
  // Now using shared defaults from src/config/defaultEnvironment.js

  /**
   * Get environment variable value
   * @param {string} key - Environment variable key
   * @param {*} defaultValue - Default value if not found
   * @returns {*} Environment variable value
   */
  get(key, defaultValue = null) {
    if (!this.isLoaded) {
      console.warn('⚠️ EnvLoader: Environment variables not loaded yet');
      return defaultValue;
    }

    return this.envVars[key] || defaultValue;
  }

  /**
   * Get all environment variables
   * @returns {Object} All environment variables
   */
  getAll() {
    if (!this.isLoaded) {
      console.warn('⚠️ EnvLoader: Environment variables not loaded yet');
      return {};
    }

    return { ...this.envVars };
  }

  /**
   * Check if environment variables are loaded
   * @returns {boolean} True if loaded
   */
  isReady() {
    return this.isLoaded;
  }

  /**
   * Store environment variables in Chrome storage
   * @param {Object} envVars - Environment variables to store
   * @returns {Promise<boolean>} Success status
   */
  async store(envVars) {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
        await chrome.storage.local.set({ mvat_env_vars: envVars });
        console.log('✅ EnvLoader: Environment variables stored in Chrome storage');
        return true;
      }
      return false;
    } catch (error) {
      console.error('❌ EnvLoader: Failed to store environment variables:', error);
      return false;
    }
  }

  /**
   * Check if we're in development mode
   * @private
   * @param {Object} [envVars] - Optional environment variables to check
   * @returns {boolean} True if in development mode
   */
  _isDevelopmentMode(envVars = null) {
    // Use provided envVars or fall back to loaded environment
    const vars = envVars || this.envVars;

    // Check multiple indicators for development mode
    const debugMode = (vars && vars.DEBUG_MODE === 'true') || this.get('DEBUG_MODE') === 'true';
    const nodeEnv = (vars && vars.NODE_ENV === 'development') || this.get('NODE_ENV') === 'development';
    const isLocalhost = typeof window !== 'undefined' &&
                       (window.location.hostname === 'localhost' ||
                        window.location.hostname === '127.0.0.1' ||
                        window.location.protocol === 'file:');

    return debugMode || nodeEnv || isLocalhost;
  }

  /**
   * Mask sensitive values for logging
   * @private
   * @param {string} key - Environment variable key
   * @param {string} value - Environment variable value
   * @returns {string} Masked value for safe logging
   */
  _maskSensitiveValue(key, value) {
    if (!value) { return 'undefined'; }

    // List of sensitive keys that should be masked
    const sensitiveKeys = [
      'API_KEY', 'SECRET', 'TOKEN', 'PASSWORD', 'ENCRYPTION_KEY',
      'STRIPE_SECRET_KEY', 'WEBHOOK_SECRET', 'DEEPSEEK_API_KEY',
      'FAKTUROWNIA_API_TOKEN', 'SENTRY_DSN'
    ];

    const isSensitive = sensitiveKeys.some(sensitiveKey =>
      key.toUpperCase().includes(sensitiveKey)
    );

    if (isSensitive) {
      // Show first 3 and last 3 characters for API keys, mask the middle
      if (value.length > 6) {
        return `${value.substring(0, 3)}***${value.substring(value.length - 3)}`;
      }
      return '***masked***';

    }

    return value;
  }
}

// Export singleton instance
export const envLoader = new EnvLoader();
