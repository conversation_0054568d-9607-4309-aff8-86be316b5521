/**
 * Confidence Scoring Utilities - Calculate extraction quality and reliability scores
 * Provides comprehensive confidence assessment for AI and fallback extractions
 *
 * Features:
 * - Multi-factor confidence calculation
 * - Field completeness scoring
 * - Data quality assessment
 * - Extraction method weighting
 * - Business logic validation scoring
 * - Comparative confidence analysis
 */

/**
 * Calculate comprehensive confidence score for extracted data
 * @param {Object} extractedData - Extracted invoice data
 * @param {Object} options - Scoring options
 * @returns {Object} - Confidence analysis result
 */
export function calculateConfidenceScore(extractedData, options = {}) {
  const {
    extractionMethod = 'unknown',
    originalText = '',
    validationResult = null,
    processingTime = 0
  } = options;

  try {
    // Calculate individual confidence components
    const completenessScore = calculateCompletenessScore(extractedData);
    const qualityScore = calculateQualityScore(extractedData);
    const validationScore = calculateValidationScore(validationResult);
    const methodScore = calculateMethodScore(extractionMethod);
    const consistencyScore = calculateConsistencyScore(extractedData);

    // Weight the different components
    const weights = {
      completeness: 0.3,
      quality: 0.25,
      validation: 0.2,
      method: 0.15,
      consistency: 0.1
    };

    // Calculate weighted overall score
    const overallScore = (
      completenessScore * weights.completeness +
      qualityScore * weights.quality +
      validationScore * weights.validation +
      methodScore * weights.method +
      consistencyScore * weights.consistency
    );

    // Determine confidence level
    const confidenceLevel = getConfidenceLevel(overallScore);

    // Generate recommendations
    const recommendations = generateRecommendations(extractedData, {
      completenessScore,
      qualityScore,
      validationScore,
      methodScore,
      consistencyScore
    });

    return {
      overallScore: Math.round(overallScore * 100) / 100,
      confidenceLevel,
      components: {
        completeness: Math.round(completenessScore * 100) / 100,
        quality: Math.round(qualityScore * 100) / 100,
        validation: Math.round(validationScore * 100) / 100,
        method: Math.round(methodScore * 100) / 100,
        consistency: Math.round(consistencyScore * 100) / 100
      },
      weights,
      recommendations,
      extractionMethod,
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    console.error('❌ Confidence scoring error:', error);
    return {
      overallScore: 0,
      confidenceLevel: 'unknown',
      components: {},
      recommendations: ['Error calculating confidence score'],
      extractionMethod,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Calculate completeness score based on field coverage
 * @param {Object} data - Extracted data
 * @returns {number} - Completeness score (0-1)
 */
function calculateCompletenessScore(data) {
  if (!data || typeof data !== 'object') { return 0; }

  const fieldWeights = {
    // Critical fields (high weight)
    invoice_number: 4,
    seller_name: 4,
    buyer_name: 4,
    total_gross: 4,

    // Important fields (medium weight)
    issue_date: 3,
    currency: 3,
    total_net: 3,
    total_vat: 3,

    // Useful fields (low weight)
    due_date: 2,
    sale_date: 2,
    seller_tax_id: 2,
    buyer_tax_id: 2,
    seller_address: 1,
    buyer_address: 1,
    payment_method: 1,
    description: 1
  };

  let totalWeight = 0;
  let achievedWeight = 0;

  Object.entries(fieldWeights).forEach(([field, weight]) => {
    totalWeight += weight;
    if (data[field] && data[field] !== null && data[field] !== '') {
      achievedWeight += weight;
    }
  });

  return totalWeight > 0 ? achievedWeight / totalWeight : 0;
}

/**
 * Calculate quality score based on data format and validity
 * @param {Object} data - Extracted data
 * @returns {number} - Quality score (0-1)
 */
function calculateQualityScore(data) {
  if (!data || typeof data !== 'object') { return 0; }

  let qualityChecks = 0;
  let passedChecks = 0;

  // Check invoice number format
  qualityChecks++;
  if (data.invoice_number && isValidInvoiceNumber(data.invoice_number)) {
    passedChecks++;
  }

  // Check date formats
  ['issue_date', 'due_date', 'sale_date'].forEach(dateField => {
    if (data[dateField]) {
      qualityChecks++;
      if (isValidDate(data[dateField])) {
        passedChecks++;
      }
    }
  });

  // Check monetary amounts
  ['total_gross', 'total_net', 'total_vat'].forEach(amountField => {
    if (data[amountField] !== null && data[amountField] !== undefined) {
      qualityChecks++;
      if (isValidAmount(data[amountField])) {
        passedChecks++;
      }
    }
  });

  // Check currency format
  if (data.currency) {
    qualityChecks++;
    if (isValidCurrency(data.currency)) {
      passedChecks++;
    }
  }

  // Check tax ID formats
  ['seller_tax_id', 'buyer_tax_id'].forEach(taxField => {
    if (data[taxField]) {
      qualityChecks++;
      if (isValidTaxId(data[taxField])) {
        passedChecks++;
      }
    }
  });

  return qualityChecks > 0 ? passedChecks / qualityChecks : 0;
}

/**
 * Calculate validation score based on validation results
 * @param {Object} validationResult - Validation result object
 * @returns {number} - Validation score (0-1)
 */
function calculateValidationScore(validationResult) {
  if (!validationResult) { return 0.5; } // Neutral score if no validation

  const { isValid, errors = [], warnings = [] } = validationResult;

  if (isValid && errors.length === 0) {
    return warnings.length === 0 ? 1.0 : 0.9;
  }

  // Penalize based on error severity
  const criticalErrors = errors.filter(e => e.type === 'required' || e.type === 'critical');
  const minorErrors = errors.filter(e => e.type !== 'required' && e.type !== 'critical');

  let score = 1.0;
  score -= criticalErrors.length * 0.3; // Heavy penalty for critical errors
  score -= minorErrors.length * 0.1; // Light penalty for minor errors
  score -= warnings.length * 0.05; // Very light penalty for warnings

  return Math.max(0, score);
}

/**
 * Calculate method score based on extraction method
 * @param {string} method - Extraction method
 * @returns {number} - Method score (0-1)
 */
function calculateMethodScore(method) {
  const methodScores = {
    'pdf+ai': 1.0, // Best: PDF text + AI
    'ocr+ai': 0.9, // Good: OCR + AI
    'pdf+fallback': 0.7, // Decent: PDF text + fallback
    'ocr+fallback': 0.6, // OK: OCR + fallback
    'ai': 0.8, // Good: AI only
    'fallback': 0.5, // Basic: Fallback only
    'pdf': 0.6, // OK: PDF only
    'ocr': 0.4, // Poor: OCR only
    'unknown': 0.3 // Very poor: Unknown method
  };

  return methodScores[method] || methodScores.unknown;
}

/**
 * Calculate consistency score based on business logic
 * @param {Object} data - Extracted data
 * @returns {number} - Consistency score (0-1)
 */
function calculateConsistencyScore(data) {
  if (!data || typeof data !== 'object') { return 0; }

  let consistencyChecks = 0;
  let passedChecks = 0;

  // Check total calculation consistency
  if (data.total_net && data.total_vat && data.total_gross) {
    consistencyChecks++;
    const calculatedTotal = parseFloat(data.total_net) + parseFloat(data.total_vat);
    const actualTotal = parseFloat(data.total_gross);
    const tolerance = 0.01; // 1 cent tolerance

    if (Math.abs(calculatedTotal - actualTotal) <= tolerance) {
      passedChecks++;
    }
  }

  // Check date logic consistency
  if (data.issue_date && data.due_date) {
    consistencyChecks++;
    const issueDate = new Date(data.issue_date);
    const dueDate = new Date(data.due_date);

    if (dueDate >= issueDate) {
      passedChecks++;
    }
  }

  // Check VAT rate consistency
  if (data.vat_rates && Array.isArray(data.vat_rates)) {
    consistencyChecks++;
    const validRates = data.vat_rates.every(rate =>
      typeof rate === 'number' && rate >= 0 && rate <= 1
    );

    if (validRates) {
      passedChecks++;
    }
  }

  // Check amount reasonableness
  if (data.total_gross) {
    consistencyChecks++;
    const amount = parseFloat(data.total_gross);

    // Reasonable invoice amounts (0.01 to 1,000,000)
    if (amount >= 0.01 && amount <= 1000000) {
      passedChecks++;
    }
  }

  return consistencyChecks > 0 ? passedChecks / consistencyChecks : 0.5;
}

/**
 * Determine confidence level from score
 * @param {number} score - Overall confidence score
 * @returns {string} - Confidence level
 */
function getConfidenceLevel(score) {
  if (score >= 0.9) { return 'very_high'; }
  if (score >= 0.8) { return 'high'; }
  if (score >= 0.7) { return 'medium'; }
  if (score >= 0.5) { return 'low'; }
  return 'very_low';
}

/**
 * Generate recommendations based on confidence components
 * @param {Object} data - Extracted data
 * @param {Object} scores - Component scores
 * @returns {Array} - Recommendations
 */
function generateRecommendations(data, scores) {
  const recommendations = [];

  if (scores.completenessScore < 0.7) {
    recommendations.push('Consider manual review - some important fields are missing');
  }

  if (scores.qualityScore < 0.6) {
    recommendations.push('Data quality issues detected - verify extracted values');
  }

  if (scores.validationScore < 0.7) {
    recommendations.push('Validation errors found - check field formats and values');
  }

  if (scores.methodScore < 0.7) {
    recommendations.push('Consider using AI extraction for better accuracy');
  }

  if (scores.consistencyScore < 0.6) {
    recommendations.push('Business logic inconsistencies detected - verify calculations');
  }

  if (recommendations.length === 0) {
    recommendations.push('Extraction quality is good - data appears reliable');
  }

  return recommendations;
}

// Validation helper functions
function isValidInvoiceNumber(invoiceNumber) {
  return typeof invoiceNumber === 'string' &&
         invoiceNumber.length >= 3 &&
         invoiceNumber.length <= 50;
}

function isValidDate(dateValue) {
  const date = new Date(dateValue);
  return !isNaN(date.getTime()) &&
         date.getFullYear() >= 2000 &&
         date.getFullYear() <= 2030;
}

function isValidAmount(amount) {
  const num = parseFloat(amount);
  return !isNaN(num) && num >= 0 && num <= 10000000;
}

function isValidCurrency(currency) {
  const validCurrencies = ['PLN', 'EUR', 'USD', 'GBP', 'CHF', 'CZK', 'SEK', 'NOK', 'DKK'];
  return typeof currency === 'string' &&
         validCurrencies.includes(currency.toUpperCase());
}

function isValidTaxId(taxId) {
  return typeof taxId === 'string' &&
         taxId.length >= 9 &&
         taxId.length <= 15 &&
         /^\d+$/.test(taxId.replace(/[\s\-]/g, ''));
}

// Self-test functionality for Node.js environment
if (typeof module !== 'undefined' && require.main === module) {
  console.log('🧪 Testing confidenceScoring...');

  const testData = {
    invoice_number: 'INV-2024-001',
    seller_name: 'Test Company',
    buyer_name: 'Customer Ltd',
    total_gross: 1234.56,
    total_net: 1000.00,
    total_vat: 234.56,
    issue_date: '2024-01-15',
    currency: 'PLN'
  };

  const result = calculateConfidenceScore(testData, {
    extractionMethod: 'pdf+ai',
    validationResult: { isValid: true, errors: [], warnings: [] }
  });

  console.log('✅ Confidence calculation test:', result.overallScore > 0 ? 'passed' : 'failed');
  console.log('✅ Completeness score test:', result.components.completeness > 0 ? 'passed' : 'failed');
  console.log('✅ Quality score test:', result.components.quality > 0 ? 'passed' : 'failed');
  console.log('✅ Confidence level test:', result.confidenceLevel ? 'passed' : 'failed');
  console.log('✅ Recommendations test:', Array.isArray(result.recommendations) ? 'passed' : 'failed');

  // Test with poor data
  const poorData = { invoice_number: 'X' };
  const poorResult = calculateConfidenceScore(poorData, { extractionMethod: 'fallback' });

  console.log('✅ Poor data test:', poorResult.overallScore < 0.5 ? 'passed' : 'failed');

  console.log('🎉 All confidenceScoring tests passed!');
}
