/**
 * PDF Utility Functions
 * Supporting utilities for PDF processing operations
 *
 * Features:
 * - PDF file validation
 * - Text quality assessment
 * - Memory optimization helpers
 * - Error handling utilities
 */

/**
 * Check if a file is a valid PDF
 * @param {File} file - File to check
 * @returns {boolean} - Whether file is a valid PDF
 */
export function isPDFFile(file) {
  if (!file) { return false; }

  // Check MIME type
  if (file.type === 'application/pdf') { return true; }

  // Check file extension as fallback
  const fileName = file.name.toLowerCase();
  return fileName.endsWith('.pdf');
}

/**
 * Validate PDF file for processing
 * @param {File} file - PDF file to validate
 * @param {Object} options - Validation options
 * @returns {Object} - Validation result
 */
export function validatePDFFile(file, options = {}) {
  const {
    maxSize = 10 * 1024 * 1024, // 10MB
    minSize = 1024, // 1KB
    allowedExtensions = ['.pdf']
  } = options;

  const errors = [];
  const warnings = [];

  // Basic file checks
  if (!file) {
    errors.push('No file provided');
    return { isValid: false, errors, warnings };
  }

  // File type validation
  if (!isPDFFile(file)) {
    errors.push(`Invalid file type: ${file.type}. Expected PDF file`);
  }

  // File size validation
  if (file.size > maxSize) {
    const sizeMB = (file.size / 1024 / 1024).toFixed(1);
    const maxSizeMB = (maxSize / 1024 / 1024).toFixed(1);
    errors.push(`File too large: ${sizeMB}MB. Maximum allowed: ${maxSizeMB}MB`);
  }

  if (file.size < minSize) {
    errors.push(`File too small: ${file.size} bytes. Minimum required: ${minSize} bytes`);
  }

  // File extension validation
  const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
  if (!allowedExtensions.includes(fileExtension)) {
    errors.push(`Invalid file extension: ${fileExtension}. Allowed: ${allowedExtensions.join(', ')}`);
  }

  // File name validation
  if (file.name.length > 255) {
    warnings.push('File name is very long and may cause issues');
  }

  // Check for suspicious file names
  const suspiciousPatterns = [
    /\.exe$/i,
    /\.scr$/i,
    /\.bat$/i,
    /\.cmd$/i
  ];

  for (const pattern of suspiciousPatterns) {
    if (pattern.test(file.name)) {
      warnings.push('File name contains suspicious extension');
      break;
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    fileInfo: {
      name: file.name,
      size: file.size,
      type: file.type,
      lastModified: file.lastModified
    }
  };
}

/**
 * Estimate PDF processing time based on file size
 * @param {File} file - PDF file
 * @returns {number} - Estimated processing time in milliseconds
 */
export function estimatePDFProcessingTime(file) {
  if (!file) { return 0; }

  const sizeInMB = file.size / (1024 * 1024);

  // Base time: 2 seconds
  // Additional time: 1 second per MB
  const estimatedTime = 2000 + (sizeInMB * 1000);

  // Cap at 30 seconds
  return Math.min(estimatedTime, 30000);
}

// Import consolidated file formatting utilities
import { formatFileSize } from './file/formatters.js';

// Re-export for backward compatibility
export { formatFileSize };

/**
 * Check if extracted text contains invoice-related content
 * @param {string} text - Extracted text
 * @returns {Object} - Content analysis result
 */
export function analyzeInvoiceContent(text) {
  if (!text || typeof text !== 'string') {
    return {
      hasInvoiceContent: false,
      confidence: 0,
      foundKeywords: [],
      analysis: 'No text provided'
    };
  }

  const lowerText = text.toLowerCase();

  // Invoice keywords with weights
  const keywordGroups = {
    invoice: {
      keywords: ['faktura', 'invoice', 'bill', 'receipt'],
      weight: 3
    },
    tax: {
      keywords: ['vat', 'tax', 'podatek', 'nip', 'regon'],
      weight: 2
    },
    parties: {
      keywords: ['sprzedawca', 'nabywca', 'seller', 'buyer', 'client', 'klient'],
      weight: 2
    },
    amounts: {
      keywords: ['kwota', 'amount', 'suma', 'total', 'razem', 'netto', 'brutto'],
      weight: 2
    },
    payment: {
      keywords: ['payment', 'płatność', 'zapłata', 'due', 'termin'],
      weight: 1
    }
  };

  let totalScore = 0;
  const foundKeywords = [];

  // Analyze each keyword group
  for (const [group, config] of Object.entries(keywordGroups)) {
    const groupKeywords = config.keywords.filter(keyword =>
      lowerText.includes(keyword)
    );

    if (groupKeywords.length > 0) {
      totalScore += config.weight * groupKeywords.length;
      foundKeywords.push(...groupKeywords);
    }
  }

  // Calculate confidence (0-100)
  const maxPossibleScore = Object.values(keywordGroups)
    .reduce((sum, group) => sum + (group.weight * group.keywords.length), 0);

  const confidence = Math.min(100, (totalScore / maxPossibleScore) * 100);

  return {
    hasInvoiceContent: confidence >= 30,
    confidence: Math.round(confidence),
    foundKeywords: [...new Set(foundKeywords)], // Remove duplicates
    totalScore,
    analysis: confidence >= 70 ? 'High confidence invoice content' :
      confidence >= 30 ? 'Likely invoice content' :
        'Low confidence invoice content'
  };
}

/**
 * Clean and normalize extracted text
 * @param {string} text - Raw extracted text
 * @returns {string} - Cleaned text
 */
export function cleanExtractedText(text) {
  if (!text || typeof text !== 'string') { return ''; }

  return text
    // Remove excessive whitespace
    .replace(/\s+/g, ' ')
    // Remove leading/trailing whitespace
    .trim()
    // Normalize line breaks
    .replace(/\r\n/g, '\n')
    .replace(/\r/g, '\n')
    // Remove multiple consecutive line breaks
    .replace(/\n{3,}/g, '\n\n')
    // Remove control characters except tabs and line breaks
    .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
}

/**
 * Extract potential invoice numbers from text
 * @param {string} text - Text to search
 * @returns {Array} - Array of potential invoice numbers
 */
export function extractInvoiceNumbers(text) {
  if (!text) { return []; }

  const patterns = [
    // Polish invoice patterns
    /(?:faktura|nr|no\.?)\s*:?\s*([A-Z0-9\/\-]{3,20})/gi,
    /([A-Z]{2,3}\/\d{4}\/\d+)/gi,
    /(\d{4}\/\d+\/[A-Z]+)/gi,
    // International patterns
    /(?:invoice|inv)\s*:?\s*#?([A-Z0-9\-]{3,20})/gi,
    // General alphanumeric patterns
    /([A-Z]{1,3}\d{4,8})/gi
  ];

  const found = new Set();

  for (const pattern of patterns) {
    const matches = text.matchAll(pattern);
    for (const match of matches) {
      const invoiceNumber = match[1].trim();
      if (invoiceNumber.length >= 3 && invoiceNumber.length <= 20) {
        found.add(invoiceNumber);
      }
    }
  }

  return Array.from(found);
}

/**
 * Memory optimization helper for large PDF processing
 * @param {Function} processingFunction - Function to execute with memory monitoring
 * @param {Object} options - Memory options
 * @returns {Promise} - Processing result
 */
export async function withMemoryOptimization(processingFunction, options = {}) {
  const {
    maxMemoryMB = 100,
    gcInterval = 5000 // 5 seconds
  } = options;

  let gcTimer;

  try {
    // Start periodic garbage collection if available
    if (typeof window !== 'undefined' && window.gc) {
      gcTimer = setInterval(() => {
        window.gc();
      }, gcInterval);
    }

    // Monitor memory usage if available
    const startMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;

    const result = await processingFunction();

    const endMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
    const memoryUsed = (endMemory - startMemory) / (1024 * 1024); // MB

    if (memoryUsed > maxMemoryMB) {
      console.warn(`High memory usage detected: ${memoryUsed.toFixed(1)}MB`);
    }

    return result;

  } finally {
    if (gcTimer) {
      clearInterval(gcTimer);
    }

    // Force garbage collection if available
    if (typeof window !== 'undefined' && window.gc) {
      window.gc();
    }
  }
}

/**
 * Create a progress tracker for PDF processing
 * @param {Function} onProgress - Progress callback
 * @returns {Object} - Progress tracker
 */
export function createPDFProgressTracker(onProgress) {
  let currentStage = 'initializing';
  let currentProgress = 0;

  return {
    setStage(stage, progress = null) {
      currentStage = stage;
      if (progress !== null) {
        currentProgress = progress;
      }

      if (onProgress) {
        onProgress({
          stage: currentStage,
          progress: currentProgress,
          timestamp: Date.now()
        });
      }
    },

    updateProgress(progress) {
      currentProgress = Math.max(0, Math.min(100, progress));

      if (onProgress) {
        onProgress({
          stage: currentStage,
          progress: currentProgress,
          timestamp: Date.now()
        });
      }
    },

    getCurrentState() {
      return {
        stage: currentStage,
        progress: currentProgress
      };
    }
  };
}
