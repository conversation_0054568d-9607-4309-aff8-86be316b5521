/**
 * ProcessingLogger - Centralized logging utility for document processing pipeline
 * Provides structured logging with timestamps, UUIDs, and performance tracking
 */

export class ProcessingLogger {
  constructor() {
    this.logLevel = 'info'; // debug, info, warn, error
    this.enablePerformanceTracking = true;
    this.enableConsoleOutput = true;
    this.logHistory = new Map(); // Store logs by UUID
    this.performanceTimers = new Map(); // Store performance timers
    this.dataPreviewLength = 200; // Characters to show in data previews
    this.enableConsoleGrouping = true; // Enable console.group for better organization
    this.enableMemoryTracking = true; // Track memory usage
  }

  /**
   * Set logging configuration
   * @param {Object} config - Configuration options
   */
  configure(config = {}) {
    this.logLevel = config.logLevel || this.logLevel;
    this.enablePerformanceTracking = config.enablePerformanceTracking ?? this.enablePerformanceTracking;
    this.enableConsoleOutput = config.enableConsoleOutput ?? this.enableConsoleOutput;
    this.dataPreviewLength = config.dataPreviewLength ?? this.dataPreviewLength;
    this.enableConsoleGrouping = config.enableConsoleGrouping ?? this.enableConsoleGrouping;
    this.enableMemoryTracking = config.enableMemoryTracking ?? this.enableMemoryTracking;
  }

  /**
   * Generate timestamp in ISO format with milliseconds
   * @returns {string} - Formatted timestamp
   */
  getTimestamp() {
    return new Date().toISOString();
  }

  /**
   * Format log entry with consistent structure
   * @param {string} level - Log level
   * @param {string} stage - Processing stage
   * @param {string} message - Log message
   * @param {string} uploadId - Upload UUID
   * @param {Object} data - Additional data
   * @returns {Object} - Formatted log entry
   */
  formatLogEntry(level, stage, message, uploadId, data = {}) {
    const timestamp = this.getTimestamp();
    const logEntry = {
      timestamp,
      level: level.toUpperCase(),
      stage,
      uploadId,
      message,
      ...data
    };

    // Store in history (only if uploadId is provided)
    if (uploadId) {
      if (!this.logHistory.has(uploadId)) {
        this.logHistory.set(uploadId, []);
      }
      this.logHistory.get(uploadId).push(logEntry);
    }

    return logEntry;
  }

  /**
   * Output log to console with appropriate styling
   * @param {Object} logEntry - Formatted log entry
   */
  outputToConsole(logEntry) {
    if (!this.enableConsoleOutput) { return; }

    const { level, stage, uploadId, message, timestamp } = logEntry;
    const uploadIdDisplay = uploadId ? uploadId.substring(0, 8) + '...' : 'no-id';
    const prefix = `[${timestamp}] [${level}] [${stage}] [${uploadIdDisplay}]`;

    // Style based on log level
    const styles = {
      DEBUG: 'color: #888; font-style: italic;',
      INFO: 'color: #2563eb; font-weight: bold;',
      WARN: 'color: #d97706; font-weight: bold;',
      ERROR: 'color: #dc2626; font-weight: bold;'
    };

    const style = styles[level] || styles.INFO;

    // Use appropriate console method
    const consoleMethods = {
      DEBUG: console.debug,
      INFO: console.info,
      WARN: console.warn,
      ERROR: console.error
    };

    const consoleMethod = consoleMethods[level] || console.log;

    // Output with styling
    consoleMethod(`%c${prefix} ${message}`, style);

    // Output additional data if present
    const additionalData = { ...logEntry };
    delete additionalData.timestamp;
    delete additionalData.level;
    delete additionalData.stage;
    delete additionalData.uploadId;
    delete additionalData.message;

    if (Object.keys(additionalData).length > 0) {
      console.log('%c└─ Data:', 'color: #6b7280; font-style: italic;', additionalData);
    }
  }

  /**
   * Check if log level should be output
   * @param {string} level - Log level to check
   * @returns {boolean} - Whether to output this level
   */
  shouldLog(level) {
    const levels = { debug: 0, info: 1, warn: 2, error: 3 };
    return levels[level.toLowerCase()] >= levels[this.logLevel.toLowerCase()];
  }

  /**
   * Log debug message
   * @param {string} stage - Processing stage
   * @param {string} message - Log message
   * @param {string} uploadId - Upload UUID
   * @param {Object} data - Additional data
   */
  debug(stage, message, uploadId, data = {}) {
    if (!this.shouldLog('debug')) { return; }
    const logEntry = this.formatLogEntry('debug', stage, message, uploadId, data);
    this.outputToConsole(logEntry);
  }

  /**
   * Log info message
   * @param {string} stage - Processing stage
   * @param {string} message - Log message
   * @param {string} uploadId - Upload UUID
   * @param {Object} data - Additional data
   */
  info(stage, message, uploadId, data = {}) {
    if (!this.shouldLog('info')) { return; }
    const logEntry = this.formatLogEntry('info', stage, message, uploadId, data);
    this.outputToConsole(logEntry);
  }

  /**
   * Log warning message
   * @param {string} stage - Processing stage
   * @param {string} message - Log message
   * @param {string} uploadId - Upload UUID
   * @param {Object} data - Additional data
   */
  warn(stage, message, uploadId, data = {}) {
    if (!this.shouldLog('warn')) { return; }
    const logEntry = this.formatLogEntry('warn', stage, message, uploadId, data);
    this.outputToConsole(logEntry);
  }

  /**
   * Log error message
   * @param {string} stage - Processing stage
   * @param {string} message - Log message
   * @param {string} uploadId - Upload UUID
   * @param {Object} data - Additional data
   */
  error(stage, message, uploadId, data = {}) {
    if (!this.shouldLog('error')) { return; }
    const logEntry = this.formatLogEntry('error', stage, message, uploadId, data);
    this.outputToConsole(logEntry);
  }

  /**
   * Start performance timer for a stage
   * @param {string} stage - Processing stage
   * @param {string} uploadId - Upload UUID
   */
  startTimer(stage, uploadId) {
    if (!this.enablePerformanceTracking) { return; }

    const timerKey = `${uploadId}-${stage}`;
    this.performanceTimers.set(timerKey, {
      startTime: performance.now(),
      stage,
      uploadId
    });

    this.debug(stage, 'Performance timer started', uploadId);
  }

  /**
   * End performance timer and log duration
   * @param {string} stage - Processing stage
   * @param {string} uploadId - Upload UUID
   * @returns {number} - Duration in milliseconds
   */
  endTimer(stage, uploadId) {
    if (!this.enablePerformanceTracking) { return 0; }

    const timerKey = `${uploadId}-${stage}`;
    const timer = this.performanceTimers.get(timerKey);

    if (!timer) {
      this.warn(stage, 'Performance timer not found', uploadId);
      return 0;
    }

    const duration = performance.now() - timer.startTime;
    this.performanceTimers.delete(timerKey);

    this.info(stage, 'Performance timer completed', uploadId, {
      duration: `${duration.toFixed(2)}ms`,
      durationMs: duration
    });

    return duration;
  }

  /**
   * Get all logs for a specific upload
   * @param {string} uploadId - Upload UUID
   * @returns {Array} - Array of log entries
   */
  getLogsForUpload(uploadId) {
    return this.logHistory.get(uploadId) || [];
  }

  /**
   * Get logs filtered by stage
   * @param {string} stage - Processing stage
   * @returns {Array} - Array of log entries
   */
  getLogsByStage(stage) {
    const allLogs = [];
    for (const logs of this.logHistory.values()) {
      allLogs.push(...logs.filter(log => log.stage === stage));
    }
    return allLogs;
  }

  /**
   * Clear logs for a specific upload (memory management)
   * @param {string} uploadId - Upload UUID
   */
  clearLogsForUpload(uploadId) {
    this.logHistory.delete(uploadId);

    // Clear any remaining timers for this upload
    for (const [key, timer] of this.performanceTimers.entries()) {
      if (timer.uploadId === uploadId) {
        this.performanceTimers.delete(key);
      }
    }
  }

  /**
   * Clear all logs (memory management)
   */
  clearAllLogs() {
    this.logHistory.clear();
    this.performanceTimers.clear();
  }

  /**
   * Get summary statistics for an upload
   * @param {string} uploadId - Upload UUID
   * @returns {Object} - Summary statistics
   */
  getUploadSummary(uploadId) {
    const logs = this.getLogsForUpload(uploadId);
    if (logs.length === 0) { return null; }

    const stages = [...new Set(logs.map(log => log.stage))];
    const levels = logs.reduce((acc, log) => {
      acc[log.level] = (acc[log.level] || 0) + 1;
      return acc;
    }, {});

    const firstLog = logs[0];
    const lastLog = logs[logs.length - 1];
    const totalDuration = new Date(lastLog.timestamp) - new Date(firstLog.timestamp);

    return {
      uploadId,
      totalLogs: logs.length,
      stages,
      levels,
      startTime: firstLog.timestamp,
      endTime: lastLog.timestamp,
      totalDurationMs: totalDuration,
      hasErrors: levels.ERROR > 0,
      hasWarnings: levels.WARN > 0
    };
  }

  /**
   * Sanitize data for logging (remove sensitive information)
   * @param {any} data - Data to sanitize
   * @returns {any} - Sanitized data
   */
  sanitizeData(data) {
    if (typeof data === 'string') {
      // Remove potential API keys, tokens, or sensitive patterns
      return data.replace(/([a-zA-Z0-9_-]{20,})/g, '[REDACTED_TOKEN]')
        .replace(/(sk-[a-zA-Z0-9]{48})/g, '[REDACTED_API_KEY]')
        .replace(/(Bearer\s+[a-zA-Z0-9_-]+)/gi, 'Bearer [REDACTED]');
    }

    if (typeof data === 'object' && data !== null) {
      const sanitized = Array.isArray(data) ? [] : {};
      for (const [key, value] of Object.entries(data)) {
        // Skip sensitive keys
        if (/api[_-]?key|token|password|secret|auth/i.test(key)) {
          sanitized[key] = '[REDACTED]';
        } else {
          sanitized[key] = this.sanitizeData(value);
        }
      }
      return sanitized;
    }

    return data;
  }

  /**
   * Create data preview (truncated content for logging)
   * @param {any} data - Data to preview
   * @param {number} maxLength - Maximum length for preview
   * @returns {string} - Data preview
   */
  createDataPreview(data, maxLength = this.dataPreviewLength) {
    let preview = '';

    if (typeof data === 'string') {
      preview = data;
    } else if (typeof data === 'object' && data !== null) {
      preview = JSON.stringify(data, null, 2);
    } else {
      preview = String(data);
    }

    if (preview.length > maxLength) {
      return preview.substring(0, maxLength) + '... [truncated]';
    }

    return preview;
  }

  /**
   * Generate a unique UUID for upload tracking
   * @returns {string} - Unique UUID
   */
  generateUploadId() {
    // Generate UUID v4 format: xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * Get current memory usage (if available)
   * @returns {Object} - Memory usage information
   */
  getMemoryUsage() {
    if (!this.enableMemoryTracking) { return null; }

    try {
      if (typeof performance !== 'undefined' && performance.memory) {
        return {
          usedJSHeapSize: performance.memory.usedJSHeapSize,
          totalJSHeapSize: performance.memory.totalJSHeapSize,
          jsHeapSizeLimit: performance.memory.jsHeapSizeLimit,
          usedMB: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
          totalMB: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024)
        };
      }
    } catch (error) {
      // Memory API not available
    }

    return null;
  }

  /**
   * Log data extraction with content preview
   * @param {string} stage - Processing stage
   * @param {any} data - Extracted data
   * @param {string} uploadId - Upload UUID
   * @param {Object} metadata - Additional metadata
   */
  logDataExtraction(stage, data, uploadId, metadata = {}) {
    if (!this.shouldLog('info')) { return; }

    const sanitizedData = this.sanitizeData(data);
    const dataPreview = this.createDataPreview(sanitizedData);
    const memoryUsage = this.getMemoryUsage();

    const logData = {
      dataType: typeof data,
      dataSize: typeof data === 'string' ? data.length : JSON.stringify(data).length,
      dataPreview,
      memoryUsage,
      ...metadata
    };

    if (this.enableConsoleGrouping) {
      console.group(`📊 ${stage} - Data Extraction`);
    }

    this.info(stage, 'Data extracted successfully', uploadId, logData);

    if (this.enableConsoleGrouping) {
      console.groupEnd();
    }
  }

  /**
   * Log processing stage transformation
   * @param {string} stage - Processing stage
   * @param {any} input - Input data
   * @param {any} output - Output data
   * @param {string} uploadId - Upload UUID
   * @param {Object} metrics - Performance metrics
   */
  logProcessingStage(stage, input, output, uploadId, metrics = {}) {
    if (!this.shouldLog('info')) { return; }

    const sanitizedInput = this.sanitizeData(input);
    const sanitizedOutput = this.sanitizeData(output);
    const inputPreview = this.createDataPreview(sanitizedInput, 100);
    const outputPreview = this.createDataPreview(sanitizedOutput, 100);
    const memoryUsage = this.getMemoryUsage();

    const logData = {
      inputType: typeof input,
      outputType: typeof output,
      inputSize: typeof input === 'string' ? input.length : JSON.stringify(input).length,
      outputSize: typeof output === 'string' ? output.length : JSON.stringify(output).length,
      inputPreview,
      outputPreview,
      memoryUsage,
      ...metrics
    };

    if (this.enableConsoleGrouping) {
      console.group(`🔄 ${stage} - Processing Transformation`);
    }

    this.info(stage, 'Processing stage completed', uploadId, logData);

    if (this.enableConsoleGrouping) {
      console.groupEnd();
    }
  }

  /**
   * Log API interaction with request/response details
   * @param {Object} request - API request details
   * @param {Object} response - API response details
   * @param {string} uploadId - Upload UUID
   * @param {Object} timing - Timing information
   */
  logAPIInteraction(request, response, uploadId, timing = {}) {
    if (!this.shouldLog('info')) { return; }

    const sanitizedRequest = this.sanitizeData(request);
    const sanitizedResponse = this.sanitizeData(response);
    const requestPreview = this.createDataPreview(sanitizedRequest, 150);
    const responsePreview = this.createDataPreview(sanitizedResponse, 150);
    const memoryUsage = this.getMemoryUsage();

    const logData = {
      requestSize: JSON.stringify(request).length,
      responseSize: JSON.stringify(response).length,
      requestPreview,
      responsePreview,
      memoryUsage,
      ...timing
    };

    if (this.enableConsoleGrouping) {
      console.group('🌐 API Interaction');
    }

    this.info('api_call', 'API interaction completed', uploadId, logData);

    if (this.enableConsoleGrouping) {
      console.groupEnd();
    }
  }

  /**
   * Log processing start
   * @param {string} uploadId - Upload UUID
   * @param {string} fileName - File name
   * @param {string} fileType - File type
   * @param {number} fileSize - File size in bytes
   */
  logProcessingStart(uploadId, fileName, fileType, fileSize) {
    if (!this.shouldLog('info')) { return; }

    const logData = {
      fileName,
      fileType,
      fileSize,
      fileSizeMB: (fileSize / 1024 / 1024).toFixed(2),
      memoryUsage: this.getMemoryUsage()
    };

    if (this.enableConsoleGrouping) {
      console.group(`🚀 Processing Started - ${fileName}`);
    }

    this.info('processing_start', 'Document processing started', uploadId, logData);

    if (this.enableConsoleGrouping) {
      console.groupEnd();
    }
  }

  /**
   * Log processing completion
   * @param {string} uploadId - Upload UUID
   * @param {Object} result - Processing result
   * @param {number} totalTime - Total processing time in milliseconds
   */
  logProcessingComplete(uploadId, result, totalTime) {
    if (!this.shouldLog('info')) { return; }

    const logData = {
      success: result.success || false,
      processingTimeMs: totalTime,
      processingTimeFormatted: this.formatDuration(totalTime),
      resultSize: JSON.stringify(result).length,
      memoryUsage: this.getMemoryUsage(),
      resultPreview: this.createDataPreview(result, 200)
    };

    if (this.enableConsoleGrouping) {
      console.group(`✅ Processing Complete - ${totalTime.toFixed(2)}ms`);
    }

    this.info('processing_complete', 'Document processing completed successfully', uploadId, logData);

    if (this.enableConsoleGrouping) {
      console.groupEnd();
    }
  }

  /**
   * Log processing error
   * @param {string} uploadId - Upload UUID
   * @param {string} stage - Processing stage where error occurred
   * @param {Error|string} error - Error information
   */
  logProcessingError(uploadId, stage, error) {
    if (!this.shouldLog('error')) { return; }

    const logData = {
      stage,
      errorMessage: error instanceof Error ? error.message : error,
      errorStack: error instanceof Error ? error.stack : undefined,
      errorType: error instanceof Error ? error.constructor.name : 'String',
      memoryUsage: this.getMemoryUsage()
    };

    if (this.enableConsoleGrouping) {
      console.group(`❌ Processing Error - ${stage}`);
    }

    this.error('processing_error', 'Document processing failed', uploadId, logData);

    if (this.enableConsoleGrouping) {
      console.groupEnd();
    }
  }

  /**
   * Log PDF processing details
   * @param {string} uploadId - Upload UUID
   * @param {Object} pdfData - PDF processing data
   */
  logPdfProcessing(uploadId, pdfData) {
    if (!this.shouldLog('info')) { return; }

    const logData = {
      pages: pdfData.pages || 0,
      textLength: pdfData.text ? pdfData.text.length : 0,
      method: pdfData.method || 'pdf.js',
      textPreview: this.createDataPreview(pdfData.text, 200),
      memoryUsage: this.getMemoryUsage()
    };

    if (this.enableConsoleGrouping) {
      console.group(`📄 PDF Processing - ${pdfData.pages || 0} pages`);
    }

    this.info('pdf_processing', 'PDF processing completed', uploadId, logData);

    if (this.enableConsoleGrouping) {
      console.groupEnd();
    }
  }

  /**
   * Log OCR processing details
   * @param {string} uploadId - Upload UUID
   * @param {Object} ocrData - OCR processing data
   */
  logOcrProcessing(uploadId, ocrData) {
    if (!this.shouldLog('info')) { return; }

    const logData = {
      textLength: ocrData.text ? ocrData.text.length : 0,
      confidence: ocrData.confidence || 0,
      language: ocrData.language || 'unknown',
      method: ocrData.method || 'tesseract',
      textPreview: this.createDataPreview(ocrData.text, 200),
      memoryUsage: this.getMemoryUsage()
    };

    if (this.enableConsoleGrouping) {
      console.group(`🔍 OCR Processing - ${(ocrData.confidence || 0).toFixed(1)}% confidence`);
    }

    this.info('ocr_processing', 'OCR processing completed', uploadId, logData);

    if (this.enableConsoleGrouping) {
      console.groupEnd();
    }
  }

  /**
   * Format duration in human-readable format
   * @param {number} durationMs - Duration in milliseconds
   * @returns {string} - Formatted duration
   */
  formatDuration(durationMs) {
    if (durationMs < 1000) {
      return `${Math.round(durationMs)}ms`;
    } else if (durationMs < 60000) {
      return `${(durationMs / 1000).toFixed(1)}s`;
    }
    const minutes = Math.floor(durationMs / 60000);
    const seconds = Math.floor((durationMs % 60000) / 1000);
    return `${minutes}m ${seconds}s`;
  }
}

// Create singleton instance
export const processingLogger = new ProcessingLogger();

// Export for testing
export default ProcessingLogger;
