/**
 * Security Scanner Utility
 * Provides advanced security scanning capabilities for uploaded files
 *
 * Features:
 * - Malicious file detection
 * - Content-based security analysis
 * - File signature verification
 * - Suspicious pattern detection
 * - Risk assessment scoring
 */

export class SecurityScanner {
  constructor(options = {}) {
    this.config = {
      enableDeepScanning: options.enableDeepScanning !== false,
      maxScanSize: options.maxScanSize || 1024 * 1024, // 1MB for content scanning
      riskThreshold: options.riskThreshold || 0.7, // Risk score threshold
      ...options
    };

    // Known malicious file signatures (magic bytes)
    this.maliciousSignatures = [
      { signature: [0x4D, 0x5A], description: 'Windows executable (PE)', risk: 0.9 },
      { signature: [0x7F, 0x45, 0x4C, 0x46], description: 'Linux executable (ELF)', risk: 0.9 },
      { signature: [0xCA, 0xFE, 0xBA, 0xBE], description: 'Java class file', risk: 0.8 },
      { signature: [0x50, 0x4B, 0x03, 0x04], description: 'ZIP archive (potential)', risk: 0.3 },
      { signature: [0x52, 0x61, 0x72, 0x21], description: 'RAR archive', risk: 0.3 }
    ];

    // Suspicious file extensions
    this.suspiciousExtensions = [
      { ext: '.exe', risk: 0.9, description: 'Windows executable' },
      { ext: '.scr', risk: 0.9, description: 'Screen saver executable' },
      { ext: '.bat', risk: 0.8, description: 'Batch script' },
      { ext: '.cmd', risk: 0.8, description: 'Command script' },
      { ext: '.com', risk: 0.8, description: 'DOS executable' },
      { ext: '.pif', risk: 0.8, description: 'Program information file' },
      { ext: '.vbs', risk: 0.8, description: 'VBScript file' },
      { ext: '.js', risk: 0.6, description: 'JavaScript file' },
      { ext: '.jar', risk: 0.7, description: 'Java archive' },
      { ext: '.app', risk: 0.7, description: 'macOS application' },
      { ext: '.deb', risk: 0.6, description: 'Debian package' },
      { ext: '.rpm', risk: 0.6, description: 'RPM package' }
    ];

    // Malicious filename patterns
    this.maliciousPatterns = [
      { pattern: /autorun\.inf/i, risk: 0.9, description: 'Autorun configuration' },
      { pattern: /desktop\.ini/i, risk: 0.7, description: 'Desktop configuration' },
      { pattern: /thumbs\.db/i, risk: 0.5, description: 'Windows thumbnail cache' },
      { pattern: /\.htaccess/i, risk: 0.6, description: 'Apache configuration' },
      { pattern: /web\.config/i, risk: 0.6, description: 'IIS configuration' },
      { pattern: /\.env/i, risk: 0.8, description: 'Environment variables file' },
      { pattern: /config\.(php|asp|jsp)/i, risk: 0.7, description: 'Configuration script' },
      { pattern: /shell\.(php|asp|jsp)/i, risk: 0.9, description: 'Web shell script' },
      { pattern: /backdoor/i, risk: 0.9, description: 'Potential backdoor' },
      { pattern: /malware/i, risk: 0.9, description: 'Potential malware' }
    ];

    // Suspicious content patterns (for text-based analysis)
    this.suspiciousContentPatterns = [
      { pattern: /<script[^>]*>/i, risk: 0.8, description: 'JavaScript code injection' },
      { pattern: /eval\s*\(/i, risk: 0.7, description: 'Dynamic code execution' },
      { pattern: /document\.write/i, risk: 0.6, description: 'DOM manipulation' },
      { pattern: /window\.location/i, risk: 0.5, description: 'URL redirection' },
      { pattern: /base64_decode/i, risk: 0.7, description: 'Base64 decoding (potential obfuscation)' },
      { pattern: /exec\s*\(/i, risk: 0.8, description: 'Command execution' },
      { pattern: /system\s*\(/i, risk: 0.8, description: 'System command execution' },
      { pattern: /shell_exec/i, risk: 0.8, description: 'Shell command execution' }
    ];
  }

  /**
   * Perform comprehensive security scan on a file
   * @param {File} file - File to scan
   * @param {Object} options - Scanning options
   * @returns {Promise<Object>} Security scan result
   */
  async scanFile(file, options = {}) {
    const startTime = performance.now();
    const result = {
      isSecure: true,
      riskScore: 0,
      threats: [],
      warnings: [],
      details: {},
      performance: {}
    };

    try {
      // Basic file information analysis
      const basicScan = this.performBasicScan(file);
      this.mergeResults(result, basicScan);

      // File signature analysis
      const signatureScan = await this.performSignatureScan(file);
      this.mergeResults(result, signatureScan);

      // Content analysis (if enabled and file is small enough)
      if (this.config.enableDeepScanning && file.size <= this.config.maxScanSize) {
        const contentScan = await this.performContentScan(file);
        this.mergeResults(result, contentScan);
      }

      // Calculate final risk assessment
      result.riskScore = Math.min(result.riskScore, 1.0);
      result.isSecure = result.riskScore < this.config.riskThreshold;

      // Performance metrics
      const endTime = performance.now();
      result.performance = {
        scanTime: Math.round(endTime - startTime),
        fileSize: file.size,
        deepScanEnabled: this.config.enableDeepScanning && file.size <= this.config.maxScanSize
      };

      return result;

    } catch (error) {
      result.threats.push({
        type: 'scan_error',
        description: `Security scan failed: ${error.message}`,
        risk: 0.5
      });
      result.riskScore = Math.max(result.riskScore, 0.5);
      result.isSecure = false;

      result.performance = {
        scanTime: Math.round(performance.now() - startTime),
        error: error.message
      };

      return result;
    }
  }

  /**
   * Perform basic security scan based on file metadata
   * @param {File} file - File to scan
   * @returns {Object} Basic scan result
   */
  performBasicScan(file) {
    const result = {
      riskScore: 0,
      threats: [],
      warnings: [],
      details: { basicScan: true }
    };

    // Check file extension
    const extension = this.getFileExtension(file.name);
    const suspiciousExt = this.suspiciousExtensions.find(ext =>
      ext.ext.toLowerCase() === extension.toLowerCase()
    );

    if (suspiciousExt) {
      result.threats.push({
        type: 'suspicious_extension',
        description: `Suspicious file extension: ${suspiciousExt.description}`,
        risk: suspiciousExt.risk,
        extension: extension
      });
      result.riskScore = Math.max(result.riskScore, suspiciousExt.risk);
    }

    // Check filename patterns
    for (const pattern of this.maliciousPatterns) {
      if (pattern.pattern.test(file.name)) {
        result.threats.push({
          type: 'malicious_filename',
          description: `Malicious filename pattern: ${pattern.description}`,
          risk: pattern.risk,
          pattern: pattern.pattern.source
        });
        result.riskScore = Math.max(result.riskScore, pattern.risk);
      }
    }

    // Check for suspicious file size patterns
    if (file.size === 0) {
      result.warnings.push({
        type: 'empty_file',
        description: 'Empty file detected',
        risk: 0.3
      });
      result.riskScore = Math.max(result.riskScore, 0.3);
    } else if (file.size > 100 * 1024 * 1024) { // 100MB
      result.warnings.push({
        type: 'large_file',
        description: 'Unusually large file size',
        risk: 0.4
      });
      result.riskScore = Math.max(result.riskScore, 0.4);
    }

    // Check for path traversal in filename
    if (file.name.includes('..') || file.name.includes('/') || file.name.includes('\\')) {
      result.threats.push({
        type: 'path_traversal',
        description: 'Filename contains path traversal characters',
        risk: 0.8
      });
      result.riskScore = Math.max(result.riskScore, 0.8);
    }

    return result;
  }

  /**
   * Perform file signature analysis
   * @param {File} file - File to scan
   * @returns {Promise<Object>} Signature scan result
   */
  async performSignatureScan(file) {
    const result = {
      riskScore: 0,
      threats: [],
      warnings: [],
      details: { signatureScan: true }
    };

    try {
      // Read first 512 bytes for signature analysis
      const headerSize = Math.min(file.size, 512);
      const buffer = await this.readFileChunk(file, 0, headerSize);
      const bytes = new Uint8Array(buffer);

      // Check against known malicious signatures
      for (const sig of this.maliciousSignatures) {
        if (this.matchesSignature(bytes, sig.signature)) {
          result.threats.push({
            type: 'malicious_signature',
            description: `Malicious file signature detected: ${sig.description}`,
            risk: sig.risk,
            signature: sig.signature
          });
          result.riskScore = Math.max(result.riskScore, sig.risk);
        }
      }

      // Check for embedded executables (nested PE headers)
      const peSignature = [0x4D, 0x5A]; // MZ header
      let peCount = 0;
      for (let i = 0; i < bytes.length - 1; i++) {
        if (bytes[i] === peSignature[0] && bytes[i + 1] === peSignature[1]) {
          peCount++;
        }
      }

      if (peCount > 1) {
        result.warnings.push({
          type: 'multiple_executables',
          description: `Multiple executable signatures found (${peCount})`,
          risk: 0.6
        });
        result.riskScore = Math.max(result.riskScore, 0.6);
      }

      result.details.signatureAnalysis = {
        bytesScanned: bytes.length,
        signaturesChecked: this.maliciousSignatures.length,
        executableCount: peCount
      };

    } catch (error) {
      result.warnings.push({
        type: 'signature_scan_error',
        description: `Signature scan failed: ${error.message}`,
        risk: 0.3
      });
      result.riskScore = Math.max(result.riskScore, 0.3);
    }

    return result;
  }

  /**
   * Perform content-based security analysis
   * @param {File} file - File to scan
   * @returns {Promise<Object>} Content scan result
   */
  async performContentScan(file) {
    const result = {
      riskScore: 0,
      threats: [],
      warnings: [],
      details: { contentScan: true }
    };

    try {
      // Only scan text-based files for content analysis
      if (!this.isTextBasedFile(file)) {
        result.details.contentAnalysis = { skipped: 'Non-text file' };
        return result;
      }

      // Read file content as text
      const content = await this.readFileAsText(file);

      // Check for suspicious content patterns
      let patternMatches = 0;
      for (const pattern of this.suspiciousContentPatterns) {
        const matches = content.match(pattern.pattern);
        if (matches) {
          patternMatches++;
          result.threats.push({
            type: 'suspicious_content',
            description: `Suspicious content pattern: ${pattern.description}`,
            risk: pattern.risk,
            matches: matches.length,
            pattern: pattern.pattern.source
          });
          result.riskScore = Math.max(result.riskScore, pattern.risk);
        }
      }

      // Check for obfuscated content
      const obfuscationScore = this.calculateObfuscationScore(content);
      if (obfuscationScore > 0.7) {
        result.warnings.push({
          type: 'obfuscated_content',
          description: 'Content appears to be obfuscated',
          risk: 0.6,
          score: obfuscationScore
        });
        result.riskScore = Math.max(result.riskScore, 0.6);
      }

      result.details.contentAnalysis = {
        contentLength: content.length,
        patternMatches,
        obfuscationScore,
        patternsChecked: this.suspiciousContentPatterns.length
      };

    } catch (error) {
      result.warnings.push({
        type: 'content_scan_error',
        description: `Content scan failed: ${error.message}`,
        risk: 0.2
      });
      result.riskScore = Math.max(result.riskScore, 0.2);
    }

    return result;
  }

  /**
   * Check if file signature matches known pattern
   * @param {Uint8Array} bytes - File bytes
   * @param {number[]} signature - Signature to match
   * @returns {boolean} True if matches
   */
  matchesSignature(bytes, signature) {
    if (bytes.length < signature.length) { return false; }

    for (let i = 0; i < signature.length; i++) {
      if (bytes[i] !== signature[i]) { return false; }
    }

    return true;
  }

  /**
   * Calculate obfuscation score for content
   * @param {string} content - File content
   * @returns {number} Obfuscation score (0-1)
   */
  calculateObfuscationScore(content) {
    if (!content || content.length === 0) { return 0; }

    let score = 0;
    const length = content.length;

    // Check for high entropy (random-looking content)
    const entropy = this.calculateEntropy(content);
    if (entropy > 4.5) { score += 0.3; }

    // Check for unusual character distribution
    const alphaNumeric = (content.match(/[a-zA-Z0-9]/g) || []).length;
    const alphaNumericRatio = alphaNumeric / length;
    if (alphaNumericRatio < 0.5) { score += 0.2; }

    // Check for base64-like patterns
    const base64Pattern = /[A-Za-z0-9+/]{20,}={0,2}/g;
    const base64Matches = content.match(base64Pattern);
    if (base64Matches && base64Matches.length > 3) { score += 0.3; }

    // Check for hex-encoded content
    const hexPattern = /[0-9a-fA-F]{40,}/g;
    const hexMatches = content.match(hexPattern);
    if (hexMatches && hexMatches.length > 2) { score += 0.2; }

    return Math.min(score, 1.0);
  }

  /**
   * Calculate Shannon entropy of content
   * @param {string} content - Content to analyze
   * @returns {number} Entropy value
   */
  calculateEntropy(content) {
    const frequency = {};
    const length = content.length;

    // Count character frequencies
    for (const char of content) {
      frequency[char] = (frequency[char] || 0) + 1;
    }

    // Calculate entropy
    let entropy = 0;
    for (const count of Object.values(frequency)) {
      const probability = count / length;
      entropy -= probability * Math.log2(probability);
    }

    return entropy;
  }

  /**
   * Check if file is text-based for content analysis
   * @param {File} file - File to check
   * @returns {boolean} True if text-based
   */
  isTextBasedFile(file) {
    const textMimeTypes = [
      'text/',
      'application/javascript',
      'application/json',
      'application/xml',
      'application/xhtml+xml'
    ];

    return textMimeTypes.some(type => file.type.startsWith(type));
  }

  /**
   * Merge scan results
   * @param {Object} target - Target result object
   * @param {Object} source - Source result object
   */
  mergeResults(target, source) {
    target.riskScore = Math.max(target.riskScore, source.riskScore);
    target.threats.push(...source.threats);
    target.warnings.push(...source.warnings);
    Object.assign(target.details, source.details);
  }

  /**
   * Read file chunk as ArrayBuffer
   * @param {File} file - File to read
   * @param {number} start - Start position
   * @param {number} end - End position
   * @returns {Promise<ArrayBuffer>} File chunk
   */
  readFileChunk(file, start, end) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = () => reject(reader.error);
      reader.readAsArrayBuffer(file.slice(start, end));
    });
  }

  /**
   * Read file as text
   * @param {File} file - File to read
   * @returns {Promise<string>} File content as text
   */
  readFileAsText(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = () => reject(reader.error);
      reader.readAsText(file);
    });
  }

  /**
   * Get file extension
   * @param {string} fileName - File name
   * @returns {string} File extension
   */
  getFileExtension(fileName) {
    const lastDot = fileName.lastIndexOf('.');
    return lastDot > 0 ? fileName.substring(lastDot) : '';
  }
}

// Export singleton instance
export const securityScanner = new SecurityScanner();
export default securityScanner;
