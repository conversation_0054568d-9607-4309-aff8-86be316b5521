/**
 * ocrUtils - OCR text processing utilities
 * Implements text cleaning, language detection, and confidence calculation
 *
 * Features:
 * - OCR text cleaning and normalization
 * - Language detection for Polish/English
 * - Confidence score calculation
 * - Polish-specific text corrections
 * - Invoice keyword detection
 * - Text validation and formatting
 */

export const ocrUtils = {
  /**
   * Clean OCR text by removing artifacts and normalizing
   * @param {string} text - Raw OCR text
   * @returns {string} - Cleaned text
   */
  cleanOCRText(text) {
    if (!text || typeof text !== 'string') {
      return '';
    }

    let cleaned = text;

    // Remove common OCR artifacts
    cleaned = cleaned.replace(/[|]/g, 'I'); // Vertical bars often misread as I
    cleaned = cleaned.replace(/[0]/g, 'O'); // Zero often misread as O in text
    cleaned = cleaned.replace(/[5]/g, 'S'); // 5 often misread as S
    cleaned = cleaned.replace(/[1]/g, 'l'); // 1 often misread as l

    // Fix common character substitutions
    cleaned = cleaned.replace(/rn/g, 'm'); // rn often misread as m
    cleaned = cleaned.replace(/vv/g, 'w'); // vv often misread as w
    cleaned = cleaned.replace(/\\/g, '/'); // Backslash to forward slash

    // Remove excessive whitespace
    cleaned = cleaned.replace(/\s+/g, ' ');
    cleaned = cleaned.replace(/\n\s*\n/g, '\n');

    // Remove leading/trailing whitespace
    cleaned = cleaned.trim();

    // Fix punctuation spacing
    cleaned = cleaned.replace(/\s+([,.;:!?])/g, '$1');
    cleaned = cleaned.replace(/([,.;:!?])\s*/g, '$1 ');

    return cleaned;
  },

  /**
   * Detect language from text content
   * @param {string} text - Text to analyze
   * @param {string} expectedLanguage - Expected language hint
   * @returns {string} - Detected language code
   */
  detectLanguage(text, expectedLanguage = 'pol+eng') {
    if (!text || text.length < 10) {
      return expectedLanguage;
    }

    const polishChars = /[ąćęłńóśźżĄĆĘŁŃÓŚŹŻ]/g;
    const polishWords = /\b(i|w|na|do|od|za|przez|dla|bez|pod|nad|przy|między|przed|po|o|z|ze|że|jak|gdy|jeśli|ale|lub|oraz|tak|nie|jest|są|będzie|było|ma|mają|może|można|należy|powinien|faktura|rachunek|kwota|suma|podatek|vat|netto|brutto|płatność|termin|data|numer|firma|adres|telefon|email)\b/gi;

    const englishWords = /\b(the|and|or|but|in|on|at|to|for|of|with|by|from|about|into|through|during|before|after|above|below|up|down|out|off|over|under|again|further|then|once|here|there|when|where|why|how|all|any|both|each|few|more|most|other|some|such|no|nor|not|only|own|same|so|than|too|very|can|will|just|should|now|invoice|bill|amount|total|tax|payment|due|date|number|company|address|phone|email)\b/gi;

    const polishCharCount = (text.match(polishChars) || []).length;
    const polishWordCount = (text.match(polishWords) || []).length;
    const englishWordCount = (text.match(englishWords) || []).length;

    const totalWords = text.split(/\s+/).length;
    const polishScore = (polishCharCount * 2 + polishWordCount) / totalWords;
    const englishScore = englishWordCount / totalWords;

    if (polishScore > englishScore && polishScore > 0.1) {
      return 'pol';
    } else if (englishScore > polishScore && englishScore > 0.1) {
      return 'eng';
    }
    return 'pol+eng'; // Mixed or uncertain

  },

  /**
   * Calculate confidence score for OCR text
   * @param {string} text - OCR text to analyze
   * @returns {number} - Confidence score (0-100)
   */
  calculateConfidence(text) {
    if (!text || text.length === 0) {
      return 0;
    }

    let score = 100;
    const length = text.length;

    // Penalize for suspicious patterns
    const suspiciousChars = /[^\w\s\-.,;:!?()[\]{}'"€$£¥₹₽ąćęłńóśźżĄĆĘŁŃÓŚŹŻ]/g;
    const suspiciousCount = (text.match(suspiciousChars) || []).length;
    score -= (suspiciousCount / length) * 30;

    // Penalize for excessive repetition
    const repeatedChars = /(.)\1{3,}/g;
    const repetitionCount = (text.match(repeatedChars) || []).length;
    score -= repetitionCount * 10;

    // Penalize for too many single characters
    const singleChars = text.split(/\s+/).filter(word => word.length === 1).length;
    const totalWords = text.split(/\s+/).length;
    if (totalWords > 0) {
      score -= (singleChars / totalWords) * 20;
    }

    // Bonus for recognizable words
    const recognizableWords = /\b(faktura|invoice|kwota|amount|suma|total|podatek|tax|vat|netto|brutto|płatność|payment|data|date|numer|number|firma|company)\b/gi;
    const recognizableCount = (text.match(recognizableWords) || []).length;
    score += recognizableCount * 5;

    // Bonus for proper sentence structure
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 5);
    score += Math.min(sentences.length * 2, 10);

    return Math.max(0, Math.min(100, Math.round(score)));
  },

  /**
   * Apply Polish-specific text corrections
   * @param {string} text - Text to correct
   * @returns {string} - Corrected text
   */
  applyPolishCorrections(text) {
    if (!text) { return ''; }

    let corrected = text;

    // Common Polish OCR corrections
    const polishCorrections = {
      'ą': ['a', 'q', 'ą'],
      'ć': ['c', 'ć'],
      'ę': ['e', 'ę'],
      'ł': ['l', 'ł'],
      'ń': ['n', 'ń'],
      'ó': ['o', 'ó'],
      'ś': ['s', 'ś'],
      'ź': ['z', 'ź'],
      'ż': ['z', 'ż'],
      'Ą': ['A', 'Ą'],
      'Ć': ['C', 'Ć'],
      'Ę': ['E', 'Ę'],
      'Ł': ['L', 'Ł'],
      'Ń': ['N', 'Ń'],
      'Ó': ['O', 'Ó'],
      'Ś': ['S', 'Ś'],
      'Ź': ['Z', 'Ź'],
      'Ż': ['Z', 'Ż']
    };

    // Apply corrections in context
    Object.entries(polishCorrections).forEach(([correct, variants]) => {
      variants.forEach(variant => {
        if (variant !== correct) {
          // Context-aware replacement for common Polish words
          const contextPatterns = [
            { pattern: new RegExp(`\\b${variant}`, 'g'), replacement: correct },
            { pattern: new RegExp(`${variant}\\b`, 'g'), replacement: correct }
          ];

          contextPatterns.forEach(({ pattern, replacement }) => {
            corrected = corrected.replace(pattern, replacement);
          });
        }
      });
    });

    // Fix common Polish word patterns
    const wordCorrections = {
      'faktur': 'faktura',
      'kwot': 'kwota',
      'sum': 'suma',
      'podatk': 'podatek',
      'płatnośc': 'płatność',
      'dat': 'data',
      'numer': 'numer',
      'firm': 'firma'
    };

    Object.entries(wordCorrections).forEach(([wrong, correct]) => {
      const pattern = new RegExp(`\\b${wrong}\\b`, 'gi');
      corrected = corrected.replace(pattern, correct);
    });

    return corrected;
  },

  /**
   * Check if text contains invoice-related keywords
   * @param {string} text - Text to check
   * @returns {boolean} - True if invoice keywords found
   */
  containsInvoiceKeywords(text) {
    if (!text) { return false; }

    const invoiceKeywords = [
      // Polish
      'faktura', 'rachunek', 'kwota', 'suma', 'podatek', 'vat', 'netto', 'brutto',
      'płatność', 'termin', 'data', 'numer', 'firma', 'sprzedawca', 'nabywca',
      // English
      'invoice', 'bill', 'amount', 'total', 'tax', 'payment', 'due', 'date',
      'number', 'company', 'seller', 'buyer', 'vendor', 'customer'
    ];

    const lowerText = text.toLowerCase();
    return invoiceKeywords.some(keyword => lowerText.includes(keyword));
  },

  /**
   * Extract structured data patterns from OCR text
   * @param {string} text - OCR text to analyze
   * @returns {Object} - Extracted patterns
   */
  extractDataPatterns(text) {
    if (!text) { return {}; }

    const patterns = {
      // Dates
      dates: text.match(/\d{1,2}[.\-/]\d{1,2}[.\-/]\d{2,4}/g) || [],

      // Numbers with currency
      amounts: text.match(/\d+[.,]\d{2}\s*(zł|PLN|EUR|USD|€|\$)/gi) || [],

      // VAT numbers
      vatNumbers: text.match(/\b(PL)?\d{10}\b/g) || [],

      // Invoice numbers
      invoiceNumbers: text.match(/\b(FV|INV|F)[\-\s]*\d+[\-/]\d+/gi) || [],

      // Phone numbers
      phoneNumbers: text.match(/\+?[\d\s\-()]{9,15}/g) || [],

      // Email addresses
      emails: text.match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g) || []
    };

    return patterns;
  },

  /**
   * Validate OCR result quality
   * @param {string} text - OCR text to validate
   * @returns {Object} - Validation result
   */
  validateOCRResult(text) {
    const result = {
      isValid: false,
      confidence: 0,
      issues: [],
      suggestions: []
    };

    if (!text || text.trim().length === 0) {
      result.issues.push('No text extracted');
      result.suggestions.push('Try preprocessing the image or adjusting OCR settings');
      return result;
    }

    const confidence = this.calculateConfidence(text);
    result.confidence = confidence;

    if (confidence < 30) {
      result.issues.push('Very low confidence score');
      result.suggestions.push('Image quality may be poor - try enhancing contrast or resolution');
    } else if (confidence < 60) {
      result.issues.push('Low confidence score');
      result.suggestions.push('Consider image preprocessing or manual review');
    }

    if (text.length < 20) {
      result.issues.push('Very short text extracted');
      result.suggestions.push('Ensure the image contains readable text');
    }

    const hasInvoiceKeywords = this.containsInvoiceKeywords(text);
    if (!hasInvoiceKeywords) {
      result.issues.push('No invoice-related keywords detected');
      result.suggestions.push('Verify this is an invoice document');
    }

    result.isValid = result.issues.length === 0 && confidence >= 60;

    return result;
  }
};

// Export to window object for non-module scripts
if (typeof window !== 'undefined') {
  window.ocrUtils = ocrUtils;
}

// Local test function
function testOCRUtils() {
  console.log('=== ocrUtils Local Test ===');

  try {
    // Test 1: Text cleaning
    console.log('Test 1: Text cleaning');
    const dirtyText = 'Faktur|a  nr   123/2024\n\n\nKwota:  100,00   zł';
    const cleaned = ocrUtils.cleanOCRText(dirtyText);
    console.log('✓ Original:', dirtyText.replace(/\n/g, '\\n'));
    console.log('✓ Cleaned:', cleaned);

    // Test 2: Language detection
    console.log('\nTest 2: Language detection');
    const polishText = 'Faktura VAT nr 123/2024 kwota 100,00 zł';
    const englishText = 'Invoice number 123/2024 amount 100.00 USD';
    console.log('✓ Polish detected:', ocrUtils.detectLanguage(polishText));
    console.log('✓ English detected:', ocrUtils.detectLanguage(englishText));

    // Test 3: Confidence calculation
    console.log('\nTest 3: Confidence calculation');
    const goodText = 'Faktura VAT nr 123/2024 data 15.01.2024 kwota 100,00 zł';
    const badText = '|||###@@@***^^^';
    console.log('✓ Good text confidence:', ocrUtils.calculateConfidence(goodText));
    console.log('✓ Bad text confidence:', ocrUtils.calculateConfidence(badText));

    // Test 4: Invoice keyword detection
    console.log('\nTest 4: Invoice keyword detection');
    console.log('✓ Invoice keywords found:', ocrUtils.containsInvoiceKeywords(goodText));
    console.log('✓ No keywords found:', ocrUtils.containsInvoiceKeywords('Random text without keywords'));

    console.log('\n✅ All ocrUtils tests passed!');
    return true;

  } catch (error) {
    console.error('❌ ocrUtils test failed:', error);
    return false;
  }
}

// Run test if in browser environment
if (typeof window !== 'undefined' && window.location) {
  // Uncomment to run test
  // testOCRUtils();
}
