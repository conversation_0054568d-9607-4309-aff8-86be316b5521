/**
 * Security Checks for File Validation
 * Advanced security validation for uploaded files in MVAT Chrome Extension
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-01-27
 */

/**
 * Security validation result object
 * @typedef {Object} SecurityResult
 * @property {boolean} isValid - Whether the file passed security checks
 * @property {string[]} errors - Array of security error messages
 * @property {string[]} warnings - Array of security warning messages
 * @property {Object} details - Detailed security check results
 */

/**
 * Perform comprehensive security checks on a file
 * @param {File} file - The file to check
 * @param {Object} config - Configuration object
 * @returns {Promise<SecurityResult>} Security validation result
 */
export async function performSecurityChecks(file, config) {
  const result = {
    isValid: true,
    errors: [],
    warnings: [],
    details: {}
  };

  try {
    // File signature validation
    const signatureCheck = await validateFileSignature(file);
    result.details.signature = signatureCheck;
    if (!signatureCheck.isValid) {
      result.errors.push(...signatureCheck.errors);
      result.isValid = false;
    }

    // Malicious content detection
    const malwareCheck = await detectMaliciousContent(file);
    result.details.malware = malwareCheck;
    if (!malwareCheck.isValid) {
      result.errors.push(...malwareCheck.errors);
      result.isValid = false;
    }
    if (malwareCheck.warnings.length > 0) {
      result.warnings.push(...malwareCheck.warnings);
    }

    // File structure validation
    const structureCheck = await validateFileStructure(file);
    result.details.structure = structureCheck;
    if (!structureCheck.isValid) {
      result.errors.push(...structureCheck.errors);
      result.isValid = false;
    }

    // Metadata security check
    const metadataCheck = await checkFileMetadata(file);
    result.details.metadata = metadataCheck;
    if (metadataCheck.warnings.length > 0) {
      result.warnings.push(...metadataCheck.warnings);
    }

  } catch (error) {
    result.errors.push(`Security check failed: ${error.message}`);
    result.isValid = false;
  }

  return result;
}

/**
 * Validate file signature (magic bytes)
 * @param {File} file - File to validate
 * @returns {Promise<Object>} Signature validation result
 */
export async function validateFileSignature(file) {
  const result = { isValid: true, errors: [], signature: null };

  try {
    // Read first 16 bytes for signature detection
    const arrayBuffer = await file.slice(0, 16).arrayBuffer();
    const bytes = new Uint8Array(arrayBuffer);
    const signature = Array.from(bytes).map(b => b.toString(16).padStart(2, '0')).join('');

    result.signature = signature;

    // Get expected signatures for file extension
    const extension = getFileExtension(file.name).toLowerCase();
    const expectedSignatures = FILE_SIGNATURES[extension];

    if (expectedSignatures) {
      const isValidSignature = expectedSignatures.some(expectedSig =>
        signature.startsWith(expectedSig.toLowerCase())
      );

      if (!isValidSignature) {
        result.errors.push(
          `File signature does not match expected format for ${extension} files`
        );
        result.isValid = false;
      }
    }

  } catch (error) {
    result.errors.push(`Failed to read file signature: ${error.message}`);
    result.isValid = false;
  }

  return result;
}

/**
 * Detect potentially malicious content
 * @param {File} file - File to check
 * @returns {Promise<Object>} Malware detection result
 */
export async function detectMaliciousContent(file) {
  const result = { isValid: true, errors: [], warnings: [], threats: [] };

  try {
    // Check for suspicious file patterns
    const suspiciousPatterns = await checkSuspiciousPatterns(file);
    if (suspiciousPatterns.length > 0) {
      result.warnings.push('File contains potentially suspicious patterns');
      result.threats.push(...suspiciousPatterns);
    }

    // Check for embedded executables
    const embeddedExecutables = await checkEmbeddedExecutables(file);
    if (embeddedExecutables.length > 0) {
      result.errors.push('File contains embedded executable content');
      result.isValid = false;
      result.threats.push(...embeddedExecutables);
    }

    // Check for script injection attempts
    const scriptInjection = await checkScriptInjection(file);
    if (scriptInjection.detected) {
      result.errors.push('File contains potential script injection attempts');
      result.isValid = false;
      result.threats.push(scriptInjection);
    }

    // Check file size anomalies
    const sizeAnomaly = checkFileSizeAnomaly(file);
    if (sizeAnomaly.suspicious) {
      result.warnings.push(sizeAnomaly.message);
    }

  } catch (error) {
    result.warnings.push(`Malware detection incomplete: ${error.message}`);
  }

  return result;
}

/**
 * Validate file structure integrity
 * @param {File} file - File to validate
 * @returns {Promise<Object>} Structure validation result
 */
export async function validateFileStructure(file) {
  const result = { isValid: true, errors: [], details: {} };

  try {
    const extension = getFileExtension(file.name).toLowerCase();

    switch (extension) {
      case 'pdf':
        const pdfStructure = await validatePDFStructure(file);
        result.details.pdf = pdfStructure;
        if (!pdfStructure.isValid) {
          result.errors.push(...pdfStructure.errors);
          result.isValid = false;
        }
        break;

      case 'jpg':
      case 'jpeg':
        const jpegStructure = await validateJPEGStructure(file);
        result.details.jpeg = jpegStructure;
        if (!jpegStructure.isValid) {
          result.errors.push(...jpegStructure.errors);
          result.isValid = false;
        }
        break;

      case 'png':
        const pngStructure = await validatePNGStructure(file);
        result.details.png = pngStructure;
        if (!pngStructure.isValid) {
          result.errors.push(...pngStructure.errors);
          result.isValid = false;
        }
        break;

      default:
        // Basic structure validation for other types
        result.details.basic = { checked: true, valid: true };
    }

  } catch (error) {
    result.errors.push(`Structure validation failed: ${error.message}`);
    result.isValid = false;
  }

  return result;
}

/**
 * Check file metadata for security issues
 * @param {File} file - File to check
 * @returns {Promise<Object>} Metadata check result
 */
export async function checkFileMetadata(file) {
  const result = { warnings: [], details: {} };

  try {
    // Check for suspicious file names
    if (isSuspiciousFileName(file.name)) {
      result.warnings.push('File name appears suspicious');
    }

    // Check last modified date
    const modifiedDate = new Date(file.lastModified);
    const now = new Date();
    const daysDiff = (now - modifiedDate) / (1000 * 60 * 60 * 24);

    if (daysDiff < 0) {
      result.warnings.push('File has future modification date');
    }

    if (daysDiff > 365 * 10) {
      result.warnings.push('File is very old (>10 years)');
    }

    result.details = {
      fileName: file.name,
      fileSize: file.size,
      lastModified: file.lastModified,
      type: file.type
    };

  } catch (error) {
    result.warnings.push(`Metadata check incomplete: ${error.message}`);
  }

  return result;
}

// Helper functions

/**
 * Check for suspicious patterns in file content
 * @private
 */
async function checkSuspiciousPatterns(file) {
  const threats = [];

  try {
    // Read a sample of the file content
    const sampleSize = Math.min(file.size, 1024 * 10); // 10KB sample
    const arrayBuffer = await file.slice(0, sampleSize).arrayBuffer();
    const content = new TextDecoder('utf-8', { fatal: false }).decode(arrayBuffer);

    // Check for suspicious patterns
    const suspiciousPatterns = [
      /eval\s*\(/gi,
      /document\.write/gi,
      /innerHTML\s*=/gi,
      /javascript:/gi,
      /vbscript:/gi,
      /<script/gi,
      /onload\s*=/gi,
      /onerror\s*=/gi
    ];

    for (const pattern of suspiciousPatterns) {
      if (pattern.test(content)) {
        threats.push({
          type: 'suspicious_pattern',
          pattern: pattern.source,
          description: 'Potentially malicious script pattern detected'
        });
      }
    }

  } catch (error) {
    // Ignore decoding errors for binary files
  }

  return threats;
}

/**
 * Check for embedded executables
 * @private
 */
async function checkEmbeddedExecutables(file) {
  const threats = [];

  try {
    const arrayBuffer = await file.slice(0, 1024).arrayBuffer();
    const bytes = new Uint8Array(arrayBuffer);

    // Check for PE header (Windows executables)
    const peSignature = [0x4D, 0x5A]; // "MZ"
    if (bytes.length >= 2 && bytes[0] === peSignature[0] && bytes[1] === peSignature[1]) {
      threats.push({
        type: 'embedded_executable',
        format: 'PE',
        description: 'Windows executable header detected'
      });
    }

    // Check for ELF header (Linux executables)
    const elfSignature = [0x7F, 0x45, 0x4C, 0x46]; // "\x7FELF"
    if (bytes.length >= 4 &&
        bytes[0] === elfSignature[0] && bytes[1] === elfSignature[1] &&
        bytes[2] === elfSignature[2] && bytes[3] === elfSignature[3]) {
      threats.push({
        type: 'embedded_executable',
        format: 'ELF',
        description: 'Linux executable header detected'
      });
    }

  } catch (error) {
    // Ignore read errors
  }

  return threats;
}

/**
 * Check for script injection attempts
 * @private
 */
async function checkScriptInjection(file) {
  const result = { detected: false, details: [] };

  try {
    const extension = getFileExtension(file.name).toLowerCase();

    // Only check text-based or document files
    if (['pdf', 'txt', 'html', 'xml'].includes(extension)) {
      const sampleSize = Math.min(file.size, 1024 * 5); // 5KB sample
      const arrayBuffer = await file.slice(0, sampleSize).arrayBuffer();
      const content = new TextDecoder('utf-8', { fatal: false }).decode(arrayBuffer);

      // Check for script injection patterns
      const injectionPatterns = [
        /<script[^>]*>.*?<\/script>/gis,
        /javascript\s*:/gi,
        /data\s*:\s*text\/html/gi,
        /data\s*:\s*application\/javascript/gi
      ];

      for (const pattern of injectionPatterns) {
        const matches = content.match(pattern);
        if (matches) {
          result.detected = true;
          result.details.push({
            pattern: pattern.source,
            matches: matches.length,
            sample: matches[0].substring(0, 100)
          });
        }
      }
    }

  } catch (error) {
    // Ignore decoding errors
  }

  return result;
}

/**
 * Check for file size anomalies
 * @private
 */
function checkFileSizeAnomaly(file) {
  const extension = getFileExtension(file.name).toLowerCase();
  const size = file.size;

  // Expected size ranges for different file types (in bytes)
  const expectedSizes = {
    'pdf': { min: 1000, max: 50 * 1024 * 1024 }, // 1KB - 50MB
    'jpg': { min: 1000, max: 20 * 1024 * 1024 }, // 1KB - 20MB
    'jpeg': { min: 1000, max: 20 * 1024 * 1024 },
    'png': { min: 500, max: 10 * 1024 * 1024 }, // 500B - 10MB
    'gif': { min: 500, max: 5 * 1024 * 1024 } // 500B - 5MB
  };

  const expected = expectedSizes[extension];
  if (expected) {
    if (size < expected.min) {
      return {
        suspicious: true,
        message: `File size unusually small for ${extension} format`
      };
    }
    if (size > expected.max) {
      return {
        suspicious: true,
        message: `File size unusually large for ${extension} format`
      };
    }
  }

  return { suspicious: false };
}

/**
 * Validate PDF structure
 * @private
 */
async function validatePDFStructure(file) {
  const result = { isValid: true, errors: [] };

  try {
    const arrayBuffer = await file.slice(0, 1024).arrayBuffer();
    const content = new TextDecoder('utf-8', { fatal: false }).decode(arrayBuffer);

    // Check for PDF header
    if (!content.startsWith('%PDF-')) {
      result.errors.push('Invalid PDF header');
      result.isValid = false;
    }

    // Check for PDF version
    const versionMatch = content.match(/%PDF-(\d+\.\d+)/);
    if (versionMatch) {
      const version = parseFloat(versionMatch[1]);
      if (version < 1.0 || version > 2.0) {
        result.errors.push(`Unsupported PDF version: ${version}`);
        result.isValid = false;
      }
    }

  } catch (error) {
    result.errors.push(`PDF structure validation failed: ${error.message}`);
    result.isValid = false;
  }

  return result;
}

/**
 * Validate JPEG structure
 * @private
 */
async function validateJPEGStructure(file) {
  const result = { isValid: true, errors: [] };

  try {
    const arrayBuffer = await file.slice(0, 4).arrayBuffer();
    const bytes = new Uint8Array(arrayBuffer);

    // Check JPEG header (FF D8)
    if (bytes.length < 2 || bytes[0] !== 0xFF || bytes[1] !== 0xD8) {
      result.errors.push('Invalid JPEG header');
      result.isValid = false;
    }

  } catch (error) {
    result.errors.push(`JPEG structure validation failed: ${error.message}`);
    result.isValid = false;
  }

  return result;
}

/**
 * Validate PNG structure
 * @private
 */
async function validatePNGStructure(file) {
  const result = { isValid: true, errors: [] };

  try {
    const arrayBuffer = await file.slice(0, 8).arrayBuffer();
    const bytes = new Uint8Array(arrayBuffer);

    // Check PNG signature
    const pngSignature = [0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A];
    if (bytes.length < 8) {
      result.errors.push('File too small to be a valid PNG');
      result.isValid = false;
    } else {
      for (let i = 0; i < 8; i++) {
        if (bytes[i] !== pngSignature[i]) {
          result.errors.push('Invalid PNG signature');
          result.isValid = false;
          break;
        }
      }
    }

  } catch (error) {
    result.errors.push(`PNG structure validation failed: ${error.message}`);
    result.isValid = false;
  }

  return result;
}

/**
 * Check if filename is suspicious
 * @private
 */
function isSuspiciousFileName(fileName) {
  const suspiciousPatterns = [
    /\.(exe|bat|cmd|scr|pif|com)$/i,
    /\.(vbs|js|jar|app)$/i,
    /^(con|prn|aux|nul|com[1-9]|lpt[1-9])$/i,
    /\.\./,
    /[<>:"|?*]/
  ];

  return suspiciousPatterns.some(pattern => pattern.test(fileName));
}

/**
 * Get file extension from filename
 * @private
 */
function getFileExtension(fileName) {
  const lastDot = fileName.lastIndexOf('.');
  return lastDot > 0 ? fileName.substring(lastDot + 1) : '';
}

/**
 * File signature database
 * Maps file extensions to their expected magic bytes
 */
const FILE_SIGNATURES = {
  'pdf': ['25504446'], // %PDF
  'jpg': ['FFD8FF'],
  'jpeg': ['FFD8FF'],
  'png': ['89504E47'],
  'gif': ['474946383761', '474946383961'], // GIF87a, GIF89a
  'bmp': ['424D'],
  'tiff': ['49492A00', '4D4D002A'],
  'zip': ['504B0304', '504B0506', '504B0708'],
  'doc': ['D0CF11E0A1B11AE1'],
  'docx': ['504B0304'], // DOCX is ZIP-based
  'xls': ['D0CF11E0A1B11AE1'],
  'xlsx': ['504B0304'], // XLSX is ZIP-based
  'ppt': ['D0CF11E0A1B11AE1'],
  'pptx': ['504B0304'] // PPTX is ZIP-based
};
