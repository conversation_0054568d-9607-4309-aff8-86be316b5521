/**
 * Summary Calculations - Utility functions for summary card calculations
 * Provides period comparison, trend analysis, and metric calculations
 *
 * Epic: EPIC-003 - Data Display & Visualization
 * Story: STORY-3.2 - Grouping & Aggregation
 * Task: TASK-3.2.2 - Summary Views
 * Assignment: ASSIGNMENT-030 - Summary Cards and Visual Indicators
 */

/**
 * Calculate trend between two periods
 * @param {number} current - Current period value
 * @param {number} previous - Previous period value
 * @returns {Object|null} - Trend object with direction and percentage
 */
export function calculateTrend(current, previous) {
  if (!previous || previous === 0 || isNaN(current) || isNaN(previous)) {
    return null;
  }

  const change = current - previous;
  const percentage = (change / Math.abs(previous)) * 100;

  // Only show trends with significant change (>1%)
  if (Math.abs(percentage) < 1) {
    return null;
  }

  return {
    direction: change > 0 ? 'up' : 'down',
    percentage: Math.abs(percentage),
    change: change,
    comparison: 'vs previous period'
  };
}

/**
 * Calculate period comparison for grouped data
 * @param {Object} currentGroup - Current period group data
 * @param {Object} previousGroup - Previous period group data
 * @param {string} metric - Metric to compare (e.g., 'total_gross')
 * @param {string} currency - Currency for comparison
 * @returns {Object|null} - Comparison result
 */
export function calculatePeriodComparison(currentGroup, previousGroup, metric = 'total_gross', currency = 'PLN') {
  if (!currentGroup || !previousGroup) {
    return null;
  }

  const currentValue = currentGroup.aggregations?.[currency]?.[metric]?.sum || 0;
  const previousValue = previousGroup.aggregations?.[currency]?.[metric]?.sum || 0;

  const trend = calculateTrend(currentValue, previousValue);

  if (!trend) {
    return null;
  }

  return {
    ...trend,
    currentValue,
    previousValue,
    metric,
    currency,
    comparison: getComparisonLabel(currentGroup.metadata, previousGroup.metadata)
  };
}

/**
 * Generate comparison label based on group metadata
 * @param {Object} currentMeta - Current group metadata
 * @param {Object} previousMeta - Previous group metadata
 * @returns {string} - Human-readable comparison label
 */
export function getComparisonLabel(currentMeta, previousMeta) {
  if (!currentMeta || !previousMeta) {
    return 'vs previous period';
  }

  const currentDisplay = currentMeta.displayName || currentMeta.groupKey;
  const previousDisplay = previousMeta.displayName || previousMeta.groupKey;

  // Handle different grouping types
  if (currentMeta.groupBy === 'year') {
    return `vs ${previousDisplay}`;
  } else if (currentMeta.groupBy === 'quarter') {
    return `vs ${previousDisplay}`;
  } else if (currentMeta.groupBy === 'month') {
    return `vs ${previousDisplay}`;
  }

  return `vs ${previousDisplay}`;
}

/**
 * Calculate summary metrics for a group
 * @param {Object} group - Group data with aggregations
 * @param {string} currency - Primary currency
 * @returns {Object} - Summary metrics
 */
export function calculateGroupSummary(group, currency = 'PLN') {
  if (!group || !group.aggregations) {
    return {
      totalGross: 0,
      totalNet: 0,
      totalVat: 0,
      count: 0,
      averageGross: 0,
      averageNet: 0
    };
  }

  const currencyData = group.aggregations[currency] || {};

  return {
    totalGross: currencyData.total_gross?.sum || 0,
    totalNet: currencyData.total_net?.sum || 0,
    totalVat: currencyData.total_vat?.sum || 0,
    count: currencyData.count || 0,
    averageGross: currencyData.total_gross?.average || 0,
    averageNet: currencyData.total_net?.average || 0,
    minGross: currencyData.total_gross?.min || 0,
    maxGross: currencyData.total_gross?.max || 0,
    medianGross: currencyData.total_gross?.median || 0
  };
}

/**
 * Generate trend cards data for a group
 * @param {Object} group - Group data
 * @param {Object} previousGroup - Previous period group for comparison
 * @param {string} currency - Primary currency
 * @returns {Array} - Array of trend card configurations
 */
export function generateTrendCards(group, previousGroup = null, currency = 'PLN') {
  const summary = calculateGroupSummary(group, currency);
  const previousSummary = previousGroup ? calculateGroupSummary(previousGroup, currency) : null;

  const cards = [
    {
      id: 'total-gross',
      title: 'Total Gross',
      value: summary.totalGross,
      currency: currency,
      subtitle: `${summary.count} invoices`,
      icon: '💰',
      trend: null,
      percentage: 0,
      comparison: ''
    },
    {
      id: 'total-net',
      title: 'Total Net',
      value: summary.totalNet,
      currency: currency,
      subtitle: 'Before VAT',
      icon: '📊',
      trend: null,
      percentage: 0,
      comparison: ''
    },
    {
      id: 'total-vat',
      title: 'Total VAT',
      value: summary.totalVat,
      currency: currency,
      subtitle: 'Tax amount',
      icon: '🏛️',
      trend: null,
      percentage: 0,
      comparison: ''
    },
    {
      id: 'average-gross',
      title: 'Average Invoice',
      value: summary.averageGross,
      currency: currency,
      subtitle: 'Per invoice',
      icon: '📈',
      trend: null,
      percentage: 0,
      comparison: ''
    }
  ];

  // Add trend data if previous period is available
  if (previousSummary) {
    const comparisonLabel = getComparisonLabel(group.metadata, previousGroup.metadata);

    // Total Gross trend
    const grossTrend = calculateTrend(summary.totalGross, previousSummary.totalGross);
    if (grossTrend) {
      cards[0].trend = grossTrend.direction;
      cards[0].percentage = grossTrend.percentage;
      cards[0].comparison = comparisonLabel;
    }

    // Total Net trend
    const netTrend = calculateTrend(summary.totalNet, previousSummary.totalNet);
    if (netTrend) {
      cards[1].trend = netTrend.direction;
      cards[1].percentage = netTrend.percentage;
      cards[1].comparison = comparisonLabel;
    }

    // Total VAT trend
    const vatTrend = calculateTrend(summary.totalVat, previousSummary.totalVat);
    if (vatTrend) {
      cards[2].trend = vatTrend.direction;
      cards[2].percentage = vatTrend.percentage;
      cards[2].comparison = comparisonLabel;
    }

    // Average trend
    const avgTrend = calculateTrend(summary.averageGross, previousSummary.averageGross);
    if (avgTrend) {
      cards[3].trend = avgTrend.direction;
      cards[3].percentage = avgTrend.percentage;
      cards[3].comparison = comparisonLabel;
    }
  }

  return cards;
}

/**
 * Find previous period group for comparison
 * @param {Array} groups - Array of all groups
 * @param {Object} currentGroup - Current group
 * @returns {Object|null} - Previous period group or null
 */
export function findPreviousPeriodGroup(groups, currentGroup) {
  if (!groups || !currentGroup || !currentGroup.metadata) {
    return null;
  }

  const { groupBy, groupKey } = currentGroup.metadata;

  // Sort groups by their sort order
  const sortedGroups = [...groups].sort((a, b) => {
    const aOrder = a.metadata?.sortOrder || 0;
    const bOrder = b.metadata?.sortOrder || 0;
    return bOrder - aOrder; // Descending order (newest first)
  });

  // Find current group index
  const currentIndex = sortedGroups.findIndex(g => g.key === groupKey);

  if (currentIndex === -1 || currentIndex === sortedGroups.length - 1) {
    return null;
  }

  // Return the next group in the sorted array (previous period)
  return sortedGroups[currentIndex + 1];
}

/**
 * Format currency value for display
 * @param {number} amount - Amount to format
 * @param {string} currency - Currency code
 * @returns {string} - Formatted currency string
 */
export function formatCurrency(amount, currency = 'PLN') {
  if (amount === null || amount === undefined || isNaN(amount)) {
    return '0.00';
  }

  return new Intl.NumberFormat('pl-PL', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2
  }).format(amount);
}

/**
 * Format number for display
 * @param {number} num - Number to format
 * @returns {string} - Formatted number string
 */
export function formatNumber(num) {
  if (num === null || num === undefined || isNaN(num)) {
    return '0';
  }

  return new Intl.NumberFormat('pl-PL').format(num);
}

/**
 * Format percentage for display
 * @param {number} percentage - Percentage to format
 * @param {number} decimals - Number of decimal places
 * @returns {string} - Formatted percentage string
 */
export function formatPercentage(percentage, decimals = 1) {
  if (percentage === null || percentage === undefined || isNaN(percentage)) {
    return '0.0%';
  }

  return `${percentage.toFixed(decimals)}%`;
}
