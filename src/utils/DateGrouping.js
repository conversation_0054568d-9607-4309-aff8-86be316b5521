/**
 * DateGrouping - Date-based grouping utilities for invoice data
 * Provides efficient date grouping with fiscal year support
 * Handles various date formats and edge cases
 *
 * Epic: EPIC-003 - Data Display & Visualization
 * Story: STORY-3.2 - Grouping & Aggregation
 * Task: TASK-3.2.1 - Grouping Logic
 */

import { format, startOfYear, endOfYear, startOfQuarter, endOfQuarter,
  startOfMonth, endOfMonth, startOfWeek, endOfWeek,
  startOfDay, endOfDay, getQuarter, getWeek, addMonths } from 'date-fns';

export class DateGrouping {
  constructor() {
    this.fiscalYearStart = 1; // January (1-12)
    this.weekStartsOn = 1; // Monday (0=Sunday, 1=Monday)
    this.dateFormat = 'yyyy-MM-dd';
    this.quarterFormat = 'yyyy-[Q]Q';
    this.monthFormat = 'yyyy-MM';
    this.weekFormat = 'yyyy-[W]ww';
  }

  /**
   * Configure date grouping settings
   * @param {Object} config - Configuration options
   */
  configure(config = {}) {
    if (config.fiscalYearStart) {
      this.fiscalYearStart = config.fiscalYearStart;
    }
    if (config.weekStartsOn !== undefined) {
      this.weekStartsOn = config.weekStartsOn;
    }
    if (config.dateFormat) {
      this.dateFormat = config.dateFormat;
    }
  }

  /**
   * Get year key for grouping
   * @param {Date} date - Date to group
   * @param {number} fiscalYearStart - Fiscal year start month (1-12)
   * @returns {string} - Year key
   */
  getYearKey(date, fiscalYearStart = this.fiscalYearStart) {
    if (fiscalYearStart === 1) {
      // Calendar year
      return date.getFullYear().toString();
    }
    // Fiscal year
    const year = date.getFullYear();
    const month = date.getMonth() + 1; // 0-based to 1-based

    if (month >= fiscalYearStart) {
      return `FY${year + 1}`;
    }
    return `FY${year}`;


  }

  /**
   * Get quarter key for grouping
   * @param {Date} date - Date to group
   * @param {number} fiscalYearStart - Fiscal year start month (1-12)
   * @returns {string} - Quarter key
   */
  getQuarterKey(date, fiscalYearStart = this.fiscalYearStart) {
    if (fiscalYearStart === 1) {
      // Calendar quarters
      const year = date.getFullYear();
      const quarter = getQuarter(date);
      return `${year}-Q${quarter}`;
    }
    // Fiscal quarters
    const fiscalYear = this.getFiscalYear(date, fiscalYearStart);
    const fiscalQuarter = this.getFiscalQuarter(date, fiscalYearStart);
    return `FY${fiscalYear}-Q${fiscalQuarter}`;

  }

  /**
   * Get month key for grouping
   * @param {Date} date - Date to group
   * @returns {string} - Month key
   */
  getMonthKey(date) {
    return format(date, this.monthFormat);
  }

  /**
   * Get week key for grouping
   * @param {Date} date - Date to group
   * @returns {string} - Week key
   */
  getWeekKey(date) {
    const year = date.getFullYear();
    const week = getWeek(date, { weekStartsOn: this.weekStartsOn });
    return `${year}-W${week.toString().padStart(2, '0')}`;
  }

  /**
   * Get day key for grouping
   * @param {Date} date - Date to group
   * @returns {string} - Day key
   */
  getDayKey(date) {
    return format(date, this.dateFormat);
  }

  /**
   * Get custom key for grouping based on date ranges
   * @param {Date} date - Date to group
   * @param {Array} customRanges - Array of custom date ranges
   * @returns {string} - Custom key
   */
  getCustomKey(date, customRanges) {
    if (!customRanges || !Array.isArray(customRanges)) {
      throw new Error('Custom ranges must be provided as an array');
    }

    for (const range of customRanges) {
      const startDate = new Date(range.start);
      const endDate = new Date(range.end);

      if (date >= startDate && date <= endDate) {
        return range.key || `${format(startDate, 'yyyy-MM-dd')}_to_${format(endDate, 'yyyy-MM-dd')}`;
      }
    }

    // If no range matches, use 'other' or the date itself
    return 'other';
  }

  /**
   * Get fiscal year for a date
   * @param {Date} date - Date to check
   * @param {number} fiscalYearStart - Fiscal year start month (1-12)
   * @returns {number} - Fiscal year
   */
  getFiscalYear(date, fiscalYearStart) {
    const year = date.getFullYear();
    const month = date.getMonth() + 1; // 0-based to 1-based

    if (month >= fiscalYearStart) {
      return year + 1;
    }
    return year;

  }

  /**
   * Get fiscal quarter for a date
   * @param {Date} date - Date to check
   * @param {number} fiscalYearStart - Fiscal year start month (1-12)
   * @returns {number} - Fiscal quarter (1-4)
   */
  getFiscalQuarter(date, fiscalYearStart) {
    const month = date.getMonth() + 1; // 0-based to 1-based

    // Calculate months from fiscal year start
    let monthsFromFiscalStart = month - fiscalYearStart;
    if (monthsFromFiscalStart < 0) {
      monthsFromFiscalStart += 12;
    }

    return Math.floor(monthsFromFiscalStart / 3) + 1;
  }

  /**
   * Get date range for a group key
   * @param {string} groupKey - Group key
   * @param {string} groupBy - Grouping type
   * @param {Object} options - Grouping options
   * @returns {Object} - Date range object
   */
  getDateRangeForGroup(groupKey, groupBy, options = {}) {
    const fiscalYearStart = options.fiscalYearStart || this.fiscalYearStart;

    switch (groupBy) {
      case 'year':
        return this.getYearDateRange(groupKey, fiscalYearStart);
      case 'quarter':
        return this.getQuarterDateRange(groupKey, fiscalYearStart);
      case 'month':
        return this.getMonthDateRange(groupKey);
      case 'week':
        return this.getWeekDateRange(groupKey);
      case 'day':
        return this.getDayDateRange(groupKey);
      case 'custom':
        return this.getCustomDateRange(groupKey, options.customDateRanges);
      default:
        throw new Error(`Unsupported groupBy type: ${groupBy}`);
    }
  }

  /**
   * Get date range for year group
   * @param {string} yearKey - Year key
   * @param {number} fiscalYearStart - Fiscal year start month
   * @returns {Object} - Date range
   */
  getYearDateRange(yearKey, fiscalYearStart) {
    if (yearKey.startsWith('FY')) {
      // Fiscal year
      const fiscalYear = parseInt(yearKey.substring(2));
      const startDate = new Date(fiscalYear - 1, fiscalYearStart - 1, 1);
      const endDate = new Date(fiscalYear, fiscalYearStart - 1, 0); // Last day of previous month
      return { start: startDate, end: endDate };
    }
    // Calendar year
    const year = parseInt(yearKey);
    return {
      start: startOfYear(new Date(year, 0, 1)),
      end: endOfYear(new Date(year, 0, 1))
    };

  }

  /**
   * Get date range for quarter group
   * @param {string} quarterKey - Quarter key
   * @param {number} fiscalYearStart - Fiscal year start month
   * @returns {Object} - Date range
   */
  getQuarterDateRange(quarterKey, fiscalYearStart) {
    if (quarterKey.startsWith('FY')) {
      // Fiscal quarter
      const [fyPart, qPart] = quarterKey.split('-Q');
      const fiscalYear = parseInt(fyPart.substring(2));
      const quarter = parseInt(qPart);

      const fiscalYearStartDate = new Date(fiscalYear - 1, fiscalYearStart - 1, 1);
      const quarterStartMonth = (quarter - 1) * 3;
      const startDate = addMonths(fiscalYearStartDate, quarterStartMonth);
      const endDate = addMonths(startDate, 3);
      endDate.setDate(0); // Last day of previous month

      return { start: startDate, end: endDate };
    }
    // Calendar quarter
    const [year, qPart] = quarterKey.split('-Q');
    const quarter = parseInt(qPart);
    const quarterStartDate = new Date(parseInt(year), (quarter - 1) * 3, 1);

    return {
      start: startOfQuarter(quarterStartDate),
      end: endOfQuarter(quarterStartDate)
    };

  }

  /**
   * Get date range for month group
   * @param {string} monthKey - Month key (YYYY-MM)
   * @returns {Object} - Date range
   */
  getMonthDateRange(monthKey) {
    const [year, month] = monthKey.split('-');
    const date = new Date(parseInt(year), parseInt(month) - 1, 1);

    return {
      start: startOfMonth(date),
      end: endOfMonth(date)
    };
  }

  /**
   * Get date range for week group
   * @param {string} weekKey - Week key (YYYY-WNN)
   * @returns {Object} - Date range
   */
  getWeekDateRange(weekKey) {
    const [year, weekPart] = weekKey.split('-W');
    const week = parseInt(weekPart);

    // Create a date in the specified year and week
    const jan1 = new Date(parseInt(year), 0, 1);
    const daysToAdd = (week - 1) * 7;
    const weekDate = new Date(jan1.getTime() + daysToAdd * 24 * 60 * 60 * 1000);

    return {
      start: startOfWeek(weekDate, { weekStartsOn: this.weekStartsOn }),
      end: endOfWeek(weekDate, { weekStartsOn: this.weekStartsOn })
    };
  }

  /**
   * Get date range for day group
   * @param {string} dayKey - Day key (YYYY-MM-DD)
   * @returns {Object} - Date range
   */
  getDayDateRange(dayKey) {
    const date = new Date(dayKey);

    return {
      start: startOfDay(date),
      end: endOfDay(date)
    };
  }

  /**
   * Get date range for custom group
   * @param {string} customKey - Custom key
   * @param {Array} customRanges - Custom date ranges
   * @returns {Object} - Date range
   */
  getCustomDateRange(customKey, customRanges) {
    if (!customRanges || !Array.isArray(customRanges)) {
      return { start: null, end: null };
    }

    const range = customRanges.find(r =>
      (r.key && r.key === customKey) ||
      customKey.includes(format(new Date(r.start), 'yyyy-MM-dd'))
    );

    if (range) {
      return {
        start: new Date(range.start),
        end: new Date(range.end)
      };
    }

    return { start: null, end: null };
  }

  /**
   * Validate date grouping configuration
   * @param {Object} config - Configuration to validate
   * @returns {Object} - Validation result
   */
  validateConfig(config) {
    const errors = [];

    if (config.fiscalYearStart && (config.fiscalYearStart < 1 || config.fiscalYearStart > 12)) {
      errors.push('Fiscal year start must be between 1 and 12');
    }

    if (config.weekStartsOn !== undefined && (config.weekStartsOn < 0 || config.weekStartsOn > 6)) {
      errors.push('Week starts on must be between 0 (Sunday) and 6 (Saturday)');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}
