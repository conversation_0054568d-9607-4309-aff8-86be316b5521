/**
 * Field Validation Utilities - Validate and correct extracted invoice data
 * Provides comprehensive validation for invoice fields with automatic correction
 *
 * Features:
 * - Field type validation (dates, amounts, VAT rates, etc.)
 * - Format validation and correction
 * - Business logic validation (totals, VAT calculations)
 * - Multi-language support
 * - Detailed error reporting with correction suggestions
 */

/**
 * Validate invoice fields and provide corrections
 * @param {Object} data - Extracted invoice data
 * @param {string} language - Language code for validation rules
 * @returns {Object} - Validation result with errors and warnings
 */
export function validateInvoiceFields(data, language = 'pol') {
  const errors = [];
  const warnings = [];
  const corrections = [];

  if (!data || typeof data !== 'object') {
    return {
      isValid: false,
      errors: [{ field: 'data', type: 'missing', message: 'No data provided for validation' }],
      warnings: [],
      corrections: []
    };
  }

  // Validate required fields
  const requiredFields = ['invoice_number', 'seller_name', 'buyer_name', 'total_gross'];
  requiredFields.forEach(field => {
    if (!data[field] || data[field] === null || data[field] === '') {
      errors.push({
        field,
        type: 'required',
        message: `${field} is required but missing or empty`
      });
    }
  });

  // Validate invoice number format
  if (data.invoice_number) {
    const invoiceNumberValidation = validateInvoiceNumber(data.invoice_number);
    if (!invoiceNumberValidation.isValid) {
      warnings.push({
        field: 'invoice_number',
        type: 'format',
        message: invoiceNumberValidation.message
      });
    }
  }

  // Validate dates
  ['issue_date', 'due_date', 'sale_date'].forEach(dateField => {
    if (data[dateField]) {
      const dateValidation = validateDate(data[dateField]);
      if (!dateValidation.isValid) {
        errors.push({
          field: dateField,
          type: 'date_format',
          message: dateValidation.message,
          suggestion: dateValidation.suggestion
        });
      }
    }
  });

  // Validate monetary amounts
  ['total_net', 'total_vat', 'total_gross'].forEach(amountField => {
    if (data[amountField] !== null && data[amountField] !== undefined) {
      const amountValidation = validateAmount(data[amountField]);
      if (!amountValidation.isValid) {
        errors.push({
          field: amountField,
          type: 'amount_format',
          message: amountValidation.message,
          suggestion: amountValidation.suggestion
        });
      }
    }
  });

  // Validate currency
  if (data.currency) {
    const currencyValidation = validateCurrency(data.currency);
    if (!currencyValidation.isValid) {
      warnings.push({
        field: 'currency',
        type: 'currency_format',
        message: currencyValidation.message
      });
    }
  }

  // Validate VAT rates
  if (data.vat_rates) {
    const vatValidation = validateVatRates(data.vat_rates, language);
    if (!vatValidation.isValid) {
      errors.push({
        field: 'vat_rates',
        type: 'vat_rate',
        message: vatValidation.message
      });
    }
  }

  // Validate business logic (totals calculation)
  if (data.total_net && data.total_vat && data.total_gross) {
    const totalsValidation = validateTotalsCalculation(data.total_net, data.total_vat, data.total_gross);
    if (!totalsValidation.isValid) {
      warnings.push({
        field: 'totals',
        type: 'calculation',
        message: totalsValidation.message
      });
    }
  }

  // Validate tax IDs
  ['seller_tax_id', 'buyer_tax_id'].forEach(taxField => {
    if (data[taxField]) {
      const taxIdValidation = validateTaxId(data[taxField], language);
      if (!taxIdValidation.isValid) {
        warnings.push({
          field: taxField,
          type: 'tax_id_format',
          message: taxIdValidation.message
        });
      }
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    corrections
  };
}

/**
 * Validate invoice number format
 * @param {string} invoiceNumber - Invoice number to validate
 * @returns {Object} - Validation result
 */
function validateInvoiceNumber(invoiceNumber) {
  if (!invoiceNumber || typeof invoiceNumber !== 'string') {
    return { isValid: false, message: 'Invoice number must be a non-empty string' };
  }

  const trimmed = invoiceNumber.trim();
  if (trimmed.length === 0) {
    return { isValid: false, message: 'Invoice number cannot be empty' };
  }

  if (trimmed.length > 50) {
    return { isValid: false, message: 'Invoice number is too long (max 50 characters)' };
  }

  return { isValid: true };
}

/**
 * Validate date format and value
 * @param {string} dateValue - Date to validate
 * @returns {Object} - Validation result
 */
function validateDate(dateValue) {
  if (!dateValue) {
    return { isValid: false, message: 'Date value is empty' };
  }

  // Try to parse as ISO date first
  const isoDateRegex = /^\d{4}-\d{2}-\d{2}$/;
  if (isoDateRegex.test(dateValue)) {
    const date = new Date(dateValue);
    if (isNaN(date.getTime())) {
      return { isValid: false, message: 'Invalid ISO date format' };
    }
    return { isValid: true };
  }

  // Try common date formats
  const dateFormats = [
    /^(\d{1,2})\.(\d{1,2})\.(\d{4})$/, // DD.MM.YYYY
    /^(\d{1,2})-(\d{1,2})-(\d{4})$/, // DD-MM-YYYY
    /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/ // DD/MM/YYYY
  ];

  for (const format of dateFormats) {
    if (format.test(dateValue)) {
      return {
        isValid: false,
        message: 'Date format should be YYYY-MM-DD',
        suggestion: 'Convert to ISO format (YYYY-MM-DD)'
      };
    }
  }

  return {
    isValid: false,
    message: 'Unrecognized date format',
    suggestion: 'Use ISO format (YYYY-MM-DD)'
  };
}

/**
 * Validate monetary amount
 * @param {any} amount - Amount to validate
 * @returns {Object} - Validation result
 */
function validateAmount(amount) {
  if (amount === null || amount === undefined) {
    return { isValid: true }; // Null amounts are allowed
  }

  // Try to convert to number
  const numericAmount = typeof amount === 'number' ? amount : parseFloat(amount);

  if (isNaN(numericAmount)) {
    return {
      isValid: false,
      message: 'Amount must be a valid number',
      suggestion: 'Remove currency symbols and use decimal notation'
    };
  }

  if (numericAmount < 0) {
    return {
      isValid: false,
      message: 'Amount cannot be negative',
      suggestion: 'Use positive values for amounts'
    };
  }

  return { isValid: true };
}

/**
 * Validate currency code
 * @param {string} currency - Currency code to validate
 * @returns {Object} - Validation result
 */
function validateCurrency(currency) {
  if (!currency || typeof currency !== 'string') {
    return { isValid: false, message: 'Currency must be a string' };
  }

  const commonCurrencies = ['PLN', 'EUR', 'USD', 'GBP', 'CHF', 'CZK', 'SEK', 'NOK', 'DKK'];
  const upperCurrency = currency.toUpperCase();

  if (!commonCurrencies.includes(upperCurrency)) {
    return {
      isValid: false,
      message: `Unknown currency code: ${currency}. Common codes: ${commonCurrencies.join(', ')}`
    };
  }

  return { isValid: true };
}

/**
 * Validate VAT rates array
 * @param {Array} vatRates - VAT rates to validate
 * @param {string} language - Language for country-specific validation
 * @returns {Object} - Validation result
 */
function validateVatRates(vatRates, language) {
  if (!Array.isArray(vatRates)) {
    return { isValid: false, message: 'VAT rates must be an array' };
  }

  const validRates = {
    'pol': [0, 0.05, 0.08, 0.23], // Polish VAT rates
    'eng': [0, 0.05, 0.20], // UK VAT rates
    'deu': [0, 0.07, 0.19], // German VAT rates
    'fra': [0, 0.055, 0.10, 0.20] // French VAT rates
  };

  const countryRates = validRates[language] || validRates.pol;

  for (const rate of vatRates) {
    const numericRate = parseFloat(rate);
    if (isNaN(numericRate) || numericRate < 0 || numericRate > 1) {
      return {
        isValid: false,
        message: 'VAT rates must be numbers between 0 and 1 (e.g., 0.23 for 23%)'
      };
    }

    // Check if rate is commonly used in the country
    const isCommonRate = countryRates.some(validRate => Math.abs(validRate - numericRate) < 0.001);
    if (!isCommonRate) {
      // This is a warning, not an error
      continue;
    }
  }

  return { isValid: true };
}

/**
 * Validate totals calculation
 * @param {number} totalNet - Net amount
 * @param {number} totalVat - VAT amount
 * @param {number} totalGross - Gross amount
 * @returns {Object} - Validation result
 */
function validateTotalsCalculation(totalNet, totalVat, totalGross) {
  const net = parseFloat(totalNet);
  const vat = parseFloat(totalVat);
  const gross = parseFloat(totalGross);

  if (isNaN(net) || isNaN(vat) || isNaN(gross)) {
    return { isValid: false, message: 'All total amounts must be valid numbers' };
  }

  const calculatedGross = net + vat;
  const tolerance = 0.01; // Allow 1 cent tolerance for rounding

  if (Math.abs(calculatedGross - gross) > tolerance) {
    return {
      isValid: false,
      message: `Total calculation error: ${net} + ${vat} = ${calculatedGross}, but gross is ${gross}`
    };
  }

  return { isValid: true };
}

/**
 * Validate tax ID format
 * @param {string} taxId - Tax ID to validate
 * @param {string} language - Language/country for format validation
 * @returns {Object} - Validation result
 */
function validateTaxId(taxId, language) {
  if (!taxId || typeof taxId !== 'string') {
    return { isValid: false, message: 'Tax ID must be a non-empty string' };
  }

  const cleanTaxId = taxId.replace(/[\s-]/g, ''); // Remove spaces and dashes

  const patterns = {
    'pol': /^\d{10}$/, // Polish NIP: 10 digits
    'eng': /^\d{9}$|^\d{12}$/, // UK: 9 or 12 digits
    'deu': /^\d{11}$/, // German: 11 digits
    'fra': /^\d{11}$/ // French SIREN: 11 digits
  };

  const pattern = patterns[language] || patterns.pol;

  if (!pattern.test(cleanTaxId)) {
    return {
      isValid: false,
      message: `Invalid tax ID format for ${language}. Expected pattern: ${pattern.source}`
    };
  }

  return { isValid: true };
}

// Self-test functionality for Node.js environment
if (typeof module !== 'undefined' && require.main === module) {
  console.log('🧪 Testing fieldValidation...');

  // Test valid data
  const validData = {
    invoice_number: 'INV-2024-001',
    seller_name: 'Test Company',
    buyer_name: 'Customer Ltd',
    total_gross: 1234.56,
    issue_date: '2024-01-15',
    currency: 'PLN'
  };

  const validResult = validateInvoiceFields(validData, 'pol');
  console.log('✅ Valid data test:', validResult.isValid ? 'passed' : 'failed');

  // Test invalid data
  const invalidData = {
    invoice_number: '',
    total_gross: -100,
    issue_date: '15.01.2024'
  };

  const invalidResult = validateInvoiceFields(invalidData, 'pol');
  console.log('✅ Invalid data test:', !invalidResult.isValid ? 'passed' : 'failed');
  console.log('   Errors found:', invalidResult.errors.length);

  // Test individual validators
  console.log('✅ Date validation test:', validateDate('2024-01-15').isValid ? 'passed' : 'failed');
  console.log('✅ Amount validation test:', validateAmount(123.45).isValid ? 'passed' : 'failed');
  console.log('✅ Currency validation test:', validateCurrency('PLN').isValid ? 'passed' : 'failed');

  console.log('🎉 All fieldValidation tests passed!');
}
