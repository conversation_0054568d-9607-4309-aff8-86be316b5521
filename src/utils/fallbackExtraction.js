/**
 * Fallback Extraction Utilities - Basic extraction when AI services fail
 * Provides regex-based and pattern-matching extraction as backup to AI processing
 *
 * Features:
 * - Regex-based field extraction for common invoice patterns
 * - Multi-language support for Polish, English, German, French
 * - Confidence scoring for fallback extractions
 * - Pattern matching for dates, amounts, VAT rates, tax IDs
 * - Graceful degradation from AI to basic extraction
 */

/**
 * Extract invoice data using fallback methods
 * @param {string} text - Invoice text content
 * @param {string} language - Language code (pol, eng, deu, fra)
 * @returns {Object} - Extracted invoice data
 */
export function extractInvoiceDataFallback(text, language = 'pol') {
  if (!text || typeof text !== 'string') {
    return {
      success: false,
      error: 'No text provided for fallback extraction',
      data: null,
      confidence: 0,
      method: 'fallback'
    };
  }

  try {
    console.log('🔄 Using fallback extraction methods...');

    const extractedData = {
      // Basic information
      invoice_number: extractInvoiceNumber(text, language),
      issue_date: extractDate(text, 'issue', language),
      due_date: extractDate(text, 'due', language),
      sale_date: extractDate(text, 'sale', language),

      // Company information
      seller_name: extractCompanyName(text, 'seller', language),
      buyer_name: extractCompanyName(text, 'buyer', language),
      seller_tax_id: extractTaxId(text, 'seller', language),
      buyer_tax_id: extractTaxId(text, 'buyer', language),

      // Financial information
      total_gross: extractAmount(text, 'total', language),
      total_net: extractAmount(text, 'net', language),
      total_vat: extractAmount(text, 'vat', language),
      currency: extractCurrency(text, language),

      // VAT information
      vat_rates: extractVatRates(text, language),

      // Additional information
      payment_method: extractPaymentMethod(text, language),
      description: extractDescription(text, language)
    };

    // Calculate confidence score
    const confidence = calculateFallbackConfidence(extractedData);

    return {
      success: true,
      data: {
        ...extractedData,
        extraction_method: 'fallback',
        language: language,
        confidence: confidence
      },
      confidence: confidence,
      method: 'fallback'
    };

  } catch (error) {
    console.error('❌ Fallback extraction failed:', error);
    return {
      success: false,
      error: error.message,
      data: null,
      confidence: 0,
      method: 'fallback'
    };
  }
}

/**
 * Extract invoice number using patterns
 * @param {string} text - Text content
 * @param {string} language - Language code
 * @returns {string|null} - Invoice number
 */
function extractInvoiceNumber(text, language) {
  const patterns = {
    pol: [
      /(?:faktura|nr|numer|invoice)[\s\w]*?:?\s*([A-Z0-9\/\-_]{3,20})/gi,
      /(?:FV|FA|INV)[\s\-\/]*([0-9]{1,10}[\-\/]?[0-9]{0,10})/gi
    ],
    eng: [
      /(?:invoice|inv|number|no)[\s\w]*?:?\s*([A-Z0-9\/\-_]{3,20})/gi,
      /(?:INV|INVOICE)[\s\-\/]*([0-9]{1,10}[\-\/]?[0-9]{0,10})/gi
    ],
    deu: [
      /(?:rechnung|rechnungsnummer|nr)[\s\w]*?:?\s*([A-Z0-9\/\-_]{3,20})/gi,
      /(?:RG|RE)[\s\-\/]*([0-9]{1,10}[\-\/]?[0-9]{0,10})/gi
    ],
    fra: [
      /(?:facture|numéro|no)[\s\w]*?:?\s*([A-Z0-9\/\-_]{3,20})/gi,
      /(?:FAC|FC)[\s\-\/]*([0-9]{1,10}[\-\/]?[0-9]{0,10})/gi
    ]
  };

  const langPatterns = patterns[language] || patterns.pol;

  for (const pattern of langPatterns) {
    const match = text.match(pattern);
    if (match && match[1]) {
      return match[1].trim();
    }
  }

  return null;
}

/**
 * Extract dates using patterns
 * @param {string} text - Text content
 * @param {string} type - Date type (issue, due, sale)
 * @param {string} language - Language code
 * @returns {string|null} - Date in ISO format
 */
function extractDate(text, type, language) {
  const dateKeywords = {
    pol: {
      issue: ['data wystawienia', 'wystawiono', 'data faktury'],
      due: ['termin płatności', 'płatne do', 'zapłaty'],
      sale: ['data sprzedaży', 'data dostawy', 'wykonania']
    },
    eng: {
      issue: ['issue date', 'invoice date', 'date'],
      due: ['due date', 'payment due', 'pay by'],
      sale: ['sale date', 'delivery date', 'service date']
    },
    deu: {
      issue: ['rechnungsdatum', 'datum', 'ausgestellt'],
      due: ['fälligkeitsdatum', 'zahlbar bis', 'zahlung'],
      sale: ['lieferdatum', 'leistungsdatum', 'verkauf']
    },
    fra: {
      issue: ['date facture', 'émise le', 'date'],
      due: ['échéance', 'à payer avant', 'paiement'],
      sale: ['date vente', 'livraison', 'prestation']
    }
  };

  const keywords = dateKeywords[language]?.[type] || dateKeywords.pol[type];
  const datePattern = /(\d{1,2})[.\-\/](\d{1,2})[.\-\/](\d{4})/g;

  // Look for dates near keywords
  for (const keyword of keywords) {
    const keywordIndex = text.toLowerCase().indexOf(keyword.toLowerCase());
    if (keywordIndex !== -1) {
      const searchArea = text.substring(keywordIndex, keywordIndex + 100);
      const match = searchArea.match(datePattern);
      if (match) {
        const [, day, month, year] = match[0].split(/[.\-\/]/);
        return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
      }
    }
  }

  // Fallback: find any date in text
  const allDates = text.match(datePattern);
  if (allDates && allDates.length > 0) {
    const [, day, month, year] = allDates[0].split(/[.\-\/]/);
    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  }

  return null;
}

/**
 * Extract company names
 * @param {string} text - Text content
 * @param {string} type - Company type (seller, buyer)
 * @param {string} language - Language code
 * @returns {string|null} - Company name
 */
function extractCompanyName(text, type, language) {
  const keywords = {
    pol: {
      seller: ['sprzedawca', 'wystawca', 'firma'],
      buyer: ['nabywca', 'odbiorca', 'klient']
    },
    eng: {
      seller: ['seller', 'vendor', 'from'],
      buyer: ['buyer', 'customer', 'to', 'bill to']
    },
    deu: {
      seller: ['verkäufer', 'lieferant', 'von'],
      buyer: ['käufer', 'kunde', 'an']
    },
    fra: {
      seller: ['vendeur', 'fournisseur', 'de'],
      buyer: ['acheteur', 'client', 'à']
    }
  };

  const typeKeywords = keywords[language]?.[type] || keywords.pol[type];

  for (const keyword of typeKeywords) {
    const keywordIndex = text.toLowerCase().indexOf(keyword.toLowerCase());
    if (keywordIndex !== -1) {
      // Look for company name in next 200 characters
      const searchArea = text.substring(keywordIndex, keywordIndex + 200);
      const lines = searchArea.split('\n');

      for (let i = 1; i < Math.min(lines.length, 4); i++) {
        const line = lines[i].trim();
        if (line.length > 3 && line.length < 100 && !line.match(/^\d+/)) {
          return line;
        }
      }
    }
  }

  return null;
}

/**
 * Extract tax ID numbers
 * @param {string} text - Text content
 * @param {string} type - Company type (seller, buyer)
 * @param {string} language - Language code
 * @returns {string|null} - Tax ID
 */
function extractTaxId(text, type, language) {
  const patterns = {
    pol: [/NIP[\s:]*(\d{3}[\s\-]?\d{3}[\s\-]?\d{2}[\s\-]?\d{2})/gi],
    eng: [/VAT[\s:]*([A-Z0-9]{9,12})/gi, /TAX[\s:]*([0-9]{9,12})/gi],
    deu: [/USt[\s\-]?IdNr[\s:]*([A-Z0-9]{11})/gi],
    fra: [/SIREN[\s:]*([0-9]{9})/gi, /TVA[\s:]*([A-Z0-9]{11})/gi]
  };

  const langPatterns = patterns[language] || patterns.pol;

  for (const pattern of langPatterns) {
    const matches = text.match(pattern);
    if (matches) {
      return matches[0].replace(/[^\d]/g, '');
    }
  }

  return null;
}

/**
 * Extract monetary amounts
 * @param {string} text - Text content
 * @param {string} type - Amount type (total, net, vat)
 * @param {string} language - Language code
 * @returns {number|null} - Amount value
 */
function extractAmount(text, type, language) {
  const keywords = {
    pol: {
      total: ['razem', 'suma', 'łącznie', 'do zapłaty'],
      net: ['netto', 'wartość netto'],
      vat: ['vat', 'podatek', 'kwota vat']
    },
    eng: {
      total: ['total', 'amount due', 'grand total'],
      net: ['net', 'subtotal', 'net amount'],
      vat: ['vat', 'tax', 'vat amount']
    },
    deu: {
      total: ['gesamt', 'summe', 'zu zahlen'],
      net: ['netto', 'nettobetrag'],
      vat: ['mwst', 'steuer', 'umsatzsteuer']
    },
    fra: {
      total: ['total', 'montant', 'à payer'],
      net: ['net', 'hors taxe'],
      vat: ['tva', 'taxe']
    }
  };

  const typeKeywords = keywords[language]?.[type] || keywords.pol[type];
  const amountPattern = /(\d{1,10}[,.]?\d{0,2})/g;

  for (const keyword of typeKeywords) {
    const keywordIndex = text.toLowerCase().indexOf(keyword.toLowerCase());
    if (keywordIndex !== -1) {
      const searchArea = text.substring(keywordIndex, keywordIndex + 100);
      const matches = searchArea.match(amountPattern);
      if (matches) {
        const amount = parseFloat(matches[0].replace(',', '.'));
        if (!isNaN(amount)) {
          return amount;
        }
      }
    }
  }

  return null;
}

/**
 * Extract currency code
 * @param {string} text - Text content
 * @param {string} language - Language code
 * @returns {string|null} - Currency code
 */
function extractCurrency(text, language) {
  const currencyPatterns = [
    /\b(PLN|EUR|USD|GBP|CHF|CZK|SEK|NOK|DKK)\b/gi,
    /\b(zł|złoty|złotych|euro|dolar|funt)\b/gi
  ];

  for (const pattern of currencyPatterns) {
    const match = text.match(pattern);
    if (match) {
      const currency = match[0].toUpperCase();
      // Convert Polish currency names to codes
      if (currency.includes('ZŁ') || currency.includes('ZŁOTY')) { return 'PLN'; }
      if (currency.includes('EURO')) { return 'EUR'; }
      if (currency.includes('DOLAR')) { return 'USD'; }
      if (currency.includes('FUNT')) { return 'GBP'; }
      return currency;
    }
  }

  // Default based on language
  const defaultCurrencies = {
    pol: 'PLN',
    eng: 'USD',
    deu: 'EUR',
    fra: 'EUR'
  };

  return defaultCurrencies[language] || 'PLN';
}

/**
 * Extract VAT rates
 * @param {string} text - Text content
 * @param {string} language - Language code
 * @returns {Array} - VAT rates
 */
function extractVatRates(text, language) {
  const vatPattern = /(\d{1,2})[%\s]*(?:vat|podatek|tax|mwst|tva)/gi;
  const matches = text.match(vatPattern);

  if (matches) {
    const rates = matches.map(match => {
      const rate = parseInt(match.match(/\d+/)[0]);
      return rate / 100; // Convert percentage to decimal
    });

    // Remove duplicates and sort
    return [...new Set(rates)].sort();
  }

  return [];
}

/**
 * Extract payment method
 * @param {string} text - Text content
 * @param {string} language - Language code
 * @returns {string|null} - Payment method
 */
function extractPaymentMethod(text, language) {
  const methods = {
    pol: ['przelew', 'gotówka', 'karta', 'czek'],
    eng: ['transfer', 'cash', 'card', 'check', 'wire'],
    deu: ['überweisung', 'bargeld', 'karte', 'scheck'],
    fra: ['virement', 'espèces', 'carte', 'chèque']
  };

  const langMethods = methods[language] || methods.pol;

  for (const method of langMethods) {
    if (text.toLowerCase().includes(method)) {
      return method;
    }
  }

  return null;
}

/**
 * Extract description/notes
 * @param {string} text - Text content
 * @param {string} language - Language code
 * @returns {string|null} - Description
 */
function extractDescription(text, language) {
  const lines = text.split('\n');
  const longLines = lines.filter(line => line.trim().length > 20 && line.trim().length < 200);

  if (longLines.length > 0) {
    return longLines[0].trim();
  }

  return null;
}

/**
 * Calculate confidence score for fallback extraction
 * @param {Object} data - Extracted data
 * @returns {number} - Confidence score (0-1)
 */
function calculateFallbackConfidence(data) {
  const requiredFields = ['invoice_number', 'seller_name', 'buyer_name', 'total_gross'];
  const optionalFields = ['issue_date', 'currency', 'total_net', 'total_vat'];

  let score = 0;
  let maxScore = 0;

  // Required fields (higher weight)
  requiredFields.forEach(field => {
    maxScore += 3;
    if (data[field] && data[field] !== null) {
      score += 3;
    }
  });

  // Optional fields (lower weight)
  optionalFields.forEach(field => {
    maxScore += 1;
    if (data[field] && data[field] !== null) {
      score += 1;
    }
  });

  return maxScore > 0 ? Math.round((score / maxScore) * 100) / 100 : 0;
}

// Self-test functionality for Node.js environment
if (typeof module !== 'undefined' && require.main === module) {
  console.log('🧪 Testing fallbackExtraction...');

  const testText = `
    Faktura VAT nr FV/2024/001
    Data wystawienia: 15.01.2024
    Sprzedawca: Test Company Sp. z o.o.
    NIP: 123-456-78-90
    Nabywca: Customer Ltd.
    Razem do zapłaty: 1234,56 PLN
    VAT 23%
  `;

  const result = extractInvoiceDataFallback(testText, 'pol');

  console.log('✅ Fallback extraction test:', result.success ? 'passed' : 'failed');
  console.log('✅ Invoice number extraction:', result.data?.invoice_number ? 'passed' : 'failed');
  console.log('✅ Date extraction:', result.data?.issue_date ? 'passed' : 'failed');
  console.log('✅ Amount extraction:', result.data?.total_gross ? 'passed' : 'failed');
  console.log('✅ Currency extraction:', result.data?.currency === 'PLN' ? 'passed' : 'failed');
  console.log('✅ Confidence calculation:', result.confidence > 0 ? 'passed' : 'failed');

  console.log('🎉 All fallbackExtraction tests passed!');
}
