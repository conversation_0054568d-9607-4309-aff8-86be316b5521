/**
 * Configuration Validation Utilities
 *
 * Validates environment configuration to ensure all required settings are present
 * and properly formatted.
 *
 * ASSIGNMENT-039: Environment Configuration Setup
 * Epic: EPIC-B01 - Subscription & Monetization System
 */

/**
 * Validate the complete configuration object
 * @param {Object} config - Configuration object to validate
 * @returns {Object} Validation result with isValid flag and errors array
 */
export function validateConfig(config) {
  const errors = [];

  try {
    // Validate API Keys
    const apiKeyErrors = validateApiKeys(config.apiKeys);
    errors.push(...apiKeyErrors);

    // Validate Company Information
    const companyErrors = validateCompanyInfo(config.company);
    errors.push(...companyErrors);

    // Validate Application Configuration
    const appErrors = validateAppConfig(config.app);
    errors.push(...appErrors);

    // Validate Subscription Configuration
    const subscriptionErrors = validateSubscriptionConfig(config.subscription);
    errors.push(...subscriptionErrors);

    // Validate Localization Configuration
    const localizationErrors = validateLocalizationConfig(config.localization);
    errors.push(...localizationErrors);

    return {
      isValid: errors.length === 0,
      errors: errors,
      warnings: []
    };

  } catch (error) {
    return {
      isValid: false,
      errors: [`Configuration validation failed: ${error.message}`],
      warnings: []
    };
  }
}

/**
 * Validate API keys configuration
 * @param {Object} apiKeys - API keys configuration
 * @returns {Array} Array of error messages
 */
function validateApiKeys(apiKeys) {
  const errors = [];

  if (!apiKeys || typeof apiKeys !== 'object') {
    errors.push('API keys configuration is missing or invalid');
    return errors;
  }

  // Validate DeepSeek API configuration
  if (!apiKeys.deepseek) {
    errors.push('DeepSeek API configuration is missing');
  } else {
    if (apiKeys.deepseek.key && typeof apiKeys.deepseek.key !== 'string') {
      errors.push('DeepSeek API key must be a string');
    } else if (apiKeys.deepseek.key && !apiKeys.deepseek.key.startsWith('sk-')) {
      errors.push('DeepSeek API key appears to be invalid (should start with "sk-")');
    }

    if (!apiKeys.deepseek.url || !isValidUrl(apiKeys.deepseek.url)) {
      errors.push('DeepSeek API URL is required and must be a valid URL');
    }

    if (apiKeys.deepseek.maxTokens && (!Number.isInteger(apiKeys.deepseek.maxTokens) || apiKeys.deepseek.maxTokens <= 0)) {
      errors.push('DeepSeek max tokens must be a positive integer');
    }

    if (apiKeys.deepseek.temperature && (typeof apiKeys.deepseek.temperature !== 'number' || apiKeys.deepseek.temperature < 0 || apiKeys.deepseek.temperature > 2)) {
      errors.push('DeepSeek temperature must be a number between 0 and 2');
    }
  }

  // Validate Stripe API configuration (if payment processing is enabled)
  if (apiKeys.stripe) {
    if (apiKeys.stripe.publishableKey && !apiKeys.stripe.publishableKey.startsWith('pk_')) {
      errors.push('Stripe publishable key appears to be invalid (should start with "pk_")');
    }

    if (apiKeys.stripe.secretKey && !apiKeys.stripe.secretKey.startsWith('sk_')) {
      errors.push('Stripe secret key appears to be invalid (should start with "sk_")');
    }
  }

  return errors;
}

/**
 * Validate company information
 * @param {Object} company - Company configuration
 * @returns {Array} Array of error messages
 */
function validateCompanyInfo(company) {
  const errors = [];

  if (!company || typeof company !== 'object') {
    errors.push('Company information is missing or invalid');
    return errors;
  }

  // Required company fields
  if (!company.name || typeof company.name !== 'string' || company.name.trim().length === 0) {
    errors.push('Company name is required and must be a non-empty string');
  }

  // Validate email addresses
  if (company.contact) {
    if (company.contact.email && !isValidEmail(company.contact.email)) {
      errors.push('Company email must be a valid email address');
    }

    if (company.contact.supportEmail && !isValidEmail(company.contact.supportEmail)) {
      errors.push('Company support email must be a valid email address');
    }

    if (company.contact.website && !isValidUrl(company.contact.website)) {
      errors.push('Company website must be a valid URL');
    }
  }

  // Validate branding colors
  if (company.branding) {
    const colorFields = ['primaryColor', 'secondaryColor', 'accentColor'];
    colorFields.forEach(field => {
      if (company.branding[field] && !isValidHexColor(company.branding[field])) {
        errors.push(`Company ${field} must be a valid hex color (e.g., #2563eb)`);
      }
    });
  }

  return errors;
}

/**
 * Validate application configuration
 * @param {Object} app - Application configuration
 * @returns {Array} Array of error messages
 */
function validateAppConfig(app) {
  const errors = [];

  if (!app || typeof app !== 'object') {
    errors.push('Application configuration is missing or invalid');
    return errors;
  }

  // Validate environment
  const validEnvironments = ['development', 'staging', 'production'];
  if (app.environment && !validEnvironments.includes(app.environment)) {
    errors.push(`Application environment must be one of: ${validEnvironments.join(', ')}`);
  }

  // Validate version format
  if (app.version && !isValidVersion(app.version)) {
    errors.push('Application version must be in semantic version format (e.g., 1.0.0)');
  }

  // Validate build number
  if (app.buildNumber && (!Number.isInteger(app.buildNumber) || app.buildNumber <= 0)) {
    errors.push('Application build number must be a positive integer');
  }

  // Validate log level
  const validLogLevels = ['error', 'warn', 'info', 'debug'];
  if (app.logLevel && !validLogLevels.includes(app.logLevel)) {
    errors.push(`Application log level must be one of: ${validLogLevels.join(', ')}`);
  }

  return errors;
}

/**
 * Validate subscription configuration
 * @param {Object} subscription - Subscription configuration
 * @returns {Array} Array of error messages
 */
function validateSubscriptionConfig(subscription) {
  const errors = [];

  if (!subscription || typeof subscription !== 'object') {
    errors.push('Subscription configuration is missing or invalid');
    return errors;
  }

  // Validate tiers
  if (!subscription.tiers || typeof subscription.tiers !== 'object') {
    errors.push('Subscription tiers configuration is missing or invalid');
    return errors;
  }

  const requiredTiers = ['starter', 'professional', 'business', 'enterprise'];
  requiredTiers.forEach(tier => {
    if (!subscription.tiers[tier]) {
      errors.push(`Subscription tier '${tier}' is missing`);
    } else {
      const tierConfig = subscription.tiers[tier];

      // Validate invoice limits
      if (tierConfig.invoiceLimit !== undefined) {
        if (!Number.isInteger(tierConfig.invoiceLimit) || (tierConfig.invoiceLimit < -1)) {
          errors.push(`Subscription tier '${tier}' invoice limit must be a non-negative integer or -1 for unlimited`);
        }
      }

      // Validate pricing (for paid tiers)
      if (tier !== 'starter') {
        if (tierConfig.priceMonthly !== undefined) {
          if (!Number.isInteger(tierConfig.priceMonthly) || tierConfig.priceMonthly <= 0) {
            errors.push(`Subscription tier '${tier}' monthly price must be a positive integer (in cents)`);
          }
        }

        if (tierConfig.priceYearly !== undefined) {
          if (!Number.isInteger(tierConfig.priceYearly) || tierConfig.priceYearly <= 0) {
            errors.push(`Subscription tier '${tier}' yearly price must be a positive integer (in cents)`);
          }
        }
      }
    }
  });

  // Validate trial configuration
  if (subscription.trial) {
    if (subscription.trial.days && (!Number.isInteger(subscription.trial.days) || subscription.trial.days <= 0)) {
      errors.push('Trial days must be a positive integer');
    }

    if (subscription.trial.invoiceLimit && (!Number.isInteger(subscription.trial.invoiceLimit) || subscription.trial.invoiceLimit <= 0)) {
      errors.push('Trial invoice limit must be a positive integer');
    }
  }

  return errors;
}

/**
 * Validate localization configuration
 * @param {Object} localization - Localization configuration
 * @returns {Array} Array of error messages
 */
function validateLocalizationConfig(localization) {
  const errors = [];

  if (!localization || typeof localization !== 'object') {
    errors.push('Localization configuration is missing or invalid');
    return errors;
  }

  // Validate language codes
  if (localization.defaultLanguage && !isValidLanguageCode(localization.defaultLanguage)) {
    errors.push('Default language must be a valid language code (e.g., en, pl, de)');
  }

  if (localization.supportedLanguages && Array.isArray(localization.supportedLanguages)) {
    localization.supportedLanguages.forEach(lang => {
      if (!isValidLanguageCode(lang)) {
        errors.push(`Supported language '${lang}' is not a valid language code`);
      }
    });
  }

  // Validate currency code
  if (localization.defaultCurrency && !isValidCurrencyCode(localization.defaultCurrency)) {
    errors.push('Default currency must be a valid currency code (e.g., EUR, USD, PLN)');
  }

  // Validate VAT rates
  if (localization.vatRates) {
    const vatFields = ['standard', 'reduced', 'zero'];
    vatFields.forEach(field => {
      if (localization.vatRates[field] !== undefined) {
        if (typeof localization.vatRates[field] !== 'number' || localization.vatRates[field] < 0 || localization.vatRates[field] > 100) {
          errors.push(`VAT rate '${field}' must be a number between 0 and 100`);
        }
      }
    });
  }

  return errors;
}

/**
 * Validate URL format
 * @param {string} url - URL to validate
 * @returns {boolean} True if valid URL
 */
function isValidUrl(url) {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

/**
 * Validate email format
 * @param {string} email - Email to validate
 * @returns {boolean} True if valid email
 */
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate hex color format
 * @param {string} color - Color to validate
 * @returns {boolean} True if valid hex color
 */
function isValidHexColor(color) {
  const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
  return hexColorRegex.test(color);
}

/**
 * Validate semantic version format
 * @param {string} version - Version to validate
 * @returns {boolean} True if valid semantic version
 */
function isValidVersion(version) {
  const versionRegex = /^\d+\.\d+\.\d+(-[a-zA-Z0-9.-]+)?$/;
  return versionRegex.test(version);
}

/**
 * Validate language code format
 * @param {string} lang - Language code to validate
 * @returns {boolean} True if valid language code
 */
function isValidLanguageCode(lang) {
  const langRegex = /^[a-z]{2}(-[A-Z]{2})?$/;
  return langRegex.test(lang);
}

/**
 * Validate currency code format
 * @param {string} currency - Currency code to validate
 * @returns {boolean} True if valid currency code
 */
function isValidCurrencyCode(currency) {
  const currencyRegex = /^[A-Z]{3}$/;
  return currencyRegex.test(currency);
}
