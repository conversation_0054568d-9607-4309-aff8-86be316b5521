/**
 * Advanced Similarity Service
 *
 * Enhanced document similarity service with improved vector search algorithms,
 * context-aware similarity detection, and advanced relationship scoring.
 * Builds upon the existing VectorSimilarityService with professional-grade enhancements.
 *
 * <AUTHOR> Agent
 * @version 1.0.0
 * @since 2025-06-14
 */

import { ProcessingLogger } from '../utils/ProcessingLogger.js';
import { VectorSimilarityService } from './VectorSimilarityService.js';
import { DocumentRelationshipScorer } from './DocumentRelationshipScorer.js';
import { VectorSearchService } from './VectorSearchService.js';

export class AdvancedSimilarityService {
  constructor(options = {}) {
    this.logger = new ProcessingLogger('AdvancedSimilarityService');

    // Configuration
    this.config = {
      // Enhanced similarity thresholds
      strongSimilarityThreshold: options.strongSimilarityThreshold || 0.85,
      moderateSimilarityThreshold: options.moderateSimilarityThreshold || 0.70,
      weakSimilarityThreshold: options.weakSimilarityThreshold || 0.55,

      // Context-aware scoring weights
      contextWeights: {
        semantic: options.semanticWeight || 0.4,
        structural: options.structuralWeight || 0.2,
        temporal: options.temporalWeight || 0.15,
        business: options.businessWeight || 0.15,
        metadata: options.metadataWeight || 0.1
      },

      // Performance optimization
      maxConcurrentComparisons: options.maxConcurrentComparisons || 8,
      batchSize: options.batchSize || 50,
      cacheEnabled: options.cacheEnabled !== false,

      // Advanced features
      useContextualEmbeddings: options.useContextualEmbeddings !== false,
      enableClusteringAnalysis: options.enableClusteringAnalysis !== false,
      adaptiveThresholds: options.adaptiveThresholds !== false,

      ...options
    };

    // Initialize enhanced services (avoid circular dependency)
    this.vectorSimilarityService = null; // Will be injected to avoid circular dependency

    this.relationshipScorer = new DocumentRelationshipScorer({
      vectorSimilarity: this.config.contextWeights.semantic,
      contentSimilarity: this.config.contextWeights.structural,
      metadataSimilarity: this.config.contextWeights.metadata,
      temporalProximity: this.config.contextWeights.temporal,
      businessContext: this.config.contextWeights.business,
      ...options.relationshipScorerOptions
    });

    this.vectorSearchService = new VectorSearchService({
      defaultAlgorithm: 'hybrid',
      similarityThreshold: this.config.weakSimilarityThreshold,
      indexingEnabled: true,
      cacheResults: this.config.cacheEnabled,
      ...options.vectorSearchOptions
    });

    // Advanced similarity cache
    this.similarityCache = new Map();
    this.maxCacheSize = options.maxCacheSize || 1000;

    // Performance metrics
    this.metrics = {
      totalComparisons: 0,
      averageProcessingTime: 0,
      cacheHitRate: 0,
      accuracyScore: 0,
      enhancementSuccess: 0
    };

    // Document clustering data
    this.documentClusters = new Map();
    this.clusterCentroids = new Map();
  }

  /**
   * Inject VectorSimilarityService to avoid circular dependency
   * @param {VectorSimilarityService} vectorSimilarityService - The service instance
   */
  setVectorSimilarityService(vectorSimilarityService) {
    this.vectorSimilarityService = vectorSimilarityService;
  }

  /**
   * Enhanced similarity calculation with context awareness
   * @param {Object} embedding1 - First document embedding
   * @param {Object} embedding2 - Second document embedding
   * @param {Object} options - Enhanced similarity options
   * @returns {Promise<Object>} Enhanced similarity result
   */
  async calculateEnhancedSimilarity(embedding1, embedding2, options = {}) {
    const startTime = Date.now();
    const comparisonId = `enhanced_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      this.metrics.totalComparisons++;

      this.logger.log('🧠 Starting enhanced similarity calculation', {
        comparisonId,
        doc1Id: embedding1.metadata?.documentId,
        doc2Id: embedding2.metadata?.documentId,
        useContextual: this.config.useContextualEmbeddings,
        enableClustering: this.config.enableClusteringAnalysis
      });

      // Check cache first
      const cacheKey = this.generateCacheKey(embedding1, embedding2, options);
      if (this.config.cacheEnabled && this.similarityCache.has(cacheKey)) {
        this.logger.log('📋 Using cached enhanced similarity result', { comparisonId });
        return this.similarityCache.get(cacheKey);
      }

      // Get base similarity from existing service (if available)
      let baseSimilarity;
      if (this.vectorSimilarityService) {
        baseSimilarity = await this.vectorSimilarityService.calculateSimilarity(
          embedding1,
          embedding2,
          { ...options, useEnhancedScoring: false } // Avoid recursion
        );
      } else {
        // Fallback to basic cosine similarity
        const score = this.cosineSimilarity(embedding1.vector, embedding2.vector);
        baseSimilarity = {
          score,
          confidence: 0.7,
          relationshipType: score >= 0.7 ? 'similar' : 'unrelated',
          isRelated: score >= 0.5
        };
      }

      // Apply context-aware enhancements
      const contextualScore = await this.calculateContextualSimilarity(
        embedding1,
        embedding2,
        baseSimilarity,
        options
      );

      // Calculate structural similarity
      const structuralScore = this.calculateStructuralSimilarity(embedding1, embedding2);

      // Apply clustering analysis if enabled
      let clusteringScore = 0;
      if (this.config.enableClusteringAnalysis) {
        clusteringScore = await this.calculateClusteringSimilarity(embedding1, embedding2);
      }

      // Combine all similarity scores
      const enhancedScore = this.combineEnhancedScores({
        base: baseSimilarity.score,
        contextual: contextualScore,
        structural: structuralScore,
        clustering: clusteringScore
      });

      // Determine enhanced relationship type
      const relationshipType = this.determineEnhancedRelationshipType(enhancedScore);

      // Calculate enhanced confidence
      const confidence = this.calculateEnhancedConfidence({
        baseConfidence: baseSimilarity.confidence,
        enhancedScore,
        contextualScore,
        structuralScore,
        clusteringScore
      });

      const result = {
        comparisonId,
        score: enhancedScore,
        confidence,
        relationshipType,
        isRelated: enhancedScore >= this.config.weakSimilarityThreshold,
        enhanced: true,
        components: {
          base: baseSimilarity.score,
          contextual: contextualScore,
          structural: structuralScore,
          clustering: clusteringScore
        },
        weights: this.config.contextWeights,
        baseSimilarity,
        metadata: {
          doc1Id: embedding1.metadata?.documentId,
          doc2Id: embedding2.metadata?.documentId,
          processingTime: Date.now() - startTime,
          timestamp: new Date().toISOString(),
          version: '1.0.0',
          algorithm: 'enhanced-contextual'
        }
      };

      // Cache result
      if (this.config.cacheEnabled) {
        this.cacheResult(cacheKey, result);
      }

      // Update metrics
      this.updateMetrics(Date.now() - startTime, true);

      this.logger.log('✅ Enhanced similarity calculation completed', {
        comparisonId,
        enhancedScore: enhancedScore.toFixed(4),
        relationshipType,
        confidence: confidence.toFixed(4),
        processingTime: result.metadata.processingTime
      });

      return result;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.updateMetrics(processingTime, false);

      this.logger.error('❌ Enhanced similarity calculation failed', {
        comparisonId,
        error: error.message,
        processingTime
      });

      // Fallback to base similarity (if available)
      if (this.vectorSimilarityService) {
        return this.vectorSimilarityService.calculateSimilarity(embedding1, embedding2, options);
      }
      // Basic fallback
      const score = this.cosineSimilarity(embedding1.vector, embedding2.vector);
      return {
        score,
        confidence: 0.5,
        relationshipType: score >= 0.7 ? 'similar' : 'unrelated',
        isRelated: score >= 0.5,
        error: error.message
      };

    }
  }

  /**
   * Find similar documents with enhanced search
   * @param {Object} targetEmbedding - Target document embedding
   * @param {Array} documentEmbeddings - Collection of document embeddings
   * @param {Object} options - Enhanced search options
   * @returns {Promise<Array>} Enhanced similarity results
   */
  async findSimilarDocumentsEnhanced(targetEmbedding, documentEmbeddings, options = {}) {
    const startTime = Date.now();

    try {
      this.logger.log('🔍 Starting enhanced document similarity search', {
        targetDoc: targetEmbedding.metadata?.documentId,
        collectionSize: documentEmbeddings?.length || 'using index',
        threshold: options.threshold || this.config.weakSimilarityThreshold,
        maxResults: options.maxResults || 10,
        useEnhanced: true
      });

      // Use enhanced vector search service
      const searchResults = await this.vectorSearchService.search(targetEmbedding.vector, {
        algorithm: 'hybrid',
        k: options.maxResults || 10,
        threshold: options.threshold || this.config.weakSimilarityThreshold,
        includeMetadata: true,
        excludeIds: [targetEmbedding.metadata?.documentId].filter(Boolean),
        ...options
      });

      // Enhance each result with contextual analysis
      const enhancedResults = [];
      for (const result of searchResults) {
        const documentEmbedding = documentEmbeddings?.find(
          doc => doc.metadata?.documentId === result.id
        );

        if (documentEmbedding) {
          const enhancedSimilarity = await this.calculateEnhancedSimilarity(
            targetEmbedding,
            documentEmbedding,
            options
          );

          enhancedResults.push({
            documentId: result.id,
            similarity: enhancedSimilarity,
            embedding: documentEmbedding,
            searchResult: result,
            enhanced: true
          });
        }
      }

      // Sort by enhanced score
      enhancedResults.sort((a, b) => b.similarity.score - a.similarity.score);

      const processingTime = Date.now() - startTime;
      this.logger.log('✅ Enhanced document similarity search completed', {
        targetDoc: targetEmbedding.metadata?.documentId,
        resultsFound: enhancedResults.length,
        processingTime
      });

      return enhancedResults;

    } catch (error) {
      this.logger.error('❌ Enhanced document similarity search failed', {
        error: error.message,
        targetDoc: targetEmbedding.metadata?.documentId
      });

      // Fallback to base similarity service (if available)
      if (this.vectorSimilarityService) {
        return this.vectorSimilarityService.findSimilarDocuments(
          targetEmbedding,
          documentEmbeddings,
          options
        );
      }
      // Basic fallback - return empty results
      return [];

    }
  }

  /**
   * Calculate contextual similarity using document metadata and content
   * @private
   */
  async calculateContextualSimilarity(embedding1, embedding2, baseSimilarity, options) {
    try {
      // Use relationship scorer for comprehensive contextual analysis
      const doc1 = { embedding: embedding1, metadata: embedding1.metadata };
      const doc2 = { embedding: embedding2, metadata: embedding2.metadata };

      const relationshipScore = this.relationshipScorer.scoreRelationship(doc1, doc2, options);

      // Combine with base similarity for contextual enhancement
      const contextualBoost = relationshipScore.compositeScore * 0.3;
      return Math.min(baseSimilarity.score + contextualBoost, 1.0);

    } catch (error) {
      this.logger.warn('⚠️ Contextual similarity calculation failed', { error: error.message });
      return baseSimilarity.score;
    }
  }

  /**
   * Calculate structural similarity based on document structure
   * @private
   */
  calculateStructuralSimilarity(embedding1, embedding2) {
    try {
      const meta1 = embedding1.metadata || {};
      const meta2 = embedding2.metadata || {};

      let structuralScore = 0;
      let factors = 0;

      // Document type similarity
      if (meta1.documentType && meta2.documentType) {
        factors++;
        if (meta1.documentType === meta2.documentType) {
          structuralScore += 0.4;
        }
      }

      // Content length similarity
      if (meta1.textLength && meta2.textLength) {
        factors++;
        const lengthRatio = Math.min(meta1.textLength, meta2.textLength) /
                           Math.max(meta1.textLength, meta2.textLength);
        structuralScore += lengthRatio * 0.3;
      }

      // Language similarity
      if (meta1.language && meta2.language) {
        factors++;
        if (meta1.language === meta2.language) {
          structuralScore += 0.3;
        }
      }

      return factors > 0 ? structuralScore / factors : 0;

    } catch (error) {
      this.logger.warn('⚠️ Structural similarity calculation failed', { error: error.message });
      return 0;
    }
  }

  /**
   * Calculate clustering-based similarity
   * @private
   */
  async calculateClusteringSimilarity(embedding1, embedding2) {
    try {
      const doc1Id = embedding1.metadata?.documentId;
      const doc2Id = embedding2.metadata?.documentId;

      if (!doc1Id || !doc2Id) {
        return 0;
      }

      // Check if documents are in the same cluster
      const cluster1 = this.documentClusters.get(doc1Id);
      const cluster2 = this.documentClusters.get(doc2Id);

      if (cluster1 && cluster2) {
        if (cluster1 === cluster2) {
          return 0.8; // High similarity for same cluster
        }

        // Calculate inter-cluster similarity
        const centroid1 = this.clusterCentroids.get(cluster1);
        const centroid2 = this.clusterCentroids.get(cluster2);

        if (centroid1 && centroid2) {
          return this.cosineSimilarity(centroid1, centroid2) * 0.4;
        }
      }

      return 0;

    } catch (error) {
      this.logger.warn('⚠️ Clustering similarity calculation failed', { error: error.message });
      return 0;
    }
  }

  /**
   * Combine enhanced similarity scores
   * @private
   */
  combineEnhancedScores(scores) {
    const weights = this.config.contextWeights;

    let combinedScore = 0;
    let totalWeight = 0;

    // Base semantic similarity
    combinedScore += scores.base * weights.semantic;
    totalWeight += weights.semantic;

    // Contextual enhancement
    combinedScore += scores.contextual * weights.business;
    totalWeight += weights.business;

    // Structural similarity
    combinedScore += scores.structural * weights.structural;
    totalWeight += weights.structural;

    // Clustering similarity
    if (scores.clustering > 0) {
      combinedScore += scores.clustering * 0.1;
      totalWeight += 0.1;
    }

    return totalWeight > 0 ? combinedScore / totalWeight : 0;
  }

  /**
   * Determine enhanced relationship type
   * @private
   */
  determineEnhancedRelationshipType(score) {
    if (score >= this.config.strongSimilarityThreshold) {
      return 'strong';
    } else if (score >= this.config.moderateSimilarityThreshold) {
      return 'moderate';
    } else if (score >= this.config.weakSimilarityThreshold) {
      return 'weak';
    }
    return 'none';
  }

  /**
   * Calculate enhanced confidence score
   * @private
   */
  calculateEnhancedConfidence(components) {
    let confidence = components.baseConfidence * 0.5;

    // Boost confidence based on multiple scoring components
    const componentCount = Object.values(components).filter(c => c > 0).length;
    confidence += (componentCount / 5) * 0.3;

    // Consistency boost
    const scores = [components.enhancedScore, components.contextualScore, components.structuralScore];
    const avgScore = scores.reduce((sum, s) => sum + s, 0) / scores.length;
    const variance = scores.reduce((sum, s) => sum + Math.pow(s - avgScore, 2), 0) / scores.length;

    confidence += (1 - Math.min(variance, 1)) * 0.2;

    return Math.min(confidence, 1.0);
  }

  /**
   * Generate cache key for similarity results
   * @private
   */
  generateCacheKey(embedding1, embedding2, options) {
    const id1 = embedding1.metadata?.documentId || 'unknown1';
    const id2 = embedding2.metadata?.documentId || 'unknown2';
    const optionsHash = this.hashObject(options);
    return `enhanced_${id1}_${id2}_${optionsHash}`;
  }

  /**
   * Cache similarity result
   * @private
   */
  cacheResult(key, result) {
    if (this.similarityCache.size >= this.maxCacheSize) {
      const firstKey = this.similarityCache.keys().next().value;
      this.similarityCache.delete(firstKey);
    }
    this.similarityCache.set(key, result);
  }

  /**
   * Update performance metrics
   * @private
   */
  updateMetrics(processingTime, success) {
    this.metrics.averageProcessingTime =
      (this.metrics.averageProcessingTime * (this.metrics.totalComparisons - 1) + processingTime) /
      this.metrics.totalComparisons;

    if (success) {
      this.metrics.enhancementSuccess++;
    }
  }

  /**
   * Simple cosine similarity calculation
   * @private
   */
  cosineSimilarity(vector1, vector2) {
    if (!vector1 || !vector2 || vector1.length !== vector2.length) {
      return 0;
    }

    let dotProduct = 0;
    let magnitude1 = 0;
    let magnitude2 = 0;

    for (let i = 0; i < vector1.length; i++) {
      dotProduct += vector1[i] * vector2[i];
      magnitude1 += vector1[i] * vector1[i];
      magnitude2 += vector2[i] * vector2[i];
    }

    magnitude1 = Math.sqrt(magnitude1);
    magnitude2 = Math.sqrt(magnitude2);

    return (magnitude1 === 0 || magnitude2 === 0) ? 0 : dotProduct / (magnitude1 * magnitude2);
  }

  /**
   * Hash object for caching
   * @private
   */
  hashObject(obj) {
    const str = JSON.stringify(obj);
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString();
  }

  /**
   * Get performance metrics
   * @returns {Object} Current performance metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      cacheHitRate: this.similarityCache.size > 0 ?
        (this.metrics.cacheHitRate / this.metrics.totalComparisons) * 100 : 0,
      enhancementSuccessRate: this.metrics.totalComparisons > 0 ?
        (this.metrics.enhancementSuccess / this.metrics.totalComparisons) * 100 : 0
    };
  }

  /**
   * Clear cache and reset metrics
   */
  reset() {
    this.similarityCache.clear();
    this.documentClusters.clear();
    this.clusterCentroids.clear();
    this.metrics = {
      totalComparisons: 0,
      averageProcessingTime: 0,
      cacheHitRate: 0,
      accuracyScore: 0,
      enhancementSuccess: 0
    };
    this.logger.log('🔄 Advanced similarity service reset');
  }
}
