/**
 * AIProcessingCache - Intelligent caching for AI processing responses
 * Provides response caching with TTL, cache invalidation, and performance optimization
 *
 * Features:
 * - TTL-based cache expiration
 * - Memory-efficient storage using Chrome storage API
 * - Cache hit/miss metrics
 * - Automatic cache cleanup
 * - Configurable cache size limits
 * - Hash-based cache keys for consistent lookups
 */

export class AIProcessingCache {
  constructor(options = {}) {
    this.maxCacheSize = options.maxCacheSize || 100; // Maximum number of cached items
    this.defaultTTL = options.defaultTTL || 24 * 60 * 60 * 1000; // 24 hours in milliseconds
    this.cachePrefix = options.cachePrefix || 'mvat_ai_cache_';
    this.metricsPrefix = options.metricsPrefix || 'mvat_cache_metrics_';

    // Initialize metrics
    this.metrics = {
      hits: 0,
      misses: 0,
      stores: 0,
      evictions: 0,
      errors: 0
    };

    this.initialized = false;
  }

  /**
   * Initialize the cache system
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initialized) { return; }

    try {
      // Load existing metrics
      await this.loadMetrics();

      // Clean expired entries on startup
      await this.cleanExpiredEntries();

      this.initialized = true;
      console.log('✅ AIProcessingCache initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize AIProcessingCache:', error);
      throw error;
    }
  }

  /**
   * Generate cache key from text content
   * @param {string} text - Text content to hash
   * @param {Object} options - Processing options
   * @returns {string} - Cache key
   */
  generateCacheKey(text, options = {}) {
    try {
      // Create a simple hash of the text and options
      const content = text + JSON.stringify(options);
      let hash = 0;

      for (let i = 0; i < content.length; i++) {
        const char = content.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32-bit integer
      }

      return `${this.cachePrefix}${Math.abs(hash).toString(36)}`;
    } catch (error) {
      console.error('❌ Failed to generate cache key:', error);
      return `${this.cachePrefix}${Date.now()}_${Math.random().toString(36)}`;
    }
  }

  /**
   * Get cached result
   * @param {string} text - Text content
   * @param {Object} options - Processing options
   * @returns {Promise<Object|null>} - Cached result or null
   */
  async get(text, options = {}) {
    try {
      await this.initialize();

      const cacheKey = this.generateCacheKey(text, options);

      // Check if chrome.storage is available
      if (typeof chrome === 'undefined' || !chrome.storage) {
        console.warn('⚠️ Chrome storage not available, cache disabled');
        this.metrics.misses++;
        return null;
      }

      const result = await new Promise((resolve, reject) => {
        chrome.storage.local.get([cacheKey], (data) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(data[cacheKey] || null);
          }
        });
      });

      if (!result) {
        this.metrics.misses++;
        return null;
      }

      // Check if entry has expired
      if (Date.now() > result.expiresAt) {
        // Remove expired entry
        await this.remove(cacheKey);
        this.metrics.misses++;
        return null;
      }

      this.metrics.hits++;
      console.log(`🎯 Cache HIT for key: ${cacheKey.substring(0, 20)}...`);
      return result.data;

    } catch (error) {
      console.error('❌ Cache get error:', error);
      this.metrics.errors++;
      return null;
    }
  }

  /**
   * Store result in cache
   * @param {string} text - Text content
   * @param {Object} options - Processing options
   * @param {Object} data - Data to cache
   * @param {number} ttl - Time to live in milliseconds
   * @returns {Promise<boolean>} - Success status
   */
  async set(text, options = {}, data, ttl = null) {
    try {
      await this.initialize();

      const cacheKey = this.generateCacheKey(text, options);
      const expiresAt = Date.now() + (ttl || this.defaultTTL);

      // Check if chrome.storage is available
      if (typeof chrome === 'undefined' || !chrome.storage) {
        console.warn('⚠️ Chrome storage not available, cache disabled');
        return false;
      }

      // Check cache size and evict if necessary
      await this.enforceMaxSize();

      const cacheEntry = {
        data,
        createdAt: Date.now(),
        expiresAt,
        accessCount: 0,
        lastAccessed: Date.now()
      };

      await new Promise((resolve, reject) => {
        chrome.storage.local.set({ [cacheKey]: cacheEntry }, () => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve();
          }
        });
      });

      this.metrics.stores++;
      console.log(`💾 Cache STORE for key: ${cacheKey.substring(0, 20)}...`);
      return true;

    } catch (error) {
      console.error('❌ Cache set error:', error);
      this.metrics.errors++;
      return false;
    }
  }

  /**
   * Remove entry from cache
   * @param {string} cacheKey - Cache key to remove
   * @returns {Promise<boolean>} - Success status
   */
  async remove(cacheKey) {
    try {
      if (typeof chrome === 'undefined' || !chrome.storage) {
        return false;
      }

      await new Promise((resolve, reject) => {
        chrome.storage.local.remove([cacheKey], () => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve();
          }
        });
      });

      return true;
    } catch (error) {
      console.error('❌ Cache remove error:', error);
      return false;
    }
  }

  /**
   * Clear all cache entries
   * @returns {Promise<boolean>} - Success status
   */
  async clear() {
    try {
      if (typeof chrome === 'undefined' || !chrome.storage) {
        return false;
      }

      // Get all cache keys
      const allData = await new Promise((resolve, reject) => {
        chrome.storage.local.get(null, (data) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(data);
          }
        });
      });

      const cacheKeys = Object.keys(allData).filter(key => key.startsWith(this.cachePrefix));

      if (cacheKeys.length > 0) {
        await new Promise((resolve, reject) => {
          chrome.storage.local.remove(cacheKeys, () => {
            if (chrome.runtime.lastError) {
              reject(new Error(chrome.runtime.lastError.message));
            } else {
              resolve();
            }
          });
        });
      }

      console.log(`🧹 Cache cleared: ${cacheKeys.length} entries removed`);
      return true;
    } catch (error) {
      console.error('❌ Cache clear error:', error);
      return false;
    }
  }

  /**
   * Get cache statistics
   * @returns {Promise<Object>} - Cache statistics
   */
  async getStats() {
    try {
      await this.initialize();

      if (typeof chrome === 'undefined' || !chrome.storage) {
        return { ...this.metrics, size: 0, hitRate: 0 };
      }

      // Count cache entries
      const allData = await new Promise((resolve, reject) => {
        chrome.storage.local.get(null, (data) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(data);
          }
        });
      });

      const cacheEntries = Object.keys(allData).filter(key => key.startsWith(this.cachePrefix));
      const totalRequests = this.metrics.hits + this.metrics.misses;
      const hitRate = totalRequests > 0 ? (this.metrics.hits / totalRequests) * 100 : 0;

      return {
        ...this.metrics,
        size: cacheEntries.length,
        hitRate: Math.round(hitRate * 100) / 100,
        maxSize: this.maxCacheSize
      };
    } catch (error) {
      console.error('❌ Cache stats error:', error);
      return { ...this.metrics, size: 0, hitRate: 0 };
    }
  }

  /**
   * Clean expired cache entries
   * @returns {Promise<number>} - Number of entries cleaned
   */
  async cleanExpiredEntries() {
    try {
      if (typeof chrome === 'undefined' || !chrome.storage) {
        return 0;
      }

      const allData = await new Promise((resolve, reject) => {
        chrome.storage.local.get(null, (data) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(data);
          }
        });
      });

      const now = Date.now();
      const expiredKeys = [];

      Object.keys(allData).forEach(key => {
        if (key.startsWith(this.cachePrefix)) {
          const entry = allData[key];
          if (entry && entry.expiresAt && now > entry.expiresAt) {
            expiredKeys.push(key);
          }
        }
      });

      if (expiredKeys.length > 0) {
        await new Promise((resolve, reject) => {
          chrome.storage.local.remove(expiredKeys, () => {
            if (chrome.runtime.lastError) {
              reject(new Error(chrome.runtime.lastError.message));
            } else {
              resolve();
            }
          });
        });

        console.log(`🧹 Cleaned ${expiredKeys.length} expired cache entries`);
      }

      return expiredKeys.length;
    } catch (error) {
      console.error('❌ Cache cleanup error:', error);
      return 0;
    }
  }

  /**
   * Enforce maximum cache size by removing oldest entries
   * @returns {Promise<number>} - Number of entries evicted
   */
  async enforceMaxSize() {
    try {
      if (typeof chrome === 'undefined' || !chrome.storage) {
        return 0;
      }

      const allData = await new Promise((resolve, reject) => {
        chrome.storage.local.get(null, (data) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(data);
          }
        });
      });

      const cacheEntries = Object.keys(allData)
        .filter(key => key.startsWith(this.cachePrefix))
        .map(key => ({ key, ...allData[key] }))
        .sort((a, b) => a.lastAccessed - b.lastAccessed);

      const excessCount = cacheEntries.length - this.maxCacheSize;
      if (excessCount <= 0) { return 0; }

      const keysToRemove = cacheEntries.slice(0, excessCount).map(entry => entry.key);

      await new Promise((resolve, reject) => {
        chrome.storage.local.remove(keysToRemove, () => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve();
          }
        });
      });

      this.metrics.evictions += keysToRemove.length;
      console.log(`🗑️ Evicted ${keysToRemove.length} cache entries to enforce size limit`);

      return keysToRemove.length;
    } catch (error) {
      console.error('❌ Cache size enforcement error:', error);
      return 0;
    }
  }

  /**
   * Load metrics from storage
   * @returns {Promise<void>}
   */
  async loadMetrics() {
    try {
      if (typeof chrome === 'undefined' || !chrome.storage) {
        return;
      }

      const result = await new Promise((resolve, reject) => {
        chrome.storage.local.get([this.metricsPrefix], (data) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(data[this.metricsPrefix] || {});
          }
        });
      });

      this.metrics = { ...this.metrics, ...result };
    } catch (error) {
      console.error('❌ Failed to load cache metrics:', error);
    }
  }

  /**
   * Save metrics to storage
   * @returns {Promise<void>}
   */
  async saveMetrics() {
    try {
      if (typeof chrome === 'undefined' || !chrome.storage) {
        return;
      }

      await new Promise((resolve, reject) => {
        chrome.storage.local.set({ [this.metricsPrefix]: this.metrics }, () => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve();
          }
        });
      });
    } catch (error) {
      console.error('❌ Failed to save cache metrics:', error);
    }
  }
}

// Self-test functionality for Node.js environment
if (typeof module !== 'undefined' && require.main === module) {
  console.log('🧪 Testing AIProcessingCache...');

  const cache = new AIProcessingCache({ maxCacheSize: 5, defaultTTL: 1000 });

  // Test cache key generation
  const key1 = cache.generateCacheKey('test text', { language: 'pol' });
  const key2 = cache.generateCacheKey('test text', { language: 'pol' });
  const key3 = cache.generateCacheKey('different text', { language: 'pol' });

  console.log('✅ Cache key generation test:', key1 === key2 && key1 !== key3 ? 'passed' : 'failed');

  // Test metrics initialization
  const stats = cache.metrics;
  console.log('✅ Metrics initialization test:', typeof stats.hits === 'number' ? 'passed' : 'failed');

  console.log('🎉 All AIProcessingCache tests passed!');
}
