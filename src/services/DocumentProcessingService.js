/**
 * DocumentProcessingService - Primary document processing orchestrator
 * Implements hierarchical document processing with specialized services
 *
 * Architecture:
 * DocumentProcessingService (Primary Orchestrator)
 *   ├── PDFProcessingService (PDF operations)
 *   ├── OCRProcessor (OCR operations)
 *   └── DocumentAnalysisService (Analysis operations)
 */

import { PDFProcessingService } from './PDFProcessingService.js';
import { OCRProcessor } from '../components/features/documents/OCRProcessor.js';
import { DocumentAnalysisService } from '../core/services/DocumentAnalysisService.js';
import { consolidatedFileValidationService } from './ConsolidatedFileValidationService.js';
import { processingLogger } from '../utils/ProcessingLogger.js';
import { uploadTracker } from '../utils/UploadTracker.js';
import analyticsService from './AnalyticsService.js';
import { ANALYTICS_EVENTS } from '../models/AnalyticsData.js';
import { documentProcessingPipeline } from './DocumentProcessingPipeline.js';

export class DocumentProcessingService {
  constructor() {
    this.initialized = false;

    // Initialize specialized services
    this.pdfService = new PDFProcessingService();
    this.ocrProcessor = new OCRProcessor();
    this.analysisService = new DocumentAnalysisService();

    // Processing configuration
    this.config = {
      maxFileSize: 50 * 1024 * 1024, // 50MB
      supportedTypes: [
        'application/pdf',
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/bmp',
        'image/tiff',
        'text/plain'
      ],
      enableProgressTracking: true,
      enableErrorRecovery: true
    };
  }

  /**
   * Initialize the document processing service and all specialized services
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initialized) {
      return;
    }

    try {
      console.log('🚀 Initializing DocumentProcessingService...');

      // Initialize specialized services
      await this.pdfService.initialize();
      await this.ocrProcessor.initialize();

      this.initialized = true;
      console.log('✅ DocumentProcessingService initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize DocumentProcessingService:', error);
      throw new Error(`DocumentProcessingService initialization failed: ${error.message}`);
    }
  }

  /**
   * Process a document through the hierarchical processing pipeline
   * @param {File} file - Document file to process
   * @param {Object} options - Processing options
   * @returns {Promise<Object>} - Processing result
   */
  async processDocument(file, options = {}) {
    const uploadId = processingLogger.generateUploadId();
    const startTime = performance.now();

    try {
      await this.initialize();

      const {
        progressCallback = null,
        enableAI = true,
        enableRAG = true,
        apiKey = null,
        language = 'pol',
        template = 'standard',
        companyInfo = null,
        useMultiStepPipeline = true // NEW: Enable multi-step pipeline by default
      } = options;

      // Use new multi-step pipeline if enabled
      if (useMultiStepPipeline) {
        console.log('🔄 Using multi-step document processing pipeline...');
        return await this.processDocumentWithPipeline(file, options);
      }

      // Start upload tracking
      uploadTracker.startUpload(uploadId, file.name, file.size);

      // Log processing start
      processingLogger.logProcessingStart(uploadId, file.name, file.type, file.size);

      if (progressCallback) {
        progressCallback({ stage: 'validation', progress: 5 });
      }

      // Phase 1: File Validation
      const validation = await this.validateFile(file);
      if (!validation.isValid) {
        throw new Error(`File validation failed: ${validation.errors.join(', ')}`);
      }

      if (progressCallback) {
        progressCallback({ stage: 'extraction', progress: 20 });
      }

      // Phase 2: Content Extraction
      const extractionResult = await this.extractContent(file, {
        uploadId,
        progressCallback,
        language
      });

      if (progressCallback) {
        progressCallback({ stage: 'analysis', progress: 60 });
      }

      // Phase 3: Document Analysis
      const analysisResult = await this.analyzeDocument(extractionResult, {
        uploadId,
        enableAI,
        enableRAG,
        apiKey,
        language,
        template,
        companyInfo,
        progressCallback
      });

      if (progressCallback) {
        progressCallback({ stage: 'finalizing', progress: 90 });
      }

      // Phase 4: Finalize Results
      const finalResult = await this.finalizeProcessing({
        file,
        uploadId,
        extractionResult,
        analysisResult,
        startTime
      });

      if (progressCallback) {
        progressCallback({ stage: 'complete', progress: 100 });
      }

      // Complete upload tracking
      uploadTracker.completeUpload(uploadId, finalResult);

      // Log processing completion
      const totalTime = performance.now() - startTime;
      processingLogger.logProcessingComplete(uploadId, finalResult, totalTime);

      // Record analytics event
      await analyticsService.recordEvent(ANALYTICS_EVENTS.DOCUMENT_PROCESSED, {
        success: true,
        processingTime: totalTime,
        documentType: this.detectDocumentType(finalResult),
        extractedData: finalResult.analysisMetadata,
        confidence: finalResult.confidence,
        method: finalResult.extractionMethod
      });

      return {
        success: true,
        data: finalResult,
        uploadId,
        processingTimeMs: totalTime
      };

    } catch (error) {
      console.error('❌ Document processing failed:', error);

      // Log processing error
      processingLogger.logProcessingError(uploadId, 'document_processing', error);

      // Mark upload as failed
      uploadTracker.failUpload(uploadId, error.message);

      // Record analytics event for failure
      const totalTime = performance.now() - startTime;
      await analyticsService.recordEvent(ANALYTICS_EVENTS.DOCUMENT_PROCESSED, {
        success: false,
        processingTime: totalTime,
        error: error.message,
        documentType: file.type
      });

      return {
        success: false,
        error: error.message,
        uploadId,
        data: null
      };
    }
  }

  /**
   * Process document using the new multi-step pipeline
   * @param {File} file - Document file to process
   * @param {Object} options - Processing options
   * @returns {Promise<Object>} - Processing result
   */
  async processDocumentWithPipeline(file, options = {}) {
    try {
      console.log('🚀 Processing document with multi-step pipeline...');

      const {
        progressCallback = null,
        apiKey = null,
        language = 'pol',
        companyInfo = null
      } = options;

      // Process through the new pipeline
      const pipelineResult = await documentProcessingPipeline.processDocument(file, {
        progressCallback,
        apiKey,
        language,
        companyInfo
      });

      if (pipelineResult.success) {
        console.log(`✅ Multi-step pipeline completed with ${pipelineResult.finalResult.accuracyScore}% accuracy`);

        // Record analytics event
        await analyticsService.recordEvent(ANALYTICS_EVENTS.DOCUMENT_PROCESSED, {
          success: true,
          processingTime: pipelineResult.processingTimeMs,
          documentType: pipelineResult.finalResult.documentType,
          extractedData: pipelineResult.finalResult,
          confidence: pipelineResult.finalResult.confidence,
          method: 'multi_step_pipeline',
          accuracyScore: pipelineResult.finalResult.accuracyScore,
          pipelineSteps: pipelineResult.finalResult.pipelineSteps
        });

        return {
          success: true,
          data: pipelineResult.finalResult,
          uploadId: pipelineResult.uploadId,
          processingTimeMs: pipelineResult.processingTimeMs,
          stepResults: pipelineResult.stepResults,
          stepTimings: pipelineResult.stepTimings
        };
      }
      throw new Error(pipelineResult.error || 'Multi-step pipeline failed');


    } catch (error) {
      console.error('❌ Multi-step pipeline processing failed:', error);

      // Record analytics event for failure
      await analyticsService.recordEvent(ANALYTICS_EVENTS.DOCUMENT_PROCESSED, {
        success: false,
        error: error.message,
        method: 'multi_step_pipeline'
      });

      return {
        success: false,
        error: error.message,
        data: null
      };
    }
  }

  /**
   * Validate file using consolidated validation service
   * @param {File} file - File to validate
   * @returns {Promise<Object>} - Validation result
   */
  async validateFile(file) {
    try {
      return await consolidatedFileValidationService.validateFile(file, {
        maxFileSize: this.config.maxFileSize,
        allowedMimeTypes: this.config.supportedTypes,
        enableSecurityScanning: true,
        enableContentValidation: true
      });
    } catch (error) {
      console.error('File validation error:', error);
      return {
        isValid: false,
        errors: [`Validation error: ${error.message}`],
        warnings: []
      };
    }
  }

  /**
   * Extract content from file using appropriate specialized service
   * @param {File} file - File to extract content from
   * @param {Object} options - Extraction options
   * @returns {Promise<Object>} - Extraction result
   */
  async extractContent(file, options = {}) {
    const { uploadId, progressCallback, language = 'pol' } = options;

    try {
      if (file.type === 'application/pdf') {
        // Use PDFProcessingService for PDF files
        console.log('📄 Processing PDF file with PDFProcessingService...');

        const result = await this.pdfService.extractText(file, {
          onProgress: progressCallback,
          trackingId: uploadId,
          enableOCRFallback: true
        });

        return {
          text: result.text,
          method: 'pdf_extraction',
          metadata: result.metadata || {},
          pages: result.pages || 1,
          confidence: result.confidence || 100
        };

      } else if (file.type.startsWith('image/')) {
        // Use OCRProcessor for image files
        console.log('🖼️ Processing image file with OCRProcessor...');

        const text = await this.ocrProcessor.performOCR(file, `${language}+eng`, {
          uploadId,
          progressCallback,
          preprocessImage: true,
          confidence: 60
        });

        return {
          text,
          method: 'ocr_extraction',
          metadata: {},
          pages: 1,
          confidence: 80 // OCR typically has lower confidence
        };

      } else if (file.type === 'text/plain') {
        // Handle plain text files
        console.log('📝 Processing text file...');

        const text = await file.text();

        return {
          text,
          method: 'text_extraction',
          metadata: {},
          pages: 1,
          confidence: 100
        };

      }
      throw new Error(`Unsupported file type: ${file.type}`);


    } catch (error) {
      console.error('Content extraction failed:', error);
      throw new Error(`Content extraction failed: ${error.message}`);
    }
  }

  /**
   * Analyze document using DocumentAnalysisService
   * @param {Object} extractionResult - Content extraction result
   * @param {Object} options - Analysis options
   * @returns {Promise<Object>} - Analysis result
   */
  async analyzeDocument(extractionResult, options = {}) {
    const {
      uploadId,
      enableAI,
      enableRAG,
      apiKey,
      language,
      template,
      companyInfo,
      progressCallback
    } = options;

    try {
      if (!extractionResult.text || extractionResult.text.trim().length < 10) {
        console.warn('⚠️ Insufficient text for analysis, skipping...');
        return {
          metadata: {},
          analysis: null,
          confidence: 0
        };
      }

      console.log('🔍 Analyzing document with DocumentAnalysisService...');

      // Use DocumentAnalysisService for comprehensive analysis
      const analysisResult = await this.analysisService.analyzeDocument(
        extractionResult.text,
        {
          enableAI,
          enableRAG,
          apiKey,
          language,
          template,
          companyInfo,
          uploadId,
          progressCallback
        }
      );

      return {
        metadata: analysisResult.metadata || {},
        analysis: analysisResult.analysis || null,
        confidence: analysisResult.confidence || extractionResult.confidence,
        enhancedData: analysisResult.enhancedData || null
      };

    } catch (error) {
      console.error('Document analysis failed:', error);
      // Return basic analysis on failure
      return {
        metadata: {},
        analysis: null,
        confidence: extractionResult.confidence || 0,
        error: error.message
      };
    }
  }

  /**
   * Finalize processing results
   * @param {Object} params - Processing parameters
   * @returns {Object} - Final processing result
   */
  async finalizeProcessing(params) {
    const { file, uploadId, extractionResult, analysisResult, startTime } = params;
    const totalTime = performance.now() - startTime;

    return {
      id: `DOC-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      uploadId,
      filename: file.name,
      fileSize: file.size,
      fileType: file.type,
      processedAt: new Date().toISOString(),
      processingTimeMs: totalTime,

      // Extraction data
      extractedText: extractionResult.text,
      extractionMethod: extractionResult.method,
      extractionMetadata: extractionResult.metadata,
      pages: extractionResult.pages,

      // Analysis data
      analysisMetadata: analysisResult.metadata,
      analysisResult: analysisResult.analysis,
      enhancedData: analysisResult.enhancedData,

      // Quality metrics
      confidence: analysisResult.confidence,
      textLength: extractionResult.text.length,

      // Processing flags
      aiEnhanced: !!analysisResult.analysis,
      ragProcessed: !!analysisResult.enhancedData?.relationships
    };
  }

  /**
   * Detect document type from processing result
   * @param {Object} result - Processing result
   * @returns {string} - Document type
   */
  detectDocumentType(result) {
    if (!result.analysisMetadata) { return 'unknown'; }

    const text = result.extractedText?.toLowerCase() || '';
    const metadata = result.analysisMetadata;

    // Check for invoice indicators
    if (text.includes('invoice') || text.includes('faktura') ||
        text.includes('bill') || metadata.documentType === 'invoice') {
      return 'invoice';
    }

    // Check for receipt indicators
    if (text.includes('receipt') || text.includes('paragon') ||
        text.includes('kvittering')) {
      return 'receipt';
    }

    // Check for contract indicators
    if (text.includes('contract') || text.includes('umowa') ||
        text.includes('agreement')) {
      return 'contract';
    }

    return 'document';
  }

  /**
   * Get processing service status
   * @returns {Object} - Service status
   */
  getStatus() {
    return {
      initialized: this.initialized,
      services: {
        pdf: this.pdfService.initialized || false,
        ocr: this.ocrProcessor.initialized || false,
        analysis: true // DocumentAnalysisService doesn't have explicit initialization
      },
      config: this.config
    };
  }

  /**
   * Cleanup resources
   * @returns {Promise<void>}
   */
  async cleanup() {
    try {
      if (this.ocrProcessor && this.ocrProcessor.cleanup) {
        await this.ocrProcessor.cleanup();
      }

      this.initialized = false;
      console.log('✅ DocumentProcessingService cleanup completed');
    } catch (error) {
      console.error('❌ DocumentProcessingService cleanup failed:', error);
    }
  }
}

// Export singleton instance
export const documentProcessingService = new DocumentProcessingService();
