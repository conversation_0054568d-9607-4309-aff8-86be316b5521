/**
 * Enhanced Embedding Generation Service
 *
 * Advanced document embedding generation with multiple AI model support,
 * optimized caching, and comprehensive error handling for RAG-based analysis.
 *
 * <AUTHOR> Agent
 * @version 1.0.0
 * @since 2025-01-28
 */

import { ProcessingLogger } from '../utils/ProcessingLogger.js';
import { EmbeddingCache } from '../utils/EmbeddingCache.js';
import { normalizeVector, validateVector } from '../utils/EmbeddingUtils.js';

export class EmbeddingGenerationService {
  constructor(options = {}) {
    this.logger = new ProcessingLogger('EmbeddingGenerationService');
    this.cache = new EmbeddingCache(options.cacheOptions);

    // Configuration
    this.config = {
      defaultModel: options.defaultModel || 'deepseek-embedding',
      maxRetries: options.maxRetries || 3,
      retryDelay: options.retryDelay || 1000,
      timeout: options.timeout || 30000,
      maxTextLength: options.maxTextLength || 8000,
      embeddingDimension: options.embeddingDimension || 1536,
      ...options
    };

    // Supported embedding models
    this.models = {
      'deepseek-embedding': {
        apiUrl: 'https://api.deepseek.com/v1/embeddings',
        maxTokens: 8192,
        dimension: 1536,
        costPerToken: 0.00002
      },
      'openai-ada-002': {
        apiUrl: 'https://api.openai.com/v1/embeddings',
        maxTokens: 8191,
        dimension: 1536,
        costPerToken: 0.0001
      },
      'local-tfidf': {
        dimension: 1536,
        costPerToken: 0
      }
    };

    // Performance metrics
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      cacheHits: 0,
      totalProcessingTime: 0,
      averageProcessingTime: 0
    };
  }

  /**
   * Generate document embedding with multiple model support
   * @param {string} text - Document text content
   * @param {Object} options - Generation options
   * @returns {Promise<Object>} Enhanced embedding result
   */
  async generateEmbedding(text, options = {}) {
    const startTime = Date.now();
    const requestId = `emb_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      this.metrics.totalRequests++;

      this.logger.log('🧠 Starting enhanced embedding generation', {
        requestId,
        textLength: text.length,
        model: options.model || this.config.defaultModel,
        documentId: options.documentId,
        useCache: options.useCache !== false
      });

      // Validate input
      this.validateInput(text, options);

      // Check cache first
      if (options.useCache !== false) {
        const cached = await this.cache.get(text, options);
        if (cached) {
          this.metrics.cacheHits++;
          this.logger.log('📋 Using cached embedding', { requestId, cacheKey: cached.cacheKey });
          return this.enhanceEmbeddingResult(cached, { fromCache: true, requestId });
        }
      }

      // Generate embedding based on model
      const model = options.model || this.config.defaultModel;
      let embedding;

      if (this.models[model]) {
        embedding = await this.generateWithModel(text, model, options, requestId);
      } else {
        throw new Error(`Unsupported embedding model: ${model}`);
      }

      // Cache the result
      if (options.useCache !== false) {
        await this.cache.set(text, options, embedding);
      }

      // Update metrics
      const processingTime = Date.now() - startTime;
      this.updateMetrics(true, processingTime);

      this.logger.log('✅ Enhanced embedding generated successfully', {
        requestId,
        model,
        dimension: embedding.vector.length,
        confidence: embedding.confidence,
        processingTime
      });

      return this.enhanceEmbeddingResult(embedding, { requestId, processingTime });

    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.updateMetrics(false, processingTime);

      this.logger.error('❌ Enhanced embedding generation failed', {
        requestId,
        error: error.message,
        processingTime
      });

      // Return fallback embedding
      return this.generateFallbackEmbedding(text, options, requestId);
    }
  }

  /**
   * Generate embedding with specific model
   * @private
   */
  async generateWithModel(text, model, options, requestId) {
    const modelConfig = this.models[model];

    if (model === 'local-tfidf') {
      return this.generateLocalEmbedding(text, options, requestId);
    }

    // API-based models
    return this.generateAPIEmbedding(text, model, modelConfig, options, requestId);
  }

  /**
   * Generate embedding using API
   * @private
   */
  async generateAPIEmbedding(text, model, modelConfig, options, requestId) {
    const apiKey = options.apiKey || this.getAPIKey(model);
    if (!apiKey) {
      throw new Error(`API key required for model: ${model}`);
    }

    // Prepare text for API
    const processedText = this.preprocessText(text, modelConfig.maxTokens);

    const requestBody = {
      model: model,
      input: processedText,
      encoding_format: 'float'
    };

    // Add model-specific parameters
    if (options.dimensions && model === 'openai-ada-002') {
      requestBody.dimensions = options.dimensions;
    }

    let attempt = 0;
    while (attempt < this.config.maxRetries) {
      try {
        const response = await this.makeAPIRequest(modelConfig.apiUrl, apiKey, requestBody, requestId);

        if (!response.data || !response.data[0] || !response.data[0].embedding) {
          throw new Error('Invalid embedding response from API');
        }

        const vector = response.data[0].embedding;

        // Validate and normalize vector
        if (!validateVector(vector)) {
          throw new Error('Invalid vector received from API');
        }

        return {
          vector: normalizeVector(vector),
          model,
          method: 'api',
          confidence: this.calculateAPIConfidence(model, vector),
          metadata: {
            requestId,
            model,
            apiProvider: this.getAPIProvider(model),
            textLength: text.length,
            processedTextLength: processedText.length,
            dimension: vector.length,
            timestamp: new Date().toISOString(),
            usage: response.usage || {},
            options
          }
        };

      } catch (error) {
        attempt++;
        if (attempt >= this.config.maxRetries) {
          throw error;
        }

        this.logger.warn(`⚠️ API request failed, retrying (${attempt}/${this.config.maxRetries})`, {
          requestId,
          error: error.message,
          attempt
        });

        await this.delay(this.config.retryDelay * attempt);
      }
    }
  }

  /**
   * Generate local TF-IDF based embedding
   * @private
   */
  generateLocalEmbedding(text, options, requestId) {
    const keywords = this.extractKeywords(text);
    const vector = this.createTFIDFVector(keywords, text);

    return {
      vector: normalizeVector(vector),
      model: 'local-tfidf',
      method: 'local',
      confidence: 0.75,
      metadata: {
        requestId,
        model: 'local-tfidf',
        textLength: text.length,
        keywordCount: keywords.length,
        dimension: vector.length,
        timestamp: new Date().toISOString(),
        options
      }
    };
  }

  /**
   * Generate fallback embedding for error cases
   * @private
   */
  generateFallbackEmbedding(text, options, requestId) {
    const vector = this.createHashBasedVector(text);

    return {
      vector: normalizeVector(vector),
      model: 'fallback',
      method: 'fallback',
      confidence: 0.3,
      metadata: {
        requestId,
        model: 'fallback',
        textLength: text.length,
        dimension: vector.length,
        timestamp: new Date().toISOString(),
        fallback: true,
        options
      }
    };
  }

  /**
   * Make API request with timeout and error handling
   * @private
   */
  async makeAPIRequest(url, apiKey, body, requestId) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`,
          'User-Agent': 'MVAT-Chrome-Extension/1.0'
        },
        body: JSON.stringify(body),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API error: ${response.status} ${response.statusText} - ${errorText}`);
      }

      return await response.json();

    } catch (error) {
      clearTimeout(timeoutId);

      if (error.name === 'AbortError') {
        throw new Error(`API request timeout after ${this.config.timeout}ms`);
      }

      throw error;
    }
  }

  /**
   * Validate input parameters
   * @private
   */
  validateInput(text, options) {
    if (!text || typeof text !== 'string') {
      throw new Error('Text must be a non-empty string');
    }

    if (text.trim().length < 10) {
      throw new Error('Text too short for meaningful embedding generation');
    }

    if (text.length > this.config.maxTextLength * 4) {
      throw new Error(`Text too long (max: ${this.config.maxTextLength * 4} characters)`);
    }

    if (options.model && !this.models[options.model]) {
      throw new Error(`Unsupported model: ${options.model}`);
    }
  }

  /**
   * Preprocess text for API consumption
   * @private
   */
  preprocessText(text, maxTokens) {
    // Simple token estimation (rough approximation)
    const estimatedTokens = text.length / 4;

    if (estimatedTokens <= maxTokens) {
      return text;
    }

    // Truncate text intelligently
    const maxChars = maxTokens * 3.5; // Conservative estimate
    const truncated = text.substring(0, maxChars);

    // Try to end at a sentence boundary
    const lastSentence = truncated.lastIndexOf('.');
    if (lastSentence > maxChars * 0.8) {
      return truncated.substring(0, lastSentence + 1);
    }

    return truncated + '...';
  }

  /**
   * Extract keywords for local embedding
   * @private
   */
  extractKeywords(text) {
    const stopWords = new Set([
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
      'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did',
      'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that',
      'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them'
    ]);

    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2 && !stopWords.has(word))
      .slice(0, 200); // Limit keywords for performance
  }

  /**
   * Create TF-IDF style vector
   * @private
   */
  createTFIDFVector(keywords, text) {
    const vector = new Array(this.config.embeddingDimension).fill(0);
    const textLower = text.toLowerCase();

    keywords.forEach((keyword, index) => {
      const frequency = (textLower.match(new RegExp(keyword, 'g')) || []).length;
      const tf = frequency / keywords.length;

      // Simple hash-based positioning
      const positions = this.getVectorPositions(keyword, 5);
      positions.forEach(pos => {
        vector[pos] += tf;
      });
    });

    return vector;
  }

  /**
   * Create hash-based vector for fallback
   * @private
   */
  createHashBasedVector(text) {
    const vector = new Array(this.config.embeddingDimension).fill(0);
    const hash = this.simpleHash(text);

    for (let i = 0; i < Math.min(100, this.config.embeddingDimension); i++) {
      vector[i] = ((hash + i * 7) % 1000) / 1000 - 0.5;
    }

    return vector;
  }

  /**
   * Get vector positions for keyword
   * @private
   */
  getVectorPositions(keyword, count = 5) {
    const positions = [];
    let hash = this.simpleHash(keyword);

    for (let i = 0; i < count; i++) {
      positions.push(Math.abs(hash + i * 17) % this.config.embeddingDimension);
    }

    return positions;
  }

  /**
   * Simple hash function
   * @private
   */
  simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash);
  }

  /**
   * Calculate confidence for API embeddings
   * @private
   */
  calculateAPIConfidence(model, vector) {
    const baseConfidence = {
      'deepseek-embedding': 0.95,
      'openai-ada-002': 0.98
    }[model] || 0.85;

    // Adjust based on vector properties
    const magnitude = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0));
    const magnitudeBonus = magnitude > 0.1 ? 0.02 : -0.05;

    return Math.min(baseConfidence + magnitudeBonus, 1.0);
  }

  /**
   * Get API provider for model
   * @private
   */
  getAPIProvider(model) {
    if (model.startsWith('deepseek')) { return 'deepseek'; }
    if (model.startsWith('openai')) { return 'openai'; }
    return 'unknown';
  }

  /**
   * Get API key for model
   * @private
   */
  getAPIKey(model) {
    const provider = this.getAPIProvider(model);

    // Try to get from environment or options
    if (typeof window !== 'undefined' && window.__MVAT_ENV__) {
      if (provider === 'deepseek') { return window.__MVAT_ENV__.DEEPSEEK_API_KEY; }
      if (provider === 'openai') { return window.__MVAT_ENV__.OPENAI_API_KEY; }
    }

    return null;
  }

  /**
   * Enhance embedding result with additional metadata
   * @private
   */
  enhanceEmbeddingResult(embedding, additionalData = {}) {
    return {
      ...embedding,
      enhanced: true,
      generatedAt: new Date().toISOString(),
      serviceVersion: '1.0.0',
      ...additionalData
    };
  }

  /**
   * Update performance metrics
   * @private
   */
  updateMetrics(success, processingTime) {
    if (success) {
      this.metrics.successfulRequests++;
    } else {
      this.metrics.failedRequests++;
    }

    this.metrics.totalProcessingTime += processingTime;
    this.metrics.averageProcessingTime = this.metrics.totalProcessingTime / this.metrics.totalRequests;
  }

  /**
   * Delay utility for retries
   * @private
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get performance metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      successRate: this.metrics.totalRequests > 0
        ? this.metrics.successfulRequests / this.metrics.totalRequests
        : 0,
      cacheHitRate: this.metrics.totalRequests > 0
        ? this.metrics.cacheHits / this.metrics.totalRequests
        : 0
    };
  }

  /**
   * Get supported models
   */
  getSupportedModels() {
    return Object.keys(this.models);
  }

  /**
   * Clear cache
   */
  async clearCache() {
    await this.cache.clear();
    this.logger.log('🧹 Embedding cache cleared');
  }

  /**
   * Get configuration
   */
  getConfiguration() {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  updateConfiguration(newConfig) {
    this.config = { ...this.config, ...newConfig };
    this.logger.log('⚙️ Configuration updated', newConfig);
  }
}
