/**
 * Document Embedding Service
 *
 * Generates document embeddings for similarity analysis and RAG capabilities.
 * Uses DeepSeek API for embedding generation with fallback to local text analysis.
 *
 * <AUTHOR> Agent
 * @version 1.0.0
 * @since 2025-06-03
 */

import { ProcessingLogger } from '../utils/ProcessingLogger.js';
import { EmbeddingGenerationService } from './EmbeddingGenerationService.js';

export class DocumentEmbeddingService {
  constructor(options = {}) {
    this.logger = new ProcessingLogger('DocumentEmbeddingService');
    this.embeddingCache = new Map();
    this.maxCacheSize = 1000;
    this.embeddingDimension = 1536; // Standard embedding dimension

    // Initialize enhanced embedding generation service
    this.embeddingGenerator = new EmbeddingGenerationService({
      defaultModel: options.defaultModel || 'deepseek-embedding',
      maxRetries: options.maxRetries || 3,
      timeout: options.timeout || 30000,
      embeddingDimension: this.embeddingDimension,
      cacheOptions: {
        maxSize: options.cacheMaxSize || 1000,
        ttlMinutes: options.cacheTtlMinutes || 60 * 24
      }
    });
  }

  /**
   * Generate document embedding using enhanced embedding generation service
   * @param {string} text - Document text content
   * @param {string} apiKey - DeepSeek API key (optional, will use environment if not provided)
   * @param {string} documentId - Unique document identifier
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Enhanced embedding result with vector and metadata
   */
  async generateEmbedding(text, apiKey, documentId, options = {}) {
    const startTime = Date.now();

    try {
      this.logger.log('🧠 Starting enhanced document embedding generation', {
        documentId,
        textLength: text.length,
        hasApiKey: !!apiKey,
        options
      });

      // Prepare options for enhanced embedding generation
      const embeddingOptions = {
        documentId,
        documentType: options.documentType,
        model: options.model || 'deepseek-embedding',
        apiKey: apiKey,
        useCache: options.useCache !== false,
        ...options
      };

      // Use enhanced embedding generation service
      const embedding = await this.embeddingGenerator.generateEmbedding(text, embeddingOptions);

      // Add backward compatibility metadata
      const enhancedEmbedding = {
        ...embedding,
        documentId,
        legacyCompatible: true,
        generatedAt: new Date().toISOString()
      };

      const processingTime = Date.now() - startTime;
      this.logger.log('✅ Enhanced document embedding generated successfully', {
        documentId,
        embeddingDimension: embedding.vector.length,
        processingTime,
        method: embedding.method,
        confidence: embedding.confidence,
        enhanced: true
      });

      return enhancedEmbedding;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.logger.error('❌ Enhanced document embedding generation failed', {
        documentId,
        error: error.message,
        processingTime
      });

      // Return fallback embedding using legacy method
      return this.generateFallbackEmbedding(text, documentId);
    }
  }

  /**
   * Generate embedding using DeepSeek API
   * @private
   */
  async generateDeepSeekEmbedding(text, apiKey, documentId, options) {
    const embeddingPrompt = this.buildEmbeddingPrompt(text, options);

    const response = await fetch('https://api.deepseek.com/v1/embeddings', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: 'deepseek-embedding',
        input: embeddingPrompt,
        encoding_format: 'float'
      })
    });

    if (!response.ok) {
      throw new Error(`DeepSeek API error: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();

    if (!result.data || !result.data[0] || !result.data[0].embedding) {
      throw new Error('Invalid embedding response from DeepSeek API');
    }

    return {
      vector: result.data[0].embedding,
      method: 'deepseek-api',
      confidence: 0.95,
      metadata: {
        documentId,
        textLength: text.length,
        model: 'deepseek-embedding',
        timestamp: new Date().toISOString(),
        options
      }
    };
  }

  /**
   * Generate local embedding using text analysis
   * @private
   */
  async generateLocalEmbedding(text, documentId, options) {
    // Simple TF-IDF style embedding for local processing
    const words = this.extractKeywords(text);
    const vector = this.createTextVector(words, text);

    return {
      vector,
      method: 'local-analysis',
      confidence: 0.75,
      metadata: {
        documentId,
        textLength: text.length,
        keywordCount: words.length,
        timestamp: new Date().toISOString(),
        options
      }
    };
  }

  /**
   * Generate fallback embedding for error cases
   * @private
   */
  generateFallbackEmbedding(text, documentId) {
    const vector = new Array(this.embeddingDimension).fill(0);

    // Simple hash-based vector for basic similarity
    const hash = this.simpleHash(text);
    for (let i = 0; i < Math.min(100, this.embeddingDimension); i++) {
      vector[i] = ((hash + i) % 1000) / 1000 - 0.5;
    }

    return {
      vector,
      method: 'fallback',
      confidence: 0.3,
      metadata: {
        documentId,
        textLength: text.length,
        timestamp: new Date().toISOString(),
        fallback: true
      }
    };
  }

  /**
   * Build embedding prompt for DeepSeek API
   * @private
   */
  buildEmbeddingPrompt(text, options) {
    const maxLength = 8000; // DeepSeek API limit
    let prompt = text;

    // Truncate if too long
    if (prompt.length > maxLength) {
      prompt = prompt.substring(0, maxLength - 100) + '...';
    }

    // Add context if provided
    if (options.documentType) {
      prompt = `Document Type: ${options.documentType}\n\n${prompt}`;
    }

    return prompt;
  }

  /**
   * Extract keywords from text for local embedding
   * @private
   */
  extractKeywords(text) {
    const stopWords = new Set([
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should'
    ]);

    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2 && !stopWords.has(word))
      .slice(0, 100); // Limit keywords
  }

  /**
   * Create vector from text analysis
   * @private
   */
  createTextVector(keywords, text) {
    const vector = new Array(this.embeddingDimension).fill(0);

    // Simple frequency-based vector
    keywords.forEach((keyword, index) => {
      const frequency = (text.match(new RegExp(keyword, 'gi')) || []).length;
      const position = (keyword.charCodeAt(0) + index) % this.embeddingDimension;
      vector[position] = Math.min(frequency / 10, 1);
    });

    // Normalize vector
    const magnitude = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0));
    if (magnitude > 0) {
      for (let i = 0; i < vector.length; i++) {
        vector[i] /= magnitude;
      }
    }

    return vector;
  }

  /**
   * Generate cache key for embedding
   * @private
   */
  getCacheKey(text, options) {
    const textHash = this.simpleHash(text);
    const optionsHash = this.simpleHash(JSON.stringify(options));
    return `${textHash}_${optionsHash}`;
  }

  /**
   * Cache embedding result
   * @private
   */
  cacheEmbedding(key, embedding) {
    if (this.embeddingCache.size >= this.maxCacheSize) {
      // Remove oldest entry
      const firstKey = this.embeddingCache.keys().next().value;
      this.embeddingCache.delete(firstKey);
    }
    this.embeddingCache.set(key, embedding);
  }

  /**
   * Simple hash function for text
   * @private
   */
  simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  /**
   * Clear embedding cache
   */
  clearCache() {
    this.embeddingCache.clear();
    this.logger.log('🧹 Embedding cache cleared');
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return {
      size: this.embeddingCache.size,
      maxSize: this.maxCacheSize,
      hitRate: this.cacheHits / (this.cacheHits + this.cacheMisses) || 0
    };
  }
}
