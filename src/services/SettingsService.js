import { encryptionService } from './EncryptionService.js';
import { apiValidationService } from './ApiValidationService.js';
import { environmentConfig } from './EnvironmentConfigService.js';

/**
 * Settings Service
 * Manages application settings with secure API key storage
 */
export class SettingsService {
  constructor() {
    this.environmentConfigInitialized = false;
    this.defaultSettings = {
      company: {
        name: '',
        taxId: '',
        address: '',
        email: '',
        phone: '',
        logo: ''
      },
      display: {
        groupBy: 'month',
        dateFormat: 'DD/MM/YYYY',
        currency: 'PLN',
        language: 'pl',
        theme: 'light'
      },
      processing: {
        ocrLanguage: 'pol',
        aiProvider: 'deepseek',
        autoProcess: true,
        cacheEnabled: true
      },
      apiKeys: {
        deepseek: '',
        openai: '',
        fakturownia: '',
        infakt: ''
      }
    };
  }

  /**
   * Initialize environment configuration
   * @returns {Promise<void>}
   */
  async initializeEnvironmentConfig() {
    if (!this.environmentConfigInitialized) {
      try {
        await environmentConfig.initialize();
        this.environmentConfigInitialized = true;
        console.log('✅ Environment configuration initialized');
      } catch (error) {
        console.warn('⚠️ Environment configuration initialization failed:', error);
        // Continue without environment config
      }
    }
  }

  /**
   * Load settings from storage
   * @returns {Promise<Object>} - Settings object
   */
  async loadSettings() {
    try {
      // Initialize environment configuration first
      await this.initializeEnvironmentConfig();

      let settings = { ...this.defaultSettings };

      if (chrome?.storage?.local) {
        const result = await chrome.storage.local.get(['settings', 'encryptedApiKeys']);

        // Load regular settings
        if (result.settings) {
          settings = this.mergeWithDefaults(result.settings);
        }

        // Load and decrypt API keys
        if (result.encryptedApiKeys) {
          try {
            const decryptedKeys = await encryptionService.decryptApiKeys(result.encryptedApiKeys);
            settings.apiKeys = { ...settings.apiKeys, ...decryptedKeys };
          } catch (error) {
            console.warn('⚠️ Failed to decrypt API keys:', error.message);
            // Keep default empty API keys if decryption fails
          }
        }
      } else {
        // Fallback for development/testing
        const stored = localStorage.getItem('mvat_settings');
        if (stored) {
          const loadedSettings = JSON.parse(stored);
          settings = this.mergeWithDefaults(loadedSettings);
        }
      }

      // IMPORTANT: Merge with environment configuration AFTER loading from storage
      // This ensures environment variables take precedence when user hasn't set values
      if (environmentConfig.isLoaded) {
        settings = this.mergeWithEnvironmentConfig(settings);
        console.log('✅ SettingsService: Environment configuration merged into settings');
      } else {
        console.warn('⚠️ SettingsService: Environment configuration not available for merging');
      }

      return settings;
    } catch (error) {
      console.error('❌ Failed to load settings:', error);
      return this.defaultSettings;
    }
  }

  /**
   * Save settings to storage
   * @param {Object} settings - Settings to save
   * @returns {Promise<void>}
   */
  async saveSettings(settings) {
    try {
      // Separate API keys from other settings
      const { apiKeys, ...regularSettings } = settings;

      if (chrome?.storage?.local) {
        // Save regular settings
        await chrome.storage.local.set({ settings: regularSettings });

        // Encrypt and save API keys separately
        if (apiKeys && Object.keys(apiKeys).length > 0) {
          const encryptedKeys = await encryptionService.encryptApiKeys(apiKeys);
          await chrome.storage.local.set({ encryptedApiKeys: encryptedKeys });
        }
      } else {
        // Fallback for development/testing (WARNING: API keys not encrypted)
        localStorage.setItem('mvat_settings', JSON.stringify(settings));
      }

      console.log('💾 Settings saved successfully');
    } catch (error) {
      console.error('❌ Failed to save settings:', error);
      throw error;
    }
  }

  /**
   * Update specific setting section
   * @param {string} section - Section name (company, display, processing, apiKeys)
   * @param {Object} updates - Updates to apply
   * @returns {Promise<Object>} - Updated settings
   */
  async updateSection(section, updates) {
    try {
      const currentSettings = await this.loadSettings();

      const newSettings = {
        ...currentSettings,
        [section]: { ...currentSettings[section], ...updates }
      };

      await this.saveSettings(newSettings);
      return newSettings;
    } catch (error) {
      console.error(`❌ Failed to update ${section} settings:`, error);
      throw error;
    }
  }

  /**
   * Update API keys with validation
   * @param {Object} apiKeys - API keys to update
   * @param {boolean} validateConnections - Whether to test connections
   * @returns {Promise<Object>} - Update result with validation
   */
  async updateApiKeys(apiKeys, validateConnections = false) {
    try {
      const result = {
        success: true,
        validationResults: null,
        errors: []
      };

      // Validate key formats
      for (const [provider, key] of Object.entries(apiKeys)) {
        if (key && key.trim()) {
          const validation = apiValidationService.validateKeyFormat(provider, key);
          if (!validation.valid) {
            result.errors.push(`${provider}: ${validation.error}`);
          }
        }
      }

      // If format validation failed, don't save
      if (result.errors.length > 0) {
        result.success = false;
        return result;
      }

      // Test connections if requested
      if (validateConnections) {
        console.log('🔌 Testing API connections...');
        const connectionTests = await apiValidationService.testAllConnections(apiKeys);
        result.validationResults = connectionTests;

        // Log results but don't block saving
        const { successful, failed } = connectionTests.summary;
        console.log(`📊 Connection tests: ${successful} successful, ${failed} failed`);
      }

      // Save the API keys
      await this.updateSection('apiKeys', apiKeys);

      return result;
    } catch (error) {
      console.error('❌ Failed to update API keys:', error);
      throw error;
    }
  }

  /**
   * Clear all API keys
   * @returns {Promise<void>}
   */
  async clearApiKeys() {
    try {
      const emptyKeys = {
        deepseek: '',
        openai: '',
        fakturownia: '',
        infakt: ''
      };

      await this.updateSection('apiKeys', emptyKeys);

      // Also clear encrypted storage
      if (chrome?.storage?.local) {
        await chrome.storage.local.remove(['encryptedApiKeys']);
      }

      console.log('🧹 API keys cleared');
    } catch (error) {
      console.error('❌ Failed to clear API keys:', error);
      throw error;
    }
  }

  /**
   * Reset all settings to defaults
   * @returns {Promise<void>}
   */
  async resetToDefaults() {
    try {
      await this.saveSettings(this.defaultSettings);

      // Clear encryption key to force regeneration
      await encryptionService.clearEncryptionKey();

      console.log('🔄 Settings reset to defaults');
    } catch (error) {
      console.error('❌ Failed to reset settings:', error);
      throw error;
    }
  }

  /**
   * Export settings (without API keys for security)
   * @returns {Promise<Object>} - Exportable settings
   */
  async exportSettings() {
    try {
      const settings = await this.loadSettings();

      // Remove API keys from export for security
      const { apiKeys, ...exportableSettings } = settings;

      return {
        ...exportableSettings,
        exportedAt: new Date().toISOString(),
        version: '1.0'
      };
    } catch (error) {
      console.error('❌ Failed to export settings:', error);
      throw error;
    }
  }

  /**
   * Import settings (preserves existing API keys)
   * @param {Object} importedSettings - Settings to import
   * @returns {Promise<Object>} - Import result
   */
  async importSettings(importedSettings) {
    try {
      const currentSettings = await this.loadSettings();

      // Merge imported settings but preserve API keys
      const newSettings = {
        ...this.mergeWithDefaults(importedSettings),
        apiKeys: currentSettings.apiKeys // Preserve existing API keys
      };

      await this.saveSettings(newSettings);

      return {
        success: true,
        message: 'Settings imported successfully (API keys preserved)'
      };
    } catch (error) {
      console.error('❌ Failed to import settings:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Merge settings with environment configuration
   * @param {Object} settings - Settings to merge
   * @returns {Object} - Merged settings
   */
  mergeWithEnvironmentConfig(settings) {
    if (!environmentConfig.isLoaded) {
      console.warn('⚠️ EnvironmentConfig not loaded, skipping merge');
      return settings;
    }

    try {
      const companyInfo = environmentConfig.getCompanyInfo();
      const apiKeys = environmentConfig.get('apiKeys', {});

      console.log('🔧 SettingsService: Merging environment configuration');
      console.log('📋 Company info available:', !!companyInfo?.name);
      console.log('🔑 API keys available:', Object.keys(apiKeys).length);

      return {
        ...settings,
        company: {
          ...settings.company,
          name: companyInfo?.name || settings.company.name,
          taxId: companyInfo?.nip || settings.company.taxId,
          address: companyInfo?.address ?
            `${companyInfo.address.street || ''}, ${companyInfo.address.city || ''} ${companyInfo.address.postalCode || ''}`.trim() :
            settings.company.address,
          email: companyInfo?.contact?.email || settings.company.email,
          phone: companyInfo?.contact?.phone || settings.company.phone,
          logo: companyInfo?.branding?.logoUrl || settings.company.logo
        },
        display: {
          ...settings.display,
          currency: environmentConfig.get('localization.defaultCurrency', settings.display.currency),
          language: environmentConfig.get('localization.defaultLanguage', settings.display.language),
          dateFormat: environmentConfig.get('localization.dateFormat', settings.display.dateFormat)
        },
        processing: {
          ...settings.processing,
          aiProvider: apiKeys.deepseek?.key ? 'deepseek' : settings.processing.aiProvider
        },
        apiKeys: {
          ...settings.apiKeys,
          deepseek: apiKeys.deepseek?.key || settings.apiKeys.deepseek,
          openai: apiKeys.openai?.key || settings.apiKeys.openai,
          fakturownia: apiKeys.fakturownia?.token || settings.apiKeys.fakturownia,
          infakt: apiKeys.infakt?.key || settings.apiKeys.infakt
        }
      };
    } catch (error) {
      console.error('❌ Failed to merge environment configuration:', error);
      return settings;
    }
  }

  /**
   * Merge settings with defaults to ensure all fields exist
   * @param {Object} settings - Settings to merge
   * @returns {Object} - Merged settings
   */
  mergeWithDefaults(settings) {
    return {
      company: { ...this.defaultSettings.company, ...settings.company },
      display: { ...this.defaultSettings.display, ...settings.display },
      processing: { ...this.defaultSettings.processing, ...settings.processing },
      apiKeys: { ...this.defaultSettings.apiKeys, ...settings.apiKeys }
    };
  }

  /**
   * Get API key for specific provider
   * @param {string} provider - Provider name
   * @returns {Promise<string>} - API key
   */
  async getApiKey(provider) {
    try {
      const settings = await this.loadSettings();
      return settings.apiKeys[provider] || '';
    } catch (error) {
      console.error(`❌ Failed to get ${provider} API key:`, error);
      return '';
    }
  }

  /**
   * Get default settings
   * @returns {Object} Default settings object
   */
  getDefaultSettings() {
    return { ...this.defaultSettings };
  }

  /**
   * Validate settings structure
   * @param {Object} settings - Settings to validate
   * @returns {Object} - Validation result
   */
  validateSettings(settings) {
    const errors = [];

    // Validate required sections
    const requiredSections = ['company', 'display', 'processing', 'apiKeys'];
    for (const section of requiredSections) {
      if (!settings[section] || typeof settings[section] !== 'object') {
        errors.push(`Missing or invalid ${section} section`);
      }
    }

    // Validate email format if provided
    if (settings.company?.email && settings.company.email.trim()) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(settings.company.email)) {
        errors.push('Invalid email format');
      }
    }

    // Validate currency
    const validCurrencies = ['PLN', 'EUR', 'USD'];
    if (settings.display?.currency && !validCurrencies.includes(settings.display.currency)) {
      errors.push('Invalid currency');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}

// Export singleton instance
export const settingsService = new SettingsService();
