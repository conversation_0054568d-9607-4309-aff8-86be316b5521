/**
 * OCRProcessingService - Enhanced OCR processing with Tesseract.js
 * Implements EPIC-002 Story 2.3 Task 2.3.1 - Tesseract.js Integration
 *
 * Features:
 * - Image-to-text conversion for JPG/PNG files
 * - Language detection (Polish/English focus)
 * - Image preprocessing for better accuracy
 * - Progress tracking and error handling
 * - Memory optimization for large images
 * - Confidence scoring for extracted text
 */

import { OCRProcessor } from '../components/features/documents/OCRProcessor.js';
import { imageUtils } from '../utils/imageUtils.js';
import { ocrUtils } from '../utils/ocrUtils.js';

// Note: Tesseract.js worker configuration is now handled in OCRProcessor
// using the correct createWorker API with workerPath options

export class OCRProcessingService {
  constructor() {
    this.ocrProcessor = new OCRProcessor();
    this.initialized = false;
    this.config = {
      defaultLanguage: 'pol+eng',
      minConfidence: 60,
      enablePreprocessing: true,
      maxImageSize: 10 * 1024 * 1024, // 10MB
      supportedFormats: ['image/jpeg', 'image/jpg', 'image/png'],
      ocrParameters: {
        tessedit_ocr_engine_mode: 1, // LSTM_ONLY
        tessedit_pageseg_mode: 3, // AUTO
        preserve_interword_spaces: 1,
        tessedit_char_whitelist: '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyzĄĆĘŁŃÓŚŹŻąćęłńóśźż.,:-/()[]{}€$£¥₹₽ '
      }
    };
  }

  /**
   * Initialize OCR processing service
   * @param {Object} options - Initialization options
   * @returns {Promise<void>}
   */
  async initialize(options = {}) {
    if (this.initialized) {
      return;
    }

    try {
      const { language = this.config.defaultLanguage } = options;

      console.log('🔧 Initializing OCR Processing Service...');

      // Initialize OCR processor
      await this.ocrProcessor.initialize(language);

      this.initialized = true;
      console.log('✅ OCR Processing Service initialized successfully');

    } catch (error) {
      console.error('❌ Failed to initialize OCR Processing Service:', error);
      throw new Error(`OCR service initialization failed: ${error.message}`);
    }
  }

  /**
   * Extract text from image file using OCR
   * @param {File} file - Image file to process
   * @param {Object} options - Processing options
   * @returns {Promise<Object>} - OCR result
   */
  async extractTextFromImage(file, options = {}) {
    await this.initialize();

    const {
      language = this.config.defaultLanguage,
      onProgress = null,
      enablePreprocessing = this.config.enablePreprocessing,
      minConfidence = this.config.minConfidence,
      trackingId = `ocr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    } = options;

    try {
      // Validate image file
      this.validateImageFile(file);

      if (onProgress) { onProgress({ stage: 'validation', progress: 5 }); }

      console.log(`🔍 Starting OCR processing for: ${file.name}`);
      console.log(`📊 File size: ${(file.size / 1024 / 1024).toFixed(2)}MB`);
      console.log(`🌐 Language: ${language}`);

      // Preprocess image if enabled
      let processedImage = file;
      if (enablePreprocessing) {
        if (onProgress) { onProgress({ stage: 'preprocessing', progress: 15 }); }
        processedImage = await this.preprocessImage(file);
      }

      if (onProgress) { onProgress({ stage: 'ocr_processing', progress: 30 }); }

      // Perform OCR with progress tracking
      const ocrResult = await this.ocrProcessor.performOCR(processedImage, language, {
        preprocessImage: false, // Already preprocessed
        confidence: minConfidence,
        progressCallback: (progress) => {
          if (onProgress) {
            const adjustedProgress = 30 + (progress * 0.6); // 30-90%
            onProgress({ stage: 'ocr_processing', progress: adjustedProgress });
          }
        }
      });

      if (onProgress) { onProgress({ stage: 'post_processing', progress: 95 }); }

      // Post-process and validate results
      const processedResult = await this.postProcessOCRResult(ocrResult, {
        language,
        minConfidence,
        trackingId
      });

      if (onProgress) { onProgress({ stage: 'complete', progress: 100 }); }

      console.log(`✅ OCR processing completed for: ${file.name}`);
      console.log(`📊 Extracted ${processedResult.text.length} characters`);
      console.log(`📊 Confidence: ${processedResult.confidence}%`);

      return {
        success: true,
        text: processedResult.text,
        confidence: processedResult.confidence,
        language: processedResult.detectedLanguage,
        metadata: {
          fileName: file.name,
          fileSize: file.size,
          processingTime: processedResult.processingTime,
          extractionMethod: 'ocr',
          ocrEngine: 'tesseract',
          trackingId,
          preprocessingApplied: enablePreprocessing
        }
      };

    } catch (error) {
      console.error(`❌ OCR processing failed for ${file.name}:`, error);

      return {
        success: false,
        text: '',
        confidence: 0,
        error: error.message,
        metadata: {
          fileName: file.name,
          fileSize: file.size,
          extractionMethod: 'ocr',
          trackingId,
          errorType: error.name || 'OCRError'
        }
      };
    }
  }

  /**
   * Validate image file for OCR processing
   * @param {File} file - Image file to validate
   * @throws {Error} - If validation fails
   */
  validateImageFile(file) {
    // Check file type
    if (!this.config.supportedFormats.includes(file.type)) {
      throw new Error(`Unsupported image format: ${file.type}. Supported formats: ${this.config.supportedFormats.join(', ')}`);
    }

    // Check file size
    if (file.size > this.config.maxImageSize) {
      throw new Error(`Image file too large: ${(file.size / 1024 / 1024).toFixed(2)}MB. Maximum size: ${(this.config.maxImageSize / 1024 / 1024)}MB`);
    }

    // Check file name
    if (!file.name || file.name.trim() === '') {
      throw new Error('Image file must have a valid name');
    }
  }

  /**
   * Preprocess image for better OCR accuracy
   * @param {File} file - Image file to preprocess
   * @returns {Promise<HTMLCanvasElement>} - Preprocessed image canvas
   */
  async preprocessImage(file) {
    try {
      console.log('🔧 Preprocessing image for OCR...');

      // Load image
      const image = await imageUtils.loadImageFromFile(file);

      // Apply preprocessing filters
      const canvas = imageUtils.createCanvasFromImage(image);
      const ctx = canvas.getContext('2d');

      // Get image data for processing
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

      // Apply enhancement filters
      imageUtils.enhanceContrast(imageData, 1.2);
      imageUtils.reduceNoise(imageData);
      imageUtils.sharpenImage(imageData);

      // Put processed data back
      ctx.putImageData(imageData, 0, 0);

      console.log('✅ Image preprocessing completed');
      return canvas;

    } catch (error) {
      console.error('❌ Image preprocessing failed:', error);
      // Return original file if preprocessing fails
      return file;
    }
  }

  /**
   * Post-process OCR result for better accuracy
   * @param {string} text - Raw OCR text
   * @param {Object} options - Processing options
   * @returns {Promise<Object>} - Processed result
   */
  async postProcessOCRResult(text, options = {}) {
    const startTime = Date.now();

    try {
      const {
        language = this.config.defaultLanguage,
        minConfidence = this.config.minConfidence,
        trackingId
      } = options;

      // Clean and normalize text
      let processedText = ocrUtils.cleanOCRText(text);

      // Detect language
      const detectedLanguage = ocrUtils.detectLanguage(processedText, language);

      // Calculate confidence score
      const confidence = ocrUtils.calculateConfidence(processedText);

      // Apply language-specific corrections
      if (detectedLanguage.includes('pol')) {
        processedText = ocrUtils.applyPolishCorrections(processedText);
      }

      const processingTime = Date.now() - startTime;

      return {
        text: processedText,
        confidence,
        detectedLanguage,
        processingTime
      };

    } catch (error) {
      console.error('❌ OCR post-processing failed:', error);
      return {
        text: text || '',
        confidence: 0,
        detectedLanguage: 'unknown',
        processingTime: Date.now() - startTime
      };
    }
  }

  /**
   * Process multiple images in batch
   * @param {Array<File>} files - Array of image files
   * @param {Object} options - Processing options
   * @returns {Promise<Array>} - Array of OCR results
   */
  async processBatch(files, options = {}) {
    const results = [];
    const { onProgress = null } = options;

    for (let i = 0; i < files.length; i++) {
      const file = files[i];

      if (onProgress) {
        onProgress({
          stage: 'batch_processing',
          progress: (i / files.length) * 100,
          currentFile: file.name,
          fileIndex: i + 1,
          totalFiles: files.length
        });
      }

      try {
        const result = await this.extractTextFromImage(file, options);
        results.push({
          file: file.name,
          index: i,
          ...result
        });
      } catch (error) {
        results.push({
          file: file.name,
          index: i,
          success: false,
          error: error.message
        });
      }
    }

    return results;
  }

  /**
   * Cleanup resources
   * @returns {Promise<void>}
   */
  async cleanup() {
    try {
      if (this.ocrProcessor) {
        await this.ocrProcessor.terminate();
      }
      this.initialized = false;
      console.log('✅ OCR Processing Service cleaned up');
    } catch (error) {
      console.error('❌ OCR cleanup failed:', error);
    }
  }
}

// Create singleton instance
const ocrProcessingService = new OCRProcessingService();

// Export for ES modules
export default ocrProcessingService;

// Export to window object for non-module scripts
if (typeof window !== 'undefined') {
  window.OCRProcessingService = OCRProcessingService;
  window.ocrProcessingService = ocrProcessingService;
}
