/**
 * Unified Progress Service
 * Consolidates all progress tracking implementations across the codebase
 *
 * CONSOLIDATION: Replaces multiple progress tracking systems:
 * - src/utils/ProgressTracker.js
 * - src/services/PDFProgressTracker.js
 * - src/utils/UploadTracker.js
 * - src/hooks/useUploadProgress.js (logic parts)
 */

export class UnifiedProgressService {
  constructor() {
    this.sessions = new Map(); // sessionId -> session data
    this.globalStats = {
      totalSessions: 0,
      completedSessions: 0,
      failedSessions: 0,
      averageProcessingTime: 0
    };
  }

  /**
   * Start a new progress tracking session
   * @param {Object} options - Session options
   * @returns {string} - Session ID
   */
  startSession(options = {}) {
    const {
      type = 'generic', // 'upload', 'pdf', 'processing', 'generic'
      name = 'Unknown Process',
      totalSteps = 100,
      stages = null,
      onProgress = null,
      onComplete = null,
      onError = null,
      metadata = {}
    } = options;

    const sessionId = this.generateSessionId();
    const timestamp = Date.now();

    // Default stages based on type
    const defaultStages = this.getDefaultStages(type);
    const sessionStages = stages || defaultStages;

    const session = {
      id: sessionId,
      type,
      name,
      totalSteps,
      stages: sessionStages,
      currentStage: 0,
      currentStageProgress: 0,
      overallProgress: 0,
      status: 'active',
      startTime: timestamp,
      endTime: null,
      errors: [],
      warnings: [],
      metadata: {
        ...metadata,
        timestamp,
        userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'Node.js'
      },
      callbacks: {
        onProgress,
        onComplete,
        onError
      }
    };

    this.sessions.set(sessionId, session);
    this.globalStats.totalSessions++;

    console.log(`🔄 Progress session started: ${sessionId} (${type})`);
    return sessionId;
  }

  /**
   * Update progress for a session
   * @param {string} sessionId - Session ID
   * @param {number} progress - Progress value (0-100)
   * @param {Object} options - Update options
   */
  updateProgress(sessionId, progress, options = {}) {
    const session = this.sessions.get(sessionId);
    if (!session || session.status !== 'active') {
      console.warn(`Session ${sessionId} not found or not active`);
      return;
    }

    const {
      stage = null,
      message = '',
      data = {}
    } = options;

    // Update stage if provided
    if (stage !== null && stage !== session.currentStage) {
      this.nextStage(sessionId, stage);
    }

    // Update progress
    const normalizedProgress = Math.max(0, Math.min(100, progress));
    session.currentStageProgress = normalizedProgress;
    session.overallProgress = this.calculateOverallProgress(session);

    // Add metadata
    if (message) {
      session.lastMessage = message;
    }
    if (Object.keys(data).length > 0) {
      session.lastData = data;
    }

    // Trigger progress callback
    this.triggerProgressCallback(sessionId);
  }

  /**
   * Move to next stage
   * @param {string} sessionId - Session ID
   * @param {number} stageIndex - Stage index (optional)
   */
  nextStage(sessionId, stageIndex = null) {
    const session = this.sessions.get(sessionId);
    if (!session || session.status !== 'active') { return; }

    if (stageIndex !== null) {
      session.currentStage = Math.max(0, Math.min(session.stages.length - 1, stageIndex));
    } else {
      session.currentStage = Math.min(session.currentStage + 1, session.stages.length - 1);
    }

    session.currentStageProgress = 0;
    session.overallProgress = this.calculateOverallProgress(session);

    console.log(`📍 Stage changed: ${sessionId} -> ${session.stages[session.currentStage]?.name}`);
    this.triggerProgressCallback(sessionId);
  }

  /**
   * Complete a session
   * @param {string} sessionId - Session ID
   * @param {Object} result - Completion result
   */
  completeSession(sessionId, result = {}) {
    const session = this.sessions.get(sessionId);
    if (!session) { return; }

    session.status = 'complete';
    session.endTime = Date.now();
    session.overallProgress = 100;
    session.result = result;

    this.globalStats.completedSessions++;
    this.updateAverageProcessingTime();

    console.log(`✅ Session completed: ${sessionId}`);

    // Trigger completion callback
    if (session.callbacks.onComplete) {
      session.callbacks.onComplete({
        sessionId,
        result,
        duration: session.endTime - session.startTime,
        session
      });
    }
  }

  /**
   * Fail a session
   * @param {string} sessionId - Session ID
   * @param {Error|string} error - Error information
   */
  failSession(sessionId, error) {
    const session = this.sessions.get(sessionId);
    if (!session) { return; }

    session.status = 'failed';
    session.endTime = Date.now();
    session.error = {
      message: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: Date.now()
    };

    this.globalStats.failedSessions++;

    console.error(`❌ Session failed: ${sessionId}`, error);

    // Trigger error callback
    if (session.callbacks.onError) {
      session.callbacks.onError({
        sessionId,
        error: session.error,
        session
      });
    }
  }

  /**
   * Cancel a session
   * @param {string} sessionId - Session ID
   */
  cancelSession(sessionId) {
    const session = this.sessions.get(sessionId);
    if (!session) { return; }

    session.status = 'cancelled';
    session.endTime = Date.now();

    console.log(`⏹️ Session cancelled: ${sessionId}`);
  }

  /**
   * Get session information
   * @param {string} sessionId - Session ID
   * @returns {Object} - Session information
   */
  getSession(sessionId) {
    return this.sessions.get(sessionId);
  }

  /**
   * Get all active sessions
   * @returns {Array} - Array of active sessions
   */
  getActiveSessions() {
    return Array.from(this.sessions.values()).filter(s => s.status === 'active');
  }

  /**
   * Calculate overall progress for a session
   * @private
   */
  calculateOverallProgress(session) {
    if (!session.stages || session.stages.length === 0) {
      return session.currentStageProgress;
    }

    const stageWeight = 100 / session.stages.length;
    const completedStagesProgress = session.currentStage * stageWeight;
    const currentStageProgress = (session.currentStageProgress / 100) * stageWeight;

    return Math.min(100, completedStagesProgress + currentStageProgress);
  }

  /**
   * Trigger progress callback for a session
   * @private
   */
  triggerProgressCallback(sessionId) {
    const session = this.sessions.get(sessionId);
    if (!session || !session.callbacks.onProgress) { return; }

    const currentStage = session.stages[session.currentStage];

    session.callbacks.onProgress({
      sessionId,
      overallProgress: session.overallProgress,
      stageProgress: session.currentStageProgress,
      currentStage: currentStage?.name || 'Unknown',
      stageIndex: session.currentStage,
      totalStages: session.stages.length,
      message: session.lastMessage || '',
      data: session.lastData || {},
      session
    });
  }

  /**
   * Get default stages for different process types
   * @private
   */
  getDefaultStages(type) {
    const stageTemplates = {
      upload: [
        { name: 'Uploading', weight: 30 },
        { name: 'Validating', weight: 20 },
        { name: 'Processing', weight: 50 }
      ],
      pdf: [
        { name: 'Loading', weight: 10 },
        { name: 'Parsing', weight: 30 },
        { name: 'Extracting', weight: 40 },
        { name: 'Analyzing', weight: 20 }
      ],
      processing: [
        { name: 'Initializing', weight: 10 },
        { name: 'Processing', weight: 70 },
        { name: 'Finalizing', weight: 20 }
      ],
      generic: [
        { name: 'Starting', weight: 10 },
        { name: 'Processing', weight: 80 },
        { name: 'Completing', weight: 10 }
      ]
    };

    return stageTemplates[type] || stageTemplates.generic;
  }

  /**
   * Generate unique session ID
   * @private
   */
  generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Update average processing time
   * @private
   */
  updateAverageProcessingTime() {
    const completedSessions = Array.from(this.sessions.values())
      .filter(s => s.status === 'complete' && s.endTime && s.startTime);

    if (completedSessions.length === 0) { return; }

    const totalTime = completedSessions.reduce((sum, session) => {
      return sum + (session.endTime - session.startTime);
    }, 0);

    this.globalStats.averageProcessingTime = totalTime / completedSessions.length;
  }

  /**
   * Clean up old sessions
   * @param {number} maxAge - Maximum age in milliseconds
   */
  cleanup(maxAge = 24 * 60 * 60 * 1000) { // 24 hours default
    const now = Date.now();
    const toDelete = [];

    for (const [sessionId, session] of this.sessions) {
      const sessionAge = now - session.startTime;
      if (sessionAge > maxAge && ['complete', 'failed', 'cancelled'].includes(session.status)) {
        toDelete.push(sessionId);
      }
    }

    toDelete.forEach(sessionId => {
      this.sessions.delete(sessionId);
    });

    if (toDelete.length > 0) {
      console.log(`🧹 Cleaned up ${toDelete.length} old progress sessions`);
    }
  }

  /**
   * Get global statistics
   * @returns {Object} - Global statistics
   */
  getGlobalStats() {
    return { ...this.globalStats };
  }
}

// Create singleton instance
export const unifiedProgressService = new UnifiedProgressService();

export default unifiedProgressService;
