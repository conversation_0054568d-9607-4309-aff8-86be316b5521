/**
 * Document Relationship Service
 *
 * Manages document relationships, stores similarity data, and provides
 * intelligent document linking capabilities for RAG-based analysis.
 *
 * <AUTHOR> Agent
 * @version 1.0.0
 * @since 2025-06-03
 */

import { ProcessingLogger } from '../utils/ProcessingLogger.js';
import { DocumentEmbeddingService } from './DocumentEmbeddingService.js';
import { VectorSimilarityService } from './VectorSimilarityService.js';

export class DocumentRelationshipService {
  constructor() {
    this.logger = new ProcessingLogger('DocumentRelationshipService');
    this.embeddingService = new DocumentEmbeddingService();
    this.similarityService = new VectorSimilarityService();

    // In-memory storage for relationships (in production, use persistent storage)
    this.documentEmbeddings = new Map();
    this.relationships = new Map();
    this.documentMetadata = new Map();
  }

  /**
   * Process document and generate relationships
   * @param {string} documentId - Unique document identifier
   * @param {string} text - Document text content
   * @param {Object} metadata - Document metadata
   * @param {string} apiKey - DeepSeek API key
   * @param {Object} options - Processing options
   * @returns {Promise<Object>} Processing result with relationships
   */
  async processDocument(documentId, text, metadata, apiKey, options = {}) {
    const startTime = Date.now();

    try {
      this.logger.log('🔗 Processing document for relationships', {
        documentId,
        textLength: text.length,
        hasApiKey: !!apiKey,
        existingDocuments: this.documentEmbeddings.size
      });

      // Store document metadata
      this.documentMetadata.set(documentId, {
        ...metadata,
        processedAt: new Date().toISOString(),
        textLength: text.length
      });

      // Generate embedding for the document
      const embedding = await this.embeddingService.generateEmbedding(
        text,
        apiKey,
        documentId,
        { ...options, documentType: metadata.documentType }
      );

      // Store embedding
      this.documentEmbeddings.set(documentId, embedding);

      // Find similar documents
      const similarDocuments = this.findSimilarDocuments(documentId, options);

      // Store relationships
      this.storeRelationships(documentId, similarDocuments);

      const processingTime = Date.now() - startTime;
      this.logger.log('✅ Document relationship processing completed', {
        documentId,
        embeddingMethod: embedding.method,
        similarDocumentsFound: similarDocuments.length,
        processingTime
      });

      return {
        documentId,
        embedding,
        similarDocuments,
        relationshipCount: similarDocuments.length,
        processingTime
      };

    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.logger.error('❌ Document relationship processing failed', {
        documentId,
        error: error.message,
        processingTime
      });

      return {
        documentId,
        error: error.message,
        similarDocuments: [],
        relationshipCount: 0,
        processingTime
      };
    }
  }

  /**
   * Find similar documents for a given document
   * @param {string} documentId - Target document ID
   * @param {Object} options - Search options
   * @returns {Array} Array of similar documents
   */
  findSimilarDocuments(documentId, options = {}) {
    try {
      const targetEmbedding = this.documentEmbeddings.get(documentId);
      if (!targetEmbedding) {
        throw new Error(`Document embedding not found: ${documentId}`);
      }

      const allEmbeddings = Array.from(this.documentEmbeddings.values());

      return this.similarityService.findSimilarDocuments(
        targetEmbedding,
        allEmbeddings,
        options
      );

    } catch (error) {
      this.logger.error('❌ Similar document search failed', {
        documentId,
        error: error.message
      });
      return [];
    }
  }

  /**
   * Get relationships for a document
   * @param {string} documentId - Document ID
   * @returns {Array} Array of related documents
   */
  getDocumentRelationships(documentId) {
    const relationships = this.relationships.get(documentId) || [];

    return relationships.map(rel => ({
      ...rel,
      metadata: this.documentMetadata.get(rel.documentId)
    }));
  }

  /**
   * Store relationships for a document
   * @private
   */
  storeRelationships(documentId, similarDocuments) {
    const relationships = similarDocuments.map(similar => ({
      documentId: similar.documentId,
      similarity: similar.similarity,
      relationshipType: similar.similarity.relationshipType,
      score: similar.similarity.score,
      confidence: similar.similarity.confidence,
      createdAt: new Date().toISOString()
    }));

    this.relationships.set(documentId, relationships);

    // Also store reverse relationships
    for (const similar of similarDocuments) {
      const existingRelationships = this.relationships.get(similar.documentId) || [];

      // Check if relationship already exists
      const existingIndex = existingRelationships.findIndex(
        rel => rel.documentId === documentId
      );

      const reverseRelationship = {
        documentId,
        similarity: similar.similarity,
        relationshipType: similar.similarity.relationshipType,
        score: similar.similarity.score,
        confidence: similar.similarity.confidence,
        createdAt: new Date().toISOString()
      };

      if (existingIndex >= 0) {
        existingRelationships[existingIndex] = reverseRelationship;
      } else {
        existingRelationships.push(reverseRelationship);
      }

      this.relationships.set(similar.documentId, existingRelationships);
    }
  }

  /**
   * Get document clusters based on similarity
   * @param {Object} options - Clustering options
   * @returns {Array} Array of document clusters
   */
  getDocumentClusters(options = {}) {
    const threshold = options.threshold || 0.8;
    const minClusterSize = options.minClusterSize || 2;

    try {
      this.logger.log('🔍 Generating document clusters', {
        documentCount: this.documentEmbeddings.size,
        threshold,
        minClusterSize
      });

      const clusters = [];
      const processed = new Set();

      for (const [documentId, embedding] of this.documentEmbeddings) {
        if (processed.has(documentId)) { continue; }

        const cluster = [documentId];
        processed.add(documentId);

        // Find all documents similar to this one
        const similarDocs = this.findSimilarDocuments(documentId, { threshold });

        for (const similar of similarDocs) {
          if (!processed.has(similar.documentId) && similar.similarity.score >= threshold) {
            cluster.push(similar.documentId);
            processed.add(similar.documentId);
          }
        }

        if (cluster.length >= minClusterSize) {
          clusters.push({
            id: `cluster_${clusters.length + 1}`,
            documents: cluster,
            size: cluster.length,
            averageSimilarity: this.calculateClusterSimilarity(cluster),
            createdAt: new Date().toISOString()
          });
        }
      }

      this.logger.log('✅ Document clusters generated', {
        clusterCount: clusters.length,
        totalDocuments: this.documentEmbeddings.size,
        clusteredDocuments: clusters.reduce((sum, cluster) => sum + cluster.size, 0)
      });

      return clusters;

    } catch (error) {
      this.logger.error('❌ Document clustering failed', {
        error: error.message
      });
      return [];
    }
  }

  /**
   * Calculate average similarity within a cluster
   * @private
   */
  calculateClusterSimilarity(documentIds) {
    if (documentIds.length < 2) { return 1.0; }

    let totalSimilarity = 0;
    let comparisons = 0;

    for (let i = 0; i < documentIds.length; i++) {
      for (let j = i + 1; j < documentIds.length; j++) {
        const embedding1 = this.documentEmbeddings.get(documentIds[i]);
        const embedding2 = this.documentEmbeddings.get(documentIds[j]);

        if (embedding1 && embedding2) {
          const similarity = this.similarityService.calculateSimilarity(embedding1, embedding2);
          totalSimilarity += similarity.score;
          comparisons++;
        }
      }
    }

    return comparisons > 0 ? totalSimilarity / comparisons : 0;
  }

  /**
   * Get relationship statistics
   * @returns {Object} Statistics about document relationships
   */
  getRelationshipStatistics() {
    const totalDocuments = this.documentEmbeddings.size;
    const totalRelationships = Array.from(this.relationships.values())
      .reduce((sum, rels) => sum + rels.length, 0);

    const relationshipTypes = {};
    for (const relationships of this.relationships.values()) {
      for (const rel of relationships) {
        relationshipTypes[rel.relationshipType] = (relationshipTypes[rel.relationshipType] || 0) + 1;
      }
    }

    return {
      totalDocuments,
      totalRelationships,
      averageRelationshipsPerDocument: totalDocuments > 0 ? totalRelationships / totalDocuments : 0,
      relationshipTypes,
      embeddingMethods: this.getEmbeddingMethodStats(),
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * Get embedding method statistics
   * @private
   */
  getEmbeddingMethodStats() {
    const methods = {};
    for (const embedding of this.documentEmbeddings.values()) {
      methods[embedding.method] = (methods[embedding.method] || 0) + 1;
    }
    return methods;
  }

  /**
   * Clear all stored data
   */
  clearAllData() {
    this.documentEmbeddings.clear();
    this.relationships.clear();
    this.documentMetadata.clear();
    this.embeddingService.clearCache();

    this.logger.log('🧹 All relationship data cleared');
  }

  /**
   * Export relationship data
   * @returns {Object} Exportable relationship data
   */
  exportData() {
    return {
      documentEmbeddings: Object.fromEntries(this.documentEmbeddings),
      relationships: Object.fromEntries(this.relationships),
      documentMetadata: Object.fromEntries(this.documentMetadata),
      statistics: this.getRelationshipStatistics(),
      exportedAt: new Date().toISOString()
    };
  }

  /**
   * Import relationship data
   * @param {Object} data - Data to import
   */
  importData(data) {
    try {
      if (data.documentEmbeddings) {
        this.documentEmbeddings = new Map(Object.entries(data.documentEmbeddings));
      }

      if (data.relationships) {
        this.relationships = new Map(Object.entries(data.relationships));
      }

      if (data.documentMetadata) {
        this.documentMetadata = new Map(Object.entries(data.documentMetadata));
      }

      this.logger.log('✅ Relationship data imported successfully', {
        documentsImported: this.documentEmbeddings.size,
        relationshipsImported: this.relationships.size
      });

    } catch (error) {
      this.logger.error('❌ Data import failed', {
        error: error.message
      });
      throw error;
    }
  }
}
