/**
 * Environment Configuration Service
 *
 * Manages environment variables, API keys, company details, and application settings
 * from .env file with secure handling and validation.
 *
 * ASSIGNMENT-041: Environment Configuration Fix and DeepSeek Enhancement
 * Epic: EPIC-005 - Enhanced AI Analysis & RAG Integration
 * Story: STORY-5.1 - Environment Configuration & API Enhancement
 * Task: TASK-5.1.1 - Fix Environment Variable Loading
 */

import { StorageAPI } from '../api/StorageAPI.js';
import { validateConfig } from '../utils/configValidation.js';
import { envLoader } from '../utils/EnvLoader.js';
import { getDefaultEnvironmentVariables } from '../config/defaultEnvironment.js';

/**
 * Environment Configuration Service
 * Handles loading, validation, and secure storage of environment configuration
 */
export class EnvironmentConfigService {
  constructor() {
    this.config = {};
    this.isLoaded = false;
    this.encryptionKey = null;
    this.storageKey = 'mvat_environment_config';
  }

  /**
   * Initialize the environment configuration service
   * @returns {Promise<boolean>} Success status
   */
  async initialize() {
    try {
      console.log('🔧 EnvironmentConfigService: Initializing...');

      // Initialize environment loader first
      await envLoader.initialize();

      // Load configuration from various sources
      await this.loadConfiguration();

      // Validate configuration
      const validation = this.validateConfiguration();
      if (!validation.isValid) {
        console.warn('⚠️ Configuration validation failed:', validation.errors);
        console.warn('⚠️ Continuing with partial configuration...');
        // Don't throw error, just log warnings and continue
      }

      // Store configuration securely
      await this.storeConfiguration();

      this.isLoaded = true;
      console.log('✅ EnvironmentConfigService: Initialized successfully');
      console.log('📊 Configuration loaded:', {
        apiKeys: Object.keys(this.config.apiKeys || {}).length,
        company: !!this.config.company?.name,
        features: Object.keys(this.config.features || {}).length,
        subscription: !!this.config.subscription
      });

      // Development mode: Log essential configuration only (reduced spam)
      if (this._isDevelopmentMode()) {
        console.log('🔍 EnvironmentConfigService: Development Mode - Configuration Summary');
        console.log('📊 Configuration loaded:', {
          apiKeys: Object.keys(this.config.apiKeys || {}).length,
          company: !!this.config.company?.name,
          features: Object.keys(this.config.features || {}).length,
          subscription: !!this.config.subscription,
          envVars: Object.keys(this.config.environment || {}).length
        });
      }

      return true;
    } catch (error) {
      console.error('❌ EnvironmentConfigService initialization failed:', error);
      this.isLoaded = false;
      return false;
    }
  }

  /**
   * Load configuration from environment variables and storage
   * @private
   */
  async loadConfiguration() {
    // In Chrome extension, we simulate environment variables
    // In a real environment, these would come from process.env
    const envVars = await this.getEnvironmentVariables();

    this.config = {
      // API Keys & External Services
      apiKeys: {
        deepseek: {
          key: envVars.DEEPSEEK_API_KEY,
          url: envVars.DEEPSEEK_API_URL || 'https://api.deepseek.com/v1',
          model: envVars.DEEPSEEK_MODEL || 'deepseek-chat',
          maxTokens: parseInt(envVars.DEEPSEEK_MAX_TOKENS) || 4000,
          temperature: parseFloat(envVars.DEEPSEEK_TEMPERATURE) || 0.1
        },
        openai: {
          key: envVars.OPENAI_API_KEY,
          url: envVars.OPENAI_API_URL || 'https://api.openai.com/v1',
          model: envVars.OPENAI_MODEL || 'gpt-4',
          maxTokens: parseInt(envVars.OPENAI_MAX_TOKENS) || 4000,
          temperature: parseFloat(envVars.OPENAI_TEMPERATURE) || 0.1
        },
        stripe: {
          publishableKey: envVars.STRIPE_PUBLISHABLE_KEY,
          secretKey: envVars.STRIPE_SECRET_KEY,
          webhookSecret: envVars.STRIPE_WEBHOOK_SECRET
        },
        fakturownia: {
          token: envVars.FAKTUROWNIA_API_TOKEN,
          accountName: envVars.FAKTUROWNIA_ACCOUNT_NAME,
          url: envVars.FAKTUROWNIA_API_URL
        },
        infakt: {
          key: envVars.INFAKT_API_KEY,
          accountName: envVars.INFAKT_ACCOUNT_NAME,
          url: envVars.INFAKT_API_URL
        }
      },

      // Company Profile & Branding
      company: {
        name: envVars.COMPANY_NAME || 'MVAT Solutions',
        legalName: envVars.COMPANY_LEGAL_NAME || 'MVAT Solutions Sp. z o.o.',
        nip: envVars.COMPANY_NIP,
        regon: envVars.COMPANY_REGON,
        krs: envVars.COMPANY_KRS,
        address: {
          street: envVars.COMPANY_ADDRESS_STREET,
          city: envVars.COMPANY_ADDRESS_CITY,
          postalCode: envVars.COMPANY_ADDRESS_POSTAL_CODE,
          country: envVars.COMPANY_ADDRESS_COUNTRY || 'Poland',
          countryCode: envVars.COMPANY_ADDRESS_COUNTRY_CODE || 'PL'
        },
        contact: {
          email: envVars.COMPANY_EMAIL,
          phone: envVars.COMPANY_PHONE,
          website: envVars.COMPANY_WEBSITE,
          supportEmail: envVars.COMPANY_SUPPORT_EMAIL
        },
        branding: {
          logoUrl: envVars.COMPANY_LOGO_URL,
          primaryColor: envVars.COMPANY_BRAND_COLOR_PRIMARY || '#2563eb',
          secondaryColor: envVars.COMPANY_BRAND_COLOR_SECONDARY || '#1e40af',
          accentColor: envVars.COMPANY_BRAND_COLOR_ACCENT || '#3b82f6'
        }
      },

      // Application Configuration
      app: {
        environment: envVars.NODE_ENV || 'development',
        version: envVars.APP_VERSION || '1.0.0',
        buildNumber: parseInt(envVars.APP_BUILD_NUMBER) || 1,
        debugMode: envVars.DEBUG_MODE === 'true',
        logLevel: envVars.LOG_LEVEL || 'info'
      },

      // Feature Flags
      features: {
        subscriptionSystem: envVars.FEATURE_SUBSCRIPTION_SYSTEM === 'true',
        paymentProcessing: envVars.FEATURE_PAYMENT_PROCESSING === 'true',
        enterpriseFeatures: envVars.FEATURE_ENTERPRISE_FEATURES === 'true',
        analyticsTracking: envVars.FEATURE_ANALYTICS_TRACKING === 'true',
        betaFeatures: envVars.FEATURE_BETA_FEATURES === 'true'
      },

      // Performance Settings
      performance: {
        maxFileSizeMB: parseInt(envVars.MAX_FILE_SIZE_MB) || 10,
        maxConcurrentUploads: parseInt(envVars.MAX_CONCURRENT_UPLOADS) || 3,
        apiTimeoutMs: parseInt(envVars.API_TIMEOUT_MS) || 30000,
        cacheTtlMinutes: parseInt(envVars.CACHE_TTL_MINUTES) || 60
      },

      // Security Settings
      security: {
        encryptionKey: envVars.ENCRYPTION_KEY,
        sessionTimeoutMinutes: parseInt(envVars.SESSION_TIMEOUT_MINUTES) || 60,
        maxLoginAttempts: parseInt(envVars.MAX_LOGIN_ATTEMPTS) || 5,
        passwordMinLength: parseInt(envVars.PASSWORD_MIN_LENGTH) || 8
      },

      // Subscription & Monetization
      subscription: {
        tiers: {
          starter: {
            invoiceLimit: parseInt(envVars.STARTER_TIER_INVOICE_LIMIT) || 10
          },
          professional: {
            invoiceLimit: parseInt(envVars.PROFESSIONAL_TIER_INVOICE_LIMIT) || 500,
            priceMonthly: parseInt(envVars.PROFESSIONAL_TIER_PRICE_MONTHLY) || 2900,
            priceYearly: parseInt(envVars.PROFESSIONAL_TIER_PRICE_YEARLY) || 29000
          },
          business: {
            invoiceLimit: parseInt(envVars.BUSINESS_TIER_INVOICE_LIMIT) || 2000,
            priceMonthly: parseInt(envVars.BUSINESS_TIER_PRICE_MONTHLY) || 9900,
            priceYearly: parseInt(envVars.BUSINESS_TIER_PRICE_YEARLY) || 99000
          },
          enterprise: {
            invoiceLimit: parseInt(envVars.ENTERPRISE_TIER_INVOICE_LIMIT) || -1,
            priceMonthly: parseInt(envVars.ENTERPRISE_TIER_PRICE_MONTHLY) || 29900,
            priceYearly: parseInt(envVars.ENTERPRISE_TIER_PRICE_YEARLY) || 299000
          }
        },
        trial: {
          days: parseInt(envVars.FREE_TRIAL_DAYS) || 14,
          invoiceLimit: parseInt(envVars.TRIAL_INVOICE_LIMIT) || 50
        }
      },

      // Localization & Internationalization
      localization: {
        defaultLanguage: envVars.DEFAULT_LANGUAGE || 'en',
        defaultCurrency: envVars.DEFAULT_CURRENCY || 'EUR',
        defaultTimezone: envVars.DEFAULT_TIMEZONE || 'Europe/Warsaw',
        supportedLanguages: (envVars.SUPPORTED_LANGUAGES || 'en,pl,de,fr').split(','),
        vatRates: {
          standard: parseFloat(envVars.VAT_RATE_STANDARD) || 23,
          reduced: parseFloat(envVars.VAT_RATE_REDUCED) || 8,
          zero: parseFloat(envVars.VAT_RATE_ZERO) || 0
        },
        dateFormat: envVars.DATE_FORMAT || 'DD/MM/YYYY',
        numberFormat: envVars.NUMBER_FORMAT || '1,234.56'
      }
    };
  }

  /**
   * Get environment variables using the dynamic EnvLoader
   * @private
   * @returns {Object} Environment variables
   */
  async getEnvironmentVariables() {
    try {
      // Use the dynamic environment loader
      if (envLoader.isReady()) {
        console.log('🔧 EnvironmentConfigService: Using dynamic environment loader');
        return envLoader.getAll();
      }
      console.warn('⚠️ EnvLoader not ready, initializing...');
      await envLoader.initialize();
      return envLoader.getAll();

    } catch (error) {
      console.warn('⚠️ Could not load environment variables from EnvLoader, using fallback');
      return this.getDefaultEnvironmentVariables();
    }
  }

  /**
   * Load environment variables from .env file (simulated)
   * @private
   * @returns {Object} Environment variables
   */
  async loadEnvironmentFromFile() {
    // Use the centralized default values from config
    // This eliminates duplicate default values and ensures consistency
    return getDefaultEnvironmentVariables();
  }

  /**
   * Get default environment variables
   * @private
   * @returns {Object} Default environment variables
   */
  getDefaultEnvironmentVariables() {
    // Use the centralized default values from config
    return getDefaultEnvironmentVariables();
  }

  /**
   * Validate the loaded configuration
   * @private
   * @returns {Object} Validation result
   */
  validateConfiguration() {
    return validateConfig(this.config);
  }

  /**
   * Store configuration securely
   * @private
   */
  async storeConfiguration() {
    try {
      // Store non-sensitive configuration
      const publicConfig = this.getPublicConfiguration();

      // Check if StorageAPI is available (Chrome extension environment)
      if (typeof StorageAPI !== 'undefined' && StorageAPI.set) {
        await StorageAPI.set({ environment_config: publicConfig });

        // Store sensitive configuration separately with encryption
        const sensitiveConfig = this.getSensitiveConfiguration();
        await StorageAPI.set({ environment_config_sensitive: sensitiveConfig });
      } else {
        // In test environment or when StorageAPI is not available
        console.log('⚠️ StorageAPI not available, skipping configuration storage');
      }

    } catch (error) {
      console.warn('⚠️ Failed to store configuration (continuing without storage):', error.message);
      // Don't throw error, just continue without storage
    }
  }

  /**
   * Get public (non-sensitive) configuration
   * @private
   * @returns {Object} Public configuration
   */
  getPublicConfiguration() {
    return {
      company: this.config.company,
      app: this.config.app,
      features: this.config.features,
      performance: this.config.performance,
      subscription: this.config.subscription,
      localization: this.config.localization
    };
  }

  /**
   * Get sensitive configuration (API keys, secrets)
   * @private
   * @returns {Object} Sensitive configuration
   */
  getSensitiveConfiguration() {
    return {
      apiKeys: this.config.apiKeys,
      security: this.config.security
    };
  }

  /**
   * Get configuration value by path
   * @param {string} path - Dot notation path (e.g., 'company.name')
   * @param {*} defaultValue - Default value if not found
   * @returns {*} Configuration value
   */
  get(path, defaultValue = null) {
    if (!this.isLoaded) {
      console.warn('⚠️ Configuration not loaded yet');
      return defaultValue;
    }

    const keys = path.split('.');
    let value = this.config;

    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key];
      } else {
        return defaultValue;
      }
    }

    return value;
  }

  /**
   * Get API key for a service
   * @param {string} service - Service name (deepseek, stripe, fakturownia)
   * @returns {string|null} API key
   */
  getApiKey(service) {
    return this.get(`apiKeys.${service}.key`);
  }

  /**
   * Get company information
   * @returns {Object} Company information
   */
  getCompanyInfo() {
    return this.get('company', {});
  }

  /**
   * Check if feature is enabled
   * @param {string} feature - Feature name
   * @returns {boolean} Feature status
   */
  isFeatureEnabled(feature) {
    return this.get(`features.${feature}`, false);
  }

  /**
   * Get subscription tier configuration
   * @param {string} tier - Tier name
   * @returns {Object} Tier configuration
   */
  getSubscriptionTier(tier) {
    return this.get(`subscription.tiers.${tier}`, {});
  }

  /**
   * Get all configuration
   * @param {boolean} maskSensitive - Whether to mask sensitive data (default: true)
   * @returns {Object} Full configuration (optionally with sensitive data masked)
   */
  getAll(maskSensitive = true) {
    if (!this.isLoaded) {
      return {};
    }

    // Return configuration with optional sensitive data masking
    const config = JSON.parse(JSON.stringify(this.config));

    if (maskSensitive) {
      // Mask API keys
      if (config.apiKeys) {
        Object.keys(config.apiKeys).forEach(service => {
          if (config.apiKeys[service].key) {
            config.apiKeys[service].key = '***masked***';
          }
        });
      }

      // Mask encryption key
      if (config.security?.encryptionKey) {
        config.security.encryptionKey = '***masked***';
      }
    }

    return config;
  }

  /**
   * Get all configuration with sensitive data unmasked (for debugging)
   * @returns {Object} Full configuration with actual sensitive values
   */
  getAllUnmasked() {
    return this.getAll(false);
  }

  /**
   * Reload configuration
   * @returns {Promise<boolean>} Success status
   */
  async reload() {
    this.isLoaded = false;
    return await this.initialize();
  }

  /**
   * Check if we're in development mode
   * @private
   * @returns {boolean} True if in development mode
   */
  _isDevelopmentMode() {
    // Check multiple indicators for development mode
    const debugMode = this.config?.app?.debugMode === true;
    const nodeEnv = this.config?.app?.environment === 'development';
    const isLocalhost = typeof window !== 'undefined' &&
                       (window.location.hostname === 'localhost' ||
                        window.location.hostname === '127.0.0.1' ||
                        window.location.protocol === 'file:');

    return debugMode || nodeEnv || isLocalhost;
  }
}

// Export singleton instance
export const environmentConfig = new EnvironmentConfigService();
