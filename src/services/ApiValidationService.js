/**
 * API Validation Service
 * Tests API key validity and connection for supported providers
 */
export class ApiValidationService {
  constructor() {
    this.timeout = 10000; // 10 second timeout
    this.providers = {
      deepseek: {
        name: 'DeepSeek',
        baseUrl: 'https://api.deepseek.com/v1',
        testEndpoint: '/models',
        keyFormat: /^sk-[a-zA-Z0-9]{32,}$/
      },
      openai: {
        name: 'OpenAI',
        baseUrl: 'https://api.openai.com/v1',
        testEndpoint: '/models',
        keyFormat: /^sk-[a-zA-Z0-9]{32,}$/
      },
      fakturownia: {
        name: 'Fakturownia',
        baseUrl: 'https://app.fakturownia.pl/api/v1',
        testEndpoint: '/invoices.json',
        keyFormat: /^[a-zA-Z0-9]{20,}$/
      },
      infakt: {
        name: 'Infakt',
        baseUrl: 'https://api.infakt.pl/v3',
        testEndpoint: '/invoices.json',
        keyFormat: /^[a-zA-Z0-9]{20,}$/
      }
    };
  }

  /**
   * Validate API key format
   * @param {string} provider - Provider name
   * @param {string} apiKey - API key to validate
   * @returns {Object} - Validation result
   */
  validateKeyFormat(provider, apiKey) {
    try {
      if (!provider || !this.providers[provider]) {
        return {
          valid: false,
          error: 'Unknown provider'
        };
      }

      if (!apiKey || typeof apiKey !== 'string') {
        return {
          valid: false,
          error: 'API key is required'
        };
      }

      const trimmedKey = apiKey.trim();
      if (!trimmedKey) {
        return {
          valid: false,
          error: 'API key cannot be empty'
        };
      }

      const providerConfig = this.providers[provider];
      if (!providerConfig.keyFormat.test(trimmedKey)) {
        return {
          valid: false,
          error: `Invalid ${providerConfig.name} API key format`
        };
      }

      return {
        valid: true,
        provider: providerConfig.name,
        keyLength: trimmedKey.length
      };
    } catch (error) {
      return {
        valid: false,
        error: 'Validation error: ' + error.message
      };
    }
  }

  /**
   * Test API connection
   * @param {string} provider - Provider name
   * @param {string} apiKey - API key to test
   * @returns {Promise<Object>} - Connection test result
   */
  async testConnection(provider, apiKey) {
    const startTime = Date.now();

    try {
      // First validate format
      const formatValidation = this.validateKeyFormat(provider, apiKey);
      if (!formatValidation.valid) {
        return {
          success: false,
          provider: provider,
          error: formatValidation.error,
          responseTime: Date.now() - startTime
        };
      }

      const providerConfig = this.providers[provider];
      const testUrl = providerConfig.baseUrl + providerConfig.testEndpoint;

      // Prepare headers based on provider
      const headers = this.getAuthHeaders(provider, apiKey.trim());

      // Make test request with timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.timeout);

      const response = await fetch(testUrl, {
        method: 'GET',
        headers,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      const responseTime = Date.now() - startTime;

      if (response.ok) {
        return {
          success: true,
          provider: providerConfig.name,
          status: response.status,
          responseTime,
          message: 'Connection successful'
        };
      }
      const errorText = await response.text().catch(() => 'Unknown error');
      return {
        success: false,
        provider: providerConfig.name,
        status: response.status,
        responseTime,
        error: `HTTP ${response.status}: ${errorText.substring(0, 100)}`
      };


    } catch (error) {
      const responseTime = Date.now() - startTime;

      if (error.name === 'AbortError') {
        return {
          success: false,
          provider: this.providers[provider]?.name || provider,
          error: 'Connection timeout',
          responseTime
        };
      }

      return {
        success: false,
        provider: this.providers[provider]?.name || provider,
        error: error.message,
        responseTime
      };
    }
  }

  /**
   * Get authentication headers for provider
   * @param {string} provider - Provider name
   * @param {string} apiKey - API key
   * @returns {Object} - Headers object
   */
  getAuthHeaders(provider, apiKey) {
    const baseHeaders = {
      'Content-Type': 'application/json',
      'User-Agent': 'MVAT-Chrome-Extension/1.0'
    };

    switch (provider) {
      case 'deepseek':
      case 'openai':
        return {
          ...baseHeaders,
          'Authorization': `Bearer ${apiKey}`
        };

      case 'fakturownia':
        return {
          ...baseHeaders,
          'Accept': 'application/json',
          'X-API-TOKEN': apiKey
        };

      case 'infakt':
        return {
          ...baseHeaders,
          'X-inFakt-ApiKey': apiKey
        };

      default:
        return baseHeaders;
    }
  }

  /**
   * Test all provided API keys
   * @param {Object} apiKeys - Object with provider keys
   * @returns {Promise<Object>} - Test results for all providers
   */
  async testAllConnections(apiKeys) {
    const results = {};
    const promises = [];

    for (const [provider, apiKey] of Object.entries(apiKeys)) {
      if (apiKey && apiKey.trim() && this.providers[provider]) {
        promises.push(
          this.testConnection(provider, apiKey).then(result => {
            results[provider] = result;
          })
        );
      } else {
        results[provider] = {
          success: false,
          provider: this.providers[provider]?.name || provider,
          error: 'No API key provided',
          skipped: true
        };
      }
    }

    await Promise.all(promises);

    return {
      results,
      summary: this.generateTestSummary(results)
    };
  }

  /**
   * Generate summary of test results
   * @param {Object} results - Test results
   * @returns {Object} - Summary statistics
   */
  generateTestSummary(results) {
    const total = Object.keys(results).length;
    const successful = Object.values(results).filter(r => r.success).length;
    const failed = Object.values(results).filter(r => !r.success && !r.skipped).length;
    const skipped = Object.values(results).filter(r => r.skipped).length;

    return {
      total,
      successful,
      failed,
      skipped,
      successRate: total > 0 ? (successful / (total - skipped)) * 100 : 0
    };
  }

  /**
   * Get supported providers list
   * @returns {Array} - List of supported providers
   */
  getSupportedProviders() {
    return Object.keys(this.providers).map(key => ({
      id: key,
      name: this.providers[key].name,
      keyFormat: this.providers[key].keyFormat.toString()
    }));
  }
}

// Export singleton instance
export const apiValidationService = new ApiValidationService();

// Self-test function for development
export async function testApiValidationService() {
  console.log('\n🔌 Testing API Validation Service...');

  try {
    const service = new ApiValidationService();

    // Test format validation
    console.log('\n📝 Testing format validation...');

    const formatTests = [
      { provider: 'deepseek', key: 'sk-test123456789012345678901234567890', expected: true },
      { provider: 'deepseek', key: 'invalid-key', expected: false },
      { provider: 'openai', key: 'sk-test123456789012345678901234567890', expected: true },
      { provider: 'fakturownia', key: 'test12345678901234567890', expected: true },
      { provider: 'infakt', key: 'test12345678901234567890', expected: true },
      { provider: 'unknown', key: 'any-key', expected: false }
    ];

    for (const test of formatTests) {
      const result = service.validateKeyFormat(test.provider, test.key);
      const passed = result.valid === test.expected;
      console.log(`${passed ? '✅' : '❌'} ${test.provider}: ${passed ? 'PASSED' : 'FAILED'}`);
    }

    // Test supported providers
    const providers = service.getSupportedProviders();
    console.log('\n📋 Supported providers:', providers.map(p => p.name).join(', '));

    // Test connection (with mock keys - will fail but test the flow)
    console.log('\n🔌 Testing connection flow...');
    const mockKeys = {
      deepseek: 'sk-test123456789012345678901234567890',
      openai: 'sk-test123456789012345678901234567890'
    };

    const connectionResults = await service.testAllConnections(mockKeys);
    console.log('📊 Connection test summary:', connectionResults.summary);

    console.log('🎉 API validation service tests completed!');

  } catch (error) {
    console.error('❌ API validation service test failed:', error);
  }
}

// Run tests if this file is executed directly
if (typeof window !== 'undefined' && window.location?.search?.includes('test=api-validation')) {
  testApiValidationService();
}
