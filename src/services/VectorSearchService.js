/**
 * Vector Search Service
 *
 * Efficient vector similarity search implementation with multiple algorithms,
 * indexing, and optimization for large-scale document collections.
 *
 * <AUTHOR> Agent
 * @version 1.0.0
 * @since 2025-01-28
 */

import { ProcessingLogger } from '../utils/ProcessingLogger.js';
import {
  cosineSimilarity,
  euclideanDistance,
  dotProduct,
  normalizeVector,
  validateVector,
  findKNearestNeighbors
} from '../utils/EmbeddingUtils.js';

export class VectorSearchService {
  constructor(options = {}) {
    this.logger = new ProcessingLogger('VectorSearchService');

    // Configuration
    this.config = {
      defaultAlgorithm: options.defaultAlgorithm || 'cosine',
      defaultK: options.defaultK || 10,
      similarityThreshold: options.similarityThreshold || 0.7,
      indexingEnabled: options.indexingEnabled !== false,
      batchSize: options.batchSize || 100,
      maxConcurrency: options.maxConcurrency || 4,
      cacheResults: options.cacheResults !== false,
      ...options
    };

    // Vector index for fast search
    this.vectorIndex = new Map();
    this.documentMetadata = new Map();
    this.indexStats = {
      totalVectors: 0,
      indexedVectors: 0,
      lastIndexUpdate: null
    };

    // Search algorithms
    this.algorithms = {
      cosine: this.cosineSimilaritySearch.bind(this),
      euclidean: this.euclideanDistanceSearch.bind(this),
      dot: this.dotProductSearch.bind(this),
      hybrid: this.hybridSearch.bind(this)
    };

    // Performance metrics
    this.metrics = {
      totalSearches: 0,
      averageSearchTime: 0,
      totalSearchTime: 0,
      cacheHits: 0,
      indexHits: 0
    };

    // Result cache
    this.resultCache = new Map();
    this.maxCacheSize = 500;
  }

  /**
   * Add vectors to the search index
   * @param {Array} vectors - Array of {id, vector, metadata} objects
   * @returns {Promise<Object>} Indexing result
   */
  async addVectors(vectors) {
    const startTime = Date.now();

    try {
      this.logger.log('📊 Adding vectors to search index', {
        count: vectors.length,
        indexingEnabled: this.config.indexingEnabled
      });

      let indexed = 0;
      for (const vectorData of vectors) {
        if (this.validateVectorData(vectorData)) {
          this.vectorIndex.set(vectorData.id, {
            vector: normalizeVector(vectorData.vector),
            metadata: vectorData.metadata || {},
            addedAt: new Date().toISOString()
          });

          if (vectorData.metadata) {
            this.documentMetadata.set(vectorData.id, vectorData.metadata);
          }

          indexed++;
        } else {
          this.logger.warn('⚠️ Invalid vector data skipped', { id: vectorData.id });
        }
      }

      // Update index stats
      this.indexStats.totalVectors = this.vectorIndex.size;
      this.indexStats.indexedVectors = indexed;
      this.indexStats.lastIndexUpdate = new Date().toISOString();

      const processingTime = Date.now() - startTime;
      this.logger.log('✅ Vectors added to index', {
        indexed,
        total: this.vectorIndex.size,
        processingTime
      });

      return {
        indexed,
        total: this.vectorIndex.size,
        processingTime,
        success: true
      };

    } catch (error) {
      this.logger.error('❌ Failed to add vectors to index', {
        error: error.message,
        vectorCount: vectors.length
      });

      return {
        indexed: 0,
        total: this.vectorIndex.size,
        error: error.message,
        success: false
      };
    }
  }

  /**
   * Search for similar vectors
   * @param {Array|Object} query - Query vector or embedding object
   * @param {Object} options - Search options
   * @returns {Promise<Array>} Search results
   */
  async search(query, options = {}) {
    const startTime = Date.now();
    const searchId = `search_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      this.metrics.totalSearches++;

      // Extract vector from query
      const queryVector = this.extractQueryVector(query);

      // Validate query vector
      if (!validateVector(queryVector)) {
        throw new Error('Invalid query vector');
      }

      const searchOptions = {
        algorithm: options.algorithm || this.config.defaultAlgorithm,
        k: options.k || this.config.defaultK,
        threshold: options.threshold || this.config.similarityThreshold,
        includeMetadata: options.includeMetadata !== false,
        excludeIds: options.excludeIds || [],
        filterFn: options.filterFn,
        ...options
      };

      this.logger.log('🔍 Starting vector search', {
        searchId,
        algorithm: searchOptions.algorithm,
        k: searchOptions.k,
        threshold: searchOptions.threshold,
        indexSize: this.vectorIndex.size
      });

      // Check cache first
      const cacheKey = this.generateCacheKey(queryVector, searchOptions);
      if (this.config.cacheResults && this.resultCache.has(cacheKey)) {
        this.metrics.cacheHits++;
        const cached = this.resultCache.get(cacheKey);
        this.logger.log('📋 Using cached search results', { searchId, resultCount: cached.length });
        return cached;
      }

      // Perform search
      const algorithm = this.algorithms[searchOptions.algorithm];
      if (!algorithm) {
        throw new Error(`Unsupported search algorithm: ${searchOptions.algorithm}`);
      }

      const results = await algorithm(queryVector, searchOptions, searchId);

      // Cache results
      if (this.config.cacheResults) {
        this.cacheResults(cacheKey, results);
      }

      // Update metrics
      const searchTime = Date.now() - startTime;
      this.updateSearchMetrics(searchTime);

      this.logger.log('✅ Vector search completed', {
        searchId,
        algorithm: searchOptions.algorithm,
        resultCount: results.length,
        searchTime
      });

      return results;

    } catch (error) {
      const searchTime = Date.now() - startTime;
      this.updateSearchMetrics(searchTime);

      this.logger.error('❌ Vector search failed', {
        searchId,
        error: error.message,
        searchTime
      });

      return [];
    }
  }

  /**
   * Cosine similarity search implementation
   * @private
   */
  async cosineSimilaritySearch(queryVector, options, searchId) {
    const results = [];
    const normalizedQuery = normalizeVector(queryVector);

    for (const [id, vectorData] of this.vectorIndex) {
      if (options.excludeIds.includes(id)) { continue; }

      const similarity = cosineSimilarity(normalizedQuery, vectorData.vector);

      if (similarity >= options.threshold) {
        const result = {
          id,
          similarity,
          distance: 1 - similarity,
          algorithm: 'cosine'
        };

        if (options.includeMetadata) {
          result.metadata = vectorData.metadata;
        }

        // Apply filter if provided
        if (!options.filterFn || options.filterFn(result)) {
          results.push(result);
        }
      }
    }

    // Sort by similarity (descending) and limit results
    return results
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, options.k);
  }

  /**
   * Euclidean distance search implementation
   * @private
   */
  async euclideanDistanceSearch(queryVector, options, searchId) {
    const results = [];
    const normalizedQuery = normalizeVector(queryVector);

    for (const [id, vectorData] of this.vectorIndex) {
      if (options.excludeIds.includes(id)) { continue; }

      const distance = euclideanDistance(normalizedQuery, vectorData.vector);
      const similarity = 1 / (1 + distance); // Convert distance to similarity

      if (similarity >= options.threshold) {
        const result = {
          id,
          similarity,
          distance,
          algorithm: 'euclidean'
        };

        if (options.includeMetadata) {
          result.metadata = vectorData.metadata;
        }

        if (!options.filterFn || options.filterFn(result)) {
          results.push(result);
        }
      }
    }

    return results
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, options.k);
  }

  /**
   * Dot product search implementation
   * @private
   */
  async dotProductSearch(queryVector, options, searchId) {
    const results = [];
    const normalizedQuery = normalizeVector(queryVector);

    for (const [id, vectorData] of this.vectorIndex) {
      if (options.excludeIds.includes(id)) { continue; }

      const dotProd = dotProduct(normalizedQuery, vectorData.vector);
      const similarity = (dotProd + 1) / 2; // Normalize to [0, 1]

      if (similarity >= options.threshold) {
        const result = {
          id,
          similarity,
          distance: 1 - similarity,
          algorithm: 'dot'
        };

        if (options.includeMetadata) {
          result.metadata = vectorData.metadata;
        }

        if (!options.filterFn || options.filterFn(result)) {
          results.push(result);
        }
      }
    }

    return results
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, options.k);
  }

  /**
   * Hybrid search combining multiple algorithms
   * @private
   */
  async hybridSearch(queryVector, options, searchId) {
    const algorithms = ['cosine', 'euclidean'];
    const allResults = new Map();

    // Run multiple algorithms
    for (const algorithm of algorithms) {
      const algoOptions = { ...options, algorithm };
      const results = await this.algorithms[algorithm](queryVector, algoOptions, searchId);

      for (const result of results) {
        if (allResults.has(result.id)) {
          // Combine scores
          const existing = allResults.get(result.id);
          existing.similarity = (existing.similarity + result.similarity) / 2;
          existing.algorithms.push(algorithm);
        } else {
          allResults.set(result.id, {
            ...result,
            algorithm: 'hybrid',
            algorithms: [algorithm]
          });
        }
      }
    }

    return Array.from(allResults.values())
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, options.k);
  }

  /**
   * Batch search for multiple queries
   * @param {Array} queries - Array of query vectors
   * @param {Object} options - Search options
   * @returns {Promise<Array>} Array of search results
   */
  async batchSearch(queries, options = {}) {
    const startTime = Date.now();

    try {
      this.logger.log('🔍 Starting batch vector search', {
        queryCount: queries.length,
        batchSize: this.config.batchSize
      });

      const results = [];
      const batches = this.createBatches(queries, this.config.batchSize);

      for (const batch of batches) {
        const batchPromises = batch.map(query => this.search(query, options));
        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);
      }

      const processingTime = Date.now() - startTime;
      this.logger.log('✅ Batch search completed', {
        queryCount: queries.length,
        totalResults: results.reduce((sum, r) => sum + r.length, 0),
        processingTime
      });

      return results;

    } catch (error) {
      this.logger.error('❌ Batch search failed', {
        error: error.message,
        queryCount: queries.length
      });
      return [];
    }
  }

  /**
   * Extract vector from query input
   * @private
   */
  extractQueryVector(query) {
    if (Array.isArray(query)) {
      return query;
    }

    if (query && query.vector) {
      return query.vector;
    }

    throw new Error('Invalid query format: expected array or object with vector property');
  }

  /**
   * Validate vector data structure
   * @private
   */
  validateVectorData(vectorData) {
    return vectorData &&
           vectorData.id &&
           vectorData.vector &&
           validateVector(vectorData.vector);
  }

  /**
   * Generate cache key for search results
   * @private
   */
  generateCacheKey(queryVector, options) {
    const vectorHash = this.hashVector(queryVector);
    const optionsHash = this.hashObject(options);
    return `${vectorHash}_${optionsHash}`;
  }

  /**
   * Cache search results
   * @private
   */
  cacheResults(key, results) {
    if (this.resultCache.size >= this.maxCacheSize) {
      // Remove oldest entry
      const firstKey = this.resultCache.keys().next().value;
      this.resultCache.delete(firstKey);
    }

    this.resultCache.set(key, results);
  }

  /**
   * Create batches from array
   * @private
   */
  createBatches(array, batchSize) {
    const batches = [];
    for (let i = 0; i < array.length; i += batchSize) {
      batches.push(array.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * Hash vector for caching
   * @private
   */
  hashVector(vector) {
    const sample = vector.slice(0, 10); // Use first 10 elements for hash
    return this.simpleHash(sample.join(','));
  }

  /**
   * Hash object for caching
   * @private
   */
  hashObject(obj) {
    return this.simpleHash(JSON.stringify(obj));
  }

  /**
   * Simple hash function
   * @private
   */
  simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Update search metrics
   * @private
   */
  updateSearchMetrics(searchTime) {
    this.metrics.totalSearchTime += searchTime;
    this.metrics.averageSearchTime = this.metrics.totalSearchTime / this.metrics.totalSearches;
  }

  /**
   * Remove vector from index
   * @param {string} id - Vector ID to remove
   */
  removeVector(id) {
    const removed = this.vectorIndex.delete(id);
    this.documentMetadata.delete(id);

    if (removed) {
      this.indexStats.totalVectors = this.vectorIndex.size;
      this.logger.log('🗑️ Vector removed from index', { id });
    }

    return removed;
  }

  /**
   * Clear all vectors from index
   */
  clearIndex() {
    this.vectorIndex.clear();
    this.documentMetadata.clear();
    this.resultCache.clear();

    this.indexStats = {
      totalVectors: 0,
      indexedVectors: 0,
      lastIndexUpdate: null
    };

    this.logger.log('🧹 Vector index cleared');
  }

  /**
   * Get search statistics
   */
  getStats() {
    return {
      ...this.metrics,
      index: this.indexStats,
      cacheSize: this.resultCache.size,
      cacheHitRate: this.metrics.totalSearches > 0
        ? this.metrics.cacheHits / this.metrics.totalSearches
        : 0
    };
  }

  /**
   * Get configuration
   */
  getConfig() {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    this.logger.log('⚙️ Vector search configuration updated', newConfig);
  }

  /**
   * Get supported algorithms
   */
  getSupportedAlgorithms() {
    return Object.keys(this.algorithms);
  }
}
