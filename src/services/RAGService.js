/**
 * RAGService - RAG-based document linking and prompt enhancement
 * Provides document similarity analysis and context-aware prompt enhancement
 */

import { DocumentEmbeddingService } from './DocumentEmbeddingService.js';
import { processingLogger } from '../utils/ProcessingLogger.js';

class RAGService {
  constructor() {
    this.embeddingService = new DocumentEmbeddingService();
    this.similarityThreshold = 0.7;
    this.maxSimilarDocuments = 5;
  }

  /**
   * Find similar documents and enhance prompt with context
   * @param {Object} documentData - Current document data
   * @param {Object} analysisResult - DeepSeek analysis result
   * @param {Object} options - Processing options
   * @returns {Promise<Object>} - RAG enhancement result
   */
  async enhanceWithRAG(documentData, analysisResult, options = {}) {
    const startTime = performance.now();
    const { progressCallback } = options;

    try {
      progressCallback?.(10);
      console.log('🔗 Starting RAG enhancement process');

      // Step 1: Generate embedding for current document
      progressCallback?.(20);
      const currentEmbedding = await this.generateDocumentEmbedding(documentData);

      // Step 2: Find similar documents
      progressCallback?.(40);
      const similarDocuments = await this.findSimilarDocuments(currentEmbedding);

      // Step 3: Extract relevant context from similar documents
      progressCallback?.(60);
      const contextData = await this.extractRelevantContext(similarDocuments, analysisResult);

      // Step 4: Enhance prompt with context
      progressCallback?.(80);
      const enhancedPrompt = await this.enhancePromptWithContext(analysisResult, contextData);

      // Step 5: Generate final result
      progressCallback?.(100);
      const result = {
        similar_docs: similarDocuments,
        enhanced_prompt: enhancedPrompt,
        context: contextData,
        confidence: this.calculateRAGConfidence(similarDocuments, contextData),
        processing_time: performance.now() - startTime
      };

      console.log(`✅ RAG enhancement completed in ${result.processing_time.toFixed(2)}ms`);
      console.log(`📊 Found ${similarDocuments.length} similar documents`);

      return result;
    } catch (error) {
      console.error('❌ RAG enhancement failed:', error);
      throw error;
    }
  }

  /**
   * Generate embedding for document
   */
  async generateDocumentEmbedding(documentData) {
    try {
      // Combine text content for embedding
      const textContent = [
        documentData.text || '',
        documentData.filename || '',
        JSON.stringify(documentData.metadata || {})
      ].join(' ').trim();

      if (!textContent) {
        throw new Error('No text content available for embedding');
      }

      // Generate embedding using the embedding service
      const embedding = await this.embeddingService.generateEmbedding(textContent);
      return embedding;
    } catch (error) {
      console.error('❌ Failed to generate document embedding:', error);
      throw error;
    }
  }

  /**
   * Find similar documents using vector similarity
   */
  async findSimilarDocuments(currentEmbedding) {
    try {
      // Get all stored document embeddings
      const storedDocuments = await this.embeddingService.getAllStoredEmbeddings();

      if (!storedDocuments || storedDocuments.length === 0) {
        console.log('📝 No stored documents found for similarity comparison');
        return [];
      }

      // Calculate similarities
      const similarities = [];
      for (const doc of storedDocuments) {
        try {
          const similarity = await this.embeddingService.calculateSimilarity(
            currentEmbedding,
            doc.embedding
          );

          if (similarity >= this.similarityThreshold) {
            similarities.push({
              ...doc,
              similarity: similarity,
              relevance_score: this.calculateRelevanceScore(doc, similarity)
            });
          }
        } catch (error) {
          console.warn(`⚠️ Failed to calculate similarity for document ${doc.id}:`, error);
        }
      }

      // Sort by similarity and limit results
      const sortedSimilar = similarities
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, this.maxSimilarDocuments);

      console.log(`🔍 Found ${sortedSimilar.length} similar documents above threshold ${this.similarityThreshold}`);
      return sortedSimilar;
    } catch (error) {
      console.error('❌ Failed to find similar documents:', error);
      return [];
    }
  }

  /**
   * Extract relevant context from similar documents
   */
  async extractRelevantContext(similarDocuments, analysisResult) {
    try {
      if (!similarDocuments || similarDocuments.length === 0) {
        return {
          field_patterns: {},
          common_structures: [],
          validation_rules: [],
          confidence_factors: []
        };
      }

      const context = {
        field_patterns: {},
        common_structures: [],
        validation_rules: [],
        confidence_factors: []
      };

      // Extract field patterns from similar documents
      for (const doc of similarDocuments) {
        if (doc.extracted_fields) {
          Object.keys(doc.extracted_fields).forEach(field => {
            if (!context.field_patterns[field]) {
              context.field_patterns[field] = [];
            }
            context.field_patterns[field].push({
              value: doc.extracted_fields[field],
              confidence: doc.similarity,
              document_id: doc.id
            });
          });
        }

        // Extract structural patterns
        if (doc.document_structure) {
          context.common_structures.push({
            structure: doc.document_structure,
            similarity: doc.similarity,
            document_id: doc.id
          });
        }

        // Extract validation patterns
        if (doc.validation_results) {
          context.validation_rules.push({
            rules: doc.validation_results,
            success_rate: doc.accuracy || 0,
            document_id: doc.id
          });
        }
      }

      // Calculate confidence factors
      context.confidence_factors = this.calculateConfidenceFactors(similarDocuments, analysisResult);

      console.log(`📋 Extracted context from ${similarDocuments.length} similar documents`);
      return context;
    } catch (error) {
      console.error('❌ Failed to extract relevant context:', error);
      return {
        field_patterns: {},
        common_structures: [],
        validation_rules: [],
        confidence_factors: []
      };
    }
  }

  /**
   * Enhance prompt with RAG context
   */
  async enhancePromptWithContext(analysisResult, contextData) {
    try {
      const basePrompt = analysisResult.prompt || this.getDefaultPrompt();

      // Build context enhancement
      const contextEnhancements = [];

      // Add field pattern context
      if (Object.keys(contextData.field_patterns).length > 0) {
        contextEnhancements.push(
          "Based on similar documents, pay special attention to these field patterns:"
        );

        Object.entries(contextData.field_patterns).forEach(([field, patterns]) => {
          const commonValues = patterns.slice(0, 3).map(p => p.value);
          contextEnhancements.push(
            `- ${field}: commonly found as ${commonValues.join(', ')}`
          );
        });
      }

      // Add structural context
      if (contextData.common_structures.length > 0) {
        contextEnhancements.push(
          "\nCommon document structures found in similar documents:"
        );

        contextData.common_structures.slice(0, 2).forEach((struct, index) => {
          contextEnhancements.push(
            `- Structure ${index + 1}: ${JSON.stringify(struct.structure)}`
          );
        });
      }

      // Add validation context
      if (contextData.validation_rules.length > 0) {
        contextEnhancements.push(
          "\nValidation patterns from successful extractions:"
        );

        contextData.validation_rules.slice(0, 2).forEach((rule, index) => {
          contextEnhancements.push(
            `- Rule ${index + 1}: ${JSON.stringify(rule.rules)}`
          );
        });
      }

      // Combine base prompt with enhancements
      const enhancedPrompt = [
        basePrompt,
        "\n--- RAG CONTEXT ENHANCEMENT ---",
        ...contextEnhancements,
        "\n--- END RAG CONTEXT ---",
        "\nUse this context to improve extraction accuracy and confidence."
      ].join('\n');

      console.log(`✨ Enhanced prompt with ${contextEnhancements.length} context elements`);
      return enhancedPrompt;
    } catch (error) {
      console.error('❌ Failed to enhance prompt with context:', error);
      return analysisResult.prompt || this.getDefaultPrompt();
    }
  }

  /**
   * Calculate relevance score for a document
   */
  calculateRelevanceScore(document, similarity) {
    let score = similarity;

    // Boost score for recent documents
    if (document.processed_at) {
      const daysSinceProcessed = (Date.now() - new Date(document.processed_at).getTime()) / (1000 * 60 * 60 * 24);
      if (daysSinceProcessed < 7) {
        score += 0.1; // Recent documents get boost
      }
    }

    // Boost score for high-accuracy documents
    if (document.accuracy && document.accuracy > 0.8) {
      score += 0.05;
    }

    // Boost score for documents with similar file types
    if (document.file_type && document.file_type === 'application/pdf') {
      score += 0.02;
    }

    return Math.min(score, 1.0); // Cap at 1.0
  }

  /**
   * Calculate confidence factors for RAG enhancement
   */
  calculateConfidenceFactors(similarDocuments, analysisResult) {
    const factors = [];

    if (similarDocuments.length > 0) {
      const avgSimilarity = similarDocuments.reduce((sum, doc) => sum + doc.similarity, 0) / similarDocuments.length;
      factors.push({
        factor: 'document_similarity',
        value: avgSimilarity,
        impact: avgSimilarity > 0.8 ? 'high' : avgSimilarity > 0.6 ? 'medium' : 'low'
      });
    }

    if (similarDocuments.some(doc => doc.accuracy > 0.8)) {
      factors.push({
        factor: 'high_accuracy_precedent',
        value: 0.9,
        impact: 'high'
      });
    }

    return factors;
  }

  /**
   * Calculate overall RAG confidence
   */
  calculateRAGConfidence(similarDocuments, contextData) {
    if (!similarDocuments || similarDocuments.length === 0) {
      return 0.1; // Low confidence without similar documents
    }

    const avgSimilarity = similarDocuments.reduce((sum, doc) => sum + doc.similarity, 0) / similarDocuments.length;
    const contextRichness = Object.keys(contextData.field_patterns).length / 10; // Normalize to 0-1
    const documentCount = Math.min(similarDocuments.length / this.maxSimilarDocuments, 1);

    return Math.min((avgSimilarity * 0.5) + (contextRichness * 0.3) + (documentCount * 0.2), 1.0);
  }

  /**
   * Get default prompt for document analysis
   */
  getDefaultPrompt() {
    return `Analyze this document and extract structured data including:
- Invoice number, date, due date
- Seller and buyer information
- Line items with descriptions, quantities, prices
- Tax information and totals
- Currency and payment terms

Provide high confidence scores for extracted fields.`;
  }
}

// Export singleton instance
export const ragService = new RAGService();
export default ragService;
