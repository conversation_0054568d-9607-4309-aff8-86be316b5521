import CryptoJS from 'crypto-js';

/**
 * Encryption service for secure API key storage
 * Uses AES-256 encryption with Chrome extension storage
 */
export class EncryptionService {
  constructor() {
    // Generate or retrieve encryption key from Chrome storage
    this.encryptionKey = null;
    this.initialized = false;
  }

  /**
   * Initialize encryption service
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      if (this.initialized) { return; }

      // Try to get existing encryption key from storage
      let encryptionKey = null;

      if (chrome?.storage?.local) {
        const result = await chrome.storage.local.get(['encryptionKey']);
        encryptionKey = result.encryptionKey;
      }

      // Generate new key if none exists
      if (!encryptionKey) {
        encryptionKey = this.generateEncryptionKey();

        // Store the key securely
        if (chrome?.storage?.local) {
          await chrome.storage.local.set({ encryptionKey });
        }
      }

      this.encryptionKey = encryptionKey;
      this.initialized = true;

      console.log('🔐 Encryption service initialized');
    } catch (error) {
      console.error('❌ Failed to initialize encryption service:', error);
      throw error;
    }
  }

  /**
   * Generate a secure encryption key
   * @returns {string} - Base64 encoded encryption key
   */
  generateEncryptionKey() {
    // Generate 256-bit key for AES-256
    const key = CryptoJS.lib.WordArray.random(256 / 8);
    return CryptoJS.enc.Base64.stringify(key);
  }

  /**
   * Encrypt sensitive data
   * @param {string} plaintext - Data to encrypt
   * @returns {Promise<string>} - Encrypted data
   */
  async encrypt(plaintext) {
    try {
      await this.initialize();

      if (!plaintext || typeof plaintext !== 'string') {
        throw new Error('Invalid plaintext for encryption');
      }

      // Encrypt using AES-256
      const encrypted = CryptoJS.AES.encrypt(plaintext, this.encryptionKey).toString();

      return encrypted;
    } catch (error) {
      console.error('❌ Encryption failed:', error);
      throw new Error('Failed to encrypt data');
    }
  }

  /**
   * Decrypt sensitive data
   * @param {string} ciphertext - Encrypted data
   * @returns {Promise<string>} - Decrypted data
   */
  async decrypt(ciphertext) {
    try {
      await this.initialize();

      if (!ciphertext || typeof ciphertext !== 'string') {
        throw new Error('Invalid ciphertext for decryption');
      }

      // Decrypt using AES-256
      const decrypted = CryptoJS.AES.decrypt(ciphertext, this.encryptionKey);
      const plaintext = decrypted.toString(CryptoJS.enc.Utf8);

      if (!plaintext) {
        throw new Error('Failed to decrypt data - invalid key or corrupted data');
      }

      return plaintext;
    } catch (error) {
      console.error('❌ Decryption failed:', error);
      throw new Error('Failed to decrypt data');
    }
  }

  /**
   * Encrypt API keys object
   * @param {Object} apiKeys - Object containing API keys
   * @returns {Promise<Object>} - Object with encrypted API keys
   */
  async encryptApiKeys(apiKeys) {
    try {
      const encrypted = {};

      for (const [provider, key] of Object.entries(apiKeys)) {
        if (key && key.trim()) {
          encrypted[provider] = await this.encrypt(key.trim());
        } else {
          encrypted[provider] = '';
        }
      }

      return encrypted;
    } catch (error) {
      console.error('❌ Failed to encrypt API keys:', error);
      throw error;
    }
  }

  /**
   * Decrypt API keys object
   * @param {Object} encryptedApiKeys - Object containing encrypted API keys
   * @returns {Promise<Object>} - Object with decrypted API keys
   */
  async decryptApiKeys(encryptedApiKeys) {
    try {
      const decrypted = {};

      for (const [provider, encryptedKey] of Object.entries(encryptedApiKeys)) {
        if (encryptedKey && encryptedKey.trim()) {
          try {
            decrypted[provider] = await this.decrypt(encryptedKey);
          } catch (error) {
            console.warn(`⚠️ Failed to decrypt ${provider} API key:`, error.message);
            decrypted[provider] = '';
          }
        } else {
          decrypted[provider] = '';
        }
      }

      return decrypted;
    } catch (error) {
      console.error('❌ Failed to decrypt API keys:', error);
      throw error;
    }
  }

  /**
   * Securely clear encryption key (for logout/reset)
   * @returns {Promise<void>}
   */
  async clearEncryptionKey() {
    try {
      this.encryptionKey = null;
      this.initialized = false;

      if (chrome?.storage?.local) {
        await chrome.storage.local.remove(['encryptionKey']);
      }

      console.log('🔐 Encryption key cleared');
    } catch (error) {
      console.error('❌ Failed to clear encryption key:', error);
      throw error;
    }
  }

  /**
   * Validate that encryption/decryption is working
   * @returns {Promise<boolean>} - True if encryption is working
   */
  async validateEncryption() {
    try {
      const testData = 'test-encryption-validation';
      const encrypted = await this.encrypt(testData);
      const decrypted = await this.decrypt(encrypted);

      return decrypted === testData;
    } catch (error) {
      console.error('❌ Encryption validation failed:', error);
      return false;
    }
  }
}

// Export singleton instance
export const encryptionService = new EncryptionService();

// Self-test function for development
export async function testEncryptionService() {
  console.log('\n🔐 Testing Encryption Service...');

  try {
    const service = new EncryptionService();

    // Test basic encryption/decryption
    const testData = 'sk-test-api-key-12345';
    console.log('📝 Original data:', testData);

    const encrypted = await service.encrypt(testData);
    console.log('🔒 Encrypted data:', encrypted.substring(0, 20) + '...');

    const decrypted = await service.decrypt(encrypted);
    console.log('🔓 Decrypted data:', decrypted);

    console.log('✅ Basic encryption test:', testData === decrypted ? 'PASSED' : 'FAILED');

    // Test API keys encryption
    const apiKeys = {
      deepseek: 'sk-deepseek-test-key',
      openai: 'sk-openai-test-key',
      fakturownia: 'fakturownia-api-key',
      infakt: ''
    };

    const encryptedKeys = await service.encryptApiKeys(apiKeys);
    console.log('🔒 Encrypted API keys:', Object.keys(encryptedKeys));

    const decryptedKeys = await service.decryptApiKeys(encryptedKeys);
    console.log('🔓 Decrypted API keys match:', JSON.stringify(apiKeys) === JSON.stringify(decryptedKeys) ? 'PASSED' : 'FAILED');

    // Test validation
    const isValid = await service.validateEncryption();
    console.log('✅ Encryption validation:', isValid ? 'PASSED' : 'FAILED');

    console.log('🎉 Encryption service tests completed!');

  } catch (error) {
    console.error('❌ Encryption service test failed:', error);
  }
}

// Run tests if this file is executed directly
if (typeof window !== 'undefined' && window.location?.search?.includes('test=encryption')) {
  testEncryptionService();
}
