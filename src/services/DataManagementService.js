/**
 * Data Management Service
 * Handles data operations: clear, export, import, reset
 */
export class DataManagementService {
  constructor() {
    this.storageKeys = [
      'settings',
      'encryptedApiKeys',
      'invoices',
      'documents',
      'cache',
      'processingHistory',
      'uploadHistory'
    ];
  }

  /**
   * Get storage usage statistics
   * @returns {Promise<Object>} Storage statistics
   */
  async getStorageStats() {
    try {
      if (!chrome?.storage?.local) {
        return this.getFallbackStorageStats();
      }

      const result = await chrome.storage.local.get(null);
      const totalSize = JSON.stringify(result).length;

      const stats = {
        totalSize,
        totalSizeFormatted: this.formatBytes(totalSize),
        itemCount: Object.keys(result).length,
        items: {}
      };

      // Calculate size per storage key
      for (const key of this.storageKeys) {
        if (result[key]) {
          const size = JSON.stringify(result[key]).length;
          stats.items[key] = {
            size,
            sizeFormatted: this.formatBytes(size),
            itemCount: Array.isArray(result[key]) ? result[key].length : 1,
            lastModified: result[key].lastModified || 'Unknown'
          };
        } else {
          stats.items[key] = {
            size: 0,
            sizeFormatted: '0 B',
            itemCount: 0,
            lastModified: 'Never'
          };
        }
      }

      return stats;
    } catch (error) {
      console.error('❌ Failed to get storage stats:', error);
      throw error;
    }
  }

  /**
   * Clear all stored data
   * @param {Object} options - Clear options
   * @returns {Promise<Object>} Operation result
   */
  async clearAllData(options = {}) {
    try {
      const {
        keepSettings = false,
        keepApiKeys = false,
        keepDocuments = false
      } = options;

      const keysToRemove = [];

      if (!keepSettings) {
        keysToRemove.push('settings');
      }

      if (!keepApiKeys) {
        keysToRemove.push('encryptedApiKeys');
      }

      if (!keepDocuments) {
        keysToRemove.push('invoices', 'documents', 'processingHistory', 'uploadHistory');
      }

      // Always clear cache
      keysToRemove.push('cache');

      if (chrome?.storage?.local) {
        await chrome.storage.local.remove(keysToRemove);
      } else {
        // Fallback for development
        keysToRemove.forEach(key => {
          localStorage.removeItem(`mvat_${key}`);
        });
      }

      console.log('🗑️ Data cleared successfully:', keysToRemove);

      return {
        success: true,
        clearedKeys: keysToRemove,
        message: `Cleared ${keysToRemove.length} data categories`
      };
    } catch (error) {
      console.error('❌ Failed to clear data:', error);
      throw error;
    }
  }

  /**
   * Export settings to JSON file
   * @param {Object} settings - Settings to export
   * @returns {Promise<Object>} Export result
   */
  async exportSettings(settings) {
    try {
      // Remove sensitive data from export
      const exportData = {
        ...settings,
        apiKeys: {} // Don't export API keys for security
      };

      const exportObject = {
        version: '1.0',
        exportDate: new Date().toISOString(),
        application: 'MVAT Chrome Extension',
        data: exportData
      };

      const jsonString = JSON.stringify(exportObject, null, 2);
      const blob = new Blob([jsonString], { type: 'application/json' });

      const filename = `mvat-settings-${new Date().toISOString().split('T')[0]}.json`;

      // Create download link
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      link.click();

      // Cleanup
      URL.revokeObjectURL(url);

      return {
        success: true,
        filename,
        size: jsonString.length,
        message: 'Settings exported successfully'
      };
    } catch (error) {
      console.error('❌ Failed to export settings:', error);
      throw error;
    }
  }

  /**
   * Import settings from JSON file
   * @param {File} file - JSON file to import
   * @returns {Promise<Object>} Import result with settings data
   */
  async importSettings(file) {
    try {
      if (!file || file.type !== 'application/json') {
        throw new Error('Please select a valid JSON file');
      }

      const text = await file.text();
      const importData = JSON.parse(text);

      // Validate import structure
      if (!importData.data || !importData.version) {
        throw new Error('Invalid settings file format');
      }

      // Validate settings structure
      const settings = importData.data;
      this.validateSettingsStructure(settings);

      return {
        success: true,
        settings,
        version: importData.version,
        exportDate: importData.exportDate,
        message: 'Settings imported successfully'
      };
    } catch (error) {
      console.error('❌ Failed to import settings:', error);
      throw error;
    }
  }

  /**
   * Reset settings to defaults
   * @param {Object} defaultSettings - Default settings object
   * @returns {Promise<Object>} Reset result
   */
  async resetToDefaults(defaultSettings) {
    try {
      if (chrome?.storage?.local) {
        await chrome.storage.local.set({ settings: defaultSettings });
      } else {
        localStorage.setItem('mvat_settings', JSON.stringify(defaultSettings));
      }

      return {
        success: true,
        message: 'Settings reset to defaults successfully'
      };
    } catch (error) {
      console.error('❌ Failed to reset settings:', error);
      throw error;
    }
  }

  /**
   * Validate settings structure
   * @param {Object} settings - Settings to validate
   * @throws {Error} If settings structure is invalid
   */
  validateSettingsStructure(settings) {
    const requiredSections = ['company', 'display', 'processing'];

    for (const section of requiredSections) {
      if (!settings[section] || typeof settings[section] !== 'object') {
        throw new Error(`Missing or invalid ${section} section`);
      }
    }

    // Validate specific fields
    if (settings.display && !settings.display.currency) {
      throw new Error('Display settings missing currency');
    }

    if (settings.processing && !settings.processing.ocrLanguage) {
      throw new Error('Processing settings missing OCR language');
    }
  }

  /**
   * Format bytes to human readable string
   * @param {number} bytes - Bytes to format
   * @returns {string} Formatted string
   */
  formatBytes(bytes) {
    if (bytes === 0) { return '0 B'; }

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Get fallback storage stats for development
   * @returns {Object} Fallback stats
   */
  getFallbackStorageStats() {
    const stats = {
      totalSize: 0,
      totalSizeFormatted: '0 B',
      itemCount: 0,
      items: {}
    };

    for (const key of this.storageKeys) {
      const item = localStorage.getItem(`mvat_${key}`);
      const size = item ? item.length : 0;

      stats.items[key] = {
        size,
        sizeFormatted: this.formatBytes(size),
        itemCount: item ? 1 : 0,
        lastModified: 'Unknown'
      };

      stats.totalSize += size;
    }

    stats.totalSizeFormatted = this.formatBytes(stats.totalSize);
    stats.itemCount = Object.keys(stats.items).filter(key => stats.items[key].size > 0).length;

    return stats;
  }
}

// Create singleton instance
export const dataManagementService = new DataManagementService();
