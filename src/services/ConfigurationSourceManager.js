/**
 * Configuration Source Manager
 *
 * Simplified configuration source selector that manages loading configuration
 * from multiple sources through the unified environment loading system.
 *
 * CONSOLIDATION: Simplified to source selection only, delegates actual loading
 * to EnvironmentConfigService and EnvLoader for unified architecture.
 *
 * ASSIGNMENT-062: Environment Loading System Consolidation
 * Epic: EPIC-006 - Code Consolidation & Architecture Cleanup
 */

import { environmentConfig } from './EnvironmentConfigService.js';
import { envLoader } from '../utils/EnvLoader.js';

/**
 * Configuration source types
 */
export const CONFIG_SOURCES = {
  ENVIRONMENT_SERVICE: 'environment_service',
  CHROME_STORAGE: 'chrome_storage',
  ENV_FILE: 'env_file',
  JSON_CONFIG: 'json_config'
};

/**
 * Configuration Source Manager Class
 */
class ConfigurationSourceManager {
  constructor() {
    this.currentSource = CONFIG_SOURCES.ENVIRONMENT_SERVICE;
    this.loadedConfig = null;
    this.isLoading = false;
    this.lastLoadTime = null;
    this.loadErrors = {};
  }

  /**
   * Get available configuration sources
   * @returns {Array} Array of source objects
   */
  getAvailableSources() {
    return [
      {
        id: CONFIG_SOURCES.ENVIRONMENT_SERVICE,
        name: 'Environment Service',
        description: 'Default environment configuration service',
        icon: '⚙️',
        priority: 1
      },
      {
        id: CONFIG_SOURCES.CHROME_STORAGE,
        name: 'Chrome Storage',
        description: 'Configuration stored in Chrome extension storage',
        icon: '💾',
        priority: 2
      },
      {
        id: CONFIG_SOURCES.ENV_FILE,
        name: '.env File',
        description: 'Environment variables from .env file',
        icon: '📄',
        priority: 3
      },
      {
        id: CONFIG_SOURCES.JSON_CONFIG,
        name: 'JSON Config',
        description: 'Configuration from JSON file',
        icon: '📋',
        priority: 4
      }
    ];
  }

  /**
   * Get current configuration source
   * @returns {string} Current source ID
   */
  getCurrentSource() {
    return this.currentSource;
  }

  /**
   * Set current configuration source
   * @param {string} sourceId - Source ID to set as current
   */
  setCurrentSource(sourceId) {
    if (Object.values(CONFIG_SOURCES).includes(sourceId)) {
      this.currentSource = sourceId;
      console.log(`🔧 ConfigurationSourceManager: Set current source to ${sourceId}`);
    } else {
      throw new Error(`Invalid configuration source: ${sourceId}`);
    }
  }

  /**
   * Load configuration from current source
   * @returns {Promise<Object>} Configuration object
   */
  async loadConfiguration() {
    if (this.isLoading) {
      console.log('⏳ Configuration loading already in progress...');
      return this.loadedConfig;
    }

    try {
      this.isLoading = true;
      console.log(`🔧 ConfigurationSourceManager: Loading from ${this.currentSource}`);

      let config = null;

      switch (this.currentSource) {
        case CONFIG_SOURCES.ENVIRONMENT_SERVICE:
          config = await this._loadFromEnvironmentService();
          break;
        case CONFIG_SOURCES.CHROME_STORAGE:
          config = await this._loadFromChromeStorage();
          break;
        case CONFIG_SOURCES.ENV_FILE:
          config = await this._loadFromEnvFile();
          break;
        case CONFIG_SOURCES.JSON_CONFIG:
          config = await this._loadFromJsonConfig();
          break;
        default:
          throw new Error(`Unsupported configuration source: ${this.currentSource}`);
      }

      this.loadedConfig = config;
      this.lastLoadTime = new Date();
      delete this.loadErrors[this.currentSource];

      console.log(`✅ ConfigurationSourceManager: Successfully loaded from ${this.currentSource}`);
      return config;

    } catch (error) {
      console.error(`❌ ConfigurationSourceManager: Failed to load from ${this.currentSource}:`, error);
      this.loadErrors[this.currentSource] = {
        error: error.message,
        timestamp: new Date()
      };
      throw error;
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Load configuration from EnvironmentConfigService
   * @private
   * @returns {Promise<Object>} Configuration object
   */
  async _loadFromEnvironmentService() {
    console.log('🔧 Loading from EnvironmentConfigService...');

    if (!environmentConfig.isLoaded) {
      await environmentConfig.initialize();
    }

    return environmentConfig.getAll();
  }

  /**
   * Load configuration from Chrome storage
   * @private
   * @returns {Promise<Object>} Configuration object
   */
  async _loadFromChromeStorage() {
    console.log('🔧 Loading from Chrome storage...');

    if (!chrome?.storage?.local) {
      throw new Error('Chrome storage API not available');
    }

    return new Promise((resolve, reject) => {
      chrome.storage.local.get(['environment_config', 'settings'], (result) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
          return;
        }

        const config = result.environment_config || result.settings || {};
        console.log(`✅ Loaded ${Object.keys(config).length} configuration items from Chrome storage`);
        resolve(config);
      });
    });
  }

  /**
   * Load configuration from .env file (delegated to unified EnvLoader)
   * @private
   * @returns {Promise<Object>} Configuration object
   */
  async _loadFromEnvFile() {
    console.log('🔧 ConfigurationSourceManager: Delegating .env loading to unified EnvLoader');

    try {
      // Use the unified environment loader
      if (!envLoader.isReady()) {
        await envLoader.initialize();
      }

      const envVars = envLoader.getAll();
      if (Object.keys(envVars).length === 0) {
        throw new Error('No environment variables found');
      }

      console.log(`✅ ConfigurationSourceManager: Loaded ${Object.keys(envVars).length} variables via EnvLoader`);

      // Convert env vars to configuration structure
      return this._convertEnvVarsToConfig(envVars);

    } catch (error) {
      console.error('❌ ConfigurationSourceManager: Failed to load from .env file:', error.message);
      throw error;
    }
  }

  /**
   * Load configuration from JSON config file
   * @private
   * @returns {Promise<Object>} Configuration object
   */
  async _loadFromJsonConfig() {
    console.log('🔧 Loading from JSON config file...');

    try {
      const response = await fetch('/config.json');
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const config = await response.json();
      console.log('✅ Loaded configuration from JSON file');
      return config;

    } catch (error) {
      throw new Error(`Failed to load JSON config: ${error.message}`);
    }
  }

  // REMOVED: _parseEnvContent method
  // Now handled by unified EnvLoader.js to eliminate duplicate parsing logic

  /**
   * Convert environment variables to configuration structure
   * @private
   * @param {Object} envVars - Environment variables
   * @returns {Object} Configuration object
   */
  _convertEnvVarsToConfig(envVars) {
    // Create a temporary EnvironmentConfigService instance to build configuration
    // Since _buildConfiguration is private, we'll create the structure manually
    return {
      company: {
        name: envVars.COMPANY_NAME || 'MVAT Solutions',
        legalName: envVars.COMPANY_LEGAL_NAME || 'MVAT Solutions Sp. z o.o.',
        nip: envVars.COMPANY_NIP,
        regon: envVars.COMPANY_REGON,
        krs: envVars.COMPANY_KRS,
        address: {
          street: envVars.COMPANY_ADDRESS_STREET,
          city: envVars.COMPANY_ADDRESS_CITY,
          postalCode: envVars.COMPANY_ADDRESS_POSTAL_CODE,
          country: envVars.COMPANY_ADDRESS_COUNTRY || 'Poland',
          countryCode: envVars.COMPANY_ADDRESS_COUNTRY_CODE || 'PL'
        },
        contact: {
          email: envVars.COMPANY_EMAIL,
          phone: envVars.COMPANY_PHONE,
          website: envVars.COMPANY_WEBSITE,
          supportEmail: envVars.COMPANY_SUPPORT_EMAIL
        }
      },
      app: {
        environment: envVars.NODE_ENV || 'development',
        version: envVars.APP_VERSION || '1.0.0',
        buildNumber: parseInt(envVars.APP_BUILD_NUMBER) || 1,
        debugMode: envVars.DEBUG_MODE === 'true',
        logLevel: envVars.LOG_LEVEL || 'info'
      },
      apiKeys: {
        deepseek: {
          key: envVars.DEEPSEEK_API_KEY,
          model: envVars.DEEPSEEK_MODEL || 'deepseek-chat',
          maxTokens: parseInt(envVars.DEEPSEEK_MAX_TOKENS) || 4000
        },
        stripe: {
          publishableKey: envVars.STRIPE_PUBLISHABLE_KEY,
          secretKey: envVars.STRIPE_SECRET_KEY,
          webhookSecret: envVars.STRIPE_WEBHOOK_SECRET
        },
        fakturownia: {
          apiKey: envVars.FAKTUROWNIA_API_KEY,
          accountName: envVars.FAKTUROWNIA_ACCOUNT_NAME
        }
      },
      features: {
        enableAI: envVars.ENABLE_AI_FEATURES !== 'false',
        enableOCR: envVars.ENABLE_OCR !== 'false',
        enableRAG: envVars.ENABLE_RAG_FEATURES !== 'false',
        enableSubscription: envVars.ENABLE_SUBSCRIPTION === 'true',
        enableAnalytics: envVars.ENABLE_ANALYTICS !== 'false'
      },
      subscription: {
        tier: envVars.SUBSCRIPTION_TIER || 'starter',
        maxDocuments: parseInt(envVars.MAX_DOCUMENTS_PER_MONTH) || 100,
        maxAPIRequests: parseInt(envVars.MAX_API_REQUESTS_PER_MONTH) || 1000,
        enablePremiumFeatures: envVars.ENABLE_PREMIUM_FEATURES === 'true'
      },
      localization: {
        defaultLanguage: envVars.DEFAULT_LANGUAGE || 'en',
        defaultCurrency: envVars.DEFAULT_CURRENCY || 'EUR',
        defaultTimezone: envVars.DEFAULT_TIMEZONE || 'Europe/Warsaw',
        supportedLanguages: (envVars.SUPPORTED_LANGUAGES || 'en,pl,de,fr').split(','),
        vatRates: {
          standard: parseFloat(envVars.VAT_RATE_STANDARD) || 23,
          reduced: parseFloat(envVars.VAT_RATE_REDUCED) || 8,
          zero: parseFloat(envVars.VAT_RATE_ZERO) || 0
        }
      }
    };
  }

  /**
   * Get loading status
   * @returns {Object} Loading status information
   */
  getLoadingStatus() {
    return {
      isLoading: this.isLoading,
      currentSource: this.currentSource,
      lastLoadTime: this.lastLoadTime,
      hasConfig: !!this.loadedConfig,
      errors: this.loadErrors
    };
  }

  /**
   * Test all available sources
   * @returns {Promise<Object>} Test results for all sources
   */
  async testAllSources() {
    console.log('🧪 ConfigurationSourceManager: Testing all configuration sources...');
    const results = {};
    const originalSource = this.currentSource;

    for (const source of this.getAvailableSources()) {
      console.log(`🔍 Testing source: ${source.name} (${source.id})`);
      try {
        this.setCurrentSource(source.id);
        const startTime = Date.now();

        // Clear any previous errors for this source
        delete this.loadErrors[source.id];

        await this.loadConfiguration();
        const loadTime = Date.now() - startTime;

        results[source.id] = {
          success: true,
          loadTime,
          configSize: this.loadedConfig ? Object.keys(this.loadedConfig).length : 0,
          message: `Successfully loaded ${this.loadedConfig ? Object.keys(this.loadedConfig).length : 0} configuration items`
        };

        console.log(`✅ Source ${source.name}: Success (${loadTime}ms, ${results[source.id].configSize} items)`);

      } catch (error) {
        console.error(`❌ Source ${source.name}: Failed - ${error.message}`);

        results[source.id] = {
          success: false,
          error: error.message,
          errorType: error.constructor.name,
          timestamp: new Date().toISOString()
        };

        // Store error for status reporting
        this.loadErrors[source.id] = {
          error: error.message,
          timestamp: new Date()
        };
      }
    }

    // Restore original source
    this.setCurrentSource(originalSource);

    console.log('🏁 ConfigurationSourceManager: Source testing completed');
    console.log('📊 Results summary:', Object.entries(results).map(([id, result]) =>
      `${id}: ${result.success ? '✅' : '❌'}`
    ).join(', '));

    return results;
  }
}

// Create and export singleton instance
export const configurationSourceManager = new ConfigurationSourceManager();

// Export class for testing
export { ConfigurationSourceManager };
