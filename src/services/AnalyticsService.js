/**
 * Analytics Service
 *
 * Comprehensive analytics service for collecting, processing, and providing
 * business intelligence insights from document processing and AI analysis.
 */

import { AnalyticsData, ANALYTICS_EVENTS, ANALYTICS_CONFIG } from '../models/AnalyticsData.js';
import { StorageAPI } from '../api/StorageAPI.js';
import { format, subDays, startOfDay, endOfDay } from 'date-fns';

class AnalyticsService {
  constructor() {
    this.data = new AnalyticsData();
    this.events = [];
    this.listeners = new Map();
    this.isInitialized = false;
    this.storageKey = 'mvat_analytics_data';
    this.eventsKey = 'mvat_analytics_events';
    // Initialize StorageAPI instance
    this.storageAPI = new StorageAPI();
  }

  /**
   * Initialize analytics service
   */
  async initialize() {
    try {
      console.log('🔧 AnalyticsService: Initializing analytics service...');

      // Load existing analytics data
      await this.loadData();

      // Set up event listeners
      this.setupEventListeners();

      this.isInitialized = true;
      console.log('✅ AnalyticsService: Analytics service initialized successfully');

      return true;
    } catch (error) {
      console.error('❌ AnalyticsService: Failed to initialize:', error);
      return false;
    }
  }

  /**
   * Load analytics data from storage
   */
  async loadData() {
    try {
      const storedData = await this.storageAPI.get(this.storageKey);
      const storedEvents = await this.storageAPI.get(this.eventsKey);

      if (storedData && storedData[this.storageKey]) {
        this.data = AnalyticsData.import(storedData[this.storageKey]);
        console.log('📊 AnalyticsService: Loaded existing analytics data');
      } else {
        // Initialize with sample data for demonstration
        this.data = new AnalyticsData();
        this.generateSampleData();
        console.log('📊 AnalyticsService: Initialized with sample data');
      }

      if (storedEvents && storedEvents[this.eventsKey]) {
        this.events = storedEvents[this.eventsKey];
        console.log(`📊 AnalyticsService: Loaded ${this.events.length} analytics events`);
      } else {
        this.events = [];
        this.generateSampleEvents();
        console.log('📊 AnalyticsService: Generated sample events');
      }

      // Clean up old events
      this.cleanupOldEvents();

    } catch (error) {
      console.error('❌ AnalyticsService: Failed to load data:', error);
      this.data = new AnalyticsData();
      this.events = [];
      this.generateSampleData();
      this.generateSampleEvents();
    }
  }

  /**
   * Save analytics data to storage
   */
  async saveData() {
    try {
      await this.storageAPI.set({ [this.storageKey]: this.data.export() });
      await this.storageAPI.set({ [this.eventsKey]: this.events });
      console.log('💾 AnalyticsService: Analytics data saved successfully');
    } catch (error) {
      console.error('❌ AnalyticsService: Failed to save data:', error);
    }
  }

  /**
   * Record analytics event
   */
  async recordEvent(eventType, eventData = {}) {
    try {
      const event = {
        id: this.generateEventId(),
        type: eventType,
        timestamp: new Date().toISOString(),
        data: eventData
      };

      this.events.push(event);

      // Process event for metrics
      await this.processEvent(event);

      // Notify listeners
      this.notifyListeners(eventType, event);

      // Save data periodically
      if (this.events.length % 10 === 0) {
        await this.saveData();
      }

      console.log(`📊 AnalyticsService: Recorded event ${eventType}:`, eventData);

    } catch (error) {
      console.error('❌ AnalyticsService: Failed to record event:', error);
    }
  }

  /**
   * Process event for metrics calculation
   */
  async processEvent(event) {
    const { type, data } = event;

    switch (type) {
      case ANALYTICS_EVENTS.DOCUMENT_PROCESSED:
        this.data.documents.addDocument(
          data.success,
          data.processingTime,
          data.documentType,
          data.error
        );

        if (data.success && data.extractedData) {
          this.data.business.addDocument(
            data.extractedData.totalAmount || 0,
            data.extractedData.vendor,
            data.extractedData.category,
            data.extractedData.date || new Date().toISOString()
          );
        }
        break;

      case ANALYTICS_EVENTS.AI_ANALYSIS_COMPLETED:
        this.data.ai.addAnalysis(
          data.success,
          data.analysisTime,
          data.confidence,
          data.analysisType,
          data.apiProvider,
          data.tokens
        );
        break;

      case ANALYTICS_EVENTS.FEATURE_USED:
        this.data.system.recordFeatureUsage(data.feature);
        break;

      case ANALYTICS_EVENTS.ERROR_OCCURRED:
        this.data.system.errorRate += 1;
        break;
    }

    this.data.touch();
  }

  /**
   * Get analytics dashboard data
   */
  getDashboardData() {
    return {
      summary: this.data.getSummary(),
      documents: {
        total: this.data.documents.totalDocuments,
        successRate: this.data.documents.successRate,
        averageProcessingTime: this.data.documents.averageProcessingTime,
        documentTypes: this.data.documents.documentTypes,
        recentErrors: this.data.documents.processingErrors.slice(-5)
      },
      ai: {
        total: this.data.ai.totalAnalyses,
        successRate: this.data.ai.successRate,
        averageConfidence: this.data.ai.averageConfidence,
        averageAnalysisTime: this.data.ai.averageAnalysisTime,
        analysisTypes: this.data.ai.analysisTypes,
        apiUsage: this.data.ai.apiUsage
      },
      business: {
        totalValue: this.data.business.totalValue,
        averageValue: this.data.business.averageValue,
        documentCount: this.data.business.documentCount,
        valueByMonth: this.data.business.valueByMonth,
        topVendors: this.getTopVendors(),
        topCategories: this.getTopCategories(),
        insights: this.data.business.insights.slice(-5)
      },
      system: {
        featureUsage: this.data.system.featureUsage,
        errorRate: this.data.system.errorRate,
        averageResponseTime: this.getAverageResponseTime(),
        memoryUsage: this.data.system.memoryUsage.slice(-20)
      }
    };
  }

  /**
   * Get chart data for visualizations
   */
  getChartData() {
    return {
      documentProcessingTrend: this.getDocumentProcessingTrend(),
      aiPerformanceTrend: this.getAIPerformanceTrend(),
      businessValueTrend: this.getBusinessValueTrend(),
      documentTypeDistribution: this.getDocumentTypeDistribution(),
      vendorDistribution: this.getVendorDistribution(),
      featureUsageChart: this.getFeatureUsageChart()
    };
  }

  /**
   * Get document processing trend data
   */
  getDocumentProcessingTrend() {
    const last30Days = Array.from({ length: 30 }, (_, i) => {
      const date = subDays(new Date(), 29 - i);
      const dateStr = format(date, 'yyyy-MM-dd');

      const dayEvents = this.events.filter(event =>
        event.type === ANALYTICS_EVENTS.DOCUMENT_PROCESSED &&
        event.timestamp.startsWith(dateStr)
      );

      return {
        date: dateStr,
        total: dayEvents.length,
        successful: dayEvents.filter(e => e.data.success).length,
        failed: dayEvents.filter(e => !e.data.success).length
      };
    });

    return last30Days;
  }

  /**
   * Get AI performance trend data
   */
  getAIPerformanceTrend() {
    const last30Days = Array.from({ length: 30 }, (_, i) => {
      const date = subDays(new Date(), 29 - i);
      const dateStr = format(date, 'yyyy-MM-dd');

      const dayEvents = this.events.filter(event =>
        event.type === ANALYTICS_EVENTS.AI_ANALYSIS_COMPLETED &&
        event.timestamp.startsWith(dateStr)
      );

      const confidenceScores = dayEvents
        .filter(e => e.data.success && e.data.confidence)
        .map(e => e.data.confidence);

      return {
        date: dateStr,
        total: dayEvents.length,
        averageConfidence: confidenceScores.length > 0
          ? confidenceScores.reduce((sum, score) => sum + score, 0) / confidenceScores.length
          : 0,
        successRate: dayEvents.length > 0
          ? (dayEvents.filter(e => e.data.success).length / dayEvents.length) * 100
          : 0
      };
    });

    return last30Days;
  }

  /**
   * Get business value trend data
   */
  getBusinessValueTrend() {
    const monthlyData = Object.entries(this.data.business.valueByMonth)
      .map(([month, value]) => ({
        month,
        value,
        documents: this.getDocumentCountForMonth(month)
      }))
      .sort((a, b) => a.month.localeCompare(b.month));

    return monthlyData;
  }

  /**
   * Get document type distribution
   */
  getDocumentTypeDistribution() {
    return Object.entries(this.data.documents.documentTypes)
      .map(([type, count]) => ({ type, count }))
      .sort((a, b) => b.count - a.count);
  }

  /**
   * Get vendor distribution
   */
  getVendorDistribution() {
    return Object.entries(this.data.business.valueByVendor)
      .map(([vendor, value]) => ({ vendor, value }))
      .sort((a, b) => b.value - a.value)
      .slice(0, 10); // Top 10 vendors
  }

  /**
   * Get feature usage chart data
   */
  getFeatureUsageChart() {
    return Object.entries(this.data.system.featureUsage)
      .map(([feature, count]) => ({ feature, count }))
      .sort((a, b) => b.count - a.count);
  }

  /**
   * Helper methods
   */
  getTopVendors(limit = 5) {
    return Object.entries(this.data.business.valueByVendor)
      .sort(([, a], [, b]) => b - a)
      .slice(0, limit)
      .map(([vendor, value]) => ({ vendor, value }));
  }

  getTopCategories(limit = 5) {
    return Object.entries(this.data.business.valueByCategory)
      .sort(([, a], [, b]) => b - a)
      .slice(0, limit)
      .map(([category, value]) => ({ category, value }));
  }

  getAverageResponseTime() {
    const times = this.data.system.responseTime;
    return times.length > 0
      ? times.reduce((sum, item) => sum + item.time, 0) / times.length
      : 0;
  }

  getDocumentCountForMonth(month) {
    return this.events.filter(event =>
      event.type === ANALYTICS_EVENTS.DOCUMENT_PROCESSED &&
      event.timestamp.startsWith(month)
    ).length;
  }

  /**
   * Utility methods
   */
  generateEventId() {
    return `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  cleanupOldEvents() {
    const cutoffDate = subDays(new Date(), ANALYTICS_CONFIG.retentionDays);
    this.events = this.events.filter(event =>
      new Date(event.timestamp) > cutoffDate
    );

    // Limit total events
    if (this.events.length > ANALYTICS_CONFIG.maxEvents) {
      this.events = this.events.slice(-ANALYTICS_CONFIG.maxEvents);
    }
  }

  setupEventListeners() {
    // Set up periodic data saving
    setInterval(() => {
      this.saveData();
    }, 60000); // Save every minute
  }

  notifyListeners(eventType, event) {
    const listeners = this.listeners.get(eventType) || [];
    listeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('❌ AnalyticsService: Listener error:', error);
      }
    });
  }

  addEventListener(eventType, listener) {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, []);
    }
    this.listeners.get(eventType).push(listener);
  }

  removeEventListener(eventType, listener) {
    const listeners = this.listeners.get(eventType) || [];
    const index = listeners.indexOf(listener);
    if (index > -1) {
      listeners.splice(index, 1);
    }
  }

  /**
   * Export analytics data
   */
  async exportData(format = 'json') {
    const data = {
      analytics: this.data.export(),
      events: this.events,
      exportedAt: new Date().toISOString(),
      format
    };

    if (format === 'json') {
      return JSON.stringify(data, null, 2);
    }

    // Add other export formats as needed
    return data;
  }

  /**
   * Generate sample data for demonstration
   */
  generateSampleData() {
    // Add sample document processing data
    for (let i = 0; i < 25; i++) {
      this.data.documents.addDocument(
        Math.random() > 0.1, // 90% success rate
        Math.random() * 5000 + 1000, // Processing time 1-6 seconds
        ['invoice', 'receipt', 'contract'][Math.floor(Math.random() * 3)],
        Math.random() > 0.9 ? new Error('Sample error') : null
      );
    }

    // Add sample AI analysis data
    for (let i = 0; i < 20; i++) {
      this.data.ai.addAnalysis(
        Math.random() > 0.05, // 95% success rate
        Math.random() * 3000 + 500, // Analysis time 0.5-3.5 seconds
        Math.random() * 30 + 70, // Confidence 70-100%
        ['document_analysis', 'field_extraction', 'classification'][Math.floor(Math.random() * 3)],
        'deepseek',
        Math.floor(Math.random() * 2000 + 500) // Tokens 500-2500
      );
    }

    // Add sample business data
    const vendors = ['Acme Corp', 'Tech Solutions', 'Office Supplies Inc', 'Energy Provider', 'Telecom Ltd'];
    const categories = ['Office Supplies', 'Utilities', 'Software', 'Services', 'Equipment'];

    for (let i = 0; i < 30; i++) {
      const value = Math.random() * 5000 + 100; // €100-€5100
      const vendor = vendors[Math.floor(Math.random() * vendors.length)];
      const category = categories[Math.floor(Math.random() * categories.length)];
      const date = new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000); // Last 90 days

      this.data.business.addDocument(value, vendor, category, date.toISOString());
    }

    // Add sample system metrics
    const features = ['upload', 'table_view', 'settings', 'export', 'ai_analysis'];
    features.forEach(feature => {
      for (let i = 0; i < Math.floor(Math.random() * 20 + 5); i++) {
        this.data.system.recordFeatureUsage(feature);
      }
    });
  }

  /**
   * Generate sample events for demonstration
   */
  generateSampleEvents() {
    const eventTypes = [
      ANALYTICS_EVENTS.DOCUMENT_PROCESSED,
      ANALYTICS_EVENTS.AI_ANALYSIS_COMPLETED,
      ANALYTICS_EVENTS.FEATURE_USED
    ];

    for (let i = 0; i < 50; i++) {
      const eventType = eventTypes[Math.floor(Math.random() * eventTypes.length)];
      const timestamp = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000); // Last 30 days

      const event = {
        id: `sample_${Date.now()}_${i}`,
        type: eventType,
        timestamp: timestamp.toISOString(),
        data: this.generateSampleEventData(eventType)
      };

      this.events.push(event);
    }
  }

  /**
   * Generate sample event data based on event type
   */
  generateSampleEventData(eventType) {
    switch (eventType) {
      case ANALYTICS_EVENTS.DOCUMENT_PROCESSED:
        return {
          success: Math.random() > 0.1,
          processingTime: Math.random() * 5000 + 1000,
          documentType: ['invoice', 'receipt', 'contract'][Math.floor(Math.random() * 3)],
          confidence: Math.random() * 30 + 70
        };

      case ANALYTICS_EVENTS.AI_ANALYSIS_COMPLETED:
        return {
          success: Math.random() > 0.05,
          analysisTime: Math.random() * 3000 + 500,
          confidence: Math.random() * 30 + 70,
          analysisType: 'document_analysis',
          apiProvider: 'deepseek',
          tokens: Math.floor(Math.random() * 2000 + 500)
        };

      case ANALYTICS_EVENTS.FEATURE_USED:
        return {
          feature: ['upload', 'table_view', 'settings', 'export'][Math.floor(Math.random() * 4)]
        };

      default:
        return {};
    }
  }

  /**
   * Reset analytics data
   */
  async resetData() {
    this.data = new AnalyticsData();
    this.events = [];
    await this.saveData();
    console.log('🔄 AnalyticsService: Analytics data reset');
  }
}

// Create singleton instance
const analyticsService = new AnalyticsService();

export default analyticsService;
