/**
 * PDFProgressTracker - Advanced progress tracking for PDF processing
 * Implements Task 2.2.2 - PDF Processing Enhancement
 *
 * Features:
 * - Granular progress tracking with detailed stages
 * - Progress persistence across browser sessions
 * - Memory usage monitoring
 * - Performance metrics collection
 * - Error state tracking
 * - Batch processing support
 */

export class PDFProgressTracker {
  constructor(options = {}) {
    this.config = {
      persistProgress: options.persistProgress !== false,
      trackMemory: options.trackMemory !== false,
      trackPerformance: options.trackPerformance !== false,
      storageKey: options.storageKey || 'mvat_pdf_progress',
      updateInterval: options.updateInterval || 100, // ms
      ...options
    };

    this.sessions = new Map(); // trackingId -> session data
    this.globalStats = {
      totalProcessed: 0,
      totalErrors: 0,
      averageProcessingTime: 0,
      memoryPeakUsage: 0
    };

    this.initialized = false;
  }

  /**
   * Initialize the progress tracker
   */
  async initialize() {
    if (this.initialized) { return; }

    try {
      // Load persisted progress if enabled
      if (this.config.persistProgress) {
        await this.loadPersistedProgress();
      }

      this.initialized = true;
      console.log('PDFProgressTracker initialized');
    } catch (error) {
      console.error('Failed to initialize PDFProgressTracker:', error);
      throw new Error(`Progress tracker initialization failed: ${error.message}`);
    }
  }

  /**
   * Start tracking progress for a new PDF processing session
   * @param {string} trackingId - Unique identifier for this processing session
   * @param {Object} options - Tracking options
   * @returns {Object} - Session tracker
   */
  startTracking(trackingId, options = {}) {
    const {
      fileName = 'unknown.pdf',
      fileSize = 0,
      totalPages = 0,
      onProgress = null,
      onComplete = null,
      onError = null
    } = options;

    const session = {
      trackingId,
      fileName,
      fileSize,
      totalPages,
      startTime: Date.now(),
      endTime: null,
      currentStage: 'initializing',
      currentProgress: 0,
      stageProgress: {},
      errors: [],
      warnings: [],
      memoryUsage: [],
      performanceMetrics: {},
      callbacks: {
        onProgress,
        onComplete,
        onError
      },
      status: 'active'
    };

    // Define processing stages with expected durations (percentages)
    session.stages = {
      initializing: { weight: 5, completed: false },
      loading: { weight: 10, completed: false },
      parsing: { weight: 15, completed: false },
      extracting: { weight: 50, completed: false },
      analyzing: { weight: 15, completed: false },
      finalizing: { weight: 5, completed: false }
    };

    this.sessions.set(trackingId, session);

    // Start memory monitoring if enabled
    if (this.config.trackMemory) {
      this.startMemoryMonitoring(trackingId);
    }

    // Persist initial state
    if (this.config.persistProgress) {
      this.persistProgress();
    }

    return this.createSessionTracker(trackingId);
  }

  /**
   * Create a session tracker interface
   * @param {string} trackingId - Session tracking ID
   * @returns {Object} - Session tracker interface
   */
  createSessionTracker(trackingId) {
    const session = this.sessions.get(trackingId);
    if (!session) {
      throw new Error(`Session not found: ${trackingId}`);
    }

    return {
      updateStage: (stage, progress = null) => this.updateStage(trackingId, stage, progress),
      updateProgress: (progress) => this.updateProgress(trackingId, progress),
      addError: (error) => this.addError(trackingId, error),
      addWarning: (warning) => this.addWarning(trackingId, warning),
      setMetadata: (metadata) => this.setMetadata(trackingId, metadata),
      complete: (result) => this.completeSession(trackingId, result),
      cancel: () => this.cancelSession(trackingId),
      getStatus: () => this.getSessionStatus(trackingId)
    };
  }

  /**
   * Update the current processing stage
   * @param {string} trackingId - Session tracking ID
   * @param {string} stage - Current stage name
   * @param {number} progress - Optional stage progress (0-100)
   */
  updateStage(trackingId, stage, progress = null) {
    const session = this.sessions.get(trackingId);
    if (!session || session.status !== 'active') { return; }

    // Mark previous stage as completed
    if (session.currentStage && session.stages[session.currentStage]) {
      session.stages[session.currentStage].completed = true;
    }

    session.currentStage = stage;

    if (progress !== null) {
      session.stageProgress[stage] = Math.max(0, Math.min(100, progress));
    }

    // Calculate overall progress
    this.calculateOverallProgress(trackingId);

    // Trigger callbacks
    this.triggerProgressCallback(trackingId);

    // Persist progress
    if (this.config.persistProgress) {
      this.persistProgress();
    }
  }

  /**
   * Update progress within the current stage
   * @param {string} trackingId - Session tracking ID
   * @param {number} progress - Progress percentage (0-100)
   */
  updateProgress(trackingId, progress) {
    const session = this.sessions.get(trackingId);
    if (!session || session.status !== 'active') { return; }

    const stage = session.currentStage;
    session.stageProgress[stage] = Math.max(0, Math.min(100, progress));

    // Calculate overall progress
    this.calculateOverallProgress(trackingId);

    // Trigger callbacks
    this.triggerProgressCallback(trackingId);
  }

  /**
   * Calculate overall progress based on stage weights
   * @param {string} trackingId - Session tracking ID
   */
  calculateOverallProgress(trackingId) {
    const session = this.sessions.get(trackingId);
    if (!session) { return; }

    let totalProgress = 0;
    let totalWeight = 0;

    for (const [stageName, stageInfo] of Object.entries(session.stages)) {
      totalWeight += stageInfo.weight;

      if (stageInfo.completed) {
        totalProgress += stageInfo.weight;
      } else if (stageName === session.currentStage) {
        const stageProgress = session.stageProgress[stageName] || 0;
        totalProgress += (stageInfo.weight * stageProgress) / 100;
      }
    }

    session.currentProgress = totalWeight > 0 ? (totalProgress / totalWeight) * 100 : 0;
  }

  /**
   * Add an error to the session
   * @param {string} trackingId - Session tracking ID
   * @param {Error|string} error - Error to add
   */
  addError(trackingId, error) {
    const session = this.sessions.get(trackingId);
    if (!session) { return; }

    const errorInfo = {
      timestamp: Date.now(),
      stage: session.currentStage,
      message: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : null
    };

    session.errors.push(errorInfo);

    // Trigger error callback
    if (session.callbacks.onError) {
      session.callbacks.onError(errorInfo);
    }
  }

  /**
   * Add a warning to the session
   * @param {string} trackingId - Session tracking ID
   * @param {string} warning - Warning message
   */
  addWarning(trackingId, warning) {
    const session = this.sessions.get(trackingId);
    if (!session) { return; }

    const warningInfo = {
      timestamp: Date.now(),
      stage: session.currentStage,
      message: warning
    };

    session.warnings.push(warningInfo);
  }

  /**
   * Set metadata for the session
   * @param {string} trackingId - Session tracking ID
   * @param {Object} metadata - Metadata to set
   */
  setMetadata(trackingId, metadata) {
    const session = this.sessions.get(trackingId);
    if (!session) { return; }

    session.metadata = { ...session.metadata, ...metadata };
  }

  /**
   * Complete a processing session
   * @param {string} trackingId - Session tracking ID
   * @param {Object} result - Processing result
   */
  completeSession(trackingId, result) {
    const session = this.sessions.get(trackingId);
    if (!session) { return; }

    session.endTime = Date.now();
    session.status = 'completed';
    session.result = result;
    session.currentProgress = 100;

    // Mark all stages as completed
    Object.values(session.stages).forEach(stage => {
      stage.completed = true;
    });

    // Calculate performance metrics
    this.calculatePerformanceMetrics(trackingId);

    // Update global stats
    this.updateGlobalStats(session);

    // Trigger completion callback
    if (session.callbacks.onComplete) {
      session.callbacks.onComplete(result);
    }

    // Stop memory monitoring
    this.stopMemoryMonitoring(trackingId);

    // Persist final state
    if (this.config.persistProgress) {
      this.persistProgress();
    }
  }

  /**
   * Cancel a processing session
   * @param {string} trackingId - Session tracking ID
   */
  cancelSession(trackingId) {
    const session = this.sessions.get(trackingId);
    if (!session) { return; }

    session.endTime = Date.now();
    session.status = 'cancelled';

    // Stop memory monitoring
    this.stopMemoryMonitoring(trackingId);

    // Persist state
    if (this.config.persistProgress) {
      this.persistProgress();
    }
  }

  /**
   * Get session status
   * @param {string} trackingId - Session tracking ID
   * @returns {Object} - Session status
   */
  getSessionStatus(trackingId) {
    const session = this.sessions.get(trackingId);
    if (!session) { return null; }

    return {
      trackingId: session.trackingId,
      fileName: session.fileName,
      fileSize: session.fileSize,
      totalPages: session.totalPages,
      currentStage: session.currentStage,
      currentProgress: session.currentProgress,
      status: session.status,
      startTime: session.startTime,
      endTime: session.endTime,
      errors: session.errors.length,
      warnings: session.warnings.length,
      memoryPeak: this.getMemoryPeak(trackingId),
      processingTime: session.endTime ? session.endTime - session.startTime : Date.now() - session.startTime
    };
  }

  /**
   * Start memory monitoring for a session
   * @param {string} trackingId - Session tracking ID
   */
  startMemoryMonitoring(trackingId) {
    if (!this.config.trackMemory || typeof performance === 'undefined' || !performance.memory) {
      return;
    }

    const session = this.sessions.get(trackingId);
    if (!session) { return; }

    session.memoryMonitorInterval = setInterval(() => {
      if (session.status !== 'active') {
        clearInterval(session.memoryMonitorInterval);
        return;
      }

      const memoryInfo = {
        timestamp: Date.now(),
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit
      };

      session.memoryUsage.push(memoryInfo);

      // Keep only last 100 measurements to prevent memory bloat
      if (session.memoryUsage.length > 100) {
        session.memoryUsage.shift();
      }
    }, this.config.updateInterval);
  }

  /**
   * Stop memory monitoring for a session
   * @param {string} trackingId - Session tracking ID
   */
  stopMemoryMonitoring(trackingId) {
    const session = this.sessions.get(trackingId);
    if (!session || !session.memoryMonitorInterval) { return; }

    clearInterval(session.memoryMonitorInterval);
    delete session.memoryMonitorInterval;
  }

  /**
   * Get memory peak for a session
   * @param {string} trackingId - Session tracking ID
   * @returns {number} - Peak memory usage in bytes
   */
  getMemoryPeak(trackingId) {
    const session = this.sessions.get(trackingId);
    if (!session || !session.memoryUsage.length) { return 0; }

    return Math.max(...session.memoryUsage.map(m => m.used));
  }

  /**
   * Calculate performance metrics for a session
   * @param {string} trackingId - Session tracking ID
   */
  calculatePerformanceMetrics(trackingId) {
    const session = this.sessions.get(trackingId);
    if (!session) { return; }

    const processingTime = session.endTime - session.startTime;
    const memoryPeak = this.getMemoryPeak(trackingId);

    session.performanceMetrics = {
      processingTime,
      memoryPeak,
      processingSpeed: session.fileSize > 0 ? session.fileSize / processingTime : 0, // bytes per ms
      pagesPerSecond: session.totalPages > 0 ? (session.totalPages * 1000) / processingTime : 0
    };
  }

  /**
   * Update global statistics
   * @param {Object} session - Completed session
   */
  updateGlobalStats(session) {
    this.globalStats.totalProcessed++;

    if (session.errors.length > 0) {
      this.globalStats.totalErrors++;
    }

    // Update average processing time
    const processingTime = session.endTime - session.startTime;
    this.globalStats.averageProcessingTime =
      (this.globalStats.averageProcessingTime * (this.globalStats.totalProcessed - 1) + processingTime) /
      this.globalStats.totalProcessed;

    // Update memory peak
    const memoryPeak = this.getMemoryPeak(session.trackingId);
    if (memoryPeak > this.globalStats.memoryPeakUsage) {
      this.globalStats.memoryPeakUsage = memoryPeak;
    }
  }

  /**
   * Trigger progress callback for a session
   * @param {string} trackingId - Session tracking ID
   */
  triggerProgressCallback(trackingId) {
    const session = this.sessions.get(trackingId);
    if (!session || !session.callbacks.onProgress) { return; }

    const progressInfo = {
      trackingId,
      stage: session.currentStage,
      progress: session.currentProgress,
      stageProgress: session.stageProgress[session.currentStage] || 0,
      fileName: session.fileName,
      fileSize: session.fileSize,
      totalPages: session.totalPages,
      errors: session.errors.length,
      warnings: session.warnings.length,
      memoryUsage: this.getCurrentMemoryUsage(trackingId),
      timestamp: Date.now()
    };

    session.callbacks.onProgress(progressInfo);
  }

  /**
   * Get current memory usage for a session
   * @param {string} trackingId - Session tracking ID
   * @returns {Object} - Current memory usage
   */
  getCurrentMemoryUsage(trackingId) {
    const session = this.sessions.get(trackingId);
    if (!session || !session.memoryUsage.length) { return null; }

    return session.memoryUsage[session.memoryUsage.length - 1];
  }

  /**
   * Persist progress to storage
   */
  async persistProgress() {
    if (!this.config.persistProgress) { return; }

    try {
      const progressData = {
        sessions: Array.from(this.sessions.entries()),
        globalStats: this.globalStats,
        timestamp: Date.now()
      };

      if (typeof chrome !== 'undefined' && chrome.storage) {
        await chrome.storage.local.set({
          [this.config.storageKey]: progressData
        });
      } else {
        localStorage.setItem(this.config.storageKey, JSON.stringify(progressData));
      }
    } catch (error) {
      console.warn('Failed to persist progress:', error);
    }
  }

  /**
   * Load persisted progress from storage
   */
  async loadPersistedProgress() {
    try {
      let progressData = null;

      if (typeof chrome !== 'undefined' && chrome.storage) {
        const result = await chrome.storage.local.get(this.config.storageKey);
        progressData = result[this.config.storageKey];
      } else {
        const stored = localStorage.getItem(this.config.storageKey);
        progressData = stored ? JSON.parse(stored) : null;
      }

      if (progressData) {
        this.sessions = new Map(progressData.sessions || []);
        this.globalStats = { ...this.globalStats, ...progressData.globalStats };
      }
    } catch (error) {
      console.warn('Failed to load persisted progress:', error);
    }
  }

  /**
   * Get global statistics
   * @returns {Object} - Global statistics
   */
  getGlobalStats() {
    return { ...this.globalStats };
  }

  /**
   * Get all active sessions
   * @returns {Array} - Array of active session statuses
   */
  getActiveSessions() {
    return Array.from(this.sessions.values())
      .filter(session => session.status === 'active')
      .map(session => this.getSessionStatus(session.trackingId));
  }

  /**
   * Clean up completed sessions older than specified time
   * @param {number} maxAge - Maximum age in milliseconds
   */
  cleanupOldSessions(maxAge = 24 * 60 * 60 * 1000) { // 24 hours default
    const cutoffTime = Date.now() - maxAge;

    for (const [trackingId, session] of this.sessions.entries()) {
      if (session.status !== 'active' && session.endTime && session.endTime < cutoffTime) {
        this.sessions.delete(trackingId);
      }
    }

    if (this.config.persistProgress) {
      this.persistProgress();
    }
  }
}

// Create singleton instance
const pdfProgressTracker = new PDFProgressTracker();

export { pdfProgressTracker };
export default pdfProgressTracker;
