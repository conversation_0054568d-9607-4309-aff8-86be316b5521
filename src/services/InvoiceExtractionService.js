/**
 * InvoiceExtractionService - AI-powered invoice data extraction
 * Integrates with DeepSeek API for structured field extraction from invoices
 *
 * Features:
 * - Structured invoice field extraction
 * - Field validation and correction
 * - Template-based extraction for different invoice types
 * - Error handling and fallback mechanisms
 * - Integration with existing document processing pipeline
 */

import { DeepSeekAPI } from '../api/DeepSeekAPI.js';
import { invoiceFieldTemplates } from '../templates/invoiceFieldTemplates.js';
import { validateInvoiceFields } from '../utils/fieldValidation.js';
import { AIProcessingCache } from './AIProcessingCache.js';
import { RateLimitManager } from './RateLimitManager.js';
import { extractInvoiceDataFallback } from '../utils/fallbackExtraction.js';
import { calculateConfidenceScore } from '../utils/confidenceScoring.js';

export class InvoiceExtractionService {
  constructor(options = {}) {
    this.deepSeekAPI = new DeepSeekAPI();
    this.templates = invoiceFieldTemplates;
    this.cache = new AIProcessingCache(options.cache);
    this.rateLimiter = new RateLimitManager(options.rateLimit);
    this.enableCache = options.enableCache !== false;
    this.enableRateLimit = options.enableRateLimit !== false;
    this.enableFallback = options.enableFallback !== false;
    this.retryAttempts = options.retryAttempts || 3;
    this.retryDelay = options.retryDelay || 1000;
  }

  /**
   * Extract structured data from invoice text with enhanced features
   * @param {string} text - Invoice text content
   * @param {Object} options - Extraction options
   * @returns {Promise<Object>} - Extraction result
   */
  async extractInvoiceData(text, options = {}) {
    const startTime = Date.now();

    try {
      const {
        apiKey,
        language = 'pol',
        documentType = 'invoice',
        companyInfo = null,
        template = 'standard',
        userId = 'default',
        userTier = 'free'
      } = options;

      if (!text || text.trim().length === 0) {
        throw new Error('Invoice text is required for extraction');
      }

      console.log('🤖 Starting enhanced AI-powered invoice extraction...');

      // Check cache first if enabled
      if (this.enableCache) {
        const cachedResult = await this.cache.get(text, { language, template, documentType });
        if (cachedResult) {
          console.log('🎯 Using cached extraction result');
          const processingTime = Date.now() - startTime;
          return {
            ...cachedResult,
            cached: true,
            processingTime
          };
        }
      }

      // Check rate limits if enabled
      if (this.enableRateLimit && apiKey) {
        const rateLimitCheck = await this.rateLimiter.checkRateLimit(userId, userTier);
        if (!rateLimitCheck.allowed) {
          console.warn('⚠️ Rate limit exceeded, using fallback extraction');
          return await this.performFallbackExtraction(text, language, startTime, rateLimitCheck);
        }
      }

      // Attempt AI extraction with retry logic
      let aiResult = null;
      if (apiKey) {
        aiResult = await this.performAIExtractionWithRetry(text, {
          apiKey,
          language,
          template,
          companyInfo,
          userId,
          userTier
        });
      }

      // Use fallback if AI extraction failed
      if (!aiResult || !aiResult.success) {
        console.warn('⚠️ AI extraction failed, using fallback methods');
        return await this.performFallbackExtraction(text, language, startTime, aiResult);
      }

      // Cache successful AI result
      if (this.enableCache && aiResult.success) {
        await this.cache.set(text, { language, template, documentType }, aiResult, 24 * 60 * 60 * 1000);
      }

      const processingTime = Date.now() - startTime;
      return {
        ...aiResult,
        cached: false,
        processingTime
      };

    } catch (error) {
      console.error('❌ Enhanced invoice extraction failed:', error);

      // Try fallback extraction on any error
      if (this.enableFallback) {
        console.log('🔄 Attempting fallback extraction due to error...');
        return await this.performFallbackExtraction(text, language, startTime, { error: error.message });
      }

      return {
        success: false,
        error: error.message,
        data: null,
        processingTime: Date.now() - startTime
      };
    }
  }

  /**
   * Perform AI extraction with retry logic
   * @param {string} text - Invoice text
   * @param {Object} options - Extraction options
   * @returns {Promise<Object>} - AI extraction result
   */
  async performAIExtractionWithRetry(text, options) {
    const { apiKey, language, template, companyInfo, userId, userTier } = options;

    for (let attempt = 0; attempt < this.retryAttempts; attempt++) {
      try {
        // Record request for rate limiting
        if (this.enableRateLimit) {
          await this.rateLimiter.recordRequest(userId, userTier);
        }

        // Get extraction template
        const extractionTemplate = this.getExtractionTemplate(template, language);

        // Generate extraction prompt
        const prompt = this.generateExtractionPrompt(text, extractionTemplate, language, companyInfo);

        // Call DeepSeek API
        const response = await this.deepSeekAPI.callAPI(prompt, apiKey, {
          model: 'deepseek-chat',
          temperature: 0.1,
          max_tokens: 4096,
          response_format: { type: 'json_object' }
        });

        if (!response.success) {
          throw new Error(`DeepSeek API error: ${response.error}`);
        }

        // Parse and validate response
        const extractedData = this.parseExtractionResponse(response.content);
        const validatedData = await this.validateAndCorrectFields(extractedData, text, language);

        // Calculate enhanced confidence score
        const confidenceResult = calculateConfidenceScore(validatedData, {
          extractionMethod: 'deepseek-ai',
          originalText: text,
          validationResult: validatedData.validation,
          processingTime: 0
        });

        console.log('✅ AI invoice extraction completed successfully');

        return {
          success: true,
          data: {
            ...validatedData,
            confidence: confidenceResult
          },
          extractionMethod: 'deepseek-ai',
          template: template,
          language: language,
          attempt: attempt + 1
        };

      } catch (error) {
        console.error(`❌ AI extraction attempt ${attempt + 1} failed:`, error);

        if (attempt < this.retryAttempts - 1) {
          // Calculate exponential backoff delay
          const delay = this.rateLimiter.calculateBackoff(attempt, this.retryDelay);
          console.log(`⏳ Retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        } else {
          return {
            success: false,
            error: error.message,
            data: null,
            attempts: this.retryAttempts
          };
        }
      }
    }
  }

  /**
   * Perform fallback extraction when AI fails
   * @param {string} text - Invoice text
   * @param {string} language - Language code
   * @param {number} startTime - Processing start time
   * @param {Object} aiError - AI extraction error details
   * @returns {Promise<Object>} - Fallback extraction result
   */
  async performFallbackExtraction(text, language, startTime, aiError = null) {
    try {
      console.log('🔄 Performing fallback extraction...');

      const fallbackResult = extractInvoiceDataFallback(text, language);

      if (fallbackResult.success) {
        // Calculate confidence score for fallback
        const confidenceResult = calculateConfidenceScore(fallbackResult.data, {
          extractionMethod: 'fallback',
          originalText: text,
          processingTime: Date.now() - startTime
        });

        const processingTime = Date.now() - startTime;

        return {
          success: true,
          data: {
            ...fallbackResult.data,
            confidence: confidenceResult
          },
          extractionMethod: 'fallback',
          language: language,
          aiError: aiError,
          processingTime,
          cached: false
        };
      }
      return {
        success: false,
        error: `Both AI and fallback extraction failed: ${fallbackResult.error}`,
        data: null,
        aiError: aiError,
        processingTime: Date.now() - startTime
      };

    } catch (error) {
      console.error('❌ Fallback extraction failed:', error);
      return {
        success: false,
        error: `Fallback extraction error: ${error.message}`,
        data: null,
        aiError: aiError,
        processingTime: Date.now() - startTime
      };
    }
  }

  /**
   * Get extraction template for specific invoice type and language
   * @param {string} template - Template name
   * @param {string} language - Language code
   * @returns {Object} - Extraction template
   */
  getExtractionTemplate(template, language) {
    const templateKey = `${template}_${language}`;
    return this.templates[templateKey] || this.templates[`standard_${language}`] || this.templates.standard_pol;
  }

  /**
   * Generate extraction prompt for DeepSeek API
   * @param {string} text - Invoice text
   * @param {Object} template - Extraction template
   * @param {string} language - Language code
   * @param {Object} companyInfo - Company information
   * @returns {string} - Generated prompt
   */
  generateExtractionPrompt(text, template, language, companyInfo) {
    const languageNames = {
      'pol': 'Polish',
      'eng': 'English',
      'deu': 'German',
      'fra': 'French'
    };

    const languageName = languageNames[language] || 'Polish';
    const companyContext = companyInfo ? `\n\nCompany Context:\n${JSON.stringify(companyInfo, null, 2)}` : '';

    return `You are an expert in extracting structured data from ${languageName} invoices. Extract the following fields from the invoice text and return them as a JSON object.

CRITICAL INSTRUCTIONS:
1. Use EXACT English field names from the template below
2. Field values should be in the original document language (${languageName})
3. Return only valid JSON without any additional text
4. If a field is not found, use null as the value
5. For monetary amounts, extract numbers only (no currency symbols)
6. For dates, use ISO format (YYYY-MM-DD) when possible

REQUIRED FIELDS TEMPLATE:
${JSON.stringify(template.fields, null, 2)}

FIELD DESCRIPTIONS:
${template.descriptions.map(desc => `- ${desc.field}: ${desc.description}`).join('\n')}

VALIDATION RULES:
${template.validationRules.map(rule => `- ${rule.field}: ${rule.rule}`).join('\n')}
${companyContext}

INVOICE TEXT TO ANALYZE:
${text}

Return the extracted data as a valid JSON object:`;
  }

  /**
   * Parse extraction response from DeepSeek API
   * @param {string} content - API response content
   * @returns {Object} - Parsed data
   */
  parseExtractionResponse(content) {
    try {
      // Try to parse as JSON directly
      return JSON.parse(content);
    } catch (error) {
      // Try to extract JSON from content if wrapped in text
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        try {
          return JSON.parse(jsonMatch[0]);
        } catch (parseError) {
          throw new Error('Failed to parse extraction response as JSON');
        }
      }
      throw new Error('No valid JSON found in extraction response');
    }
  }

  /**
   * Validate and correct extracted fields
   * @param {Object} data - Extracted data
   * @param {string} originalText - Original invoice text
   * @param {string} language - Language code
   * @returns {Promise<Object>} - Validated data
   */
  async validateAndCorrectFields(data, originalText, language) {
    try {
      // Use field validation utility
      const validationResult = validateInvoiceFields(data, language);

      if (validationResult.isValid) {
        return {
          ...data,
          validation: {
            status: 'valid',
            errors: [],
            warnings: validationResult.warnings || []
          }
        };
      }

      // Apply corrections for common issues
      const correctedData = this.applyCorrections(data, validationResult.errors);

      return {
        ...correctedData,
        validation: {
          status: 'corrected',
          errors: validationResult.errors,
          warnings: validationResult.warnings || [],
          corrections: validationResult.corrections || []
        }
      };

    } catch (error) {
      console.warn('⚠️ Field validation failed, returning raw data:', error);
      return {
        ...data,
        validation: {
          status: 'unvalidated',
          errors: [error.message],
          warnings: []
        }
      };
    }
  }

  /**
   * Apply corrections to extracted data
   * @param {Object} data - Original data
   * @param {Array} errors - Validation errors
   * @returns {Object} - Corrected data
   */
  applyCorrections(data, errors) {
    const corrected = { ...data };

    errors.forEach(error => {
      switch (error.type) {
        case 'date_format':
          corrected[error.field] = this.correctDateFormat(data[error.field]);
          break;
        case 'amount_format':
          corrected[error.field] = this.correctAmountFormat(data[error.field]);
          break;
        case 'vat_rate':
          corrected[error.field] = this.correctVatRate(data[error.field]);
          break;
        default:
          // Keep original value for unknown error types
          break;
      }
    });

    return corrected;
  }

  /**
   * Correct date format to ISO standard
   * @param {string} dateValue - Original date value
   * @returns {string} - Corrected date
   */
  correctDateFormat(dateValue) {
    if (!dateValue) { return null; }

    try {
      const date = new Date(dateValue);
      if (isNaN(date.getTime())) {
        // Try common Polish date formats
        const polishFormats = [
          /(\d{1,2})\.(\d{1,2})\.(\d{4})/, // DD.MM.YYYY
          /(\d{1,2})-(\d{1,2})-(\d{4})/, // DD-MM-YYYY
          /(\d{1,2})\/(\d{1,2})\/(\d{4})/ // DD/MM/YYYY
        ];

        for (const format of polishFormats) {
          const match = dateValue.match(format);
          if (match) {
            const [, day, month, year] = match;
            return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
          }
        }
        return dateValue; // Return original if no format matches
      }
      return date.toISOString().split('T')[0];
    } catch (error) {
      return dateValue;
    }
  }

  /**
   * Correct amount format to decimal number
   * @param {string} amountValue - Original amount value
   * @returns {number} - Corrected amount
   */
  correctAmountFormat(amountValue) {
    if (!amountValue) { return null; }

    try {
      // Remove currency symbols and spaces
      const cleaned = amountValue.toString()
        .replace(/[^\d,.-]/g, '')
        .replace(/,/g, '.');

      return parseFloat(cleaned) || null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Correct VAT rate format
   * @param {string} vatValue - Original VAT value
   * @returns {number} - Corrected VAT rate
   */
  correctVatRate(vatValue) {
    if (!vatValue) { return null; }

    try {
      const cleaned = vatValue.toString().replace(/[^\d.]/g, '');
      const rate = parseFloat(cleaned);

      // Convert percentage to decimal if needed
      if (rate > 1) {
        return rate / 100;
      }
      return rate;
    } catch (error) {
      return null;
    }
  }

  /**
   * Calculate confidence score for extracted data
   * @param {Object} data - Extracted data
   * @returns {number} - Confidence score (0-1)
   */
  calculateConfidence(data) {
    if (!data || typeof data !== 'object') { return 0; }

    const requiredFields = ['seller_name', 'buyer_name', 'total_gross', 'invoice_number', 'issue_date'];
    const foundFields = requiredFields.filter(field => data[field] && data[field] !== null);

    const baseConfidence = foundFields.length / requiredFields.length;

    // Adjust confidence based on validation status
    if (data.validation) {
      switch (data.validation.status) {
        case 'valid':
          return Math.min(baseConfidence + 0.1, 1.0);
        case 'corrected':
          return Math.max(baseConfidence - 0.1, 0.1);
        case 'unvalidated':
          return Math.max(baseConfidence - 0.2, 0.1);
        default:
          return baseConfidence;
      }
    }

    return baseConfidence;
  }

  /**
   * Get processing statistics and cache performance
   * @returns {Promise<Object>} - Processing statistics
   */
  async getProcessingStats() {
    try {
      const cacheStats = await this.cache.getStats();
      const rateLimitStats = {
        enabled: this.enableRateLimit,
        retryAttempts: this.retryAttempts,
        retryDelay: this.retryDelay
      };

      return {
        cache: cacheStats,
        rateLimit: rateLimitStats,
        features: {
          cacheEnabled: this.enableCache,
          rateLimitEnabled: this.enableRateLimit,
          fallbackEnabled: this.enableFallback
        },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ Failed to get processing stats:', error);
      return {
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Clear cache and reset rate limits
   * @param {string} userId - User ID for rate limit reset
   * @returns {Promise<Object>} - Reset result
   */
  async resetProcessingData(userId = 'default') {
    try {
      const cacheCleared = await this.cache.clear();
      const rateLimitsReset = await this.rateLimiter.resetLimits(userId);

      return {
        success: true,
        cacheCleared,
        rateLimitsReset,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ Failed to reset processing data:', error);
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}

// Self-test functionality for Node.js environment
if (typeof module !== 'undefined' && require.main === module) {
  console.log('🧪 Testing InvoiceExtractionService...');

  const service = new InvoiceExtractionService();

  // Test template retrieval
  const template = service.getExtractionTemplate('standard', 'pol');
  console.log('✅ Template retrieval test passed');

  // Test prompt generation
  const prompt = service.generateExtractionPrompt('Test invoice text', template, 'pol', null);
  console.log('✅ Prompt generation test passed');

  // Test response parsing
  const testResponse = '{"invoice_number": "INV-001", "total_gross": 1234.56}';
  const parsed = service.parseExtractionResponse(testResponse);
  console.log('✅ Response parsing test passed');

  // Test confidence calculation
  const confidence = service.calculateConfidence(parsed);
  console.log('✅ Confidence calculation test passed');

  console.log('🎉 All InvoiceExtractionService tests passed!');
}
