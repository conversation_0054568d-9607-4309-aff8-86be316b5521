/**
 * SandboxCommunicationService - Message passing between popup and Tesseract.js sandbox
 * Implements secure communication for OCR processing in Chrome extension
 *
 * Features:
 * - Secure message passing with request/response pattern
 * - Promise-based API for easy integration
 * - Error handling and timeout management
 * - Progress tracking and status monitoring
 * - Automatic sandbox lifecycle management
 */

export class SandboxCommunicationService {
  constructor() {
    this.sandboxFrame = null;
    this.pendingRequests = new Map();
    this.requestIdCounter = 0;
    this.isInitialized = false;
    this.sandboxReady = false;

    // Configuration
    this.config = {
      sandboxUrl: chrome?.runtime?.getURL('sandbox/sandbox.html') || 'sandbox/sandbox.html',
      requestTimeout: 60000, // 60 seconds (increased for Tesseract)
      tesseractTimeout: 90000, // 90 seconds for Tesseract initialization
      maxRetries: 3,
      retryDelay: 2000, // Increased retry delay
      exponentialBackoff: true
    };

    this.setupMessageHandling();
  }

  /**
   * Initialize the sandbox iframe
   */
  async initialize() {
    if (this.isInitialized) {
      return;
    }

    try {
      await this.createSandboxFrame();
      await this.waitForSandboxReady();
      this.isInitialized = true;
      console.log('✅ SandboxCommunicationService initialized');
    } catch (error) {
      console.error('❌ Failed to initialize SandboxCommunicationService:', error);
      throw error;
    }
  }

  /**
   * Create and setup sandbox iframe
   */
  async createSandboxFrame() {
    return new Promise((resolve, reject) => {
      console.log('🔧 Creating sandbox iframe...');

      // Remove existing frame if any
      if (this.sandboxFrame) {
        console.log('🧹 Removing existing sandbox frame');
        this.sandboxFrame.remove();
      }

      // Create new iframe
      this.sandboxFrame = document.createElement('iframe');
      this.sandboxFrame.src = this.config.sandboxUrl;
      this.sandboxFrame.style.display = 'none';
      this.sandboxFrame.sandbox = 'allow-scripts'; // Remove allow-same-origin for security

      console.log('📋 Sandbox iframe config:', {
        src: this.sandboxFrame.src,
        sandbox: this.sandboxFrame.sandbox
      });

      let loadResolved = false;

      // Handle load events
      this.sandboxFrame.onload = () => {
        console.log('📦 Sandbox iframe loaded successfully');
        if (!loadResolved) {
          loadResolved = true;
          // Give the iframe a moment to initialize its scripts
          setTimeout(() => {
            resolve();
          }, 500);
        }
      };

      this.sandboxFrame.onerror = (error) => {
        console.error('❌ Sandbox iframe load error:', error);
        if (!loadResolved) {
          loadResolved = true;
          reject(new Error('Failed to load sandbox iframe'));
        }
      };

      // Add to DOM
      console.log('📎 Adding sandbox iframe to DOM');
      document.body.appendChild(this.sandboxFrame);

      // Timeout fallback
      setTimeout(() => {
        if (!loadResolved) {
          loadResolved = true;
          console.error('⏰ Sandbox iframe load timeout');
          reject(new Error('Sandbox iframe load timeout'));
        }
      }, 15000); // Increased timeout to 15 seconds
    });
  }

  /**
   * Wait for sandbox to signal ready state
   */
  async waitForSandboxReady() {
    return new Promise((resolve, reject) => {
      console.log('⏳ Waiting for sandbox ready signal...');

      const startTime = Date.now();
      const timeout = setTimeout(() => {
        const elapsed = Date.now() - startTime;
        console.error(`❌ Sandbox ready timeout after ${elapsed}ms`);
        console.log('📋 Sandbox state:', {
          sandboxReady: this.sandboxReady,
          sandboxFrame: !!this.sandboxFrame,
          frameLoaded: this.sandboxFrame?.contentDocument?.readyState
        });
        reject(new Error('Sandbox ready timeout'));
      }, 15000); // Increased timeout to 15 seconds

      const checkReady = () => {
        const elapsed = Date.now() - startTime;

        if (this.sandboxReady) {
          clearTimeout(timeout);
          console.log(`✅ Sandbox ready signal received after ${elapsed}ms`);
          resolve();
        } else {
          if (elapsed % 2000 === 0) { // Log every 2 seconds
            console.log(`⏳ Still waiting for sandbox ready... (${elapsed}ms elapsed)`);
          }
          setTimeout(checkReady, 100);
        }
      };

      checkReady();
    });
  }

  /**
   * Setup message handling for sandbox communication
   */
  setupMessageHandling() {
    window.addEventListener('message', (event) => {
      console.log('📨 Received message in SandboxCommunicationService:', event.data);

      // Verify message is from our sandbox
      if (!this.sandboxFrame) {
        console.log('⚠️ No sandbox frame available, ignoring message');
        return;
      }

      if (event.source !== this.sandboxFrame.contentWindow) {
        console.log('⚠️ Message not from sandbox iframe, ignoring');
        return;
      }

      const { type, data } = event.data;
      console.log(`📥 Processing sandbox message: ${type}`, data);

      switch (type) {
        case 'SANDBOX_READY':
          this.sandboxReady = true;
          console.log('✅ Sandbox ready signal received:', data);
          break;

        case 'RESPONSE':
          console.log('📬 Received response for request:', data.requestId);
          this.handleResponse(data.requestId, data.data);
          break;

        case 'ERROR':
          console.log('❌ Received error for request:', data.requestId, data.error);
          this.handleError(data.requestId, data.error);
          break;

        default:
          console.warn('⚠️ Unknown message type from sandbox:', type, data);
      }
    });

    console.log('✅ SandboxCommunicationService message handler setup complete');
  }

  /**
   * Send message to sandbox and return promise
   */
  async sendMessage(type, data, timeout = this.config.requestTimeout) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const requestId = ++this.requestIdCounter;

    return new Promise((resolve, reject) => {
      // Store request for response handling
      const timeoutId = setTimeout(() => {
        this.pendingRequests.delete(requestId);
        reject(new Error(`Request timeout: ${type}`));
      }, timeout);

      this.pendingRequests.set(requestId, {
        resolve,
        reject,
        timeoutId,
        type,
        timestamp: Date.now()
      });

      // Send message to sandbox
      try {
        this.sandboxFrame.contentWindow.postMessage({
          type,
          data,
          requestId
        }, '*');
      } catch (error) {
        this.pendingRequests.delete(requestId);
        clearTimeout(timeoutId);
        reject(error);
      }
    });
  }

  /**
   * Handle response from sandbox
   */
  handleResponse(requestId, data) {
    const request = this.pendingRequests.get(requestId);
    if (request) {
      clearTimeout(request.timeoutId);
      this.pendingRequests.delete(requestId);
      request.resolve(data);
    }
  }

  /**
   * Handle error from sandbox
   */
  handleError(requestId, error) {
    const request = this.pendingRequests.get(requestId);
    if (request) {
      clearTimeout(request.timeoutId);
      this.pendingRequests.delete(requestId);
      request.reject(new Error(error));
    }
  }

  /**
   * Initialize Tesseract in sandbox with enhanced timeout and retry logic
   */
  async initializeTesseract(language = 'pol+eng') {
    const maxRetries = this.config.maxRetries;
    let lastError;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔧 Initializing Tesseract (attempt ${attempt}/${maxRetries}) with language: ${language}`);

        // Use longer timeout for Tesseract initialization
        const result = await this.sendMessage('INIT_TESSERACT', { language }, this.config.tesseractTimeout);

        console.log('✅ Tesseract initialized in sandbox:', result);
        return result;
      } catch (error) {
        lastError = error;
        console.warn(`⚠️ Tesseract initialization attempt ${attempt} failed:`, error.message);

        if (attempt < maxRetries) {
          const delay = this.config.exponentialBackoff ?
            this.config.retryDelay * Math.pow(2, attempt - 1) :
            this.config.retryDelay;

          console.log(`⏳ Retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    console.error('❌ Failed to initialize Tesseract after all retries:', lastError);
    throw new Error(`Tesseract initialization failed after ${maxRetries} attempts: ${lastError.message}`);
  }

  /**
   * Process OCR in sandbox
   */
  async processOCR(imageData, options = {}) {
    try {
      const result = await this.sendMessage('PROCESS_OCR', {
        imageData,
        options
      }, 60000); // 60 second timeout for OCR processing

      console.log('✅ OCR processing completed in sandbox');
      return result;
    } catch (error) {
      console.error('❌ OCR processing failed in sandbox:', error);
      throw error;
    }
  }

  /**
   * Get sandbox status
   */
  async getStatus() {
    try {
      return await this.sendMessage('GET_STATUS', {});
    } catch (error) {
      console.error('❌ Failed to get sandbox status:', error);
      throw error;
    }
  }

  /**
   * Terminate Tesseract worker in sandbox
   */
  async terminateWorker() {
    try {
      const result = await this.sendMessage('TERMINATE_WORKER', {});
      console.log('✅ Tesseract worker terminated in sandbox');
      return result;
    } catch (error) {
      console.error('❌ Failed to terminate worker in sandbox:', error);
      throw error;
    }
  }

  /**
   * Convert File/Blob to base64 data URL for sandbox transmission
   */
  async fileToDataURL(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  /**
   * Convert canvas to data URL for sandbox transmission
   */
  canvasToDataURL(canvas, type = 'image/png', quality = 0.9) {
    return canvas.toDataURL(type, quality);
  }

  /**
   * Cleanup resources
   */
  destroy() {
    // Clear pending requests
    for (const [requestId, request] of this.pendingRequests) {
      clearTimeout(request.timeoutId);
      request.reject(new Error('Service destroyed'));
    }
    this.pendingRequests.clear();

    // Remove sandbox frame
    if (this.sandboxFrame) {
      this.sandboxFrame.remove();
      this.sandboxFrame = null;
    }

    this.isInitialized = false;
    this.sandboxReady = false;

    console.log('🧹 SandboxCommunicationService destroyed');
  }

  /**
   * Health check for sandbox communication
   */
  async healthCheck() {
    try {
      const status = await this.getStatus();
      return {
        healthy: true,
        status,
        timestamp: Date.now()
      };
    } catch (error) {
      return {
        healthy: false,
        error: error.message,
        timestamp: Date.now()
      };
    }
  }
}

// Create singleton instance
export const sandboxCommunicationService = new SandboxCommunicationService();
