/**
 * PDFMetadataExtractor - Comprehensive PDF metadata extraction
 * Implements Task 2.2.2 - PDF Processing Enhancement
 *
 * Features:
 * - Comprehensive PDF metadata extraction
 * - Document structure analysis
 * - Content classification
 * - Security information extraction
 * - Performance metrics
 * - Invoice-specific metadata detection
 */

import * as pdfjsLib from 'pdfjs-dist';

export class PDFMetadataExtractor {
  constructor(options = {}) {
    this.config = {
      extractStructure: options.extractStructure !== false,
      extractSecurity: options.extractSecurity !== false,
      extractFonts: options.extractFonts !== false,
      extractImages: options.extractImages !== false,
      analyzeContent: options.analyzeContent !== false,
      maxAnalysisPages: options.maxAnalysisPages || 10,
      ...options
    };

    this.initialized = false;
  }

  /**
   * Initialize the metadata extractor
   */
  async initialize() {
    if (this.initialized) { return; }

    try {
      this.initialized = true;
      console.log('PDFMetadataExtractor initialized');
    } catch (error) {
      console.error('Failed to initialize PDFMetadataExtractor:', error);
      throw new Error(`Metadata extractor initialization failed: ${error.message}`);
    }
  }

  /**
   * Extract comprehensive metadata from PDF file
   * @param {File} file - PDF file to analyze
   * @param {Object} options - Extraction options
   * @returns {Promise<Object>} - Comprehensive metadata
   */
  async extractMetadata(file, options = {}) {
    await this.initialize();

    const {
      onProgress = null,
      includeStructure = this.config.extractStructure,
      includeSecurity = this.config.extractSecurity,
      includeFonts = this.config.extractFonts,
      includeImages = this.config.extractImages,
      analyzeContent = this.config.analyzeContent
    } = options;

    try {
      if (onProgress) { onProgress({ stage: 'loading', progress: 0 }); }

      // Load PDF document
      const arrayBuffer = await file.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);

      const loadingTask = pdfjsLib.getDocument({
        data: uint8Array,
        verbosity: 0
      });

      const pdf = await loadingTask.promise;

      if (onProgress) { onProgress({ stage: 'extracting_basic', progress: 20 }); }

      // Extract basic metadata
      const basicMetadata = await this.extractBasicMetadata(pdf, file);

      if (onProgress) { onProgress({ stage: 'extracting_document', progress: 40 }); }

      // Extract document metadata
      const documentMetadata = await this.extractDocumentMetadata(pdf);

      if (onProgress) { onProgress({ stage: 'analyzing_structure', progress: 60 }); }

      // Extract structure information
      const structureMetadata = includeStructure ?
        await this.extractStructureMetadata(pdf) : {};

      if (onProgress) { onProgress({ stage: 'analyzing_content', progress: 80 }); }

      // Analyze content if requested
      const contentMetadata = analyzeContent ?
        await this.analyzeContentMetadata(pdf) : {};

      if (onProgress) { onProgress({ stage: 'finalizing', progress: 100 }); }

      // Combine all metadata
      const metadata = {
        ...basicMetadata,
        ...documentMetadata,
        ...structureMetadata,
        ...contentMetadata,
        extractionTimestamp: Date.now(),
        extractorVersion: '1.0.0'
      };

      return {
        success: true,
        metadata,
        extractionTime: Date.now() - (metadata.extractionStart || Date.now())
      };

    } catch (error) {
      console.error('Metadata extraction failed:', error);
      return {
        success: false,
        error: error.message,
        metadata: null
      };
    }
  }

  /**
   * Extract basic PDF metadata
   * @param {Object} pdf - PDF document object
   * @param {File} file - Original file
   * @returns {Promise<Object>} - Basic metadata
   */
  async extractBasicMetadata(pdf, file) {
    const metadata = await pdf.getMetadata();
    const info = metadata.info || {};

    return {
      // File information
      fileName: file.name,
      fileSize: file.size,
      fileSizeFormatted: this.formatFileSize(file.size),
      lastModified: file.lastModified,
      mimeType: file.type,

      // PDF basic info
      numPages: pdf.numPages,
      pdfVersion: pdf.pdfInfo?.version || 'unknown',
      isLinearized: pdf.pdfInfo?.isLinearized || false,

      // Document info
      title: info.Title || '',
      author: info.Author || '',
      subject: info.Subject || '',
      keywords: info.Keywords || '',
      creator: info.Creator || '',
      producer: info.Producer || '',
      creationDate: this.parseDate(info.CreationDate),
      modificationDate: this.parseDate(info.ModDate),

      // Processing metadata
      extractionStart: Date.now()
    };
  }

  /**
   * Extract document-level metadata
   * @param {Object} pdf - PDF document object
   * @returns {Promise<Object>} - Document metadata
   */
  async extractDocumentMetadata(pdf) {
    try {
      const metadata = {};

      // Get PDF catalog
      const catalog = await pdf.getPage(1).then(page => page.commonObjs);

      // Extract page dimensions from first page
      const firstPage = await pdf.getPage(1);
      const viewport = firstPage.getViewport({ scale: 1.0 });

      metadata.pageInfo = {
        width: viewport.width,
        height: viewport.height,
        rotation: viewport.rotation,
        aspectRatio: viewport.width / viewport.height
      };

      // Determine page orientation
      metadata.pageInfo.orientation = viewport.width > viewport.height ? 'landscape' : 'portrait';

      // Estimate document type based on dimensions
      metadata.documentType = this.estimateDocumentType(metadata.pageInfo);

      // Clean up
      firstPage.cleanup();

      return metadata;

    } catch (error) {
      console.warn('Failed to extract document metadata:', error);
      return {};
    }
  }

  /**
   * Extract structure metadata
   * @param {Object} pdf - PDF document object
   * @returns {Promise<Object>} - Structure metadata
   */
  async extractStructureMetadata(pdf) {
    try {
      const metadata = {
        structure: {
          hasOutline: false,
          hasAnnotations: false,
          hasFormFields: false,
          hasEmbeddedFiles: false,
          pageVariations: []
        }
      };

      // Check for outline (bookmarks)
      try {
        const outline = await pdf.getOutline();
        metadata.structure.hasOutline = outline && outline.length > 0;
        if (metadata.structure.hasOutline) {
          metadata.structure.outlineItems = outline.length;
        }
      } catch (error) {
        // Outline not available
      }

      // Analyze page variations (sample first few pages)
      const pagesToAnalyze = Math.min(pdf.numPages, 5);
      const pageAnalysis = [];

      for (let i = 1; i <= pagesToAnalyze; i++) {
        try {
          const page = await pdf.getPage(i);
          const viewport = page.getViewport({ scale: 1.0 });

          const pageInfo = {
            pageNumber: i,
            width: viewport.width,
            height: viewport.height,
            rotation: viewport.rotation
          };

          // Check for annotations
          const annotations = await page.getAnnotations();
          pageInfo.annotationCount = annotations.length;

          if (annotations.length > 0) {
            metadata.structure.hasAnnotations = true;
          }

          pageAnalysis.push(pageInfo);
          page.cleanup();

        } catch (error) {
          console.warn(`Failed to analyze page ${i}:`, error);
        }
      }

      metadata.structure.pageVariations = pageAnalysis;

      // Check for consistent page dimensions
      const uniqueDimensions = new Set(
        pageAnalysis.map(p => `${p.width}x${p.height}`)
      );
      metadata.structure.hasConsistentDimensions = uniqueDimensions.size === 1;

      return metadata;

    } catch (error) {
      console.warn('Failed to extract structure metadata:', error);
      return { structure: {} };
    }
  }

  /**
   * Analyze content metadata
   * @param {Object} pdf - PDF document object
   * @returns {Promise<Object>} - Content metadata
   */
  async analyzeContentMetadata(pdf) {
    try {
      const metadata = {
        content: {
          textDensity: 0,
          averageWordsPerPage: 0,
          languageHints: [],
          hasImages: false,
          hasTables: false,
          invoiceIndicators: {
            score: 0,
            indicators: []
          }
        }
      };

      const pagesToAnalyze = Math.min(pdf.numPages, this.config.maxAnalysisPages);
      let totalWords = 0;
      let totalTextLength = 0;
      const textSamples = [];

      for (let i = 1; i <= pagesToAnalyze; i++) {
        try {
          const page = await pdf.getPage(i);
          const textContent = await page.getTextContent();

          const pageText = textContent.items
            .map(item => item.str)
            .join(' ')
            .trim();

          if (pageText) {
            const words = pageText.split(/\s+/).filter(word => word.length > 0);
            totalWords += words.length;
            totalTextLength += pageText.length;

            if (textSamples.length < 3) {
              textSamples.push(pageText.substring(0, 500)); // First 500 chars
            }
          }

          page.cleanup();

        } catch (error) {
          console.warn(`Failed to analyze content on page ${i}:`, error);
        }
      }

      // Calculate content metrics
      if (pagesToAnalyze > 0) {
        metadata.content.averageWordsPerPage = totalWords / pagesToAnalyze;
        metadata.content.textDensity = totalTextLength / pagesToAnalyze;
      }

      // Analyze text samples for language and content type
      if (textSamples.length > 0) {
        const combinedText = textSamples.join(' ').toLowerCase();

        // Language detection (basic)
        metadata.content.languageHints = this.detectLanguageHints(combinedText);

        // Invoice detection
        metadata.content.invoiceIndicators = this.analyzeInvoiceIndicators(combinedText);
      }

      return metadata;

    } catch (error) {
      console.warn('Failed to analyze content metadata:', error);
      return { content: {} };
    }
  }

  /**
   * Estimate document type based on page dimensions
   * @param {Object} pageInfo - Page information
   * @returns {string} - Estimated document type
   */
  estimateDocumentType(pageInfo) {
    const { width, height, aspectRatio } = pageInfo;

    // Common paper sizes (in points, 72 DPI)
    const paperSizes = {
      'A4': { width: 595, height: 842, tolerance: 10 },
      'Letter': { width: 612, height: 792, tolerance: 10 },
      'Legal': { width: 612, height: 1008, tolerance: 10 },
      'A3': { width: 842, height: 1191, tolerance: 15 },
      'Tabloid': { width: 792, height: 1224, tolerance: 15 }
    };

    for (const [size, dimensions] of Object.entries(paperSizes)) {
      const widthMatch = Math.abs(width - dimensions.width) <= dimensions.tolerance;
      const heightMatch = Math.abs(height - dimensions.height) <= dimensions.tolerance;

      if (widthMatch && heightMatch) {
        return size;
      }
    }

    // Check for common aspect ratios
    if (Math.abs(aspectRatio - (4 / 3)) < 0.1) { return 'Presentation (4:3)'; }
    if (Math.abs(aspectRatio - (16 / 9)) < 0.1) { return 'Presentation (16:9)'; }
    if (aspectRatio > 2) { return 'Banner/Wide'; }
    if (aspectRatio < 0.5) { return 'Tall/Narrow'; }

    return 'Custom';
  }

  /**
   * Detect language hints from text
   * @param {string} text - Text to analyze
   * @returns {Array} - Array of language hints
   */
  detectLanguageHints(text) {
    const hints = [];

    // Polish indicators
    const polishWords = ['faktura', 'sprzedawca', 'nabywca', 'nip', 'regon', 'kwota', 'podatek'];
    const polishCount = polishWords.filter(word => text.includes(word)).length;
    if (polishCount >= 2) { hints.push({ language: 'Polish', confidence: polishCount / polishWords.length }); }

    // English indicators
    const englishWords = ['invoice', 'bill', 'receipt', 'seller', 'buyer', 'amount', 'tax', 'total'];
    const englishCount = englishWords.filter(word => text.includes(word)).length;
    if (englishCount >= 2) { hints.push({ language: 'English', confidence: englishCount / englishWords.length }); }

    // German indicators
    const germanWords = ['rechnung', 'verkäufer', 'käufer', 'betrag', 'steuer', 'gesamt'];
    const germanCount = germanWords.filter(word => text.includes(word)).length;
    if (germanCount >= 2) { hints.push({ language: 'German', confidence: germanCount / germanWords.length }); }

    return hints.sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * Analyze invoice indicators in text
   * @param {string} text - Text to analyze
   * @returns {Object} - Invoice analysis result
   */
  analyzeInvoiceIndicators(text) {
    const indicators = [];
    let score = 0;

    // Invoice keywords with weights
    const invoiceKeywords = [
      { words: ['faktura', 'invoice', 'bill', 'receipt'], weight: 3, name: 'Document Type' },
      { words: ['nip', 'tax id', 'vat number', 'regon'], weight: 2, name: 'Tax Identifiers' },
      { words: ['sprzedawca', 'seller', 'vendor', 'from'], weight: 2, name: 'Seller Info' },
      { words: ['nabywca', 'buyer', 'customer', 'to'], weight: 2, name: 'Buyer Info' },
      { words: ['kwota', 'amount', 'total', 'suma', 'razem'], weight: 2, name: 'Amount Info' },
      { words: ['data', 'date', 'datum'], weight: 1, name: 'Date Info' },
      { words: ['netto', 'net', 'brutto', 'gross', 'vat'], weight: 2, name: 'Tax Info' }
    ];

    for (const category of invoiceKeywords) {
      const foundWords = category.words.filter(word => text.includes(word));
      if (foundWords.length > 0) {
        score += category.weight;
        indicators.push({
          category: category.name,
          foundWords,
          weight: category.weight
        });
      }
    }

    // Number patterns that might indicate invoice numbers or amounts
    const numberPatterns = [
      /\d{4}\/\d+/g, // Year/number format
      /[A-Z]{2,3}\/\d{4}\/\d+/g, // Prefix/year/number
      /\d+[.,]\d{2}/g // Currency amounts
    ];

    let numberMatches = 0;
    for (const pattern of numberPatterns) {
      const matches = text.match(pattern);
      if (matches) {
        numberMatches += matches.length;
      }
    }

    if (numberMatches >= 3) {
      score += 1;
      indicators.push({
        category: 'Number Patterns',
        foundWords: [`${numberMatches} numeric patterns`],
        weight: 1
      });
    }

    // Calculate confidence (0-100)
    const maxPossibleScore = invoiceKeywords.reduce((sum, cat) => sum + cat.weight, 0) + 1;
    const confidence = Math.min(100, (score / maxPossibleScore) * 100);

    return {
      score,
      confidence: Math.round(confidence),
      indicators,
      isLikelyInvoice: confidence >= 40
    };
  }

  /**
   * Parse PDF date string
   * @param {string} dateString - PDF date string
   * @returns {Object} - Parsed date information
   */
  parseDate(dateString) {
    if (!dateString) { return null; }

    try {
      // PDF date format: D:YYYYMMDDHHmmSSOHH'mm'
      const match = dateString.match(/D:(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/);
      if (match) {
        const [, year, month, day, hour, minute, second] = match;
        const date = new Date(year, month - 1, day, hour, minute, second);

        return {
          raw: dateString,
          parsed: date.toISOString(),
          formatted: date.toLocaleString(),
          timestamp: date.getTime()
        };
      }

      // Fallback: try to parse as regular date
      const date = new Date(dateString);
      if (!isNaN(date.getTime())) {
        return {
          raw: dateString,
          parsed: date.toISOString(),
          formatted: date.toLocaleString(),
          timestamp: date.getTime()
        };
      }

      return {
        raw: dateString,
        parsed: null,
        formatted: dateString,
        timestamp: null
      };

    } catch (error) {
      return {
        raw: dateString,
        parsed: null,
        formatted: dateString,
        timestamp: null,
        error: error.message
      };
    }
  }

  /**
   * Format file size for display
   * @param {number} bytes - File size in bytes
   * @returns {string} - Formatted file size
   */
  formatFileSize(bytes) {
    if (bytes === 0) { return '0 Bytes'; }

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Get extractor configuration
   * @returns {Object} - Current configuration
   */
  getConfig() {
    return { ...this.config };
  }

  /**
   * Update extractor configuration
   * @param {Object} newConfig - New configuration options
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
  }
}

// Create singleton instance
const pdfMetadataExtractor = new PDFMetadataExtractor();

export { pdfMetadataExtractor };
export default pdfMetadataExtractor;
