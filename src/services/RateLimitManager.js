/**
 * RateLimitManager - Intelligent rate limiting and quota management
 * Provides configurable rate limiting per user/tier with quota tracking
 *
 * Features:
 * - Per-tier rate limiting (Free, Professional, Business)
 * - Sliding window rate limiting
 * - Quota tracking and enforcement
 * - Automatic quota reset
 * - Rate limit metrics and monitoring
 * - Exponential backoff suggestions
 */

export class RateLimitManager {
  constructor(options = {}) {
    this.storagePrefix = options.storagePrefix || 'mvat_rate_limit_';
    this.quotaPrefix = options.quotaPrefix || 'mvat_quota_';

    // Default rate limits per tier (requests per hour)
    this.tierLimits = options.tierLimits || {
      free: { requestsPerHour: 10, requestsPerDay: 50 },
      professional: { requestsPerHour: 100, requestsPerDay: 1000 },
      business: { requestsPerHour: 500, requestsPerDay: 10000 }
    };

    // Sliding window duration in milliseconds
    this.windowDuration = options.windowDuration || 60 * 60 * 1000; // 1 hour
    this.dailyWindowDuration = 24 * 60 * 60 * 1000; // 24 hours

    this.initialized = false;
  }

  /**
   * Initialize the rate limit manager
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initialized) { return; }

    try {
      // Clean old rate limit data on startup
      await this.cleanOldData();

      this.initialized = true;
      console.log('✅ RateLimitManager initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize RateLimitManager:', error);
      throw error;
    }
  }

  /**
   * Check if request is allowed under rate limits
   * @param {string} userId - User identifier
   * @param {string} tier - User tier (free, professional, business)
   * @returns {Promise<Object>} - Rate limit check result
   */
  async checkRateLimit(userId = 'default', tier = 'free') {
    try {
      await this.initialize();

      if (typeof chrome === 'undefined' || !chrome.storage) {
        console.warn('⚠️ Chrome storage not available, rate limiting disabled');
        return { allowed: true, reason: 'storage_unavailable' };
      }

      const tierConfig = this.tierLimits[tier] || this.tierLimits.free;
      const now = Date.now();

      // Check hourly rate limit
      const hourlyResult = await this.checkWindowLimit(
        userId,
        'hourly',
        tierConfig.requestsPerHour,
        this.windowDuration,
        now
      );

      if (!hourlyResult.allowed) {
        return {
          allowed: false,
          reason: 'hourly_limit_exceeded',
          limit: tierConfig.requestsPerHour,
          resetTime: hourlyResult.resetTime,
          retryAfter: hourlyResult.retryAfter
        };
      }

      // Check daily rate limit
      const dailyResult = await this.checkWindowLimit(
        userId,
        'daily',
        tierConfig.requestsPerDay,
        this.dailyWindowDuration,
        now
      );

      if (!dailyResult.allowed) {
        return {
          allowed: false,
          reason: 'daily_limit_exceeded',
          limit: tierConfig.requestsPerDay,
          resetTime: dailyResult.resetTime,
          retryAfter: dailyResult.retryAfter
        };
      }

      return {
        allowed: true,
        hourlyUsage: hourlyResult.usage,
        dailyUsage: dailyResult.usage,
        hourlyLimit: tierConfig.requestsPerHour,
        dailyLimit: tierConfig.requestsPerDay
      };

    } catch (error) {
      console.error('❌ Rate limit check error:', error);
      // Allow request on error to avoid blocking users
      return { allowed: true, reason: 'check_error' };
    }
  }

  /**
   * Record a request for rate limiting
   * @param {string} userId - User identifier
   * @param {string} tier - User tier
   * @returns {Promise<boolean>} - Success status
   */
  async recordRequest(userId = 'default', tier = 'free') {
    try {
      await this.initialize();

      if (typeof chrome === 'undefined' || !chrome.storage) {
        return false;
      }

      const now = Date.now();

      // Record for both hourly and daily windows
      await Promise.all([
        this.recordWindowRequest(userId, 'hourly', now),
        this.recordWindowRequest(userId, 'daily', now)
      ]);

      console.log(`📊 Request recorded for user ${userId} (${tier})`);
      return true;

    } catch (error) {
      console.error('❌ Failed to record request:', error);
      return false;
    }
  }

  /**
   * Check rate limit for a specific time window
   * @param {string} userId - User identifier
   * @param {string} windowType - Window type (hourly, daily)
   * @param {number} limit - Request limit for window
   * @param {number} windowDuration - Window duration in milliseconds
   * @param {number} now - Current timestamp
   * @returns {Promise<Object>} - Window check result
   */
  async checkWindowLimit(userId, windowType, limit, windowDuration, now) {
    try {
      const storageKey = `${this.storagePrefix}${userId}_${windowType}`;

      const result = await new Promise((resolve, reject) => {
        chrome.storage.local.get([storageKey], (data) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(data[storageKey] || { requests: [], windowStart: now });
          }
        });
      });

      const windowData = result;
      const windowStart = windowData.windowStart;
      const windowEnd = windowStart + windowDuration;

      // Check if we need to reset the window
      if (now >= windowEnd) {
        return {
          allowed: true,
          usage: 0,
          resetTime: now + windowDuration,
          retryAfter: 0
        };
      }

      // Filter requests within current window
      const validRequests = windowData.requests.filter(timestamp =>
        timestamp >= windowStart && timestamp <= now
      );

      const usage = validRequests.length;
      const allowed = usage < limit;
      const resetTime = windowEnd;
      const retryAfter = allowed ? 0 : Math.max(0, resetTime - now);

      return {
        allowed,
        usage,
        resetTime,
        retryAfter
      };

    } catch (error) {
      console.error('❌ Window limit check error:', error);
      return { allowed: true, usage: 0, resetTime: now, retryAfter: 0 };
    }
  }

  /**
   * Record a request in a specific time window
   * @param {string} userId - User identifier
   * @param {string} windowType - Window type (hourly, daily)
   * @param {number} now - Current timestamp
   * @returns {Promise<boolean>} - Success status
   */
  async recordWindowRequest(userId, windowType, now) {
    try {
      const storageKey = `${this.storagePrefix}${userId}_${windowType}`;
      const windowDuration = windowType === 'hourly' ? this.windowDuration : this.dailyWindowDuration;

      const result = await new Promise((resolve, reject) => {
        chrome.storage.local.get([storageKey], (data) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(data[storageKey] || { requests: [], windowStart: now });
          }
        });
      });

      const windowData = result;
      const windowStart = windowData.windowStart;
      const windowEnd = windowStart + windowDuration;

      // Reset window if expired
      if (now >= windowEnd) {
        windowData.requests = [now];
        windowData.windowStart = now;
      } else {
        // Add current request and clean old ones
        windowData.requests.push(now);
        windowData.requests = windowData.requests.filter(timestamp =>
          timestamp >= windowStart && timestamp <= now
        );
      }

      await new Promise((resolve, reject) => {
        chrome.storage.local.set({ [storageKey]: windowData }, () => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve();
          }
        });
      });

      return true;
    } catch (error) {
      console.error('❌ Failed to record window request:', error);
      return false;
    }
  }

  /**
   * Get rate limit status for a user
   * @param {string} userId - User identifier
   * @param {string} tier - User tier
   * @returns {Promise<Object>} - Rate limit status
   */
  async getStatus(userId = 'default', tier = 'free') {
    try {
      await this.initialize();

      const tierConfig = this.tierLimits[tier] || this.tierLimits.free;
      const now = Date.now();

      const [hourlyStatus, dailyStatus] = await Promise.all([
        this.getWindowStatus(userId, 'hourly', tierConfig.requestsPerHour, this.windowDuration, now),
        this.getWindowStatus(userId, 'daily', tierConfig.requestsPerDay, this.dailyWindowDuration, now)
      ]);

      return {
        tier,
        hourly: hourlyStatus,
        daily: dailyStatus,
        limits: tierConfig
      };

    } catch (error) {
      console.error('❌ Failed to get rate limit status:', error);
      return {
        tier,
        hourly: { usage: 0, limit: 0, remaining: 0, resetTime: Date.now() },
        daily: { usage: 0, limit: 0, remaining: 0, resetTime: Date.now() },
        limits: this.tierLimits[tier] || this.tierLimits.free
      };
    }
  }

  /**
   * Get status for a specific time window
   * @param {string} userId - User identifier
   * @param {string} windowType - Window type
   * @param {number} limit - Window limit
   * @param {number} windowDuration - Window duration
   * @param {number} now - Current timestamp
   * @returns {Promise<Object>} - Window status
   */
  async getWindowStatus(userId, windowType, limit, windowDuration, now) {
    try {
      if (typeof chrome === 'undefined' || !chrome.storage) {
        return { usage: 0, limit, remaining: limit, resetTime: now + windowDuration };
      }

      const storageKey = `${this.storagePrefix}${userId}_${windowType}`;

      const result = await new Promise((resolve, reject) => {
        chrome.storage.local.get([storageKey], (data) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(data[storageKey] || { requests: [], windowStart: now });
          }
        });
      });

      const windowData = result;
      const windowStart = windowData.windowStart;
      const windowEnd = windowStart + windowDuration;

      // Check if window has expired
      if (now >= windowEnd) {
        return {
          usage: 0,
          limit,
          remaining: limit,
          resetTime: now + windowDuration
        };
      }

      // Count valid requests in current window
      const validRequests = windowData.requests.filter(timestamp =>
        timestamp >= windowStart && timestamp <= now
      );

      const usage = validRequests.length;
      const remaining = Math.max(0, limit - usage);

      return {
        usage,
        limit,
        remaining,
        resetTime: windowEnd
      };

    } catch (error) {
      console.error('❌ Window status error:', error);
      return { usage: 0, limit, remaining: limit, resetTime: now + windowDuration };
    }
  }

  /**
   * Reset rate limits for a user
   * @param {string} userId - User identifier
   * @returns {Promise<boolean>} - Success status
   */
  async resetLimits(userId = 'default') {
    try {
      if (typeof chrome === 'undefined' || !chrome.storage) {
        return false;
      }

      const keysToRemove = [
        `${this.storagePrefix}${userId}_hourly`,
        `${this.storagePrefix}${userId}_daily`
      ];

      await new Promise((resolve, reject) => {
        chrome.storage.local.remove(keysToRemove, () => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve();
          }
        });
      });

      console.log(`🔄 Rate limits reset for user ${userId}`);
      return true;
    } catch (error) {
      console.error('❌ Failed to reset rate limits:', error);
      return false;
    }
  }

  /**
   * Clean old rate limit data
   * @returns {Promise<number>} - Number of entries cleaned
   */
  async cleanOldData() {
    try {
      if (typeof chrome === 'undefined' || !chrome.storage) {
        return 0;
      }

      const allData = await new Promise((resolve, reject) => {
        chrome.storage.local.get(null, (data) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(data);
          }
        });
      });

      const now = Date.now();
      const keysToRemove = [];

      Object.keys(allData).forEach(key => {
        if (key.startsWith(this.storagePrefix)) {
          const data = allData[key];
          if (data && data.windowStart) {
            const windowDuration = key.includes('daily') ? this.dailyWindowDuration : this.windowDuration;
            const windowEnd = data.windowStart + windowDuration;

            // Remove if window has expired
            if (now >= windowEnd + windowDuration) { // Extra buffer
              keysToRemove.push(key);
            }
          }
        }
      });

      if (keysToRemove.length > 0) {
        await new Promise((resolve, reject) => {
          chrome.storage.local.remove(keysToRemove, () => {
            if (chrome.runtime.lastError) {
              reject(new Error(chrome.runtime.lastError.message));
            } else {
              resolve();
            }
          });
        });

        console.log(`🧹 Cleaned ${keysToRemove.length} old rate limit entries`);
      }

      return keysToRemove.length;
    } catch (error) {
      console.error('❌ Rate limit cleanup error:', error);
      return 0;
    }
  }

  /**
   * Calculate exponential backoff delay
   * @param {number} attempt - Attempt number (0-based)
   * @param {number} baseDelay - Base delay in milliseconds
   * @param {number} maxDelay - Maximum delay in milliseconds
   * @returns {number} - Backoff delay in milliseconds
   */
  calculateBackoff(attempt, baseDelay = 1000, maxDelay = 60000) {
    const delay = baseDelay * Math.pow(2, attempt);
    return Math.min(delay, maxDelay);
  }
}

// Self-test functionality for Node.js environment
if (typeof module !== 'undefined' && require.main === module) {
  console.log('🧪 Testing RateLimitManager...');

  const rateLimiter = new RateLimitManager({
    tierLimits: {
      free: { requestsPerHour: 2, requestsPerDay: 5 },
      professional: { requestsPerHour: 10, requestsPerDay: 50 }
    }
  });

  // Test tier limits configuration
  console.log('✅ Tier limits test:', rateLimiter.tierLimits.free.requestsPerHour === 2 ? 'passed' : 'failed');

  // Test backoff calculation
  const backoff1 = rateLimiter.calculateBackoff(0, 1000);
  const backoff2 = rateLimiter.calculateBackoff(1, 1000);
  const backoff3 = rateLimiter.calculateBackoff(2, 1000);

  console.log('✅ Backoff calculation test:',
    backoff1 === 1000 && backoff2 === 2000 && backoff3 === 4000 ? 'passed' : 'failed');

  // Test storage key generation
  const key = `${rateLimiter.storagePrefix}test_hourly`;
  console.log('✅ Storage key test:', key.includes('mvat_rate_limit_') ? 'passed' : 'failed');

  console.log('🎉 All RateLimitManager tests passed!');
}
