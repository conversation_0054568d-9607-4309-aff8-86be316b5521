/**
 * DeepSeek Service for AI-powered document analysis
 * Handles communication with DeepSeek API for text analysis
 */

class DeepSeekService {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.baseUrl = 'https://api.deepseek.com/v1';
  }

  /**
   * Analyze text using DeepSeek API
   * @param {string} text - Text content to analyze
   * @returns {Promise<Object>} Analysis results
   */
  async analyzeText(text) {
    try {
      const response = await fetch(`${this.baseUrl}/analyze`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          text: text,
          analysis_type: 'document',
          options: {
            extract_fields: true,
            detect_language: true,
            confidence_scores: true
          }
        })
      });

      if (!response.ok) {
        throw new Error(`DeepSeek API error: ${response.statusText}`);
      }

      const result = await response.json();
      return this.processAnalysisResult(result);
    } catch (error) {
      console.error('Error in DeepSeek analysis:', error);
      throw error;
    }
  }

  /**
   * Process and structure the analysis result
   * @param {Object} result - Raw API response
   * @returns {Object} Processed analysis result
   */
  processAnalysisResult(result) {
    return {
      fields: this.extractFields(result),
      language: result.detected_language,
      confidence: result.confidence_scores,
      raw_result: result
    };
  }

  /**
   * Extract structured fields from analysis result
   * @param {Object} result - Analysis result
   * @returns {Object} Extracted fields with confidence scores
   */
  extractFields(result) {
    const fields = {};
    const confidenceThreshold = 0.8; // 80% confidence threshold

    // Process each detected field
    if (result.extracted_fields) {
      for (const [field, data] of Object.entries(result.extracted_fields)) {
        if (data.confidence >= confidenceThreshold) {
          fields[field] = {
            value: data.value,
            confidence: data.confidence,
            source: 'deepseek'
          };
        }
      }
    }

    return fields;
  }

  /**
   * Validate API key
   * @returns {Promise<boolean>}
   */
  async validateApiKey() {
    try {
      const response = await fetch(`${this.baseUrl}/validate`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`
        }
      });
      return response.ok;
    } catch (error) {
      console.error('API key validation error:', error);
      return false;
    }
  }

  /**
   * Get API usage statistics
   * @returns {Promise<Object>}
   */
  async getUsageStats() {
    try {
      const response = await fetch(`${this.baseUrl}/usage`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to get usage stats');
      }

      return await response.json();
    } catch (error) {
      console.error('Error getting usage stats:', error);
      throw error;
    }
  }
}

export default DeepSeekService;
