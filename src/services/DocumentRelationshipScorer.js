/**
 * Document Relationship Scorer
 *
 * Advanced scoring and ranking system for document relationships based on
 * multiple factors including similarity, metadata, content analysis, and business context.
 *
 * <AUTHOR> Agent
 * @version 1.0.0
 * @since 2025-01-28
 */

import { ProcessingLogger } from '../utils/ProcessingLogger.js';

export class DocumentRelationshipScorer {
  constructor(options = {}) {
    this.logger = new ProcessingLogger('DocumentRelationshipScorer');

    // Scoring weights configuration
    this.weights = {
      vectorSimilarity: options.vectorSimilarity || 0.4,
      contentSimilarity: options.contentSimilarity || 0.2,
      metadataSimilarity: options.metadataSimilarity || 0.15,
      temporalProximity: options.temporalProximity || 0.1,
      businessContext: options.businessContext || 0.1,
      documentType: options.documentType || 0.05,
      ...options.weights
    };

    // Scoring thresholds
    this.thresholds = {
      strongRelationship: options.strongRelationship || 0.8,
      moderateRelationship: options.moderateRelationship || 0.6,
      weakRelationship: options.weakRelationship || 0.4,
      ...options.thresholds
    };

    // Business context rules
    this.businessRules = {
      sameVendor: 0.3,
      sameProject: 0.4,
      sameTimeframe: 0.2,
      relatedAmount: 0.15,
      sameCategory: 0.25,
      ...options.businessRules
    };

    // Performance metrics
    this.metrics = {
      totalScorings: 0,
      averageScoringTime: 0,
      totalScoringTime: 0,
      relationshipDistribution: {
        strong: 0,
        moderate: 0,
        weak: 0,
        none: 0
      }
    };
  }

  /**
   * Score relationship between two documents
   * @param {Object} doc1 - First document with embedding and metadata
   * @param {Object} doc2 - Second document with embedding and metadata
   * @param {Object} options - Scoring options
   * @returns {Object} Comprehensive relationship score
   */
  scoreRelationship(doc1, doc2, options = {}) {
    const startTime = Date.now();
    const scoringId = `score_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      this.metrics.totalScorings++;

      this.logger.log('🎯 Starting relationship scoring', {
        scoringId,
        doc1Id: doc1.id || doc1.metadata?.documentId,
        doc2Id: doc2.id || doc2.metadata?.documentId,
        useWeights: Object.keys(this.weights)
      });

      // Calculate individual scores
      const scores = {
        vectorSimilarity: this.calculateVectorSimilarity(doc1, doc2),
        contentSimilarity: this.calculateContentSimilarity(doc1, doc2),
        metadataSimilarity: this.calculateMetadataSimilarity(doc1, doc2),
        temporalProximity: this.calculateTemporalProximity(doc1, doc2),
        businessContext: this.calculateBusinessContext(doc1, doc2),
        documentType: this.calculateDocumentTypeSimilarity(doc1, doc2)
      };

      // Calculate weighted composite score
      const compositeScore = this.calculateCompositeScore(scores);

      // Determine relationship strength
      const relationshipStrength = this.determineRelationshipStrength(compositeScore);

      // Calculate confidence
      const confidence = this.calculateConfidence(scores, doc1, doc2);

      // Generate explanation
      const explanation = this.generateExplanation(scores, relationshipStrength);

      const result = {
        scoringId,
        compositeScore,
        relationshipStrength,
        confidence,
        scores,
        weights: this.weights,
        explanation,
        metadata: {
          doc1Id: doc1.id || doc1.metadata?.documentId,
          doc2Id: doc2.id || doc2.metadata?.documentId,
          scoringTime: Date.now() - startTime,
          timestamp: new Date().toISOString(),
          version: '1.0.0'
        }
      };

      // Update metrics
      this.updateMetrics(relationshipStrength, Date.now() - startTime);

      this.logger.log('✅ Relationship scoring completed', {
        scoringId,
        compositeScore: compositeScore.toFixed(4),
        relationshipStrength,
        confidence: confidence.toFixed(4),
        scoringTime: result.metadata.scoringTime
      });

      return result;

    } catch (error) {
      const scoringTime = Date.now() - startTime;
      this.updateMetrics('error', scoringTime);

      this.logger.error('❌ Relationship scoring failed', {
        scoringId,
        error: error.message,
        scoringTime
      });

      return {
        scoringId,
        compositeScore: 0,
        relationshipStrength: 'none',
        confidence: 0,
        error: error.message,
        metadata: {
          scoringTime,
          timestamp: new Date().toISOString()
        }
      };
    }
  }

  /**
   * Score multiple document relationships in batch
   * @param {Array} documents - Array of documents
   * @param {Object} options - Scoring options
   * @returns {Array} Relationship matrix with scores
   */
  scoreMultipleRelationships(documents, options = {}) {
    const startTime = Date.now();

    try {
      this.logger.log('🎯 Starting batch relationship scoring', {
        documentCount: documents.length,
        totalPairs: (documents.length * (documents.length - 1)) / 2
      });

      const relationships = [];

      for (let i = 0; i < documents.length; i++) {
        for (let j = i + 1; j < documents.length; j++) {
          const score = this.scoreRelationship(documents[i], documents[j], options);

          if (score.compositeScore >= this.thresholds.weakRelationship) {
            relationships.push({
              doc1Index: i,
              doc2Index: j,
              doc1Id: documents[i].id || documents[i].metadata?.documentId,
              doc2Id: documents[j].id || documents[j].metadata?.documentId,
              ...score
            });
          }
        }
      }

      // Sort by composite score (descending)
      relationships.sort((a, b) => b.compositeScore - a.compositeScore);

      const processingTime = Date.now() - startTime;
      this.logger.log('✅ Batch relationship scoring completed', {
        documentCount: documents.length,
        relationshipsFound: relationships.length,
        processingTime
      });

      return {
        relationships,
        summary: {
          totalDocuments: documents.length,
          totalRelationships: relationships.length,
          processingTime,
          averageScore: relationships.length > 0
            ? relationships.reduce((sum, r) => sum + r.compositeScore, 0) / relationships.length
            : 0
        }
      };

    } catch (error) {
      this.logger.error('❌ Batch relationship scoring failed', {
        error: error.message,
        documentCount: documents.length
      });

      return {
        relationships: [],
        error: error.message
      };
    }
  }

  /**
   * Calculate vector similarity score
   * @private
   */
  calculateVectorSimilarity(doc1, doc2) {
    try {
      if (!doc1.embedding?.vector || !doc2.embedding?.vector) {
        return 0;
      }

      // Use existing similarity if available
      if (doc1.similarity?.score !== undefined) {
        return doc1.similarity.score;
      }

      // Calculate cosine similarity
      const vector1 = doc1.embedding.vector;
      const vector2 = doc2.embedding.vector;

      if (vector1.length !== vector2.length) {
        return 0;
      }

      let dotProduct = 0;
      let magnitude1 = 0;
      let magnitude2 = 0;

      for (let i = 0; i < vector1.length; i++) {
        dotProduct += vector1[i] * vector2[i];
        magnitude1 += vector1[i] * vector1[i];
        magnitude2 += vector2[i] * vector2[i];
      }

      magnitude1 = Math.sqrt(magnitude1);
      magnitude2 = Math.sqrt(magnitude2);

      if (magnitude1 === 0 || magnitude2 === 0) {
        return 0;
      }

      return dotProduct / (magnitude1 * magnitude2);

    } catch (error) {
      this.logger.warn('⚠️ Vector similarity calculation failed', { error: error.message });
      return 0;
    }
  }

  /**
   * Calculate content similarity score
   * @private
   */
  calculateContentSimilarity(doc1, doc2) {
    try {
      const text1 = doc1.text || doc1.content || '';
      const text2 = doc2.text || doc2.content || '';

      if (!text1 || !text2) {
        return 0;
      }

      // Simple Jaccard similarity on words
      const words1 = new Set(text1.toLowerCase().split(/\s+/));
      const words2 = new Set(text2.toLowerCase().split(/\s+/));

      const intersection = new Set([...words1].filter(x => words2.has(x)));
      const union = new Set([...words1, ...words2]);

      return union.size > 0 ? intersection.size / union.size : 0;

    } catch (error) {
      this.logger.warn('⚠️ Content similarity calculation failed', { error: error.message });
      return 0;
    }
  }

  /**
   * Calculate metadata similarity score
   * @private
   */
  calculateMetadataSimilarity(doc1, doc2) {
    try {
      const meta1 = doc1.metadata || {};
      const meta2 = doc2.metadata || {};

      let score = 0;
      let factors = 0;

      // Compare specific metadata fields
      const fields = ['vendor', 'supplier', 'company', 'category', 'type', 'currency'];

      for (const field of fields) {
        if (meta1[field] && meta2[field]) {
          factors++;
          if (meta1[field].toLowerCase() === meta2[field].toLowerCase()) {
            score += 1;
          }
        }
      }

      return factors > 0 ? score / factors : 0;

    } catch (error) {
      this.logger.warn('⚠️ Metadata similarity calculation failed', { error: error.message });
      return 0;
    }
  }

  /**
   * Calculate temporal proximity score
   * @private
   */
  calculateTemporalProximity(doc1, doc2) {
    try {
      const date1 = this.extractDate(doc1);
      const date2 = this.extractDate(doc2);

      if (!date1 || !date2) {
        return 0;
      }

      const timeDiff = Math.abs(date1.getTime() - date2.getTime());
      const daysDiff = timeDiff / (1000 * 60 * 60 * 24);

      // Score based on temporal proximity (closer = higher score)
      if (daysDiff <= 1) { return 1.0; }
      if (daysDiff <= 7) { return 0.8; }
      if (daysDiff <= 30) { return 0.6; }
      if (daysDiff <= 90) { return 0.4; }
      if (daysDiff <= 365) { return 0.2; }
      return 0.1;

    } catch (error) {
      this.logger.warn('⚠️ Temporal proximity calculation failed', { error: error.message });
      return 0;
    }
  }

  /**
   * Calculate business context score
   * @private
   */
  calculateBusinessContext(doc1, doc2) {
    try {
      let score = 0;
      const meta1 = doc1.metadata || {};
      const meta2 = doc2.metadata || {};

      // Same vendor/supplier
      if (meta1.vendor && meta2.vendor &&
          meta1.vendor.toLowerCase() === meta2.vendor.toLowerCase()) {
        score += this.businessRules.sameVendor;
      }

      // Same project
      if (meta1.project && meta2.project &&
          meta1.project.toLowerCase() === meta2.project.toLowerCase()) {
        score += this.businessRules.sameProject;
      }

      // Related amounts
      if (meta1.amount && meta2.amount) {
        const ratio = Math.min(meta1.amount, meta2.amount) / Math.max(meta1.amount, meta2.amount);
        if (ratio > 0.8) {
          score += this.businessRules.relatedAmount;
        }
      }

      // Same category
      if (meta1.category && meta2.category &&
          meta1.category.toLowerCase() === meta2.category.toLowerCase()) {
        score += this.businessRules.sameCategory;
      }

      return Math.min(score, 1.0);

    } catch (error) {
      this.logger.warn('⚠️ Business context calculation failed', { error: error.message });
      return 0;
    }
  }

  /**
   * Calculate document type similarity score
   * @private
   */
  calculateDocumentTypeSimilarity(doc1, doc2) {
    try {
      const type1 = doc1.metadata?.documentType || doc1.type || '';
      const type2 = doc2.metadata?.documentType || doc2.type || '';

      if (!type1 || !type2) {
        return 0;
      }

      return type1.toLowerCase() === type2.toLowerCase() ? 1.0 : 0.0;

    } catch (error) {
      this.logger.warn('⚠️ Document type similarity calculation failed', { error: error.message });
      return 0;
    }
  }

  /**
   * Calculate weighted composite score
   * @private
   */
  calculateCompositeScore(scores) {
    let compositeScore = 0;
    let totalWeight = 0;

    for (const [factor, score] of Object.entries(scores)) {
      const weight = this.weights[factor] || 0;
      compositeScore += score * weight;
      totalWeight += weight;
    }

    return totalWeight > 0 ? compositeScore / totalWeight : 0;
  }

  /**
   * Determine relationship strength
   * @private
   */
  determineRelationshipStrength(score) {
    if (score >= this.thresholds.strongRelationship) {
      return 'strong';
    } else if (score >= this.thresholds.moderateRelationship) {
      return 'moderate';
    } else if (score >= this.thresholds.weakRelationship) {
      return 'weak';
    }
    return 'none';
  }

  /**
   * Calculate confidence in the relationship score
   * @private
   */
  calculateConfidence(scores, doc1, doc2) {
    let confidence = 0;
    let factors = 0;

    // Base confidence on data availability
    if (doc1.embedding && doc2.embedding) {
      confidence += 0.3;
      factors++;
    }

    if (doc1.metadata && doc2.metadata) {
      confidence += 0.2;
      factors++;
    }

    if (doc1.text && doc2.text) {
      confidence += 0.2;
      factors++;
    }

    // Adjust based on score consistency
    const scoreValues = Object.values(scores);
    const avgScore = scoreValues.reduce((sum, s) => sum + s, 0) / scoreValues.length;
    const variance = scoreValues.reduce((sum, s) => sum + Math.pow(s - avgScore, 2), 0) / scoreValues.length;

    // Lower variance = higher confidence
    confidence += (1 - Math.min(variance, 1)) * 0.3;

    return Math.min(confidence, 1.0);
  }

  /**
   * Generate human-readable explanation
   * @private
   */
  generateExplanation(scores, relationshipStrength) {
    const explanations = [];

    if (scores.vectorSimilarity > 0.7) {
      explanations.push('High semantic similarity');
    }

    if (scores.metadataSimilarity > 0.5) {
      explanations.push('Similar metadata attributes');
    }

    if (scores.temporalProximity > 0.6) {
      explanations.push('Close temporal proximity');
    }

    if (scores.businessContext > 0.3) {
      explanations.push('Related business context');
    }

    if (explanations.length === 0) {
      explanations.push('Low overall similarity');
    }

    return {
      strength: relationshipStrength,
      factors: explanations,
      summary: `${relationshipStrength} relationship based on ${explanations.join(', ')}`
    };
  }

  /**
   * Extract date from document
   * @private
   */
  extractDate(doc) {
    const meta = doc.metadata || {};

    // Try various date fields
    const dateFields = ['date', 'createdAt', 'timestamp', 'issueDate', 'documentDate'];

    for (const field of dateFields) {
      if (meta[field]) {
        const date = new Date(meta[field]);
        if (!isNaN(date.getTime())) {
          return date;
        }
      }
    }

    return null;
  }

  /**
   * Update performance metrics
   * @private
   */
  updateMetrics(relationshipStrength, scoringTime) {
    this.metrics.totalScoringTime += scoringTime;
    this.metrics.averageScoringTime = this.metrics.totalScoringTime / this.metrics.totalScorings;

    if (this.metrics.relationshipDistribution[relationshipStrength]) {
      this.metrics.relationshipDistribution[relationshipStrength]++;
    }
  }

  /**
   * Get scoring statistics
   */
  getStats() {
    return {
      ...this.metrics,
      weights: this.weights,
      thresholds: this.thresholds,
      businessRules: this.businessRules
    };
  }

  /**
   * Update scoring weights
   */
  updateWeights(newWeights) {
    this.weights = { ...this.weights, ...newWeights };
    this.logger.log('⚙️ Scoring weights updated', newWeights);
  }

  /**
   * Update scoring thresholds
   */
  updateThresholds(newThresholds) {
    this.thresholds = { ...this.thresholds, ...newThresholds };
    this.logger.log('⚙️ Scoring thresholds updated', newThresholds);
  }

  /**
   * Get configuration
   */
  getConfig() {
    return {
      weights: this.weights,
      thresholds: this.thresholds,
      businessRules: this.businessRules
    };
  }
}
