/**
 * PDFProcessingService - Enhanced PDF processing with PDF.js
 * Implements Task 2.2.1 - PDF.js Integration
 *
 * Features:
 * - Text extraction from PDF documents
 * - Multi-page PDF support
 * - Memory optimization for large files
 * - Progress tracking
 * - Error handling and recovery
 * - CSP-compliant worker configuration
 */

import * as pdfjsLib from 'pdfjs-dist';
import { pdfProgressTracker } from './PDFProgressTracker.js';
import { pdfMetadataExtractor } from './PDFMetadataExtractor.js';
import { performanceMonitor, memoryOptimizer, optimizeForLargeFiles } from '../utils/pdfPerformanceUtils.js';
import { consolidatedFileValidationService } from './ConsolidatedFileValidationService.js';

export class PDFProcessingService {
  constructor(options = {}) {
    this.config = {
      maxFileSize: options.maxFileSize || 10 * 1024 * 1024, // 10MB
      maxPages: options.maxPages || 100,
      textExtractionTimeout: options.textExtractionTimeout || 30000, // 30 seconds
      renderScale: options.renderScale || 2.0,
      enableOCRFallback: options.enableOCRFallback !== false,
      ...options
    };

    this.initialized = false;
    this.workerInitialized = false;
  }

  /**
   * Initialize the PDF processing service
   */
  async initialize() {
    if (this.initialized) { return; }

    try {
      // Configure PDF.js worker for Chrome extension
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        // Chrome extension environment
        pdfjsLib.GlobalWorkerOptions.workerSrc = chrome.runtime.getURL('assets/pdf.worker.min.js');
      } else {
        // Development/test environment
        pdfjsLib.GlobalWorkerOptions.workerSrc = '/node_modules/pdfjs-dist/build/pdf.worker.min.js';
      }

      this.initialized = true;
      console.log('PDFProcessingService initialized');
    } catch (error) {
      console.error('Failed to initialize PDFProcessingService:', error);
      throw new Error(`PDF service initialization failed: ${error.message}`);
    }
  }

  /**
   * Extract text from PDF file with enhanced progress tracking and performance monitoring
   * @param {File} file - PDF file to process
   * @param {Object} options - Processing options
   * @returns {Promise<Object>} - Extraction result
   */
  async extractText(file, options = {}) {
    await this.initialize();

    const {
      onProgress = null,
      enableOCRFallback = this.config.enableOCRFallback,
      maxPages = this.config.maxPages,
      trackingId = `pdf_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      extractMetadata = true,
      optimizePerformance = true
    } = options;

    // Start performance monitoring
    const perfMonitor = performanceMonitor.startMonitoring(trackingId, {
      trackMemory: true,
      trackTime: true
    });

    // Start progress tracking
    const progressTracker = pdfProgressTracker.startTracking(trackingId, {
      fileName: file.name,
      fileSize: file.size,
      onProgress: onProgress
    });

    try {
      // Validate file
      await this.validatePDFFile(file);

      progressTracker.updateStage('initializing', 0);
      perfMonitor.checkpoint('validation_complete');

      // Optimize settings for large files
      const optimizations = optimizePerformance ? optimizeForLargeFiles(file.size) : {};

      progressTracker.updateStage('loading', 0);

      // Convert file to array buffer with memory optimization
      const extractionResult = await memoryOptimizer.withOptimization(async () => {
        const arrayBuffer = await file.arrayBuffer();
        const uint8Array = new Uint8Array(arrayBuffer);

        progressTracker.updateStage('parsing', 10);
        perfMonitor.checkpoint('file_loaded');

        // Load PDF document with optimized settings
        const loadingTask = pdfjsLib.getDocument({
          data: uint8Array,
          verbosity: 0,
          maxImageSize: optimizations.maxImageSize || 1024 * 1024,
          disableFontFace: optimizations.disableFontFace || true,
          disableRange: false,
          disableStream: !optimizations.enableStreaming
        });

        const pdf = await loadingTask.promise;
        const numPages = Math.min(pdf.numPages, maxPages);

        progressTracker.setMetadata({ totalPages: pdf.numPages, numPages });
        progressTracker.updateStage('extracting', 20);
        perfMonitor.checkpoint('pdf_loaded');

        // Extract text from all pages
        const textResult = await this.extractTextFromPages(pdf, numPages, progressTracker);

        progressTracker.updateStage('analyzing', 80);
        perfMonitor.checkpoint('text_extracted');

        // Extract comprehensive metadata if requested
        let metadataResult = null;
        if (extractMetadata) {
          try {
            metadataResult = await pdfMetadataExtractor.extractMetadata(file, {
              onProgress: (progress) => {
                progressTracker.updateProgress(80 + (progress.progress * 0.15));
              }
            });
            perfMonitor.checkpoint('metadata_extracted');
          } catch (metadataError) {
            progressTracker.addWarning(`Metadata extraction failed: ${metadataError.message}`);
          }
        }

        // Check extraction quality
        const qualityCheck = this.assessTextQuality(textResult.text);

        progressTracker.updateStage('finalizing', 95);

        return {
          text: textResult.text,
          pageTexts: textResult.pageTexts,
          processingTime: textResult.processingTime,
          quality: qualityCheck,
          metadata: metadataResult?.metadata || null,
          pdf,
          numPages,
          totalPages: pdf.numPages
        };
      }, {
        forceGC: file.size > 10 * 1024 * 1024, // Force GC for large files
        cleanupInterval: 5000
      });

      progressTracker.updateStage('complete', 100);
      const perfMetrics = perfMonitor.stop();

      const result = {
        success: true,
        text: extractionResult.text,
        pageTexts: extractionResult.pageTexts,
        metadata: {
          numPages: extractionResult.numPages,
          totalPages: extractionResult.totalPages,
          extractionMethod: 'pdf_text_enhanced',
          quality: extractionResult.quality,
          processingTime: extractionResult.processingTime,
          fileSize: file.size,
          fileName: file.name,
          trackingId,
          performance: perfMetrics,
          comprehensive: extractionResult.metadata
        }
      };

      progressTracker.complete(result);
      return result;

    } catch (error) {
      console.error('Enhanced PDF text extraction failed:', error);

      progressTracker.addError(error);
      perfMonitor.stop();

      const result = {
        success: false,
        error: error.message,
        text: '',
        metadata: {
          fileName: file.name,
          fileSize: file.size,
          extractionMethod: 'failed',
          trackingId,
          error: error.message
        }
      };

      progressTracker.complete(result);
      return result;
    }
  }

  /**
   * Extract text from PDF pages with enhanced progress tracking
   * @param {Object} pdf - PDF document object
   * @param {number} numPages - Number of pages to process
   * @param {Object} progressTracker - Progress tracker instance
   * @returns {Promise<Object>} - Extraction result
   */
  async extractTextFromPages(pdf, numPages, progressTracker) {
    const startTime = Date.now();
    let fullText = '';
    const pageTexts = [];
    let successfulPages = 0;
    let failedPages = 0;

    for (let pageNum = 1; pageNum <= numPages; pageNum++) {
      try {
        const page = await pdf.getPage(pageNum);
        const textContent = await page.getTextContent();

        // Extract text items and join them
        const pageText = textContent.items
          .map(item => item.str)
          .join(' ')
          .trim();

        if (pageText) {
          pageTexts.push({
            pageNumber: pageNum,
            text: pageText,
            length: pageText.length,
            wordCount: pageText.split(/\s+/).filter(word => word.length > 0).length
          });
          fullText += pageText + '\n';
          successfulPages++;
        }

        // Update progress with detailed information
        const progress = 20 + (pageNum / numPages) * 60; // 20-80% range for extraction
        progressTracker.updateProgress(progress);

        // Clean up page resources
        page.cleanup();

      } catch (pageError) {
        console.warn(`Failed to extract text from page ${pageNum}:`, pageError);
        progressTracker.addWarning(`Page ${pageNum} extraction failed: ${pageError.message}`);
        failedPages++;
        // Continue with other pages
      }
    }

    const processingTime = Date.now() - startTime;

    // Add extraction statistics to metadata
    progressTracker.setMetadata({
      successfulPages,
      failedPages,
      extractionRate: numPages > 0 ? (successfulPages / numPages) * 100 : 0,
      averagePageLength: pageTexts.length > 0 ?
        pageTexts.reduce((sum, page) => sum + page.length, 0) / pageTexts.length : 0,
      totalWords: pageTexts.reduce((sum, page) => sum + page.wordCount, 0)
    });

    return {
      text: fullText.trim(),
      pageTexts,
      processingTime,
      statistics: {
        successfulPages,
        failedPages,
        totalPages: numPages,
        extractionRate: numPages > 0 ? (successfulPages / numPages) * 100 : 0
      }
    };
  }

  /**
   * Assess the quality of extracted text
   * @param {string} text - Extracted text
   * @returns {Object} - Quality assessment
   */
  assessTextQuality(text) {
    const length = text.length;
    const wordCount = text.split(/\s+/).filter(word => word.length > 0).length;
    const lineCount = text.split('\n').length;

    // Check for common invoice keywords
    const invoiceKeywords = [
      'faktura', 'invoice', 'vat', 'nip', 'tax', 'amount', 'total',
      'sprzedawca', 'nabywca', 'seller', 'buyer', 'kwota', 'suma'
    ];

    const foundKeywords = invoiceKeywords.filter(keyword =>
      text.toLowerCase().includes(keyword)
    );

    // Quality scoring
    let score = 0;
    if (length > 100) { score += 25; }
    if (wordCount > 20) { score += 25; }
    if (foundKeywords.length >= 3) { score += 30; }
    if (lineCount > 5) { score += 20; }

    return {
      score,
      length,
      wordCount,
      lineCount,
      foundKeywords: foundKeywords.length,
      isGoodQuality: score >= 70,
      hasInvoiceContent: foundKeywords.length >= 3
    };
  }

  /**
   * Validate PDF file before processing using consolidated validation service
   * @param {File} file - File to validate
   * @throws {Error} - If validation fails
   */
  async validatePDFFile(file) {
    try {
      // Use consolidated validation service with PDF-specific options
      const validation = await consolidatedFileValidationService.validateFile(file, {
        maxFileSize: this.config.maxFileSize,
        allowedMimeTypes: ['application/pdf'],
        enableSecurityScanning: true,
        enableContentValidation: true
      });

      if (!validation.isValid) {
        throw new Error(validation.errors.join('; '));
      }
    } catch (error) {
      // If it's already a validation error, re-throw it
      if (error.message.includes('validation')) {
        throw error;
      }
      // Otherwise, wrap it in a validation error
      throw new Error(`PDF validation failed: ${error.message}`);
    }
  }

  /**
   * Get PDF document metadata
   * @param {File} file - PDF file
   * @returns {Promise<Object>} - Document metadata
   */
  async getMetadata(file) {
    await this.initialize();

    try {
      await this.validatePDFFile(file);

      const arrayBuffer = await file.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);

      const loadingTask = pdfjsLib.getDocument({ data: uint8Array });
      const pdf = await loadingTask.promise;

      const metadata = await pdf.getMetadata();

      return {
        success: true,
        metadata: {
          numPages: pdf.numPages,
          title: metadata.info?.Title || '',
          author: metadata.info?.Author || '',
          creator: metadata.info?.Creator || '',
          producer: metadata.info?.Producer || '',
          creationDate: metadata.info?.CreationDate || null,
          modificationDate: metadata.info?.ModDate || null,
          pdfVersion: pdf.pdfInfo?.version || '',
          fileSize: file.size,
          fileName: file.name
        }
      };

    } catch (error) {
      console.error('Failed to get PDF metadata:', error);
      return {
        success: false,
        error: error.message,
        metadata: null
      };
    }
  }

  /**
   * Check if PDF.js is properly configured
   * @returns {boolean} - Whether PDF.js is ready
   */
  isReady() {
    return this.initialized && pdfjsLib.GlobalWorkerOptions.workerSrc;
  }

  /**
   * Get service configuration
   * @returns {Object} - Current configuration
   */
  getConfig() {
    return { ...this.config };
  }

  /**
   * Update service configuration
   * @param {Object} newConfig - New configuration options
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
  }
}

// Create singleton instance
const pdfProcessingService = new PDFProcessingService();

export { pdfProcessingService };
export default pdfProcessingService;
