/**
 * Enhanced DeepSeek Analysis Service
 *
 * Provides comprehensive document analysis capabilities using enhanced DeepSeek API integration.
 * Includes document classification, confidence scoring, metadata extraction, and business intelligence.
 *
 * ASSIGNMENT-041: Environment Configuration Fix and DeepSeek Enhancement
 * Epic: EPIC-005 - Enhanced AI Analysis & RAG Integration
 */

import { DeepSeekAPI } from '../api/DeepSeekAPI.js';
import { processingLogger } from '../utils/ProcessingLogger.js';

/**
 * Enhanced DeepSeek Analysis Service Class
 * Provides comprehensive document analysis beyond basic data extraction
 */
export class EnhancedDeepSeekAnalysis {
  constructor() {
    this.deepSeekAPI = new DeepSeekAPI();
    this.analysisCache = new Map();
    this.cacheTimeout = 30 * 60 * 1000; // 30 minutes
  }

  /**
   * Perform comprehensive document analysis
   * @param {string} text - Document text content
   * @param {string} apiKey - DeepSeek API key
   * @param {string} uploadId - Upload session ID for logging
   * @param {Object} options - Analysis options
   * @returns {Promise<Object>} Comprehensive analysis results
   */
  async performComprehensiveAnalysis(text, apiKey, uploadId, options = {}) {
    try {
      processingLogger.info('enhanced_analysis', 'Starting comprehensive document analysis', uploadId, {
        textLength: text.length,
        options
      });

      const startTime = performance.now();

      // Parallel analysis for better performance
      const [
        classification,
        metadata,
        businessIntelligence,
        confidenceScore
      ] = await Promise.all([
        this.classifyDocument(text, apiKey, uploadId),
        this.extractEnhancedMetadata(text, apiKey, uploadId),
        this.generateBusinessIntelligence(text, apiKey, uploadId),
        this.calculateConfidenceScore(text, uploadId)
      ]);

      const analysisTime = performance.now() - startTime;

      const comprehensiveAnalysis = {
        classification,
        metadata,
        businessIntelligence,
        confidenceScore,
        analysisMetrics: {
          processingTimeMs: analysisTime,
          textLength: text.length,
          timestamp: new Date().toISOString(),
          uploadId
        }
      };

      processingLogger.info('enhanced_analysis', 'Comprehensive analysis completed', uploadId, {
        analysisTimeMs: analysisTime,
        classification: classification.documentType,
        confidence: confidenceScore.overall,
        metadataFields: Object.keys(metadata).length
      });

      return comprehensiveAnalysis;

    } catch (error) {
      processingLogger.error('enhanced_analysis', 'Comprehensive analysis failed', uploadId, {
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * Classify document type and context
   * @param {string} text - Document text
   * @param {string} apiKey - API key
   * @param {string} uploadId - Upload ID
   * @returns {Promise<Object>} Classification results
   */
  async classifyDocument(text, apiKey, uploadId) {
    try {
      const prompt = this.generateClassificationPrompt(text);

      const response = await this.deepSeekAPI.callAPI(prompt, apiKey, {
        temperature: 0.1,
        max_tokens: 1000,
        responseFormat: { type: 'json_object' },
        systemPrompt: 'You are an expert document classifier. Analyze the document and provide detailed classification in JSON format.'
      }, uploadId);

      if (response.success) {
        const classification = this.deepSeekAPI.extractJSON(response.content);

        processingLogger.debug('enhanced_analysis', 'Document classification completed', uploadId, {
          documentType: classification.documentType,
          industry: classification.industry,
          language: classification.language
        });

        return classification;
      }

      throw new Error('Classification failed: ' + response.error);

    } catch (error) {
      processingLogger.error('enhanced_analysis', 'Document classification failed', uploadId, {
        error: error.message
      });

      // Return fallback classification
      return {
        documentType: 'unknown',
        confidence: 0.1,
        industry: 'unknown',
        language: 'unknown',
        region: 'unknown',
        fallback: true
      };
    }
  }

  /**
   * Extract enhanced metadata beyond basic fields
   * @param {string} text - Document text
   * @param {string} apiKey - API key
   * @param {string} uploadId - Upload ID
   * @returns {Promise<Object>} Enhanced metadata
   */
  async extractEnhancedMetadata(text, apiKey, uploadId) {
    try {
      const prompt = this.generateMetadataPrompt(text);

      const response = await this.deepSeekAPI.callAPI(prompt, apiKey, {
        temperature: 0.1,
        max_tokens: 2000,
        responseFormat: { type: 'json_object' },
        systemPrompt: 'You are an expert at extracting comprehensive metadata from business documents. Provide detailed metadata in JSON format.'
      }, uploadId);

      if (response.success) {
        const metadata = this.deepSeekAPI.extractJSON(response.content);

        processingLogger.debug('enhanced_analysis', 'Enhanced metadata extraction completed', uploadId, {
          metadataFields: Object.keys(metadata).length,
          hasCompanyRelationships: !!metadata.companyRelationships,
          hasTransactionPatterns: !!metadata.transactionPatterns
        });

        return metadata;
      }

      throw new Error('Metadata extraction failed: ' + response.error);

    } catch (error) {
      processingLogger.error('enhanced_analysis', 'Enhanced metadata extraction failed', uploadId, {
        error: error.message
      });

      return {
        extractionDate: new Date().toISOString(),
        fallback: true
      };
    }
  }

  /**
   * Generate business intelligence insights
   * @param {string} text - Document text
   * @param {string} apiKey - API key
   * @param {string} uploadId - Upload ID
   * @returns {Promise<Object>} Business intelligence insights
   */
  async generateBusinessIntelligence(text, apiKey, uploadId) {
    try {
      const prompt = this.generateBusinessIntelligencePrompt(text);

      const response = await this.deepSeekAPI.callAPI(prompt, apiKey, {
        temperature: 0.2,
        max_tokens: 1500,
        responseFormat: { type: 'json_object' },
        systemPrompt: 'You are a business intelligence analyst. Analyze the document for business insights, patterns, and recommendations in JSON format.'
      }, uploadId);

      if (response.success) {
        const insights = this.deepSeekAPI.extractJSON(response.content);

        processingLogger.debug('enhanced_analysis', 'Business intelligence generation completed', uploadId, {
          insightCategories: Object.keys(insights).length,
          hasRecommendations: !!insights.recommendations,
          hasRiskAssessment: !!insights.riskAssessment
        });

        return insights;
      }

      throw new Error('Business intelligence generation failed: ' + response.error);

    } catch (error) {
      processingLogger.error('enhanced_analysis', 'Business intelligence generation failed', uploadId, {
        error: error.message
      });

      return {
        analysisDate: new Date().toISOString(),
        fallback: true
      };
    }
  }

  /**
   * Calculate confidence score for the analysis
   * @param {string} text - Document text
   * @param {string} uploadId - Upload ID
   * @returns {Promise<Object>} Confidence scores
   */
  async calculateConfidenceScore(text, uploadId) {
    try {
      const scores = {
        textQuality: this.assessTextQuality(text),
        structureClarity: this.assessStructureClarity(text),
        dataCompleteness: this.assessDataCompleteness(text),
        languageConsistency: this.assessLanguageConsistency(text)
      };

      // Calculate overall confidence
      const weights = {
        textQuality: 0.3,
        structureClarity: 0.3,
        dataCompleteness: 0.25,
        languageConsistency: 0.15
      };

      const overall = Object.keys(scores).reduce((sum, key) => {
        return sum + (scores[key] * weights[key]);
      }, 0);

      const confidenceScore = {
        overall: Math.round(overall * 100) / 100,
        breakdown: scores,
        weights,
        assessment: this.getConfidenceAssessment(overall)
      };

      processingLogger.debug('enhanced_analysis', 'Confidence score calculated', uploadId, {
        overallScore: confidenceScore.overall,
        assessment: confidenceScore.assessment
      });

      return confidenceScore;

    } catch (error) {
      processingLogger.error('enhanced_analysis', 'Confidence score calculation failed', uploadId, {
        error: error.message
      });

      return {
        overall: 0.5,
        breakdown: {},
        fallback: true
      };
    }
  }

  /**
   * Generate classification prompt
   * @private
   */
  generateClassificationPrompt(text) {
    return `Analyze this document and classify it comprehensively. Return JSON with:
{
  "documentType": "invoice|receipt|contract|statement|report|other",
  "subType": "specific document subtype",
  "industry": "detected industry sector",
  "language": "detected language code",
  "region": "detected country/region",
  "confidence": "classification confidence 0-1",
  "businessContext": "B2B|B2C|internal|government",
  "urgency": "low|medium|high",
  "complianceRequirements": ["list of detected compliance needs"],
  "processingComplexity": "simple|moderate|complex"
}

Document text: "${text.substring(0, 2000)}..."`;
  }

  /**
   * Generate metadata extraction prompt
   * @private
   */
  generateMetadataPrompt(text) {
    return `Extract comprehensive metadata from this document. Return JSON with:
{
  "companyRelationships": {
    "vendor": "vendor company details",
    "customer": "customer company details",
    "relationshipType": "supplier|client|partner|internal"
  },
  "transactionPatterns": {
    "frequency": "one-time|recurring|subscription",
    "paymentTerms": "detected payment terms",
    "currency": "detected currency",
    "amounts": {"net": 0, "tax": 0, "gross": 0}
  },
  "complianceFlags": ["VAT", "GDPR", "SOX", "etc"],
  "riskIndicators": ["late payment", "high amount", "new vendor", "etc"],
  "documentQuality": {
    "readability": "score 0-1",
    "completeness": "score 0-1",
    "authenticity": "score 0-1"
  },
  "extractionMetadata": {
    "processingDate": "ISO timestamp",
    "sourceFormat": "PDF|image|text",
    "ocrConfidence": "if applicable"
  }
}

Document text: "${text.substring(0, 3000)}..."`;
  }

  /**
   * Generate business intelligence prompt
   * @private
   */
  generateBusinessIntelligencePrompt(text) {
    return `Analyze this document for business intelligence insights. Return JSON with:
{
  "spendingPatterns": {
    "category": "detected spending category",
    "trend": "increasing|decreasing|stable",
    "seasonality": "detected seasonal patterns"
  },
  "vendorAnalysis": {
    "vendorRating": "assessment of vendor",
    "paymentHistory": "inferred payment patterns",
    "riskLevel": "low|medium|high"
  },
  "costOptimization": {
    "opportunities": ["list of cost saving opportunities"],
    "benchmarking": "comparison with industry standards",
    "recommendations": ["actionable recommendations"]
  },
  "complianceInsights": {
    "status": "compliant|non-compliant|needs-review",
    "requirements": ["applicable regulations"],
    "actions": ["required compliance actions"]
  },
  "riskAssessment": {
    "financialRisk": "low|medium|high",
    "operationalRisk": "low|medium|high",
    "complianceRisk": "low|medium|high",
    "mitigationActions": ["recommended actions"]
  }
}

Document text: "${text.substring(0, 2500)}..."`;
  }

  /**
   * Assess text quality
   * @private
   */
  assessTextQuality(text) {
    const length = text.length;
    const wordCount = text.split(/\s+/).length;
    const avgWordLength = length / wordCount;
    const specialCharRatio = (text.match(/[^\w\s]/g) || []).length / length;

    let score = 0.5;

    // Length assessment
    if (length > 100 && length < 10000) { score += 0.2; }
    if (avgWordLength > 3 && avgWordLength < 8) { score += 0.2; }
    if (specialCharRatio < 0.3) { score += 0.1; }

    return Math.min(1, Math.max(0, score));
  }

  /**
   * Assess structure clarity
   * @private
   */
  assessStructureClarity(text) {
    const lines = text.split('\n').filter(line => line.trim());
    const hasNumbers = /\d/.test(text);
    const hasDates = /\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4}/.test(text);
    const hasAmounts = /[\$€£¥]\s*\d+|\d+[\.,]\d{2}/.test(text);

    let score = 0.3;

    if (lines.length > 5) { score += 0.2; }
    if (hasNumbers) { score += 0.2; }
    if (hasDates) { score += 0.15; }
    if (hasAmounts) { score += 0.15; }

    return Math.min(1, score);
  }

  /**
   * Assess data completeness
   * @private
   */
  assessDataCompleteness(text) {
    const indicators = [
      /company|business|corp|ltd|inc|gmbh/i,
      /invoice|receipt|bill|statement/i,
      /\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4}/,
      /[\$€£¥]\s*\d+|\d+[\.,]\d{2}/,
      /total|sum|amount|subtotal/i,
      /tax|vat|gst/i
    ];

    const matches = indicators.filter(pattern => pattern.test(text)).length;
    return matches / indicators.length;
  }

  /**
   * Assess language consistency
   * @private
   */
  assessLanguageConsistency(text) {
    // Simple heuristic for language consistency
    const words = text.toLowerCase().split(/\s+/);
    const uniqueWords = new Set(words);
    const repetitionRatio = words.length / uniqueWords.size;

    // Reasonable repetition indicates consistent language
    return Math.min(1, Math.max(0, 1 - (repetitionRatio - 2) / 10));
  }

  /**
   * Get confidence assessment text
   * @private
   */
  getConfidenceAssessment(score) {
    if (score >= 0.8) { return 'high'; }
    if (score >= 0.6) { return 'medium'; }
    if (score >= 0.4) { return 'low'; }
    return 'very-low';
  }
}

// Export singleton instance
export const enhancedDeepSeekAnalysis = new EnhancedDeepSeekAnalysis();
