/**
 * Vector Similarity Service
 *
 * Calculates similarity scores between document embeddings for RAG-based document linking.
 * Provides various similarity metrics and ranking algorithms.
 *
 * <AUTHOR> Agent
 * @version 1.0.0
 * @since 2025-06-03
 */

import { ProcessingLogger } from '../utils/ProcessingLogger.js';
import { VectorSearchService } from './VectorSearchService.js';
import { DocumentRelationshipScorer } from './DocumentRelationshipScorer.js';
import { AdvancedSimilarityService } from './AdvancedSimilarityService.js';

export class VectorSimilarityService {
  constructor(options = {}) {
    this.logger = new ProcessingLogger('VectorSimilarityService');
    this.similarityThreshold = options.similarityThreshold || 0.7; // Default similarity threshold
    this.maxRelatedDocuments = options.maxRelatedDocuments || 10; // Maximum related documents to return

    // Initialize enhanced vector search service
    this.vectorSearch = new VectorSearchService({
      defaultAlgorithm: options.defaultAlgorithm || 'cosine',
      defaultK: options.maxRelatedDocuments || 10,
      similarityThreshold: options.similarityThreshold || 0.7,
      indexingEnabled: options.indexingEnabled !== false,
      cacheResults: options.cacheResults !== false
    });

    // Initialize document relationship scorer
    this.relationshipScorer = new DocumentRelationshipScorer({
      vectorSimilarity: options.vectorSimilarityWeight || 0.4,
      contentSimilarity: options.contentSimilarityWeight || 0.2,
      metadataSimilarity: options.metadataSimilarityWeight || 0.15,
      temporalProximity: options.temporalProximityWeight || 0.1,
      businessContext: options.businessContextWeight || 0.1,
      documentType: options.documentTypeWeight || 0.05
    });

    // Document index for enhanced search
    this.documentIndex = new Map();

    // Initialize advanced similarity service for enhanced features (avoid circular dependency)
    this.advancedSimilarityService = new AdvancedSimilarityService({
      strongSimilarityThreshold: options.strongSimilarityThreshold || 0.85,
      moderateSimilarityThreshold: options.moderateSimilarityThreshold || 0.70,
      weakSimilarityThreshold: this.similarityThreshold,
      maxRelatedDocuments: this.maxRelatedDocuments,
      useContextualEmbeddings: options.useContextualEmbeddings !== false,
      enableClusteringAnalysis: options.enableClusteringAnalysis !== false,
      vectorSimilarityOptions: {
        similarityThreshold: this.similarityThreshold,
        maxRelatedDocuments: this.maxRelatedDocuments
      },
      relationshipScorerOptions: {
        vectorSimilarity: options.vectorSimilarityWeight || 0.4,
        contentSimilarity: options.contentSimilarityWeight || 0.2,
        metadataSimilarity: options.metadataSimilarityWeight || 0.15,
        temporalProximity: options.temporalProximityWeight || 0.1,
        businessContext: options.businessContextWeight || 0.1,
        documentType: options.documentTypeWeight || 0.05
      },
      vectorSearchOptions: {
        defaultAlgorithm: options.defaultAlgorithm || 'cosine',
        similarityThreshold: this.similarityThreshold,
        indexingEnabled: options.indexingEnabled !== false
      }
    });

    // Inject this instance to avoid circular dependency
    this.advancedSimilarityService.setVectorSimilarityService(this);
  }

  /**
   * Add documents to the vector search index
   * @param {Array} documents - Array of documents with embeddings
   * @returns {Promise<Object>} Indexing result
   */
  async addDocumentsToIndex(documents) {
    try {
      this.logger.log('📊 Adding documents to enhanced vector index', {
        documentCount: documents.length
      });

      // Prepare vectors for indexing
      const vectors = documents.map(doc => ({
        id: doc.id || doc.metadata?.documentId,
        vector: doc.embedding?.vector || doc.vector,
        metadata: {
          ...doc.metadata,
          text: doc.text,
          content: doc.content,
          documentType: doc.documentType || doc.metadata?.documentType
        }
      }));

      // Add to vector search index
      const indexResult = await this.vectorSearch.addVectors(vectors);

      // Store documents in local index for relationship scoring
      for (const doc of documents) {
        const docId = doc.id || doc.metadata?.documentId;
        if (docId) {
          this.documentIndex.set(docId, doc);
        }
      }

      this.logger.log('✅ Documents added to enhanced index', {
        indexed: indexResult.indexed,
        total: indexResult.total
      });

      return indexResult;

    } catch (error) {
      this.logger.error('❌ Failed to add documents to index', {
        error: error.message,
        documentCount: documents.length
      });

      return {
        indexed: 0,
        total: 0,
        error: error.message,
        success: false
      };
    }
  }

  /**
   * Calculate enhanced similarity between two document embeddings
   * @param {Object} embedding1 - First document embedding
   * @param {Object} embedding2 - Second document embedding
   * @param {Object} options - Calculation options
   * @returns {Promise<Object>} Enhanced similarity result with advanced scoring
   */
  async calculateEnhancedSimilarity(embedding1, embedding2, options = {}) {
    try {
      this.logger.log('🧠 Calculating advanced similarity with enhanced features', {
        doc1: embedding1.metadata?.documentId,
        doc2: embedding2.metadata?.documentId,
        useAdvanced: true
      });

      // Use advanced similarity service for enhanced calculation
      const enhancedResult = await this.advancedSimilarityService.calculateEnhancedSimilarity(
        embedding1,
        embedding2,
        options
      );

      // Convert to legacy format for backward compatibility
      return {
        score: enhancedResult.score,
        confidence: enhancedResult.confidence,
        relationshipType: enhancedResult.relationshipType,
        isRelated: enhancedResult.isRelated,
        enhanced: true,
        advancedFeatures: {
          components: enhancedResult.components,
          weights: enhancedResult.weights,
          algorithm: enhancedResult.metadata.algorithm
        },
        metadata: {
          doc1Id: embedding1.metadata?.documentId,
          doc2Id: embedding2.metadata?.documentId,
          method1: embedding1.method,
          method2: embedding2.method,
          timestamp: new Date().toISOString(),
          processingTime: enhancedResult.metadata.processingTime,
          options
        }
      };

    } catch (error) {
      this.logger.error('❌ Enhanced similarity calculation failed, falling back to standard', {
        error: error.message,
        doc1: embedding1.metadata?.documentId,
        doc2: embedding2.metadata?.documentId
      });

      // Fallback to standard calculation
      return this.calculateSimilarity(embedding1, embedding2, options);
    }
  }

  /**
   * Calculate similarity between two document embeddings using enhanced scoring
   * @param {Object} embedding1 - First document embedding
   * @param {Object} embedding2 - Second document embedding
   * @param {Object} options - Calculation options
   * @returns {Object} Enhanced similarity result with comprehensive scoring
   */
  calculateSimilarity(embedding1, embedding2, options = {}) {
    try {
      this.logger.log('🔍 Calculating enhanced document similarity', {
        doc1: embedding1.metadata?.documentId,
        doc2: embedding2.metadata?.documentId,
        method1: embedding1.method,
        method2: embedding2.method,
        enhanced: true,
        options
      });

      // Validate embeddings
      if (!this.validateEmbedding(embedding1) || !this.validateEmbedding(embedding2)) {
        throw new Error('Invalid embedding vectors provided');
      }

      // Use enhanced relationship scoring if available
      if (options.useEnhancedScoring !== false) {
        const doc1 = this.documentIndex.get(embedding1.metadata?.documentId) || { embedding: embedding1, metadata: embedding1.metadata };
        const doc2 = this.documentIndex.get(embedding2.metadata?.documentId) || { embedding: embedding2, metadata: embedding2.metadata };

        const relationshipScore = this.relationshipScorer.scoreRelationship(doc1, doc2, options);

        // Convert to legacy format for backward compatibility
        const result = {
          score: relationshipScore.compositeScore,
          confidence: relationshipScore.confidence,
          relationshipType: relationshipScore.relationshipStrength,
          isRelated: relationshipScore.compositeScore >= this.similarityThreshold,
          enhanced: true,
          enhancedScoring: relationshipScore,
          metadata: {
            doc1Id: embedding1.metadata?.documentId,
            doc2Id: embedding2.metadata?.documentId,
            method1: embedding1.method,
            method2: embedding2.method,
            timestamp: new Date().toISOString(),
            options
          }
        };

        this.logger.log('✅ Enhanced similarity calculated successfully', {
          score: result.score.toFixed(4),
          confidence: result.confidence.toFixed(4),
          relationshipType: result.relationshipType,
          isRelated: result.isRelated,
          enhanced: true
        });

        return result;
      }

      // Fallback to legacy calculation
      const score = this.cosineSimilarity(embedding1.vector, embedding2.vector);
      const confidence = this.calculateConfidence(embedding1, embedding2, score);
      const relationshipType = this.determineRelationshipType(score, embedding1, embedding2);

      const result = {
        score,
        confidence,
        relationshipType,
        isRelated: score >= this.similarityThreshold,
        enhanced: false,
        metadata: {
          doc1Id: embedding1.metadata?.documentId,
          doc2Id: embedding2.metadata?.documentId,
          method1: embedding1.method,
          method2: embedding2.method,
          timestamp: new Date().toISOString(),
          options
        }
      };

      this.logger.log('✅ Legacy similarity calculated successfully', {
        score: score.toFixed(4),
        confidence: confidence.toFixed(4),
        relationshipType,
        isRelated: result.isRelated,
        enhanced: false
      });

      return result;

    } catch (error) {
      this.logger.error('❌ Similarity calculation failed', {
        error: error.message,
        doc1: embedding1.metadata?.documentId,
        doc2: embedding2.metadata?.documentId
      });

      return {
        score: 0,
        confidence: 0,
        relationshipType: 'unknown',
        isRelated: false,
        error: error.message
      };
    }
  }

  /**
   * Find similar documents using advanced enhanced search
   * @param {Object} targetEmbedding - Target document embedding
   * @param {Array} documentEmbeddings - Collection of document embeddings (optional if using index)
   * @param {Object} options - Search options
   * @returns {Promise<Array>} Ranked list of similar documents with advanced scoring
   */
  async findSimilarDocumentsEnhanced(targetEmbedding, documentEmbeddings = null, options = {}) {
    try {
      this.logger.log('🔍 Finding similar documents with advanced enhanced search', {
        targetDoc: targetEmbedding.metadata?.documentId,
        collectionSize: documentEmbeddings?.length || 'using index',
        useAdvanced: true
      });

      // Use advanced similarity service for enhanced search
      const enhancedResults = await this.advancedSimilarityService.findSimilarDocumentsEnhanced(
        targetEmbedding,
        documentEmbeddings,
        {
          ...options,
          threshold: options.threshold || this.similarityThreshold,
          maxResults: options.maxResults || this.maxRelatedDocuments
        }
      );

      this.logger.log('✅ Advanced enhanced search completed', {
        targetDoc: targetEmbedding.metadata?.documentId,
        foundCount: enhancedResults.length,
        topScore: enhancedResults[0]?.similarity.score.toFixed(4) || 'N/A'
      });

      return enhancedResults;

    } catch (error) {
      this.logger.error('❌ Advanced enhanced search failed, falling back to standard', {
        error: error.message,
        targetDoc: targetEmbedding.metadata?.documentId
      });

      // Fallback to standard search
      return this.findSimilarDocuments(targetEmbedding, documentEmbeddings, options);
    }
  }

  /**
   * Find similar documents using enhanced vector search
   * @param {Object} targetEmbedding - Target document embedding
   * @param {Array} documentEmbeddings - Collection of document embeddings (optional if using index)
   * @param {Object} options - Search options
   * @returns {Promise<Array>} Ranked list of similar documents with enhanced scoring
   */
  async findSimilarDocuments(targetEmbedding, documentEmbeddings = null, options = {}) {
    const startTime = Date.now();

    try {
      this.logger.log('🔍 Finding similar documents with enhanced search', {
        targetDoc: targetEmbedding.metadata?.documentId,
        collectionSize: documentEmbeddings?.length || 'using index',
        threshold: options.threshold || this.similarityThreshold,
        maxResults: options.maxResults || this.maxRelatedDocuments,
        enhanced: true
      });

      let results = [];

      // Use enhanced vector search if index is available
      if (this.vectorSearch.vectorIndex.size > 0 && options.useEnhancedSearch !== false) {
        const searchOptions = {
          algorithm: options.algorithm || 'cosine',
          k: options.maxResults || this.maxRelatedDocuments,
          threshold: options.threshold || this.similarityThreshold,
          includeMetadata: true,
          excludeIds: [targetEmbedding.metadata?.documentId].filter(Boolean),
          ...options
        };

        const searchResults = await this.vectorSearch.search(targetEmbedding.vector, searchOptions);

        // Convert search results to legacy format with enhanced scoring
        results = searchResults.map(result => {
          const docId = result.id;
          const document = this.documentIndex.get(docId);

          return {
            documentId: docId,
            similarity: {
              score: result.similarity,
              confidence: 0.9, // High confidence for vector search
              relationshipType: this.determineRelationshipType(result.similarity),
              isRelated: result.similarity >= this.similarityThreshold,
              enhanced: true,
              algorithm: result.algorithm,
              distance: result.distance
            },
            embedding: document?.embedding,
            document: document,
            metadata: result.metadata
          };
        });

      } else if (documentEmbeddings) {
        // Fallback to legacy search
        this.logger.log('🔄 Using legacy document search', {
          reason: 'index not available or disabled'
        });

        const similarities = [];

        for (const docEmbedding of documentEmbeddings) {
          // Skip self-comparison
          if (docEmbedding.metadata?.documentId === targetEmbedding.metadata?.documentId) {
            continue;
          }

          const similarity = this.calculateSimilarity(targetEmbedding, docEmbedding, options);

          if (similarity.isRelated) {
            similarities.push({
              documentId: docEmbedding.metadata?.documentId,
              similarity,
              embedding: docEmbedding
            });
          }
        }

        // Sort by similarity score (descending)
        similarities.sort((a, b) => b.similarity.score - a.similarity.score);

        // Limit results
        const maxResults = options.maxResults || this.maxRelatedDocuments;
        results = similarities.slice(0, maxResults);
      }

      const processingTime = Date.now() - startTime;
      this.logger.log('✅ Enhanced similar documents found', {
        targetDoc: targetEmbedding.metadata?.documentId,
        foundCount: results.length,
        processingTime,
        topScore: results[0]?.similarity.score.toFixed(4) || 'N/A',
        enhanced: true
      });

      return results;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.logger.error('❌ Enhanced similar document search failed', {
        error: error.message,
        targetDoc: targetEmbedding.metadata?.documentId,
        processingTime
      });

      return [];
    }
  }

  /**
   * Calculate cosine similarity between two vectors
   * @private
   */
  cosineSimilarity(vector1, vector2) {
    if (vector1.length !== vector2.length) {
      throw new Error('Vector dimensions must match');
    }

    let dotProduct = 0;
    let magnitude1 = 0;
    let magnitude2 = 0;

    for (let i = 0; i < vector1.length; i++) {
      dotProduct += vector1[i] * vector2[i];
      magnitude1 += vector1[i] * vector1[i];
      magnitude2 += vector2[i] * vector2[i];
    }

    magnitude1 = Math.sqrt(magnitude1);
    magnitude2 = Math.sqrt(magnitude2);

    if (magnitude1 === 0 || magnitude2 === 0) {
      return 0;
    }

    return dotProduct / (magnitude1 * magnitude2);
  }

  /**
   * Calculate confidence score based on embedding quality
   * @private
   */
  calculateConfidence(embedding1, embedding2, similarityScore) {
    const conf1 = embedding1.confidence || 0.5;
    const conf2 = embedding2.confidence || 0.5;

    // Average confidence weighted by similarity score
    const baseConfidence = (conf1 + conf2) / 2;

    // Adjust based on embedding methods
    let methodBonus = 0;
    if (embedding1.method === 'deepseek-api' && embedding2.method === 'deepseek-api') {
      methodBonus = 0.1;
    } else if (embedding1.method === 'deepseek-api' || embedding2.method === 'deepseek-api') {
      methodBonus = 0.05;
    }

    // Adjust based on similarity score
    const scoreBonus = similarityScore > 0.8 ? 0.1 : 0;

    return Math.min(baseConfidence + methodBonus + scoreBonus, 1.0);
  }

  /**
   * Determine relationship type based on similarity score
   * @private
   */
  determineRelationshipType(score, embedding1, embedding2) {
    if (score >= 0.9) {
      return 'duplicate';
    } else if (score >= 0.8) {
      return 'very-similar';
    } else if (score >= 0.7) {
      return 'similar';
    } else if (score >= 0.5) {
      return 'related';
    }
    return 'unrelated';

  }

  /**
   * Validate embedding structure
   * @private
   */
  validateEmbedding(embedding) {
    return embedding &&
           embedding.vector &&
           Array.isArray(embedding.vector) &&
           embedding.vector.length > 0 &&
           embedding.vector.every(val => typeof val === 'number' && !isNaN(val));
  }

  /**
   * Batch similarity calculation for multiple documents
   * @param {Array} embeddings - Array of document embeddings
   * @param {Object} options - Calculation options
   * @returns {Array} Similarity matrix
   */
  calculateSimilarityMatrix(embeddings, options = {}) {
    const startTime = Date.now();

    try {
      this.logger.log('🔍 Calculating similarity matrix', {
        documentCount: embeddings.length,
        options
      });

      const matrix = [];

      for (let i = 0; i < embeddings.length; i++) {
        matrix[i] = [];
        for (let j = 0; j < embeddings.length; j++) {
          if (i === j) {
            matrix[i][j] = { score: 1.0, confidence: 1.0, relationshipType: 'self' };
          } else if (j < i) {
            // Use symmetry to avoid duplicate calculations
            matrix[i][j] = matrix[j][i];
          } else {
            matrix[i][j] = this.calculateSimilarity(embeddings[i], embeddings[j], options);
          }
        }
      }

      const processingTime = Date.now() - startTime;
      this.logger.log('✅ Similarity matrix calculated', {
        documentCount: embeddings.length,
        comparisons: (embeddings.length * (embeddings.length - 1)) / 2,
        processingTime
      });

      return matrix;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.logger.error('❌ Similarity matrix calculation failed', {
        error: error.message,
        documentCount: embeddings.length,
        processingTime
      });

      return [];
    }
  }

  /**
   * Set similarity threshold
   * @param {number} threshold - New threshold value (0-1)
   */
  setSimilarityThreshold(threshold) {
    if (threshold >= 0 && threshold <= 1) {
      this.similarityThreshold = threshold;
      this.logger.log('⚙️ Similarity threshold updated', { threshold });
    } else {
      throw new Error('Threshold must be between 0 and 1');
    }
  }

  /**
   * Get current configuration
   */
  getConfiguration() {
    return {
      similarityThreshold: this.similarityThreshold,
      maxRelatedDocuments: this.maxRelatedDocuments
    };
  }

  /**
   * Update configuration
   * @param {Object} config - New configuration
   */
  updateConfiguration(config) {
    if (config.similarityThreshold !== undefined) {
      this.setSimilarityThreshold(config.similarityThreshold);
    }

    if (config.maxRelatedDocuments !== undefined) {
      this.maxRelatedDocuments = Math.max(1, Math.min(50, config.maxRelatedDocuments));
    }

    this.logger.log('⚙️ Configuration updated', this.getConfiguration());
  }
}
