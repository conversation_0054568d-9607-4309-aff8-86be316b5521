/**
 * File Validation Feedback Component
 * Provides user-friendly feedback for file validation results
 *
 * Features:
 * - Clear error and warning messages
 * - Visual indicators for validation status
 * - Detailed validation information
 * - Accessibility compliance
 * - Responsive design
 */

import React from 'react';

/**
 * FileValidationFeedback Component
 * @param {Object} props - Component props
 * @param {Object} props.validationResult - Validation result object
 * @param {Function} props.onDismiss - Callback for dismissing feedback
 * @param {boolean} props.showDetails - Whether to show detailed information
 * @param {string} props.className - Additional CSS classes
 */
export function FileValidationFeedback({
  validationResult,
  onDismiss,
  showDetails = false,
  className = ''
}) {
  if (!validationResult) { return null; }

  const { isValid, errors = [], warnings = [], fileName, fileSize, performance = {} } = validationResult;

  // Determine feedback type and styling
  const feedbackType = isValid ? (warnings.length > 0 ? 'warning' : 'success') : 'error';

  const baseClasses = 'rounded-lg border p-4 mb-4 transition-all duration-200';
  const typeClasses = {
    success: 'bg-green-50 border-green-200 text-green-800',
    warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    error: 'bg-red-50 border-red-200 text-red-800'
  };

  const iconClasses = {
    success: 'text-green-500',
    warning: 'text-yellow-500',
    error: 'text-red-500'
  };

  const icons = {
    success: '✅',
    warning: '⚠️',
    error: '❌'
  };

  return (
    <div
      className={`${baseClasses} ${typeClasses[feedbackType]} ${className}`}
      role="alert"
      aria-live="polite"
    >
      {/* Header */}
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3">
          <span className={`text-xl ${iconClasses[feedbackType]}`} aria-hidden="true">
            {icons[feedbackType]}
          </span>
          <div className="flex-1">
            <h3 className="font-medium text-sm">
              {feedbackType === 'success' && 'File Validation Successful'}
              {feedbackType === 'warning' && 'File Validation Completed with Warnings'}
              {feedbackType === 'error' && 'File Validation Failed'}
            </h3>
            {fileName && (
              <p className="text-xs opacity-75 mt-1">
                File: {fileName} {fileSize && `(${formatFileSize(fileSize)})`}
              </p>
            )}
          </div>
        </div>

        {onDismiss && (
          <button
            onClick={onDismiss}
            className="text-current opacity-50 hover:opacity-75 transition-opacity"
            aria-label="Dismiss validation feedback"
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        )}
      </div>

      {/* Error Messages */}
      {errors.length > 0 && (
        <div className="mt-3">
          <h4 className="font-medium text-xs mb-2">Errors:</h4>
          <ul className="space-y-1">
            {errors.map((error, index) => (
              <li key={index} className="text-xs flex items-start space-x-2">
                <span className="text-red-500 mt-0.5" aria-hidden="true">•</span>
                <span>{error}</span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Warning Messages */}
      {warnings.length > 0 && (
        <div className="mt-3">
          <h4 className="font-medium text-xs mb-2">Warnings:</h4>
          <ul className="space-y-1">
            {warnings.map((warning, index) => (
              <li key={index} className="text-xs flex items-start space-x-2">
                <span className="text-yellow-500 mt-0.5" aria-hidden="true">•</span>
                <span>{warning}</span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Success Message */}
      {isValid && errors.length === 0 && warnings.length === 0 && (
        <div className="mt-2">
          <p className="text-xs">
            File passed all validation checks and is ready for processing.
          </p>
        </div>
      )}

      {/* Detailed Information */}
      {showDetails && (
        <details className="mt-3">
          <summary className="cursor-pointer text-xs font-medium hover:underline">
            View Detailed Information
          </summary>
          <div className="mt-2 space-y-2 text-xs">
            {validationResult.fileType && (
              <div>
                <span className="font-medium">MIME Type:</span> {validationResult.fileType}
              </div>
            )}
            {validationResult.fileExtension && (
              <div>
                <span className="font-medium">Extension:</span> {validationResult.fileExtension}
              </div>
            )}
            {validationResult.metadata?.detectedMimeType && (
              <div>
                <span className="font-medium">Detected Type:</span> {validationResult.metadata.detectedMimeType}
              </div>
            )}
            {validationResult.metadata?.hash && (
              <div>
                <span className="font-medium">File Hash:</span>
                <code className="ml-1 text-xs bg-gray-100 px-1 rounded">
                  {validationResult.metadata.hash.substring(0, 16)}...
                </code>
              </div>
            )}
            {performance.validationTime && (
              <div>
                <span className="font-medium">Validation Time:</span> {performance.validationTime}ms
              </div>
            )}
            {performance.throughput && (
              <div>
                <span className="font-medium">Throughput:</span> {formatFileSize(performance.throughput)}/s
              </div>
            )}
          </div>
        </details>
      )}
    </div>
  );
}

/**
 * Multiple Files Validation Feedback Component
 * @param {Object} props - Component props
 * @param {Object} props.validationResult - Multi-file validation result
 * @param {Function} props.onDismiss - Callback for dismissing feedback
 * @param {boolean} props.showDetails - Whether to show detailed information
 */
export function MultiFileValidationFeedback({
  validationResult,
  onDismiss,
  showDetails = false
}) {
  if (!validationResult) { return null; }

  const { isValid, totalFiles, totalSize, errors = [], warnings = [], results = [] } = validationResult;
  const validFiles = results.filter(r => r.isValid).length;
  const invalidFiles = results.filter(r => !r.isValid).length;

  const feedbackType = isValid ? (warnings.length > 0 ? 'warning' : 'success') : 'error';

  const baseClasses = 'rounded-lg border p-4 mb-4 transition-all duration-200';
  const typeClasses = {
    success: 'bg-green-50 border-green-200 text-green-800',
    warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    error: 'bg-red-50 border-red-200 text-red-800'
  };

  return (
    <div
      className={`${baseClasses} ${typeClasses[feedbackType]}`}
      role="alert"
      aria-live="polite"
    >
      {/* Header */}
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3">
          <span className="text-xl" aria-hidden="true">
            {feedbackType === 'success' ? '✅' : feedbackType === 'warning' ? '⚠️' : '❌'}
          </span>
          <div className="flex-1">
            <h3 className="font-medium text-sm">
              Multiple Files Validation {isValid ? 'Completed' : 'Failed'}
            </h3>
            <p className="text-xs opacity-75 mt-1">
              {totalFiles} files, {formatFileSize(totalSize)} total
              {validFiles > 0 && ` • ${validFiles} valid`}
              {invalidFiles > 0 && ` • ${invalidFiles} invalid`}
            </p>
          </div>
        </div>

        {onDismiss && (
          <button
            onClick={onDismiss}
            className="text-current opacity-50 hover:opacity-75 transition-opacity"
            aria-label="Dismiss validation feedback"
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        )}
      </div>

      {/* Summary Errors */}
      {errors.length > 0 && (
        <div className="mt-3">
          <h4 className="font-medium text-xs mb-2">Issues:</h4>
          <ul className="space-y-1">
            {errors.slice(0, 5).map((error, index) => (
              <li key={index} className="text-xs flex items-start space-x-2">
                <span className="text-red-500 mt-0.5" aria-hidden="true">•</span>
                <span>{error}</span>
              </li>
            ))}
            {errors.length > 5 && (
              <li className="text-xs text-gray-600">
                ... and {errors.length - 5} more issues
              </li>
            )}
          </ul>
        </div>
      )}

      {/* Individual File Results */}
      {showDetails && results.length > 0 && (
        <details className="mt-3">
          <summary className="cursor-pointer text-xs font-medium hover:underline">
            View Individual File Results
          </summary>
          <div className="mt-2 space-y-2">
            {results.map((result, index) => (
              <div key={index} className="border-l-2 border-gray-200 pl-3 py-1">
                <div className="flex items-center space-x-2 text-xs">
                  <span>{result.isValid ? '✅' : '❌'}</span>
                  <span className="font-medium">{result.fileName}</span>
                  <span className="text-gray-500">({formatFileSize(result.fileSize)})</span>
                </div>
                {result.errors.length > 0 && (
                  <div className="mt-1 text-xs text-red-600">
                    {result.errors.slice(0, 2).join(', ')}
                    {result.errors.length > 2 && '...'}
                  </div>
                )}
              </div>
            ))}
          </div>
        </details>
      )}
    </div>
  );
}

/**
 * Security Scan Feedback Component
 * @param {Object} props - Component props
 * @param {Object} props.scanResult - Security scan result
 * @param {Function} props.onDismiss - Callback for dismissing feedback
 */
export function SecurityScanFeedback({ scanResult, onDismiss }) {
  if (!scanResult) { return null; }

  const { isSecure, riskScore, threats = [], warnings = [] } = scanResult;
  const riskLevel = getRiskLevel(riskScore);

  const feedbackType = isSecure ? 'success' : 'error';
  const baseClasses = 'rounded-lg border p-4 mb-4 transition-all duration-200';
  const typeClasses = {
    success: 'bg-green-50 border-green-200 text-green-800',
    error: 'bg-red-50 border-red-200 text-red-800'
  };

  return (
    <div
      className={`${baseClasses} ${typeClasses[feedbackType]}`}
      role="alert"
      aria-live="polite"
    >
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3">
          <span className="text-xl" aria-hidden="true">
            {isSecure ? '🛡️' : '⚠️'}
          </span>
          <div className="flex-1">
            <h3 className="font-medium text-sm">
              Security Scan {isSecure ? 'Passed' : 'Failed'}
            </h3>
            <p className="text-xs opacity-75 mt-1">
              Risk Level: {riskLevel} (Score: {Math.round(riskScore * 100)}%)
            </p>
          </div>
        </div>

        {onDismiss && (
          <button
            onClick={onDismiss}
            className="text-current opacity-50 hover:opacity-75 transition-opacity"
            aria-label="Dismiss security feedback"
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        )}
      </div>

      {/* Threats */}
      {threats.length > 0 && (
        <div className="mt-3">
          <h4 className="font-medium text-xs mb-2">Security Threats:</h4>
          <ul className="space-y-1">
            {threats.map((threat, index) => (
              <li key={index} className="text-xs flex items-start space-x-2">
                <span className="text-red-500 mt-0.5" aria-hidden="true">🚨</span>
                <span>{threat.description}</span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Warnings */}
      {warnings.length > 0 && (
        <div className="mt-3">
          <h4 className="font-medium text-xs mb-2">Security Warnings:</h4>
          <ul className="space-y-1">
            {warnings.map((warning, index) => (
              <li key={index} className="text-xs flex items-start space-x-2">
                <span className="text-yellow-500 mt-0.5" aria-hidden="true">⚠️</span>
                <span>{warning.description}</span>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}

/**
 * Format file size for display
 * @param {number} bytes - Size in bytes
 * @returns {string} Formatted size
 */
function formatFileSize(bytes) {
  if (bytes === 0) { return '0 Bytes'; }
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Get risk level description
 * @param {number} riskScore - Risk score (0-1)
 * @returns {string} Risk level
 */
function getRiskLevel(riskScore) {
  if (riskScore < 0.3) { return 'Low'; }
  if (riskScore < 0.6) { return 'Medium'; }
  if (riskScore < 0.8) { return 'High'; }
  return 'Critical';
}

export default FileValidationFeedback;
