/**
 * RelatedDocuments Component
 *
 * Displays related documents based on RAG similarity analysis.
 * Shows document relationships, similarity scores, and provides navigation.
 *
 * <AUTHOR> Agent
 * @version 1.0.0
 * @since 2025-06-03
 */

import React, { useState, useEffect } from 'react';

const RelatedDocuments = ({ relationshipData, onDocumentSelect }) => {
  const [expandedDocument, setExpandedDocument] = useState(null);
  const [sortBy, setSortBy] = useState('similarity');

  if (!relationshipData || !relationshipData.similarDocuments || relationshipData.similarDocuments.length === 0) {
    return (
      <div className="related-documents-empty">
        <div className="text-gray-500 text-sm text-center py-4">
          <svg className="w-8 h-8 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          No related documents found
        </div>
      </div>
    );
  }

  const sortedDocuments = [...relationshipData.similarDocuments].sort((a, b) => {
    switch (sortBy) {
      case 'similarity':
        return b.similarity.score - a.similarity.score;
      case 'confidence':
        return b.similarity.confidence - a.similarity.confidence;
      case 'type':
        return (a.similarity.relationshipType || '').localeCompare(b.similarity.relationshipType || '');
      default:
        return 0;
    }
  });

  const getRelationshipColor = (type) => {
    switch (type) {
      case 'duplicate':
        return 'bg-red-100 text-red-800';
      case 'very-similar':
        return 'bg-orange-100 text-orange-800';
      case 'similar':
        return 'bg-blue-100 text-blue-800';
      case 'related':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getRelationshipIcon = (type) => {
    switch (type) {
      case 'duplicate':
        return '🔄';
      case 'very-similar':
        return '🔗';
      case 'similar':
        return '📎';
      case 'related':
        return '🔍';
      default:
        return '📄';
    }
  };

  const formatScore = (score) => {
    return (score * 100).toFixed(1) + '%';
  };

  const toggleExpanded = (documentId) => {
    setExpandedDocument(expandedDocument === documentId ? null : documentId);
  };

  return (
    <div className="related-documents">
      <div className="related-documents-header mb-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <svg className="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
            </svg>
            Related Documents ({sortedDocuments.length})
          </h3>

          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="text-sm border border-gray-300 rounded px-2 py-1 bg-white"
          >
            <option value="similarity">Sort by Similarity</option>
            <option value="confidence">Sort by Confidence</option>
            <option value="type">Sort by Type</option>
          </select>
        </div>

        <div className="text-sm text-gray-600 mt-1">
          Found {sortedDocuments.length} related document{sortedDocuments.length !== 1 ? 's' : ''}
          {relationshipData.processingTime && ` in ${relationshipData.processingTime}ms`}
        </div>
      </div>

      <div className="related-documents-list space-y-3">
        {sortedDocuments.map((relatedDoc, index) => (
          <div
            key={relatedDoc.documentId || index}
            className="related-document-item border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center mb-2">
                  <span className="text-lg mr-2">
                    {getRelationshipIcon(relatedDoc.similarity.relationshipType)}
                  </span>
                  <h4 className="font-medium text-gray-900 truncate">
                    {relatedDoc.embedding?.metadata?.filename || relatedDoc.documentId}
                  </h4>
                  <span className={`ml-2 px-2 py-1 text-xs font-medium rounded-full ${getRelationshipColor(relatedDoc.similarity.relationshipType)}`}>
                    {relatedDoc.similarity.relationshipType}
                  </span>
                </div>

                <div className="flex items-center space-x-4 text-sm text-gray-600 mb-2">
                  <div className="flex items-center">
                    <span className="font-medium">Similarity:</span>
                    <span className="ml-1 font-mono">{formatScore(relatedDoc.similarity.score)}</span>
                  </div>
                  <div className="flex items-center">
                    <span className="font-medium">Confidence:</span>
                    <span className="ml-1 font-mono">{formatScore(relatedDoc.similarity.confidence)}</span>
                  </div>
                  {relatedDoc.embedding?.metadata?.documentType && (
                    <div className="flex items-center">
                      <span className="font-medium">Type:</span>
                      <span className="ml-1">{relatedDoc.embedding.metadata.documentType}</span>
                    </div>
                  )}
                </div>

                {relatedDoc.embedding?.metadata && (
                  <div className="text-xs text-gray-500">
                    {relatedDoc.embedding.metadata.processedAt && (
                      <span>Processed: {new Date(relatedDoc.embedding.metadata.processedAt).toLocaleDateString()}</span>
                    )}
                    {relatedDoc.embedding.metadata.textLength && (
                      <span className="ml-3">Size: {relatedDoc.embedding.metadata.textLength} chars</span>
                    )}
                  </div>
                )}
              </div>

              <div className="flex items-center space-x-2 ml-4">
                <button
                  onClick={() => toggleExpanded(relatedDoc.documentId)}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                  title="Show details"
                >
                  <svg
                    className={`w-5 h-5 transform transition-transform ${expandedDocument === relatedDoc.documentId ? 'rotate-180' : ''}`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>

                {onDocumentSelect && (
                  <button
                    onClick={() => onDocumentSelect(relatedDoc)}
                    className="text-blue-600 hover:text-blue-800 transition-colors text-sm font-medium"
                  >
                    View
                  </button>
                )}
              </div>
            </div>

            {expandedDocument === relatedDoc.documentId && (
              <div className="mt-3 pt-3 border-t border-gray-100">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <h5 className="font-medium text-gray-700 mb-1">Embedding Details</h5>
                    <div className="space-y-1 text-gray-600">
                      <div>Method: {relatedDoc.embedding?.method || 'unknown'}</div>
                      <div>Dimension: {relatedDoc.embedding?.vector?.length || 'unknown'}</div>
                      <div>Quality: {formatScore(relatedDoc.embedding?.confidence || 0)}</div>
                    </div>
                  </div>

                  <div>
                    <h5 className="font-medium text-gray-700 mb-1">Similarity Analysis</h5>
                    <div className="space-y-1 text-gray-600">
                      <div>Score: {formatScore(relatedDoc.similarity.score)}</div>
                      <div>Confidence: {formatScore(relatedDoc.similarity.confidence)}</div>
                      <div>Type: {relatedDoc.similarity.relationshipType}</div>
                    </div>
                  </div>
                </div>

                {relatedDoc.similarity.metadata && (
                  <div className="mt-3">
                    <h5 className="font-medium text-gray-700 mb-1">Analysis Metadata</h5>
                    <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
                      <pre className="whitespace-pre-wrap">
                        {JSON.stringify(relatedDoc.similarity.metadata, null, 2)}
                      </pre>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        ))}
      </div>

      {relationshipData.relationshipCount > sortedDocuments.length && (
        <div className="text-center mt-4">
          <div className="text-sm text-gray-500">
            Showing {sortedDocuments.length} of {relationshipData.relationshipCount} related documents
          </div>
        </div>
      )}
    </div>
  );
};

export default RelatedDocuments;
