/**
 * PromptGenerator - Unified prompt generation service
 * Consolidates functionality from core/prompt_generators/accounting_fields.js
 * and core/prompt_generators/document_fields.js
 */

import { FIELD_DESCRIPTIONS, DOCUMENT_KIND_FIELDS, COMMON_FIELDS, POSITION_FIELDS } from '../../../core/config/fieldDefinitions.js';
import { DOCUMENT_TYPES_WITH_POSITIONS, mapToFakturowniaDocumentType } from '../../../core/config/documentTypes.js';

export class PromptGenerator {
  constructor() {
    this.fieldDescriptions = FIELD_DESCRIPTIONS;
    this.documentKindFields = DOCUMENT_KIND_FIELDS;
    this.commonFields = COMMON_FIELDS;
    this.positionFields = POSITION_FIELDS;
    this.documentTypesWithPositions = DOCUMENT_TYPES_WITH_POSITIONS;
  }

  /**
   * Generate document analysis prompt for determining document type
   * @param {string} documentContent - Document content
   * @param {Object} languageMapping - Language mapping instance
   * @returns {Object} - Prompt configuration
   */
  generateDocumentAnalysisPrompt(documentContent, languageMapping) {
    const detectedLang = languageMapping ? languageMapping.detectDocumentLanguageCode(documentContent) : 'pl';

    const systemPrompt = `You are an expert accounting document analyst specializing in Polish and EU business documents.

TASK: Analyze the document and determine its type, provide description, and extract basic information.

CRITICAL INSTRUCTIONS:
1. Field names in JSON output must remain in English as defined in the structure
2. Field values should be in the detected language (${detectedLang})
3. Use exact document type codes from the provided list
4. Provide accurate descriptions based on document content

DOCUMENT TYPES:
- vat: VAT invoice
- proforma: Proforma invoice
- bill: Bill/Receipt
- correction: Correction invoice
- advance: Advance invoice
- final: Final invoice
- estimate: Estimate/Order
- accounting_note: Accounting note
- And other standard business document types

RESPONSE FORMAT: Return only valid JSON object with this structure:
{
  "documentKind": "document_type_code",
  "description": "Brief description of the document",
  "summary": "Key information summary",
  "confidence": "high|medium|low"
}`;

    const prompt = `Analyze this document and determine its type:

${documentContent}

Determine the document type and provide analysis in the specified JSON format.`;

    return {
      prompt,
      systemPrompt,
      options: {
        temperature: 0.1,
        max_tokens: 1000
      }
    };
  }

  /**
   * Generate metadata extraction prompt
   * @param {string} documentType - Document type
   * @param {string} documentContent - Document content
   * @param {string} language - Document language
   * @param {string} companyContext - Company context
   * @param {string} ragContext - RAG context
   * @returns {Object} - Prompt configuration
   */
  generateMetadataExtractionPrompt(documentType, documentContent, language = 'pl', companyContext = '', ragContext = '') {
    const fields = this.getFieldsForDocumentType(documentType);
    const structure = this.generateExpectedJsonStructure(documentType);

    const systemPrompt = `You are an expert accounting document analyst with deep knowledge of Polish accounting and VAT regulations.

TASK: Extract structured data from ${documentType} document.

CRITICAL INSTRUCTIONS:
1. Field names in JSON output must remain in English as defined in the structure
2. Field values should be extracted in the original document language (${language})
3. Use exact field names from the provided structure
4. For income field: 1=company as seller (revenue), 0=company as buyer (expense)
5. Extract all monetary amounts with proper decimal formatting
6. Ensure date formats are YYYY-MM-DD

${companyContext}

${ragContext}

EXPECTED JSON STRUCTURE:
${JSON.stringify(structure, null, 2)}`;

    const prompt = `Extract all relevant data from this ${documentType} document:

${documentContent}

Return the extracted data in the exact JSON structure provided above.`;

    return {
      prompt,
      systemPrompt,
      options: {
        temperature: 0.1,
        max_tokens: 2000
      }
    };
  }

  /**
   * Generate positions extraction prompt
   * @param {string} documentType - Document type
   * @param {string} documentContent - Document content
   * @param {string} language - Document language
   * @param {Object} basicData - Basic document data
   * @param {string} ocrTableData - OCR table data
   * @returns {Object} - Prompt configuration
   */
  generatePositionsPrompt(documentType, documentContent, language = 'pl', basicData = null, ocrTableData = '') {
    if (!this.documentTypeHasPositions(documentType)) {
      return null;
    }

    const positionStructure = this.generatePositionStructure(documentType);

    const systemPrompt = `You are an expert at extracting line items from ${documentType} documents.

TASK: Extract all positions/line items from the document.

CRITICAL INSTRUCTIONS:
1. Field names must remain in English as defined in the structure
2. Extract values in the original document language (${language})
3. Ensure numeric values use decimal notation (e.g., 123.45)
4. For correction documents, extract both before_positions and after_positions
5. Validate totals against document totals when possible

POSITION STRUCTURE:
${JSON.stringify(positionStructure, null, 2)}`;

    let prompt = `Extract all line items/positions from this ${documentType} document:

${documentContent}`;

    if (ocrTableData) {
      prompt += `\n\nAdditional table data from OCR:\n${ocrTableData}`;
    }

    if (basicData) {
      prompt += `\n\nDocument totals for validation:
- Total Net: ${basicData.total_net || 'N/A'}
- Total VAT: ${basicData.total_vat || 'N/A'}
- Total Gross: ${basicData.total_gross || 'N/A'}`;
    }

    prompt += '\n\nReturn the positions in the specified JSON structure.';

    return {
      prompt,
      systemPrompt,
      options: {
        temperature: 0.1,
        max_tokens: 3000
      }
    };
  }

  /**
   * Generate context-aware document prompt (from DocumentFields)
   * @param {string} documentType - Document type
   * @param {string} documentContent - Document content
   * @param {string} language - Document language
   * @param {string} companyContext - Company context
   * @param {string} ragContext - RAG context
   * @param {Object} documentTypeResult - Document type analysis result
   * @returns {string} - Generated prompt
   */
  generateContextAwareDocumentPrompt(documentType, documentContent, language, companyContext, ragContext, documentTypeResult) {
    const structure = this.generateExpectedJsonStructure(documentType);

    let prompt = `You are an expert accounting document analyst. Extract structured data from this ${documentType} document.

CRITICAL INSTRUCTIONS:
1. Field names in JSON must remain in English
2. Field values should be in the detected language (${language})
3. For income field: determine if company is buyer (0) or seller (1)
4. Extract all monetary amounts accurately
5. Use YYYY-MM-DD format for dates

${companyContext}

${ragContext}`;

    if (documentTypeResult && documentTypeResult.description) {
      prompt += `\n\nDocument Analysis Context:
- Type: ${documentTypeResult.documentType}
- Description: ${documentTypeResult.description}
- Summary: ${documentTypeResult.summary || 'N/A'}`;
    }

    prompt += `\n\nEXPECTED JSON STRUCTURE:
${JSON.stringify(structure, null, 2)}

DOCUMENT CONTENT:
${documentContent}

Extract the data and return it in the exact JSON structure above.`;

    return prompt;
  }

  /**
   * Get fields for a specific document type
   * @param {string} documentType - Document type
   * @returns {Array} - Array of field names
   */
  getFieldsForDocumentType(documentType) {
    const commonFields = [...this.commonFields];
    const specificFields = this.documentKindFields[documentType] || [];
    return [...commonFields, ...specificFields];
  }

  /**
   * Generate expected JSON structure for document type
   * @param {string} documentType - Document type
   * @returns {Object} - JSON structure
   */
  generateExpectedJsonStructure(documentType) {
    const fields = this.getFieldsForDocumentType(documentType);
    const structure = {};

    fields.forEach(field => {
      if (field === 'positions') {
        if (this.documentTypeHasPositions(documentType)) {
          structure.positions = [this.generatePositionStructure(documentType)];
        }
      } else {
        const description = this.fieldDescriptions[field] || `${field} value`;
        structure[field] = `(${description})`;
      }
    });

    return structure;
  }

  /**
   * Generate position structure for document type
   * @param {string} documentType - Document type
   * @returns {Object} - Position structure
   */
  generatePositionStructure(documentType) {
    const structure = {};

    Object.keys(this.positionFields).forEach(field => {
      const description = this.positionFields[field];
      structure[field] = `(${description})`;
    });

    // Add correction-specific fields for correction documents
    if (documentType === 'correction') {
      structure.kind = '(position type: normal|correction)';
      structure.correction_before_attributes = '(original position data)';
      structure.correction_after_attributes = '(corrected position data)';
    }

    return structure;
  }

  /**
   * Check if document type has positions
   * @param {string} documentType - Document type
   * @returns {boolean} - Whether document type has positions
   */
  documentTypeHasPositions(documentType) {
    return this.documentTypesWithPositions.includes(documentType.toLowerCase());
  }

  /**
   * Generate validation prompt for extracted data
   * @param {Object} extractedData - Extracted data
   * @param {string} documentContent - Original document content
   * @returns {Object} - Validation prompt configuration
   */
  generateValidationPrompt(extractedData, documentContent) {
    const systemPrompt = `You are a data validation expert for accounting documents.

TASK: Validate the extracted data against the original document and identify any inconsistencies.

VALIDATION CHECKS:
1. Verify all monetary amounts match the document
2. Check date formats and consistency
3. Validate company information accuracy
4. Ensure position totals match document totals
5. Check for missing critical information

RESPONSE FORMAT: Return JSON with validation results:
{
  "valid": true/false,
  "errors": ["list of errors found"],
  "warnings": ["list of warnings"],
  "suggestions": ["list of improvement suggestions"]
}`;

    const prompt = `Validate this extracted data against the original document:

EXTRACTED DATA:
${JSON.stringify(extractedData, null, 2)}

ORIGINAL DOCUMENT:
${documentContent}

Perform thorough validation and return results in the specified format.`;

    return {
      prompt,
      systemPrompt,
      options: {
        temperature: 0.1,
        max_tokens: 1500
      }
    };
  }
}

// Create singleton instance
const promptGenerator = new PromptGenerator();

// Export for ES modules
export default promptGenerator;

// Export to window object for non-module scripts
if (typeof window !== 'undefined') {
  window.PromptGenerator = PromptGenerator;
  window.promptGenerator = promptGenerator;
}

// Local test function
function testPromptGenerator() {
  console.log('=== PromptGenerator Local Test ===');

  try {
    const generator = new PromptGenerator();

    // Test 1: Document analysis prompt
    console.log('Test 1: Document analysis prompt');
    const mockLanguageMapping = {
      detectDocumentLanguageCode: () => 'pl'
    };
    const analysisPrompt = generator.generateDocumentAnalysisPrompt('FAKTURA VAT', mockLanguageMapping);
    console.log('✓ Document analysis prompt generated');
    console.log('System prompt length:', analysisPrompt.systemPrompt.length);

    // Test 2: Metadata extraction prompt
    console.log('\nTest 2: Metadata extraction prompt');
    const metadataPrompt = generator.generateMetadataExtractionPrompt('vat', 'Sample content', 'pl');
    console.log('✓ Metadata extraction prompt generated');
    console.log('Prompt length:', metadataPrompt.prompt.length);

    // Test 3: Positions prompt
    console.log('\nTest 3: Positions prompt');
    const positionsPrompt = generator.generatePositionsPrompt('vat', 'Sample content', 'pl');
    console.log('✓ Positions prompt generated');
    console.log('Has positions for VAT:', generator.documentTypeHasPositions('vat'));

    // Test 4: JSON structure generation
    console.log('\nTest 4: JSON structure generation');
    const structure = generator.generateExpectedJsonStructure('vat');
    console.log('✓ JSON structure generated');
    console.log('Structure fields:', Object.keys(structure).length);

    // Test 5: Position structure
    console.log('\nTest 5: Position structure');
    const positionStructure = generator.generatePositionStructure('vat');
    console.log('✓ Position structure generated');
    console.log('Position fields:', Object.keys(positionStructure).length);

    console.log('\n✅ All PromptGenerator tests passed!');
    return true;

  } catch (error) {
    console.error('❌ PromptGenerator test failed:', error);
    return false;
  }
}

// Run test if in browser environment
if (typeof window !== 'undefined' && window.location) {
  // Uncomment to run test
  // testPromptGenerator();
}

// ===== LOCAL TESTING (Node.js equivalent of Python's if __name__ == '__main__') =====
if (typeof process !== 'undefined' && process.argv && process.argv[1] && process.argv[1].includes('PromptGenerator.js')) {
  console.log('🧪 Running local tests for PromptGenerator...');

  // Create test instance
  const generator = new PromptGenerator();

  // Mock language mapping
  const mockLanguageMapping = {
    detectDocumentLanguageCode: () => 'pl'
  };

  // Test data
  const sampleCompanyData = {
    name: 'Test Sp. z o.o.',
    taxId: '1234567890',
    address: 'ul. Testowa 123',
    city: 'Warszawa',
    postCode: '00-001'
  };

  const sampleDocumentText = `
    FAKTURA VAT
    Numer: FV/2025/001
    Data wystawienia: 2025-01-27
    Sprzedawca: ABC Sp. z o.o.
    NIP: 1234567890
    Nabywca: XYZ Sp. z o.o.
    NIP: 0987654321
    `;

  console.log('✅ Test 1: Document analysis prompt generation');
  try {
    const analysisPrompt = generator.generateDocumentAnalysisPrompt(sampleDocumentText, mockLanguageMapping);
    console.log('📝 Generated document analysis prompt');
    console.log('📊 System prompt length:', analysisPrompt.systemPrompt.length);
    console.log('📊 User prompt length:', analysisPrompt.prompt.length);
    console.log('📊 Temperature:', analysisPrompt.options.temperature);
  } catch (error) {
    console.log('❌ Error in document analysis prompt generation:', error.message);
  }

  console.log('\n✅ Test 2: Metadata extraction prompt generation');
  try {
    const companyContext = `Company: ${sampleCompanyData.name} (${sampleCompanyData.taxId})`;
    const metadataPrompt = generator.generateMetadataExtractionPrompt('vat', sampleDocumentText, 'pl', companyContext);
    console.log('📝 Generated metadata extraction prompt');
    console.log('📊 System prompt length:', metadataPrompt.systemPrompt.length);
    console.log('📊 User prompt length:', metadataPrompt.prompt.length);
    console.log('📊 Max tokens:', metadataPrompt.options.max_tokens);
  } catch (error) {
    console.log('❌ Error in metadata prompt generation:', error.message);
  }

  console.log('\n✅ Test 3: Position extraction prompt generation');
  try {
    const basicData = { total_net: '1000.00', total_vat: '230.00', total_gross: '1230.00' };
    const positionPrompt = generator.generatePositionsPrompt('vat', sampleDocumentText, 'pl', basicData);
    console.log('📝 Generated position extraction prompt');
    console.log('📊 System prompt length:', positionPrompt.systemPrompt.length);
    console.log('📊 User prompt length:', positionPrompt.prompt.length);
    console.log('📊 Max tokens:', positionPrompt.options.max_tokens);
  } catch (error) {
    console.log('❌ Error in position prompt generation:', error.message);
  }

  console.log('\n✅ Test 4: JSON structure generation');
  try {
    const vatStructure = generator.generateExpectedJsonStructure('vat');
    const correctionStructure = generator.generateExpectedJsonStructure('correction');
    console.log('📝 Generated JSON structures');
    console.log('📊 VAT structure fields:', Object.keys(vatStructure).length);
    console.log('📊 Correction structure fields:', Object.keys(correctionStructure).length);
    console.log('📊 VAT has positions:', 'positions' in vatStructure);
  } catch (error) {
    console.log('❌ Error in JSON structure generation:', error.message);
  }

  console.log('\n✅ Test 5: Document type position checking');
  try {
    const vatHasPositions = generator.documentTypeHasPositions('vat');
    const billHasPositions = generator.documentTypeHasPositions('bill');
    const unknownHasPositions = generator.documentTypeHasPositions('unknown');
    console.log('📝 Document type position checking');
    console.log('📊 VAT has positions:', vatHasPositions);
    console.log('📊 Bill has positions:', billHasPositions);
    console.log('📊 Unknown has positions:', unknownHasPositions);
  } catch (error) {
    console.log('❌ Error in document type checking:', error.message);
  }

  console.log('\n✅ Test 6: Validation prompt generation');
  try {
    const extractedData = {
      kind: 'vat',
      number: 'FV/2025/001',
      total_net: '1000.00',
      total_gross: '1230.00'
    };
    const validationPrompt = generator.generateValidationPrompt(extractedData, sampleDocumentText);
    console.log('📝 Generated validation prompt');
    console.log('📊 System prompt length:', validationPrompt.systemPrompt.length);
    console.log('📊 User prompt length:', validationPrompt.prompt.length);
  } catch (error) {
    console.log('❌ Error in validation prompt generation:', error.message);
  }

  console.log('\n🎉 All tests completed for PromptGenerator');
}
