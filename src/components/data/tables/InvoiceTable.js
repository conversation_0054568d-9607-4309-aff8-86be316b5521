/**
 * InvoiceTable - Invoice table rendering and management
 * Extracted and refactored from components/update_invoices_table.js
 * Handles invoice display, grouping, and actions
 */

import { StorageAPI } from '../../api/StorageAPI.js';
import { ValidationService } from '../../core/services/ValidationService.js';

export class InvoiceTable {
  constructor() {
    this.storageAPI = new StorageAPI();
    this.validationService = new ValidationService();
    this.currentInvoices = [];
    this.groupingSettings = { groupByPeriod: 'monthly' };
    this.companyInfo = {};
  }

  /**
   * Initialize the invoice table
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      // Load settings
      const settings = await this.storageAPI.getSettings();
      this.companyInfo = settings.accounting.company || {};

      // Load display settings
      const displayData = await this.storageAPI.get('displaySettings');
      this.groupingSettings = displayData.displaySettings || { groupByPeriod: 'monthly' };

      console.log('InvoiceTable initialized');
    } catch (error) {
      console.error('Failed to initialize InvoiceTable:', error);
    }
  }

  /**
   * Update the invoice table with new data
   * @param {Array} invoices - Array of invoices to display
   * @returns {Promise<void>}
   */
  async updateTable(invoices) {
    try {
      await this.initialize();

      this.currentInvoices = invoices;

      // Get container
      const container = document.getElementById('invoiceTablesContainer');
      if (!container) {
        console.error('Invoice table container not found');
        return;
      }

      // Clear container
      container.innerHTML = '';

      // Show/hide empty state
      const emptyState = document.getElementById('emptyState');
      if (invoices.length === 0) {
        if (emptyState) { emptyState.style.display = 'block'; }
        return;
      }
      if (emptyState) { emptyState.style.display = 'none'; }


      // Process and validate invoices
      const processedInvoices = await this.processInvoices(invoices);

      // Group invoices by period
      const groupedInvoices = this.groupInvoicesByPeriod(processedInvoices);

      // Render grouped tables
      this.renderGroupedTables(container, groupedInvoices);

      // Show validation errors if any
      this.showValidationErrors(processedInvoices);

    } catch (error) {
      console.error('Error updating invoice table:', error);
    }
  }

  /**
   * Process invoices (validation, type determination, etc.)
   * @param {Array} invoices - Raw invoices
   * @returns {Promise<Array>} - Processed invoices
   */
  async processInvoices(invoices) {
    const processed = [];

    for (const invoice of invoices) {
      const processedInvoice = { ...invoice };

      // Determine invoice type
      processedInvoice.invoiceType = this.determineInvoiceType(invoice);

      // Validate invoice
      const validation = this.validationService.validateDocument(invoice);
      processedInvoice.validation = validation;

      // Check if invoice is valid for company
      const isValid = this.validateInvoiceForCompany(invoice);
      if (!isValid.valid) {
        processedInvoice.invalidInvoice = true;
        processedInvoice.invalidReason = isValid.reason;
      }

      processed.push(processedInvoice);
    }

    return processed;
  }

  /**
   * Determine invoice type (Purchase/Sales)
   * @param {Object} invoice - Invoice data
   * @returns {string} - Invoice type
   */
  determineInvoiceType(invoice) {
    const companyNip = this.companyInfo.taxId ? this.normalizeNip(this.companyInfo.taxId) : '';
    const sellerNip = invoice.seller_tax_no ? this.normalizeNip(invoice.seller_tax_no) : '';
    const buyerNip = invoice.buyer_tax_no ? this.normalizeNip(invoice.buyer_tax_no) : '';

    // If company NIP matches seller, it's a sales invoice
    if (companyNip && sellerNip === companyNip) {
      return 'Sales Invoice';
    }

    // If company NIP matches buyer, it's a purchase invoice
    if (companyNip && buyerNip === companyNip) {
      return 'Purchase Invoice';
    }

    // Fallback based on income field
    if (invoice.income === '1' || invoice.income === 1) {
      return 'Sales Invoice';
    }

    return 'Purchase Invoice';
  }

  /**
   * Validate invoice for company
   * @param {Object} invoice - Invoice data
   * @returns {Object} - Validation result
   */
  validateInvoiceForCompany(invoice) {
    const companyNip = this.companyInfo.taxId ? this.normalizeNip(this.companyInfo.taxId) : '';
    const sellerNip = invoice.seller_tax_no ? this.normalizeNip(invoice.seller_tax_no) : '';
    const buyerNip = invoice.buyer_tax_no ? this.normalizeNip(invoice.buyer_tax_no) : '';

    // Check for missing NIPs
    if (!sellerNip || !buyerNip) {
      return {
        valid: false,
        reason: `Missing ${!sellerNip ? 'seller' : ''}${!sellerNip && !buyerNip ? ' and ' : ''}${!buyerNip ? 'buyer' : ''} NIP`
      };
    }

    // Check if company NIP is involved
    if (companyNip && sellerNip !== companyNip && buyerNip !== companyNip) {
      return {
        valid: false,
        reason: 'Invoice does not contain company NIP on either side'
      };
    }

    return { valid: true };
  }

  /**
   * Group invoices by period
   * @param {Array} invoices - Processed invoices
   * @returns {Object} - Grouped invoices
   */
  groupInvoicesByPeriod(invoices) {
    const grouped = {};

    invoices.forEach(invoice => {
      if (!invoice.issue_date && !invoice.accountingDate) { return; }

      const date = new Date(invoice.issue_date || invoice.accountingDate);
      const period = this.getPeriodKey(date);

      if (!grouped[period]) {
        grouped[period] = {
          purchases: [],
          sales: []
        };
      }

      if (invoice.invoiceType.startsWith('Sales')) {
        grouped[period].sales.push(invoice);
      } else {
        grouped[period].purchases.push(invoice);
      }
    });

    return grouped;
  }

  /**
   * Get period key for grouping
   * @param {Date} date - Date object
   * @returns {string} - Period key
   */
  getPeriodKey(date) {
    const year = date.getFullYear();

    switch (this.groupingSettings.groupByPeriod) {
      case 'monthly':
        const month = date.getMonth() + 1;
        return `${year}-${month.toString().padStart(2, '0')}`;
      case 'quarterly':
        const quarter = Math.floor(date.getMonth() / 3) + 1;
        return `${year} Q${quarter}`;
      case 'yearly':
        return year.toString();
      default:
        return year.toString();
    }
  }

  /**
   * Render grouped tables
   * @param {Element} container - Container element
   * @param {Object} groupedInvoices - Grouped invoices
   */
  renderGroupedTables(container, groupedInvoices) {
    const sortedPeriods = Object.keys(groupedInvoices).sort((a, b) => b.localeCompare(a));

    sortedPeriods.forEach(period => {
      const periodData = groupedInvoices[period];

      // Create period container
      const periodContainer = document.createElement('div');
      periodContainer.className = 'period-container mb-6';

      // Period header
      const periodHeader = document.createElement('h3');
      periodHeader.className = 'text-lg font-semibold mb-4 text-gray-800';
      periodHeader.textContent = this.formatPeriodLabel(period);
      periodContainer.appendChild(periodHeader);

      // Render purchase table if has purchases
      if (periodData.purchases.length > 0) {
        const purchaseTable = this.createTable('Purchase Invoices', periodData.purchases, true);
        periodContainer.appendChild(purchaseTable);
      }

      // Render sales table if has sales
      if (periodData.sales.length > 0) {
        const salesTable = this.createTable('Sales Invoices', periodData.sales, false);
        periodContainer.appendChild(salesTable);
      }

      container.appendChild(periodContainer);
    });
  }

  /**
   * Create a table for invoices
   * @param {string} title - Table title
   * @param {Array} invoices - Invoices to display
   * @param {boolean} isPurchase - Whether this is a purchase table
   * @returns {Element} - Table element
   */
  createTable(title, invoices, isPurchase) {
    const tableContainer = document.createElement('div');
    tableContainer.className = 'table-container mb-4';

    // Table header
    const header = document.createElement('h4');
    header.className = 'text-md font-medium mb-2 text-gray-700';
    header.textContent = `${title} (${invoices.length})`;
    tableContainer.appendChild(header);

    // Table
    const table = document.createElement('table');
    table.className = 'invoice-table w-full border-collapse border border-gray-300';

    // Table header
    const thead = document.createElement('thead');
    thead.innerHTML = `
      <tr class="bg-gray-100">
        <th class="border border-gray-300 p-2 text-left">
          <input type="checkbox" class="select-all-checkbox">
        </th>
        <th class="border border-gray-300 p-2 text-left">Document</th>
        <th class="border border-gray-300 p-2 text-left">${isPurchase ? 'Seller' : 'Buyer'}</th>
        <th class="border border-gray-300 p-2 text-right">Amount</th>
        <th class="border border-gray-300 p-2 text-left">Date</th>
        <th class="border border-gray-300 p-2 text-left">Details</th>
        <th class="border border-gray-300 p-2 text-left">Status</th>
      </tr>
    `;
    table.appendChild(thead);

    // Table body
    const tbody = document.createElement('tbody');
    invoices.forEach((invoice, index) => {
      const row = this.createInvoiceRow(invoice, index, isPurchase);
      tbody.appendChild(row);
    });
    table.appendChild(tbody);

    tableContainer.appendChild(table);
    return tableContainer;
  }

  /**
   * Create a row for an invoice
   * @param {Object} invoice - Invoice data
   * @param {number} index - Row index
   * @param {boolean} isPurchase - Whether this is a purchase invoice
   * @returns {Element} - Table row element
   */
  createInvoiceRow(invoice, index, isPurchase) {
    const row = document.createElement('tr');

    // Add invalid class if needed
    if (invoice.invalidInvoice) {
      row.classList.add('invalid-invoice', 'bg-red-50');
      row.title = invoice.invalidReason || 'Invalid invoice';
    }

    // Checkbox cell
    const checkboxCell = document.createElement('td');
    checkboxCell.className = 'border border-gray-300 p-2';
    checkboxCell.innerHTML = `
      <div class="flex items-center">
        <input type="checkbox" class="invoice-checkbox" data-invoice-id="${invoice.fileHash}">
        <span class="ml-2 text-xs text-gray-500">${index + 1}</span>
      </div>
    `;
    row.appendChild(checkboxCell);

    // Document cell
    const docCell = document.createElement('td');
    docCell.className = 'border border-gray-300 p-2';
    const docType = invoice.kind || invoice.documentType || 'invoice';
    docCell.innerHTML = `
      <div class="font-medium">${invoice.documentName || invoice.fileName || 'Unknown'}</div>
      <div class="text-sm text-gray-600">Type: ${docType}</div>
    `;
    row.appendChild(docCell);

    // Party cell (Seller or Buyer)
    const partyCell = document.createElement('td');
    partyCell.className = 'border border-gray-300 p-2';
    if (isPurchase) {
      const sellerName = invoice.seller_name || invoice.seller || 'Unknown Seller';
      const sellerNip = invoice.seller_tax_no || '';
      partyCell.innerHTML = `
        <div>${sellerName}</div>
        ${sellerNip ? `<div class="text-sm text-gray-600">NIP: ${sellerNip}</div>` : ''}
      `;
    } else {
      const buyerName = invoice.buyer_name || invoice.buyer || 'Unknown Buyer';
      const buyerNip = invoice.buyer_tax_no || '';
      partyCell.innerHTML = `
        <div>${buyerName}</div>
        ${buyerNip ? `<div class="text-sm text-gray-600">NIP: ${buyerNip}</div>` : ''}
      `;
    }
    row.appendChild(partyCell);

    // Amount cell
    const amountCell = document.createElement('td');
    amountCell.className = 'border border-gray-300 p-2 text-right';
    const totalNet = parseFloat(invoice.total_net) || 0;
    const totalVat = parseFloat(invoice.total_vat) || 0;
    const totalGross = parseFloat(invoice.total_gross) || 0;
    amountCell.innerHTML = `
      <div class="text-sm">Net: ${totalNet.toFixed(2)} PLN</div>
      <div class="text-sm">VAT: ${totalVat.toFixed(2)} PLN</div>
      <div class="font-medium">Gross: ${totalGross.toFixed(2)} PLN</div>
    `;
    row.appendChild(amountCell);

    // Date cell
    const dateCell = document.createElement('td');
    dateCell.className = 'border border-gray-300 p-2';
    const date = new Date(invoice.issue_date || invoice.accountingDate);
    dateCell.textContent = date.toLocaleDateString('pl-PL');
    row.appendChild(dateCell);

    // Details cell
    const detailsCell = document.createElement('td');
    detailsCell.className = 'border border-gray-300 p-2';
    const description = invoice.description || invoice.summary || 'No description';
    detailsCell.innerHTML = `
      <div class="text-sm line-clamp-2" title="${description}">
        ${description.length > 50 ? description.substring(0, 50) + '...' : description}
      </div>
    `;
    row.appendChild(detailsCell);

    // Status cell
    const statusCell = document.createElement('td');
    statusCell.className = 'border border-gray-300 p-2';
    const syncStatus = invoice.syncedWithFakturownia ? 'Synced' : 'Pending';
    const statusColor = invoice.syncedWithFakturownia ? 'text-green-600' : 'text-yellow-600';
    statusCell.innerHTML = `
      <span class="text-sm ${statusColor}">${syncStatus}</span>
    `;
    row.appendChild(statusCell);

    return row;
  }

  /**
   * Format period label for display
   * @param {string} period - Period key
   * @returns {string} - Formatted label
   */
  formatPeriodLabel(period) {
    if (period.includes('Q')) {
      return period; // Already formatted for quarters
    } else if (period.includes('-')) {
      const [year, month] = period.split('-');
      const monthNames = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
      ];
      return `${monthNames[parseInt(month) - 1]} ${year}`;
    }
    return period; // Year only

  }

  /**
   * Show validation errors
   * @param {Array} invoices - Processed invoices
   */
  showValidationErrors(invoices) {
    const invalidInvoices = invoices.filter(inv => inv.invalidInvoice);

    if (invalidInvoices.length > 0) {
      const errorMessage = `Found ${invalidInvoices.length} invalid invoice(s)`;
      this.showErrorBanner(errorMessage);
    }
  }

  /**
   * Show error banner
   * @param {string} message - Error message
   */
  showErrorBanner(message) {
    // Remove existing banner
    const existingBanner = document.getElementById('errorBanner');
    if (existingBanner) {
      existingBanner.remove();
    }

    // Create new banner
    const banner = document.createElement('div');
    banner.id = 'errorBanner';
    banner.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4';
    banner.innerHTML = `
      <div class="flex justify-between items-center">
        <span>${message}</span>
        <button class="text-red-700 hover:text-red-900" onclick="this.parentElement.parentElement.remove()">×</button>
      </div>
    `;

    // Insert at top of container
    const container = document.getElementById('invoiceTablesContainer');
    if (container) {
      container.insertBefore(banner, container.firstChild);
    }
  }

  /**
   * Normalize NIP (remove non-digits)
   * @param {string} nip - NIP to normalize
   * @returns {string} - Normalized NIP
   */
  normalizeNip(nip) {
    return nip ? nip.replace(/\D/g, '') : '';
  }
}

// Create singleton instance
const invoiceTable = new InvoiceTable();

// Export for ES modules
export default invoiceTable;

// Export to window object for non-module scripts
if (typeof window !== 'undefined') {
  window.InvoiceTable = InvoiceTable;
  window.invoiceTable = invoiceTable;

  // Legacy compatibility
  window.updateInvoicesTable = async (invoices) => {
    await invoiceTable.updateTable(invoices);
  };
}

// Local test function
function testInvoiceTable() {
  console.log('=== InvoiceTable Local Test ===');

  try {
    const table = new InvoiceTable();

    // Test 1: Initialization
    console.log('Test 1: Initialization');
    console.log('✓ InvoiceTable instance created');

    // Test 2: Period key generation
    console.log('\nTest 2: Period key generation');
    const testDate = new Date('2024-03-15');
    table.groupingSettings = { groupByPeriod: 'monthly' };
    const monthlyKey = table.getPeriodKey(testDate);
    console.log('✓ Monthly period key:', monthlyKey);

    table.groupingSettings = { groupByPeriod: 'quarterly' };
    const quarterlyKey = table.getPeriodKey(testDate);
    console.log('✓ Quarterly period key:', quarterlyKey);

    // Test 3: NIP normalization
    console.log('\nTest 3: NIP normalization');
    const normalizedNip = table.normalizeNip('PL 123-456-78-90');
    console.log('✓ NIP normalization works:', normalizedNip);

    // Test 4: Invoice type determination
    console.log('\nTest 4: Invoice type determination');
    table.companyInfo = { taxId: '1234567890' };
    const testInvoice = {
      seller_tax_no: '1234567890',
      buyer_tax_no: '0987654321'
    };
    const invoiceType = table.determineInvoiceType(testInvoice);
    console.log('✓ Invoice type determination:', invoiceType);

    console.log('\n✅ All InvoiceTable tests passed!');
    return true;

  } catch (error) {
    console.error('❌ InvoiceTable test failed:', error);
    return false;
  }
}

// Run test if in browser environment
if (typeof window !== 'undefined' && window.location) {
  // Uncomment to run test
  // testInvoiceTable();
}

// ===== LOCAL TESTING (Node.js equivalent of Python's if __name__ == '__main__') =====
if (typeof process !== 'undefined' && process.argv && process.argv[1] && process.argv[1].includes('InvoiceTable.js')) {
  console.log('🧪 Running local tests for InvoiceTable...');

  // Mock document for Node.js
  if (typeof document === 'undefined') {
    global.document = {
      createElement: (tag) => ({
        tagName: tag.toUpperCase(),
        className: '',
        innerHTML: '',
        textContent: '',
        style: {},
        appendChild: () => {},
        insertBefore: () => {},
        remove: () => {},
        classList: {
          add: () => {},
          remove: () => {}
        }
      }),
      getElementById: () => null
    };
  }

  try {
    const table = new InvoiceTable();

    console.log('✅ Test 1: InvoiceTable initialization');
    console.log('📊 Table created:', !!table);
    console.log('📊 Storage API available:', !!table.storageAPI);
    console.log('📊 Validation service available:', !!table.validationService);
    console.log('📊 Current invoices array:', Array.isArray(table.currentInvoices));
    console.log('📊 Grouping settings:', JSON.stringify(table.groupingSettings));

    console.log('\n✅ Test 2: Period key generation');
    const testDates = [
      new Date('2024-01-15'),
      new Date('2024-03-15'),
      new Date('2024-06-15'),
      new Date('2024-12-15')
    ];

    // Test monthly grouping
    table.groupingSettings = { groupByPeriod: 'monthly' };
    testDates.forEach(date => {
      const key = table.getPeriodKey(date);
      console.log(`📊 Monthly ${date.toISOString().split('T')[0]}: ${key}`);
    });

    // Test quarterly grouping
    table.groupingSettings = { groupByPeriod: 'quarterly' };
    testDates.forEach(date => {
      const key = table.getPeriodKey(date);
      console.log(`📊 Quarterly ${date.toISOString().split('T')[0]}: ${key}`);
    });

    // Test yearly grouping
    table.groupingSettings = { groupByPeriod: 'yearly' };
    const yearlyKey = table.getPeriodKey(testDates[0]);
    console.log(`📊 Yearly ${testDates[0].toISOString().split('T')[0]}: ${yearlyKey}`);

    console.log('\n✅ Test 3: NIP normalization');
    const testNips = [
      'PL 123-456-78-90',
      '123 456 78 90',
      '1234567890',
      'PL1234567890',
      '',
      null,
      undefined
    ];

    testNips.forEach(nip => {
      const normalized = table.normalizeNip(nip);
      console.log(`📊 "${nip}" → "${normalized}"`);
    });

    console.log('\n✅ Test 4: Invoice type determination');
    table.companyInfo = { taxId: '1234567890' };

    const testInvoices = [
      {
        seller_tax_no: '1234567890',
        buyer_tax_no: '0987654321',
        income: '1'
      },
      {
        seller_tax_no: '0987654321',
        buyer_tax_no: '1234567890',
        income: '0'
      },
      {
        seller_tax_no: '1111111111',
        buyer_tax_no: '2222222222',
        income: '1'
      },
      {
        seller_tax_no: '1111111111',
        buyer_tax_no: '2222222222',
        income: '0'
      }
    ];

    testInvoices.forEach((invoice, index) => {
      const type = table.determineInvoiceType(invoice);
      console.log(`📊 Invoice ${index + 1}: ${type}`);
    });

    console.log('\n✅ Test 5: Invoice validation for company');
    const validationTests = [
      {
        seller_tax_no: '1234567890',
        buyer_tax_no: '0987654321'
      },
      {
        seller_tax_no: '0987654321',
        buyer_tax_no: '1234567890'
      },
      {
        seller_tax_no: '1111111111',
        buyer_tax_no: '2222222222'
      },
      {
        seller_tax_no: '',
        buyer_tax_no: '1234567890'
      },
      {
        seller_tax_no: '1234567890',
        buyer_tax_no: ''
      }
    ];

    validationTests.forEach((invoice, index) => {
      const validation = table.validateInvoiceForCompany(invoice);
      console.log(`📊 Validation ${index + 1}: ${validation.valid ? '✅' : '❌'} ${validation.reason || ''}`);
    });

    console.log('\n✅ Test 6: Invoice grouping by period');
    const sampleInvoices = [
      {
        issue_date: '2024-01-15',
        seller_tax_no: '1234567890',
        buyer_tax_no: '0987654321',
        invoiceType: 'Sales Invoice'
      },
      {
        issue_date: '2024-01-20',
        seller_tax_no: '0987654321',
        buyer_tax_no: '1234567890',
        invoiceType: 'Purchase Invoice'
      },
      {
        issue_date: '2024-02-10',
        seller_tax_no: '1234567890',
        buyer_tax_no: '1111111111',
        invoiceType: 'Sales Invoice'
      }
    ];

    table.groupingSettings = { groupByPeriod: 'monthly' };
    const grouped = table.groupInvoicesByPeriod(sampleInvoices);
    console.log('📊 Grouped periods:', Object.keys(grouped));
    Object.keys(grouped).forEach(period => {
      const data = grouped[period];
      console.log(`📊 ${period}: ${data.sales.length} sales, ${data.purchases.length} purchases`);
    });

    console.log('\n✅ Test 7: Period label formatting');
    const testPeriods = ['2024-01', '2024-03', '2024 Q1', '2024 Q4', '2024'];
    testPeriods.forEach(period => {
      const formatted = table.formatPeriodLabel(period);
      console.log(`📊 "${period}" → "${formatted}"`);
    });

    console.log('\n🎉 All tests completed for InvoiceTable');
    console.log('📋 Note: Full functionality requires browser environment with DOM elements');

  } catch (error) {
    console.error('❌ InvoiceTable test failed:', error);
  }
}
