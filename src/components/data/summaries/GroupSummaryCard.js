/**
 * GroupSummaryCard - Display component for group summaries
 * Shows aggregated statistics and metadata for invoice groups
 * Supports expand/collapse functionality and multiple currencies
 *
 * Epic: EPIC-003 - Data Display & Visualization
 * Story: STORY-3.2 - Grouping & Aggregation
 * Task: TASK-3.2.1 - Grouping Logic
 */

import React, { useState } from 'react';

export const GroupSummaryCard = ({
  group,
  isExpanded = false,
  onToggleExpand,
  onInvoiceSelect,
  showDetails = true,
  className = ''
}) => {
  const [showAllCurrencies, setShowAllCurrencies] = useState(false);

  if (!group) {
    return null;
  }

  const {
    key,
    invoices = [],
    metadata = {},
    aggregations = {},
    statistics = {}
  } = group;

  const currencies = Object.keys(aggregations);
  const primaryCurrency = currencies[0] || 'PLN';
  const hasMultipleCurrencies = currencies.length > 1;

  const formatCurrency = (amount, currency = primaryCurrency) => {
    if (amount === null || amount === undefined || isNaN(amount)) {
      return '0.00';
    }
    return new Intl.NumberFormat('pl-PL', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(amount);
  };

  const formatDate = (date) => {
    if (!date) { return 'N/A'; }
    return new Date(date).toLocaleDateString('pl-PL');
  };

  const formatNumber = (num) => {
    if (num === null || num === undefined || isNaN(num)) {
      return '0';
    }
    return new Intl.NumberFormat('pl-PL').format(num);
  };

  const getCurrencyData = (currency) => {
    return aggregations[currency] || {};
  };

  const getDisplayCurrencies = () => {
    if (showAllCurrencies || currencies.length <= 1) {
      return currencies;
    }
    return [primaryCurrency];
  };

  const getTrendIndicator = (current, previous) => {
    if (!previous || previous === 0) { return null; }
    const change = ((current - previous) / previous) * 100;
    if (Math.abs(change) < 1) { return null; }

    return {
      direction: change > 0 ? 'up' : 'down',
      percentage: Math.abs(change).toFixed(1)
    };
  };

  return (
    <div className={`group-summary-card bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      {/* Header */}
      <div
        className="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50 transition-colors"
        onClick={onToggleExpand}
      >
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <span className="text-lg">
              {isExpanded ? '▼' : '▶'}
            </span>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              {metadata.displayName || key}
            </h3>
            <p className="text-sm text-gray-500">
              {statistics.invoiceCount || invoices.length} invoice{(statistics.invoiceCount || invoices.length) !== 1 ? 's' : ''}
              {statistics.dateRange && (
                <span className="ml-2">
                  ({formatDate(statistics.dateRange.start)} - {formatDate(statistics.dateRange.end)})
                </span>
              )}
            </p>
          </div>
        </div>

        {/* Quick Summary */}
        <div className="text-right">
          {getDisplayCurrencies().map(currency => {
            const currencyData = getCurrencyData(currency);
            const totalGross = currencyData.total_gross?.sum || 0;

            return (
              <div key={currency} className="text-sm">
                <div className="font-semibold text-gray-900">
                  {formatCurrency(totalGross, currency)}
                </div>
                <div className="text-gray-500">
                  {formatNumber(currencyData.count || 0)} items
                </div>
              </div>
            );
          })}

          {hasMultipleCurrencies && !showAllCurrencies && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                setShowAllCurrencies(true);
              }}
              className="text-xs text-blue-600 hover:text-blue-800 mt-1"
            >
              +{currencies.length - 1} more
            </button>
          )}
        </div>
      </div>

      {/* Expanded Content */}
      {isExpanded && (
        <div className="border-t border-gray-200">
          {/* Currency Details */}
          {getDisplayCurrencies().map(currency => {
            const currencyData = getCurrencyData(currency);

            return (
              <div key={currency} className="p-4 border-b border-gray-100 last:border-b-0">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium text-gray-900">
                    {currency} {hasMultipleCurrencies && `(${currencyData.count || 0} invoices)`}
                  </h4>
                  {hasMultipleCurrencies && showAllCurrencies && currency !== primaryCurrency && (
                    <span className="text-xs text-gray-500">Secondary Currency</span>
                  )}
                </div>

                {/* Financial Metrics */}
                <div className="grid grid-cols-3 gap-4 mb-4">
                  <div className="text-center">
                    <div className="text-sm text-gray-500">Net Total</div>
                    <div className="font-semibold">
                      {formatCurrency(currencyData.total_net?.sum || 0, currency)}
                    </div>
                    <div className="text-xs text-gray-400">
                      Avg: {formatCurrency(currencyData.total_net?.average || 0, currency)}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm text-gray-500">VAT Total</div>
                    <div className="font-semibold">
                      {formatCurrency(currencyData.total_vat?.sum || 0, currency)}
                    </div>
                    <div className="text-xs text-gray-400">
                      Avg: {formatCurrency(currencyData.total_vat?.average || 0, currency)}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm text-gray-500">Gross Total</div>
                    <div className="font-semibold text-lg">
                      {formatCurrency(currencyData.total_gross?.sum || 0, currency)}
                    </div>
                    <div className="text-xs text-gray-400">
                      Avg: {formatCurrency(currencyData.total_gross?.average || 0, currency)}
                    </div>
                  </div>
                </div>

                {/* Statistical Details */}
                {showDetails && (
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <div className="text-gray-500">Range</div>
                      <div>
                        {formatCurrency(currencyData.total_gross?.min || 0, currency)} - {formatCurrency(currencyData.total_gross?.max || 0, currency)}
                      </div>
                    </div>
                    <div>
                      <div className="text-gray-500">Median</div>
                      <div>
                        {formatCurrency(currencyData.total_gross?.median || 0, currency)}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            );
          })}

          {/* Group Statistics */}
          {showDetails && statistics && (
            <div className="p-4 bg-gray-50">
              <h5 className="font-medium text-gray-900 mb-2">Group Statistics</h5>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-500">Date Range:</span>
                  <div>{statistics.daySpan || 0} days</div>
                </div>
                <div>
                  <span className="text-gray-500">Currencies:</span>
                  <div>{statistics.currencies?.join(', ') || 'N/A'}</div>
                </div>
              </div>
            </div>
          )}

          {/* Invoice List */}
          {invoices.length > 0 && (
            <div className="p-4">
              <div className="flex items-center justify-between mb-2">
                <h5 className="font-medium text-gray-900">Invoices</h5>
                <span className="text-sm text-gray-500">
                  {invoices.length} item{invoices.length !== 1 ? 's' : ''}
                </span>
              </div>

              <div className="max-h-48 overflow-y-auto">
                <div className="space-y-1">
                  {invoices.slice(0, 10).map((invoice, index) => (
                    <div
                      key={invoice.id || index}
                      className="flex items-center justify-between p-2 hover:bg-gray-50 rounded cursor-pointer text-sm"
                      onClick={() => onInvoiceSelect && onInvoiceSelect(invoice)}
                    >
                      <div className="flex-1 min-w-0">
                        <div className="font-medium truncate">
                          {invoice.number || invoice.filename || `Invoice ${index + 1}`}
                        </div>
                        <div className="text-gray-500 text-xs">
                          {formatDate(invoice.issue_date || invoice.groupingDate)}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">
                          {formatCurrency(invoice.total_gross, invoice.currency)}
                        </div>
                        <div className="text-xs text-gray-500">
                          {invoice.seller_name || 'Unknown Seller'}
                        </div>
                      </div>
                    </div>
                  ))}

                  {invoices.length > 10 && (
                    <div className="text-center py-2 text-sm text-gray-500">
                      ... and {invoices.length - 10} more invoices
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default GroupSummaryCard;
