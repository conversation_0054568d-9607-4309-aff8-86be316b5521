/**
 * SummaryCard - Enhanced summary card component with trend indicators
 * Displays group summaries with visual trends and period comparisons
 *
 * Epic: EPIC-003 - Data Display & Visualization
 * Story: STORY-3.2 - Grouping & Aggregation
 * Task: TASK-3.2.2 - Summary Views
 * Assignment: ASSIGNMENT-030 - Summary Cards and Visual Indicators
 */

import React, { useState } from 'react';
import { TrendIndicator, TrendCard, TrendGrid } from '../charts/TrendIndicator.jsx';
import {
  calculateGroupSummary,
  generateTrendCards,
  findPreviousPeriodGroup,
  formatCurrency,
  formatNumber
} from '../../utils/summaryCalculations.js';

export const SummaryCard = ({
  group,
  allGroups = [],
  isExpanded = false,
  onToggleExpand,
  onInvoiceSelect,
  showTrends = true,
  showDetails = true,
  primaryCurrency = 'PLN',
  className = ''
}) => {
  const [showAllCurrencies, setShowAllCurrencies] = useState(false);

  if (!group) {
    return null;
  }

  const {
    key,
    invoices = [],
    metadata = {},
    aggregations = {},
    statistics = {}
  } = group;

  const currencies = Object.keys(aggregations);
  const hasMultipleCurrencies = currencies.length > 1;
  const displayCurrency = currencies.includes(primaryCurrency) ? primaryCurrency : currencies[0] || 'PLN';

  // Calculate summary metrics
  const summary = calculateGroupSummary(group, displayCurrency);

  // Find previous period for trend comparison
  const previousGroup = showTrends ? findPreviousPeriodGroup(allGroups, group) : null;

  // Generate trend cards
  const trendCards = showTrends ? generateTrendCards(group, previousGroup, displayCurrency) : [];

  const formatDate = (date) => {
    if (!date) { return 'N/A'; }
    return new Date(date).toLocaleDateString('pl-PL');
  };

  const getCurrencyData = (currency) => {
    return aggregations[currency] || {};
  };

  const getDisplayCurrencies = () => {
    if (showAllCurrencies || currencies.length <= 1) {
      return currencies;
    }
    return [displayCurrency];
  };

  return (
    <div className={`summary-card bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow ${className}`}>
      {/* Header */}
      <div
        className="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50 transition-colors"
        onClick={onToggleExpand}
        role="button"
        tabIndex={0}
        aria-expanded={isExpanded}
        aria-label={`Toggle details for ${metadata.displayName || key}`}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            onToggleExpand();
          }
        }}
      >
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <span className="text-lg text-gray-400" aria-hidden="true">
              {isExpanded ? '▼' : '▶'}
            </span>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              {metadata.displayName || key}
            </h3>
            <p className="text-sm text-gray-500">
              {summary.count} invoice{summary.count !== 1 ? 's' : ''}
              {statistics.dateRange && (
                <span className="ml-2">
                  ({formatDate(statistics.dateRange.start)} - {formatDate(statistics.dateRange.end)})
                </span>
              )}
            </p>
          </div>
        </div>

        {/* Quick Summary with Trend */}
        <div className="text-right">
          <div className="text-lg font-bold text-gray-900">
            {formatCurrency(summary.totalGross, displayCurrency)}
          </div>
          <div className="text-sm text-gray-500">
            {formatNumber(summary.count)} items
          </div>
          {showTrends && previousGroup && (
            <div className="mt-1">
              <TrendIndicator
                trend={trendCards[0]?.trend}
                percentage={trendCards[0]?.percentage}
                comparison={trendCards[0]?.comparison}
                size="sm"
              />
            </div>
          )}
        </div>
      </div>

      {/* Expanded Content */}
      {isExpanded && (
        <div className="border-t border-gray-200">
          {/* Trend Cards Grid */}
          {showTrends && trendCards.length > 0 && (
            <div className="p-4 bg-gray-50">
              <h4 className="text-sm font-medium text-gray-900 mb-3">Key Metrics</h4>
              <TrendGrid
                cards={trendCards}
                columns={4}
                gap={3}
                className="mb-4"
              />
            </div>
          )}

          {/* Currency Details */}
          {getDisplayCurrencies().map(currency => {
            const currencyData = getCurrencyData(currency);
            const currencySummary = calculateGroupSummary(group, currency);

            return (
              <div key={currency} className="p-4 border-b border-gray-100 last:border-b-0">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium text-gray-900">
                    {currency} {hasMultipleCurrencies && `(${currencySummary.count} invoices)`}
                  </h4>
                  {hasMultipleCurrencies && showAllCurrencies && currency !== displayCurrency && (
                    <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                      Secondary Currency
                    </span>
                  )}
                </div>

                {/* Financial Metrics Grid */}
                <div className="grid grid-cols-3 gap-4 mb-4">
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <div className="text-xs text-blue-600 font-medium uppercase tracking-wide">Net Total</div>
                    <div className="text-lg font-bold text-blue-900">
                      {formatCurrency(currencySummary.totalNet, currency)}
                    </div>
                    <div className="text-xs text-blue-600">
                      Avg: {formatCurrency(currencySummary.averageNet, currency)}
                    </div>
                  </div>
                  <div className="text-center p-3 bg-yellow-50 rounded-lg">
                    <div className="text-xs text-yellow-600 font-medium uppercase tracking-wide">VAT Total</div>
                    <div className="text-lg font-bold text-yellow-900">
                      {formatCurrency(currencySummary.totalVat, currency)}
                    </div>
                    <div className="text-xs text-yellow-600">
                      Tax amount
                    </div>
                  </div>
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <div className="text-xs text-green-600 font-medium uppercase tracking-wide">Gross Total</div>
                    <div className="text-xl font-bold text-green-900">
                      {formatCurrency(currencySummary.totalGross, currency)}
                    </div>
                    <div className="text-xs text-green-600">
                      Avg: {formatCurrency(currencySummary.averageGross, currency)}
                    </div>
                  </div>
                </div>

                {/* Statistical Details */}
                {showDetails && (
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div className="text-center">
                      <div className="text-gray-500 text-xs">Range</div>
                      <div className="font-medium">
                        {formatCurrency(currencySummary.minGross, currency)} - {formatCurrency(currencySummary.maxGross, currency)}
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-gray-500 text-xs">Median</div>
                      <div className="font-medium">
                        {formatCurrency(currencySummary.medianGross, currency)}
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-gray-500 text-xs">Count</div>
                      <div className="font-medium">
                        {formatNumber(currencySummary.count)} invoices
                      </div>
                    </div>
                  </div>
                )}
              </div>
            );
          })}

          {/* Multiple Currencies Toggle */}
          {hasMultipleCurrencies && !showAllCurrencies && (
            <div className="p-4 text-center border-b border-gray-100">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setShowAllCurrencies(true);
                }}
                className="text-sm text-blue-600 hover:text-blue-800 font-medium"
              >
                Show all {currencies.length} currencies
              </button>
            </div>
          )}

          {/* Group Statistics */}
          {showDetails && statistics && (
            <div className="p-4 bg-gray-50">
              <h5 className="font-medium text-gray-900 mb-3">Group Statistics</h5>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-gray-500 text-xs">Date Range</span>
                  <div className="font-medium">{statistics.daySpan || 0} days</div>
                </div>
                <div>
                  <span className="text-gray-500 text-xs">Currencies</span>
                  <div className="font-medium">{statistics.currencies?.join(', ') || 'N/A'}</div>
                </div>
                <div>
                  <span className="text-gray-500 text-xs">First Invoice</span>
                  <div className="font-medium">{formatDate(statistics.firstInvoiceDate)}</div>
                </div>
                <div>
                  <span className="text-gray-500 text-xs">Last Invoice</span>
                  <div className="font-medium">{formatDate(statistics.lastInvoiceDate)}</div>
                </div>
              </div>
            </div>
          )}

          {/* Invoice List */}
          {invoices.length > 0 && (
            <div className="p-4">
              <div className="flex items-center justify-between mb-3">
                <h5 className="font-medium text-gray-900">Invoices</h5>
                <span className="text-sm text-gray-500">
                  {invoices.length} item{invoices.length !== 1 ? 's' : ''}
                </span>
              </div>

              <div className="max-h-48 overflow-y-auto">
                <div className="space-y-2">
                  {invoices.slice(0, 10).map((invoice, index) => (
                    <div
                      key={invoice.id || index}
                      className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg cursor-pointer text-sm border border-gray-100"
                      onClick={() => onInvoiceSelect && onInvoiceSelect(invoice)}
                      role="button"
                      tabIndex={0}
                      aria-label={`View invoice ${invoice.number || `#${index + 1}`}`}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          e.preventDefault();
                          onInvoiceSelect && onInvoiceSelect(invoice);
                        }
                      }}
                    >
                      <div className="flex-1 min-w-0">
                        <div className="font-medium truncate">
                          {invoice.number || invoice.filename || `Invoice ${index + 1}`}
                        </div>
                        <div className="text-gray-500 text-xs">
                          {formatDate(invoice.issue_date || invoice.groupingDate)} • {invoice.seller_name || 'Unknown Seller'}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold text-gray-900">
                          {formatCurrency(invoice.total_gross, invoice.currency)}
                        </div>
                        <div className="text-xs text-gray-500">
                          {invoice.currency || displayCurrency}
                        </div>
                      </div>
                    </div>
                  ))}

                  {invoices.length > 10 && (
                    <div className="text-center py-3 text-sm text-gray-500 border-t border-gray-100">
                      ... and {invoices.length - 10} more invoices
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

/**
 * GroupSummary - Container component for multiple summary cards
 */
export const GroupSummary = ({
  groups = [],
  expandedGroups = new Set(),
  onToggleGroup,
  onInvoiceSelect,
  showTrends = true,
  primaryCurrency = 'PLN',
  className = ''
}) => {
  if (!groups || groups.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <p>No grouped data available</p>
      </div>
    );
  }

  return (
    <div className={`group-summary-container space-y-4 ${className}`}>
      {groups.map((group, index) => (
        <SummaryCard
          key={group.key || index}
          group={group}
          allGroups={groups}
          isExpanded={expandedGroups.has(group.key)}
          onToggleExpand={() => onToggleGroup && onToggleGroup(group.key)}
          onInvoiceSelect={onInvoiceSelect}
          showTrends={showTrends}
          primaryCurrency={primaryCurrency}
        />
      ))}
    </div>
  );
};

export default SummaryCard;
