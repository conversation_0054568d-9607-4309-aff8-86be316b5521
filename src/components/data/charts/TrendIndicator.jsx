/**
 * TrendIndicator - Visual trend indicator component
 * Shows trend direction with arrows, colors, and percentage changes
 * Supports various trend types and comparison periods
 *
 * Epic: EPIC-003 - Data Display & Visualization
 * Story: STORY-3.2 - Grouping & Aggregation
 * Task: TASK-3.2.2 - Summary Views
 * Assignment: ASSIGNMENT-030 - Summary Cards and Visual Indicators
 */

import React from 'react';

export const TrendIndicator = ({
  trend = null,
  percentage = 0,
  comparison = '',
  size = 'md',
  showPercentage = true,
  showComparison = true,
  className = ''
}) => {
  // Handle null or invalid trend data
  if (!trend || percentage === 0 || isNaN(percentage)) {
    return showComparison && comparison ? (
      <div className={`text-xs text-gray-400 ${className}`}>
        {comparison}
      </div>
    ) : null;
  }

  const isPositive = trend === 'up' || percentage > 0;
  const isNegative = trend === 'down' || percentage < 0;

  // Size configurations
  const sizeConfig = {
    sm: {
      container: 'text-xs',
      arrow: 'text-xs',
      percentage: 'text-xs',
      comparison: 'text-xs'
    },
    md: {
      container: 'text-sm',
      arrow: 'text-sm',
      percentage: 'text-sm',
      comparison: 'text-xs'
    },
    lg: {
      container: 'text-base',
      arrow: 'text-base',
      percentage: 'text-sm',
      comparison: 'text-xs'
    }
  };

  const config = sizeConfig[size] || sizeConfig.md;

  // Color and icon configurations
  const getIndicatorStyles = () => {
    if (isPositive) {
      return {
        containerClass: 'text-green-600',
        arrow: '↗',
        bgClass: 'bg-green-50',
        borderClass: 'border-green-200'
      };
    } else if (isNegative) {
      return {
        containerClass: 'text-red-600',
        arrow: '↘',
        bgClass: 'bg-red-50',
        borderClass: 'border-red-200'
      };
    }
    return {
      containerClass: 'text-gray-500',
      arrow: '→',
      bgClass: 'bg-gray-50',
      borderClass: 'border-gray-200'
    };

  };

  const styles = getIndicatorStyles();
  const absPercentage = Math.abs(percentage);

  return (
    <div className={`inline-flex items-center space-x-1 ${config.container} ${className}`}>
      {/* Trend Arrow */}
      <span
        className={`${config.arrow} ${styles.containerClass} font-bold`}
        aria-label={`Trend ${isPositive ? 'up' : isNegative ? 'down' : 'neutral'}`}
      >
        {styles.arrow}
      </span>

      {/* Percentage Change */}
      {showPercentage && (
        <span className={`${config.percentage} font-medium ${styles.containerClass}`}>
          {absPercentage.toFixed(1)}%
        </span>
      )}

      {/* Comparison Period */}
      {showComparison && comparison && (
        <span className={`${config.comparison} text-gray-500`}>
          {comparison}
        </span>
      )}
    </div>
  );
};

/**
 * TrendCard - Enhanced card with trend indicator
 * Combines metric display with trend visualization
 */
export const TrendCard = ({
  title,
  value,
  currency = '',
  trend = null,
  percentage = 0,
  comparison = '',
  subtitle = '',
  icon = null,
  size = 'md',
  className = ''
}) => {
  const formatValue = (val) => {
    if (currency && !isNaN(val)) {
      return new Intl.NumberFormat('pl-PL', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 2
      }).format(val);
    }

    if (!isNaN(val)) {
      return new Intl.NumberFormat('pl-PL').format(val);
    }

    return val || '0';
  };

  const sizeConfig = {
    sm: {
      container: 'p-3',
      title: 'text-xs',
      value: 'text-lg',
      subtitle: 'text-xs'
    },
    md: {
      container: 'p-4',
      title: 'text-sm',
      value: 'text-xl',
      subtitle: 'text-xs'
    },
    lg: {
      container: 'p-6',
      title: 'text-base',
      value: 'text-2xl',
      subtitle: 'text-sm'
    }
  };

  const config = sizeConfig[size] || sizeConfig.md;

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${config.container} ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          {icon && <span className="text-gray-400">{icon}</span>}
          <h4 className={`${config.title} font-medium text-gray-600 uppercase tracking-wide`}>
            {title}
          </h4>
        </div>
        <TrendIndicator
          trend={trend}
          percentage={percentage}
          comparison={comparison}
          size="sm"
          showComparison={false}
        />
      </div>

      {/* Value */}
      <div className={`${config.value} font-bold text-gray-900 mb-1`}>
        {formatValue(value)}
      </div>

      {/* Subtitle and Comparison */}
      <div className="flex items-center justify-between">
        {subtitle && (
          <span className={`${config.subtitle} text-gray-500`}>
            {subtitle}
          </span>
        )}
        {comparison && (
          <TrendIndicator
            trend={trend}
            percentage={percentage}
            comparison={comparison}
            size="sm"
            showPercentage={false}
          />
        )}
      </div>
    </div>
  );
};

/**
 * TrendGrid - Grid layout for multiple trend cards
 */
export const TrendGrid = ({
  cards = [],
  columns = 3,
  gap = 4,
  className = ''
}) => {
  const gridClass = `grid grid-cols-1 md:grid-cols-${Math.min(columns, 3)} lg:grid-cols-${columns} gap-${gap}`;

  return (
    <div className={`${gridClass} ${className}`}>
      {cards.map((card, index) => (
        <TrendCard
          key={card.id || index}
          {...card}
        />
      ))}
    </div>
  );
};

export default TrendIndicator;
