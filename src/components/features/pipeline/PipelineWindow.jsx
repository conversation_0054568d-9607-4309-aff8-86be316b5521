/**
 * Pipeline Window Component
 * Opens multi-step pipeline in a separate detached window for better user experience
 * Replaces modal/overlay approach with dedicated window
 */

import React, { useEffect, useRef, useState } from 'react';
import { createRoot } from 'react-dom/client';
import EnhancedPipelineVisualization, { VIEW_MODES } from './EnhancedPipelineVisualization.jsx';

/**
 * Pipeline Window Manager
 * Handles opening and managing separate windows for pipeline processing
 */
export class PipelineWindowManager {
  constructor() {
    this.windows = new Map(); // Track open windows by file ID
  }

  /**
   * Open pipeline in separate window
   * @param {Object} file - File to process
   * @param {Object} options - Pipeline options
   * @returns {Window} - Reference to opened window
   */
  openPipelineWindow(file, options = {}) {
    const fileId = file?.name || `pipeline-${Date.now()}`;
    
    // Close existing window for this file if open
    if (this.windows.has(fileId)) {
      this.closePipelineWindow(fileId);
    }

    // Window configuration
    const windowFeatures = [
      'width=1200',
      'height=800',
      'left=100',
      'top=100',
      'resizable=yes',
      'scrollbars=yes',
      'status=no',
      'menubar=no',
      'toolbar=no',
      'location=no',
      'directories=no'
    ].join(',');

    // Open new window
    const pipelineWindow = window.open('', `pipeline-${fileId}`, windowFeatures);
    
    if (!pipelineWindow) {
      console.error('Failed to open pipeline window - popup blocked?');
      return null;
    }

    // Set up window document
    this.setupWindowDocument(pipelineWindow, file, options);
    
    // Track window
    this.windows.set(fileId, pipelineWindow);
    
    // Handle window close
    pipelineWindow.addEventListener('beforeunload', () => {
      this.windows.delete(fileId);
    });

    return pipelineWindow;
  }

  /**
   * Setup window document with React app
   * @param {Window} pipelineWindow - Window reference
   * @param {Object} file - File to process
   * @param {Object} options - Pipeline options
   */
  setupWindowDocument(pipelineWindow, file, options) {
    const doc = pipelineWindow.document;
    
    // Set up basic HTML structure
    doc.open();
    doc.write(`
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Pipeline: ${file?.name || 'Processing'} - MVAT</title>
        <style>
          * { margin: 0; padding: 0; box-sizing: border-box; }
          body { 
            font-family: Inter, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f9fafb;
            height: 100vh;
            overflow: hidden;
          }
          #pipeline-root { 
            width: 100%; 
            height: 100vh; 
            display: flex; 
            flex-direction: column;
          }
          .pipeline-header {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
          }
          .pipeline-content {
            flex: 1;
            padding: 1.5rem;
            overflow: hidden;
          }
          .close-btn {
            background: #ef4444;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            cursor: pointer;
            font-size: 0.875rem;
            font-weight: 500;
          }
          .close-btn:hover {
            background: #dc2626;
          }
        </style>
      </head>
      <body>
        <div id="pipeline-root">
          <div class="pipeline-header">
            <div>
              <h1 style="font-size: 1.25rem; font-weight: 600; color: #111827;">
                Multi-Step Pipeline: ${file?.name || 'Processing'}
              </h1>
              <p style="font-size: 0.875rem; color: #6b7280; margin-top: 0.25rem;">
                Real-time document processing pipeline
              </p>
            </div>
            <button class="close-btn" onclick="window.close()">
              Close Window
            </button>
          </div>
          <div class="pipeline-content">
            <div id="pipeline-app"></div>
          </div>
        </div>
      </body>
      </html>
    `);
    doc.close();

    // Wait for document to be ready, then render React app
    setTimeout(() => {
      this.renderPipelineApp(pipelineWindow, file, options);
    }, 100);
  }

  /**
   * Render React pipeline app in window
   * @param {Window} pipelineWindow - Window reference
   * @param {Object} file - File to process
   * @param {Object} options - Pipeline options
   */
  renderPipelineApp(pipelineWindow, file, options) {
    const container = pipelineWindow.document.getElementById('pipeline-app');
    if (!container) {
      console.error('Pipeline app container not found');
      return;
    }

    // Create React root and render pipeline
    const root = createRoot(container);
    
    const PipelineApp = () => (
      <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        <EnhancedPipelineVisualization
          file={file}
          isProcessing={options.isProcessing || false}
          onProcessingChange={options.onProcessingChange}
          onStepComplete={options.onStepComplete}
          onError={options.onError}
          autoRun={options.autoRun !== false}
          initialViewMode={VIEW_MODES.DETAILED} // Always use detailed view in separate window
          windowMode={true} // Flag to indicate window mode
        />
      </div>
    );

    root.render(<PipelineApp />);
  }

  /**
   * Close pipeline window for specific file
   * @param {string} fileId - File identifier
   */
  closePipelineWindow(fileId) {
    const pipelineWindow = this.windows.get(fileId);
    if (pipelineWindow && !pipelineWindow.closed) {
      pipelineWindow.close();
    }
    this.windows.delete(fileId);
  }

  /**
   * Close all pipeline windows
   */
  closeAllWindows() {
    for (const [fileId, pipelineWindow] of this.windows) {
      if (pipelineWindow && !pipelineWindow.closed) {
        pipelineWindow.close();
      }
    }
    this.windows.clear();
  }

  /**
   * Get window for specific file
   * @param {string} fileId - File identifier
   * @returns {Window|null} - Window reference or null
   */
  getWindow(fileId) {
    const pipelineWindow = this.windows.get(fileId);
    return (pipelineWindow && !pipelineWindow.closed) ? pipelineWindow : null;
  }

  /**
   * Check if window is open for file
   * @param {string} fileId - File identifier
   * @returns {boolean} - True if window is open
   */
  isWindowOpen(fileId) {
    return this.getWindow(fileId) !== null;
  }
}

// Global instance
export const pipelineWindowManager = new PipelineWindowManager();

/**
 * React Hook for Pipeline Window Management
 * @returns {Object} - Window management functions
 */
export function usePipelineWindow() {
  const [openWindows, setOpenWindows] = useState(new Set());

  const openWindow = (file, options = {}) => {
    const fileId = file?.name || `pipeline-${Date.now()}`;
    const pipelineWindow = pipelineWindowManager.openPipelineWindow(file, options);
    
    if (pipelineWindow) {
      setOpenWindows(prev => new Set([...prev, fileId]));
      
      // Listen for window close
      pipelineWindow.addEventListener('beforeunload', () => {
        setOpenWindows(prev => {
          const newSet = new Set(prev);
          newSet.delete(fileId);
          return newSet;
        });
      });
    }
    
    return pipelineWindow;
  };

  const closeWindow = (fileId) => {
    pipelineWindowManager.closePipelineWindow(fileId);
    setOpenWindows(prev => {
      const newSet = new Set(prev);
      newSet.delete(fileId);
      return newSet;
    });
  };

  const closeAllWindows = () => {
    pipelineWindowManager.closeAllWindows();
    setOpenWindows(new Set());
  };

  const isWindowOpen = (fileId) => {
    return openWindows.has(fileId) && pipelineWindowManager.isWindowOpen(fileId);
  };

  return {
    openWindow,
    closeWindow,
    closeAllWindows,
    isWindowOpen,
    openWindows: Array.from(openWindows)
  };
}

export default PipelineWindowManager;
