/**
 * Performance Monitor for Pipeline Window
 * Tracks and displays real-time performance metrics
 */

import React, { useState, useEffect, useRef } from 'react';
import { Activity, Clock, Cpu, HardDrive, Zap, TrendingUp, BarChart3 } from 'lucide-react';

/**
 * Performance Monitor Hook
 */
export const usePerformanceMonitor = () => {
  const [metrics, setMetrics] = useState({
    processingTime: 0,
    memoryUsage: 0,
    cpuUsage: 0,
    throughput: 0,
    stepTimes: {},
    totalSteps: 0,
    completedSteps: 0,
    errorCount: 0,
    startTime: null,
    lastUpdateTime: Date.now()
  });

  const intervalRef = useRef(null);
  const startTimeRef = useRef(null);

  // Start monitoring
  const startMonitoring = () => {
    startTimeRef.current = Date.now();
    setMetrics(prev => ({
      ...prev,
      startTime: startTimeRef.current,
      processingTime: 0
    }));

    intervalRef.current = setInterval(() => {
      updateMetrics();
    }, 1000);
  };

  // Stop monitoring
  const stopMonitoring = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  // Update metrics
  const updateMetrics = () => {
    const now = Date.now();
    const processingTime = startTimeRef.current ? (now - startTimeRef.current) / 1000 : 0;

    // Simulate memory and CPU usage (in real app, these would come from actual monitoring)
    const memoryUsage = performance.memory ? performance.memory.usedJSHeapSize : 0;
    const cpuUsage = Math.random() * 30 + 10; // Simulated CPU usage

    setMetrics(prev => ({
      ...prev,
      processingTime,
      memoryUsage,
      cpuUsage,
      throughput: prev.completedSteps / Math.max(processingTime, 1),
      lastUpdateTime: now
    }));
  };

  // Record step completion
  const recordStepCompletion = (stepId, duration) => {
    setMetrics(prev => ({
      ...prev,
      stepTimes: {
        ...prev.stepTimes,
        [stepId]: duration
      },
      completedSteps: prev.completedSteps + 1
    }));
  };

  // Record step error
  const recordStepError = (stepId) => {
    setMetrics(prev => ({
      ...prev,
      errorCount: prev.errorCount + 1
    }));
  };

  // Set total steps
  const setTotalSteps = (count) => {
    setMetrics(prev => ({
      ...prev,
      totalSteps: count
    }));
  };

  // Reset metrics
  const resetMetrics = () => {
    setMetrics({
      processingTime: 0,
      memoryUsage: 0,
      cpuUsage: 0,
      throughput: 0,
      stepTimes: {},
      totalSteps: 0,
      completedSteps: 0,
      errorCount: 0,
      startTime: null,
      lastUpdateTime: Date.now()
    });
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopMonitoring();
    };
  }, []);

  return {
    metrics,
    startMonitoring,
    stopMonitoring,
    recordStepCompletion,
    recordStepError,
    setTotalSteps,
    resetMetrics
  };
};

/**
 * Performance Dashboard Component
 */
export const PerformanceDashboard = ({ 
  metrics = {}, 
  isVisible = true,
  className = '' 
}) => {
  if (!isVisible) return null;

  const formatTime = (seconds) => {
    if (seconds < 60) return `${seconds.toFixed(1)}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds.toFixed(0)}s`;
  };

  const formatBytes = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${(bytes / Math.pow(k, i)).toFixed(1)} ${sizes[i]}`;
  };

  const getProgressPercentage = () => {
    if (metrics.totalSteps === 0) return 0;
    return (metrics.completedSteps / metrics.totalSteps) * 100;
  };

  const getEfficiencyScore = () => {
    const errorRate = metrics.totalSteps > 0 ? (metrics.errorCount / metrics.totalSteps) * 100 : 0;
    const completionRate = getProgressPercentage();
    return Math.max(0, completionRate - errorRate);
  };

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <BarChart3 size={20} className="text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900">Performance Monitor</h3>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
          <span className="text-sm text-gray-600">Live</span>
        </div>
      </div>

      {/* Metrics Grid */}
      <div className="p-4">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          {/* Processing Time */}
          <div className="bg-blue-50 rounded-lg p-3">
            <div className="flex items-center space-x-2 mb-1">
              <Clock size={16} className="text-blue-600" />
              <span className="text-sm font-medium text-blue-800">Processing Time</span>
            </div>
            <div className="text-xl font-bold text-blue-900">
              {formatTime(metrics.processingTime || 0)}
            </div>
          </div>

          {/* Memory Usage */}
          <div className="bg-green-50 rounded-lg p-3">
            <div className="flex items-center space-x-2 mb-1">
              <HardDrive size={16} className="text-green-600" />
              <span className="text-sm font-medium text-green-800">Memory Usage</span>
            </div>
            <div className="text-xl font-bold text-green-900">
              {formatBytes(metrics.memoryUsage || 0)}
            </div>
          </div>

          {/* CPU Usage */}
          <div className="bg-yellow-50 rounded-lg p-3">
            <div className="flex items-center space-x-2 mb-1">
              <Cpu size={16} className="text-yellow-600" />
              <span className="text-sm font-medium text-yellow-800">CPU Usage</span>
            </div>
            <div className="text-xl font-bold text-yellow-900">
              {(metrics.cpuUsage || 0).toFixed(1)}%
            </div>
          </div>

          {/* Throughput */}
          <div className="bg-purple-50 rounded-lg p-3">
            <div className="flex items-center space-x-2 mb-1">
              <Zap size={16} className="text-purple-600" />
              <span className="text-sm font-medium text-purple-800">Throughput</span>
            </div>
            <div className="text-xl font-bold text-purple-900">
              {(metrics.throughput || 0).toFixed(1)}/s
            </div>
          </div>
        </div>

        {/* Progress and Efficiency */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
          {/* Progress */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">Progress</span>
              <span className="text-sm text-gray-600">
                {metrics.completedSteps || 0} / {metrics.totalSteps || 0}
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${getProgressPercentage()}%` }}
              />
            </div>
            <div className="text-xs text-gray-500 mt-1">
              {getProgressPercentage().toFixed(1)}% complete
            </div>
          </div>

          {/* Efficiency Score */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">Efficiency</span>
              <span className="text-sm text-gray-600">
                {metrics.errorCount || 0} errors
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-300 ${
                  getEfficiencyScore() > 80 ? 'bg-green-500' :
                  getEfficiencyScore() > 60 ? 'bg-yellow-500' : 'bg-red-500'
                }`}
                style={{ width: `${getEfficiencyScore()}%` }}
              />
            </div>
            <div className="text-xs text-gray-500 mt-1">
              {getEfficiencyScore().toFixed(1)}% efficiency
            </div>
          </div>
        </div>

        {/* Step Times */}
        {Object.keys(metrics.stepTimes || {}).length > 0 && (
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-700 mb-3">Step Performance</h4>
            <div className="space-y-2">
              {Object.entries(metrics.stepTimes || {}).map(([stepId, duration]) => (
                <div key={stepId} className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">{stepId}:</span>
                  <span className="font-mono text-gray-900">{formatTime(duration)}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * Mini Performance Widget
 */
export const MiniPerformanceWidget = ({ 
  metrics = {}, 
  className = '' 
}) => {
  const formatTime = (seconds) => {
    if (seconds < 60) return `${seconds.toFixed(0)}s`;
    const minutes = Math.floor(seconds / 60);
    return `${minutes}m`;
  };

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-3 ${className}`}>
      <div className="flex items-center space-x-3">
        <Activity size={16} className="text-blue-600" />
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between text-xs">
            <span className="text-gray-600">Time:</span>
            <span className="font-mono">{formatTime(metrics.processingTime || 0)}</span>
          </div>
          <div className="flex items-center justify-between text-xs">
            <span className="text-gray-600">Steps:</span>
            <span className="font-mono">
              {metrics.completedSteps || 0}/{metrics.totalSteps || 0}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default usePerformanceMonitor;
