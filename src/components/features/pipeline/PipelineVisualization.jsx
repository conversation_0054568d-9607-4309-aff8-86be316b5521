/**
 * PipelineVisualization - Main component for displaying the multi-step pipeline
 * Shows visual pipeline steps with arrows, progress, and interactive controls
 */

import React, { useState, useEffect, useCallback } from 'react';
import PipelineStepCard from './PipelineStepCard.jsx';
import ConsoleLogger from './ConsoleLogger.jsx';
import { PIPELINE_STEPS, STEP_STATUS, getNextAvailableSteps } from '../../../core/config/pipelineSteps.js';
import { documentProcessingPipeline } from '../../../services/DocumentProcessingPipeline.js';

const PipelineVisualization = ({
  file = null,
  isProcessing = false,
  onProcessingChange,
  onStepComplete,
  onError,
  autoRun = false
}) => {
  const [pipelineState, setPipelineState] = useState({
    steps: {},
    currentStep: null,
    completedSteps: [],
    errors: {},
    results: {},
    timings: {},
    overallProgress: 0
  });

  const [selectedOutput, setSelectedOutput] = useState(null);
  const [consoleLogs, setConsoleLogs] = useState([]);

  // Add log entry to console
  const addLog = useCallback((level, message, stepName = null, data = null) => {
    const logEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      stepName,
      data
    };
    setConsoleLogs(prev => [...prev, logEntry]);
  }, []);

  // Clear console logs
  const clearLogs = useCallback(() => {
    setConsoleLogs([]);
  }, []);

  // Export console logs
  const exportLogs = useCallback(() => {
    const logData = JSON.stringify(consoleLogs, null, 2);
    const blob = new Blob([logData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `pipeline-logs-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }, [consoleLogs]);

  // Initialize pipeline state
  useEffect(() => {
    const initialSteps = {};
    PIPELINE_STEPS.forEach(step => {
      initialSteps[step.id] = {
        status: STEP_STATUS.PENDING,
        progress: 0,
        result: null,
        error: null,
        timing: 0
      };
    });

    setPipelineState(prev => ({
      ...prev,
      steps: initialSteps,
      currentStep: null,
      completedSteps: [],
      errors: {},
      results: {},
      timings: {},
      overallProgress: 0
    }));
  }, [file]);

  // Auto-run pipeline when file is provided
  useEffect(() => {
    if (file && autoRun && !isProcessing) {
      runFullPipeline();
    }
  }, [file, autoRun, isProcessing]);

  const updateStepState = useCallback((stepId, updates) => {
    setPipelineState(prev => ({
      ...prev,
      steps: {
        ...prev.steps,
        [stepId]: {
          ...prev.steps[stepId],
          ...updates
        }
      }
    }));
  }, []);

  const calculateOverallProgress = (completedSteps, currentStep, currentProgress) => {
    const totalSteps = PIPELINE_STEPS.length;
    const completedCount = completedSteps.length;
    const currentStepProgress = currentStep ? (currentProgress / 100) : 0;
    return Math.round(((completedCount + currentStepProgress) / totalSteps) * 100);
  };

  const runFullPipeline = async () => {
    if (!file || isProcessing) return;

    try {
      addLog('info', '🚀 Starting full pipeline execution', null, { fileName: file.name });
      onProcessingChange?.(true);

      // Get API key from environment variables
      const apiKey = window.__MVAT_ENV__?.DEEPSEEK_API_KEY || process.env.DEEPSEEK_API_KEY;
      if (!apiKey) {
        addLog('warning', '⚠️ No DeepSeek API key found in environment variables');
      } else {
        addLog('info', '✅ DeepSeek API key loaded from environment');
      }

      const progressCallback = (stepId, progress, message) => {
        updateStepState(stepId, {
          status: STEP_STATUS.RUNNING,
          progress: progress
        });

        setPipelineState(prev => ({
          ...prev,
          currentStep: stepId,
          overallProgress: calculateOverallProgress(prev.completedSteps, stepId, progress)
        }));

        if (message) {
          addLog('info', message, stepId, { progress });
        }
      };

      const result = await documentProcessingPipeline.processDocument(file, {
        progressCallback,
        apiKey,
        language: 'pol',
        companyInfo: {
          name: window.__MVAT_ENV__?.COMPANY_NAME || 'MVAT Solutions'
        }
      });

      if (result.success) {
        addLog('success', '✅ Pipeline execution completed successfully');

        // Update all steps as completed
        const finalSteps = { ...pipelineState.steps };
        const completedSteps = [];

        PIPELINE_STEPS.forEach(step => {
          const stepResult = result.stepResults?.[step.id];
          const hasError = stepResult?.error ||
                          stepResult?.success === false ||
                          stepResult?.metadata?.error ||
                          stepResult?.metadata?.extractionMethod === 'failed' ||
                          (stepResult?.confidence === 0 && stepResult?.success !== true);

          finalSteps[step.id] = {
            status: hasError ? STEP_STATUS.ERROR : STEP_STATUS.COMPLETED,
            progress: 100,
            result: stepResult || null,
            error: hasError ? (stepResult?.error || stepResult?.metadata?.error || 'Step failed') : null,
            timing: result.stepTimings?.[step.id] || 0
          };

          if (!hasError) {
            completedSteps.push(step.id);
            addLog('success', `✅ Step completed: ${step.name}`, step.id, {
              timing: result.stepTimings?.[step.id],
              confidence: stepResult?.confidence
            });
          } else {
            addLog('error', `❌ Step failed: ${step.name}`, step.id, {
              error: stepResult?.error || stepResult?.metadata?.error
            });
          }
        });

        setPipelineState(prev => ({
          ...prev,
          steps: finalSteps,
          completedSteps,
          currentStep: null,
          overallProgress: 100,
          results: result.stepResults || {},
          timings: result.stepTimings || {}
        }));

        onStepComplete?.(result);
      } else {
        addLog('error', `❌ Pipeline execution failed: ${result.error || 'Unknown error'}`);
        throw new Error(result.error || 'Pipeline processing failed');
      }
    } catch (error) {
      console.error('Pipeline execution error:', error);
      addLog('error', `❌ Pipeline execution error: ${error.message}`, null, { error: error.stack });

      // Mark current step as error
      if (pipelineState.currentStep) {
        updateStepState(pipelineState.currentStep, {
          status: STEP_STATUS.ERROR,
          error: error.message
        });
      }

      onError?.(error.message);
    } finally {
      onProcessingChange?.(false);
    }
  };

  const runSingleStep = async (stepId) => {
    if (!file || isProcessing) return;

    try {
      addLog('info', `🔄 Running single step: ${stepId}`, stepId);
      onProcessingChange?.(true);
      updateStepState(stepId, {
        status: STEP_STATUS.RUNNING,
        progress: 0
      });

      const progressCallback = (progress) => {
        updateStepState(stepId, {
          progress: progress
        });
      };

      // Get API key for steps that need it
      const apiKey = window.__MVAT_ENV__?.DEEPSEEK_API_KEY || process.env.DEEPSEEK_API_KEY;

      let result;
      switch (stepId) {
        case 'pdf_extraction':
          result = await documentProcessingPipeline.runPdfExtraction(file, { progressCallback });
          break;
        case 'deepseek_analysis':
          if (!apiKey) {
            throw new Error('DeepSeek API key required for analysis');
          }
          result = await documentProcessingPipeline.runDeepSeekAnalysis(file, {
            progressCallback,
            apiKey,
            language: 'pol',
            companyInfo: {
              name: window.__MVAT_ENV__?.COMPANY_NAME || 'MVAT Solutions'
            }
          });
          break;
        case 'rag_enhancement':
          result = await documentProcessingPipeline.runRAGEnhancement(file, { progressCallback });
          break;
        case 'tesseract_reference':
          result = await documentProcessingPipeline.runTesseractReference(file, { progressCallback });
          break;
        case 'field_mapping':
          result = await documentProcessingPipeline.runFieldMapping(file, { progressCallback });
          break;
        case 'data_validation':
          result = await documentProcessingPipeline.runDataValidation(file, { progressCallback });
          break;
        case 'final_output':
          result = await documentProcessingPipeline.runFinalOutput(file, { progressCallback });
          break;
        default:
          throw new Error(`Unknown step: ${stepId}`);
      }

      // Check if step actually succeeded
      const hasError = result.data?.error ||
                      result.data?.success === false ||
                      result.data?.metadata?.error ||
                      result.data?.metadata?.extractionMethod === 'failed' ||
                      (result.data?.confidence === 0 && result.data?.success !== true);

      if (result.success && !hasError) {
        updateStepState(stepId, {
          status: STEP_STATUS.COMPLETED,
          progress: 100,
          result: result.data,
          timing: result.timing || 0
        });

        setPipelineState(prev => ({
          ...prev,
          completedSteps: [...prev.completedSteps.filter(id => id !== stepId), stepId],
          results: {
            ...prev.results,
            [stepId]: result.data
          },
          timings: {
            ...prev.timings,
            [stepId]: result.timing || 0
          }
        }));

        onStepComplete?.({ stepId, result: result.data });
      } else {
        // Step failed
        const errorMessage = result.data?.error ||
                           result.data?.metadata?.error ||
                           result.error ||
                           'Step execution failed';

        updateStepState(stepId, {
          status: STEP_STATUS.ERROR,
          progress: 100,
          result: result.data,
          error: errorMessage,
          timing: result.timing || 0
        });

        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error(`Step ${stepId} execution error:`, error);
      updateStepState(stepId, {
        status: STEP_STATUS.ERROR,
        error: error.message
      });
      onError?.(error.message);
    } finally {
      onProcessingChange?.(false);
    }
  };

  const handleStepAction = (stepId, actionType) => {
    switch (actionType) {
      case 'rerun':
        runSingleStep(stepId);
        break;
      case 'view_raw':
      case 'view_output':
      case 'enhance_prompt':
      case 'view_similar':
      case 'compare_pdf':
      case 'view_mapping':
      case 'view_errors':
        setSelectedOutput({ stepId, actionType, data: pipelineState.results[stepId] });
        break;
      case 'export':
      case 'save':
        // Handle export/save actions
        console.log(`${actionType} action for step ${stepId}`);
        break;
      default:
        console.warn(`Unknown action: ${actionType}`);
    }
  };

  const renderPipelineArrow = (index) => {
    if (index === PIPELINE_STEPS.length - 1) return null;

    return (
      <div className="flex justify-center my-3">
        <div className="flex flex-col items-center">
          <div className="h-4 w-0.5 bg-gray-300"></div>
          <div className="w-0 h-0 border-t-4 border-t-gray-400 border-l-2 border-l-transparent border-r-2 border-r-transparent"></div>
        </div>
      </div>
    );
  };

  // Remove modal - data will be displayed in fold/unfold sections within step cards

  return (
    <div className="w-full h-full">
      {/* Pipeline Header */}
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-lg font-semibold text-gray-900">
            Multi-Step Processing Pipeline
          </h3>
          <div className="flex items-center space-x-2">
            {file && (
              <button
                onClick={runFullPipeline}
                disabled={isProcessing}
                className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:bg-gray-400"
              >
                {isProcessing ? '⏳ Processing...' : '🚀 Run Pipeline'}
              </button>
            )}
          </div>
        </div>

        {/* Overall Progress */}
        {pipelineState.overallProgress > 0 && (
          <div className="mb-4">
            <div className="flex justify-between text-sm text-gray-600 mb-1">
              <span>Overall Progress</span>
              <span>{pipelineState.overallProgress}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${pipelineState.overallProgress}%` }}
              />
            </div>
          </div>
        )}
      </div>

      {/* Side-by-Side Layout: Pipeline Steps (Left) + Console Logs (Right) */}
      <div className="flex gap-4 h-full">
        {/* Left Side: Pipeline Steps */}
        <div className="flex-1 min-w-0">
          <div className="space-y-2">
            {PIPELINE_STEPS.map((step, index) => (
              <div key={step.id}>
                <PipelineStepCard
                  step={step}
                  status={pipelineState.steps[step.id]?.status}
                  progress={pipelineState.steps[step.id]?.progress}
                  result={pipelineState.steps[step.id]?.result}
                  error={pipelineState.steps[step.id]?.error}
                  timing={pipelineState.steps[step.id]?.timing}
                  onAction={handleStepAction}
                  isActive={pipelineState.currentStep === step.id}
                  showExpandableData={true}
                />
                {renderPipelineArrow(index)}
              </div>
            ))}
          </div>
        </div>

        {/* Right Side: Console Logs */}
        <div className="w-96 min-w-96">
          <ConsoleLogger
            logs={consoleLogs}
            isProcessing={isProcessing}
            onClearLogs={clearLogs}
            onExportLogs={exportLogs}
            className="h-full"
          />
        </div>
      </div>
    </div>
  );
};

export default PipelineVisualization;
