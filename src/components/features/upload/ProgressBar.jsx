import React from 'react';

/**
 * Reusable Progress Bar Component
 * Provides consistent progress visualization across the application
 *
 * Features:
 * - Multiple visual variants
 * - Configurable sizes
 * - Smooth animations
 * - Accessibility compliance
 * - Percentage display option
 * - Gradient support
 */
function ProgressBar({
  progress = 0,
  variant = 'default',
  size = 'md',
  showPercentage = false,
  animated = true,
  striped = false,
  className = '',
  label = '',
  ...props
}) {
  // Ensure progress is within valid range
  const normalizedProgress = Math.min(Math.max(progress, 0), 100);

  // Variant configurations
  const variants = {
    default: {
      bg: 'bg-gray-200',
      fill: 'bg-blue-600',
      text: 'text-blue-600'
    },
    active: {
      bg: 'bg-blue-100',
      fill: 'bg-blue-600',
      text: 'text-blue-600'
    },
    success: {
      bg: 'bg-green-100',
      fill: 'bg-green-600',
      text: 'text-green-600'
    },
    warning: {
      bg: 'bg-yellow-100',
      fill: 'bg-yellow-500',
      text: 'text-yellow-600'
    },
    error: {
      bg: 'bg-red-100',
      fill: 'bg-red-600',
      text: 'text-red-600'
    },
    info: {
      bg: 'bg-blue-100',
      fill: 'bg-blue-500',
      text: 'text-blue-600'
    },
    gradient: {
      bg: 'bg-gray-200',
      fill: 'bg-gradient-to-r from-blue-500 to-purple-600',
      text: 'text-blue-600'
    }
  };

  // Size configurations
  const sizes = {
    xs: 'h-1',
    sm: 'h-2',
    md: 'h-3',
    lg: 'h-4',
    xl: 'h-6'
  };

  const currentVariant = variants[variant] || variants.default;
  const currentSize = sizes[size] || sizes.md;

  // Animation classes
  const animationClasses = animated ? 'transition-all duration-300 ease-out' : '';

  // Striped pattern classes
  const stripedClasses = striped ? 'bg-stripes' : '';

  // Progress bar fill styles
  const fillStyle = {
    width: `${normalizedProgress}%`,
    transform: normalizedProgress === 0 ? 'translateX(-100%)' : 'translateX(0)'
  };

  // Generate unique ID for accessibility
  const progressId = `progress-${Math.random().toString(36).substr(2, 9)}`;

  return (
    <div className={`w-full ${className}`} {...props}>
      {/* Label and percentage */}
      {(label || showPercentage) && (
        <div className="flex items-center justify-between mb-1">
          {label && (
            <span className="text-sm font-medium text-gray-700" id={`${progressId}-label`}>
              {label}
            </span>
          )}
          {showPercentage && (
            <span className={`text-sm font-medium ${currentVariant.text}`}>
              {Math.round(normalizedProgress)}%
            </span>
          )}
        </div>
      )}

      {/* Progress bar container */}
      <div
        className={`w-full ${currentVariant.bg} rounded-full ${currentSize} overflow-hidden relative`}
        role="progressbar"
        aria-valuenow={normalizedProgress}
        aria-valuemin="0"
        aria-valuemax="100"
        aria-labelledby={label ? `${progressId}-label` : undefined}
        aria-label={!label ? `Progress: ${Math.round(normalizedProgress)}%` : undefined}
      >
        {/* Progress bar fill */}
        <div
          className={`${currentSize} rounded-full ${currentVariant.fill} ${animationClasses} ${stripedClasses} relative overflow-hidden`}
          style={fillStyle}
        >
          {/* Animated shine effect for active progress */}
          {animated && variant === 'active' && normalizedProgress > 0 && normalizedProgress < 100 && (
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-pulse" />
          )}

          {/* Striped pattern overlay */}
          {striped && (
            <div className="absolute inset-0 bg-stripes-pattern opacity-20" />
          )}
        </div>

        {/* Indeterminate animation for unknown progress */}
        {normalizedProgress === 0 && animated && variant === 'active' && (
          <div className="absolute inset-0">
            <div className="h-full w-1/3 bg-blue-600 opacity-60 animate-pulse rounded-full" />
          </div>
        )}
      </div>

      {/* Additional status text */}
      {variant === 'error' && normalizedProgress === 0 && (
        <div className="mt-1">
          <span className="text-xs text-red-600">Processing failed</span>
        </div>
      )}

      {variant === 'success' && normalizedProgress === 100 && (
        <div className="mt-1">
          <span className="text-xs text-green-600">Completed successfully</span>
        </div>
      )}

      {/* Accessibility: Screen reader announcements */}
      <div className="sr-only" aria-live="polite" aria-atomic="true">
        {normalizedProgress === 0 && variant === 'active' && 'Processing started'}
        {normalizedProgress > 0 && normalizedProgress < 100 && `${Math.round(normalizedProgress)}% complete`}
        {normalizedProgress === 100 && 'Processing completed'}
        {variant === 'error' && 'Processing failed'}
      </div>
    </div>
  );
}

// CSS for striped pattern (to be added to global styles)
const stripedPatternCSS = `
.bg-stripes {
  background-image: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.15) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.15) 75%,
    transparent 75%,
    transparent
  );
  background-size: 1rem 1rem;
}

.bg-stripes-pattern {
  background-image: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 4px,
    rgba(255, 255, 255, 0.3) 4px,
    rgba(255, 255, 255, 0.3) 8px
  );
}

@keyframes progress-shine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-progress-shine {
  animation: progress-shine 2s infinite;
}
`;

export default ProgressBar;
export { stripedPatternCSS };
