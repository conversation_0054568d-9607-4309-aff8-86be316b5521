/**
 * DocumentUpload<PERSON><PERSON><PERSON> - <PERSON>les document upload and processing
 * Extracted from popup.js file upload logic
 * Manages drag-and-drop, file validation, and processing workflow
 */

import { DocumentProcessor } from '../processors/DocumentProcessor.js';
import { StorageAPI } from '../../api/StorageAPI.js';

export class DocumentUploadHandler {
  constructor() {
    this.documentProcessor = new DocumentProcessor();
    this.storageAPI = new StorageAPI();
    this.dropZone = null;
    this.fileInput = null;
    this.progressCallback = null;
    this.completionCallback = null;
    this.maxFileSize = 50 * 1024 * 1024; // 50MB
    this.supportedTypes = [
      'application/pdf',
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/bmp',
      'image/tiff',
      'text/plain'
    ];
  }

  /**
   * Initialize the upload handler
   * @param {Object} options - Initialization options
   */
  initialize(options = {}) {
    const {
      dropZoneSelector = '#dropZone',
      fileInputSelector = '#fileInput',
      progressCallback = null,
      completionCallback = null
    } = options;

    this.progressCallback = progressCallback;
    this.completionCallback = completionCallback;

    // Get DOM elements
    this.dropZone = document.querySelector(dropZoneSelector);
    this.fileInput = document.querySelector(fileInputSelector);

    if (this.dropZone) {
      this.setupDropZone();
    }

    if (this.fileInput) {
      this.setupFileInput();
    }

    console.log('DocumentUploadHandler initialized');
  }

  /**
   * Set up drag-and-drop functionality
   */
  setupDropZone() {
    // Prevent default drag behaviors
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
      this.dropZone.addEventListener(eventName, this.preventDefaults, false);
      document.body.addEventListener(eventName, this.preventDefaults, false);
    });

    // Highlight drop zone when item is dragged over it
    ['dragenter', 'dragover'].forEach(eventName => {
      this.dropZone.addEventListener(eventName, () => this.highlight(), false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
      this.dropZone.addEventListener(eventName, () => this.unhighlight(), false);
    });

    // Handle dropped files
    this.dropZone.addEventListener('drop', (e) => this.handleDrop(e), false);

    // Handle click to open file dialog
    this.dropZone.addEventListener('click', () => {
      if (this.fileInput) {
        this.fileInput.click();
      }
    });
  }

  /**
   * Set up file input functionality
   */
  setupFileInput() {
    this.fileInput.addEventListener('change', (e) => {
      this.handleFiles(e.target.files);
    });
  }

  /**
   * Prevent default drag behaviors
   * @param {Event} e - Event object
   */
  preventDefaults(e) {
    e.preventDefault();
    e.stopPropagation();
  }

  /**
   * Highlight drop zone
   */
  highlight() {
    this.dropZone.classList.add('drag-over', 'border-blue-500', 'bg-blue-50');
  }

  /**
   * Remove highlight from drop zone
   */
  unhighlight() {
    this.dropZone.classList.remove('drag-over', 'border-blue-500', 'bg-blue-50');
  }

  /**
   * Handle drop event
   * @param {Event} e - Drop event
   */
  handleDrop(e) {
    const dt = e.dataTransfer;
    const files = dt.files;
    this.handleFiles(files);
  }

  /**
   * Handle file selection
   * @param {FileList} files - Selected files
   */
  async handleFiles(files) {
    if (!files || files.length === 0) {
      return;
    }

    const fileArray = Array.from(files);

    // Validate files
    const validationResults = fileArray.map(file => this.validateFile(file));
    const validFiles = fileArray.filter((file, index) => validationResults[index].valid);
    const invalidFiles = fileArray.filter((file, index) => !validationResults[index].valid);

    // Show validation errors
    if (invalidFiles.length > 0) {
      this.showValidationErrors(invalidFiles, validationResults);
    }

    if (validFiles.length === 0) {
      return;
    }

    // Process valid files
    await this.processFiles(validFiles);
  }

  /**
   * Validate a file
   * @param {File} file - File to validate
   * @returns {Object} - Validation result
   */
  validateFile(file) {
    const errors = [];

    // Check file size
    if (file.size > this.maxFileSize) {
      errors.push(`File size (${this.formatFileSize(file.size)}) exceeds limit (${this.formatFileSize(this.maxFileSize)})`);
    }

    // Check file type
    if (!this.supportedTypes.includes(file.type)) {
      errors.push(`File type "${file.type}" is not supported`);
    }

    // Check file name
    if (!file.name || file.name.trim() === '') {
      errors.push('File has no name');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Process multiple files
   * @param {Array<File>} files - Files to process
   */
  async processFiles(files) {
    try {
      // Get API key and settings
      const settings = await this.storageAPI.getSettings();
      const apiKey = settings.app.deepseekApiKey;

      if (!apiKey) {
        this.showError('DeepSeek API key is not configured. Please check your settings.');
        return;
      }

      // Show progress
      this.showProgress('Starting document processing...', 0);

      const results = [];

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const progress = Math.round((i / files.length) * 100);

        this.showProgress(`Processing ${file.name}...`, progress);

        try {
          const result = await this.documentProcessor.processDocument(file, {
            apiKey,
            preferredLanguage: settings.app.preferredLanguage || 'pol',
            companyInfo: settings.accounting.company,
            enableOCR: true,
            enableRAG: true,
            validateResults: true
          });

          results.push({
            file: file.name,
            success: result.success,
            data: result.data,
            error: result.error
          });

          if (result.success) {
            this.showProgress(`✅ ${file.name} processed successfully`, progress);
          } else {
            this.showProgress(`❌ ${file.name} failed: ${result.error}`, progress);
          }

        } catch (error) {
          console.error(`Error processing ${file.name}:`, error);
          results.push({
            file: file.name,
            success: false,
            error: error.message
          });
        }
      }

      // Show completion
      this.showProgress('Processing complete!', 100);

      // Call completion callback
      if (this.completionCallback) {
        this.completionCallback(results);
      }

      // Show summary
      this.showProcessingSummary(results);

    } catch (error) {
      console.error('Error processing files:', error);
      this.showError(`Processing failed: ${error.message}`);
    }
  }

  /**
   * Show processing progress
   * @param {string} message - Progress message
   * @param {number} percentage - Progress percentage
   */
  showProgress(message, percentage) {
    if (this.progressCallback) {
      this.progressCallback(message, percentage);
    }

    // Update UI elements if they exist (browser environment only)
    if (typeof document !== 'undefined') {
      const progressBar = document.getElementById('progressBar');
      const progressText = document.getElementById('progressText');

      if (progressBar) {
        progressBar.style.width = `${percentage}%`;
      }

      if (progressText) {
        progressText.textContent = message;
      }
    }

    console.log(`Progress: ${percentage}% - ${message}`);
  }

  /**
   * Show validation errors
   * @param {Array<File>} invalidFiles - Invalid files
   * @param {Array<Object>} validationResults - Validation results
   */
  showValidationErrors(invalidFiles, validationResults) {
    const errorMessages = invalidFiles.map((file, index) => {
      const fileIndex = validationResults.findIndex(result => !result.valid);
      const errors = validationResults[fileIndex].errors;
      return `${file.name}: ${errors.join(', ')}`;
    });

    this.showError(`File validation errors:\n${errorMessages.join('\n')}`);
  }

  /**
   * Show processing summary
   * @param {Array<Object>} results - Processing results
   */
  showProcessingSummary(results) {
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;

    let message = `Processing complete: ${successful} successful`;
    if (failed > 0) {
      message += `, ${failed} failed`;
    }

    this.showSuccess(message);
  }

  /**
   * Show error message
   * @param {string} message - Error message
   */
  showError(message) {
    // Try to use global notification system
    if (typeof window.showError === 'function') {
      window.showError(message);
    } else {
      alert(`Error: ${message}`);
    }
  }

  /**
   * Show success message
   * @param {string} message - Success message
   */
  showSuccess(message) {
    // Try to use global notification system
    if (typeof window.showSuccess === 'function') {
      window.showSuccess(message);
    } else {
      console.log(`Success: ${message}`);
    }
  }

  /**
   * Format file size for display
   * @param {number} bytes - File size in bytes
   * @returns {string} - Formatted file size
   */
  formatFileSize(bytes) {
    if (bytes === 0) { return '0 Bytes'; }

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Set progress callback
   * @param {Function} callback - Progress callback function
   */
  setProgressCallback(callback) {
    this.progressCallback = callback;
  }

  /**
   * Set completion callback
   * @param {Function} callback - Completion callback function
   */
  setCompletionCallback(callback) {
    this.completionCallback = callback;
  }

  /**
   * Reset the upload handler
   */
  reset() {
    if (this.fileInput) {
      this.fileInput.value = '';
    }

    this.unhighlight();

    // Reset progress
    this.showProgress('Ready for upload', 0);
  }
}

// Create singleton instance
const documentUploadHandler = new DocumentUploadHandler();

// Export for ES modules
export default documentUploadHandler;

// Export to window object for non-module scripts
if (typeof window !== 'undefined') {
  window.DocumentUploadHandler = DocumentUploadHandler;
  window.documentUploadHandler = documentUploadHandler;
}

// Local test function
function testDocumentUploadHandler() {
  console.log('=== DocumentUploadHandler Local Test ===');

  try {
    const handler = new DocumentUploadHandler();

    // Test 1: Initialization
    console.log('Test 1: Initialization');
    console.log('✓ DocumentUploadHandler instance created');

    // Test 2: File validation
    console.log('\nTest 2: File validation');
    const validFile = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
    const validResult = handler.validateFile(validFile);
    console.log('✓ Valid file validation:', validResult.valid);

    const invalidFile = new File(['test'], 'test.xyz', { type: 'application/unknown' });
    const invalidResult = handler.validateFile(invalidFile);
    console.log('✓ Invalid file validation:', invalidResult.valid, invalidResult.errors);

    // Test 3: File size formatting
    console.log('\nTest 3: File size formatting');
    const formattedSize = handler.formatFileSize(1024 * 1024 * 2.5); // 2.5 MB
    console.log('✓ File size formatting:', formattedSize);

    // Test 4: Supported types check
    console.log('\nTest 4: Supported types');
    console.log('✓ Supported types:', handler.supportedTypes.length, 'types');
    console.log('✓ PDF supported:', handler.supportedTypes.includes('application/pdf'));

    console.log('\n✅ All DocumentUploadHandler tests passed!');
    return true;

  } catch (error) {
    console.error('❌ DocumentUploadHandler test failed:', error);
    return false;
  }
}

// Run test if in browser environment
if (typeof window !== 'undefined' && window.location) {
  // Uncomment to run test
  // testDocumentUploadHandler();
}

// ===== LOCAL TESTING (Node.js equivalent of Python's if __name__ == '__main__') =====
if (typeof process !== 'undefined' && process.argv && process.argv[1] && process.argv[1].includes('DocumentUploadHandler.js')) {
  console.log('🧪 Running local tests for DocumentUploadHandler...');

  // Mock File constructor for Node.js
  if (typeof File === 'undefined') {
    global.File = class File {
      constructor(content, name, options = {}) {
        this.content = content;
        this.name = name;
        this.type = options.type || 'text/plain';
        this.size = Array.isArray(content) ? content.join('').length : content.length;
      }

      async text() {
        return Array.isArray(this.content) ? this.content.join('') : this.content;
      }
    };
  }

  try {
    const handler = new DocumentUploadHandler();

    console.log('✅ Test 1: DocumentUploadHandler initialization');
    console.log('📊 Handler created:', !!handler);
    console.log('📊 Document processor available:', !!handler.documentProcessor);
    console.log('📊 Storage API available:', !!handler.storageAPI);
    console.log('📊 Max file size:', handler.formatFileSize(handler.maxFileSize));
    console.log('📊 Supported types:', handler.supportedTypes.length);

    console.log('\n✅ Test 2: File validation - valid files');
    const validFiles = [
      new File(['PDF content'], 'invoice.pdf', { type: 'application/pdf' }),
      new File(['Image content'], 'scan.jpg', { type: 'image/jpeg' }),
      new File(['Text content'], 'document.txt', { type: 'text/plain' })
    ];

    validFiles.forEach((file, index) => {
      const result = handler.validateFile(file);
      console.log(`📊 File ${index + 1} (${file.type}): ${result.valid ? '✅' : '❌'}`);
      if (!result.valid) {
        console.log(`   Errors: ${result.errors.join(', ')}`);
      }
    });

    console.log('\n✅ Test 3: File validation - invalid files');
    const invalidFiles = [
      new File(['content'], 'document.xyz', { type: 'application/unknown' }),
      new File([''], '', { type: 'application/pdf' }),
      { name: 'large.pdf', type: 'application/pdf', size: 60 * 1024 * 1024 } // 60MB
    ];

    invalidFiles.forEach((file, index) => {
      const result = handler.validateFile(file);
      console.log(`📊 Invalid file ${index + 1}: ${result.valid ? '✅' : '❌'}`);
      if (!result.valid) {
        console.log(`   Errors: ${result.errors.join(', ')}`);
      }
    });

    console.log('\n✅ Test 4: File size formatting');
    const testSizes = [0, 512, 1024, 1024 * 1024, 1024 * 1024 * 2.5, 1024 * 1024 * 1024];
    testSizes.forEach(size => {
      const formatted = handler.formatFileSize(size);
      console.log(`📊 ${size} bytes = ${formatted}`);
    });

    console.log('\n✅ Test 5: Supported file types validation');
    const testTypes = [
      'application/pdf',
      'image/jpeg',
      'image/png',
      'text/plain',
      'application/unknown',
      'video/mp4'
    ];

    testTypes.forEach(type => {
      const supported = handler.supportedTypes.includes(type);
      console.log(`📊 ${type}: ${supported ? '✅' : '❌'}`);
    });

    console.log('\n✅ Test 6: Callback management');
    let progressCalled = false;
    let completionCalled = false;

    handler.setProgressCallback((message, percentage) => {
      progressCalled = true;
      console.log(`📊 Progress callback: ${percentage}% - ${message}`);
    });

    handler.setCompletionCallback((results) => {
      completionCalled = true;
      console.log(`📊 Completion callback: ${results.length} results`);
    });

    // Test progress callback
    handler.showProgress('Test progress', 50);
    console.log(`📊 Progress callback called: ${progressCalled ? '✅' : '❌'}`);

    console.log('\n✅ Test 7: Error handling');
    const emptyFileList = [];
    console.log('📊 Empty file list handling: ✅');

    const nullFile = null;
    try {
      handler.validateFile(nullFile);
      console.log('📊 Null file handling: ❌ (should have thrown error)');
    } catch (error) {
      console.log('📊 Null file handling: ✅ (correctly threw error)');
    }

    console.log('\n🎉 All tests completed for DocumentUploadHandler');
    console.log('📋 Note: Full functionality requires browser environment with DOM elements');

  } catch (error) {
    console.error('❌ DocumentUploadHandler test failed:', error);
  }
}
