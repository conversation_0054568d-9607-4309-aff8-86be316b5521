import React from 'react';
import prettyBytes from 'pretty-bytes';
import ProgressBar from './ProgressBar.jsx';

/**
 * Individual File Progress Item Component
 * Displays detailed progress for a single file
 *
 * Features:
 * - File-specific progress tracking
 * - Stage-specific status indicators
 * - Expandable details view
 * - Error handling and retry
 * - Accessibility compliance
 * - Performance metrics display
 */
function FileProgressItem({
  file,
  isExpanded = false,
  onToggleExpansion,
  onRetry,
  className = ''
}) {
  if (!file) { return null; }

  const {
    id,
    name,
    size,
    progress = 0,
    status = 'pending',
    stage = 'uploading',
    error = null,
    startTime,
    endTime,
    estimatedTime,
    validationResult,
    securityResult
  } = file;

  // Status configurations
  const statusConfig = {
    pending: {
      icon: '⏳',
      title: 'Pending',
      description: 'Waiting to start...',
      color: 'gray',
      bgColor: 'bg-gray-50',
      textColor: 'text-gray-600'
    },
    uploading: {
      icon: '📤',
      title: 'Uploading',
      description: 'Transferring file...',
      color: 'blue',
      bgColor: 'bg-blue-50',
      textColor: 'text-blue-600'
    },
    validating: {
      icon: '🔍',
      title: 'Validating',
      description: 'Checking file format and security...',
      color: 'yellow',
      bgColor: 'bg-yellow-50',
      textColor: 'text-yellow-600'
    },
    processing: {
      icon: '⚙️',
      title: 'Processing',
      description: 'Preparing for analysis...',
      color: 'indigo',
      bgColor: 'bg-indigo-50',
      textColor: 'text-indigo-600'
    },
    complete: {
      icon: '✅',
      title: 'Complete',
      description: 'Successfully processed',
      color: 'green',
      bgColor: 'bg-green-50',
      textColor: 'text-green-600'
    },
    error: {
      icon: '❌',
      title: 'Failed',
      description: error || 'Processing failed',
      color: 'red',
      bgColor: 'bg-red-50',
      textColor: 'text-red-600'
    }
  };

  const currentStatus = statusConfig[status] || statusConfig.pending;
  const progressPercentage = Math.min(Math.max(progress, 0), 100);

  // Calculate processing time
  const getProcessingTime = () => {
    if (!startTime) { return null; }
    const end = endTime || Date.now();
    const duration = (end - startTime) / 1000;

    if (duration < 60) {
      return `${Math.round(duration)}s`;
    }
    const minutes = Math.floor(duration / 60);
    const seconds = Math.round(duration % 60);
    return `${minutes}m ${seconds}s`;
  };

  // Format estimated time remaining
  const formatEstimatedTime = (seconds) => {
    if (!seconds || seconds <= 0) { return ''; }

    if (seconds < 60) {
      return `${Math.round(seconds)}s remaining`;
    }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return `${minutes}m ${remainingSeconds}s remaining`;
  };

  return (
    <div className={`bg-white border border-gray-200 rounded-lg overflow-hidden ${className}`}>
      {/* Main file info - always visible */}
      <div className="p-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 flex-1 min-w-0">
            {/* Status icon */}
            <div className="flex-shrink-0">
              {status === 'uploading' || status === 'validating' || status === 'processing' ? (
                <div className="relative">
                  <div className={`w-6 h-6 rounded-full border-2 border-current ${currentStatus.textColor} animate-spin border-t-transparent`} />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-xs" role="img" aria-label={currentStatus.title}>
                      {currentStatus.icon}
                    </span>
                  </div>
                </div>
              ) : (
                <span className={`text-lg ${currentStatus.textColor}`} role="img" aria-label={currentStatus.title}>
                  {currentStatus.icon}
                </span>
              )}
            </div>

            {/* File info */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <h5 className="text-sm font-medium text-gray-900 truncate" title={name}>
                  {name}
                </h5>
                <div className="flex items-center space-x-2 text-xs text-gray-500">
                  {size && <span>{prettyBytes(size)}</span>}
                  {progressPercentage > 0 && status !== 'complete' && (
                    <span className="font-medium">{progressPercentage}%</span>
                  )}
                </div>
              </div>

              <div className="flex items-center justify-between mt-1">
                <p className={`text-xs ${currentStatus.textColor}`}>
                  {currentStatus.description}
                </p>
                <div className="flex items-center space-x-2 text-xs text-gray-500">
                  {getProcessingTime() && (
                    <span>{getProcessingTime()}</span>
                  )}
                  {estimatedTime && status !== 'complete' && status !== 'error' && (
                    <span>{formatEstimatedTime(estimatedTime)}</span>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex items-center space-x-2 ml-3">
            {status === 'error' && onRetry && (
              <button
                onClick={onRetry}
                className="px-2 py-1 text-xs font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                aria-label={`Retry ${name}`}
              >
                Retry
              </button>
            )}

            {onToggleExpansion && (validationResult || securityResult || error) && (
              <button
                onClick={onToggleExpansion}
                className="p-1 text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 rounded"
                aria-label={`${isExpanded ? 'Hide' : 'Show'} details for ${name}`}
              >
                <svg
                  className={`w-4 h-4 transform transition-transform ${isExpanded ? 'rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
            )}
          </div>
        </div>

        {/* Progress bar */}
        {status !== 'pending' && status !== 'complete' && (
          <div className="mt-2">
            <ProgressBar
              progress={progressPercentage}
              variant={status === 'error' ? 'error' : 'active'}
              size="sm"
              showPercentage={false}
            />
          </div>
        )}
      </div>

      {/* Expanded details */}
      {isExpanded && (
        <div className={`border-t border-gray-200 p-3 ${currentStatus.bgColor}`}>
          <div className="space-y-3">
            {/* Error details */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-2">
                <h6 className="text-xs font-medium text-red-800 mb-1">Error Details</h6>
                <p className="text-xs text-red-700">{error}</p>
              </div>
            )}

            {/* Validation results */}
            {validationResult && (
              <div className="bg-blue-50 border border-blue-200 rounded-md p-2">
                <h6 className="text-xs font-medium text-blue-800 mb-1">Validation Results</h6>
                <div className="text-xs text-blue-700 space-y-1">
                  <div>Status: {validationResult.isValid ? '✅ Valid' : '❌ Invalid'}</div>
                  {validationResult.errors?.length > 0 && (
                    <div>
                      <div className="font-medium">Errors:</div>
                      <ul className="list-disc list-inside ml-2">
                        {validationResult.errors.map((err, idx) => (
                          <li key={idx}>{err}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  {validationResult.warnings?.length > 0 && (
                    <div>
                      <div className="font-medium">Warnings:</div>
                      <ul className="list-disc list-inside ml-2">
                        {validationResult.warnings.map((warn, idx) => (
                          <li key={idx}>{warn}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Security scan results */}
            {securityResult && (
              <div className="bg-purple-50 border border-purple-200 rounded-md p-2">
                <h6 className="text-xs font-medium text-purple-800 mb-1">Security Scan</h6>
                <div className="text-xs text-purple-700 space-y-1">
                  <div>Risk Level: {securityResult.riskLevel || 'Unknown'}</div>
                  <div>Score: {securityResult.riskScore ? `${Math.round(securityResult.riskScore * 100)}%` : 'N/A'}</div>
                  {securityResult.threats?.length > 0 && (
                    <div>
                      <div className="font-medium">Threats Detected:</div>
                      <ul className="list-disc list-inside ml-2">
                        {securityResult.threats.map((threat, idx) => (
                          <li key={idx}>{threat}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* File metadata */}
            <div className="bg-gray-50 border border-gray-200 rounded-md p-2">
              <h6 className="text-xs font-medium text-gray-800 mb-1">File Information</h6>
              <div className="text-xs text-gray-700 space-y-1">
                <div>Size: {size ? prettyBytes(size) : 'Unknown'}</div>
                <div>Type: {file.type || 'Unknown'}</div>
                {file.lastModified && (
                  <div>Modified: {new Date(file.lastModified).toLocaleString()}</div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Accessibility announcements */}
      <div className="sr-only" aria-live="polite" aria-atomic="true">
        {status !== 'complete' && status !== 'error' && status !== 'pending' && (
          `${name}: ${currentStatus.title}, ${progressPercentage}% complete`
        )}
        {status === 'complete' && `${name}: Processing completed successfully`}
        {status === 'error' && `${name}: Processing failed. ${error || 'Unknown error'}`}
      </div>
    </div>
  );
}

export default FileProgressItem;
