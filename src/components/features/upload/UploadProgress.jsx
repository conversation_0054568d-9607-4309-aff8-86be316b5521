import React, { useState, useEffect } from 'react';
import FileProgressItem from './FileProgressItem.jsx';
import ProgressBar from './ProgressBar.jsx';

/**
 * Enhanced Upload Progress Component
 * Manages progress tracking for multiple files with detailed feedback
 *
 * Features:
 * - Multiple file progress tracking
 * - Real-time status updates
 * - Cancel/retry functionality
 * - Detailed progress stages
 * - Accessibility compliance
 * - Performance optimized
 */
function UploadProgress({
  files = [],
  overallProgress = 0,
  isActive = false,
  onCancel,
  onRetry,
  onClear,
  showDetails = true,
  className = ''
}) {
  const [expandedFiles, setExpandedFiles] = useState(new Set());

  // Calculate overall statistics
  const totalFiles = files.length;
  const completedFiles = files.filter(f => f.status === 'complete').length;
  const failedFiles = files.filter(f => f.status === 'error').length;
  const processingFiles = files.filter(f => ['uploading', 'validating', 'processing'].includes(f.status)).length;

  // Toggle file details expansion
  const toggleFileExpansion = (fileId) => {
    setExpandedFiles(prev => {
      const newSet = new Set(prev);
      if (newSet.has(fileId)) {
        newSet.delete(fileId);
      } else {
        newSet.add(fileId);
      }
      return newSet;
    });
  };

  // Auto-expand failed files
  useEffect(() => {
    const failedFileIds = files
      .filter(f => f.status === 'error')
      .map(f => f.id);

    if (failedFileIds.length > 0) {
      setExpandedFiles(prev => new Set([...prev, ...failedFileIds]));
    }
  }, [files]);

  if (!isActive && totalFiles === 0) {
    return null;
  }

  return (
    <div className={`w-full max-w-2xl mx-auto space-y-4 ${className}`}>
      {/* Overall Progress Header */}
      <div className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              {isActive ? (
                <div className="w-8 h-8 rounded-full border-2 border-blue-600 border-t-transparent animate-spin" />
              ) : completedFiles === totalFiles ? (
                <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center">
                  <span className="text-green-600 text-lg">✓</span>
                </div>
              ) : failedFiles > 0 ? (
                <div className="w-8 h-8 rounded-full bg-red-100 flex items-center justify-center">
                  <span className="text-red-600 text-lg">⚠</span>
                </div>
              ) : (
                <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                  <span className="text-gray-600 text-lg">📄</span>
                </div>
              )}
            </div>

            <div className="flex-1">
              <h3 className="text-sm font-medium text-gray-900">
                {isActive ? 'Processing Files...' :
                  completedFiles === totalFiles ? 'All Files Processed' :
                    failedFiles > 0 ? 'Processing Completed with Errors' :
                      'Processing Paused'}
              </h3>
              <p className="text-xs text-gray-600">
                {completedFiles}/{totalFiles} files completed
                {failedFiles > 0 && ` • ${failedFiles} failed`}
                {processingFiles > 0 && ` • ${processingFiles} processing`}
              </p>
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex items-center space-x-2">
            {isActive && onCancel && (
              <button
                onClick={onCancel}
                className="px-3 py-1 text-xs font-medium text-red-600 bg-red-50 border border-red-200 rounded-md hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                aria-label="Cancel upload"
              >
                Cancel
              </button>
            )}

            {!isActive && failedFiles > 0 && onRetry && (
              <button
                onClick={onRetry}
                className="px-3 py-1 text-xs font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                aria-label="Retry failed uploads"
              >
                Retry Failed
              </button>
            )}

            {!isActive && onClear && (
              <button
                onClick={onClear}
                className="px-3 py-1 text-xs font-medium text-gray-600 bg-gray-50 border border-gray-200 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                aria-label="Clear progress"
              >
                Clear
              </button>
            )}
          </div>
        </div>

        {/* Overall Progress Bar */}
        <ProgressBar
          progress={overallProgress}
          variant={failedFiles > 0 ? 'error' : isActive ? 'active' : 'success'}
          showPercentage={true}
          className="mb-2"
        />

        {/* Summary stats */}
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>
            {isActive ? 'Processing...' : 'Completed'}
          </span>
          <span>
            {Math.round(overallProgress)}% complete
          </span>
        </div>
      </div>

      {/* Individual File Progress */}
      {showDetails && totalFiles > 0 && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-gray-900">
              File Details ({totalFiles})
            </h4>
            {totalFiles > 3 && (
              <button
                onClick={() => {
                  if (expandedFiles.size === totalFiles) {
                    setExpandedFiles(new Set());
                  } else {
                    setExpandedFiles(new Set(files.map(f => f.id)));
                  }
                }}
                className="text-xs text-blue-600 hover:text-blue-800"
              >
                {expandedFiles.size === totalFiles ? 'Collapse All' : 'Expand All'}
              </button>
            )}
          </div>

          <div className="space-y-2">
            {files.map((file) => (
              <FileProgressItem
                key={file.id}
                file={file}
                isExpanded={expandedFiles.has(file.id)}
                onToggleExpansion={() => toggleFileExpansion(file.id)}
                onRetry={onRetry ? () => onRetry([file.id]) : undefined}
              />
            ))}
          </div>
        </div>
      )}

      {/* Accessibility announcements */}
      <div className="sr-only" aria-live="polite" aria-atomic="true">
        {isActive && `Processing ${processingFiles} of ${totalFiles} files. ${Math.round(overallProgress)}% complete.`}
        {!isActive && completedFiles === totalFiles && `All ${totalFiles} files processed successfully.`}
        {!isActive && failedFiles > 0 && `Processing completed. ${completedFiles} successful, ${failedFiles} failed.`}
      </div>
    </div>
  );
}

export default UploadProgress;
