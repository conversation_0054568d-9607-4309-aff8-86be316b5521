/**
 * Settings Source Selector Component
 *
 * UI component for selecting and loading configuration from multiple sources:
 * - EnvironmentConfigService
 * - Chrome storage
 * - .env file
 * - JSON config file
 *
 * ASSIGNMENT-048: Settings Loading Enhancement
 * Epic: EPIC-005 - Enhanced AI Analysis & RAG Integration
 */

import React, { useState, useEffect } from 'react';
import { configurationSourceManager, CONFIG_SOURCES } from '../../../services/ConfigurationSourceManager.js';
import { JsonConfigInput } from './JsonConfigInput.jsx';

/**
 * Settings Source Selector Component
 */
export function SettingsSourceSelector({ onConfigurationLoaded, onError }) {
  const [selectedSource, setSelectedSource] = useState(CONFIG_SOURCES.ENVIRONMENT_SERVICE);
  const [isLoading, setIsLoading] = useState(false);
  const [loadingStatus, setLoadingStatus] = useState({});
  const [testResults, setTestResults] = useState({});
  const [showDropdown, setShowDropdown] = useState(false);
  const [showJsonInput, setShowJsonInput] = useState(false);

  useEffect(() => {
    // Initialize with current source
    setSelectedSource(configurationSourceManager.getCurrentSource());
    updateLoadingStatus();
  }, []);

  /**
   * Update loading status
   */
  const updateLoadingStatus = () => {
    const status = configurationSourceManager.getLoadingStatus();
    setLoadingStatus(status);
  };

  /**
   * Handle source selection
   */
  const handleSourceSelect = (sourceId) => {
    setSelectedSource(sourceId);
    setShowDropdown(false);
    configurationSourceManager.setCurrentSource(sourceId);
    updateLoadingStatus();
  };

  /**
   * Load configuration from selected source
   */
  const handleLoadConfiguration = async () => {
    try {
      setIsLoading(true);
      console.log(`🔧 SettingsSourceSelector: Loading from ${selectedSource}`);

      const config = await configurationSourceManager.loadConfiguration();

      updateLoadingStatus();

      if (onConfigurationLoaded) {
        onConfigurationLoaded(config, selectedSource);
      }

      console.log(`✅ SettingsSourceSelector: Successfully loaded configuration from ${selectedSource}`);

    } catch (error) {
      console.error(`❌ SettingsSourceSelector: Failed to load from ${selectedSource}:`, error);
      updateLoadingStatus();

      if (onError) {
        onError(error, selectedSource);
      }
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Test all sources
   */
  const handleTestAllSources = async () => {
    try {
      setIsLoading(true);
      console.log('🧪 SettingsSourceSelector: Testing all sources...');

      const results = await configurationSourceManager.testAllSources();
      setTestResults(results);

      console.log('✅ SettingsSourceSelector: Source testing completed:', results);

    } catch (error) {
      console.error('❌ SettingsSourceSelector: Source testing failed:', error);
      if (onError) {
        onError(error, 'test_all');
      }
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Handle JSON configuration loading
   */
  const handleJsonConfigurationLoaded = async (config, sourceType) => {
    try {
      console.log('🔧 SettingsSourceSelector: JSON configuration loaded:', sourceType);

      if (onConfigurationLoaded) {
        onConfigurationLoaded(config, sourceType);
      }

      // Update loading status
      updateLoadingStatus();

    } catch (error) {
      console.error('❌ SettingsSourceSelector: Failed to handle JSON configuration:', error);
      if (onError) {
        onError(error, sourceType);
      }
    }
  };

  /**
   * Toggle JSON input display
   */
  const handleToggleJsonInput = () => {
    setShowJsonInput(!showJsonInput);
  };

  const availableSources = configurationSourceManager.getAvailableSources();
  const currentSource = availableSources.find(s => s.id === selectedSource);

  return (
    <div className="bg-white shadow rounded-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">Configuration Source</h3>
        <div className="flex space-x-2">
          <button
            onClick={handleToggleJsonInput}
            disabled={isLoading}
            className="bg-purple-600 text-white px-3 py-1 rounded text-sm font-medium hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50"
          >
            {showJsonInput ? '📋 Hide JSON Input' : '📋 JSON Input'}
          </button>
          <button
            onClick={handleTestAllSources}
            disabled={isLoading}
            className="bg-gray-600 text-white px-3 py-1 rounded text-sm font-medium hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 disabled:opacity-50"
          >
            {isLoading ? '🧪 Testing...' : '🧪 Test All'}
          </button>
          <button
            onClick={handleLoadConfiguration}
            disabled={isLoading}
            className="bg-blue-600 text-white px-4 py-2 rounded text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {isLoading ? '⏳ Loading...' : '🔄 Load Settings'}
          </button>
        </div>
      </div>

      {/* Source Selector Dropdown */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Select Configuration Source
        </label>
        <div className="relative">
          <button
            onClick={() => setShowDropdown(!showDropdown)}
            className="w-full bg-white border border-gray-300 rounded-md px-3 py-2 text-left shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <div className="flex items-center">
              <span className="mr-2">{currentSource?.icon}</span>
              <div className="flex-1">
                <div className="font-medium">{currentSource?.name}</div>
                <div className="text-sm text-gray-500">{currentSource?.description}</div>
              </div>
              <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </button>

          {showDropdown && (
            <div className="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none">
              {availableSources.map((source) => (
                <button
                  key={source.id}
                  onClick={() => handleSourceSelect(source.id)}
                  className={`w-full text-left px-3 py-2 hover:bg-gray-100 ${
                    source.id === selectedSource ? 'bg-blue-50 text-blue-600' : 'text-gray-900'
                  }`}
                >
                  <div className="flex items-center">
                    <span className="mr-2">{source.icon}</span>
                    <div>
                      <div className="font-medium">{source.name}</div>
                      <div className="text-sm text-gray-500">{source.description}</div>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Loading Status */}
      {loadingStatus.lastLoadTime && (
        <div className="mb-4 p-3 bg-gray-50 rounded-md">
          <div className="text-sm text-gray-600">
            <div className="flex items-center justify-between">
              <span>Last loaded from: <strong>{loadingStatus.currentSource}</strong></span>
              <span className="text-xs">{loadingStatus.lastLoadTime.toLocaleString()}</span>
            </div>
            {loadingStatus.hasConfig && (
              <div className="mt-1 text-green-600">
                ✅ Configuration loaded successfully
              </div>
            )}
          </div>
        </div>
      )}

      {/* Error Display */}
      {Object.keys(loadingStatus.errors || {}).length > 0 && (
        <div className="mb-4 p-3 bg-red-50 rounded-md">
          <h4 className="text-sm font-medium text-red-800 mb-2">Loading Errors:</h4>
          {Object.entries(loadingStatus.errors).map(([source, errorInfo]) => (
            <div key={source} className="text-sm text-red-600 mb-1">
              <strong>{source}:</strong> {errorInfo.error}
              <span className="text-xs text-red-500 ml-2">
                ({errorInfo.timestamp.toLocaleString()})
              </span>
            </div>
          ))}
        </div>
      )}

      {/* Test Results */}
      {Object.keys(testResults).length > 0 && (
        <div className="mt-4 p-3 bg-gray-50 rounded-md">
          <h4 className="text-sm font-medium text-gray-800 mb-2">Source Test Results:</h4>
          <div className="space-y-2">
            {Object.entries(testResults).map(([sourceId, result]) => {
              const source = availableSources.find(s => s.id === sourceId);
              return (
                <div key={sourceId} className="flex items-center justify-between text-sm">
                  <div className="flex items-center">
                    <span className="mr-2">{source?.icon}</span>
                    <span>{source?.name}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    {result.success ? (
                      <>
                        <span className="text-green-600">✅ Success</span>
                        <span className="text-gray-500">
                          ({result.loadTime}ms, {result.configSize} items)
                        </span>
                      </>
                    ) : (
                      <span className="text-red-600">❌ {result.error}</span>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* JSON Configuration Input */}
      {showJsonInput && (
        <div className="mt-4">
          <JsonConfigInput
            onConfigurationLoaded={handleJsonConfigurationLoaded}
            onError={onError}
            disabled={isLoading}
          />
        </div>
      )}

      {/* Help Text */}
      <div className="mt-4 p-3 bg-blue-50 rounded-md">
        <h4 className="text-sm font-medium text-blue-800 mb-1">Configuration Sources:</h4>
        <ul className="text-sm text-blue-600 space-y-1">
          <li>• <strong>Environment Service:</strong> Default configuration with .env integration</li>
          <li>• <strong>Chrome Storage:</strong> Settings stored in browser extension storage</li>
          <li>• <strong>.env File:</strong> Direct environment variable loading</li>
          <li>• <strong>JSON Config:</strong> Configuration from config.json file</li>
          <li>• <strong>JSON Text Input:</strong> Paste JSON configuration directly</li>
        </ul>
      </div>
    </div>
  );
}
