import React, { useState, useEffect } from 'react';

/**
 * Theme Selector Component
 * Handles theme selection with system preference detection
 */
export function ThemeSelector({
  currentTheme = 'light',
  onChange,
  disabled = false
}) {
  const [systemTheme, setSystemTheme] = useState('light');

  // Theme options
  const themes = [
    {
      value: 'light',
      name: 'Light',
      icon: '☀️',
      description: 'Clean, bright interface for daytime use'
    },
    {
      value: 'dark',
      name: 'Dark',
      icon: '🌙',
      description: 'Easy on the eyes for low-light environments'
    },
    {
      value: 'auto',
      name: 'Auto',
      icon: '🔄',
      description: 'Follows your system preference'
    }
  ];

  // Detect system theme preference
  useEffect(() => {
    const detectSystemTheme = () => {
      if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
        setSystemTheme('dark');
      } else {
        setSystemTheme('light');
      }
    };

    // Initial detection
    detectSystemTheme();

    // Listen for changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleChange = (e) => {
      setSystemTheme(e.matches ? 'dark' : 'light');
    };

    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleChange);
    } else {
      // Fallback for older browsers
      mediaQuery.addListener(handleChange);
    }

    return () => {
      if (mediaQuery.removeEventListener) {
        mediaQuery.removeEventListener('change', handleChange);
      } else {
        mediaQuery.removeListener(handleChange);
      }
    };
  }, []);

  // Get effective theme (resolve 'auto' to actual theme)
  const getEffectiveTheme = (theme) => {
    return theme === 'auto' ? systemTheme : theme;
  };

  // Handle theme selection
  const handleThemeChange = (themeValue) => {
    if (onChange) {
      onChange(themeValue);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <label className="block text-sm font-medium text-gray-700">
          Theme Preference
        </label>
        {currentTheme === 'auto' && (
          <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
            Using: {systemTheme === 'dark' ? '🌙 Dark' : '☀️ Light'}
          </span>
        )}
      </div>

      <div className="grid grid-cols-1 gap-3">
        {themes.map(theme => (
          <div
            key={theme.value}
            className={`relative border rounded-lg p-4 cursor-pointer transition-all ${
              currentTheme === theme.value
                ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
                : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
            } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
            onClick={() => !disabled && handleThemeChange(theme.value)}
          >
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 flex items-center justify-center text-lg">
                  {theme.icon}
                </div>
              </div>
              <div className="ml-3 flex-1">
                <div className="flex items-center">
                  <input
                    type="radio"
                    name="theme"
                    value={theme.value}
                    checked={currentTheme === theme.value}
                    onChange={() => handleThemeChange(theme.value)}
                    disabled={disabled}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <label className="ml-2 text-sm font-medium text-gray-900 cursor-pointer">
                    {theme.name}
                  </label>
                </div>
                <p className="text-xs text-gray-500 mt-1 ml-6">
                  {theme.description}
                </p>
              </div>
            </div>

            {/* Theme Preview */}
            <div className="mt-3 ml-11">
              <div className="flex space-x-2">
                <div className={`w-16 h-10 rounded border-2 flex items-center justify-center text-xs ${
                  getEffectiveTheme(theme.value) === 'dark'
                    ? 'bg-gray-800 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-800'
                }`}>
                  Preview
                </div>
                <div className="flex flex-col space-y-1">
                  <div className={`w-8 h-2 rounded ${
                    getEffectiveTheme(theme.value) === 'dark' ? 'bg-gray-600' : 'bg-gray-200'
                  }`} />
                  <div className={`w-6 h-2 rounded ${
                    getEffectiveTheme(theme.value) === 'dark' ? 'bg-gray-700' : 'bg-gray-300'
                  }`} />
                  <div className={`w-4 h-2 rounded ${
                    getEffectiveTheme(theme.value) === 'dark' ? 'bg-gray-600' : 'bg-gray-200'
                  }`} />
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Theme Information */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <span className="text-blue-500 text-sm">💡</span>
          </div>
          <div className="ml-2">
            <h4 className="text-xs font-medium text-blue-800">Theme Tips</h4>
            <ul className="text-xs text-blue-700 mt-1 space-y-1">
              <li>• Light theme is ideal for well-lit environments</li>
              <li>• Dark theme reduces eye strain in low light</li>
              <li>• Auto theme switches based on your system settings</li>
              <li>• Theme changes apply immediately across the extension</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Current Selection Summary */}
      <div className="text-sm text-gray-600">
        <strong>Current selection:</strong> {themes.find(t => t.value === currentTheme)?.name}
        {currentTheme === 'auto' && (
          <span className="text-gray-500">
            {' '}(currently using {systemTheme} mode)
          </span>
        )}
      </div>
    </div>
  );
}

export default ThemeSelector;
