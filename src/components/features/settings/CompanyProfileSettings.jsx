import React, { useState, useEffect } from 'react';
import { useSettings } from '../../../popup/hooks/useSettings.js';
import { CompanyInformation } from './CompanyInformation.jsx';
import { BusinessConfiguration } from './BusinessConfiguration.jsx';
import { LogoUpload } from './LogoUpload.jsx';

/**
 * Company Profile Settings Component
 * Main component for managing company profile and business configuration
 */
export function CompanyProfileSettings() {
  const { settings, updateSettingSection, isLoading } = useSettings();
  const [activeTab, setActiveTab] = useState('information');
  const [isSaving, setIsSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState('');
  const [errors, setErrors] = useState({});

  // Local state for form data
  const [companyData, setCompanyData] = useState({
    name: '',
    taxId: '',
    address: '',
    email: '',
    phone: '',
    logo: ''
  });

  const [businessData, setBusinessData] = useState({
    currency: 'PLN',
    vatRate: '23',
    fiscalYearStart: '01-01',
    industry: ''
  });

  // Load settings data
  useEffect(() => {
    if (settings.company) {
      setCompanyData(settings.company);
    }
    if (settings.display) {
      setBusinessData(prev => ({
        ...prev,
        currency: settings.display.currency || 'PLN'
      }));
    }
    // Note: VAT rate and other business config will be added to settings schema
  }, [settings]);

  // Tabs configuration
  const tabs = [
    {
      id: 'information',
      name: 'Company Info',
      icon: '🏢',
      description: 'Basic company details and contact information'
    },
    {
      id: 'business',
      name: 'Business Config',
      icon: '⚙️',
      description: 'Currency, VAT rates, and business settings'
    },
    {
      id: 'logo',
      name: 'Logo & Branding',
      icon: '🎨',
      description: 'Company logo and visual identity'
    }
  ];

  // Validate form data
  const validateData = () => {
    const newErrors = {};

    // Company information validation
    if (!companyData.name?.trim()) {
      newErrors.name = 'Company name is required';
    }

    if (companyData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(companyData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Business configuration validation
    if (!businessData.currency) {
      newErrors.currency = 'Currency is required';
    }

    if (!businessData.vatRate) {
      newErrors.vatRate = 'VAT rate is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Save settings
  const handleSave = async () => {
    if (!validateData()) {
      setSaveMessage('❌ Please fix the errors above');
      return;
    }

    setIsSaving(true);
    setSaveMessage('');

    try {
      // Update company section
      await updateSettingSection('company', companyData);

      // Update display section with currency
      await updateSettingSection('display', {
        ...settings.display,
        currency: businessData.currency
      });

      // TODO: Add business configuration to settings schema
      // For now, we'll store it in company section as metadata
      await updateSettingSection('company', {
        ...companyData,
        businessConfig: {
          vatRate: businessData.vatRate,
          fiscalYearStart: businessData.fiscalYearStart,
          industry: businessData.industry
        }
      });

      setSaveMessage('✅ Company profile saved successfully');

      // Clear message after 3 seconds
      setTimeout(() => setSaveMessage(''), 3000);

    } catch (error) {
      console.error('Failed to save company profile:', error);
      setSaveMessage('❌ Failed to save: ' + error.message);
    } finally {
      setIsSaving(false);
    }
  };

  // Reset to defaults
  const handleReset = () => {
    if (confirm('Are you sure you want to reset all company settings to defaults? This cannot be undone.')) {
      setCompanyData({
        name: '',
        taxId: '',
        address: '',
        email: '',
        phone: '',
        logo: ''
      });
      setBusinessData({
        currency: 'PLN',
        vatRate: '23',
        fiscalYearStart: '01-01',
        industry: ''
      });
      setErrors({});
      setSaveMessage('');
    }
  };

  // Handle company data changes
  const handleCompanyChange = (newData) => {
    setCompanyData(newData);
    // Clear related errors
    const newErrors = { ...errors };
    Object.keys(newData).forEach(key => {
      if (newErrors[key]) { delete newErrors[key]; }
    });
    setErrors(newErrors);
  };

  // Handle business data changes
  const handleBusinessChange = (newData) => {
    setBusinessData(newData);
    // Clear related errors
    const newErrors = { ...errors };
    Object.keys(newData).forEach(key => {
      if (newErrors[key]) { delete newErrors[key]; }
    });
    setErrors(newErrors);
  };

  // Handle logo changes
  const handleLogoChange = (logoData) => {
    setCompanyData(prev => ({ ...prev, logo: logoData }));
    if (errors.logo) {
      setErrors(prev => ({ ...prev, logo: undefined }));
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500" />
        <span className="ml-2 text-gray-600">Loading company settings...</span>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Company Profile</h2>
        <p className="text-gray-600">
          Configure your company information and business settings for accurate document processing.
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {tabs.map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mb-6">
        {activeTab === 'information' && (
          <CompanyInformation
            companyData={companyData}
            onChange={handleCompanyChange}
            errors={errors}
            disabled={isSaving}
          />
        )}

        {activeTab === 'business' && (
          <BusinessConfiguration
            businessData={businessData}
            onChange={handleBusinessChange}
            errors={errors}
            disabled={isSaving}
          />
        )}

        {activeTab === 'logo' && (
          <LogoUpload
            logoData={companyData.logo}
            onChange={handleLogoChange}
            errors={errors}
            disabled={isSaving}
          />
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-between pt-6 border-t border-gray-200">
        <button
          type="button"
          onClick={handleReset}
          disabled={isSaving}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          Reset to Defaults
        </button>

        <div className="flex items-center space-x-4">
          {saveMessage && (
            <span className={`text-sm ${saveMessage.startsWith('✅') ? 'text-green-600' : 'text-red-600'}`}>
              {saveMessage}
            </span>
          )}

          <button
            type="button"
            onClick={handleSave}
            disabled={isSaving}
            className="px-6 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 flex items-center"
          >
            {isSaving && (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
            )}
            {isSaving ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </div>
    </div>
  );
}

export default CompanyProfileSettings;
