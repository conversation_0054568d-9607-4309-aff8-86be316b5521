import React, { useState, useEffect } from 'react';
import { ThemeSelector } from './ThemeSelector.jsx';
import { LanguageSelector } from './LanguageSelector.jsx';

/**
 * Display Preferences Component
 * Handles theme, language, table preferences, and notification settings
 */
export function DisplayPreferences({
  displayData = {},
  onChange,
  errors = {},
  disabled = false
}) {
  const [localData, setLocalData] = useState({
    theme: 'light',
    language: 'pl',
    dateFormat: 'DD/MM/YYYY',
    currency: 'PLN',
    groupBy: 'month',
    tablePageSize: 10,
    showNotifications: true,
    notificationDuration: 3000,
    ...displayData
  });

  // Date format options
  const dateFormats = [
    { value: 'DD/MM/YYYY', label: 'DD/MM/YYYY', example: '27/01/2025' },
    { value: 'MM/DD/YYYY', label: 'MM/DD/YYYY', example: '01/27/2025' },
    { value: 'YYYY-MM-DD', label: 'YYYY-MM-DD', example: '2025-01-27' },
    { value: 'DD.MM.YYYY', label: 'DD.MM.YYYY', example: '27.01.2025' }
  ];

  // Currency options
  const currencies = [
    { code: 'PLN', name: 'Polish Złoty', symbol: 'zł' },
    { code: 'EUR', name: 'Euro', symbol: '€' },
    { code: 'USD', name: 'US Dollar', symbol: '$' },
    { code: 'GBP', name: 'British Pound', symbol: '£' },
    { code: 'CZK', name: 'Czech Koruna', symbol: 'Kč' },
    { code: 'HUF', name: 'Hungarian Forint', symbol: 'Ft' }
  ];

  // Grouping options
  const groupingOptions = [
    { value: 'month', label: 'By Month', description: 'Group data by calendar month' },
    { value: 'quarter', label: 'By Quarter', description: 'Group data by fiscal quarter' },
    { value: 'year', label: 'By Year', description: 'Group data by calendar year' }
  ];

  // Table page size options
  const pageSizeOptions = [
    { value: 5, label: '5 items per page' },
    { value: 10, label: '10 items per page' },
    { value: 25, label: '25 items per page' },
    { value: 50, label: '50 items per page' },
    { value: 100, label: '100 items per page' }
  ];

  // Update local data when props change
  useEffect(() => {
    setLocalData(prev => ({
      ...prev,
      ...displayData
    }));
  }, [displayData]);

  // Handle input changes
  const handleInputChange = (field, value) => {
    const newData = { ...localData, [field]: value };
    setLocalData(newData);

    // Notify parent component
    if (onChange) {
      onChange(newData);
    }
  };

  // Handle language change with format updates
  const handleLanguageChange = (languageCode, languageData) => {
    const updates = {
      language: languageCode
    };

    // Update related formats based on language
    if (languageData) {
      if (languageCode === 'pl') {
        updates.dateFormat = 'DD/MM/YYYY';
        updates.currency = 'PLN';
      } else if (languageCode === 'en') {
        updates.dateFormat = 'MM/DD/YYYY';
        updates.currency = 'USD';
      }
    }

    const newData = { ...localData, ...updates };
    setLocalData(newData);

    if (onChange) {
      onChange(newData);
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <span className="text-purple-500 text-xl">🎨</span>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-purple-800">Display Preferences</h3>
            <p className="text-sm text-purple-700 mt-1">
              Customize the appearance and behavior of your MVAT extension interface.
              Changes apply immediately across all components.
            </p>
          </div>
        </div>
      </div>

      {/* Theme Selection */}
      <div>
        <h4 className="text-lg font-medium text-gray-900 mb-4">Theme & Appearance</h4>
        <ThemeSelector
          currentTheme={localData.theme}
          onChange={(theme) => handleInputChange('theme', theme)}
          disabled={disabled}
        />
      </div>

      {/* Language Selection */}
      <div>
        <h4 className="text-lg font-medium text-gray-900 mb-4">Language & Locale</h4>
        <LanguageSelector
          currentLanguage={localData.language}
          onChange={handleLanguageChange}
          disabled={disabled}
        />
      </div>

      {/* Formatting Options */}
      <div>
        <h4 className="text-lg font-medium text-gray-900 mb-4">Formatting Options</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Date Format */}
          <div>
            <label htmlFor="date-format" className="block text-sm font-medium text-gray-700 mb-2">
              Date Format
            </label>
            <select
              id="date-format"
              value={localData.dateFormat}
              onChange={(e) => handleInputChange('dateFormat', e.target.value)}
              disabled={disabled}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.dateFormat ? 'border-red-300' : 'border-gray-300'
              } ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}`}
            >
              {dateFormats.map(format => (
                <option key={format.value} value={format.value}>
                  {format.label} (e.g., {format.example})
                </option>
              ))}
            </select>
            {errors.dateFormat && (
              <p className="mt-1 text-sm text-red-600">{errors.dateFormat}</p>
            )}
          </div>

          {/* Currency */}
          <div>
            <label htmlFor="currency" className="block text-sm font-medium text-gray-700 mb-2">
              Default Currency
            </label>
            <select
              id="currency"
              value={localData.currency}
              onChange={(e) => handleInputChange('currency', e.target.value)}
              disabled={disabled}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.currency ? 'border-red-300' : 'border-gray-300'
              } ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}`}
            >
              {currencies.map(currency => (
                <option key={currency.code} value={currency.code}>
                  {currency.name} ({currency.symbol})
                </option>
              ))}
            </select>
            {errors.currency && (
              <p className="mt-1 text-sm text-red-600">{errors.currency}</p>
            )}
          </div>
        </div>
      </div>

      {/* Table Preferences */}
      <div>
        <h4 className="text-lg font-medium text-gray-900 mb-4">Table & Data Display</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Grouping */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Default Grouping
            </label>
            <div className="space-y-2">
              {groupingOptions.map(option => (
                <div key={option.value} className="flex items-start">
                  <input
                    id={`group-${option.value}`}
                    type="radio"
                    name="groupBy"
                    value={option.value}
                    checked={localData.groupBy === option.value}
                    onChange={(e) => handleInputChange('groupBy', e.target.value)}
                    disabled={disabled}
                    className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <div className="ml-3">
                    <label htmlFor={`group-${option.value}`} className="text-sm font-medium text-gray-700 cursor-pointer">
                      {option.label}
                    </label>
                    <p className="text-xs text-gray-500">{option.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Page Size */}
          <div>
            <label htmlFor="page-size" className="block text-sm font-medium text-gray-700 mb-2">
              Table Page Size
            </label>
            <select
              id="page-size"
              value={localData.tablePageSize}
              onChange={(e) => handleInputChange('tablePageSize', parseInt(e.target.value))}
              disabled={disabled}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                disabled ? 'bg-gray-100 cursor-not-allowed' : 'border-gray-300'
              }`}
            >
              {pageSizeOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            <p className="mt-1 text-xs text-gray-500">
              Number of items to display per page in data tables
            </p>
          </div>
        </div>
      </div>

      {/* Notification Settings */}
      <div>
        <h4 className="text-lg font-medium text-gray-900 mb-4">Notifications</h4>
        <div className="space-y-4">
          <div className="flex items-center">
            <input
              id="show-notifications"
              type="checkbox"
              checked={localData.showNotifications}
              onChange={(e) => handleInputChange('showNotifications', e.target.checked)}
              disabled={disabled}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="show-notifications" className="ml-2 text-sm text-gray-700">
              Show notifications for processing status and results
            </label>
          </div>

          {localData.showNotifications && (
            <div className="ml-6">
              <label htmlFor="notification-duration" className="block text-sm font-medium text-gray-700 mb-2">
                Notification Duration
              </label>
              <select
                id="notification-duration"
                value={localData.notificationDuration}
                onChange={(e) => handleInputChange('notificationDuration', parseInt(e.target.value))}
                disabled={disabled}
                className="w-48 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value={2000}>2 seconds</option>
                <option value={3000}>3 seconds</option>
                <option value={5000}>5 seconds</option>
                <option value={10000}>10 seconds</option>
                <option value={0}>Until dismissed</option>
              </select>
            </div>
          )}
        </div>
      </div>

      {/* Preview Section */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h4 className="text-sm font-medium text-gray-800 mb-3">Settings Preview</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="text-gray-600">Theme:</span>
            <div className="font-medium">{localData.theme}</div>
          </div>
          <div>
            <span className="text-gray-600">Language:</span>
            <div className="font-medium">{localData.language.toUpperCase()}</div>
          </div>
          <div>
            <span className="text-gray-600">Date:</span>
            <div className="font-mono text-gray-800">{dateFormats.find(f => f.value === localData.dateFormat)?.example}</div>
          </div>
          <div>
            <span className="text-gray-600">Currency:</span>
            <div className="font-medium">{currencies.find(c => c.code === localData.currency)?.symbol} {localData.currency}</div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default DisplayPreferences;
