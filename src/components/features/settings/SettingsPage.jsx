import React, { useState } from 'react';
import { ApiKeyManager } from './ApiKeyManager.jsx';
import { CompanyProfileSettings } from './CompanyProfileSettings.jsx';
import { DisplayPreferences } from './DisplayPreferences.jsx';
import { ProcessingPreferences } from './ProcessingPreferences.jsx';
import { DataManagementTab } from './DataManagementTab.jsx';
import { FeaturesAndSubscriptionTab } from './FeaturesAndSubscriptionTab.jsx';
import { EnvironmentSettings } from './EnvironmentSettings.jsx';
import { useSettings } from '../../../popup/hooks/useSettings.js';

/**
 * Display Preferences Wrapper
 */
function DisplayPreferencesWrapper() {
  const { settings, updateSettingSection, isLoading } = useSettings();
  const [isSaving, setIsSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState('');

  const handleChange = async (newDisplayData) => {
    setIsSaving(true);
    setSaveMessage('');

    try {
      await updateSettingSection('display', newDisplayData);
      setSaveMessage('✅ Display preferences saved successfully');
      setTimeout(() => setSaveMessage(''), 3000);
    } catch (error) {
      console.error('Failed to save display preferences:', error);
      setSaveMessage('❌ Failed to save: ' + error.message);
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500" />
        <span className="ml-2 text-gray-600">Loading display settings...</span>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <DisplayPreferences
        displayData={settings.display || {}}
        onChange={handleChange}
        disabled={isSaving}
      />

      {saveMessage && (
        <div className="mt-4 text-center">
          <span className={`text-sm ${saveMessage.startsWith('✅') ? 'text-green-600' : 'text-red-600'}`}>
            {saveMessage}
          </span>
        </div>
      )}
    </div>
  );
}

/**
 * Processing Preferences Wrapper
 */
function ProcessingPreferencesWrapper() {
  const { settings, updateSettingSection, isLoading } = useSettings();
  const [isSaving, setIsSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState('');

  const handleChange = async (newProcessingData) => {
    setIsSaving(true);
    setSaveMessage('');

    try {
      await updateSettingSection('processing', newProcessingData);
      setSaveMessage('✅ Processing preferences saved successfully');
      setTimeout(() => setSaveMessage(''), 3000);
    } catch (error) {
      console.error('Failed to save processing preferences:', error);
      setSaveMessage('❌ Failed to save: ' + error.message);
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500" />
        <span className="ml-2 text-gray-600">Loading processing settings...</span>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <ProcessingPreferences
        processingData={settings.processing || {}}
        onChange={handleChange}
        disabled={isSaving}
      />

      {saveMessage && (
        <div className="mt-4 text-center">
          <span className={`text-sm ${saveMessage.startsWith('✅') ? 'text-green-600' : 'text-red-600'}`}>
            {saveMessage}
          </span>
        </div>
      )}
    </div>
  );
}

/**
 * Settings Page Component
 * Main settings interface with tabbed navigation
 *
 * MOVED FROM: src/popup/components/Settings/SettingsPage.jsx
 * TO: src/components/settings/SettingsPage.jsx (shared location)
 *
 * CONSOLIDATION: This is the advanced version with 6 tabs and proper React hooks
 * Replaces the simple version that was in src/popup/components/settings/SettingsPage.jsx
 */
export function SettingsPage() {
  const [activeTab, setActiveTab] = useState('api-keys');

  const tabs = [
    {
      id: 'configuration',
      name: 'Configuration',
      icon: '🔧',
      description: 'Environment configuration, sources, and debug tools'
    },
    {
      id: 'api-keys',
      name: 'API Keys',
      icon: '🔑',
      description: 'Configure API keys for AI and accounting services'
    },
    {
      id: 'company',
      name: 'Company',
      icon: '🏢',
      description: 'Company profile and business information',
      disabled: false
    },
    {
      id: 'display',
      name: 'Display',
      icon: '🎨',
      description: 'Display preferences, formatting, and localization',
      disabled: false
    },
    {
      id: 'processing',
      name: 'Processing',
      icon: '⚙️',
      description: 'Document processing and AI settings',
      disabled: false
    },
    {
      id: 'features',
      name: 'Features',
      icon: '⚡',
      description: 'Feature flags and subscription settings',
      disabled: false
    },
    {
      id: 'data',
      name: 'Data',
      icon: '💾',
      description: 'Data management and export options',
      disabled: false
    }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'configuration':
        return <EnvironmentSettings />;
      case 'api-keys':
        return <ApiKeyManager />;
      case 'company':
        return <CompanyProfileSettings />;
      case 'display':
        return <DisplayPreferencesWrapper />;
      case 'processing':
        return <ProcessingPreferencesWrapper />;
      case 'features':
        return <FeaturesAndSubscriptionTab />;
      case 'data':
        return <DataManagementTab />;
      default:
        return <ApiKeyManager />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="bg-white shadow-sm border-b border-gray-200">
          <div className="px-6 py-4">
            <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
            <p className="text-gray-600 mt-1">
              Configure your MVAT extension preferences and integrations
            </p>
          </div>
        </div>

        <div className="flex">
          {/* Sidebar Navigation */}
          <div className="w-64 bg-white shadow-sm min-h-screen">
            <nav className="p-4">
              <ul className="space-y-2">
                {tabs.map((tab) => (
                  <li key={tab.id}>
                    <button
                      onClick={() => !tab.disabled && setActiveTab(tab.id)}
                      disabled={tab.disabled}
                      className={`
                        w-full text-left px-4 py-3 rounded-lg transition-colors
                        ${activeTab === tab.id
                    ? 'bg-blue-50 text-blue-700 border border-blue-200'
                    : tab.disabled
                      ? 'text-gray-400 cursor-not-allowed'
                      : 'text-gray-700 hover:bg-gray-50'
                  }
                      `}
                    >
                      <div className="flex items-center">
                        <span className="text-lg mr-3">{tab.icon}</span>
                        <div>
                          <div className="font-medium">{tab.name}</div>
                          <div className="text-xs text-gray-500 mt-1">
                            {tab.description}
                          </div>
                        </div>
                      </div>
                      {tab.disabled && (
                        <div className="text-xs text-gray-400 mt-1 ml-8">
                          Coming soon
                        </div>
                      )}
                    </button>
                  </li>
                ))}
              </ul>
            </nav>
          </div>

          {/* Main Content */}
          <div className="flex-1 bg-white">
            <div className="p-6">
              {renderTabContent()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
