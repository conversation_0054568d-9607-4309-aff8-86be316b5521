import React, { useState, useEffect } from 'react';
import { validateNip, formatNip } from '../../../utils/nipValidator.js';

/**
 * Company Information Component
 * Handles company details form with Polish NIP validation
 */
export function CompanyInformation({
  companyData = {},
  onChange,
  errors = {},
  disabled = false
}) {
  const [localData, setLocalData] = useState({
    name: '',
    taxId: '',
    address: '',
    email: '',
    phone: '',
    ...companyData
  });

  const [nipValidation, setNipValidation] = useState({ valid: true, error: null });
  const [isValidating, setIsValidating] = useState(false);

  // Update local data when props change
  useEffect(() => {
    setLocalData(prev => ({
      ...prev,
      ...companyData
    }));
  }, [companyData]);

  // Handle input changes
  const handleInputChange = (field, value) => {
    const newData = { ...localData, [field]: value };
    setLocalData(newData);

    // Special handling for NIP validation
    if (field === 'taxId') {
      validateNipField(value);
    }

    // Notify parent component
    if (onChange) {
      onChange(newData);
    }
  };

  // Validate NIP field
  const validateNipField = (nipValue) => {
    if (!nipValue || nipValue.trim() === '') {
      setNipValidation({ valid: true, error: null });
      return;
    }

    setIsValidating(true);

    // Debounce validation
    setTimeout(() => {
      const validation = validateNip(nipValue);
      setNipValidation(validation);
      setIsValidating(false);

      // Auto-format valid NIP
      if (validation.valid && validation.formatted !== nipValue) {
        const newData = { ...localData, taxId: validation.formatted };
        setLocalData(newData);
        if (onChange) {
          onChange(newData);
        }
      }
    }, 300);
  };

  // Email validation
  const validateEmail = (email) => {
    if (!email) { return true; }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // Phone validation (basic)
  const validatePhone = (phone) => {
    if (!phone) { return true; }
    const phoneRegex = /^[\+]?[0-9\s\-\(\)]{7,15}$/;
    return phoneRegex.test(phone);
  };

  return (
    <div className="space-y-6">
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <span className="text-blue-500 text-xl">ℹ️</span>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">Company Information</h3>
            <p className="text-sm text-blue-700 mt-1">
              Configure your company details for accurate document processing and invoice generation.
              All fields are optional except where noted.
            </p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Company Name */}
        <div className="md:col-span-2">
          <label htmlFor="company-name" className="block text-sm font-medium text-gray-700 mb-2">
            Company Name *
          </label>
          <input
            id="company-name"
            type="text"
            value={localData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            disabled={disabled}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.name ? 'border-red-300' : 'border-gray-300'
            } ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}`}
            placeholder="Enter your company name"
            aria-describedby={errors.name ? 'company-name-error' : undefined}
          />
          {errors.name && (
            <p id="company-name-error" className="mt-1 text-sm text-red-600">
              {errors.name}
            </p>
          )}
        </div>

        {/* Tax ID (NIP) */}
        <div>
          <label htmlFor="tax-id" className="block text-sm font-medium text-gray-700 mb-2">
            Tax ID (NIP)
            <span className="text-gray-500 text-xs ml-1">Polish format</span>
          </label>
          <div className="relative">
            <input
              id="tax-id"
              type="text"
              value={localData.taxId}
              onChange={(e) => handleInputChange('taxId', e.target.value)}
              disabled={disabled}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                nipValidation.error || errors.taxId ? 'border-red-300' :
                  nipValidation.valid && localData.taxId ? 'border-green-300' : 'border-gray-300'
              } ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}`}
              placeholder="XXX-XXX-XX-XX"
              aria-describedby="tax-id-help"
            />
            {isValidating && (
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500" />
              </div>
            )}
          </div>
          <p id="tax-id-help" className="mt-1 text-xs text-gray-500">
            Polish NIP format: XXX-XXX-XX-XX (dashes optional)
          </p>
          {nipValidation.error && (
            <p className="mt-1 text-sm text-red-600">
              {nipValidation.error}
            </p>
          )}
          {errors.taxId && (
            <p className="mt-1 text-sm text-red-600">
              {errors.taxId}
            </p>
          )}
        </div>

        {/* Email */}
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
            Email Address
          </label>
          <input
            id="email"
            type="email"
            value={localData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            disabled={disabled}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.email || (localData.email && !validateEmail(localData.email)) ? 'border-red-300' : 'border-gray-300'
            } ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}`}
            placeholder="<EMAIL>"
            aria-describedby={errors.email ? 'email-error' : undefined}
          />
          {localData.email && !validateEmail(localData.email) && (
            <p className="mt-1 text-sm text-red-600">
              Please enter a valid email address
            </p>
          )}
          {errors.email && (
            <p id="email-error" className="mt-1 text-sm text-red-600">
              {errors.email}
            </p>
          )}
        </div>

        {/* Phone */}
        <div>
          <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
            Phone Number
          </label>
          <input
            id="phone"
            type="tel"
            value={localData.phone}
            onChange={(e) => handleInputChange('phone', e.target.value)}
            disabled={disabled}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.phone || (localData.phone && !validatePhone(localData.phone)) ? 'border-red-300' : 'border-gray-300'
            } ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}`}
            placeholder="+48 123 456 789"
            aria-describedby={errors.phone ? 'phone-error' : undefined}
          />
          {localData.phone && !validatePhone(localData.phone) && (
            <p className="mt-1 text-sm text-red-600">
              Please enter a valid phone number
            </p>
          )}
          {errors.phone && (
            <p id="phone-error" className="mt-1 text-sm text-red-600">
              {errors.phone}
            </p>
          )}
        </div>

        {/* Address */}
        <div className="md:col-span-2">
          <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-2">
            Business Address
          </label>
          <textarea
            id="address"
            rows={3}
            value={localData.address}
            onChange={(e) => handleInputChange('address', e.target.value)}
            disabled={disabled}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.address ? 'border-red-300' : 'border-gray-300'
            } ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}`}
            placeholder="Street address, city, postal code, country"
            aria-describedby={errors.address ? 'address-error' : undefined}
          />
          {errors.address && (
            <p id="address-error" className="mt-1 text-sm text-red-600">
              {errors.address}
            </p>
          )}
        </div>
      </div>

      {/* Validation Summary */}
      {localData.name && localData.taxId && nipValidation.valid && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center">
            <span className="text-green-500 text-lg mr-2">✅</span>
            <span className="text-sm text-green-800">
              Company information looks good! Your NIP is valid and properly formatted.
            </span>
          </div>
        </div>
      )}
    </div>
  );
}

export default CompanyInformation;
