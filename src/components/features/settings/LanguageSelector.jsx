import React from 'react';

/**
 * Language Selector Component
 * Handles language/locale selection with future i18n support
 */
export function LanguageSelector({
  currentLanguage = 'pl',
  onChange,
  disabled = false
}) {
  // Supported languages
  const languages = [
    {
      code: 'pl',
      name: '<PERSON><PERSON>',
      nativeName: '<PERSON><PERSON>',
      flag: '🇵🇱',
      description: 'Polish language with local formatting',
      dateFormat: 'DD/MM/YYYY',
      numberFormat: '1 234,56',
      currency: 'PLN'
    },
    {
      code: 'en',
      name: 'English',
      nativeName: 'English',
      flag: '🇺🇸',
      description: 'English language with international formatting',
      dateFormat: 'MM/DD/YYYY',
      numberFormat: '1,234.56',
      currency: 'USD'
    }
  ];

  // Handle language selection
  const handleLanguageChange = (languageCode) => {
    if (onChange) {
      const selectedLanguage = languages.find(lang => lang.code === languageCode);
      onChange(languageCode, selectedLanguage);
    }
  };

  const currentLangData = languages.find(lang => lang.code === currentLanguage);

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <label className="block text-sm font-medium text-gray-700">
          Language & Locale
        </label>
        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
          {currentLangData?.flag} {currentLangData?.nativeName}
        </span>
      </div>

      <div className="grid grid-cols-1 gap-3">
        {languages.map(language => (
          <div
            key={language.code}
            className={`relative border rounded-lg p-4 cursor-pointer transition-all ${
              currentLanguage === language.code
                ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
                : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
            } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
            onClick={() => !disabled && handleLanguageChange(language.code)}
          >
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 flex items-center justify-center text-lg">
                  {language.flag}
                </div>
              </div>
              <div className="ml-3 flex-1">
                <div className="flex items-center">
                  <input
                    type="radio"
                    name="language"
                    value={language.code}
                    checked={currentLanguage === language.code}
                    onChange={() => handleLanguageChange(language.code)}
                    disabled={disabled}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <label className="ml-2 text-sm font-medium text-gray-900 cursor-pointer">
                    {language.name}
                    <span className="text-gray-500 ml-1">({language.nativeName})</span>
                  </label>
                </div>
                <p className="text-xs text-gray-500 mt-1 ml-6">
                  {language.description}
                </p>
              </div>
            </div>

            {/* Language Details */}
            <div className="mt-3 ml-11">
              <div className="grid grid-cols-2 gap-4 text-xs">
                <div>
                  <span className="text-gray-500">Date format:</span>
                  <div className="font-mono text-gray-700">{language.dateFormat}</div>
                </div>
                <div>
                  <span className="text-gray-500">Numbers:</span>
                  <div className="font-mono text-gray-700">{language.numberFormat}</div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Language Information */}
      <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <span className="text-amber-500 text-sm">🌍</span>
          </div>
          <div className="ml-2">
            <h4 className="text-xs font-medium text-amber-800">Language Settings</h4>
            <ul className="text-xs text-amber-700 mt-1 space-y-1">
              <li>• Language affects interface text and formatting</li>
              <li>• Date and number formats change with language</li>
              <li>• OCR language is set separately in Processing settings</li>
              <li>• More languages will be added in future updates</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Current Selection Details */}
      {currentLangData && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
          <h4 className="text-sm font-medium text-gray-800 mb-2">
            Current Language Settings
          </h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Language:</span>
              <div className="font-medium">{currentLangData.flag} {currentLangData.name}</div>
            </div>
            <div>
              <span className="text-gray-600">Date Format:</span>
              <div className="font-mono text-gray-800">{currentLangData.dateFormat}</div>
            </div>
            <div>
              <span className="text-gray-600">Number Format:</span>
              <div className="font-mono text-gray-800">{currentLangData.numberFormat}</div>
            </div>
            <div>
              <span className="text-gray-600">Default Currency:</span>
              <div className="font-medium">{currentLangData.currency}</div>
            </div>
          </div>
        </div>
      )}

      {/* Future Languages Notice */}
      <div className="text-xs text-gray-500 text-center p-2 border border-dashed border-gray-300 rounded">
        🚧 Additional languages (German, French, Czech) coming soon
      </div>
    </div>
  );
}

export default LanguageSelector;
