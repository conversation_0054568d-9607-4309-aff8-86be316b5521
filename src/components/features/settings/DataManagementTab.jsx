import React, { useState, useEffect } from 'react';
import { dataManagementService } from '../../../services/DataManagementService.js';

// Import unified loading components
import { LoadingSpinner } from '../../ui/feedback/LoadingSpinner.jsx';
import { DEFAULT_SETTINGS } from '../../../utils/settingsSchema.js';
import { useSettings } from '../../../popup/hooks/useSettings.js';

/**
 * Data Management Tab Component
 * Provides data operations: clear, export, import, reset
 */
export function DataManagementTab() {
  const { settings, loadSettings, updateSection } = useSettings();
  const [storageStats, setStorageStats] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [showConfirmDialog, setShowConfirmDialog] = useState(null);

  // Load storage statistics on mount
  useEffect(() => {
    loadStorageStats();
  }, []);

  const loadStorageStats = async () => {
    try {
      setIsLoading(true);
      const stats = await dataManagementService.getStorageStats();
      setStorageStats(stats);
    } catch (error) {
      setMessage('❌ Failed to load storage statistics');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearData = async (options = {}) => {
    try {
      setIsLoading(true);
      setMessage('🗑️ Clearing data...');

      const result = await dataManagementService.clearAllData(options);

      if (result.success) {
        setMessage(`✅ ${result.message}`);
        await loadStorageStats();

        // Reload settings if they were cleared
        if (!options.keepSettings) {
          await loadSettings();
        }
      }
    } catch (error) {
      setMessage('❌ Failed to clear data: ' + error.message);
    } finally {
      setIsLoading(false);
      setShowConfirmDialog(null);
    }
  };

  const handleExportSettings = async () => {
    try {
      setIsLoading(true);
      setMessage('📤 Exporting settings...');

      const result = await dataManagementService.exportSettings(settings);

      if (result.success) {
        setMessage(`✅ Settings exported to ${result.filename}`);
      }
    } catch (error) {
      setMessage('❌ Failed to export settings: ' + error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleImportSettings = async (event) => {
    const file = event.target.files[0];
    if (!file) { return; }

    try {
      setIsLoading(true);
      setMessage('📥 Importing settings...');

      const result = await dataManagementService.importSettings(file);

      if (result.success) {
        // Apply imported settings
        for (const [section, data] of Object.entries(result.settings)) {
          if (section !== 'apiKeys') { // Don't import API keys for security
            await updateSection(section, data);
          }
        }

        setMessage('✅ Settings imported successfully');
        await loadSettings();
      }
    } catch (error) {
      setMessage('❌ Failed to import settings: ' + error.message);
    } finally {
      setIsLoading(false);
      event.target.value = ''; // Reset file input
    }
  };

  const handleResetSettings = async () => {
    try {
      setIsLoading(true);
      setMessage('🔄 Resetting to defaults...');

      const result = await dataManagementService.resetToDefaults(DEFAULT_SETTINGS);

      if (result.success) {
        setMessage('✅ Settings reset to defaults');
        await loadSettings();
      }
    } catch (error) {
      setMessage('❌ Failed to reset settings: ' + error.message);
    } finally {
      setIsLoading(false);
      setShowConfirmDialog(null);
    }
  };

  const clearMessage = () => {
    setTimeout(() => setMessage(''), 3000);
  };

  // Clear message after 3 seconds
  useEffect(() => {
    if (message) {
      clearMessage();
    }
  }, [message]);

  if (isLoading && !storageStats) {
    return (
      <div className="flex items-center justify-center py-8">
        <LoadingSpinner
          size="large"
          color="blue"
          message="Loading data management..."
        />
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Data Management</h3>
        <p className="text-gray-600 text-sm">
          Manage your stored data, backup settings, and control data retention
        </p>
      </div>

      {/* Storage Overview */}
      {storageStats && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 mb-3">Storage Overview</h4>
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{storageStats.totalSizeFormatted}</div>
              <div className="text-sm text-gray-600">Total Storage Used</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{storageStats.itemCount}</div>
              <div className="text-sm text-gray-600">Data Categories</div>
            </div>
          </div>

          <div className="space-y-2">
            {Object.entries(storageStats.items).map(([key, stats]) => (
              <div key={key} className="flex justify-between items-center text-sm">
                <span className="capitalize text-gray-700">{key.replace(/([A-Z])/g, ' $1')}</span>
                <span className="text-gray-500">{stats.sizeFormatted}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Data Operations */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Export Settings */}
        <div className="border border-gray-200 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 mb-2">📤 Export Settings</h4>
          <p className="text-sm text-gray-600 mb-3">
            Download your settings as a JSON file for backup or transfer
          </p>
          <button
            onClick={handleExportSettings}
            disabled={isLoading}
            className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Export Settings
          </button>
        </div>

        {/* Import Settings */}
        <div className="border border-gray-200 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 mb-2">📥 Import Settings</h4>
          <p className="text-sm text-gray-600 mb-3">
            Restore settings from a previously exported JSON file
          </p>
          <input
            type="file"
            accept=".json"
            onChange={handleImportSettings}
            disabled={isLoading}
            className="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          />
        </div>

        {/* Reset Settings */}
        <div className="border border-gray-200 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 mb-2">🔄 Reset Settings</h4>
          <p className="text-sm text-gray-600 mb-3">
            Reset all settings to default values
          </p>
          <button
            onClick={() => setShowConfirmDialog('reset')}
            disabled={isLoading}
            className="w-full bg-yellow-600 text-white px-4 py-2 rounded-md hover:bg-yellow-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Reset to Defaults
          </button>
        </div>

        {/* Clear Data */}
        <div className="border border-gray-200 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 mb-2">🗑️ Clear Data</h4>
          <p className="text-sm text-gray-600 mb-3">
            Remove stored documents and processing history
          </p>
          <button
            onClick={() => setShowConfirmDialog('clear')}
            disabled={isLoading}
            className="w-full bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Clear All Data
          </button>
        </div>
      </div>

      {/* Status Message */}
      {message && (
        <div className="text-center">
          <span className={`text-sm ${message.startsWith('✅') ? 'text-green-600' : message.startsWith('❌') ? 'text-red-600' : 'text-blue-600'}`}>
            {message}
          </span>
        </div>
      )}

      {/* Confirmation Dialogs */}
      {showConfirmDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {showConfirmDialog === 'reset' ? 'Reset Settings?' : 'Clear All Data?'}
            </h3>
            <p className="text-gray-600 mb-6">
              {showConfirmDialog === 'reset'
                ? 'This will reset all settings to their default values. Your API keys will be preserved.'
                : 'This will permanently delete all stored documents, processing history, and cache. This action cannot be undone.'
              }
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => setShowConfirmDialog(null)}
                className="flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400"
              >
                Cancel
              </button>
              <button
                onClick={() => showConfirmDialog === 'reset' ? handleResetSettings() : handleClearData({ keepSettings: true, keepApiKeys: true })}
                className={`flex-1 px-4 py-2 rounded-md text-white ${
                  showConfirmDialog === 'reset'
                    ? 'bg-yellow-600 hover:bg-yellow-700'
                    : 'bg-red-600 hover:bg-red-700'
                }`}
              >
                {showConfirmDialog === 'reset' ? 'Reset' : 'Clear Data'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
