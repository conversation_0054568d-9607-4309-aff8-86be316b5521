/**
 * JSON Configuration Input Component
 *
 * UI component for loading configuration from JSON text input.
 * Allows users to paste JSON configuration and load it into the system.
 *
 * ASSIGNMENT-054: Comprehensive Settings UI Enhancement
 * Epic: EPIC-004 - Settings & Configuration Management
 */

import { useState } from 'react';

/**
 * JSON Configuration Input Component
 */
export function JsonConfigInput({ onConfigurationLoaded, onError, disabled = false }) {
  const [jsonText, setJsonText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [validationError, setValidationError] = useState('');
  const [showPreview, setShowPreview] = useState(false);
  const [parsedConfig, setParsedConfig] = useState(null);

  /**
   * Validate and parse JSON input
   */
  const validateJson = (text) => {
    try {
      if (!text.trim()) {
        return { valid: false, error: 'JSON input is empty' };
      }

      const parsed = JSON.parse(text);

      // Basic validation for expected configuration structure
      if (typeof parsed !== 'object' || parsed === null) {
        return { valid: false, error: 'Configuration must be a JSON object' };
      }

      return { valid: true, parsed };
    } catch (error) {
      return { valid: false, error: `Invalid JSON: ${error.message}` };
    }
  };

  /**
   * Handle JSON text change
   */
  const handleJsonChange = (event) => {
    const text = event.target.value;
    setJsonText(text);
    setValidationError('');
    setParsedConfig(null);
    setShowPreview(false);

    // Real-time validation
    if (text.trim()) {
      const validation = validateJson(text);
      if (!validation.valid) {
        setValidationError(validation.error);
      } else {
        setParsedConfig(validation.parsed);
      }
    }
  };

  /**
   * Preview configuration
   */
  const handlePreview = () => {
    const validation = validateJson(jsonText);
    if (validation.valid) {
      setParsedConfig(validation.parsed);
      setShowPreview(true);
      setValidationError('');
    } else {
      setValidationError(validation.error);
      setShowPreview(false);
    }
  };

  /**
   * Load configuration
   */
  const handleLoadConfiguration = async () => {
    try {
      setIsLoading(true);
      setValidationError('');

      const validation = validateJson(jsonText);
      if (!validation.valid) {
        setValidationError(validation.error);
        return;
      }

      console.log('🔧 JsonConfigInput: Loading configuration from JSON input');

      // Call the parent callback with the parsed configuration
      if (onConfigurationLoaded) {
        await onConfigurationLoaded(validation.parsed, 'json_text_input');
      }

      console.log('✅ JsonConfigInput: Configuration loaded successfully');

    } catch (error) {
      console.error('❌ JsonConfigInput: Failed to load configuration:', error);
      setValidationError(`Failed to load configuration: ${error.message}`);

      if (onError) {
        onError(error, 'json_text_input');
      }
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Clear input
   */
  const handleClear = () => {
    setJsonText('');
    setValidationError('');
    setParsedConfig(null);
    setShowPreview(false);
  };

  /**
   * Load example configuration
   */
  const handleLoadExample = () => {
    const exampleConfig = {
      company: {
        name: 'Example Company Ltd',
        taxId: '1234567890',
        email: '<EMAIL>',
        phone: '+48 ***********'
      },
      apiKeys: {
        deepseek: {
          key: 'your-deepseek-api-key-here'
        },
        openai: {
          key: 'your-openai-api-key-here'
        }
      },
      features: {
        subscriptionSystem: true,
        paymentProcessing: false,
        enterpriseFeatures: false
      },
      display: {
        currency: 'PLN',
        language: 'en',
        dateFormat: 'DD/MM/YYYY'
      }
    };

    setJsonText(JSON.stringify(exampleConfig, null, 2));
    setParsedConfig(exampleConfig);
    setValidationError('');
  };

  return (
    <div className="bg-white shadow rounded-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">JSON Configuration Input</h3>
        <div className="flex space-x-2">
          <button
            onClick={handleLoadExample}
            disabled={disabled || isLoading}
            className="bg-gray-600 text-white px-3 py-1 rounded text-sm font-medium hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 disabled:opacity-50"
          >
            📋 Load Example
          </button>
          <button
            onClick={handleClear}
            disabled={disabled || isLoading}
            className="bg-red-600 text-white px-3 py-1 rounded text-sm font-medium hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50"
          >
            🗑️ Clear
          </button>
        </div>
      </div>

      {/* JSON Text Area */}
      <div className="mb-4">
        <label htmlFor="json-input" className="block text-sm font-medium text-gray-700 mb-2">
          Configuration JSON
        </label>
        <textarea
          id="json-input"
          value={jsonText}
          onChange={handleJsonChange}
          disabled={disabled || isLoading}
          placeholder="Paste your JSON configuration here..."
          className={`w-full h-64 px-3 py-2 border rounded-md text-sm font-mono resize-vertical focus:outline-none focus:ring-2 focus:ring-blue-500 ${
            validationError ? 'border-red-300 bg-red-50' : 'border-gray-300'
          } disabled:opacity-50`}
        />
      </div>

      {/* Validation Error */}
      {validationError && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-800">❌ {validationError}</p>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex space-x-3 mb-4">
        <button
          onClick={handlePreview}
          disabled={disabled || isLoading || !jsonText.trim() || !!validationError}
          className="bg-blue-600 text-white px-4 py-2 rounded text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
        >
          👁️ Preview
        </button>
        <button
          onClick={handleLoadConfiguration}
          disabled={disabled || isLoading || !jsonText.trim() || !!validationError}
          className="bg-green-600 text-white px-4 py-2 rounded text-sm font-medium hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50"
        >
          {isLoading ? '⏳ Loading...' : '📥 Load Configuration'}
        </button>
      </div>

      {/* Configuration Preview */}
      {showPreview && parsedConfig && (
        <div className="mt-4 p-4 bg-gray-50 border border-gray-200 rounded-md">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Configuration Preview:</h4>
          <div className="space-y-2 text-sm">
            {parsedConfig.company && (
              <div>
                <span className="font-medium">Company:</span> {parsedConfig.company.name || 'Not specified'}
              </div>
            )}
            {parsedConfig.apiKeys && (
              <div>
                <span className="font-medium">API Keys:</span> {Object.keys(parsedConfig.apiKeys).length} configured
              </div>
            )}
            {parsedConfig.features && (
              <div>
                <span className="font-medium">Features:</span> {Object.keys(parsedConfig.features).length} settings
              </div>
            )}
            <div>
              <span className="font-medium">Total Properties:</span> {Object.keys(parsedConfig).length}
            </div>
          </div>
        </div>
      )}

      {/* Help Text */}
      <div className="mt-4 p-3 bg-blue-50 rounded-md">
        <h4 className="text-sm font-medium text-blue-800 mb-1">JSON Configuration Format:</h4>
        <ul className="text-sm text-blue-600 space-y-1">
          <li>• <strong>company:</strong> Company information (name, taxId, email, phone)</li>
          <li>• <strong>apiKeys:</strong> API keys for services (deepseek, openai, etc.)</li>
          <li>• <strong>features:</strong> Feature flags and settings</li>
          <li>• <strong>display:</strong> Display preferences (currency, language, dateFormat)</li>
        </ul>
      </div>
    </div>
  );
}

export default JsonConfigInput;
