/**
 * Environment Settings Component
 *
 * UI component for viewing and managing environment configuration settings
 * including API keys, company details, and application settings.
 *
 * ASSIGNMENT-039: Environment Configuration Setup
 * Epic: EPIC-B01 - Subscription & Monetization System
 */

import React, { useState, useEffect } from 'react';
import { environmentConfig } from '../../../services/EnvironmentConfigService.js';
import { SettingsSourceSelector } from './SettingsSourceSelector.jsx';
import { useSettings } from '../../../popup/hooks/useSettings.js';

/**
 * Environment Settings Component
 * Displays and manages environment configuration
 */
export function EnvironmentSettings() {
  const { environmentConfiguration, loadEnvironmentConfiguration, isLoading } = useSettings();
  const [config, setConfig] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('source');
  const [sourceLoadMessage, setSourceLoadMessage] = useState('');

  useEffect(() => {
    loadConfiguration();
  }, []);

  // Update config when environmentConfiguration changes
  useEffect(() => {
    if (environmentConfiguration && Object.keys(environmentConfiguration).length > 0) {
      setConfig(environmentConfiguration);
      setLoading(false);
      console.log('✅ EnvironmentSettings: Updated config from useSettings hook');
    }
  }, [environmentConfiguration]);

  /**
   * Load configuration from service
   */
  const loadConfiguration = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!environmentConfig.isLoaded) {
        await environmentConfig.initialize();
      }

      const configData = environmentConfig.getAll();
      setConfig(configData);

      // Development mode: Log all settings values to console
      if (isDevelopmentMode()) {
        console.info('🔍 EnvironmentSettings: Development Mode - All Settings Values');
        console.info('📋 Full Config Structure:', JSON.stringify(configData, null, 2));
        console.info('📋 API Keys:', JSON.stringify({
          deepseek: configData.apiKeys?.deepseek?.key || 'Not set',
          stripe: configData.apiKeys?.stripe?.publishableKey || 'Not set',
          fakturownia: configData.apiKeys?.fakturownia?.token || 'Not set'
        }));
        console.info('📋 Company Information:', JSON.stringify({
          name: configData.company?.name || 'Not set',
          legalName: configData.company?.legalName || 'Not set',
          nip: configData.company?.nip || 'Not set',
          email: configData.company?.contact?.email || 'Not set',
          phone: configData.company?.contact?.phone || 'Not set',
          address: configData.company?.address || 'Not set'
        }));
        console.info('📋 Feature Flags:', JSON.stringify(configData.features || {}));
        console.info('📋 Subscription Configuration:', JSON.stringify(configData.subscription || {}));
        console.info('📋 Localization Settings:', JSON.stringify(configData.localization || {}));
        console.info('🔍 EnvironmentSettings: End of settings values');
      }

    } catch (err) {
      console.error('Failed to load environment configuration:', err);
      setError('Failed to load configuration. Please check your environment settings.');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Reload configuration
   */
  const handleReload = async () => {
    await environmentConfig.reload();
    await loadConfiguration();
  };

  /**
   * Handle configuration loaded from source selector
   */
  const handleConfigurationLoaded = async (newConfig, sourceId) => {
    console.log(`✅ EnvironmentSettings: Configuration loaded from ${sourceId}`);

    // Update the environment configuration in the useSettings hook
    try {
      await loadEnvironmentConfiguration(sourceId);
      setSourceLoadMessage(`✅ Configuration loaded successfully from ${sourceId}`);
    } catch (error) {
      console.error('Failed to update environment configuration:', error);
      setSourceLoadMessage(`❌ Failed to load configuration from ${sourceId}: ${error.message}`);
    }

    setTimeout(() => setSourceLoadMessage(''), 5000);
  };

  /**
   * Handle source loading error
   */
  const handleSourceError = (error, sourceId) => {
    setError(`Failed to load from ${sourceId}: ${error.message}`);
    setSourceLoadMessage(`❌ Failed to load from ${sourceId}`);
    setTimeout(() => setSourceLoadMessage(''), 5000);
  };

  /**
   * Check if we're in development mode
   */
  const isDevelopmentMode = () => {
    return environmentConfig._isDevelopmentMode?.() ||
           (typeof window !== 'undefined' && window.location.protocol === 'file:') ||
           (config?.app?.debugMode === true) ||
           (config?.app?.environment === 'development');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" />
        <span className="ml-3 text-gray-600">Loading configuration...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Configuration Error</h3>
            <p className="mt-1 text-sm text-red-700">{error}</p>
          </div>
        </div>
        <div className="mt-4">
          <button
            onClick={handleReload}
            className="bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Environment Configuration</h2>
            <p className="mt-1 text-sm text-gray-600">
              View and manage your environment settings, API keys, and company information.
            </p>
          </div>
          <button
            onClick={handleReload}
            className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Reload Configuration
          </button>
        </div>
      </div>

      {/* Source Load Message */}
      {sourceLoadMessage && (
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
          <p className="text-sm text-blue-800">{sourceLoadMessage}</p>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'source', name: 'Configuration Source', icon: '🔧' },
            { id: 'debug', name: 'Debug Values', icon: '🐛' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {activeTab === 'source' && (
          <SettingsSourceSelector
            onConfigurationLoaded={handleConfigurationLoaded}
            onError={handleSourceError}
          />
        )}
        {activeTab === 'debug' && <DebugValuesTab config={config} />}
      </div>
    </div>
  );
}

/**
 * Debug Values Tab - Shows all environment variables with actual values
 */
function DebugValuesTab({ config = {} }) {
  const [showSensitive, setShowSensitive] = useState(false);
  const [actualConfig, setActualConfig] = useState(config);

  // Update config when showSensitive changes
  useEffect(() => {
    const loadConfigData = async () => {
      if (showSensitive) {
        // Load unmasked data
        try {
          const unmaskedConfig = environmentConfig.getAllUnmasked();
          setActualConfig(unmaskedConfig);
        } catch (error) {
          console.warn('Failed to load unmasked config:', error);
          setActualConfig(config);
        }
      } else {
        // Use the original masked config
        setActualConfig(config);
      }
    };

    loadConfigData();
  }, [showSensitive, config]);

  // Get all environment variables from the config
  const getAllEnvVars = () => {
    const envVars = [];

    // Recursively extract all key-value pairs
    const extractValues = (obj, prefix = '') => {
      if (obj && typeof obj === 'object') {
        Object.entries(obj).forEach(([key, value]) => {
          const fullKey = prefix ? `${prefix}.${key}` : key;

          if (value && typeof value === 'object' && !Array.isArray(value)) {
            extractValues(value, fullKey);
          } else {
            envVars.push({
              key: fullKey,
              value: value,
              type: typeof value,
              sensitive: fullKey.toLowerCase().includes('key') ||
                        fullKey.toLowerCase().includes('secret') ||
                        fullKey.toLowerCase().includes('token') ||
                        fullKey.toLowerCase().includes('password')
            });
          }
        });
      }
    };

    extractValues(actualConfig);
    return envVars.sort((a, b) => a.key.localeCompare(b.key));
  };

  const envVars = getAllEnvVars();

  return (
    <div className="bg-white shadow rounded-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">Debug: All Environment Variables</h3>
        <div className="flex items-center space-x-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={showSensitive}
              onChange={(e) => setShowSensitive(e.target.checked)}
              className="mr-2"
            />
            <span className="text-sm text-gray-700">Show sensitive values</span>
          </label>
          <span className="text-sm text-gray-500">
            Total: {envVars.length} variables
          </span>
        </div>
      </div>

      {envVars.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">No environment variables found</p>
        </div>
      ) : (
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {envVars.map((envVar, index) => (
            <div key={index} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2">
                  <span className="font-mono text-sm font-medium text-gray-900 truncate">
                    {envVar.key}
                  </span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    envVar.sensitive
                      ? 'bg-red-100 text-red-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {envVar.type}
                  </span>
                  {envVar.sensitive && (
                    <span className="px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                      sensitive
                    </span>
                  )}
                </div>
                <div className="mt-1">
                  <span className="font-mono text-sm text-gray-600">
                    {String(envVar.value || 'undefined')}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      <div className="mt-4 p-3 bg-yellow-50 rounded-md">
        <h4 className="text-sm font-medium text-yellow-800 mb-1">Debug Information:</h4>
        <ul className="text-sm text-yellow-600 space-y-1">
          <li>• This tab shows all environment variables currently loaded in the system</li>
          <li>• Sensitive values (API keys, secrets, tokens) are masked by default</li>
          <li>• Use the checkbox above to reveal sensitive values for debugging</li>
          <li>• If values show as "Not configured", check your environment configuration</li>
        </ul>
      </div>
    </div>
  );
}

/**
 * Configuration Field Component
 */
function ConfigField({ label, value, sensitive = false }) {
  const displayValue = sensitive && value ? '***masked***' : value || 'Not configured';

  return (
    <div>
      <label className="block text-xs font-medium text-gray-700 mb-1">{label}</label>
      <div className={`px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm ${
        !value ? 'text-gray-400 italic' : 'text-gray-900'
      }`}>
        {displayValue}
      </div>
    </div>
  );
}

export default EnvironmentSettings;
