import React, { useState, useRef } from 'react';

/**
 * Logo Upload Component
 * Handles company logo upload with image optimization
 */
export function LogoUpload({
  logoData = '',
  onChange,
  errors = {},
  disabled = false
}) {
  const [isUploading, setIsUploading] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef(null);

  // Maximum file size (2MB)
  const MAX_FILE_SIZE = 2 * 1024 * 1024;

  // Accepted file types
  const ACCEPTED_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];

  // Handle file selection
  const handleFileSelect = (file) => {
    if (!file) { return; }

    // Validate file type
    if (!ACCEPTED_TYPES.includes(file.type)) {
      alert('Please select a valid image file (JPEG, PNG, GIF, or WebP)');
      return;
    }

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      alert('File size must be less than 2MB');
      return;
    }

    setIsUploading(true);

    // Read and process the file
    const reader = new FileReader();
    reader.onload = (e) => {
      const img = new Image();
      img.onload = () => {
        // Resize image if needed
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // Calculate new dimensions (max 300x300, maintain aspect ratio)
        const maxSize = 300;
        let { width, height } = img;

        if (width > height) {
          if (width > maxSize) {
            height = (height * maxSize) / width;
            width = maxSize;
          }
        } else {
          if (height > maxSize) {
            width = (width * maxSize) / height;
            height = maxSize;
          }
        }

        canvas.width = width;
        canvas.height = height;

        // Draw and compress
        ctx.drawImage(img, 0, 0, width, height);
        const compressedDataUrl = canvas.toDataURL('image/jpeg', 0.8);

        // Update parent component
        if (onChange) {
          onChange(compressedDataUrl);
        }

        setIsUploading(false);
      };

      img.onerror = () => {
        alert('Failed to process image. Please try a different file.');
        setIsUploading(false);
      };

      img.src = e.target.result;
    };

    reader.onerror = () => {
      alert('Failed to read file. Please try again.');
      setIsUploading(false);
    };

    reader.readAsDataURL(file);
  };

  // Handle drag events
  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  // Handle drop
  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (disabled) { return; }

    const files = e.dataTransfer.files;
    if (files && files[0]) {
      handleFileSelect(files[0]);
    }
  };

  // Handle file input change
  const handleInputChange = (e) => {
    const files = e.target.files;
    if (files && files[0]) {
      handleFileSelect(files[0]);
    }
  };

  // Remove logo
  const handleRemove = () => {
    if (onChange) {
      onChange('');
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Open file dialog
  const openFileDialog = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <label className="block text-sm font-medium text-gray-700">
          Company Logo
        </label>
        {logoData && (
          <button
            type="button"
            onClick={handleRemove}
            disabled={disabled}
            className="text-sm text-red-600 hover:text-red-800 disabled:opacity-50"
          >
            Remove Logo
          </button>
        )}
      </div>

      {/* Upload Area */}
      <div
        className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          dragActive ? 'border-blue-400 bg-blue-50' : 'border-gray-300'
        } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:border-gray-400'}`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={openFileDialog}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={ACCEPTED_TYPES.join(',')}
          onChange={handleInputChange}
          disabled={disabled}
          className="hidden"
          aria-describedby="logo-help"
        />

        {isUploading ? (
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mb-2" />
            <p className="text-sm text-gray-600">Processing image...</p>
          </div>
        ) : logoData ? (
          <div className="flex flex-col items-center">
            <img
              src={logoData}
              alt="Company Logo"
              className="max-w-32 max-h-32 object-contain mb-2 rounded"
            />
            <p className="text-sm text-gray-600">Click to change logo</p>
          </div>
        ) : (
          <div className="flex flex-col items-center">
            <svg
              className="mx-auto h-12 w-12 text-gray-400"
              stroke="currentColor"
              fill="none"
              viewBox="0 0 48 48"
              aria-hidden="true"
            >
              <path
                d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                strokeWidth={2}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            <div className="mt-2">
              <p className="text-sm text-gray-600">
                <span className="font-medium text-blue-600">Click to upload</span> or drag and drop
              </p>
              <p className="text-xs text-gray-500">PNG, JPG, GIF, WebP up to 2MB</p>
            </div>
          </div>
        )}
      </div>

      <p id="logo-help" className="text-xs text-gray-500">
        Upload your company logo for professional document headers.
        Images will be automatically resized and optimized.
      </p>

      {errors.logo && (
        <p className="text-sm text-red-600">
          {errors.logo}
        </p>
      )}

      {/* Logo Guidelines */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
        <h4 className="text-xs font-medium text-gray-700 mb-2">Logo Guidelines</h4>
        <ul className="text-xs text-gray-600 space-y-1">
          <li>• Square or rectangular logos work best</li>
          <li>• High contrast logos are recommended</li>
          <li>• Transparent backgrounds (PNG) are preferred</li>
          <li>• Maximum file size: 2MB</li>
          <li>• Supported formats: JPEG, PNG, GIF, WebP</li>
        </ul>
      </div>
    </div>
  );
}

export default LogoUpload;
