import React, { useState } from 'react';
import { useSettings } from '../../../popup/hooks/useSettings.js';

/**
 * Features and Subscription Tab Component
 * Consolidates Features and Subscription settings from EnvironmentSettings
 */
export function FeaturesAndSubscriptionTab() {
  const { environmentConfiguration } = useSettings();
  const [activeSubTab, setActiveSubTab] = useState('features');

  const config = environmentConfiguration || {};

  const subTabs = [
    { id: 'features', name: 'Features', icon: '⚡' },
    { id: 'subscription', name: 'Subscription', icon: '💳' }
  ];

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Features & Subscription</h2>
        <p className="mt-1 text-sm text-gray-600">
          Manage feature flags and subscription settings.
        </p>
      </div>

      {/* Sub-tab Navigation */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {subTabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveSubTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeSubTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Sub-tab Content */}
      <div className="space-y-6">
        {activeSubTab === 'features' && <FeaturesTab config={config.features} />}
        {activeSubTab === 'subscription' && <SubscriptionTab config={config.subscription} />}
      </div>
    </div>
  );
}

/**
 * Features Tab - Shows feature flags and toggles
 */
function FeaturesTab({ config = {} }) {
  return (
    <div className="bg-white shadow rounded-lg p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Feature Configuration</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-3">Core Features</h4>
          <div className="space-y-3">
            <FeatureToggle
              label="PDF Processing"
              value={config.pdfProcessing?.enabled}
              description="Enable PDF document processing"
            />
            <FeatureToggle
              label="OCR Processing"
              value={config.ocrProcessing?.enabled}
              description="Enable OCR text extraction"
            />
            <FeatureToggle
              label="AI Analysis"
              value={config.aiAnalysis?.enabled}
              description="Enable AI-powered document analysis"
            />
            <FeatureToggle
              label="RAG Integration"
              value={config.ragIntegration?.enabled}
              description="Enable RAG-based document linking"
            />
          </div>
        </div>

        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-3">Advanced Features</h4>
          <div className="space-y-3">
            <FeatureToggle
              label="Bulk Processing"
              value={config.bulkProcessing?.enabled}
              description="Enable bulk document processing"
            />
            <FeatureToggle
              label="Export Features"
              value={config.exportFeatures?.enabled}
              description="Enable data export functionality"
            />
            <FeatureToggle
              label="Analytics"
              value={config.analytics?.enabled}
              description="Enable usage analytics"
            />
            <FeatureToggle
              label="Debug Mode"
              value={config.debugMode?.enabled}
              description="Enable debug logging and tools"
            />
          </div>
        </div>
      </div>

      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h4 className="text-sm font-medium text-blue-800 mb-2">Feature Information</h4>
        <ul className="text-sm text-blue-600 space-y-1">
          <li>• Features are controlled by environment configuration</li>
          <li>• Some features may require specific subscription tiers</li>
          <li>• Changes to features may require extension reload</li>
        </ul>
      </div>
    </div>
  );
}

/**
 * Subscription Tab - Shows subscription and billing information
 */
function SubscriptionTab({ config = {} }) {
  return (
    <div className="bg-white shadow rounded-lg p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Subscription & Billing</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-3">Current Plan</h4>
          <div className="space-y-3">
            <ConfigField label="Subscription Tier" value={config.currentTier || 'Free'} />
            <ConfigField label="Status" value={config.status || 'Active'} />
            <ConfigField label="Billing Cycle" value={config.billingCycle || 'Monthly'} />
            <ConfigField label="Next Billing Date" value={config.nextBillingDate || 'N/A'} />
          </div>
        </div>

        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-3">Usage Limits</h4>
          <div className="space-y-3">
            <ConfigField
              label="Documents Processed"
              value={`${config.usage?.documentsProcessed || 0} / ${config.limits?.documentsPerMonth || '∞'}`}
            />
            <ConfigField
              label="API Calls"
              value={`${config.usage?.apiCalls || 0} / ${config.limits?.apiCallsPerMonth || '∞'}`}
            />
            <ConfigField
              label="Storage Used"
              value={`${config.usage?.storageUsed || '0 MB'} / ${config.limits?.storageLimit || '∞'}`}
            />
          </div>
        </div>
      </div>

      <div className="mt-6 p-4 bg-green-50 rounded-lg">
        <h4 className="text-sm font-medium text-green-800 mb-2">Subscription Information</h4>
        <ul className="text-sm text-green-600 space-y-1">
          <li>• Subscription details are loaded from environment configuration</li>
          <li>• Usage tracking helps monitor your plan limits</li>
          <li>• Contact support for plan changes or billing questions</li>
        </ul>
      </div>
    </div>
  );
}

/**
 * Feature Toggle Component
 */
function FeatureToggle({ label, value, description }) {
  const isEnabled = value === true || value === 'true';

  return (
    <div className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
      <div className="flex-1">
        <div className="flex items-center">
          <span className="font-medium text-gray-900">{label}</span>
          <span className={`ml-2 px-2 py-1 text-xs rounded-full ${
            isEnabled
              ? 'bg-green-100 text-green-800'
              : 'bg-gray-100 text-gray-600'
          }`}>
            {isEnabled ? 'Enabled' : 'Disabled'}
          </span>
        </div>
        {description && (
          <p className="text-sm text-gray-600 mt-1">{description}</p>
        )}
      </div>
    </div>
  );
}

/**
 * Configuration Field Component
 */
function ConfigField({ label, value }) {
  return (
    <div className="flex justify-between items-center py-2 border-b border-gray-100">
      <span className="text-sm font-medium text-gray-700">{label}:</span>
      <span className="text-sm text-gray-900 font-mono">
        {value || 'Not configured'}
      </span>
    </div>
  );
}

export default FeaturesAndSubscriptionTab;
