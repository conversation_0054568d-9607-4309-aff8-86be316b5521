import React, { useState, useEffect } from 'react';

/**
 * Processing Preferences Component
 * Handles OCR quality, analysis depth, auto-processing, and batch settings
 */
export function ProcessingPreferences({
  processingData = {},
  onChange,
  errors = {},
  disabled = false
}) {
  const [localData, setLocalData] = useState({
    ocrLanguage: 'pol',
    ocrQuality: 'balanced',
    aiProvider: 'deepseek',
    analysisDepth: 'standard',
    autoProcess: true,
    cacheEnabled: true,
    batchSize: 5,
    timeoutSeconds: 30,
    retryAttempts: 3,
    ...processingData
  });

  // OCR Language options
  const ocrLanguages = [
    { code: 'pol', name: 'Polish', flag: '🇵🇱', description: 'Optimized for Polish documents' },
    { code: 'eng', name: 'English', flag: '🇺🇸', description: 'Optimized for English documents' },
    { code: 'pol+eng', name: 'Polish + English', flag: '🌍', description: 'Multi-language recognition' }
  ];

  // OCR Quality options
  const ocrQualityOptions = [
    {
      value: 'fast',
      name: 'Fast',
      icon: '⚡',
      description: 'Quick processing with good accuracy',
      processingTime: '~2-5 seconds',
      accuracy: '85-90%'
    },
    {
      value: 'balanced',
      name: 'Balanced',
      icon: '⚖️',
      description: 'Good balance of speed and accuracy',
      processingTime: '~5-10 seconds',
      accuracy: '90-95%'
    },
    {
      value: 'high',
      name: 'High Quality',
      icon: '🎯',
      description: 'Best accuracy for complex documents',
      processingTime: '~10-20 seconds',
      accuracy: '95-98%'
    }
  ];

  // AI Provider options
  const aiProviders = [
    {
      value: 'deepseek',
      name: 'DeepSeek',
      icon: '🧠',
      description: 'Advanced AI for document analysis',
      features: ['VAT extraction', 'Invoice parsing', 'Data validation']
    },
    {
      value: 'openai',
      name: 'OpenAI',
      icon: '🤖',
      description: 'GPT-powered document understanding',
      features: ['Natural language processing', 'Complex document analysis']
    }
  ];

  // Analysis Depth options
  const analysisDepthOptions = [
    {
      value: 'basic',
      name: 'Basic',
      icon: '📄',
      description: 'Extract essential information only',
      features: ['VAT numbers', 'Amounts', 'Dates']
    },
    {
      value: 'standard',
      name: 'Standard',
      icon: '📊',
      description: 'Comprehensive data extraction',
      features: ['All basic fields', 'Company details', 'Line items']
    },
    {
      value: 'comprehensive',
      name: 'Comprehensive',
      icon: '🔍',
      description: 'Deep analysis with validation',
      features: ['All standard fields', 'Data validation', 'Anomaly detection']
    }
  ];

  // Update local data when props change
  useEffect(() => {
    setLocalData(prev => ({
      ...prev,
      ...processingData
    }));
  }, [processingData]);

  // Handle input changes
  const handleInputChange = (field, value) => {
    const newData = { ...localData, [field]: value };
    setLocalData(newData);

    // Notify parent component
    if (onChange) {
      onChange(newData);
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <span className="text-green-500 text-xl">⚙️</span>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-green-800">Processing Preferences</h3>
            <p className="text-sm text-green-700 mt-1">
              Configure document processing behavior, OCR settings, and AI analysis options.
              These settings affect processing speed and accuracy.
            </p>
          </div>
        </div>
      </div>

      {/* OCR Settings */}
      <div>
        <h4 className="text-lg font-medium text-gray-900 mb-4">OCR (Text Recognition)</h4>

        {/* OCR Language */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-3">
            OCR Language
          </label>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            {ocrLanguages.map(language => (
              <div
                key={language.code}
                className={`border rounded-lg p-3 cursor-pointer transition-all ${
                  localData.ocrLanguage === language.code
                    ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                onClick={() => !disabled && handleInputChange('ocrLanguage', language.code)}
              >
                <div className="flex items-center">
                  <input
                    type="radio"
                    name="ocrLanguage"
                    value={language.code}
                    checked={localData.ocrLanguage === language.code}
                    onChange={() => handleInputChange('ocrLanguage', language.code)}
                    disabled={disabled}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <span className="ml-2 text-lg">{language.flag}</span>
                  <span className="ml-2 text-sm font-medium">{language.name}</span>
                </div>
                <p className="text-xs text-gray-500 mt-1 ml-6">{language.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* OCR Quality */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            OCR Quality Level
          </label>
          <div className="space-y-3">
            {ocrQualityOptions.map(quality => (
              <div
                key={quality.value}
                className={`border rounded-lg p-4 cursor-pointer transition-all ${
                  localData.ocrQuality === quality.value
                    ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                onClick={() => !disabled && handleInputChange('ocrQuality', quality.value)}
              >
                <div className="flex items-start">
                  <input
                    type="radio"
                    name="ocrQuality"
                    value={quality.value}
                    checked={localData.ocrQuality === quality.value}
                    onChange={() => handleInputChange('ocrQuality', quality.value)}
                    disabled={disabled}
                    className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <div className="ml-3 flex-1">
                    <div className="flex items-center">
                      <span className="text-lg mr-2">{quality.icon}</span>
                      <span className="text-sm font-medium">{quality.name}</span>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">{quality.description}</p>
                    <div className="flex space-x-4 mt-2 text-xs">
                      <span className="text-gray-600">
                        <strong>Time:</strong> {quality.processingTime}
                      </span>
                      <span className="text-gray-600">
                        <strong>Accuracy:</strong> {quality.accuracy}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* AI Analysis Settings */}
      <div>
        <h4 className="text-lg font-medium text-gray-900 mb-4">AI Analysis</h4>

        {/* AI Provider */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-3">
            AI Provider
          </label>
          <div className="space-y-3">
            {aiProviders.map(provider => (
              <div
                key={provider.value}
                className={`border rounded-lg p-4 cursor-pointer transition-all ${
                  localData.aiProvider === provider.value
                    ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                onClick={() => !disabled && handleInputChange('aiProvider', provider.value)}
              >
                <div className="flex items-start">
                  <input
                    type="radio"
                    name="aiProvider"
                    value={provider.value}
                    checked={localData.aiProvider === provider.value}
                    onChange={() => handleInputChange('aiProvider', provider.value)}
                    disabled={disabled}
                    className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <div className="ml-3 flex-1">
                    <div className="flex items-center">
                      <span className="text-lg mr-2">{provider.icon}</span>
                      <span className="text-sm font-medium">{provider.name}</span>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">{provider.description}</p>
                    <div className="flex flex-wrap gap-1 mt-2">
                      {provider.features.map(feature => (
                        <span key={feature} className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                          {feature}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Analysis Depth */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Analysis Depth
          </label>
          <div className="space-y-3">
            {analysisDepthOptions.map(depth => (
              <div
                key={depth.value}
                className={`border rounded-lg p-4 cursor-pointer transition-all ${
                  localData.analysisDepth === depth.value
                    ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                onClick={() => !disabled && handleInputChange('analysisDepth', depth.value)}
              >
                <div className="flex items-start">
                  <input
                    type="radio"
                    name="analysisDepth"
                    value={depth.value}
                    checked={localData.analysisDepth === depth.value}
                    onChange={() => handleInputChange('analysisDepth', depth.value)}
                    disabled={disabled}
                    className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <div className="ml-3 flex-1">
                    <div className="flex items-center">
                      <span className="text-lg mr-2">{depth.icon}</span>
                      <span className="text-sm font-medium">{depth.name}</span>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">{depth.description}</p>
                    <div className="flex flex-wrap gap-1 mt-2">
                      {depth.features.map(feature => (
                        <span key={feature} className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded">
                          {feature}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Processing Options */}
      <div>
        <h4 className="text-lg font-medium text-gray-900 mb-4">Processing Options</h4>
        <div className="space-y-4">
          {/* Auto Process */}
          <div className="flex items-center">
            <input
              id="auto-process"
              type="checkbox"
              checked={localData.autoProcess}
              onChange={(e) => handleInputChange('autoProcess', e.target.checked)}
              disabled={disabled}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="auto-process" className="ml-2 text-sm text-gray-700">
              Auto-process uploaded documents
            </label>
          </div>

          {/* Cache Enabled */}
          <div className="flex items-center">
            <input
              id="cache-enabled"
              type="checkbox"
              checked={localData.cacheEnabled}
              onChange={(e) => handleInputChange('cacheEnabled', e.target.checked)}
              disabled={disabled}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="cache-enabled" className="ml-2 text-sm text-gray-700">
              Enable result caching for faster repeated processing
            </label>
          </div>

          {/* Advanced Settings */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
            <div>
              <label htmlFor="batch-size" className="block text-sm font-medium text-gray-700 mb-2">
                Batch Size
              </label>
              <select
                id="batch-size"
                value={localData.batchSize}
                onChange={(e) => handleInputChange('batchSize', parseInt(e.target.value))}
                disabled={disabled}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value={1}>1 document</option>
                <option value={3}>3 documents</option>
                <option value={5}>5 documents</option>
                <option value={10}>10 documents</option>
              </select>
              <p className="text-xs text-gray-500 mt-1">Documents processed simultaneously</p>
            </div>

            <div>
              <label htmlFor="timeout" className="block text-sm font-medium text-gray-700 mb-2">
                Timeout (seconds)
              </label>
              <select
                id="timeout"
                value={localData.timeoutSeconds}
                onChange={(e) => handleInputChange('timeoutSeconds', parseInt(e.target.value))}
                disabled={disabled}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value={15}>15 seconds</option>
                <option value={30}>30 seconds</option>
                <option value={60}>60 seconds</option>
                <option value={120}>120 seconds</option>
              </select>
              <p className="text-xs text-gray-500 mt-1">Maximum processing time per document</p>
            </div>

            <div>
              <label htmlFor="retry-attempts" className="block text-sm font-medium text-gray-700 mb-2">
                Retry Attempts
              </label>
              <select
                id="retry-attempts"
                value={localData.retryAttempts}
                onChange={(e) => handleInputChange('retryAttempts', parseInt(e.target.value))}
                disabled={disabled}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value={0}>No retries</option>
                <option value={1}>1 retry</option>
                <option value={3}>3 retries</option>
                <option value={5}>5 retries</option>
              </select>
              <p className="text-xs text-gray-500 mt-1">Retries on processing failure</p>
            </div>
          </div>
        </div>
      </div>

      {/* Configuration Summary */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h4 className="text-sm font-medium text-gray-800 mb-3">Processing Configuration</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="text-gray-600">OCR:</span>
            <div className="font-medium">{localData.ocrLanguage} / {localData.ocrQuality}</div>
          </div>
          <div>
            <span className="text-gray-600">AI:</span>
            <div className="font-medium">{localData.aiProvider} / {localData.analysisDepth}</div>
          </div>
          <div>
            <span className="text-gray-600">Auto-process:</span>
            <div className="font-medium">{localData.autoProcess ? 'Enabled' : 'Disabled'}</div>
          </div>
          <div>
            <span className="text-gray-600">Cache:</span>
            <div className="font-medium">{localData.cacheEnabled ? 'Enabled' : 'Disabled'}</div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ProcessingPreferences;
