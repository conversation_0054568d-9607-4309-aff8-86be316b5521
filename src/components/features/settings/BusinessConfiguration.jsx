import React, { useState, useEffect } from 'react';

/**
 * Business Configuration Component
 * Handles business settings like currency, VAT rates, fiscal year
 */
export function BusinessConfiguration({
  businessData = {},
  onChange,
  errors = {},
  disabled = false
}) {
  const [localData, setLocalData] = useState({
    currency: 'PLN',
    vatRate: '23',
    fiscalYearStart: '01-01',
    industry: '',
    ...businessData
  });

  // Currency options
  const currencies = [
    { code: 'PLN', name: 'Polish Złoty (PLN)', symbol: 'zł' },
    { code: 'EUR', name: 'Euro (EUR)', symbol: '€' },
    { code: 'USD', name: 'US Dollar (USD)', symbol: '$' },
    { code: 'GBP', name: 'British Pound (GBP)', symbol: '£' },
    { code: 'CZK', name: 'Czech Koruna (CZK)', symbol: 'Kč' },
    { code: 'HUF', name: 'Hungarian Forint (HUF)', symbol: 'Ft' }
  ];

  // Polish VAT rates
  const vatRates = [
    { value: '23', label: '23% - Standard rate', description: 'Most goods and services' },
    { value: '8', label: '8% - Reduced rate', description: 'Books, newspapers, some food' },
    { value: '5', label: '5% - Reduced rate', description: 'Basic food, medicines' },
    { value: '0', label: '0% - Zero rate', description: 'Exports, some services' },
    { value: 'exempt', label: 'VAT Exempt', description: 'Healthcare, education, finance' },
    { value: 'custom', label: 'Custom Rate', description: 'Enter custom percentage' }
  ];

  // Industry options
  const industries = [
    { value: '', label: 'Select Industry' },
    { value: 'accounting', label: 'Accounting & Bookkeeping' },
    { value: 'consulting', label: 'Business Consulting' },
    { value: 'construction', label: 'Construction' },
    { value: 'education', label: 'Education & Training' },
    { value: 'finance', label: 'Financial Services' },
    { value: 'healthcare', label: 'Healthcare' },
    { value: 'hospitality', label: 'Hospitality & Tourism' },
    { value: 'it', label: 'Information Technology' },
    { value: 'legal', label: 'Legal Services' },
    { value: 'manufacturing', label: 'Manufacturing' },
    { value: 'marketing', label: 'Marketing & Advertising' },
    { value: 'real-estate', label: 'Real Estate' },
    { value: 'retail', label: 'Retail & E-commerce' },
    { value: 'transportation', label: 'Transportation & Logistics' },
    { value: 'other', label: 'Other' }
  ];

  // Update local data when props change
  useEffect(() => {
    setLocalData(prev => ({
      ...prev,
      ...businessData
    }));
  }, [businessData]);

  // Handle input changes
  const handleInputChange = (field, value) => {
    const newData = { ...localData, [field]: value };
    setLocalData(newData);

    // Notify parent component
    if (onChange) {
      onChange(newData);
    }
  };

  // Handle custom VAT rate
  const handleVatRateChange = (value) => {
    if (value === 'custom') {
      setLocalData(prev => ({ ...prev, vatRate: '', customVatRate: true }));
    } else {
      setLocalData(prev => ({ ...prev, vatRate: value, customVatRate: false }));
      if (onChange) {
        onChange({ ...localData, vatRate: value, customVatRate: false });
      }
    }
  };

  // Validate custom VAT rate
  const validateCustomVatRate = (rate) => {
    const numRate = parseFloat(rate);
    return !isNaN(numRate) && numRate >= 0 && numRate <= 100;
  };

  return (
    <div className="space-y-6">
      <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <span className="text-amber-500 text-xl">⚙️</span>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-amber-800">Business Configuration</h3>
            <p className="text-sm text-amber-700 mt-1">
              Configure business-specific settings for accurate calculations and compliance.
              These settings affect how documents are processed and invoices are generated.
            </p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Currency */}
        <div>
          <label htmlFor="currency" className="block text-sm font-medium text-gray-700 mb-2">
            Default Currency *
          </label>
          <select
            id="currency"
            value={localData.currency}
            onChange={(e) => handleInputChange('currency', e.target.value)}
            disabled={disabled}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.currency ? 'border-red-300' : 'border-gray-300'
            } ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}`}
            aria-describedby={errors.currency ? 'currency-error' : 'currency-help'}
          >
            {currencies.map(currency => (
              <option key={currency.code} value={currency.code}>
                {currency.name}
              </option>
            ))}
          </select>
          <p id="currency-help" className="mt-1 text-xs text-gray-500">
            Primary currency for document processing and calculations
          </p>
          {errors.currency && (
            <p id="currency-error" className="mt-1 text-sm text-red-600">
              {errors.currency}
            </p>
          )}
        </div>

        {/* Industry */}
        <div>
          <label htmlFor="industry" className="block text-sm font-medium text-gray-700 mb-2">
            Industry Sector
          </label>
          <select
            id="industry"
            value={localData.industry}
            onChange={(e) => handleInputChange('industry', e.target.value)}
            disabled={disabled}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.industry ? 'border-red-300' : 'border-gray-300'
            } ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}`}
            aria-describedby="industry-help"
          >
            {industries.map(industry => (
              <option key={industry.value} value={industry.value}>
                {industry.label}
              </option>
            ))}
          </select>
          <p id="industry-help" className="mt-1 text-xs text-gray-500">
            Helps optimize document processing for your business type
          </p>
          {errors.industry && (
            <p className="mt-1 text-sm text-red-600">
              {errors.industry}
            </p>
          )}
        </div>

        {/* VAT Rate */}
        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Default VAT Rate *
          </label>
          <div className="space-y-3">
            {vatRates.map(rate => (
              <div key={rate.value} className="flex items-start">
                <input
                  id={`vat-${rate.value}`}
                  type="radio"
                  name="vatRate"
                  value={rate.value}
                  checked={localData.customVatRate ? rate.value === 'custom' : localData.vatRate === rate.value}
                  onChange={(e) => handleVatRateChange(e.target.value)}
                  disabled={disabled}
                  className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <div className="ml-3 flex-1">
                  <label htmlFor={`vat-${rate.value}`} className="text-sm font-medium text-gray-700 cursor-pointer">
                    {rate.label}
                  </label>
                  <p className="text-xs text-gray-500">{rate.description}</p>
                </div>
              </div>
            ))}

            {/* Custom VAT Rate Input */}
            {localData.customVatRate && (
              <div className="ml-7 mt-2">
                <div className="flex items-center space-x-2">
                  <input
                    type="number"
                    min="0"
                    max="100"
                    step="0.1"
                    value={localData.vatRate}
                    onChange={(e) => handleInputChange('vatRate', e.target.value)}
                    disabled={disabled}
                    className={`w-24 px-2 py-1 border rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                      localData.vatRate && !validateCustomVatRate(localData.vatRate) ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="0.0"
                  />
                  <span className="text-sm text-gray-500">%</span>
                </div>
                {localData.vatRate && !validateCustomVatRate(localData.vatRate) && (
                  <p className="mt-1 text-xs text-red-600">
                    Please enter a valid percentage (0-100)
                  </p>
                )}
              </div>
            )}
          </div>
          {errors.vatRate && (
            <p className="mt-1 text-sm text-red-600">
              {errors.vatRate}
            </p>
          )}
        </div>

        {/* Fiscal Year Start */}
        <div>
          <label htmlFor="fiscal-year" className="block text-sm font-medium text-gray-700 mb-2">
            Fiscal Year Start
          </label>
          <select
            id="fiscal-year"
            value={localData.fiscalYearStart}
            onChange={(e) => handleInputChange('fiscalYearStart', e.target.value)}
            disabled={disabled}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.fiscalYearStart ? 'border-red-300' : 'border-gray-300'
            } ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}`}
            aria-describedby="fiscal-year-help"
          >
            <option value="01-01">January 1st (Calendar Year)</option>
            <option value="04-01">April 1st</option>
            <option value="07-01">July 1st</option>
            <option value="10-01">October 1st</option>
          </select>
          <p id="fiscal-year-help" className="mt-1 text-xs text-gray-500">
            When your fiscal year begins (affects reporting periods)
          </p>
          {errors.fiscalYearStart && (
            <p className="mt-1 text-sm text-red-600">
              {errors.fiscalYearStart}
            </p>
          )}
        </div>
      </div>

      {/* Configuration Summary */}
      {localData.currency && localData.vatRate && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="text-sm font-medium text-blue-800 mb-2">Configuration Summary</h4>
          <div className="text-sm text-blue-700 space-y-1">
            <p><strong>Currency:</strong> {currencies.find(c => c.code === localData.currency)?.name}</p>
            <p><strong>VAT Rate:</strong> {localData.customVatRate ? `${localData.vatRate}% (Custom)` : vatRates.find(r => r.value === localData.vatRate)?.label}</p>
            <p><strong>Fiscal Year:</strong> Starts {localData.fiscalYearStart === '01-01' ? 'January 1st' : localData.fiscalYearStart}</p>
            {localData.industry && (
              <p><strong>Industry:</strong> {industries.find(i => i.value === localData.industry)?.label}</p>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

export default BusinessConfiguration;
