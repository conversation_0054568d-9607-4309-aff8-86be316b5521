import React, { useState, useEffect } from 'react';
import { useSettings } from '../../../popup/hooks/useSettings.js';
import { apiValidationService } from '../../../services/ApiValidationService.js';
import { environmentConfig } from '../../../services/EnvironmentConfigService.js';

/**
 * API Key Manager Component
 * Manages secure API key storage and validation
 *
 * MOVED FROM: src/popup/components/Settings/ApiKeyManager.jsx
 * TO: src/components/settings/ApiKeyManager.jsx (shared location)
 */
export function ApiKeyManager() {
  const { settings, updateApiKeys, clearApiKeys, isLoading } = useSettings();
  const [apiKeys, setApiKeys] = useState({
    deepseek: '',
    openai: '',
    fakturownia: '',
    infakt: ''
  });
  const [validationResults, setValidationResults] = useState({});
  const [isValidating, setIsValidating] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [showKeys, setShowKeys] = useState({});
  const [saveMessage, setSaveMessage] = useState('');

  // Load API keys from settings and environment config
  useEffect(() => {
    const loadApiKeys = async () => {
      let keys = {
        deepseek: '',
        openai: '',
        fakturownia: '',
        infakt: ''
      };

      // First, try to load from settings (user-saved keys)
      if (settings.apiKeys) {
        keys = { ...keys, ...settings.apiKeys };
      }

      // Then, try to load from environment config (if not already set)
      try {
        if (environmentConfig.isLoaded) {
          const envConfig = environmentConfig.getAllUnmasked();
          if (envConfig.apiKeys) {
            // Only use environment keys if settings keys are empty
            Object.keys(keys).forEach(provider => {
              if (!keys[provider] && envConfig.apiKeys[provider]?.key) {
                keys[provider] = envConfig.apiKeys[provider].key;
              }
            });
          }
        }
      } catch (error) {
        console.warn('Failed to load API keys from environment config:', error);
      }

      setApiKeys(keys);
    };

    loadApiKeys();
  }, [settings.apiKeys]);

  // Provider configurations
  const providers = [
    {
      id: 'deepseek',
      name: 'DeepSeek',
      description: 'AI processing for document analysis',
      placeholder: 'sk-...',
      required: true
    },
    {
      id: 'openai',
      name: 'OpenAI',
      description: 'Alternative AI provider',
      placeholder: 'sk-...',
      required: false
    },
    {
      id: 'fakturownia',
      name: 'Fakturownia',
      description: 'Polish accounting system integration',
      placeholder: 'API token...',
      required: false
    },
    {
      id: 'infakt',
      name: 'Infakt',
      description: 'Polish invoicing system integration',
      placeholder: 'API key...',
      required: false
    }
  ];

  // Handle input changes
  const handleInputChange = (provider, value) => {
    setApiKeys(prev => ({
      ...prev,
      [provider]: value
    }));

    // Clear validation result when key changes
    if (validationResults[provider]) {
      setValidationResults(prev => ({
        ...prev,
        [provider]: null
      }));
    }

    // Clear save message
    setSaveMessage('');
  };

  // Toggle key visibility
  const toggleKeyVisibility = (provider) => {
    setShowKeys(prev => ({
      ...prev,
      [provider]: !prev[provider]
    }));
  };

  // Validate single API key
  const validateSingleKey = async (provider) => {
    if (!apiKeys[provider] || !apiKeys[provider].trim()) {
      return;
    }

    setIsValidating(true);

    try {
      const result = await apiValidationService.testConnection(provider, apiKeys[provider]);
      setValidationResults(prev => ({
        ...prev,
        [provider]: result
      }));
    } catch (error) {
      setValidationResults(prev => ({
        ...prev,
        [provider]: {
          success: false,
          error: error.message,
          provider: provider
        }
      }));
    } finally {
      setIsValidating(false);
    }
  };

  // Validate all API keys
  const validateAllKeys = async () => {
    setIsValidating(true);

    try {
      const keysToTest = Object.fromEntries(
        Object.entries(apiKeys).filter(([_, key]) => key && key.trim())
      );

      const results = await apiValidationService.testAllConnections(keysToTest);
      setValidationResults(results.results);
    } catch (error) {
      console.error('Failed to validate API keys:', error);
    } finally {
      setIsValidating(false);
    }
  };

  // Save API keys
  const handleSave = async (validateConnections = false) => {
    setIsSaving(true);
    setSaveMessage('');

    try {
      const result = await updateApiKeys(apiKeys, validateConnections);

      if (result.success) {
        setSaveMessage('✅ API keys saved successfully');

        if (result.validationResults) {
          setValidationResults(result.validationResults.results);
        }
      } else {
        setSaveMessage('❌ Failed to save: ' + result.errors.join(', '));
      }
    } catch (error) {
      setSaveMessage('❌ Failed to save API keys: ' + error.message);
    } finally {
      setIsSaving(false);
    }
  };

  // Clear all API keys
  const handleClearAll = async () => {
    if (!confirm('Are you sure you want to clear all API keys? This action cannot be undone.')) {
      return;
    }

    try {
      await clearApiKeys();
      setApiKeys({
        deepseek: '',
        openai: '',
        fakturownia: '',
        infakt: ''
      });
      setValidationResults({});
      setSaveMessage('🧹 All API keys cleared');
    } catch (error) {
      setSaveMessage('❌ Failed to clear API keys: ' + error.message);
    }
  };

  // Get validation status icon
  const getValidationIcon = (provider) => {
    const result = validationResults[provider];
    if (!result) { return null; }

    if (result.success) {
      return <span className="text-green-500 text-sm">✅ Connected</span>;
    } else if (result.skipped) {
      return <span className="text-gray-400 text-sm">⏭️ Skipped</span>;
    }
    return <span className="text-red-500 text-sm">❌ Failed</span>;

  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-gray-600">Loading API key settings...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="border-b border-gray-200 pb-4">
        <h2 className="text-xl font-semibold text-gray-900">API Key Management</h2>
        <p className="text-sm text-gray-600 mt-1">
          Configure API keys for AI processing and accounting integrations. Keys are encrypted and stored securely.
        </p>
      </div>

      <div className="space-y-4">
        {providers.map((provider) => (
          <div key={provider.id} className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <div>
                <h3 className="font-medium text-gray-900">
                  {provider.name}
                  {provider.required && <span className="text-red-500 ml-1">*</span>}
                </h3>
                <p className="text-sm text-gray-600">{provider.description}</p>
              </div>
              {getValidationIcon(provider.id)}
            </div>

            <div className="flex gap-2">
              <div className="flex-1">
                <input
                  type={showKeys[provider.id] ? 'text' : 'password'}
                  value={apiKeys[provider.id]}
                  onChange={(e) => handleInputChange(provider.id, e.target.value)}
                  placeholder={provider.placeholder}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <button
                type="button"
                onClick={() => toggleKeyVisibility(provider.id)}
                className="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
                title={showKeys[provider.id] ? 'Hide key' : 'Show key'}
              >
                {showKeys[provider.id] ? '🙈' : '👁️'}
              </button>

              <button
                type="button"
                onClick={() => validateSingleKey(provider.id)}
                disabled={!apiKeys[provider.id] || isValidating}
                className="px-3 py-2 text-sm bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
                title="Test connection"
              >
                {isValidating ? '⏳' : '🔌'}
              </button>
            </div>

            {validationResults[provider.id] && !validationResults[provider.id].success && (
              <div className="mt-2 text-sm text-red-600">
                Error: {validationResults[provider.id].error}
              </div>
            )}
          </div>
        ))}
      </div>

      <div className="flex flex-wrap gap-3 pt-4 border-t border-gray-200">
        <button
          onClick={() => handleSave(false)}
          disabled={isSaving}
          className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
        >
          {isSaving ? 'Saving...' : 'Save Keys'}
        </button>

        <button
          onClick={() => handleSave(true)}
          disabled={isSaving || isValidating}
          className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
        >
          {isSaving ? 'Saving...' : 'Save & Test All'}
        </button>

        <button
          onClick={validateAllKeys}
          disabled={isValidating}
          className="px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
        >
          {isValidating ? 'Testing...' : 'Test All'}
        </button>

        <button
          onClick={handleClearAll}
          className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600"
        >
          Clear All
        </button>
      </div>

      {saveMessage && (
        <div className="p-3 rounded-md bg-gray-50 border">
          <p className="text-sm">{saveMessage}</p>
        </div>
      )}
    </div>
  );
}
