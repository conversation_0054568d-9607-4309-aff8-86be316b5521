/**
 * DocumentProcessor - Unified document processing pipeline
 * Consolidates functionality from components/analyzer.js
 * Orchestrates PDF extraction, OCR, and AI analysis
 */

import { documentProcessingService } from '../../services/DocumentProcessingService.js';
import { StorageAPI } from '../../api/StorageAPI.js';
import { processingLogger } from '../../utils/ProcessingLogger.js';
import { uploadTracker } from '../../utils/UploadTracker.js';

export class DocumentProcessor {
  constructor() {
    this.processingService = documentProcessingService;
    this.storageAPI = new StorageAPI();
    this.initialized = false;
  }

  /**
   * Initialize the document processor
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initialized) { return; }

    try {
      // Initialize the hierarchical processing service
      await this.processingService.initialize();

      this.initialized = true;
      console.log('DocumentProcessor initialized successfully');
    } catch (error) {
      console.error('Failed to initialize DocumentProcessor:', error);
      throw new Error(`DocumentProcessor initialization failed: ${error.message}`);
    }
  }

  /**
   * Process a document file (unified analysis)
   * @param {File} file - Document file to process
   * @param {Object} options - Processing options
   * @returns {Promise<Object>} - Processing result
   */
  async processDocument(file, options = {}) {
    // Start upload tracking
    const uploadId = uploadTracker.startUpload(file, options);
    processingLogger.info('document_processing', `Starting document processing for ${file.name}`, uploadId, {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      options: { ...options, apiKey: options.apiKey ? '[REDACTED]' : null }
    });

    try {
      await this.initialize();
      processingLogger.startTimer('document_processing', uploadId);

      const {
        apiKey = null,
        preferredLanguage = 'pol',
        companyInfo = null,
        enableOCR = true,
        enableRAG = true,
        validateResults = true
      } = options;

      if (!apiKey) {
        const error = new Error('API key is required for document processing');
        processingLogger.error('document_processing', 'API key missing', uploadId, { error: error.message });
        uploadTracker.failUpload(uploadId, error);
        throw error;
      }

      processingLogger.info('document_processing', `Processing document: ${file.name}`, uploadId, {
        preferredLanguage,
        enableOCR,
        enableRAG,
        validateResults,
        companyInfo: companyInfo ? 'provided' : 'not_provided'
      });

      uploadTracker.updateStage(uploadId, 'processing_service', { service: 'DocumentProcessingService' });

      // Use hierarchical DocumentProcessingService for complete processing
      const result = await this.processingService.processDocument(file, {
        progressCallback: (progress) => {
          uploadTracker.updateProgress(uploadId, progress.progress, progress.stage);
        },
        enableAI: true,
        enableRAG,
        apiKey,
        language: preferredLanguage,
        companyInfo
      });

      const processingDuration = processingLogger.endTimer('document_processing', uploadId);

      if (result.success) {
        processingLogger.info('document_processing', 'Document processed successfully', uploadId, {
          processingDurationMs: processingDuration,
          extractedDataKeys: Object.keys(result.data || {}),
          dataSize: JSON.stringify(result.data || {}).length
        });

        // Store additional metadata
        result.data.fileSize = file.size;
        result.data.fileType = file.type;
        result.data.processedAt = new Date().toISOString();
        result.data.uploadId = uploadId;
        result.data.processingDurationMs = processingDuration;

        // Add to processing history
        await this.addToHistory(result.data);

        uploadTracker.completeUpload(uploadId, result);
        processingLogger.info('document_processing', 'Processing completed and stored', uploadId, {
          historyUpdated: true,
          uploadCompleted: true
        });
      } else {
        processingLogger.warn('document_processing', 'Document processing returned unsuccessful result', uploadId, {
          error: result.error,
          processingDurationMs: processingDuration
        });
        uploadTracker.failUpload(uploadId, result.error || 'Unknown processing error');
      }

      return result;
    } catch (error) {
      const processingDuration = processingLogger.endTimer('document_processing', uploadId);
      processingLogger.error('document_processing', 'Document processing failed', uploadId, {
        error: error.message,
        stack: error.stack,
        processingDurationMs: processingDuration
      });
      uploadTracker.failUpload(uploadId, error);

      return {
        success: false,
        error: error.message,
        data: null,
        uploadId
      };
    }
  }

  /**
   * Extract text from document (legacy compatibility)
   * @param {File} file - Document file
   * @param {Object} options - Extraction options
   * @returns {Promise<Object>} - Extraction result
   */
  async extractText(file, options = {}) {
    try {
      await this.initialize();

      const {
        enableOCR = true,
        ocrLanguage = 'pol+eng',
        preferredLanguage = 'pol'
      } = options;

      let result = {
        text: '',
        method: 'none',
        ocrUsed: false,
        success: false
      };

      // Use hierarchical processing service for content extraction
      const extractionResult = await this.processingService.extractContent(file, {
        language: ocrLanguage.split('+')[0] || 'pol' // Extract primary language
      });

      result = {
        text: extractionResult.text,
        method: extractionResult.method,
        ocrUsed: extractionResult.method.includes('ocr'),
        success: extractionResult.text.length > 0,
        confidence: extractionResult.confidence,
        pages: extractionResult.pages
      };

      return result;
    } catch (error) {
      console.error('Text extraction failed:', error);
      return {
        text: '',
        method: 'error',
        ocrUsed: false,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Analyze document with AI (legacy compatibility)
   * @param {string} text - Document text
   * @param {string} fileName - File name
   * @param {string} apiKey - API key
   * @param {Object} companyInfo - Company information
   * @param {string} ragContext - RAG context
   * @param {string} pdfData - PDF data
   * @returns {Promise<Object>} - Analysis result
   */
  async analyzeWithAI(text, fileName, apiKey, companyInfo = null, ragContext = '', pdfData = null) {
    try {
      // Create a File object from text for compatibility
      const file = new File([text], fileName, { type: 'text/plain' });

      // Use the unified processing pipeline
      const result = await this.processDocument(file, {
        apiKey,
        companyInfo,
        enableRAG: ragContext.length > 0
      });

      if (result.success) {
        // Format result for legacy compatibility
        const data = result.data;
        return {
          success: true,
          data: {
            documentName: data.documentName || fileName,
            seller: data.seller_name || 'Unknown Seller',
            sellerNip: data.seller_tax_no || '',
            buyer: data.buyer_name || 'Unknown Buyer',
            buyerNip: data.buyer_tax_no || '',
            totalNett: data.total_net || 0,
            totalVat: data.total_vat || 0,
            totalGross: data.total_gross || 0,
            issueDate: data.issue_date || '',
            accountingDate: data.sell_date || data.issue_date || '',
            income: data.income || '0',
            documentType: data.kind || 'vat',
            positions: data.positions || [],
            rawData: data
          }
        };
      }
      return { success: false, error: result.error };

    } catch (error) {
      console.error('AI analysis failed:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Process multiple documents in batch
   * @param {Array<File>} files - Array of files to process
   * @param {Object} options - Processing options
   * @returns {Promise<Array>} - Array of processing results
   */
  async processBatch(files, options = {}) {
    const results = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      console.log(`Processing file ${i + 1} of ${files.length}: ${file.name}`);

      try {
        const result = await this.processDocument(file, options);
        results.push({
          file: file.name,
          index: i,
          ...result
        });
      } catch (error) {
        console.error(`Failed to process file ${file.name}:`, error);
        results.push({
          file: file.name,
          index: i,
          success: false,
          error: error.message
        });
      }
    }

    return results;
  }

  /**
   * Get processing statistics
   * @returns {Promise<Object>} - Processing statistics
   */
  async getProcessingStats() {
    try {
      const history = await this.getProcessingHistory();

      const stats = {
        totalProcessed: history.length,
        successfulProcessed: history.filter(item => item.success !== false).length,
        documentTypes: {},
        languages: {},
        ocrUsage: 0,
        averageProcessingTime: 0
      };

      history.forEach(item => {
        // Count document types
        const docType = item.documentType || item.kind || 'unknown';
        stats.documentTypes[docType] = (stats.documentTypes[docType] || 0) + 1;

        // Count languages
        const lang = item.language || 'unknown';
        stats.languages[lang] = (stats.languages[lang] || 0) + 1;

        // Count OCR usage
        if (item.ocrUsed) {
          stats.ocrUsage++;
        }
      });

      return stats;
    } catch (error) {
      console.error('Failed to get processing stats:', error);
      return {
        totalProcessed: 0,
        successfulProcessed: 0,
        documentTypes: {},
        languages: {},
        ocrUsage: 0,
        averageProcessingTime: 0
      };
    }
  }

  /**
   * Add document to processing history
   * @param {Object} documentData - Document data
   * @returns {Promise<void>}
   */
  async addToHistory(documentData) {
    try {
      const history = await this.getProcessingHistory();

      const historyItem = {
        id: `proc_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
        fileName: documentData.documentName,
        documentType: documentData.documentType || documentData.kind,
        language: documentData.language,
        ocrUsed: documentData.ocrUsed,
        processedAt: documentData.processedAt || new Date().toISOString(),
        fileSize: documentData.fileSize,
        fileType: documentData.fileType,
        success: true
      };

      history.unshift(historyItem); // Add to beginning

      // Keep only last 100 items
      if (history.length > 100) {
        history.splice(100);
      }

      await this.storageAPI.set({ processingHistory: history });
    } catch (error) {
      console.error('Failed to add to processing history:', error);
    }
  }

  /**
   * Get processing history
   * @returns {Promise<Array>} - Processing history
   */
  async getProcessingHistory() {
    try {
      const data = await this.storageAPI.get('processingHistory');
      return data.processingHistory || [];
    } catch (error) {
      console.error('Failed to get processing history:', error);
      return [];
    }
  }

  /**
   * Clear processing history
   * @returns {Promise<void>}
   */
  async clearHistory() {
    try {
      await this.storageAPI.remove('processingHistory');
      console.log('Processing history cleared');
    } catch (error) {
      console.error('Failed to clear processing history:', error);
    }
  }

  /**
   * Validate document before processing using consolidated validation service
   * @param {File} file - File to validate
   * @returns {Promise<Object>} - Validation result
   */
  async validateDocument(file) {
    try {
      // Use consolidated validation service with document processing specific options
      const validation = await consolidatedFileValidationService.validateFile(file, {
        maxFileSize: 50 * 1024 * 1024, // 50MB
        allowedMimeTypes: [
          'application/pdf',
          'image/jpeg',
          'image/png',
          'image/gif',
          'image/bmp',
          'image/tiff',
          'text/plain'
        ],
        enableSecurityScanning: true,
        enableContentValidation: true
      });

      // Convert to legacy format for backward compatibility
      return {
        valid: validation.isValid,
        errors: validation.errors,
        warnings: validation.warnings
      };
    } catch (error) {
      console.error('Document validation error:', error);
      return {
        valid: false,
        errors: [`Validation error: ${error.message}`],
        warnings: []
      };
    }
  }
}

// Create singleton instance
const documentProcessor = new DocumentProcessor();

// Export for ES modules
export default documentProcessor;

// Export to window object for non-module scripts
if (typeof window !== 'undefined') {
  window.DocumentProcessor = DocumentProcessor;
  window.documentProcessor = documentProcessor;

  // Legacy compatibility functions
  window.unifiedAnalyzeInvoice = async (text, fileName, apiKey, companyInfo, ragContext, pdfData) => {
    return await documentProcessor.analyzeWithAI(text, fileName, apiKey, companyInfo, ragContext, pdfData);
  };

  window.analyzeDocumentWithMvp = async (file, apiKey, preferredLanguage, companyInfo) => {
    return await documentProcessor.processDocument(file, {
      apiKey,
      preferredLanguage,
      companyInfo
    });
  };
}

// Local test function
function testDocumentProcessor() {
  console.log('=== DocumentProcessor Local Test ===');

  try {
    const processor = new DocumentProcessor();

    // Test 1: Initialization
    console.log('Test 1: Initialization');
    console.log('✓ DocumentProcessor instance created');
    console.log('Initialized:', processor.initialized);

    // Test 2: Document validation
    console.log('\nTest 2: Document validation');
    const mockFile = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
    const validation = processor.validateDocument(mockFile);
    console.log('✓ Document validation works');
    console.log('Valid:', validation.valid);
    console.log('Errors:', validation.errors.length);

    // Test 3: Invalid file validation
    console.log('\nTest 3: Invalid file validation');
    const invalidFile = new File(['test'], 'test.xyz', { type: 'application/unknown' });
    const invalidValidation = processor.validateDocument(invalidFile);
    console.log('✓ Invalid file validation works');
    console.log('Valid:', invalidValidation.valid);
    console.log('Errors:', invalidValidation.errors);

    // Test 4: Large file validation
    console.log('\nTest 4: Large file validation');
    const largeFile = {
      name: 'large.pdf',
      type: 'application/pdf',
      size: 60 * 1024 * 1024 // 60MB
    };
    const largeValidation = processor.validateDocument(largeFile);
    console.log('✓ Large file validation works');
    console.log('Valid:', largeValidation.valid);
    console.log('Has size error:', largeValidation.errors.some(e => e.includes('size')));

    console.log('\n✅ All DocumentProcessor tests passed!');
    return true;

  } catch (error) {
    console.error('❌ DocumentProcessor test failed:', error);
    return false;
  }
}

// Run test if in browser environment
if (typeof window !== 'undefined' && window.location) {
  // Uncomment to run test
  // testDocumentProcessor();
}

// ===== LOCAL TESTING (Node.js equivalent of Python's if __name__ == '__main__') =====
if (typeof process !== 'undefined' && process.argv && process.argv[1] && process.argv[1].includes('DocumentProcessor.js')) {
  console.log('🧪 Running local tests for DocumentProcessor...');

  // Mock File constructor for Node.js
  if (typeof File === 'undefined') {
    global.File = class File {
      constructor(content, name, options = {}) {
        this.content = content;
        this.name = name;
        this.type = options.type || 'text/plain';
        this.size = Array.isArray(content) ? content.join('').length : content.length;
      }

      async text() {
        return Array.isArray(this.content) ? this.content.join('') : this.content;
      }
    };
  }

  try {
    const processor = new DocumentProcessor();

    console.log('✅ Test 1: DocumentProcessor initialization');
    console.log('📊 Initialized:', processor.initialized);
    console.log('📊 Has analysis service:', !!processor.analysisService);
    console.log('📊 Has PDF processor:', !!processor.pdfProcessor);
    console.log('📊 Has OCR processor:', !!processor.ocrProcessor);
    console.log('📊 Has storage API:', !!processor.storageAPI);

    console.log('\n✅ Test 2: Document validation - valid PDF');
    const validPdf = new File(['PDF content'], 'invoice.pdf', { type: 'application/pdf' });
    const validResult = processor.validateDocument(validPdf);
    console.log('📊 Valid:', validResult.valid);
    console.log('📊 Issues:', validResult.errors.length);
    console.log('📊 Warnings:', validResult.warnings.length);

    console.log('\n✅ Test 3: Document validation - invalid file type');
    const invalidFile = new File(['content'], 'document.xyz', { type: 'application/unknown' });
    const invalidResult = processor.validateDocument(invalidFile);
    console.log('📊 Valid:', invalidResult.valid);
    console.log('📊 Issues:', invalidResult.errors.length);
    console.log('📊 Issue message:', invalidResult.errors[0] || 'None');

    console.log('\n✅ Test 4: Document validation - large file');
    const largeFile = {
      name: 'large.pdf',
      type: 'application/pdf',
      size: 60 * 1024 * 1024, // 60MB
      text: async () => 'large file content'
    };
    const largeResult = processor.validateDocument(largeFile);
    console.log('📊 Valid:', largeResult.valid);
    console.log('📊 Has size issue:', largeResult.errors.some(e => e.includes('size')));

    console.log('\n✅ Test 5: Supported file types');
    const supportedTypes = [
      'application/pdf',
      'image/jpeg',
      'image/png',
      'text/plain'
    ];
    supportedTypes.forEach(type => {
      const testFile = new File(['content'], `test.${type.split('/')[1]}`, { type });
      const result = processor.validateDocument(testFile);
      console.log(`📊 ${type}: ${result.valid ? '✅' : '❌'}`);
    });

    console.log('\n✅ Test 6: File validation edge cases');
    const noNameFile = new File(['content'], '', { type: 'application/pdf' });
    const noNameResult = processor.validateDocument(noNameFile);
    console.log('📊 No name file warnings:', noNameResult.warnings.length);

    const emptyFile = new File([''], 'empty.pdf', { type: 'application/pdf' });
    const emptyResult = processor.validateDocument(emptyFile);
    console.log('📊 Empty file valid:', emptyResult.valid);

    console.log('\n🎉 All tests completed for DocumentProcessor');
    console.log('📋 Note: Full functionality requires browser environment and API keys');

  } catch (error) {
    console.error('❌ DocumentProcessor test failed:', error);
  }
}
