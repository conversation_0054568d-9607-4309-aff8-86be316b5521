/**
 * OCRProcessor - Optical Character Recognition processing
 * Handles Tesseract.js OCR operations with optimization for invoices
 */

import { createWorker } from 'tesseract.js';
import { processingLogger } from '../../utils/ProcessingLogger.js';

export class OCRProcessor {
  constructor() {
    this.worker = null;
    this.initialized = false;
    this.supportedLanguages = ['pol', 'eng', 'deu', 'fra', 'spa', 'ita'];
  }

  /**
   * Initialize Tesseract worker
   * @param {string} language - OCR language (default: 'pol+eng')
   * @returns {Promise<void>}
   */
  async initialize(language = 'pol+eng') {
    if (this.initialized && this.worker) {
      return;
    }

    try {
      console.log('Initializing OCR worker with language:', language);

      // Configure worker options for Chrome extension
      const workerOptions = {};

      // Set worker path for Chrome extension CSP compliance
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        workerOptions.workerPath = chrome.runtime.getURL('assets/tesseract.worker.min.js');
      }

      // Create worker with proper configuration
      this.worker = await createWorker(language, 1, workerOptions);

      this.initialized = true;
      console.log('OCR worker initialized successfully');
    } catch (error) {
      console.error('Failed to initialize OCR worker:', error);
      throw new Error(`OCR initialization failed: ${error.message}`);
    }
  }

  /**
   * Perform OCR on image or canvas
   * @param {HTMLCanvasElement|HTMLImageElement|File|string} input - Input image
   * @param {string} language - OCR language
   * @param {Object} options - OCR options
   * @returns {Promise<string>} - Extracted text
   */
  async performOCR(input, language = 'pol+eng', options = {}) {
    const uploadId = options.uploadId || null;

    try {
      if (uploadId) {
        processingLogger.info('ocr_processing', 'Starting Tesseract OCR processing', uploadId, {
          language,
          inputType: input.constructor.name,
          inputSize: input.size || input.width || 'unknown'
        });
        processingLogger.startTimer('ocr_processing', uploadId);
      }

      await this.initialize(language);

      const {
        preprocessImage = true,
        confidence = 60,
        progressCallback = null
      } = options;

      let imageInput = input;

      // Preprocess image if enabled
      if (preprocessImage && (input instanceof HTMLCanvasElement || input instanceof HTMLImageElement)) {
        if (uploadId) {
          processingLogger.debug('ocr_processing', 'Preprocessing image for better OCR results', uploadId, {
            originalWidth: input.width || input.naturalWidth,
            originalHeight: input.height || input.naturalHeight
          });
        }
        imageInput = this.preprocessImage(input);
      }

      if (uploadId) {
        processingLogger.info('ocr_processing', 'Starting Tesseract text recognition', uploadId, {
          preprocessingApplied: preprocessImage,
          confidenceThreshold: confidence
        });
      }

      console.log('Starting OCR recognition...');

      const { data } = await this.worker.recognize(imageInput, {
        logger: (m) => {
          if (uploadId && m.status === 'recognizing text') {
            processingLogger.debug('ocr_processing', `Tesseract progress: ${m.status}`, uploadId, {
              progress: m.progress,
              status: m.status
            });
          }
          if (progressCallback && m.status === 'recognizing text') {
            progressCallback(m.progress);
          }
        }
      });

      if (uploadId) {
        processingLogger.info('ocr_processing', 'Tesseract recognition completed', uploadId, {
          rawTextLength: data.text ? data.text.length : 0,
          wordsDetected: data.words ? data.words.length : 0,
          linesDetected: data.lines ? data.lines.length : 0,
          averageConfidence: data.words ?
            Math.round(data.words.reduce((sum, word) => sum + word.confidence, 0) / data.words.length) : 0
        });
      }

      // Filter out low-confidence text
      const filteredText = this.filterLowConfidenceText(data, confidence);

      if (uploadId) {
        const duration = processingLogger.endTimer('ocr_processing', uploadId);

        // Enhanced logging with OCR data content
        processingLogger.logDataExtraction('ocr_processing', filteredText, uploadId, {
          rawTextLength: data.text ? data.text.length : 0,
          finalTextLength: filteredText.length,
          wordsDetected: data.words ? data.words.length : 0,
          linesDetected: data.lines ? data.lines.length : 0,
          paragraphsDetected: data.paragraphs ? data.paragraphs.length : 0,
          averageConfidence: data.words ?
            Math.round(data.words.reduce((sum, word) => sum + word.confidence, 0) / data.words.length) : 0,
          highConfidenceWords: data.words ? data.words.filter(w => w.confidence > 90).length : 0,
          lowConfidenceWords: data.words ? data.words.filter(w => w.confidence < 60).length : 0,
          processingDurationMs: duration,
          confidenceFilter: confidence,
          textReduction: data.text ?
            Math.round(((data.text.length - filteredText.length) / data.text.length) * 100) : 0,
          language
        });

        processingLogger.info('ocr_processing', 'OCR processing completed successfully', uploadId, {
          finalTextLength: filteredText.length,
          processingDurationMs: duration,
          confidenceFilter: confidence,
          textReduction: data.text ?
            Math.round(((data.text.length - filteredText.length) / data.text.length) * 100) : 0
        });
      }

      console.log(`OCR completed. Extracted ${filteredText.length} characters`);
      return filteredText;
    } catch (error) {
      if (uploadId) {
        processingLogger.endTimer('ocr_processing', uploadId);
        processingLogger.error('ocr_processing', 'Tesseract OCR processing failed', uploadId, {
          error: error.message,
          stack: error.stack,
          language,
          inputType: input.constructor.name
        });
      }
      console.error('OCR recognition failed:', error);
      throw new Error(`OCR failed: ${error.message}`);
    }
  }

  /**
   * Perform OCR on multiple images
   * @param {Array} images - Array of images
   * @param {string} language - OCR language
   * @param {Object} options - OCR options
   * @returns {Promise<Array>} - Array of extracted texts
   */
  async performBatchOCR(images, language = 'pol+eng', options = {}) {
    const results = [];

    for (let i = 0; i < images.length; i++) {
      try {
        console.log(`Processing image ${i + 1} of ${images.length}`);
        const text = await this.performOCR(images[i], language, options);
        results.push({
          index: i,
          text,
          success: true
        });
      } catch (error) {
        console.error(`Failed to process image ${i + 1}:`, error);
        results.push({
          index: i,
          text: '',
          success: false,
          error: error.message
        });
      }
    }

    return results;
  }

  /**
   * Preprocess image for better OCR results
   * @param {HTMLCanvasElement|HTMLImageElement} image - Input image
   * @returns {HTMLCanvasElement} - Preprocessed image
   */
  preprocessImage(image) {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    // Set canvas size
    canvas.width = image.width || image.naturalWidth;
    canvas.height = image.height || image.naturalHeight;

    // Draw original image
    ctx.drawImage(image, 0, 0);

    // Get image data
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;

    // Apply preprocessing filters
    this.applyContrastEnhancement(data);
    this.applyNoiseReduction(data);
    this.applySharpening(data);

    // Put processed data back
    ctx.putImageData(imageData, 0, 0);

    return canvas;
  }

  /**
   * Apply contrast enhancement
   * @param {Uint8ClampedArray} data - Image data
   */
  applyContrastEnhancement(data) {
    const factor = 1.2; // Contrast factor

    for (let i = 0; i < data.length; i += 4) {
      // Apply contrast to RGB channels
      data[i] = Math.min(255, Math.max(0, (data[i] - 128) * factor + 128)); // Red
      data[i + 1] = Math.min(255, Math.max(0, (data[i + 1] - 128) * factor + 128)); // Green
      data[i + 2] = Math.min(255, Math.max(0, (data[i + 2] - 128) * factor + 128)); // Blue
    }
  }

  /**
   * Apply noise reduction
   * @param {Uint8ClampedArray} data - Image data
   */
  applyNoiseReduction(data) {
    // Simple noise reduction by averaging nearby pixels
    const width = Math.sqrt(data.length / 4);
    const temp = new Uint8ClampedArray(data);

    for (let i = 0; i < data.length; i += 4) {
      const x = (i / 4) % width;
      const y = Math.floor((i / 4) / width);

      if (x > 0 && x < width - 1 && y > 0 && y < width - 1) {
        // Average with neighboring pixels
        for (let c = 0; c < 3; c++) {
          const sum = temp[i + c] + temp[i - 4 + c] + temp[i + 4 + c] +
                     temp[i - width * 4 + c] + temp[i + width * 4 + c];
          data[i + c] = sum / 5;
        }
      }
    }
  }

  /**
   * Apply sharpening filter
   * @param {Uint8ClampedArray} data - Image data
   */
  applySharpening(data) {
    // Simple sharpening kernel
    const kernel = [-1, -1, -1, -1, 9, -1, -1, -1, -1];
    const width = Math.sqrt(data.length / 4);
    const temp = new Uint8ClampedArray(data);

    for (let i = 0; i < data.length; i += 4) {
      const x = (i / 4) % width;
      const y = Math.floor((i / 4) / width);

      if (x > 0 && x < width - 1 && y > 0 && y < width - 1) {
        for (let c = 0; c < 3; c++) {
          let sum = 0;
          for (let ky = -1; ky <= 1; ky++) {
            for (let kx = -1; kx <= 1; kx++) {
              const idx = ((y + ky) * width + (x + kx)) * 4 + c;
              sum += temp[idx] * kernel[(ky + 1) * 3 + (kx + 1)];
            }
          }
          data[i + c] = Math.min(255, Math.max(0, sum));
        }
      }
    }
  }

  /**
   * Filter out low-confidence text
   * @param {Object} ocrData - OCR result data
   * @param {number} minConfidence - Minimum confidence threshold
   * @returns {string} - Filtered text
   */
  filterLowConfidenceText(ocrData, minConfidence = 60) {
    if (!ocrData.words) {
      return ocrData.text || '';
    }

    const filteredWords = ocrData.words
      .filter(word => word.confidence >= minConfidence)
      .map(word => word.text);

    return filteredWords.join(' ');
  }

  /**
   * Extract table data using OCR
   * @param {HTMLCanvasElement|HTMLImageElement} image - Input image
   * @param {string} language - OCR language
   * @returns {Promise<Array>} - Extracted table data
   */
  async extractTableData(image, language = 'pol+eng') {
    try {
      await this.initialize(language);

      const { data } = await this.worker.recognize(image, {
        logger: m => console.log(m)
      });

      // Extract table structure from OCR data
      const tableData = this.parseTableFromOCR(data);
      return tableData;
    } catch (error) {
      console.error('Table extraction failed:', error);
      throw new Error(`Table extraction failed: ${error.message}`);
    }
  }

  /**
   * Parse table structure from OCR data
   * @param {Object} ocrData - OCR result data
   * @returns {Array} - Parsed table data
   */
  parseTableFromOCR(ocrData) {
    if (!ocrData.lines) {
      return [];
    }

    const rows = [];

    // Group words by lines and sort by position
    ocrData.lines.forEach(line => {
      const words = line.words
        .filter(word => word.confidence > 50)
        .sort((a, b) => a.bbox.x0 - b.bbox.x0)
        .map(word => word.text);

      if (words.length > 0) {
        rows.push(words);
      }
    });

    return rows;
  }

  /**
   * Terminate OCR worker
   * @returns {Promise<void>}
   */
  async terminate() {
    if (this.worker) {
      await this.worker.terminate();
      this.worker = null;
      this.initialized = false;
      console.log('OCR worker terminated');
    }
  }

  /**
   * Check if language is supported
   * @param {string} language - Language code
   * @returns {boolean} - Whether language is supported
   */
  isLanguageSupported(language) {
    return this.supportedLanguages.some(lang => language.includes(lang));
  }
}

// Create singleton instance
const ocrProcessor = new OCRProcessor();

// Export for ES modules
export default ocrProcessor;

// Export to window object for non-module scripts
if (typeof window !== 'undefined') {
  window.OCRProcessor = OCRProcessor;
  window.ocrProcessor = ocrProcessor;
}

// Local test function
function testOCRProcessor() {
  console.log('=== OCRProcessor Local Test ===');

  try {
    const processor = new OCRProcessor();

    // Test 1: Initialization check
    console.log('Test 1: Initialization check');
    console.log('✓ OCRProcessor instance created');
    console.log('Initialized:', processor.initialized);

    // Test 2: Language support check
    console.log('\nTest 2: Language support check');
    const polishSupported = processor.isLanguageSupported('pol');
    const englishSupported = processor.isLanguageSupported('eng');
    console.log('✓ Language support check works');
    console.log('Polish supported:', polishSupported);
    console.log('English supported:', englishSupported);

    // Test 3: Image preprocessing (mock)
    console.log('\nTest 3: Image preprocessing');
    const canvas = document.createElement('canvas');
    canvas.width = 100;
    canvas.height = 100;
    const ctx = canvas.getContext('2d');
    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, 100, 100);
    ctx.fillStyle = 'black';
    ctx.fillText('Test', 10, 50);

    const preprocessed = processor.preprocessImage(canvas);
    console.log('✓ Image preprocessing works');
    console.log('Preprocessed canvas size:', preprocessed.width, 'x', preprocessed.height);

    // Test 4: OCR data filtering
    console.log('\nTest 4: OCR data filtering');
    const mockOCRData = {
      text: 'Test text with confidence',
      words: [
        { text: 'Test', confidence: 90 },
        { text: 'text', confidence: 80 },
        { text: 'with', confidence: 40 }, // Low confidence
        { text: 'confidence', confidence: 95 }
      ]
    };
    const filtered = processor.filterLowConfidenceText(mockOCRData, 60);
    console.log('✓ OCR data filtering works');
    console.log('Filtered text:', filtered);

    // Test 5: Table parsing
    console.log('\nTest 5: Table parsing');
    const mockTableData = {
      lines: [
        {
          words: [
            { text: 'Item', confidence: 90, bbox: { x0: 10 } },
            { text: 'Price', confidence: 85, bbox: { x0: 100 } },
            { text: 'Qty', confidence: 80, bbox: { x0: 200 } }
          ]
        },
        {
          words: [
            { text: 'Product1', confidence: 88, bbox: { x0: 10 } },
            { text: '100.00', confidence: 92, bbox: { x0: 100 } },
            { text: '2', confidence: 95, bbox: { x0: 200 } }
          ]
        }
      ]
    };
    const tableData = processor.parseTableFromOCR(mockTableData);
    console.log('✓ Table parsing works');
    console.log('Table rows:', tableData.length);

    console.log('\n✅ All OCRProcessor tests passed!');
    return true;

  } catch (error) {
    console.error('❌ OCRProcessor test failed:', error);
    return false;
  }
}

// Run test if in browser environment
if (typeof window !== 'undefined' && window.location) {
  // Uncomment to run test
  // testOCRProcessor();
}

// ===== LOCAL TESTING (Node.js equivalent of Python's if __name__ == '__main__') =====
if (typeof process !== 'undefined' && process.argv && process.argv[1] && process.argv[1].includes('OCRProcessor.js')) {
  console.log('🧪 Running local tests for OCRProcessor...');

  try {
    const processor = new OCRProcessor();

    console.log('✅ Test 1: OCRProcessor initialization');
    console.log('📊 Initialized:', processor.initialized);
    console.log('📊 Worker available:', !!processor.worker);
    console.log('📊 Supported languages:', processor.supportedLanguages.length);
    console.log('📊 Languages:', processor.supportedLanguages.join(', '));

    console.log('\n✅ Test 2: Language support checking');
    const testLanguages = ['pol', 'eng', 'deu', 'fra', 'xyz', 'pol+eng'];
    testLanguages.forEach(lang => {
      const supported = processor.isLanguageSupported(lang);
      console.log(`📊 ${lang}: ${supported ? '✅' : '❌'}`);
    });

    console.log('\n✅ Test 3: OCR data filtering');
    const mockOCRData = {
      text: 'Test text with confidence levels',
      words: [
        { text: 'Test', confidence: 95 },
        { text: 'text', confidence: 85 },
        { text: 'with', confidence: 45 }, // Low confidence
        { text: 'confidence', confidence: 90 },
        { text: 'levels', confidence: 30 } // Very low confidence
      ]
    };

    const filtered60 = processor.filterLowConfidenceText(mockOCRData, 60);
    const filtered80 = processor.filterLowConfidenceText(mockOCRData, 80);
    console.log('📊 Original text:', mockOCRData.text);
    console.log('📊 Filtered (60% confidence):', filtered60);
    console.log('📊 Filtered (80% confidence):', filtered80);

    console.log('\n✅ Test 4: Table parsing from OCR data');
    const mockTableData = {
      lines: [
        {
          words: [
            { text: 'Item', confidence: 90, bbox: { x0: 10 } },
            { text: 'Price', confidence: 85, bbox: { x0: 100 } },
            { text: 'Qty', confidence: 80, bbox: { x0: 200 } }
          ]
        },
        {
          words: [
            { text: 'Product1', confidence: 88, bbox: { x0: 10 } },
            { text: '100.00', confidence: 92, bbox: { x0: 100 } },
            { text: '2', confidence: 95, bbox: { x0: 200 } }
          ]
        },
        {
          words: [
            { text: 'Product2', confidence: 75, bbox: { x0: 10 } },
            { text: '50.00', confidence: 88, bbox: { x0: 100 } },
            { text: '1', confidence: 90, bbox: { x0: 200 } }
          ]
        }
      ]
    };

    const tableData = processor.parseTableFromOCR(mockTableData);
    console.log('📊 Parsed table rows:', tableData.length);
    console.log('📊 First row:', tableData[0] ? tableData[0].join(' | ') : 'None');
    console.log('📊 Second row:', tableData[1] ? tableData[1].join(' | ') : 'None');

    console.log('\n✅ Test 5: OCR data filtering edge cases');
    const emptyOCRData = { text: 'Simple text without words array' };
    const filteredEmpty = processor.filterLowConfidenceText(emptyOCRData, 60);
    console.log('📊 Empty words array handling:', filteredEmpty);

    const noWordsData = { words: [] };
    const filteredNoWords = processor.filterLowConfidenceText(noWordsData, 60);
    console.log('📊 No words handling:', filteredNoWords);

    console.log('\n✅ Test 6: Image processing parameters');
    console.log('📊 Contrast enhancement available:', typeof processor.applyContrastEnhancement === 'function');
    console.log('📊 Noise reduction available:', typeof processor.applyNoiseReduction === 'function');
    console.log('📊 Sharpening available:', typeof processor.applySharpening === 'function');

    console.log('\n🎉 All tests completed for OCRProcessor');
    console.log('📋 Note: Full OCR functionality requires browser environment with Tesseract.js');

  } catch (error) {
    console.error('❌ OCRProcessor test failed:', error);
  }
}
