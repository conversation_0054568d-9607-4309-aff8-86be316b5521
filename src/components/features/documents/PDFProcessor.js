/**
 * PDFProcessor - PDF text extraction and processing
 * Extracted and refactored from components/pdf_extraction.js
 * Handles PDF.js and OCR-based text extraction
 */

import { processingLogger } from '../../utils/ProcessingLogger.js';

export class PDFProcessor {
  constructor() {
    this.pdfJS = null;
    this.initialized = false;
  }

  /**
   * Initialize PDF.js library
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initialized) { return; }

    try {
      // Import PDF.js library
      this.pdfJS = await import('lib/pdf.mjs');
      this.initialized = true;
      console.log('PDFProcessor initialized successfully');
    } catch (error) {
      console.error('Failed to initialize PDFProcessor:', error);
      throw new Error('PDF.js library not available');
    }
  }

  /**
   * Extract text from PDF using PDF.js
   * @param {string|Uint8Array} pdfData - PDF data
   * @param {string} uploadId - Upload tracking ID
   * @returns {Promise<string>} - Extracted text
   */
  async extractTextWithPDFJS(pdfData, uploadId = null) {
    try {
      if (uploadId) {
        processingLogger.info('pdf_extraction', 'Starting PDF.js text extraction', uploadId, {
          dataSize: pdfData.length || pdfData.byteLength || 0
        });
        processingLogger.startTimer('pdf_extraction', uploadId);
      }

      await this.initialize();

      const pdfBytes = this.pdfDataToUint8Array(pdfData);
      const loadingTask = this.pdfJS.getDocument({ data: pdfBytes });
      const pdf = await loadingTask.promise;

      if (uploadId) {
        processingLogger.info('pdf_extraction', 'PDF document loaded successfully', uploadId, {
          numPages: pdf.numPages,
          pdfSize: pdfBytes.length
        });
      }

      let fullText = '';
      for (let i = 1; i <= pdf.numPages; i++) {
        const page = await pdf.getPage(i);
        const textContent = await page.getTextContent();
        const pageText = textContent.items.map(item => item.str).join(' ');
        fullText += pageText + '\n';

        if (uploadId) {
          processingLogger.debug('pdf_extraction', `Extracted text from page ${i}`, uploadId, {
            pageNumber: i,
            pageTextLength: pageText.length,
            textItems: textContent.items.length
          });
        }
      }

      const extractedText = fullText.trim();

      if (uploadId) {
        const duration = processingLogger.endTimer('pdf_extraction', uploadId);

        // Enhanced logging with data content
        processingLogger.logDataExtraction('pdf_extraction', extractedText, uploadId, {
          totalTextLength: extractedText.length,
          pagesProcessed: pdf.numPages,
          extractionDurationMs: duration,
          averageTextPerPage: Math.round(extractedText.length / pdf.numPages),
          wordsExtracted: extractedText.split(/\s+/).length,
          linesExtracted: extractedText.split('\n').length
        });

        processingLogger.info('pdf_extraction', 'PDF.js text extraction completed', uploadId, {
          totalTextLength: extractedText.length,
          pagesProcessed: pdf.numPages,
          extractionDurationMs: duration,
          averageTextPerPage: Math.round(extractedText.length / pdf.numPages)
        });
      }

      return extractedText;
    } catch (error) {
      if (uploadId) {
        processingLogger.endTimer('pdf_extraction', uploadId);
        processingLogger.error('pdf_extraction', 'PDF.js text extraction failed', uploadId, {
          error: error.message,
          stack: error.stack
        });
      }
      console.error('Error extracting text with PDF.js:', error);
      throw new Error(`PDF.js extraction failed: ${error.message}`);
    }
  }

  /**
   * Render PDF page to canvas for OCR
   * @param {string|Uint8Array} pdfData - PDF data
   * @param {number} pageNumber - Page number (1-based)
   * @param {number} scale - Rendering scale
   * @returns {Promise<HTMLCanvasElement>} - Canvas with rendered page
   */
  async renderPageToCanvas(pdfData, pageNumber = 1, scale = 2.0) {
    try {
      await this.initialize();

      const pdfBytes = this.pdfDataToUint8Array(pdfData);
      const loadingTask = this.pdfJS.getDocument({ data: pdfBytes });
      const pdf = await loadingTask.promise;

      if (pageNumber > pdf.numPages) {
        throw new Error(`Page ${pageNumber} does not exist (total pages: ${pdf.numPages})`);
      }

      const page = await pdf.getPage(pageNumber);
      const viewport = page.getViewport({ scale });

      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d');
      canvas.height = viewport.height;
      canvas.width = viewport.width;

      const renderContext = {
        canvasContext: context,
        viewport: viewport
      };

      await page.render(renderContext).promise;
      return canvas;
    } catch (error) {
      console.error('Error rendering PDF page:', error);
      throw new Error(`PDF page rendering failed: ${error.message}`);
    }
  }

  /**
   * Get PDF metadata
   * @param {string|Uint8Array} pdfData - PDF data
   * @returns {Promise<Object>} - PDF metadata
   */
  async getPDFMetadata(pdfData) {
    try {
      await this.initialize();

      const pdfBytes = this.pdfDataToUint8Array(pdfData);
      const loadingTask = this.pdfJS.getDocument({ data: pdfBytes });
      const pdf = await loadingTask.promise;

      const metadata = await pdf.getMetadata();

      return {
        numPages: pdf.numPages,
        title: metadata.info?.Title || '',
        author: metadata.info?.Author || '',
        subject: metadata.info?.Subject || '',
        creator: metadata.info?.Creator || '',
        producer: metadata.info?.Producer || '',
        creationDate: metadata.info?.CreationDate || null,
        modificationDate: metadata.info?.ModDate || null
      };
    } catch (error) {
      console.error('Error getting PDF metadata:', error);
      return {
        numPages: 0,
        title: '',
        author: '',
        subject: '',
        creator: '',
        producer: '',
        creationDate: null,
        modificationDate: null
      };
    }
  }

  /**
   * Extract text from PDF with fallback strategies
   * @param {string|Uint8Array} pdfData - PDF data
   * @param {Object} options - Extraction options
   * @returns {Promise<Object>} - Extraction result
   */
  async extractText(pdfData, options = {}) {
    const {
      enableOCR = false,
      ocrLanguage = 'pol+eng',
      minTextLength = 100,
      checkKeywords = true
    } = options;

    const result = {
      text: '',
      method: 'none',
      ocrUsed: false,
      success: false,
      metadata: null
    };

    try {
      // Get PDF metadata
      result.metadata = await this.getPDFMetadata(pdfData);

      // First try PDF.js extraction
      const pdfJsText = await this.extractTextWithPDFJS(pdfData);

      if (pdfJsText && pdfJsText.length >= minTextLength) {
        const hasKeywords = checkKeywords ? this.checkForInvoiceKeywords(pdfJsText) : true;

        if (hasKeywords) {
          result.text = pdfJsText;
          result.method = 'pdfjs';
          result.success = true;
          return result;
        }
      }

      // If PDF.js didn't work well and OCR is enabled, try OCR
      if (enableOCR && window.ocrProcessor) {
        try {
          const canvas = await this.renderPageToCanvas(pdfData, 1, 2.0);
          const ocrText = await window.ocrProcessor.performOCR(canvas, ocrLanguage);

          if (ocrText && ocrText.length >= minTextLength) {
            // Combine texts if both are available
            if (pdfJsText && pdfJsText.length > 0) {
              result.text = this.combineTextResults(pdfJsText, ocrText);
              result.method = 'combined';
            } else {
              result.text = ocrText;
              result.method = 'ocr';
            }
            result.ocrUsed = true;
            result.success = true;
            return result;
          }
        } catch (ocrError) {
          console.warn('OCR extraction failed:', ocrError);
        }
      }

      // Fallback to whatever we got from PDF.js
      result.text = pdfJsText || '';
      result.method = 'pdfjs_fallback';
      result.success = result.text.length > 0;

      return result;
    } catch (error) {
      console.error('PDF text extraction failed:', error);
      result.error = error.message;
      return result;
    }
  }

  /**
   * Check if text contains invoice keywords
   * @param {string} text - Text to check
   * @returns {boolean} - Whether text contains invoice keywords
   */
  checkForInvoiceKeywords(text) {
    const keywords = [
      // Polish keywords
      'faktura', 'vat', 'nip', 'sprzedawca', 'nabywca', 'kwota', 'suma', 'razem',
      'podatek', 'netto', 'brutto', 'płatność', 'termin', 'data', 'wystawienia',
      // English keywords
      'invoice', 'tax', 'seller', 'buyer', 'amount', 'total', 'payment', 'date',
      'due', 'net', 'gross', 'vat', 'tax id'
    ];

    const lowerText = text.toLowerCase();
    const foundKeywords = keywords.filter(keyword => lowerText.includes(keyword));

    // If we found at least 5 keywords, consider it a good extraction
    return foundKeywords.length >= 5;
  }

  /**
   * Combine text results from different extraction methods
   * @param {string} pdfJsText - Text from PDF.js
   * @param {string} ocrText - Text from OCR
   * @returns {string} - Combined text
   */
  combineTextResults(pdfJsText, ocrText) {
    return `${pdfJsText}\n\n--- OCR ENHANCED TEXT ---\n\n${ocrText}`;
  }

  /**
   * Convert PDF data to Uint8Array
   * @param {string|Uint8Array} pdfData - PDF data
   * @returns {Uint8Array} - PDF data as Uint8Array
   */
  pdfDataToUint8Array(pdfData) {
    if (pdfData instanceof Uint8Array) {
      return pdfData;
    }

    if (typeof pdfData !== 'string') {
      throw new Error('PDF data must be string or Uint8Array');
    }

    // Handle data URLs
    if (pdfData.startsWith('data:application/pdf;base64,')) {
      const base64 = pdfData.split(',')[1];
      return this.base64ToUint8Array(base64);
    }

    // Handle other base64 data URLs
    if (pdfData.includes('base64,')) {
      const base64 = pdfData.split('base64,')[1];
      return this.base64ToUint8Array(base64);
    }

    // Handle raw base64
    if (pdfData.match(/^[A-Za-z0-9+/=]+$/)) {
      return this.base64ToUint8Array(pdfData);
    }

    throw new Error('Unknown PDF data format');
  }

  /**
   * Convert base64 string to Uint8Array
   * @param {string} base64 - Base64 string
   * @returns {Uint8Array} - Uint8Array
   */
  base64ToUint8Array(base64) {
    try {
      const binaryStr = atob(base64);
      const bytes = new Uint8Array(binaryStr.length);
      for (let i = 0; i < binaryStr.length; i++) {
        bytes[i] = binaryStr.charCodeAt(i);
      }
      return bytes;
    } catch (error) {
      throw new Error(`Invalid base64 data: ${error.message}`);
    }
  }

  /**
   * Validate PDF data
   * @param {string|Uint8Array} pdfData - PDF data to validate
   * @returns {boolean} - Whether data is valid PDF
   */
  validatePDFData(pdfData) {
    try {
      const bytes = this.pdfDataToUint8Array(pdfData);

      // Check PDF header
      const header = new TextDecoder().decode(bytes.slice(0, 5));
      return header === '%PDF-';
    } catch (error) {
      return false;
    }
  }
}

// Create singleton instance
const pdfProcessor = new PDFProcessor();

// Export for ES modules
export default pdfProcessor;

// Export to window object for non-module scripts
if (typeof window !== 'undefined') {
  window.PDFProcessor = PDFProcessor;
  window.pdfProcessor = pdfProcessor;
}

// Local test function
function testPDFProcessor() {
  console.log('=== PDFProcessor Local Test ===');

  try {
    const processor = new PDFProcessor();

    // Test 1: Initialization
    console.log('Test 1: Initialization');
    console.log('✓ PDFProcessor instance created');

    // Test 2: Base64 conversion
    console.log('\nTest 2: Base64 conversion');
    const testBase64 = 'SGVsbG8gV29ybGQ='; // "Hello World"
    const uint8Array = processor.base64ToUint8Array(testBase64);
    console.log('✓ Base64 to Uint8Array conversion works');
    console.log('Converted length:', uint8Array.length);

    // Test 3: PDF data validation
    console.log('\nTest 3: PDF data validation');
    const fakePDFData = '%PDF-1.4\n%âãÏÓ';
    const isValid = processor.validatePDFData(fakePDFData);
    console.log('✓ PDF validation works');
    console.log('Fake PDF valid:', isValid);

    // Test 4: Invoice keywords check
    console.log('\nTest 4: Invoice keywords check');
    const testText = 'FAKTURA VAT nr 123/2024 Sprzedawca: Test Sp. z o.o. NIP: *********0 Nabywca: Buyer Ltd. Kwota netto: 100.00 PLN VAT: 23.00 PLN Brutto: 123.00 PLN';
    const hasKeywords = processor.checkForInvoiceKeywords(testText);
    console.log('✓ Invoice keywords check works');
    console.log('Has invoice keywords:', hasKeywords);

    // Test 5: Text combination
    console.log('\nTest 5: Text combination');
    const combined = processor.combineTextResults('PDF text', 'OCR text');
    console.log('✓ Text combination works');
    console.log('Combined length:', combined.length);

    console.log('\n✅ All PDFProcessor tests passed!');
    return true;

  } catch (error) {
    console.error('❌ PDFProcessor test failed:', error);
    return false;
  }
}

// Run test if in browser environment
if (typeof window !== 'undefined' && window.location) {
  // Uncomment to run test
  // testPDFProcessor();
}

// ===== LOCAL TESTING (Node.js equivalent of Python's if __name__ == '__main__') =====
if (typeof process !== 'undefined' && process.argv && process.argv[1] && process.argv[1].includes('PDFProcessor.js')) {
  console.log('🧪 Running local tests for PDFProcessor...');

  // Mock atob for Node.js
  if (typeof atob === 'undefined') {
    global.atob = (str) => Buffer.from(str, 'base64').toString('binary');
  }

  try {
    const processor = new PDFProcessor();

    console.log('✅ Test 1: PDFProcessor initialization');
    console.log('📊 Initialized:', processor.initialized);
    console.log('📊 PDF.js available:', !!processor.pdfJS);

    console.log('\n✅ Test 2: Base64 to Uint8Array conversion');
    const testBase64 = 'SGVsbG8gV29ybGQ='; // "Hello World"
    const uint8Array = processor.base64ToUint8Array(testBase64);
    console.log('📊 Original base64:', testBase64);
    console.log('📊 Converted length:', uint8Array.length);
    console.log('📊 First few bytes:', Array.from(uint8Array.slice(0, 5)));

    console.log('\n✅ Test 3: PDF data validation');
    const validPDFHeader = '%PDF-1.4\n%âãÏÓ';
    const invalidData = 'Not a PDF file';
    const validResult = processor.validatePDFData(validPDFHeader);
    const invalidResult = processor.validatePDFData(invalidData);
    console.log('📊 Valid PDF header:', validResult);
    console.log('📊 Invalid data:', invalidResult);

    console.log('\n✅ Test 4: Invoice keywords detection');
    const testTexts = [
      'FAKTURA VAT nr 123/2024 Sprzedawca: Test Sp. z o.o. NIP: *********0 Nabywca: Buyer Ltd. Kwota netto: 100.00 PLN VAT: 23.00 PLN Brutto: 123.00 PLN',
      'Invoice #123 Seller: Company Ltd Tax ID: ********* Buyer: Customer Inc Amount: $100.00 VAT: $23.00 Total: $123.00',
      'This is just a regular document without any invoice-related content.',
      'Faktura sprzedaż podatek VAT netto brutto płatność termin data'
    ];

    testTexts.forEach((text, index) => {
      const hasKeywords = processor.checkForInvoiceKeywords(text);
      console.log(`📊 Text ${index + 1} has invoice keywords: ${hasKeywords ? '✅' : '❌'}`);
    });

    console.log('\n✅ Test 5: Text combination');
    const pdfText = 'Text extracted from PDF.js';
    const ocrText = 'Text extracted from OCR';
    const combined = processor.combineTextResults(pdfText, ocrText);
    console.log('📊 PDF text length:', pdfText.length);
    console.log('📊 OCR text length:', ocrText.length);
    console.log('📊 Combined text length:', combined.length);
    console.log('📊 Contains separator:', combined.includes('--- OCR ENHANCED TEXT ---'));

    console.log('\n✅ Test 6: PDF data format handling');
    const dataUrlPDF = 'data:application/pdf;base64,JVBERi0xLjQ='; // %PDF-1.4 in base64
    const rawBase64 = 'JVBERi0xLjQ=';

    try {
      const dataUrlBytes = processor.pdfDataToUint8Array(dataUrlPDF);
      console.log('📊 Data URL conversion: ✅');
      console.log('📊 Data URL bytes length:', dataUrlBytes.length);
    } catch (err) {
      console.log('📊 Data URL conversion: ❌', err.message);
    }

    try {
      const rawBytes = processor.pdfDataToUint8Array(rawBase64);
      console.log('📊 Raw base64 conversion: ✅');
      console.log('📊 Raw bytes length:', rawBytes.length);
    } catch (err) {
      console.log('📊 Raw base64 conversion: ❌', err.message);
    }

    console.log('\n✅ Test 7: Invalid input handling');
    try {
      processor.pdfDataToUint8Array('invalid data format');
      console.log('📊 Invalid data handling: ❌ (should have thrown)');
    } catch (err) {
      console.log('📊 Invalid data handling: ✅ (correctly threw)');
    }

    try {
      processor.base64ToUint8Array('invalid base64!!!');
      console.log('📊 Invalid base64 handling: ❌ (should have thrown)');
    } catch (err) {
      console.log('📊 Invalid base64 handling: ✅ (correctly threw)');
    }

    console.log('\n🎉 All tests completed for PDFProcessor');
    console.log('📋 Note: Full PDF processing requires browser environment with PDF.js library');

  } catch (error) {
    console.error('❌ PDFProcessor test failed:', error);
  }
}
