import React from 'react';

/**
 * Unified Loading Spinner Component
 * Consolidates all loading spinner implementations across the codebase
 *
 * CONSOLIDATION: Replaces multiple spinner implementations:
 * - src/popup/styles/globals.css loading-spinner
 * - src/popup/App.jsx inline spinner
 * - src/popup/error-handler.js spinner detection
 * - Various component-specific spinners
 */
export function LoadingSpinner({
  size = 'medium',
  color = 'blue',
  message = '',
  className = '',
  variant = 'default'
}) {
  // Size configurations
  const sizeClasses = {
    small: 'w-4 h-4 border-2',
    medium: 'w-8 h-8 border-3',
    large: 'w-12 h-12 border-4',
    xlarge: 'w-16 h-16 border-4'
  };

  // Color configurations
  const colorClasses = {
    blue: 'border-blue-500 border-t-transparent',
    gray: 'border-gray-400 border-t-transparent',
    green: 'border-green-500 border-t-transparent',
    red: 'border-red-500 border-t-transparent',
    purple: 'border-purple-500 border-t-transparent',
    indigo: 'border-indigo-500 border-t-transparent'
  };

  // Variant configurations
  const variants = {
    default: 'animate-spin rounded-full',
    pulse: 'animate-pulse rounded-full bg-current',
    dots: 'flex space-x-1',
    bars: 'flex space-x-1'
  };

  // Get classes
  const sizeClass = sizeClasses[size] || sizeClasses.medium;
  const colorClass = colorClasses[color] || colorClasses.blue;
  const variantClass = variants[variant] || variants.default;

  // Render different variants
  const renderSpinner = () => {
    switch (variant) {
      case 'pulse':
        return (
          <div className={`${sizeClass} ${variantClass} ${colorClass.split(' ')[0]} opacity-75`} />
        );

      case 'dots':
        return (
          <div className={variantClass}>
            {[0, 1, 2].map(i => (
              <div
                key={i}
                className={`w-2 h-2 rounded-full ${colorClass.split(' ')[0]} animate-bounce`}
                style={{ animationDelay: `${i * 0.1}s` }}
              />
            ))}
          </div>
        );

      case 'bars':
        return (
          <div className={variantClass}>
            {[0, 1, 2, 3].map(i => (
              <div
                key={i}
                className={`w-1 h-4 ${colorClass.split(' ')[0]} animate-pulse`}
                style={{ animationDelay: `${i * 0.1}s` }}
              />
            ))}
          </div>
        );

      default:
        return (
          <div className={`${sizeClass} ${variantClass} ${colorClass}`} />
        );
    }
  };

  return (
    <div className={`flex flex-col items-center justify-center ${className}`}>
      {renderSpinner()}
      {message && (
        <p className="mt-2 text-sm text-gray-600 text-center">
          {message}
        </p>
      )}
    </div>
  );
}

/**
 * Loading Overlay Component
 * Full-screen loading overlay with spinner
 */
export function LoadingOverlay({
  isVisible = false,
  message = 'Loading...',
  size = 'large',
  color = 'blue',
  backdrop = 'blur'
}) {
  if (!isVisible) { return null; }

  const backdropClasses = {
    blur: 'backdrop-blur-sm bg-white/80',
    dark: 'bg-black/50',
    light: 'bg-white/90',
    none: 'bg-transparent'
  };

  return (
    <div className={`fixed inset-0 z-50 flex items-center justify-center ${backdropClasses[backdrop]}`}>
      <div className="bg-white rounded-lg shadow-lg p-6 max-w-sm mx-4">
        <LoadingSpinner
          size={size}
          color={color}
          message={message}
          className="py-4"
        />
      </div>
    </div>
  );
}

/**
 * Inline Loading Component
 * For inline loading states within components
 */
export function InlineLoading({
  message = 'Loading...',
  size = 'small',
  color = 'gray',
  className = ''
}) {
  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <LoadingSpinner size={size} color={color} />
      <span className="text-sm text-gray-600">{message}</span>
    </div>
  );
}

/**
 * Button Loading State
 * For buttons with loading states
 */
export function ButtonLoading({
  isLoading = false,
  children,
  loadingText = 'Loading...',
  size = 'small',
  color = 'white',
  className = '',
  ...buttonProps
}) {
  return (
    <button
      {...buttonProps}
      disabled={isLoading || buttonProps.disabled}
      className={`relative ${className} ${isLoading ? 'cursor-not-allowed' : ''}`}
    >
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <LoadingSpinner size={size} color={color} />
        </div>
      )}
      <span className={isLoading ? 'opacity-0' : 'opacity-100'}>
        {isLoading ? loadingText : children}
      </span>
    </button>
  );
}

/**
 * Page Loading Component
 * For full page loading states
 */
export function PageLoading({
  message = 'Loading page...',
  error = null,
  onRetry = null
}) {
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
        <div className="text-center max-w-sm">
          <div className="text-red-500 text-4xl mb-4">⚠️</div>
          <h2 className="text-lg font-semibold text-gray-900 mb-2">
            Loading Error
          </h2>
          <p className="text-gray-600 text-sm mb-4">
            {error}
          </p>
          {onRetry && (
            <button
              onClick={onRetry}
              className="bg-blue-600 text-white px-4 py-2 rounded text-sm hover:bg-blue-700 transition-colors"
            >
              Retry
            </button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <LoadingSpinner
        size="large"
        color="blue"
        message={message}
      />
    </div>
  );
}

export default LoadingSpinner;
