import React from 'react';
import LoadingSpinner from './LoadingSpinner.jsx';

/**
 * Unified Progress Component
 * Consolidates all progress bar implementations across the codebase
 *
 * CONSOLIDATION: Replaces multiple progress implementations:
 * - src/components/upload/ProgressBar.jsx
 * - src/popup/components/upload/FileUploadProgress.jsx
 * - src/components/upload/UploadProgress.jsx
 * - src/components/upload/FileProgressItem.jsx
 */
export function UnifiedProgress({
  progress = 0,
  variant = 'default',
  size = 'medium',
  color = 'blue',
  animated = true,
  striped = false,
  showPercentage = true,
  message = '',
  className = ''
}) {
  // Normalize progress to 0-100 range
  const normalizedProgress = Math.min(Math.max(progress, 0), 100);

  // Size configurations
  const sizeClasses = {
    small: 'h-2',
    medium: 'h-4',
    large: 'h-6',
    xlarge: 'h-8'
  };

  // Color configurations
  const colorClasses = {
    blue: 'bg-blue-600',
    green: 'bg-green-600',
    yellow: 'bg-yellow-500',
    red: 'bg-red-600',
    purple: 'bg-purple-600',
    indigo: 'bg-indigo-600',
    gray: 'bg-gray-500'
  };

  // Variant configurations
  const variants = {
    default: 'rounded-full',
    square: 'rounded-none',
    rounded: 'rounded-lg'
  };

  const sizeClass = sizeClasses[size] || sizeClasses.medium;
  const colorClass = colorClasses[color] || colorClasses.blue;
  const variantClass = variants[variant] || variants.default;

  return (
    <div className={`w-full ${className}`}>
      {/* Progress bar container */}
      <div className={`relative w-full bg-gray-200 ${variantClass} ${sizeClass} overflow-hidden`}>
        {/* Progress fill */}
        <div
          className={`${sizeClass} ${colorClass} ${variantClass} transition-all duration-300 ease-out relative overflow-hidden`}
          style={{ width: `${normalizedProgress}%` }}
        >
          {/* Animated shine effect */}
          {animated && normalizedProgress > 0 && (
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-pulse" />
          )}

          {/* Striped pattern overlay */}
          {striped && (
            <div className="absolute inset-0 bg-stripes-pattern opacity-20" />
          )}
        </div>

        {/* Indeterminate animation for unknown progress */}
        {normalizedProgress === 0 && animated && (
          <div className="absolute inset-0">
            <div className={`${sizeClass} w-1/3 ${colorClass} opacity-60 animate-pulse ${variantClass}`} />
          </div>
        )}
      </div>

      {/* Progress info */}
      <div className="flex justify-between items-center mt-1">
        {message && (
          <span className="text-sm text-gray-600">{message}</span>
        )}
        {showPercentage && (
          <span className="text-sm font-medium text-gray-900">
            {Math.round(normalizedProgress)}%
          </span>
        )}
      </div>
    </div>
  );
}

/**
 * File Progress Component
 * For individual file upload progress
 */
export function FileProgress({
  file,
  progress = 0,
  status = 'pending',
  stage = 'uploading',
  error = null,
  onCancel,
  onRetry,
  className = ''
}) {
  // Status configurations
  const statusConfig = {
    pending: {
      icon: '⏳',
      title: 'Pending',
      color: 'gray',
      bgColor: 'bg-gray-50'
    },
    uploading: {
      icon: '📤',
      title: 'Uploading',
      color: 'blue',
      bgColor: 'bg-blue-50'
    },
    validating: {
      icon: '🔍',
      title: 'Validating',
      color: 'yellow',
      bgColor: 'bg-yellow-50'
    },
    processing: {
      icon: '⚙️',
      title: 'Processing',
      color: 'indigo',
      bgColor: 'bg-indigo-50'
    },
    complete: {
      icon: '✅',
      title: 'Complete',
      color: 'green',
      bgColor: 'bg-green-50'
    },
    error: {
      icon: '❌',
      title: 'Error',
      color: 'red',
      bgColor: 'bg-red-50'
    },
    cancelled: {
      icon: '⏹️',
      title: 'Cancelled',
      color: 'gray',
      bgColor: 'bg-gray-50'
    }
  };

  const currentStatus = statusConfig[status] || statusConfig.pending;

  return (
    <div className={`border rounded-lg p-4 ${currentStatus.bgColor} ${className}`}>
      {/* File info header */}
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          <span className="text-lg">{currentStatus.icon}</span>
          <div>
            <p className="font-medium text-gray-900 truncate max-w-xs">
              {file?.name || 'Unknown file'}
            </p>
            <p className="text-sm text-gray-500">
              {file?.size ? formatFileSize(file.size) : ''} • {currentStatus.title}
            </p>
          </div>
        </div>

        {/* Action buttons */}
        <div className="flex space-x-1">
          {status === 'error' && onRetry && (
            <button
              onClick={() => onRetry(file)}
              className="text-blue-600 hover:text-blue-800 text-sm"
            >
              Retry
            </button>
          )}
          {['uploading', 'validating', 'processing'].includes(status) && onCancel && (
            <button
              onClick={() => onCancel(file)}
              className="text-red-600 hover:text-red-800 text-sm"
            >
              Cancel
            </button>
          )}
        </div>
      </div>

      {/* Progress bar */}
      {!['complete', 'error', 'cancelled'].includes(status) && (
        <UnifiedProgress
          progress={progress}
          color={currentStatus.color}
          size="small"
          animated={true}
          showPercentage={false}
          message={stage}
        />
      )}

      {/* Error message */}
      {error && (
        <div className="mt-2 p-2 bg-red-100 border border-red-200 rounded text-sm text-red-700">
          {error}
        </div>
      )}
    </div>
  );
}

/**
 * Multi-File Progress Component
 * For tracking progress of multiple files
 */
export function MultiFileProgress({
  files = [],
  overallProgress = 0,
  onCancel,
  onRetry,
  onClear,
  className = ''
}) {
  const totalFiles = files.length;
  const completedFiles = files.filter(f => f.status === 'complete').length;
  const failedFiles = files.filter(f => f.status === 'error').length;
  const processingFiles = files.filter(f =>
    ['uploading', 'validating', 'processing'].includes(f.status)
  ).length;

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Overall progress */}
      <div className="bg-white rounded-lg border p-4">
        <div className="flex justify-between items-center mb-2">
          <h3 className="font-medium text-gray-900">Upload Progress</h3>
          <span className="text-sm text-gray-500">
            {completedFiles} of {totalFiles} completed
          </span>
        </div>

        <UnifiedProgress
          progress={overallProgress}
          color="blue"
          size="medium"
          animated={processingFiles > 0}
          message={`${processingFiles} processing, ${failedFiles} failed`}
        />

        {/* Action buttons */}
        <div className="flex justify-end space-x-2 mt-3">
          {processingFiles > 0 && onCancel && (
            <button
              onClick={onCancel}
              className="text-red-600 hover:text-red-800 text-sm"
            >
              Cancel All
            </button>
          )}
          {failedFiles > 0 && onRetry && (
            <button
              onClick={onRetry}
              className="text-blue-600 hover:text-blue-800 text-sm"
            >
              Retry Failed
            </button>
          )}
          {completedFiles > 0 && onClear && (
            <button
              onClick={onClear}
              className="text-gray-600 hover:text-gray-800 text-sm"
            >
              Clear Completed
            </button>
          )}
        </div>
      </div>

      {/* Individual file progress */}
      <div className="space-y-2">
        {files.map((file, index) => (
          <FileProgress
            key={file.id || index}
            file={file}
            progress={file.progress}
            status={file.status}
            stage={file.stage}
            error={file.error}
            onCancel={onCancel}
            onRetry={onRetry}
          />
        ))}
      </div>
    </div>
  );
}

/**
 * Utility function to format file size
 */
function formatFileSize(bytes) {
  if (bytes === 0) { return '0 Bytes'; }
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

export default UnifiedProgress;
