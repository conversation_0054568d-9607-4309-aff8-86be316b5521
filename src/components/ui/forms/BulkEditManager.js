/**
 * BulkEditManager - Bulk editing functionality for invoices
 * Extracted and refactored from components/bulk_edit.js
 * Handles bulk operations on selected invoices
 */

import { StorageAPI } from '../../api/StorageAPI.js';
import { FAKTUROWNIA_DOCUMENT_TYPES, VALID_DOCUMENT_TYPES } from '../../core/config/documentTypes.js';

export class BulkEditManager {
  constructor() {
    this.storageAPI = new StorageAPI();
    this.modal = null;
    this.selectedInvoices = [];
    this.onUpdateCallback = null;
  }

  /**
   * Initialize bulk edit functionality
   * @param {Object} options - Initialization options
   */
  initialize(options = {}) {
    const {
      onUpdateCallback = null,
      buttonContainerSelector = '.action-buttons'
    } = options;

    this.onUpdateCallback = onUpdateCallback;
    this.addBulkEditButton(buttonContainerSelector);

    console.log('BulkEditManager initialized');
  }

  /**
   * Add bulk edit button to the UI
   * @param {string} containerSelector - Container selector for the button
   */
  addBulkEditButton(containerSelector) {
    const container = document.querySelector(containerSelector);

    if (container) {
      const bulkEditButton = document.createElement('button');
      bulkEditButton.className = 'primary-button';
      bulkEditButton.id = 'bulkEditBtn';
      bulkEditButton.innerHTML = '<span class="button-icon">✏️</span><span>Bulk Edit Selected</span>';
      bulkEditButton.addEventListener('click', () => this.showBulkEditModal());

      container.appendChild(bulkEditButton);
    }
  }

  /**
   * Show bulk edit modal
   */
  showBulkEditModal() {
    const selectedCheckboxes = this.getSelectedCheckboxes();

    if (selectedCheckboxes.length === 0) {
      this.showError('Please select at least one invoice to edit.');
      return;
    }

    this.selectedInvoices = Array.from(selectedCheckboxes).map(checkbox => checkbox.dataset.invoiceId);
    this.createModal(selectedCheckboxes.length);
  }

  /**
   * Get selected checkboxes from the UI
   * @returns {NodeList} - Selected checkboxes
   */
  getSelectedCheckboxes() {
    return document.querySelectorAll(
      '#purchaseInvoicesTableBody input[type="checkbox"]:checked, ' +
      '#salesInvoicesTableBody input[type="checkbox"]:checked, ' +
      '.invoice-checkbox:checked'
    );
  }

  /**
   * Create and show the bulk edit modal
   * @param {number} selectedCount - Number of selected invoices
   */
  createModal(selectedCount) {
    // Remove existing modal if any
    if (this.modal) {
      this.closeModal();
    }

    // Create modal
    this.modal = document.createElement('div');
    this.modal.className = 'modal bulk-edit-modal';
    this.modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
    `;

    // Create modal content
    const modalContent = document.createElement('div');
    modalContent.className = 'modal-content';
    modalContent.style.cssText = `
      background: white;
      border-radius: 8px;
      width: 600px;
      max-width: 90%;
      max-height: 90%;
      overflow-y: auto;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    `;

    // Create header
    const header = this.createModalHeader(selectedCount);
    modalContent.appendChild(header);

    // Create form
    const form = this.createBulkEditForm();
    modalContent.appendChild(form);

    this.modal.appendChild(modalContent);
    document.body.appendChild(this.modal);

    // Close modal on outside click
    this.modal.addEventListener('click', (e) => {
      if (e.target === this.modal) {
        this.closeModal();
      }
    });
  }

  /**
   * Create modal header
   * @param {number} selectedCount - Number of selected invoices
   * @returns {Element} - Header element
   */
  createModalHeader(selectedCount) {
    const header = document.createElement('div');
    header.className = 'modal-header';
    header.style.cssText = `
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px;
      border-bottom: 1px solid #e5e7eb;
    `;

    const title = document.createElement('h3');
    title.textContent = `Bulk Edit ${selectedCount} Invoice(s)`;
    title.style.margin = '0';
    title.style.fontSize = '18px';
    title.style.fontWeight = '600';
    header.appendChild(title);

    const closeButton = document.createElement('button');
    closeButton.innerHTML = '&times;';
    closeButton.style.cssText = `
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      color: #6b7280;
      padding: 0;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
    `;
    closeButton.onclick = () => this.closeModal();
    header.appendChild(closeButton);

    return header;
  }

  /**
   * Create bulk edit form
   * @returns {Element} - Form element
   */
  createBulkEditForm() {
    const form = document.createElement('form');
    form.style.padding = '20px';

    // Document Kind field
    form.appendChild(this.createDocumentKindField());

    // Status field
    form.appendChild(this.createStatusField());

    // Accounting Kind field
    form.appendChild(this.createAccountingKindField());

    // Income Type field
    form.appendChild(this.createIncomeTypeField());

    // Buttons
    form.appendChild(this.createFormButtons());

    return form;
  }

  /**
   * Create document kind field
   * @returns {Element} - Field element
   */
  createDocumentKindField() {
    const fieldset = this.createFieldset('Document Kind');

    const select = document.createElement('select');
    select.id = 'bulkEditKind';
    select.className = 'form-select';
    this.styleFormElement(select);

    // Add options
    const options = [
      { value: '', text: '-- No Change --' },
      ...Object.entries(FAKTUROWNIA_DOCUMENT_TYPES).map(([value, text]) => ({ value, text }))
    ];

    options.forEach(option => {
      const optionElement = document.createElement('option');
      optionElement.value = option.value;
      optionElement.textContent = option.text;
      select.appendChild(optionElement);
    });

    fieldset.appendChild(select);
    return fieldset;
  }

  /**
   * Create status field
   * @returns {Element} - Field element
   */
  createStatusField() {
    const fieldset = this.createFieldset('Invoice Status');

    const select = document.createElement('select');
    select.id = 'bulkEditStatus';
    select.className = 'form-select';
    this.styleFormElement(select);

    const statusOptions = [
      { value: '', text: '-- No Change --' },
      { value: 'draft', text: 'Draft' },
      { value: 'sent', text: 'Sent' },
      { value: 'paid', text: 'Paid' },
      { value: 'partial', text: 'Partially Paid' },
      { value: 'overdue', text: 'Overdue' },
      { value: 'cancelled', text: 'Cancelled' }
    ];

    statusOptions.forEach(option => {
      const optionElement = document.createElement('option');
      optionElement.value = option.value;
      optionElement.textContent = option.text;
      select.appendChild(optionElement);
    });

    fieldset.appendChild(select);
    return fieldset;
  }

  /**
   * Create accounting kind field
   * @returns {Element} - Field element
   */
  createAccountingKindField() {
    const fieldset = this.createFieldset('Accounting Kind (Purchase Invoices)');

    const select = document.createElement('select');
    select.id = 'bulkEditAccountingKind';
    select.className = 'form-select';
    this.styleFormElement(select);

    const accountingKindOptions = [
      { value: '', text: '-- No Change --' },
      { value: 'purchases', text: 'Purchase of Goods and Materials' },
      { value: 'expenses', text: 'Business Expenses' },
      { value: 'media', text: 'Media and Telecommunications Services' },
      { value: 'salary', text: 'Salaries' },
      { value: 'fuel', text: 'Fuel and Vehicle Operation' },
      { value: 'fixed_assets', text: 'Fixed Assets' },
      { value: 'no_vat_deduction', text: 'No VAT Deduction Possible' }
    ];

    accountingKindOptions.forEach(option => {
      const optionElement = document.createElement('option');
      optionElement.value = option.value;
      optionElement.textContent = option.text;
      select.appendChild(optionElement);
    });

    fieldset.appendChild(select);
    return fieldset;
  }

  /**
   * Create income type field
   * @returns {Element} - Field element
   */
  createIncomeTypeField() {
    const fieldset = this.createFieldset('Invoice Type');

    const select = document.createElement('select');
    select.id = 'bulkEditIncome';
    select.className = 'form-select';
    this.styleFormElement(select);

    const incomeOptions = [
      { value: '', text: '-- No Change --' },
      { value: '1', text: 'Sales Invoice (Income)' },
      { value: '0', text: 'Purchase Invoice (Expense)' }
    ];

    incomeOptions.forEach(option => {
      const optionElement = document.createElement('option');
      optionElement.value = option.value;
      optionElement.textContent = option.text;
      select.appendChild(optionElement);
    });

    fieldset.appendChild(select);
    return fieldset;
  }

  /**
   * Create form buttons
   * @returns {Element} - Buttons container
   */
  createFormButtons() {
    const container = document.createElement('div');
    container.style.cssText = `
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      margin-top: 20px;
      padding-top: 20px;
      border-top: 1px solid #e5e7eb;
    `;

    const cancelButton = document.createElement('button');
    cancelButton.textContent = 'Cancel';
    cancelButton.type = 'button';
    cancelButton.className = 'secondary-button';
    cancelButton.style.cssText = `
      padding: 10px 20px;
      border: 1px solid #d1d5db;
      background: white;
      color: #374151;
      border-radius: 6px;
      cursor: pointer;
    `;
    cancelButton.onclick = () => this.closeModal();
    container.appendChild(cancelButton);

    const saveButton = document.createElement('button');
    saveButton.textContent = 'Save Changes';
    saveButton.type = 'button';
    saveButton.className = 'primary-button';
    saveButton.style.cssText = `
      padding: 10px 20px;
      border: none;
      background: #3b82f6;
      color: white;
      border-radius: 6px;
      cursor: pointer;
    `;
    saveButton.onclick = () => this.saveBulkChanges();
    container.appendChild(saveButton);

    return container;
  }

  /**
   * Create a fieldset with legend
   * @param {string} legend - Legend text
   * @returns {Element} - Fieldset element
   */
  createFieldset(legend) {
    const fieldset = document.createElement('fieldset');
    fieldset.style.cssText = `
      margin-bottom: 20px;
      border: 1px solid #d1d5db;
      border-radius: 6px;
      padding: 15px;
    `;

    const legendElement = document.createElement('legend');
    legendElement.textContent = legend;
    legendElement.style.cssText = `
      padding: 0 10px;
      font-weight: 500;
      color: #374151;
    `;
    fieldset.appendChild(legendElement);

    return fieldset;
  }

  /**
   * Style form elements consistently
   * @param {Element} element - Element to style
   */
  styleFormElement(element) {
    element.style.cssText = `
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #d1d5db;
      border-radius: 6px;
      font-size: 14px;
      background: white;
    `;
  }

  /**
   * Save bulk changes
   */
  async saveBulkChanges() {
    try {
      const changes = this.getFormValues();

      if (Object.keys(changes).length === 0) {
        this.showError('No changes specified.');
        return;
      }

      await this.applyBulkChanges(this.selectedInvoices, changes);
      this.closeModal();

      // Call update callback if provided
      if (this.onUpdateCallback) {
        this.onUpdateCallback();
      }

    } catch (error) {
      console.error('Error saving bulk changes:', error);
      this.showError(`Error saving changes: ${error.message}`);
    }
  }

  /**
   * Get form values
   * @returns {Object} - Form values
   */
  getFormValues() {
    const changes = {};

    const kind = document.getElementById('bulkEditKind')?.value;
    const status = document.getElementById('bulkEditStatus')?.value;
    const accountingKind = document.getElementById('bulkEditAccountingKind')?.value;
    const income = document.getElementById('bulkEditIncome')?.value;

    if (kind) { changes.kind = kind; }
    if (status) { changes.status = status; }
    if (accountingKind) { changes.accounting_kind = accountingKind; }
    if (income) { changes.income = income; }

    return changes;
  }

  /**
   * Apply bulk changes to selected invoices
   * @param {Array} invoiceIds - Array of invoice IDs
   * @param {Object} changes - Changes to apply
   */
  async applyBulkChanges(invoiceIds, changes) {
    if (!invoiceIds || invoiceIds.length === 0) {
      throw new Error('No invoices selected for bulk edit.');
    }

    const invoices = await this.storageAPI.getInvoices();
    let updatedCount = 0;

    // Update each selected invoice
    for (const invoice of invoices) {
      if (invoiceIds.includes(invoice.fileHash)) {
        // Apply changes
        Object.assign(invoice, changes);
        updatedCount++;
      }
    }

    // Save updated invoices
    await this.storageAPI.saveInvoices(invoices);

    this.showSuccess(`Updated ${updatedCount} invoice(s) successfully.`);
  }

  /**
   * Close the modal
   */
  closeModal() {
    if (this.modal) {
      document.body.removeChild(this.modal);
      this.modal = null;
    }
  }

  /**
   * Show error message
   * @param {string} message - Error message
   */
  showError(message) {
    if (typeof window.showError === 'function') {
      window.showError(message);
    } else {
      alert(`Error: ${message}`);
    }
  }

  /**
   * Show success message
   * @param {string} message - Success message
   */
  showSuccess(message) {
    if (typeof window.showSuccess === 'function') {
      window.showSuccess(message);
    } else {
      console.log(`Success: ${message}`);
    }
  }
}

// Create singleton instance
const bulkEditManager = new BulkEditManager();

// Export for ES modules
export default bulkEditManager;

// Export to window object for non-module scripts
if (typeof window !== 'undefined') {
  window.BulkEditManager = BulkEditManager;
  window.bulkEditManager = bulkEditManager;

  // Legacy compatibility
  window.showBulkEditModal = () => bulkEditManager.showBulkEditModal();
  window.addBulkEditButton = () => bulkEditManager.addBulkEditButton('.action-buttons');
}

// Local test function
function testBulkEditManager() {
  console.log('=== BulkEditManager Local Test ===');

  try {
    const manager = new BulkEditManager();

    // Test 1: Initialization
    console.log('Test 1: Initialization');
    console.log('✓ BulkEditManager instance created');

    // Test 2: Form values processing
    console.log('\nTest 2: Form values processing');
    // Create mock form elements
    const mockForm = document.createElement('div');
    mockForm.innerHTML = `
      <select id="bulkEditKind"><option value="vat" selected>VAT</option></select>
      <select id="bulkEditStatus"><option value="paid" selected>Paid</option></select>
    `;
    document.body.appendChild(mockForm);

    const values = manager.getFormValues();
    console.log('✓ Form values extracted:', Object.keys(values).length, 'fields');

    // Cleanup
    document.body.removeChild(mockForm);

    // Test 3: Fieldset creation
    console.log('\nTest 3: Fieldset creation');
    const fieldset = manager.createFieldset('Test Field');
    console.log('✓ Fieldset created');
    console.log('Has legend:', fieldset.querySelector('legend') !== null);

    console.log('\n✅ All BulkEditManager tests passed!');
    return true;

  } catch (error) {
    console.error('❌ BulkEditManager test failed:', error);
    return false;
  }
}

// ===== LOCAL TESTING (Node.js equivalent of Python's if __name__ == '__main__') =====
if (typeof process !== 'undefined' && process.argv && process.argv[1] && process.argv[1].includes('BulkEditManager.js')) {
  console.log('🧪 Running local tests for BulkEditManager...');

  // Create test instance
  const manager = new BulkEditManager();

  // Test core functionality
  console.log('✅ Test 1: Manager initialization');
  console.log('Manager created:', !!manager);
  console.log('Storage API available:', !!manager.storageAPI);

  console.log('✅ Test 2: Form value processing');
  // Mock form values
  const mockChanges = {
    kind: 'vat',
    status: 'paid',
    accounting_kind: 'purchases',
    income: '0'
  };
  console.log('Mock changes:', mockChanges);

  console.log('✅ Test 3: Document types available');
  console.log('Available document types:', Object.keys(FAKTUROWNIA_DOCUMENT_TYPES).length);
  console.log('Valid document types:', VALID_DOCUMENT_TYPES.length);

  console.log('✅ Test 4: Error handling');
  try {
    // Test with empty invoice list
    manager.selectedInvoices = [];
    console.log('Empty selection handled correctly');
  } catch (error) {
    console.log('Error handling test:', error.message);
  }

  console.log('✅ All tests completed for BulkEditManager');
}
