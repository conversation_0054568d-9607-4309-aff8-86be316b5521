/**
 * GroupingControls - UI controls for grouping options
 * Provides user interface for configuring data grouping and aggregation
 * Supports various grouping criteria and display options
 *
 * Epic: EPIC-003 - Data Display & Visualization
 * Story: STORY-3.2 - Grouping & Aggregation
 * Task: TASK-3.2.1 - Grouping Logic
 */

import React, { useState, useEffect } from 'react';

export const GroupingControls = ({
  onGroupingChange,
  initialGroupBy = 'month',
  initialSortBy = 'date',
  initialSortDirection = 'desc',
  disabled = false,
  className = ''
}) => {
  const [groupBy, setGroupBy] = useState(initialGroupBy);
  const [sortBy, setSortBy] = useState(initialSortBy);
  const [sortDirection, setSortDirection] = useState(initialSortDirection);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [fiscalYearStart, setFiscalYearStart] = useState(1);
  const [currencyHandling, setCurrencyHandling] = useState('separate');
  const [aggregations, setAggregations] = useState(['sum', 'count', 'average']);

  const groupingOptions = [
    { value: 'year', label: 'Year', icon: '📅' },
    { value: 'quarter', label: 'Quarter', icon: '📊' },
    { value: 'month', label: 'Month', icon: '🗓️' },
    { value: 'week', label: 'Week', icon: '📆' },
    { value: 'day', label: 'Day', icon: '📋' }
  ];

  const sortOptions = [
    { value: 'date', label: 'Date' },
    { value: 'count', label: 'Count' },
    { value: 'total', label: 'Total Amount' },
    { value: 'name', label: 'Name' }
  ];

  const aggregationOptions = [
    { value: 'sum', label: 'Sum' },
    { value: 'count', label: 'Count' },
    { value: 'average', label: 'Average' },
    { value: 'min', label: 'Minimum' },
    { value: 'max', label: 'Maximum' },
    { value: 'median', label: 'Median' }
  ];

  const monthOptions = [
    { value: 1, label: 'January' },
    { value: 2, label: 'February' },
    { value: 3, label: 'March' },
    { value: 4, label: 'April' },
    { value: 5, label: 'May' },
    { value: 6, label: 'June' },
    { value: 7, label: 'July' },
    { value: 8, label: 'August' },
    { value: 9, label: 'September' },
    { value: 10, label: 'October' },
    { value: 11, label: 'November' },
    { value: 12, label: 'December' }
  ];

  // Notify parent component of changes
  useEffect(() => {
    if (onGroupingChange) {
      onGroupingChange({
        groupBy,
        sortBy,
        sortDirection,
        fiscalYearStart,
        currencyHandling,
        aggregations
      });
    }
  }, [groupBy, sortBy, sortDirection, fiscalYearStart, currencyHandling, aggregations, onGroupingChange]);

  const handleGroupByChange = (newGroupBy) => {
    setGroupBy(newGroupBy);
  };

  const handleSortChange = (newSortBy) => {
    setSortBy(newSortBy);
  };

  const handleSortDirectionToggle = () => {
    setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc');
  };

  const handleAggregationToggle = (aggregation) => {
    setAggregations(prev => {
      if (prev.includes(aggregation)) {
        return prev.filter(agg => agg !== aggregation);
      }
      return [...prev, aggregation];

    });
  };

  return (
    <div className={`grouping-controls bg-white border border-gray-200 rounded-lg p-4 ${className}`}>
      {/* Main Controls */}
      <div className="flex flex-wrap items-center gap-4 mb-4">
        {/* Group By Selection */}
        <div className="flex items-center gap-2">
          <label className="text-sm font-medium text-gray-700">Group by:</label>
          <div className="flex bg-gray-100 rounded-lg p-1">
            {groupingOptions.map(option => (
              <button
                key={option.value}
                onClick={() => handleGroupByChange(option.value)}
                disabled={disabled}
                className={`
                  px-3 py-1 text-sm rounded-md transition-colors
                  ${groupBy === option.value
                ? 'bg-blue-500 text-white shadow-sm'
                : 'text-gray-600 hover:text-gray-800 hover:bg-gray-200'
              }
                  ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                `}
                title={option.label}
              >
                <span className="mr-1">{option.icon}</span>
                {option.label}
              </button>
            ))}
          </div>
        </div>

        {/* Sort Controls */}
        <div className="flex items-center gap-2">
          <label className="text-sm font-medium text-gray-700">Sort by:</label>
          <select
            value={sortBy}
            onChange={(e) => handleSortChange(e.target.value)}
            disabled={disabled}
            className="px-3 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {sortOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>

          <button
            onClick={handleSortDirectionToggle}
            disabled={disabled}
            className={`
              px-2 py-1 text-sm border border-gray-300 rounded-md transition-colors
              ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'}
            `}
            title={`Sort ${sortDirection === 'asc' ? 'Ascending' : 'Descending'}`}
          >
            {sortDirection === 'asc' ? '↑' : '↓'}
          </button>
        </div>

        {/* Advanced Options Toggle */}
        <button
          onClick={() => setShowAdvanced(!showAdvanced)}
          disabled={disabled}
          className={`
            px-3 py-1 text-sm text-blue-600 hover:text-blue-800 transition-colors
            ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
          `}
        >
          {showAdvanced ? 'Hide' : 'Show'} Advanced Options
        </button>
      </div>

      {/* Advanced Options */}
      {showAdvanced && (
        <div className="border-t border-gray-200 pt-4 space-y-4">
          {/* Fiscal Year Settings */}
          {(groupBy === 'year' || groupBy === 'quarter') && (
            <div className="flex items-center gap-4">
              <label className="text-sm font-medium text-gray-700">Fiscal Year Starts:</label>
              <select
                value={fiscalYearStart}
                onChange={(e) => setFiscalYearStart(parseInt(e.target.value))}
                disabled={disabled}
                className="px-3 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {monthOptions.map(month => (
                  <option key={month.value} value={month.value}>
                    {month.label}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* Currency Handling */}
          <div className="flex items-center gap-4">
            <label className="text-sm font-medium text-gray-700">Currency Handling:</label>
            <div className="flex gap-2">
              <label className="flex items-center">
                <input
                  type="radio"
                  value="separate"
                  checked={currencyHandling === 'separate'}
                  onChange={(e) => setCurrencyHandling(e.target.value)}
                  disabled={disabled}
                  className="mr-1"
                />
                <span className="text-sm">Separate by Currency</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  value="combined"
                  checked={currencyHandling === 'combined'}
                  onChange={(e) => setCurrencyHandling(e.target.value)}
                  disabled={disabled}
                  className="mr-1"
                />
                <span className="text-sm">Combine All</span>
              </label>
            </div>
          </div>

          {/* Aggregation Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Aggregations:</label>
            <div className="flex flex-wrap gap-2">
              {aggregationOptions.map(option => (
                <label key={option.value} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={aggregations.includes(option.value)}
                    onChange={() => handleAggregationToggle(option.value)}
                    disabled={disabled}
                    className="mr-1"
                  />
                  <span className="text-sm">{option.label}</span>
                </label>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Current Settings Summary */}
      <div className="mt-4 p-3 bg-gray-50 rounded-md">
        <div className="text-xs text-gray-600">
          <strong>Current Settings:</strong> Group by {groupBy}, sort by {sortBy} ({sortDirection}),
          {fiscalYearStart !== 1 && ` fiscal year starts ${monthOptions.find(m => m.value === fiscalYearStart)?.label},`}
          {' '}{currencyHandling} currencies, {aggregations.length} aggregation{aggregations.length !== 1 ? 's' : ''}
        </div>
      </div>
    </div>
  );
};

export default GroupingControls;
