/**
 * TabManager - Manages tab switching and state
 * Extracted from popup.js tab switching logic
 */

export class TabManager {
  constructor() {
    this.activeTab = 'invoices'; // Default tab
    this.tabs = new Map();
    this.listeners = new Map();
    this.initialized = false;
  }

  /**
   * Initialize tab manager
   * @param {Object} options - Initialization options
   */
  initialize(options = {}) {
    const {
      defaultTab = 'invoices',
      tabContainerSelector = '.tab-container',
      tabButtonSelector = '.tab-button',
      tabContentSelector = '.tab-content'
    } = options;

    this.defaultTab = defaultTab;
    this.activeTab = defaultTab;

    // Register default tabs
    this.registerTab('invoices', {
      label: 'Invoices',
      icon: '📄',
      contentSelector: '#invoices-tab',
      buttonSelector: '#invoices-tab-btn'
    });

    this.registerTab('documents', {
      label: 'Documents',
      icon: '📁',
      contentSelector: '#documents-tab',
      buttonSelector: '#documents-tab-btn'
    });

    this.registerTab('settings', {
      label: 'Settings',
      icon: '⚙️',
      contentSelector: '#settings-tab',
      buttonSelector: '#settings-tab-btn'
    });

    this.registerTab('bulk-edit', {
      label: 'Bulk Edit',
      icon: '✏️',
      contentSelector: '#bulk-edit-tab',
      buttonSelector: '#bulk-edit-tab-btn'
    });

    // Set up event listeners
    this.setupEventListeners();

    // Show default tab
    this.showTab(this.defaultTab);

    this.initialized = true;
    console.log('TabManager initialized');
  }

  /**
   * Register a new tab
   * @param {string} tabId - Tab identifier
   * @param {Object} config - Tab configuration
   */
  registerTab(tabId, config) {
    this.tabs.set(tabId, {
      id: tabId,
      label: config.label || tabId,
      icon: config.icon || '',
      contentSelector: config.contentSelector,
      buttonSelector: config.buttonSelector,
      visible: config.visible !== false,
      enabled: config.enabled !== false,
      badge: config.badge || null,
      onShow: config.onShow || null,
      onHide: config.onHide || null
    });
  }

  /**
   * Set up event listeners for tab buttons
   */
  setupEventListeners() {
    // Skip DOM operations in Node.js environment
    if (typeof document === 'undefined') { return; }

    // Listen for tab button clicks
    document.addEventListener('click', (event) => {
      const tabButton = event.target.closest('[data-tab]');
      if (tabButton) {
        const tabId = tabButton.getAttribute('data-tab');
        this.showTab(tabId);
      }
    });

    // Listen for keyboard navigation
    document.addEventListener('keydown', (event) => {
      if (event.ctrlKey || event.metaKey) {
        const tabKeys = {
          '1': 'invoices',
          '2': 'documents',
          '3': 'settings',
          '4': 'bulk-edit'
        };

        if (tabKeys[event.key]) {
          event.preventDefault();
          this.showTab(tabKeys[event.key]);
        }
      }
    });
  }

  /**
   * Show a specific tab
   * @param {string} tabId - Tab to show
   * @returns {boolean} - Success status
   */
  showTab(tabId) {
    if (!this.tabs.has(tabId)) {
      console.warn(`Tab ${tabId} not found`);
      return false;
    }

    const tab = this.tabs.get(tabId);

    if (!tab.enabled) {
      console.warn(`Tab ${tabId} is disabled`);
      return false;
    }

    // Hide current tab
    if (this.activeTab && this.activeTab !== tabId) {
      this.hideTab(this.activeTab);
    }

    // Show new tab
    this.activeTab = tabId;

    // Update tab buttons
    this.updateTabButtons();

    // Show tab content
    this.showTabContent(tabId);

    // Call onShow callback
    if (tab.onShow && typeof tab.onShow === 'function') {
      try {
        tab.onShow(tabId);
      } catch (error) {
        console.error(`Error in onShow callback for tab ${tabId}:`, error);
      }
    }

    // Emit tab change event
    this.emit('tabChanged', { tabId, previousTab: this.activeTab });

    // Store active tab in localStorage (skip in Node.js environment)
    if (typeof localStorage !== 'undefined') {
      try {
        localStorage.setItem('activeTab', tabId);
      } catch (error) {
        console.warn('Could not save active tab to localStorage:', error);
      }
    }

    return true;
  }

  /**
   * Hide a specific tab
   * @param {string} tabId - Tab to hide
   */
  hideTab(tabId) {
    if (!this.tabs.has(tabId)) { return; }

    const tab = this.tabs.get(tabId);

    // Hide tab content
    this.hideTabContent(tabId);

    // Call onHide callback
    if (tab.onHide && typeof tab.onHide === 'function') {
      try {
        tab.onHide(tabId);
      } catch (error) {
        console.error(`Error in onHide callback for tab ${tabId}:`, error);
      }
    }
  }

  /**
   * Update tab button states
   */
  updateTabButtons() {
    // Skip DOM operations in Node.js environment
    if (typeof document === 'undefined') { return; }

    this.tabs.forEach((tab, tabId) => {
      const button = document.querySelector(tab.buttonSelector);
      if (button) {
        // Update active state
        if (tabId === this.activeTab) {
          button.classList.add('active', 'bg-blue-500', 'text-white');
          button.classList.remove('bg-gray-200', 'text-gray-700');
        } else {
          button.classList.remove('active', 'bg-blue-500', 'text-white');
          button.classList.add('bg-gray-200', 'text-gray-700');
        }

        // Update enabled/disabled state
        if (tab.enabled) {
          button.classList.remove('opacity-50', 'cursor-not-allowed');
          button.removeAttribute('disabled');
        } else {
          button.classList.add('opacity-50', 'cursor-not-allowed');
          button.setAttribute('disabled', 'true');
        }

        // Update badge
        this.updateTabBadge(button, tab.badge);
      }
    });
  }

  /**
   * Show tab content
   * @param {string} tabId - Tab ID
   */
  showTabContent(tabId) {
    // Skip DOM operations in Node.js environment
    if (typeof document === 'undefined') { return; }

    const tab = this.tabs.get(tabId);
    if (!tab || !tab.contentSelector) { return; }

    const content = document.querySelector(tab.contentSelector);
    if (content) {
      content.style.display = 'block';
      content.classList.remove('hidden');
      content.classList.add('active');
    }
  }

  /**
   * Hide tab content
   * @param {string} tabId - Tab ID
   */
  hideTabContent(tabId) {
    // Skip DOM operations in Node.js environment
    if (typeof document === 'undefined') { return; }

    const tab = this.tabs.get(tabId);
    if (!tab || !tab.contentSelector) { return; }

    const content = document.querySelector(tab.contentSelector);
    if (content) {
      content.style.display = 'none';
      content.classList.add('hidden');
      content.classList.remove('active');
    }
  }

  /**
   * Update tab badge
   * @param {Element} button - Tab button element
   * @param {string|number} badge - Badge content
   */
  updateTabBadge(button, badge) {
    let badgeElement = button.querySelector('.tab-badge');

    if (badge !== null && badge !== undefined && badge !== '') {
      if (!badgeElement) {
        badgeElement = document.createElement('span');
        badgeElement.className = 'tab-badge bg-red-500 text-white text-xs rounded-full px-2 py-1 ml-2';
        button.appendChild(badgeElement);
      }
      badgeElement.textContent = badge;
    } else if (badgeElement) {
      badgeElement.remove();
    }
  }

  /**
   * Get active tab
   * @returns {string} - Active tab ID
   */
  getActiveTab() {
    return this.activeTab;
  }

  /**
   * Get tab configuration
   * @param {string} tabId - Tab ID
   * @returns {Object|null} - Tab configuration
   */
  getTab(tabId) {
    return this.tabs.get(tabId) || null;
  }

  /**
   * Get all tabs
   * @returns {Array} - Array of tab configurations
   */
  getAllTabs() {
    return Array.from(this.tabs.values());
  }

  /**
   * Enable/disable a tab
   * @param {string} tabId - Tab ID
   * @param {boolean} enabled - Enabled state
   */
  setTabEnabled(tabId, enabled) {
    if (this.tabs.has(tabId)) {
      this.tabs.get(tabId).enabled = enabled;
      this.updateTabButtons();
    }
  }

  /**
   * Show/hide a tab
   * @param {string} tabId - Tab ID
   * @param {boolean} visible - Visible state
   */
  setTabVisible(tabId, visible) {
    if (this.tabs.has(tabId)) {
      this.tabs.get(tabId).visible = visible;

      // Skip DOM operations in Node.js environment
      if (typeof document === 'undefined') { return; }

      const tab = this.tabs.get(tabId);
      const button = document.querySelector(tab.buttonSelector);
      if (button) {
        button.style.display = visible ? 'block' : 'none';
      }
    }
  }

  /**
   * Set tab badge
   * @param {string} tabId - Tab ID
   * @param {string|number} badge - Badge content
   */
  setTabBadge(tabId, badge) {
    if (this.tabs.has(tabId)) {
      this.tabs.get(tabId).badge = badge;
      this.updateTabButtons();
    }
  }

  /**
   * Add event listener
   * @param {string} event - Event name
   * @param {Function} callback - Callback function
   */
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  /**
   * Remove event listener
   * @param {string} event - Event name
   * @param {Function} callback - Callback function
   */
  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  /**
   * Emit event
   * @param {string} event - Event name
   * @param {any} data - Event data
   */
  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Restore active tab from localStorage
   */
  restoreActiveTab() {
    // Skip localStorage operations in Node.js environment
    if (typeof localStorage === 'undefined') { return; }

    try {
      const savedTab = localStorage.getItem('activeTab');
      if (savedTab && this.tabs.has(savedTab)) {
        this.showTab(savedTab);
      }
    } catch (error) {
      console.warn('Could not restore active tab from localStorage:', error);
    }
  }
}

// Create singleton instance
const tabManager = new TabManager();

// Export for ES modules
export default tabManager;

// Export to window object for non-module scripts
if (typeof window !== 'undefined') {
  window.TabManager = TabManager;
  window.tabManager = tabManager;
}

// ===== LOCAL TESTING (Node.js equivalent of Python's if __name__ == '__main__') =====
if (typeof process !== 'undefined' && process.argv && process.argv[1] && process.argv[1].includes('TabManager.js')) {
  console.log('🧪 Running local tests for TabManager...');

  // Test 1: TabManager initialization
  console.log('✅ Test 1: TabManager initialization');
  const manager = new TabManager();
  console.log('📝 Default active tab:', manager.getActiveTab());
  console.log('📝 Initialized:', manager.initialized);

  // Test 2: Tab registration
  console.log('\n✅ Test 2: Tab registration');
  manager.registerTab('test-tab', {
    label: 'Test Tab',
    icon: '🧪',
    contentSelector: '#test-content',
    buttonSelector: '#test-button'
  });
  console.log('📝 Registered tabs count:', manager.getAllTabs().length);
  console.log('📝 Test tab exists:', manager.getTab('test-tab') !== null);

  // Test 3: Tab configuration
  console.log('\n✅ Test 3: Tab configuration');
  const testTab = manager.getTab('test-tab');
  console.log('📝 Test tab label:', testTab?.label);
  console.log('📝 Test tab icon:', testTab?.icon);
  console.log('📝 Test tab enabled:', testTab?.enabled);

  // Test 4: Tab state management
  console.log('\n✅ Test 4: Tab state management');
  manager.setTabEnabled('test-tab', false);
  console.log('📝 Test tab disabled:', !manager.getTab('test-tab').enabled);

  manager.setTabVisible('test-tab', false);
  console.log('📝 Test tab hidden:', !manager.getTab('test-tab').visible);

  manager.setTabBadge('test-tab', '5');
  console.log('📝 Test tab badge:', manager.getTab('test-tab').badge);

  // Test 5: Event system
  console.log('\n✅ Test 5: Event system');
  let eventFired = false;
  manager.on('testEvent', (data) => {
    eventFired = true;
    console.log('📝 Event received with data:', data);
  });

  manager.emit('testEvent', { test: 'data' });
  console.log('📝 Event system working:', eventFired);

  // Test 6: Tab switching (without DOM)
  console.log('\n✅ Test 6: Tab switching logic');
  const originalActiveTab = manager.getActiveTab();
  console.log('📝 Original active tab:', originalActiveTab);

  // Test invalid tab
  const invalidResult = manager.showTab('non-existent-tab');
  console.log('📝 Invalid tab handled correctly:', !invalidResult);

  // Test 7: All tabs listing
  console.log('\n✅ Test 7: All tabs listing');
  const allTabs = manager.getAllTabs();
  console.log('📝 Total tabs registered:', allTabs.length);
  console.log('📝 Tab IDs:', allTabs.map(tab => tab.id));

  console.log('\n🎉 All tests completed for TabManager');
  console.log('📋 Note: DOM-dependent features require browser environment');
}
