/**
 * MVAT Chrome Extension Main Script
 * CSP Compliant - No inline scripts or eval()
 */

console.log('🚀 MVAT Extension loading...');

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
  console.log('📋 MVAT DOM loaded');
  initializeMVAT();
});

// Initialize MVAT Extension
function initializeMVAT() {
  const root = document.getElementById('root');
  if (!root) {
    console.error('❌ Root element not found');
    return;
  }

  // React will handle all loading states
  console.log('✅ MVAT Extension ready for React');
}

// Create the main application UI
function createMainApp(root) {
  const app = document.createElement('div');
  app.className = 'mvat-app';
  
  // Create header
  const header = createHeader();
  app.appendChild(header);
  
  // Create status card
  const statusCard = createStatusCard();
  app.appendChild(statusCard);
  
  // Create warning card
  const warningCard = createWarningCard();
  app.appendChild(warningCard);
  
  // Create test buttons
  const buttonGrid = createButtonGrid();
  app.appendChild(buttonGrid);
  
  // Create test results area
  const testResults = createTestResults();
  app.appendChild(testResults);
  
  // Create footer
  const footer = createFooter();
  app.appendChild(footer);
  
  root.appendChild(app);
  
  // Add event listeners
  setupEventListeners();
}

// Create header section
function createHeader() {
  const header = document.createElement('div');
  header.className = 'mvat-header';
  
  const logo = document.createElement('div');
  logo.className = 'mvat-logo';
  logo.textContent = 'M';
  
  const title = document.createElement('h1');
  title.className = 'mvat-title';
  title.textContent = 'MVAT Invoice Processor';
  
  header.appendChild(logo);
  header.appendChild(title);
  
  return header;
}

// Create status card
function createStatusCard() {
  const card = document.createElement('div');
  card.className = 'status-card success';
  
  const title = document.createElement('h2');
  title.className = 'card-title success';
  title.textContent = 'Extension Status';
  
  const text = document.createElement('p');
  text.className = 'card-text success';
  text.textContent = '✅ Extension loaded successfully as detached popup window';
  
  card.appendChild(title);
  card.appendChild(text);
  
  return card;
}

// Create warning card
function createWarningCard() {
  const card = document.createElement('div');
  card.className = 'status-card warning';
  
  const title = document.createElement('h2');
  title.className = 'card-title warning';
  title.textContent = 'Development Mode';
  
  const text = document.createElement('p');
  text.className = 'card-text warning';
  text.textContent = 'This is a test build. Full functionality will be available after completing Epic B1-B3.';
  
  card.appendChild(title);
  card.appendChild(text);
  
  return card;
}

// Create button grid
function createButtonGrid() {
  const grid = document.createElement('div');
  grid.className = 'button-grid';
  
  const storageButton = document.createElement('button');
  storageButton.id = 'test-storage';
  storageButton.className = 'test-button primary';
  storageButton.textContent = 'Test Storage';
  
  const aiButton = document.createElement('button');
  aiButton.id = 'test-ai';
  aiButton.className = 'test-button success';
  aiButton.textContent = 'Test AI (Mock)';
  
  grid.appendChild(storageButton);
  grid.appendChild(aiButton);
  
  return grid;
}

// Create test results area
function createTestResults() {
  const results = document.createElement('div');
  results.id = 'test-results';
  results.className = 'test-results';
  
  const title = document.createElement('p');
  title.className = 'results-title';
  title.textContent = 'Test Results:';
  
  const text = document.createElement('p');
  text.className = 'results-text';
  text.textContent = 'Click buttons above to test extension functionality';
  
  results.appendChild(title);
  results.appendChild(text);
  
  return results;
}

// Create footer
function createFooter() {
  const footer = document.createElement('div');
  footer.className = 'footer';
  
  const text1 = document.createElement('p');
  text1.className = 'footer-text';
  text1.textContent = 'MVAT v1.0.0 - Development Build';
  
  const text2 = document.createElement('p');
  text2.className = 'footer-subtext';
  text2.textContent = 'Detached popup window mode active';
  
  footer.appendChild(text1);
  footer.appendChild(text2);
  
  return footer;
}

// Setup event listeners
function setupEventListeners() {
  const storageButton = document.getElementById('test-storage');
  const aiButton = document.getElementById('test-ai');
  
  if (storageButton) {
    storageButton.addEventListener('click', testStorage);
  }
  
  if (aiButton) {
    aiButton.addEventListener('click', testAI);
  }
}

// Test storage functionality using StorageAPI
async function testStorage() {
  const resultsDiv = document.getElementById('test-results');
  updateResults(resultsDiv, 'Testing Chrome Storage...', '');

  try {
    // Check if chrome.storage is available
    if (typeof chrome === 'undefined' || !chrome.storage) {
      throw new Error('Chrome storage API not available');
    }

    // Test storage write
    const testData = {
      test: 'MVAT storage test',
      timestamp: new Date().toISOString()
    };

    // Use Chrome storage API directly with proper error handling
    await new Promise((resolve, reject) => {
      chrome.storage.local.set(testData, () => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve();
        }
      });
    });

    // Test storage read
    const result = await new Promise((resolve, reject) => {
      chrome.storage.local.get(['test', 'timestamp'], (data) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(data);
        }
      });
    });

    const successTitle = '✅ Storage Test Passed';
    const successText = `Stored: ${result.test}\nTime: ${new Date(result.timestamp).toLocaleTimeString()}`;

    updateResults(resultsDiv, successTitle, successText, 'success');

    console.log('✅ Storage test passed:', result);

  } catch (error) {
    const errorTitle = '❌ Storage Test Failed';
    const errorText = `Error: ${error.message}`;

    updateResults(resultsDiv, errorTitle, errorText, 'error');

    console.error('❌ Storage test failed:', error);
  }
}

// Test AI functionality (mock)
async function testAI() {
  const resultsDiv = document.getElementById('test-results');
  updateResults(resultsDiv, 'Testing AI Integration (Mock)...', '');
  
  try {
    // Simulate AI processing delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock AI response
    const mockResponse = {
      number: 'INV-2024-001',
      seller_name: 'Test Company Sp. z o.o.',
      buyer_name: 'Customer Business Ltd.',
      total_gross: '1,234.56',
      currency: 'PLN',
      confidence: 0.95
    };
    
    const successTitle = '✅ AI Test Passed (Mock)';
    const successText = `Invoice: ${mockResponse.number}\nAmount: ${mockResponse.total_gross} ${mockResponse.currency}\nConfidence: ${(mockResponse.confidence * 100).toFixed(1)}%`;
    
    updateResults(resultsDiv, successTitle, successText, 'success');
    
    console.log('✅ AI test passed (mock):', mockResponse);
    
  } catch (error) {
    const errorTitle = '❌ AI Test Failed';
    const errorText = `Error: ${error.message}`;
    
    updateResults(resultsDiv, errorTitle, errorText, 'error');
    
    console.error('❌ AI test failed:', error);
  }
}

// Update test results display
function updateResults(resultsDiv, title, text, type = '') {
  // Clear previous content
  resultsDiv.innerHTML = '';
  
  const titleElement = document.createElement('p');
  titleElement.className = `results-title ${type === 'success' ? 'results-success' : type === 'error' ? 'results-error' : ''}`;
  titleElement.textContent = title;
  
  const textElement = document.createElement('p');
  textElement.className = 'results-text';
  
  // Handle multiline text
  const lines = text.split('\n');
  lines.forEach((line, index) => {
    if (index > 0) {
      textElement.appendChild(document.createElement('br'));
    }
    textElement.appendChild(document.createTextNode(line));
  });
  
  resultsDiv.appendChild(titleElement);
  if (text) {
    resultsDiv.appendChild(textElement);
  }
}

// Handle errors
window.addEventListener('error', function(event) {
  console.error('🚨 MVAT Error:', event.error);
  
  const resultsDiv = document.getElementById('test-results');
  if (resultsDiv) {
    const errorTitle = '❌ Extension Error';
    const errorText = `Error: ${event.error?.message || 'Unknown error'}`;
    updateResults(resultsDiv, errorTitle, errorText, 'error');
  }
});

// Handle unhandled promise rejections
window.addEventListener('unhandledrejection', function(event) {
  console.error('🚨 MVAT Unhandled Promise Rejection:', event.reason);
  
  const resultsDiv = document.getElementById('test-results');
  if (resultsDiv) {
    const errorTitle = '❌ Promise Rejection';
    const errorText = `Error: ${event.reason?.message || 'Unknown promise rejection'}`;
    updateResults(resultsDiv, errorTitle, errorText, 'error');
  }
});

console.log('📦 MVAT Extension script loaded');
