#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# MVAT Chrome Extension Pre-commit Hook
# Ensures all quality gates pass before allowing commits

echo "🚀 MVAT Pre-commit Quality Gates"
echo "================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    if [ "$status" = "success" ]; then
        echo "${GREEN}✅ $message${NC}"
    elif [ "$status" = "error" ]; then
        echo "${RED}❌ $message${NC}"
    elif [ "$status" = "warning" ]; then
        echo "${YELLOW}⚠️  $message${NC}"
    else
        echo "${BLUE}ℹ️  $message${NC}"
    fi
}

# Function to run command and check exit code
run_check() {
    local name=$1
    local command=$2
    
    print_status "info" "Running $name..."
    
    if eval "$command"; then
        print_status "success" "$name passed"
        return 0
    else
        print_status "error" "$name failed"
        return 1
    fi
}

# Track overall success
OVERALL_SUCCESS=0

echo ""
print_status "info" "Step 1: Code Formatting & Linting"
echo "-----------------------------------"

# Run linting using Makefile
if ! run_check "ESLint via Makefile" "make lint"; then
    OVERALL_SUCCESS=1
fi

echo ""
print_status "info" "Step 2: Unit Tests"
echo "------------------"

# Run unit tests with coverage using Makefile
if ! run_check "Vitest Unit Tests via Makefile" "make test-unit"; then
    OVERALL_SUCCESS=1
fi

echo ""
print_status "info" "Step 3: Build Verification"
echo "--------------------------"

# Verify the build works using Makefile
if ! run_check "Build Verification via Makefile" "make build-extension"; then
    OVERALL_SUCCESS=1
fi

echo ""
print_status "info" "Step 8: Security & Quality Analysis"
echo "-----------------------------------"

# Run security audit (non-blocking for development) using Makefile node
if ! make -s -C . bash -c "source /opt/nvm/nvm.sh && nvm use lts/jod && npm audit --audit-level=high"; then
    print_status "warning" "Security audit found issues - please review"
fi

# Check for TODO/FIXME comments in staged files
STAGED_FILES=$(git diff --cached --name-only --diff-filter=ACM | grep -E '\.(js|jsx|ts|tsx)$' || true)
if [ -n "$STAGED_FILES" ]; then
    TODO_COUNT=$(echo "$STAGED_FILES" | xargs grep -l "TODO\|FIXME" 2>/dev/null | wc -l || echo "0")
    if [ "$TODO_COUNT" -gt 0 ]; then
        print_status "warning" "Found $TODO_COUNT files with TODO/FIXME comments"
    fi
fi

echo ""
print_status "info" "Step 9: Documentation Check"
echo "---------------------------"

# Check if documentation needs updating
if git diff --cached --name-only | grep -q "src/"; then
    if ! git diff --cached --name-only | grep -q "docs/\|README.md\|CHANGELOG.md"; then
        print_status "warning" "Code changes detected but no documentation updates"
        print_status "info" "Consider updating relevant documentation"
    fi
fi

echo ""
print_status "info" "Step 10: Commit Message Validation"
echo "----------------------------------"

# Get the commit message (if available)
COMMIT_MSG_FILE=".git/COMMIT_EDITMSG"
if [ -f "$COMMIT_MSG_FILE" ]; then
    COMMIT_MSG=$(head -n 1 "$COMMIT_MSG_FILE")
    
    # Check commit message format (conventional commits)
    if ! echo "$COMMIT_MSG" | grep -qE "^(feat|fix|docs|style|refactor|test|chore|perf|ci|build)(\(.+\))?: .+"; then
        print_status "warning" "Commit message doesn't follow conventional commit format"
        print_status "info" "Expected format: type(scope): description"
        print_status "info" "Example: feat(auth): add user authentication"
    fi
    
    # Check for epic/story/task references
    if ! echo "$COMMIT_MSG" | grep -qE "(Epic|Story|Task): [A-Z0-9.-]+"; then
        print_status "warning" "Commit message missing Epic/Story/Task reference"
        print_status "info" "Add Epic: EPIC-ID, Story: STORY-ID, or Task: TASK-ID"
    fi
fi

echo ""
echo "================================="

# Final result
if [ $OVERALL_SUCCESS -eq 0 ]; then
    print_status "success" "All critical quality gates passed! 🎉"
    print_status "info" "Commit is allowed to proceed"
    echo ""
    print_status "info" "Remember to:"
    print_status "info" "- Link your commit to Epic/Story/Task"
    print_status "info" "- Update CHANGELOG.md if needed"
    print_status "info" "- Update documentation if needed"
    echo ""
    exit 0
else
    print_status "error" "Critical quality gates failed! ❌"
    print_status "error" "Commit is blocked"
    echo ""
    print_status "info" "To fix issues:"
    print_status "info" "- Run 'npm run lint:fix' to fix linting issues"
    print_status "info" "- Run 'npm run format' to fix formatting"
    print_status "info" "- Fix failing unit tests"
    print_status "info" "- Fix TypeScript errors"
    print_status "info" "- Fix build errors"
    echo ""
    print_status "info" "To bypass (not recommended):"
    print_status "info" "- Use 'git commit --no-verify' (only for emergencies)"
    echo ""
    exit 1
fi
