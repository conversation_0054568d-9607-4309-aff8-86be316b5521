#!/usr/bin/env node

/**
 * Comprehensive Multi-Step Pipeline Test
 * Tests the new 7-step document processing pipeline implementation
 */

import { PIPELINE_STEPS } from './src/core/config/pipelineSteps.js';
import fs from 'fs';
import path from 'path';

console.log('🧪 COMPREHENSIVE MULTI-STEP PIPELINE TEST');
console.log('============================================================');
console.log('📅 Timestamp:', new Date().toISOString());
console.log('🎯 Purpose: Test new 7-step document processing pipeline');
console.log('============================================================\n');

/**
 * Test 1: Pipeline Configuration Verification
 */
async function testPipelineConfiguration() {
  console.log('🔧 Test 1: Pipeline Configuration Verification');

  try {
    // Check if we have exactly 7 steps
    if (PIPELINE_STEPS.length !== 7) {
      throw new Error(`Expected 7 pipeline steps, got ${PIPELINE_STEPS.length}`);
    }
    console.log('✅ Pipeline has correct number of steps (7)');

    // Verify step IDs match the new specification
    const expectedSteps = [
      'pdf_extraction',
      'deepseek_analysis_1',
      'rag_enhancement',
      'tesseract_reference',
      'deepseek_analysis_2',
      'deepseek_analysis_3',
      'final_output'
    ];

    const actualSteps = PIPELINE_STEPS.map(step => step.id);

    for (let i = 0; i < expectedSteps.length; i++) {
      if (actualSteps[i] !== expectedSteps[i]) {
        throw new Error(`Step ${i + 1} mismatch: expected ${expectedSteps[i]}, got ${actualSteps[i]}`);
      }
    }
    console.log('✅ All step IDs match specification');

    // Verify step names and descriptions
    const step2 = PIPELINE_STEPS.find(s => s.id === 'deepseek_analysis_1');
    if (!step2.description.includes('document kind, language, basic info')) {
      throw new Error('DeepSeek Analysis 1 description incorrect');
    }
    console.log('✅ Step descriptions match specification');

    // Verify new color configurations
    const step5 = PIPELINE_STEPS.find(s => s.id === 'deepseek_analysis_2');
    const step6 = PIPELINE_STEPS.find(s => s.id === 'deepseek_analysis_3');

    if (step5.color !== 'violet' || step6.color !== 'pink') {
      throw new Error('New color configurations not applied correctly');
    }
    console.log('✅ New color configurations applied correctly');

    console.log('✅ Test 1 PASSED: Pipeline configuration verified\n');
    return true;

  } catch (error) {
    console.error('❌ Test 1 FAILED:', error.message);
    return false;
  }
}

/**
 * Test 2: Pipeline Service Methods Verification (Static Analysis)
 */
async function testPipelineServiceMethods() {
  console.log('🔧 Test 2: Pipeline Service Methods Verification (Static Analysis)');

  try {
    // Read the DocumentProcessingPipeline.js file and check for method definitions
    const pipelineFile = './src/services/DocumentProcessingPipeline.js';
    if (!fs.existsSync(pipelineFile)) {
      throw new Error('DocumentProcessingPipeline.js file not found');
    }

    const pipelineContent = fs.readFileSync(pipelineFile, 'utf8');

    const requiredMethods = [
      'analyzeDocumentStructure',
      'enhanceWithRAG',
      'analyzeFieldsByDocumentType',
      'analyzePositionsTable',
      'generateStructureAnalysisPrompt',
      'generateFieldAnalysisPrompt',
      'generatePositionsAnalysisPrompt',
      'mapLanguageToOCR'
    ];

    for (const method of requiredMethods) {
      if (!pipelineContent.includes(`async ${method}(`) && !pipelineContent.includes(`${method}(`)) {
        throw new Error(`Method ${method} not found in pipeline file`);
      }
    }
    console.log('✅ All required methods found in source code');

    // Check for specific implementation details
    if (!pipelineContent.includes('deepseek_analysis_1') ||
        !pipelineContent.includes('deepseek_analysis_2') ||
        !pipelineContent.includes('deepseek_analysis_3')) {
      throw new Error('New DeepSeek analysis steps not implemented');
    }
    console.log('✅ New DeepSeek analysis steps implemented');

    if (!pipelineContent.includes('pol+eng') || !pipelineContent.includes('mapLanguageToOCR')) {
      throw new Error('Language mapping functionality not implemented');
    }
    console.log('✅ Language mapping functionality implemented');

    console.log('✅ Test 2 PASSED: Pipeline service methods verified (static analysis)\n');
    return true;

  } catch (error) {
    console.error('❌ Test 2 FAILED:', error.message);
    return false;
  }
}

/**
 * Test 3: Configuration Files Verification
 */
async function testConfigurationFiles() {
  console.log('🔧 Test 3: Configuration Files Verification');

  try {
    // Check if configuration files exist
    const configFiles = [
      './src/core/config/pipelineSteps.js',
      './src/core/config/documentTypes.js',
      './src/core/config/fieldDefinitions.js'
    ];

    for (const file of configFiles) {
      if (!fs.existsSync(file)) {
        throw new Error(`Configuration file missing: ${file}`);
      }
    }
    console.log('✅ All configuration files exist');

    // Test imports
    const { DOCUMENT_TYPES } = await import('./src/core/config/documentTypes.js');
    const { FIELD_DEFINITIONS } = await import('./src/core/config/fieldDefinitions.js');

    if (!DOCUMENT_TYPES || Object.keys(DOCUMENT_TYPES).length === 0) {
      throw new Error('DOCUMENT_TYPES not properly exported');
    }
    console.log('✅ Document types configuration loaded');

    if (!FIELD_DEFINITIONS || Object.keys(FIELD_DEFINITIONS).length === 0) {
      throw new Error('FIELD_DEFINITIONS not properly exported');
    }
    console.log('✅ Field definitions configuration loaded');

    // Test specific configurations
    if (!DOCUMENT_TYPES.invoice || !DOCUMENT_TYPES.invoice_correction) {
      throw new Error('Required document types missing');
    }
    console.log('✅ Required document types present');

    if (!FIELD_DEFINITIONS.document_number || !FIELD_DEFINITIONS.positions_table) {
      throw new Error('Required field definitions missing');
    }
    console.log('✅ Required field definitions present');

    console.log('✅ Test 3 PASSED: Configuration files verified\n');
    return true;

  } catch (error) {
    console.error('❌ Test 3 FAILED:', error.message);
    return false;
  }
}

/**
 * Test 4: Pipeline Implementation Verification
 */
async function testPipelineImplementation() {
  console.log('🔧 Test 4: Pipeline Implementation Verification');

  try {
    // Check pipeline file structure
    const pipelineFile = './src/services/DocumentProcessingPipeline.js';
    const pipelineContent = fs.readFileSync(pipelineFile, 'utf8');

    // Check for new pipeline steps in processDocument method
    if (!pipelineContent.includes('deepseek_analysis_1') ||
        !pipelineContent.includes('rag_enhancement') ||
        !pipelineContent.includes('deepseek_analysis_2') ||
        !pipelineContent.includes('deepseek_analysis_3')) {
      throw new Error('New pipeline steps not implemented in processDocument method');
    }
    console.log('✅ New pipeline steps implemented in processDocument method');

    // Check for proper step result storage
    if (!pipelineContent.includes('storeStepResult') ||
        !pipelineContent.includes('stepResults.get')) {
      throw new Error('Step result storage not properly implemented');
    }
    console.log('✅ Step result storage properly implemented');

    // Check for enhanced prompt generation
    if (!pipelineContent.includes('generateStructureAnalysisPrompt') ||
        !pipelineContent.includes('generateFieldAnalysisPrompt') ||
        !pipelineContent.includes('generatePositionsAnalysisPrompt')) {
      throw new Error('Enhanced prompt generation not implemented');
    }
    console.log('✅ Enhanced prompt generation implemented');

    console.log('✅ Test 4 PASSED: Pipeline implementation verified\n');
    return true;

  } catch (error) {
    console.error('❌ Test 4 FAILED:', error.message);
    return false;
  }
}

/**
 * Test 5: Sample Data Directory Verification
 */
async function testSampleDataDirectory() {
  console.log('🔧 Test 5: Sample Data Directory Verification');

  try {
    // Check if sample data directory exists
    const sampleDir = './data/samples/input';
    if (!fs.existsSync(sampleDir)) {
      console.log('⚠️ Sample data directory not found, creating structure...');

      // Create directory structure
      fs.mkdirSync('./data', { recursive: true });
      fs.mkdirSync('./data/samples', { recursive: true });
      fs.mkdirSync('./data/samples/input', { recursive: true });
      fs.mkdirSync('./data/samples/output', { recursive: true });

      console.log('✅ Sample data directory structure created');
    } else {
      console.log('✅ Sample data directory exists');
    }

    // Get sample files
    const sampleFiles = fs.readdirSync(sampleDir).filter(file =>
      file.endsWith('.pdf') || file.endsWith('.jpg') || file.endsWith('.png')
    );

    console.log(`📁 Found ${sampleFiles.length} sample files in input directory`);

    // Check output directory
    const outputDir = './data/samples/output';
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
      console.log('✅ Output directory created');
    } else {
      console.log('✅ Output directory exists');
    }

    console.log('✅ Test 5 PASSED: Sample data directory structure verified\n');
    return true;

  } catch (error) {
    console.error('❌ Test 5 FAILED:', error.message);
    return false;
  }
}

/**
 * Main test runner
 */
async function runAllTests() {
  console.log('🚀 Starting comprehensive pipeline tests...\n');

  const tests = [
    testPipelineConfiguration,
    testPipelineServiceMethods,
    testConfigurationFiles,
    testPipelineImplementation,
    testSampleDataDirectory
  ];

  let passed = 0;
  let failed = 0;

  for (const test of tests) {
    const result = await test();
    if (result) {
      passed++;
    } else {
      failed++;
    }
  }

  console.log('============================================================');
  console.log('📊 COMPREHENSIVE PIPELINE TEST RESULTS');
  console.log('============================================================');
  console.log(`✅ Tests Passed: ${passed}`);
  console.log(`❌ Tests Failed: ${failed}`);
  console.log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);
  console.log('============================================================');

  if (failed === 0) {
    console.log('🎉 ALL TESTS PASSED - COMPREHENSIVE PIPELINE READY!');
    process.exit(0);
  } else {
    console.log('❌ SOME TESTS FAILED - REVIEW IMPLEMENTATION');
    process.exit(1);
  }
}

// Run tests
runAllTests().catch(error => {
  console.error('💥 Test runner failed:', error);
  process.exit(1);
});
