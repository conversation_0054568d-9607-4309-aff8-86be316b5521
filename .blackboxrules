
# Prioritization
- User requires prioritizing comprehensive DeepSeek API analysis as next primary goal, followed by RAG-based document linking, with monetization/usage tracking as the final epic after all core features are implemented.
- User requires prioritizing core document processing functionality (drag/drop, table display, deepseek analysis, RAG similarity, settings) before implementing subscription/monetization features.
`
- Features placed in paused directories are not to be planned nor implemented until user move them manually outside ./paused

# Preferences
- User prefers TailwindCSS 4.0, Vite, React, Node 22 LTS stack with comprehensive testing (e2e, functional, Selenium with screenshots), Makefile-driven workflows, single purpose files, DRY principles, and 2025 JS/UI/UX best practices for Chrome extension development.
- User prefers Chrome extensions built with PDF.js, Tesseract.js, OpenAI JS libs, detached popup windows, comprehensive testing (e2e, functional, Selenium with screenshots), Makefile-driven workflows, and detailed project planning with epics/stories/tasks structure following single purpose files and DRY principles.
- User requires best practice testing frameworks integrated into EPIC 1 with comprehensive functional tests and selenium/rendering tools that run on any code changes.
- User prefers systematic implementation following documented action plans with mandatory testing of all changes and keeping documentation updated throughout development.
- User suggests using Chrome extension sandboxes (https://developer.chrome.com/docs/extensions/reference/manifest/sandbox) to resolve Tesseract.js CSP violations and external script loading issues in Chrome extensions.
- User prefers consolidating duplicate configuration views into a single source of truth rather than having multiple nested configuration interfaces in different parts of the settings.
- User prefers systematic codebase analysis using double for loops to compare all files against each other to identify and eliminate redundancies and conflicts (like duplicate loading spinners) across the entire source code.
- User requires systematic code consolidation using double for loops to compare each file in src and config directories against all other files to identify conflicts and redundancies, with separate assignments for each file comparison.
- User questions the need for dummy page loading before normal extension loads, suggesting preference to avoid unnecessary loading screens.

# Planning & Structure
- User requires comprehensive business planning with epics/stories/tasks structure, changelog files linked to each component, git commits with changelog messages, and detailed criteria for writing project components with granular technical and business requirements.
- User requires task assignment workflow: docs/ → business plan → epics.md → epics/<epic>.md → assignments/<assignment>.md → implement+tests → git commit with precommit → update epics.md → changelog.md → changelogs/, with assignments stored in docs/assignments subdirectory and assignment.template.md for creating new assignments.
- User requires comprehensive file-by-file analysis of src directory structure with line-by-line functionality documentation, saved as analysis in docs/ directory as part of assignment workflow.
- User requires comprehensive file-by-file analysis to identify and resolve conflicts, redundancies, and structural issues in codebase, particularly duplicate components like SettingsPage in different directories.

# Code Quality & Workflow
- User requires all epic/story/task/subtask completion to be done via git commits with mandatory pre-commit hooks that run all tests (vitest, playwright, lint, format, typecheck, analyze) with zero failures allowed.
- User requires using node via @Makefile as implemented there.
- User requires all commands to be wrapped in Makefile targets and executed only via `make` command.
- User requires strict adherence to documented plans/criteria.
- User requires keeping docs/ updated throughout development, creating separate changelog files linked to epics/stories/tasks/subtasks, and using git commits with changelog messages for all changes.
- User requires comprehensive changelog tracking linked to epics/stories/tasks.
- User requires specific docs organization with epics/ and changelogs/ subdirectories using template-based naming conventions, mandatory git commits with changelog messages for all epic/story/task completion, and pre-commit hooks running all tests with zero failures allowed.
- User requires systematic assignment workflow with continuous assignment completion cycle without stopping after each task.
- User requires systematic assignment workflow with selenium browser tests as first step to verify Chrome extension state, mandatory git commits with pre-commit hooks passing all tests, and continuous assignment completion cycle without stopping after each task.
- User wants separate dist directories for different build types (dev-extension, build → dist/build).

# Data Processing & Logging
- User requires console logs showing data read from uploads (PDF.js output, then Tesseract output), using both for DeepSeek API analysis, with timestamps and unique upload UUIDs, storing all processed data.

# Configuration
- User requires setting up API keys and company details from .env file as part of configuration setup tasks.
- User requires .env files to be included at compile time for Chrome extensions, suggesting copying .env to dist directory during development build process.
- User confirmed that comprehensive environment variable logging in Chrome extension console works correctly in development mode, showing all 79 variables with proper masking for sensitive data like API keys.
- User requires implementation of a settings loading button in settings tab with dropdown for multiple sources (EnvironmentConfigService, chrome storage, .env file, json config file).
- User expects settings page to have UI controls (buttons, dropdowns, text fields, text inputs) for loading environment variables from multiple sources (storage, env file, JSON, paste) and clear data flow from EnvLoader/EnvironmentConfigService to SettingsPage.
- User requires dummy fallbacks/hardcoded data to be forbidden (like getAppVersion or loadConfiguration), but static JSON files imported during build time are acceptable; user identified issues with version display mismatch, environment variables not showing in settings tab, and missing buttons for loading env vars from JSON text input.

# Build & Versioning
- User requires build targets to use separate dist directories (dev-extension → dist/dev, build → dist/build).
- User requires extension version to correspond to VERSION file.
- User requires VERSION file to be bumped at each git commit.

# Testing
- User requires selenium browser tests as first step in any assignment to verify Chrome extension state and use browser-like tests to verify functionality and catch console errors.
- User requires proper tests in next assignment to assert that SettingsService.js correctly passes variables to SettingsPage.jsx in Chrome extension environment.
- User requires selenium browser tests including draganddrop of invoice from docs/data/samples/invoices/input in production dropdown field and in debug workflow to verify functionality and cach errors.

# New task

Following our established workflow procedures documented in docs , create a new assignment for the next task and begin implementing it systematically.

**Required Workflow Steps:**
1. Review current state in docs  directory (business plan, epics, assignments)
2. Identify next task from epics.md and corresponding epic file in epics/ directory
3. Create new assignment file in docs/assignments/ using assignment.template.md
4. Implement the assignment with comprehensive testing (functional, e2e, selenium)
5. Execute git commit with pre-commit hooks (all tests must pass with zero failures)
6. Update epics.md, specific epic file, changelog.md, and create corresponding changelog file in docs/changelogs/

**Mandatory Requirements:**
- Follow exact workflow: docs → business plan → epics.md → docs/epics/<epic>.md → docs/assignments/<assignment>.md → implement+tests via make using data samples and checking output from fakturownia -> run make dev-extension and make build-extension → git precommit  via make → update epics.md → changelog.md → docs/changelogs/ -> bump VERSIONN -> git commit
- All commands must be wrapped in Makefile targets and executed only via `make` command
- Task completion requires successful git commit with pre-commit hooks passing all tests
- Use selenium browser tests to verify Chrome extension state as first step
- Use browser-like tests to verify functionally and catch any console errors
- Continue assignment completion cycle without stopping after each task
- For functional tests of deepseek analysis use files at docs/data/samples/input
- For outputs from fakturownia save them to docs/data/samples/output and compare with inputs

**Documentation Compliance:**
Ensure all docs procedures are followed including business plan alignment, git workflow, architecture guidelines, README updates, template usage, epic tracking, and comprehensive changelog maintenance.
