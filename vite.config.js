import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';
import { readFileSync } from 'fs';

// Chrome Extension Vite Plugin
function chromeExtension() {
  return {
    name: 'chrome-extension',
    generateBundle(options, bundle) {
      // Read VERSION file
      const version = readFileSync(resolve(__dirname, 'VERSION'), 'utf-8').trim();

      // Read and update manifest.json with version
      const manifestContent = readFileSync(resolve(__dirname, 'manifest.json'), 'utf-8');
      const manifest = JSON.parse(manifestContent);
      manifest.version = version;

      // Copy updated manifest.json to output
      this.emitFile({
        type: 'asset',
        fileName: 'manifest.json',
        source: JSON.stringify(manifest, null, 2)
      });

      // Find the generated CSS file
      const cssFiles = Object.keys(bundle).filter(fileName =>
        fileName.startsWith('assets/') && fileName.endsWith('.css')
      );
      const cssFileName = cssFiles.length > 0 ? cssFiles[0] : null;

      // Generate popup.html that loads the React bundle as ES module with CSS
      const popupHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>MVAT - Multi-VAT Invoice Processor</title>
  <meta name="description" content="Parse invoices using PDF.js, Tesseract.js, and OpenAI">
  ${cssFileName ? `<link rel="stylesheet" href="${cssFileName}">` : ''}
  <style>
    /* Loading spinner styles for initial load */
    .loading-spinner {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 400px;
      padding: 2rem;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    .spinner {
      width: 32px;
      height: 32px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #3498db;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 1rem;
    }
    .loading-text {
      color: #666;
      font-size: 14px;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    /* Loading handled by React components */
  </style>
</head>
<body class="w-screen">
  <div id="root">
    <!-- Loading state -->
    <div class="loading-spinner">
      <div class="spinner"></div>
      <div class="loading-text">Loading MVAT...</div>
    </div>
  </div>
  <script type="module" src="popup.js"></script>
</body>
</html>`;

      this.emitFile({
        type: 'asset',
        fileName: 'popup.html',
        source: popupHtml
      });

      // Copy icons to output
      const iconSizes = ['16', '32', '48', '128'];
      iconSizes.forEach(size => {
        try {
          const iconPath = resolve(__dirname, `public/icons/icon-${size}.png`);
          const iconContent = readFileSync(iconPath);
          this.emitFile({
            type: 'asset',
            fileName: `icons/icon-${size}.png`,
            source: iconContent
          });
        } catch (error) {
          console.warn(`Warning: Could not copy icon-${size}.png:`, error.message);
        }
      });

      // Note: CSS and JS are now handled by Vite bundling
      // No need to copy static files since we're using React

      // Copy PDF.js worker for Chrome extension
      try {
        const pdfWorkerPath = resolve(__dirname, 'node_modules/pdfjs-dist/build/pdf.worker.min.js');
        const pdfWorkerContent = readFileSync(pdfWorkerPath, 'utf-8');
        this.emitFile({
          type: 'asset',
          fileName: 'assets/pdf.worker.min.js',
          source: pdfWorkerContent
        });
      } catch (error) {
        console.warn('Warning: Could not copy PDF.js worker:', error.message);
      }

      // Copy Tesseract.js files for Chrome extension CSP compliance
      try {
        // Copy Tesseract.js worker
        const tesseractWorkerPath = resolve(__dirname, 'node_modules/tesseract.js/dist/worker.min.js');
        const tesseractWorkerContent = readFileSync(tesseractWorkerPath, 'utf-8');
        this.emitFile({
          type: 'asset',
          fileName: 'assets/tesseract.worker.min.js',
          source: tesseractWorkerContent
        });

        // Copy main Tesseract.js library for sandbox
        const tesseractMainPath = resolve(__dirname, 'node_modules/tesseract.js/dist/tesseract.min.js');
        const tesseractMainContent = readFileSync(tesseractMainPath, 'utf-8');
        this.emitFile({
          type: 'asset',
          fileName: 'assets/tesseract.min.js',
          source: tesseractMainContent
        });

        console.log('✅ Tesseract.js library and worker copied to assets/');
      } catch (error) {
        console.warn('Warning: Could not copy Tesseract.js files:', error.message);
      }

      // Copy sandbox files for Tesseract.js processing
      try {
        const sandboxHtmlPath = resolve(__dirname, 'src/sandbox/sandbox.html');
        const sandboxHtmlContent = readFileSync(sandboxHtmlPath, 'utf-8');
        this.emitFile({
          type: 'asset',
          fileName: 'sandbox/sandbox.html',
          source: sandboxHtmlContent
        });

        const sandboxJsPath = resolve(__dirname, 'src/sandbox/sandbox.js');
        const sandboxJsContent = readFileSync(sandboxJsPath, 'utf-8');
        this.emitFile({
          type: 'asset',
          fileName: 'sandbox/sandbox.js',
          source: sandboxJsContent
        });

        console.log('✅ Sandbox files copied to sandbox/');
      } catch (error) {
        console.warn('Warning: Could not copy sandbox files:', error.message);
      }
    }
  };
}

export default defineConfig(({ mode }) => {
  const isDev = mode === 'development';

  // Read VERSION file
  const version = readFileSync(resolve(__dirname, 'VERSION'), 'utf-8').trim();

  // Load environment variables from .env files only
  const env = loadEnv(mode, process.cwd(), '');

  // Create environment variables object for injection
  const envVars = {};
  const filteredEnv = {};

  // Add VERSION to environment variables
  filteredEnv.APP_VERSION = version;

  // Add APP_VERSION to environment variables injection
  envVars[`import.meta.env.APP_VERSION`] = JSON.stringify(version);
  envVars[`define_process_env_default.APP_VERSION`] = JSON.stringify(version);

  Object.keys(env).forEach(key => {
    if (key.startsWith('DEEPSEEK_') ||
        key.startsWith('COMPANY_') ||
        key.startsWith('STRIPE_') ||
        key.startsWith('FAKTUROWNIA_') ||
        key.startsWith('FEATURE_') ||
        key.startsWith('STARTER_') ||
        key.startsWith('PROFESSIONAL_') ||
        key.startsWith('BUSINESS_') ||
        key.startsWith('ENTERPRISE_') ||
        key.startsWith('NODE_ENV') ||
        key.startsWith('APP_') ||
        key.startsWith('DEBUG_') ||
        key.startsWith('LOG_') ||
        key.startsWith('MAX_') ||
        key.startsWith('API_') ||
        key.startsWith('CACHE_') ||
        key.startsWith('ENCRYPTION_') ||
        key.startsWith('SESSION_') ||
        key.startsWith('PASSWORD_') ||
        key.startsWith('FREE_') ||
        key.startsWith('TRIAL_') ||
        key.startsWith('DEV_') ||
        key.startsWith('TEST_') ||
        key.startsWith('MOCK_') ||
        key.startsWith('SELENIUM_') ||
        key.startsWith('ANALYTICS_') ||
        key.startsWith('SENTRY_') ||
        key.startsWith('GOOGLE_') ||
        key.startsWith('DEFAULT_') ||
        key.startsWith('SUPPORTED_') ||
        key.startsWith('VAT_') ||
        key.startsWith('DATE_') ||
        key.startsWith('NUMBER_')) {
      // Add to both import.meta.env injection and filtered env
      envVars[`import.meta.env.${key}`] = JSON.stringify(env[key]);
      // Also add individual process.env replacements for compatibility
      envVars[`define_process_env_default.${key}`] = JSON.stringify(env[key]);
      filteredEnv[key] = env[key];
    }
  });

  console.log('🔧 Vite: Injecting', Object.keys(filteredEnv).length, 'environment variables');
  console.log('🔧 Vite: Environment variables:', Object.keys(filteredEnv).join(', '));

  return {
    plugins: [
      react({
        jsxRuntime: 'automatic'
      }),
      chromeExtension()
    ],

    define: {
      'process.env.NODE_ENV': JSON.stringify(mode),
      'process.env.DEV': JSON.stringify(isDev),
      global: 'globalThis',
      // Inject environment variables
      ...envVars,
      // Define the environment object that will be assigned to window.__MVAT_ENV__
      '__MVAT_ENV_OBJECT__': JSON.stringify(filteredEnv),
      // Define process.env object for compatibility
      'define_process_env_default': JSON.stringify(filteredEnv)
    },

    build: {
      outDir: process.env.VITE_BUILD_TARGET === 'dev' ? 'dist/dev' :
              process.env.VITE_BUILD_TARGET === 'build' ? 'dist/build' : 'dist',
      emptyOutDir: true,
      sourcemap: isDev,
      minify: !isDev,
      target: 'es2022',

      rollupOptions: {
        input: {
          popup: resolve(__dirname, 'src/popup/main.jsx'),
          background: resolve(__dirname, 'src/background/background.js'),
          // Add content script if needed
          // content: resolve(__dirname, 'src/content/content.js')
        },

        output: {
          format: 'es', // Use ES modules format
          entryFileNames: (chunkInfo) => {
            // Keep specific names for extension files
            if (chunkInfo.name === 'background') {
              return 'background.js';
            }
            if (chunkInfo.name === 'popup') {
              return 'popup.js';
            }
            if (chunkInfo.name === 'content') {
              return 'content.js';
            }
            return 'assets/[name]-[hash].js';
          },
          chunkFileNames: 'assets/[name]-[hash].js',
          assetFileNames: (assetInfo) => {
            // Keep specific names for extension assets
            if (assetInfo.name === 'popup.html') {
              return 'popup.html';
            }
            if (assetInfo.name?.endsWith('.css')) {
              return 'assets/[name]-[hash].css';
            }
            return 'assets/[name]-[hash].[ext]';
          }
        }
      }
    },

    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
        '@components': resolve(__dirname, 'src/components'),
        '@api': resolve(__dirname, 'src/api'),
        '@core': resolve(__dirname, 'src/core'),
        '@shared': resolve(__dirname, 'src/shared'),
        '@popup': resolve(__dirname, 'src/popup'),
        '@background': resolve(__dirname, 'src/background')
      }
    },

    server: {
      port: 3000,
      open: false, // Don't auto-open browser for extension dev
      hmr: {
        port: 3001
      }
    },

    // Chrome extension specific optimizations
    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        'react-router-dom'
      ],
      exclude: [
        // Exclude Chrome APIs from bundling
        'chrome'
      ]
    },



    // Handle Chrome extension CSP restrictions
    esbuild: {
      // Remove console logs in production
      drop: isDev ? [] : ['console', 'debugger']
    }
  };
});
