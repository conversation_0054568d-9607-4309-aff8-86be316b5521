# <type>(<scope>): <subject>
#
# <body>
#
# <footer>
#
# Epic: <EPIC-ID>
# Story: <STORY-ID>
# Task: <TASK-ID>

# --- COMMIT MESSAGE GUIDELINES ---
#
# Type (required):
#   feat:     A new feature
#   fix:      A bug fix
#   docs:     Documentation only changes
#   style:    Changes that do not affect the meaning of the code
#   refactor: A code change that neither fixes a bug nor adds a feature
#   perf:     A code change that improves performance
#   test:     Adding missing tests or correcting existing tests
#   chore:    Changes to the build process or auxiliary tools
#   ci:       Changes to CI configuration files and scripts
#   build:    Changes that affect the build system or external dependencies
#
# Scope (optional):
#   business, subscription, security, ai, analytics, integration, 
#   platform, global, customer, ui, api, core, tests, docs, build
#
# Subject (required):
#   - Use imperative mood ("add" not "added" or "adds")
#   - Don't capitalize first letter
#   - No period at the end
#   - Limit to 50 characters
#
# Body (optional):
#   - Explain what and why, not how
#   - Wrap at 72 characters
#   - Separate from subject with blank line
#
# Footer (optional):
#   - Reference issues, breaking changes, etc.
#   - Format: "Closes #123" or "BREAKING CHANGE: ..."
#
# Epic/Story/Task (required):
#   - Link to business epic, story, or task
#   - Format: "Epic: B1-SUBSCRIPTION-MONETIZATION"
#   - Format: "Story: B1.1-SUBSCRIPTION-TIER-MANAGEMENT"
#   - Format: "Task: B1.1.1-SUBSCRIPTION-DATA-MODELS"
#
# Examples:
#   feat(subscription): add tier validation logic
#   
#   Implement subscription tier validation with usage limits
#   and feature gating for freemium model.
#   
#   Epic: B1-SUBSCRIPTION-MONETIZATION
#   Story: B1.1-SUBSCRIPTION-TIER-MANAGEMENT
#   Task: B1.1.2-SUBSCRIPTION-SERVICE-LAYER
#
#   fix(ai): handle API timeout errors gracefully
#   
#   Add retry logic and fallback mechanisms for AI provider
#   timeouts to improve reliability.
#   
#   Closes #456
#   
#   Epic: B3-AI-POWERED-EXTRACTION
#   Story: B3.2-AI-PROVIDER-MANAGEMENT
#   Task: B3.2.3-ERROR-HANDLING
