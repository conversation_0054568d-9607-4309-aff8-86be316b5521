# 🎯 **GITHUB ISSUE CREATION GUIDELINES**

## **📋 INITIAL TASK ASSIGNMENT MESSAGE**

Use this template when creating new GitHub issues for task assignments:

---

### **🔍 MANDATORY PRE-ASSIGNMENT CHECKLIST**

Before creating any new task assignment, **ALWAYS** review the following documentation in order:

#### **1. Business Context & Strategy**
- [ ] **Read:** `@docs/business-planning/BUSINESS_PLAN.md` - Understand customer needs, market analysis, and business objectives
- [ ] **Check:** Current business priorities and revenue impact
- [ ] **Verify:** How this task aligns with customer wants/needs/fears from business plan

#### **2. Current Project State**
- [ ] **Review:** `@docs/EPICS.md` - Current epic progress and priorities
- [ ] **Check:** Active work and next priorities
- [ ] **Verify:** Dependencies and blockers

#### **3. Epic & Story Context**
- [ ] **Review:** `@docs/epics/EPIC-XXX.md` - Detailed epic requirements
- [ ] **Check:** Story breakdown and task dependencies
- [ ] **Verify:** Acceptance criteria and technical requirements

#### **4. Recent Changes & Progress**
- [ ] **Review:** `@docs/CHANGELOGS.md` - Recent completions and active work
- [ ] **Check:** `@docs/changelogs/` - Latest changelog entries
- [ ] **Verify:** What has been completed and what's in progress

#### **5. Assignment History**
- [ ] **Review:** `@docs/assignments/` - Previous assignments and patterns
- [ ] **Check:** Assignment template and workflow requirements
- [ ] **Verify:** Proper assignment numbering and dependencies

---

## **📝 GITHUB ISSUE TEMPLATE**

```markdown
# 🎯 **TASK ASSIGNMENT: [ASSIGNMENT_TITLE]**

## **📋 ASSIGNMENT OVERVIEW**

**Assignment ID:** ASSIGNMENT-XXX  
**Epic:** EPIC-XXX - [Epic Name]  
**Story:** STORY-XXX - [Story Name]  
**Task:** TASK-XXX - [Task Name]  
**Priority:** [Critical/High/Medium/Low]  
**Estimate:** [X hours/days]  

## **🎯 BUSINESS CONTEXT**

### **Customer Value**
[How this addresses customer wants/needs from business plan]

### **Revenue Impact**
[Connection to subscription tiers or business objectives]

### **Market Priority**
[Why this task is prioritized now based on @docs/EPICS.md]

## **📚 DOCUMENTATION REFERENCES**

### **Required Reading** *(Must review before starting)*
- [ ] [Business Plan](docs/business-planning/BUSINESS_PLAN.md) - Customer context
- [ ] [Epic Overview](docs/EPICS.md) - Current project status  
- [ ] [Epic Details](docs/epics/EPIC-XXX.md) - Detailed requirements
- [ ] [Recent Changes](docs/CHANGELOGS.md) - Latest progress

### **Assignment Details**
- [ ] [Assignment Document](docs/assignments/ASSIGNMENT-XXX.md) - Complete specifications

## **🎯 ACCEPTANCE CRITERIA**

- [ ] [Specific completion criteria from assignment]
- [ ] [Testing requirements]
- [ ] [Documentation requirements]
- [ ] [Performance requirements]

## **🧪 TESTING REQUIREMENTS**

### **Mandatory Testing** *(All must pass)*
- [ ] **Unit Tests:** >95% coverage
- [ ] **Functional Tests:** API/integration testing
- [ ] **E2E Tests:** Complete user workflows  
- [ ] **Visual Tests:** Selenium screenshots
- [ ] **Pre-commit:** All 4 test tiers passing

## **🔄 WORKFLOW REQUIREMENTS**

### **Implementation Process**
1. **Review Documentation:** Complete all required reading above
2. **Create Branch:** `feature/ASSIGNMENT-XXX-[description]`
3. **Implement:** Follow single-purpose files and DRY principles
4. **Test:** Write tests alongside code development
5. **Document:** Update relevant documentation

### **Completion Process**
1. **Pre-commit:** Run `make pre-commit` (all tests must pass)
2. **Changelog:** Create `docs/changelogs/CHANGELOG-EPIC-XXX-STORY-XXX-TASK-XXX.md`
3. **Commit:** Use format: `feat(EPIC-XXX/STORY-XXX/TASK-XXX): [description]`
4. **Update Docs:** Update @docs/EPICS.md and @docs/CHANGELOGS.md
5. **PR Review:** Submit for review with changelog reference

## **📊 SUCCESS METRICS**

- [ ] **Technical:** All tests passing, >95% coverage
- [ ] **Business:** Acceptance criteria met
- [ ] **Quality:** Code review approved
- [ ] **Documentation:** All docs updated

## **🔗 DEPENDENCIES**

### **Blockers**
- [ ] [List any blocking tasks or requirements]

### **Prerequisites**
- [ ] [Required completions before starting]

---

**Assignment Created:** [YYYY-MM-DD]  
**Documentation Reviewed:** [YYYY-MM-DD]  
**Ready for Assignment:** [Yes/No]
```

---

## **⚠️ CRITICAL WORKFLOW REQUIREMENTS**

### **Documentation-First Approach**
Every task assignment MUST follow this flow:

```
@docs/ → business plan → epics.md → epics/<epic>.md → assignments/<assignment>.md → implement+tests → git commit with precommit → epics.md → changelog.md → changelogs/
```

### **Mandatory Pre-Assignment Steps**
1. **Business Alignment:** Verify task aligns with business plan priorities
2. **Epic Context:** Understand current epic status and dependencies  
3. **Technical Readiness:** Confirm all prerequisites are complete
4. **Resource Availability:** Ensure developer capacity and timeline

### **Quality Gates**
- [ ] **Documentation Review:** All @docs references checked
- [ ] **Business Justification:** Clear customer/revenue value
- [ ] **Technical Specification:** Complete acceptance criteria
- [ ] **Testing Strategy:** All 4 test tiers planned
- [ ] **Workflow Compliance:** Follows documented process

---

## **🎯 ASSIGNMENT CREATION CHECKLIST**

### **Before Creating GitHub Issue**
- [ ] Reviewed all required @docs files
- [ ] Verified business context and customer value
- [ ] Confirmed epic/story/task dependencies
- [ ] Checked recent changelog entries
- [ ] Created detailed assignment document
- [ ] Validated technical requirements
- [ ] Planned testing strategy

### **GitHub Issue Creation**
- [ ] Used proper template format
- [ ] Included all documentation references
- [ ] Specified clear acceptance criteria
- [ ] Defined testing requirements
- [ ] Set appropriate priority and estimate
- [ ] Added relevant labels and milestone

### **Post-Creation Validation**
- [ ] Assignment document created in @docs/assignments/
- [ ] All documentation links working
- [ ] Dependencies clearly identified
- [ ] Success metrics defined
- [ ] Workflow process documented

---

**Template Version:** 1.0  
**Last Updated:** 2025-01-27  
**Next Review:** After first assignment completion
