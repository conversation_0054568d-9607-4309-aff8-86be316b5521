# 📋 **EPIC-{EPIC_ID}: {EPIC_NAME}**

## **📖 EPIC OVERVIEW**

**Epic ID:** EPIC-{EPIC_ID}
**Epic Name:** {EPIC_NAME}
**Priority:** {Critical|High|Medium|Low}
**Estimate:** {X} days
**Status:** {Not Started|In Progress|Complete|Blocked}
**Progress:** {X}%

### **📦 VERSION INFORMATION**
**Start Version:** {X.Y.Z} (version when epic begins)
**Target Version:** {X.Y.Z} (expected version when epic completes)
**Version Impact:** {MAJOR|MINOR|PATCH} - {Brief description of version impact}
**Breaking Changes:** {Yes/No} - {Description if yes}

**Epic Description:**
{Brief description of what this epic accomplishes and why it's important}

---

## **🎯 BUSINESS VALUE**

### **Target Users**
- {User type 1}
- {User type 2}
- {User type 3}

### **Success Metrics**
- {Metric 1}: {Target value}
- {Metric 2}: {Target value}
- {Metric 3}: {Target value}

### **Business Requirements**
- [ ] {Requirement 1}
- [ ] {Requirement 2}
- [ ] {Requirement 3}

---

## **🔧 TECHNICAL REQUIREMENTS**

### **Technical Specifications**
- [ ] {Technical requirement 1}
- [ ] {Technical requirement 2}
- [ ] {Technical requirement 3}

### **Dependencies**
- **Depends on:** {List of dependent epics/stories}
- **Blocks:** {List of blocked epics/stories}
- **Related:** {List of related epics/stories}

---

## **📋 STORIES BREAKDOWN**

### **STORY {STORY_ID}: {STORY_NAME}**
**Priority:** {Critical|High|Medium|Low} | **Estimate:** {X} story points

**User Story:**
As a {user type}, I want {goal} so that {benefit}.

**Acceptance Criteria:**
- [ ] Given {context}, when {action}, then {expected result}
- [ ] Given {context}, when {action}, then {expected result}
- [ ] Given {context}, when {action}, then {expected result}

**Technical Requirements:**
- {Technical requirement 1}
- {Technical requirement 2}

**Tasks:**
- [ ] **Task {TASK_ID}:** {Task name} - {X}h
- [ ] **Task {TASK_ID}:** {Task name} - {X}h

---

## **🧪 TESTING STRATEGY**

### **Test Coverage Requirements**
- [ ] Unit tests: 95%+ coverage
- [ ] Functional tests: All critical paths
- [ ] E2E tests: Complete user workflows
- [ ] Visual regression tests: UI components

### **Quality Gates**
- [ ] All tests passing
- [ ] Code review completed
- [ ] Documentation updated
- [ ] Performance benchmarks met

---

## **📊 PROGRESS TRACKING**

### **Version Progress**
**Epic Progress:**
- v{X.Y.Z}: Epic started with {initial milestone}
- v{X.Y.Z}: {Story/Task completion milestone}
- v{X.Y.Z}: {Story/Task completion milestone}
- v{X.Y.Z}: (Target) Epic completion with {final milestone}

### **Completion Criteria**
- [ ] All stories completed
- [ ] All acceptance criteria met
- [ ] All tests passing
- [ ] Documentation complete
- [ ] Code review approved
- [ ] Target version reached

### **Risk Assessment**
- **Low Risk:** {List low-risk items}
- **Medium Risk:** {List medium-risk items}
- **High Risk:** {List high-risk items}

### **Mitigation Strategies**
- {Risk 1}: {Mitigation strategy}
- {Risk 2}: {Mitigation strategy}

---

## **🔗 RELATED DOCUMENTATION**

### **Epic Documentation**
- [Task Breakdown](../TASK_BREAKDOWN_{STORY_ID}.md)
- [Technical Specifications](../TECHNICAL_SPECS_{EPIC_ID}.md)
- [Testing Plan](../TESTING_PLAN_{EPIC_ID}.md)

### **Changelogs**
- [Epic Changelog](../changelogs/CHANGELOG-EPIC-{EPIC_ID}.md)
- [Story Changelogs](../changelogs/) - See individual story changelogs

### **Related Files**
- {List of related source files}
- {List of related test files}
- {List of related documentation files}

---

## **📈 SUCCESS METRICS TRACKING**

| Metric | Target | Current | Status | Last Updated |
|--------|--------|---------|--------|--------------|
| {Metric 1} | {Target} | {Current} | {Status} | {Date} |
| {Metric 2} | {Target} | {Current} | {Status} | {Date} |
| {Metric 3} | {Target} | {Current} | {Status} | {Date} |

---

**Created:** {YYYY-MM-DD}
**Last Updated:** {YYYY-MM-DD HH:MM:SS UTC}
**Next Review:** {YYYY-MM-DD}
**Owner:** {Team/Person responsible}
**Stakeholders:** {List of stakeholders}
