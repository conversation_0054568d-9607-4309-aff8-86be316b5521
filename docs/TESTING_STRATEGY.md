# 🧪 **COMPREHENSIVE TESTING STRATEGY**

## **📋 TESTING OVERVIEW**

This document outlines the comprehensive testing strategy for the MVAT Chrome Extension, covering all testing types: unit, functional, end-to-end, and Selenium-based visual testing.

---

## **🎯 TESTING OBJECTIVES**

1. **Quality Assurance:** Ensure 90%+ code coverage and zero critical bugs
2. **User Experience:** Validate all user workflows work seamlessly
3. **Performance:** Verify extension performs well with large datasets
4. **Compatibility:** Test across different Chrome versions and operating systems
5. **Reliability:** Ensure consistent behavior across different document types
6. **Security:** Validate data handling and storage security

---

## **📚 TESTING PYRAMID**

### **Level 1: Unit Tests (70% of tests)**
**Framework:** Jest + React Testing Library  
**Coverage Target:** 95%  
**Execution:** Fast (< 5 seconds total)

#### **Components to Test:**
- **React Components:**
  - `src/popup/components/common/` - But<PERSON>, Card, Modal, etc.
  - `src/popup/components/tables/` - InvoiceTable, GroupedView, SummaryStats
  - `src/popup/components/settings/` - CompanySettings, DisplaySettings
  - `src/popup/components/upload/` - FileUpload, DragDrop, ProgressBar

- **Custom Hooks:**
  - `src/popup/hooks/useStorage.js`
  - `src/popup/hooks/useDocumentProcessor.js`
  - `src/popup/hooks/useSettings.js`

- **Utility Functions:**
  - `src/shared/utils/dateUtils.js`
  - `src/shared/utils/validationUtils.js`
  - `src/shared/utils/formatUtils.js`

- **Services:**
  - `src/api/StorageAPI.js`
  - `src/api/DeepSeekAPI.js`
  - `src/core/services/DocumentAnalysisService.js`

#### **Test Structure:**
```javascript
// Example: src/popup/components/common/__tests__/Button.test.jsx
describe('Button Component', () => {
  test('renders with correct text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });

  test('handles click events', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    fireEvent.click(screen.getByText('Click me'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  test('applies correct CSS classes', () => {
    render(<Button variant="primary">Primary</Button>);
    expect(screen.getByText('Primary')).toHaveClass('btn-primary');
  });
});
```

### **Level 2: Functional Tests (20% of tests)**
**Framework:** Enhanced test-runner.js  
**Coverage Target:** All API integrations and data flows  
**Execution:** Medium (< 30 seconds total)

#### **Test Categories:**

**API Integration Tests:**
- DeepSeek API connection and response parsing
- OpenAI API fallback mechanisms
- Storage API CRUD operations
- Chrome extension API interactions

**Data Processing Tests:**
- PDF.js text extraction accuracy
- Tesseract.js OCR processing
- Field extraction and validation
- Data transformation and storage

**Business Logic Tests:**
- Invoice categorization logic
- Date grouping algorithms
- Summary calculation accuracy
- Settings validation and persistence

#### **Test Structure:**
```javascript
// Example: tests/functional/documentProcessing.test.js
describe('Document Processing Pipeline', () => {
  test('processes PDF invoice end-to-end', async () => {
    const pdfFile = await loadTestPDF('sample-invoice.pdf');
    const result = await documentProcessor.process(pdfFile);
    
    expect(result.success).toBe(true);
    expect(result.data.seller_name).toBeDefined();
    expect(result.data.total_gross).toMatch(/^\d+\.\d{2}$/);
    expect(result.data.positions).toHaveLength(greaterThan(0));
  });

  test('handles OCR processing for scanned documents', async () => {
    const imageFile = await loadTestImage('scanned-invoice.jpg');
    const result = await ocrProcessor.process(imageFile);
    
    expect(result.confidence).toBeGreaterThan(0.8);
    expect(result.text).toContain('FAKTURA');
    expect(result.language).toBe('pol');
  });
});
```

### **Level 3: End-to-End Tests (8% of tests)**
**Framework:** Playwright  
**Coverage Target:** Complete user workflows  
**Execution:** Slow (< 2 minutes total)

#### **Test Scenarios:**

**User Workflow Tests:**
1. **Document Upload and Processing:**
   - Upload PDF invoice
   - Wait for processing completion
   - Verify extracted data in table
   - Check data persistence

2. **Settings Management:**
   - Open settings tab
   - Update company information
   - Save settings
   - Verify persistence across sessions

3. **Data Visualization:**
   - Switch between year/quarter/month views
   - Apply filters and sorting
   - Export data to JSON
   - Verify export accuracy

4. **Error Handling:**
   - Upload invalid file types
   - Test with corrupted PDFs
   - Simulate API failures
   - Verify graceful error handling

#### **Test Structure:**
```javascript
// Example: tests/e2e/userWorkflows.spec.js
test('complete invoice processing workflow', async ({ page, extensionId }) => {
  // Navigate to extension popup
  await page.goto(`chrome-extension://${extensionId}/popup.html`);
  
  // Upload test invoice
  const fileInput = page.locator('input[type="file"]');
  await fileInput.setInputFiles('tests/fixtures/test-invoice.pdf');
  
  // Wait for processing
  await page.waitForSelector('[data-testid="processing-complete"]');
  
  // Verify data appears in table
  const invoiceRow = page.locator('[data-testid="invoice-row"]').first();
  await expect(invoiceRow).toContainText('Test Company');
  await expect(invoiceRow).toContainText('1,230.00');
  
  // Test grouping functionality
  await page.selectOption('[data-testid="group-by-select"]', 'month');
  await expect(page.locator('[data-testid="month-group"]')).toBeVisible();
});
```

### **Level 4: Selenium Visual Tests (2% of tests)**
**Framework:** Selenium WebDriver + Python  
**Coverage Target:** Visual regression and cross-browser compatibility  
**Execution:** Slowest (< 5 minutes total)

#### **Visual Test Categories:**

**Screenshot Comparison Tests:**
- Component rendering across different screen sizes
- Theme switching (light/dark mode)
- Table layouts with various data volumes
- Error state visualizations

**Cross-Browser Tests:**
- Chrome stable, beta, dev channels
- Different operating systems (Windows, macOS, Linux)
- Various screen resolutions and DPI settings

#### **Test Structure:**
```python
# Example: tests/selenium/visual_regression.py
class VisualRegressionTests(PopupTests):
    def test_invoice_table_layout(self):
        """Test invoice table renders correctly with sample data"""
        self.load_sample_data()
        self.navigate_to_table_view()
        
        # Take screenshot
        screenshot_path = self.take_screenshot('invoice_table_layout')
        
        # Compare with baseline
        baseline_path = 'tests/selenium/baselines/invoice_table_layout.png'
        similarity = self.compare_screenshots(screenshot_path, baseline_path)
        
        self.assertGreater(similarity, 0.95, "Table layout differs from baseline")
    
    def test_responsive_design(self):
        """Test responsive behavior at different screen sizes"""
        screen_sizes = [(1920, 1080), (1366, 768), (768, 1024)]
        
        for width, height in screen_sizes:
            self.driver.set_window_size(width, height)
            screenshot_path = self.take_screenshot(f'responsive_{width}x{height}')
            
            # Verify key elements are visible
            self.assert_element_visible('[data-testid="main-navigation"]')
            self.assert_element_visible('[data-testid="invoice-table"]')
```

---

## **🔧 TESTING INFRASTRUCTURE**

### **Test Configuration Files:**

**Jest Configuration (`jest.config.js`):**
```javascript
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/tests/unit/setup.js'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@popup/(.*)$': '<rootDir>/src/popup/$1',
    '^@shared/(.*)$': '<rootDir>/src/shared/$1'
  },
  collectCoverageFrom: [
    'src/**/*.{js,jsx}',
    '!src/**/*.test.{js,jsx}',
    '!src/**/index.js'
  ],
  coverageThreshold: {
    global: {
      branches: 90,
      functions: 90,
      lines: 90,
      statements: 90
    }
  }
};
```

**Playwright Configuration (`tests/e2e/playwright.config.js`):**
```javascript
module.exports = {
  testDir: './tests/e2e',
  timeout: 30000,
  use: {
    headless: false,
    viewport: { width: 1280, height: 720 },
    screenshot: 'only-on-failure',
    video: 'retain-on-failure'
  },
  projects: [
    {
      name: 'chrome-extension',
      use: {
        ...devices['Desktop Chrome'],
        launchOptions: {
          args: [
            '--disable-extensions-except=./dist',
            '--load-extension=./dist'
          ]
        }
      }
    }
  ]
};
```

### **Test Data Management:**

**Test Fixtures Structure:**
```
tests/fixtures/
├── pdfs/
│   ├── valid-vat-invoice.pdf
│   ├── multi-page-invoice.pdf
│   ├── corrupted-file.pdf
│   └── non-invoice-document.pdf
├── images/
│   ├── scanned-invoice-high-quality.jpg
│   ├── scanned-invoice-low-quality.jpg
│   └── non-document-image.jpg
├── json/
│   ├── sample-invoice-data.json
│   ├── sample-settings.json
│   └── expected-extraction-results.json
└── api-responses/
    ├── deepseek-success.json
    ├── deepseek-error.json
    └── openai-fallback.json
```

---

## **📊 TESTING METRICS & REPORTING**

### **Coverage Requirements:**
- **Unit Tests:** 95% line coverage, 90% branch coverage
- **Functional Tests:** 100% API endpoint coverage
- **E2E Tests:** 100% critical user path coverage
- **Visual Tests:** 100% component visual coverage

### **Performance Benchmarks:**
- **Unit Tests:** Complete in < 5 seconds
- **Functional Tests:** Complete in < 30 seconds
- **E2E Tests:** Complete in < 2 minutes
- **Visual Tests:** Complete in < 5 minutes
- **Total Test Suite:** Complete in < 7 minutes

### **Quality Gates:**
- All tests must pass before merge
- Coverage thresholds must be met
- No critical or high-severity issues
- Performance benchmarks must be met
- Visual regression tests must pass

### **Test Reporting:**
- **HTML Coverage Reports:** Generated by Jest
- **E2E Test Videos:** Recorded by Playwright on failures
- **Screenshot Comparisons:** Generated by Selenium tests
- **Performance Metrics:** Tracked in test execution logs
- **CI/CD Integration:** Results posted to pull requests

---

## **🚀 CONTINUOUS INTEGRATION**

### **Makefile Test Commands:**
```makefile
# Run all tests
test-all: test-unit test-functional test-e2e test-visual

# Individual test types
test-unit:
	npm run test:unit -- --coverage

test-functional:
	node tests/test-runner.js

test-e2e:
	npx playwright test

test-visual:
	python tests/selenium/run_all_tests.py

# Test with watch mode for development
test-watch:
	npm run test:unit -- --watch

# Generate test reports
test-report:
	npm run test:unit -- --coverage --coverageReporters=html
	npx playwright show-report
```

### **Pre-commit Hooks:**
- Run unit tests and functional tests
- Check code coverage thresholds
- Lint code and fix formatting
- Validate commit message format

### **CI Pipeline:**
1. **Fast Feedback (< 1 minute):**
   - Lint and format check
   - Unit tests execution
   - Basic build verification

2. **Comprehensive Testing (< 10 minutes):**
   - Functional tests
   - E2E tests across multiple browsers
   - Visual regression tests
   - Performance benchmarks

3. **Release Validation (< 15 minutes):**
   - Full test suite execution
   - Security scans
   - Extension packaging validation
   - Documentation generation

---

## **📋 TEST EXECUTION CHECKLIST**

### **Before Each Development Session:**
- [ ] Run `make test-unit` to ensure baseline functionality
- [ ] Check test coverage reports for any gaps
- [ ] Review failed tests from previous session

### **Before Each Commit:**
- [ ] Run `make test-all` to execute full test suite
- [ ] Verify all new code has corresponding tests
- [ ] Update test documentation if needed

### **Before Each Release:**
- [ ] Execute full test suite on clean environment
- [ ] Run visual regression tests on multiple screen sizes
- [ ] Validate extension packaging and installation
- [ ] Perform manual smoke testing of critical paths

### **Weekly Maintenance:**
- [ ] Review and update test fixtures
- [ ] Analyze test execution performance
- [ ] Update baseline screenshots if UI changes
- [ ] Review and refactor slow or flaky tests

This comprehensive testing strategy ensures the MVAT Chrome Extension maintains high quality, reliability, and user experience across all supported environments and use cases.
