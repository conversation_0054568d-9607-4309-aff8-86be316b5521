# 🎯 **GITHUB ISSUE CREATION MESSAGE**

## **📋 INITIAL TASK ASSIGNMENT GUIDELINES**

Use this message when creating new GitHub issues for task assignments in the MVAT Chrome Extension project.

---

## **🔍 MANDATORY PRE-ASSIGNMENT REVIEW**

**⚠️ CRITICAL:** Before creating any new task assignment, you MUST review our comprehensive documentation system to ensure business alignment and technical readiness.

### **📚 Required Documentation Review (In Order):**

#### **1. Business Foundation** *(Understanding WHY)*
- [ ] **Read:** [`@docs/business-planning/BUSINESS_PLAN.md`](docs/business-planning/BUSINESS_PLAN.md)
  - Customer analysis (wants, needs, fears, blockers)
  - Market strategy and revenue model
  - Subscription tier structure and pricing
  - Success metrics and business objectives

#### **2. Current Project State** *(Understanding WHERE WE ARE)*
- [ ] **Review:** [`@docs/EPICS.md`](docs/EPICS.md)
  - Current epic progress and priorities
  - Active work and next priorities
  - Core functionality vs. business feature prioritization
  - Dependencies and blockers

#### **3. Epic & Story Context** *(Understanding WHAT TO BUILD)*
- [ ] **Review:** [`@docs/epics/EPIC-XXX.md`](docs/epics/) (relevant epic)
  - Detailed epic requirements and scope
  - Story breakdown and task dependencies
  - Acceptance criteria and technical specifications
  - Testing requirements and quality standards

#### **4. Recent Progress** *(Understanding WHAT'S BEEN DONE)*
- [ ] **Check:** [`@docs/CHANGELOGS.md`](docs/CHANGELOGS.md)
  - Recent completions and active work
  - Progress tracking and milestone status
- [ ] **Review:** [`@docs/changelogs/`](docs/changelogs/) (latest entries)
  - Detailed implementation history
  - Technical decisions and challenges
  - Testing results and coverage

#### **5. Assignment System** *(Understanding HOW TO WORK)*
- [ ] **Review:** [`@docs/assignments/README.md`](docs/assignments/README.md)
  - Complete assignment workflow process
  - Quality standards and testing requirements
  - Documentation integration requirements
- [ ] **Check:** [`@docs/assignments/`](docs/assignments/) (previous assignments)
  - Assignment patterns and numbering
  - Implementation approaches and lessons learned

---

## **📝 GITHUB ISSUE TEMPLATE MESSAGE**

Copy and customize this template for new GitHub issues:

```markdown
# 🎯 **TASK ASSIGNMENT: [ASSIGNMENT_TITLE]**

## **⚠️ DOCUMENTATION REVIEW REQUIRED**

**Before starting this assignment, you MUST review our documentation system:**

### **📚 Required Reading Checklist:**
- [ ] [Business Plan](docs/business-planning/BUSINESS_PLAN.md) - Customer context & business objectives
- [ ] [Epic Overview](docs/EPICS.md) - Current project status & priorities  
- [ ] [Epic Details](docs/epics/EPIC-XXX.md) - Detailed requirements & specifications
- [ ] [Recent Changes](docs/CHANGELOGS.md) - Latest progress & completions
- [ ] [Assignment System](docs/assignments/README.md) - Workflow & quality standards

## **📋 ASSIGNMENT OVERVIEW**

**Assignment ID:** ASSIGNMENT-XXX  
**Epic:** EPIC-XXX - [Epic Name]  
**Story:** STORY-XXX - [Story Name]  
**Task:** TASK-XXX - [Task Name]  
**Priority:** [Critical/High/Medium/Low]  
**Estimate:** [X hours/days]  

## **🎯 BUSINESS CONTEXT**

### **Customer Value** *(from business plan)*
[How this addresses specific customer wants/needs/fears from @docs/business-planning/BUSINESS_PLAN.md]

### **Revenue Impact** *(from subscription strategy)*
[Connection to subscription tiers: STARTER/PROFESSIONAL/BUSINESS/ENTERPRISE]

### **Market Priority** *(from epic status)*
[Why this task is prioritized now based on @docs/EPICS.md current focus]

## **📚 ASSIGNMENT DETAILS**

**Complete Specification:** [`docs/assignments/ASSIGNMENT-XXX.md`](docs/assignments/ASSIGNMENT-XXX.md)

### **Key Requirements:**
- [Primary objective from assignment document]
- [Critical acceptance criteria]
- [Testing requirements summary]

## **🧪 MANDATORY TESTING** *(All 4 Tiers Required)*

- [ ] **Unit Tests:** >95% coverage (Vitest + React Testing Library)
- [ ] **Functional Tests:** API/integration testing
- [ ] **E2E Tests:** Complete user workflows (Playwright)
- [ ] **Visual Tests:** Screenshot validation (Selenium)
- [ ] **Pre-commit:** All tests passing via `make pre-commit`

## **🔄 WORKFLOW REQUIREMENTS**

### **Implementation Process:**
```
@docs/ → business plan → epics.md → epics/<epic>.md → assignments/<assignment>.md → implement+tests → git commit with precommit → epics.md → changelog.md → changelogs/
```

### **Completion Checklist:**
- [ ] All acceptance criteria met
- [ ] All 4 test tiers passing with >95% coverage
- [ ] Changelog created: `docs/changelogs/CHANGELOG-EPIC-XXX-STORY-XXX-TASK-XXX.md`
- [ ] Git commit with format: `feat(EPIC-XXX/STORY-XXX/TASK-XXX): [description]`
- [ ] Documentation updated: `@docs/EPICS.md` and `@docs/CHANGELOGS.md`

## **📊 SUCCESS CRITERIA**

- [ ] **Business:** Customer value delivered per business plan
- [ ] **Technical:** All tests passing, >95% coverage
- [ ] **Quality:** Code review approved, standards met
- [ ] **Documentation:** All @docs files updated

## **🔗 DEPENDENCIES & BLOCKERS**

### **Prerequisites:**
- [ ] [Required completions before starting]

### **Blockers:**
- [ ] [Any blocking tasks or requirements]

---

**Assignment Created:** [YYYY-MM-DD]  
**Documentation Reviewed:** [Yes/No - Must be Yes before assignment]  
**Ready for Implementation:** [Yes/No]

**Next Steps:**
1. Complete documentation review checklist above
2. Review detailed assignment document
3. Create feature branch: `feature/ASSIGNMENT-XXX-[description]`
4. Follow documented workflow process
```

---

## **🎯 CRITICAL SUCCESS FACTORS**

### **Documentation-Driven Development**
- Every task MUST be grounded in business value from our documented business plan
- Technical decisions MUST align with customer needs and revenue objectives
- Progress MUST be tracked through our comprehensive changelog system

### **Quality Assurance**
- **Testing:** All 4 tiers (unit, functional, e2e, visual) with >95% coverage
- **Standards:** 2025 JS/UI/UX best practices with single-purpose files
- **Process:** Pre-commit hooks ensuring zero failures before commits

### **Business Alignment**
- **Customer Focus:** Address specific wants/needs/fears from business plan
- **Revenue Impact:** Contribute to subscription tier strategy
- **Market Timing:** Follow prioritization from epic status

---

## **📋 ASSIGNMENT CREATION CHECKLIST**

### **Before Creating GitHub Issue:**
- [ ] Completed all required documentation review
- [ ] Verified business context and customer value
- [ ] Confirmed epic/story/task dependencies are met
- [ ] Checked recent changelog entries for context
- [ ] Created detailed assignment document in `@docs/assignments/`
- [ ] Validated technical requirements and testing strategy

### **GitHub Issue Quality:**
- [ ] Used proper template format with all sections
- [ ] Included all required documentation references
- [ ] Specified clear acceptance criteria and success metrics
- [ ] Defined comprehensive testing requirements
- [ ] Set appropriate priority, estimate, and labels

---

**Template Version:** 1.0  
**Created:** 2025-01-27 21:50:00 UTC  
**Usage:** Copy template section for new GitHub issues  
**Next Update:** After first assignment completion feedback
