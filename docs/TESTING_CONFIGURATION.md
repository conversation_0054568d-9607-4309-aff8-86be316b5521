# 🧪 **TESTING CONFIGURATION GUIDE**

## **📋 OVERVIEW**

This document provides detailed configuration examples for all testing frameworks used in the MVAT Chrome Extension project. These configurations ensure comprehensive testing coverage with automated execution on code changes.

---

## **🔧 JEST UNIT TESTING CONFIGURATION**

### **jest.config.js**
```javascript
module.exports = {
  // Test environment
  testEnvironment: 'jsdom',
  
  // Setup files
  setupFilesAfterEnv: ['<rootDir>/tests/unit/setup.js'],
  
  // Module name mapping for clean imports
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@popup/(.*)$': '<rootDir>/src/popup/$1',
    '^@shared/(.*)$': '<rootDir>/src/shared/$1',
    '^@api/(.*)$': '<rootDir>/src/api/$1',
    '^@core/(.*)$': '<rootDir>/src/core/$1',
    '^@components/(.*)$': '<rootDir>/src/components/$1',
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy'
  },
  
  // Test file patterns
  testMatch: [
    '<rootDir>/tests/unit/**/*.test.{js,jsx}',
    '<rootDir>/src/**/__tests__/**/*.{js,jsx}',
    '<rootDir>/src/**/*.{test,spec}.{js,jsx}'
  ],
  
  // Coverage configuration
  collectCoverageFrom: [
    'src/**/*.{js,jsx}',
    '!src/**/*.test.{js,jsx}',
    '!src/**/__tests__/**',
    '!src/**/index.js',
    '!src/popup/main.jsx',
    '!src/background/background.js'
  ],
  
  // Coverage thresholds
  coverageThreshold: {
    global: {
      branches: 95,
      functions: 95,
      lines: 95,
      statements: 95
    },
    // Specific thresholds for critical modules
    'src/api/': {
      branches: 98,
      functions: 98,
      lines: 98,
      statements: 98
    },
    'src/core/services/': {
      branches: 97,
      functions: 97,
      lines: 97,
      statements: 97
    }
  },
  
  // Coverage reporters
  coverageReporters: ['text', 'lcov', 'html', 'json-summary'],
  
  // Transform configuration
  transform: {
    '^.+\\.(js|jsx)$': ['babel-jest', {
      presets: [
        ['@babel/preset-env', { targets: { node: 'current' } }],
        ['@babel/preset-react', { runtime: 'automatic' }]
      ]
    }]
  },
  
  // Test timeout
  testTimeout: 10000,
  
  // Clear mocks between tests
  clearMocks: true,
  
  // Restore mocks after each test
  restoreMocks: true
};
```

### **tests/unit/setup.js**
```javascript
import '@testing-library/jest-dom';
import 'jest-chrome';

// Mock Chrome APIs
global.chrome = {
  storage: {
    local: {
      get: jest.fn(),
      set: jest.fn(),
      remove: jest.fn(),
      clear: jest.fn()
    }
  },
  runtime: {
    sendMessage: jest.fn(),
    onMessage: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    }
  },
  windows: {
    create: jest.fn(),
    get: jest.fn(),
    remove: jest.fn()
  }
};

// Mock PDF.js
jest.mock('pdfjs-dist', () => ({
  getDocument: jest.fn(() => ({
    promise: Promise.resolve({
      numPages: 1,
      getPage: jest.fn(() => Promise.resolve({
        getTextContent: jest.fn(() => Promise.resolve({
          items: [{ str: 'Mock PDF text' }]
        }))
      }))
    })
  }))
}));

// Mock Tesseract.js
jest.mock('tesseract.js', () => ({
  createWorker: jest.fn(() => Promise.resolve({
    recognize: jest.fn(() => Promise.resolve({
      data: { text: 'Mock OCR text', confidence: 0.95 }
    })),
    terminate: jest.fn()
  }))
}));

// Global test utilities
global.createMockFile = (name = 'test.pdf', type = 'application/pdf') => {
  return new File(['mock content'], name, { type });
};

global.createMockInvoice = (overrides = {}) => ({
  id: 'test-invoice-1',
  fileName: 'test-invoice.pdf',
  kind: 'vat',
  number: 'INV-2025-001',
  seller_name: 'Test Company',
  buyer_name: 'Customer Company',
  total_gross: '1230.00',
  currency: 'PLN',
  issue_date: '2025-01-27',
  ...overrides
});
```

---

## **🎭 PLAYWRIGHT E2E CONFIGURATION**

### **playwright.config.js**
```javascript
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './tests/e2e',
  
  // Test execution settings
  timeout: 30000,
  expect: { timeout: 5000 },
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  
  // Reporter configuration
  reporter: [
    ['html'],
    ['json', { outputFile: 'test-results/results.json' }],
    ['junit', { outputFile: 'test-results/results.xml' }]
  ],
  
  // Global test settings
  use: {
    baseURL: 'chrome-extension://test-extension-id',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure'
  },
  
  // Project configurations
  projects: [
    {
      name: 'chrome-extension',
      use: {
        ...devices['Desktop Chrome'],
        launchOptions: {
          args: [
            '--disable-extensions-except=./dist',
            '--load-extension=./dist',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor'
          ]
        }
      }
    },
    
    {
      name: 'chrome-extension-headless',
      use: {
        ...devices['Desktop Chrome'],
        headless: true,
        launchOptions: {
          args: [
            '--disable-extensions-except=./dist',
            '--load-extension=./dist',
            '--disable-web-security',
            '--headless=new'
          ]
        }
      }
    }
  ],
  
  // Output directory
  outputDir: 'test-results/',
  
  // Global setup and teardown
  globalSetup: require.resolve('./tests/e2e/global-setup.js'),
  globalTeardown: require.resolve('./tests/e2e/global-teardown.js')
});
```

### **tests/e2e/global-setup.js**
```javascript
import { chromium } from '@playwright/test';
import path from 'path';

async function globalSetup() {
  // Build extension before testing
  const { execSync } = require('child_process');
  console.log('Building extension for E2E tests...');
  execSync('npm run build', { stdio: 'inherit' });
  
  // Launch browser with extension
  const browser = await chromium.launch({
    headless: false,
    args: [
      '--disable-extensions-except=' + path.resolve('./dist'),
      '--load-extension=' + path.resolve('./dist')
    ]
  });
  
  const context = await browser.newContext();
  const page = await context.newPage();
  
  // Get extension ID
  await page.goto('chrome://extensions/');
  const extensionId = await page.evaluate(() => {
    const extensions = document.querySelectorAll('extensions-item');
    for (const ext of extensions) {
      if (ext.shadowRoot.querySelector('#name').textContent.includes('MVAT')) {
        return ext.id;
      }
    }
    return null;
  });
  
  // Store extension ID for tests
  process.env.EXTENSION_ID = extensionId;
  
  await browser.close();
  console.log(`Extension ID: ${extensionId}`);
}

export default globalSetup;
```

---

## **🐍 SELENIUM VISUAL TESTING CONFIGURATION**

### **tests/visual/selenium_config.py**
```python
import os
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

class SeleniumConfig:
    def __init__(self):
        self.extension_path = os.path.abspath('./dist')
        self.screenshots_dir = './tests/visual/screenshots'
        self.baselines_dir = './tests/visual/baselines'
        self.reports_dir = './tests/visual/reports'
        
        # Create directories if they don't exist
        os.makedirs(self.screenshots_dir, exist_ok=True)
        os.makedirs(self.baselines_dir, exist_ok=True)
        os.makedirs(self.reports_dir, exist_ok=True)
    
    def create_driver(self, headless=False):
        """Create Chrome WebDriver with extension loaded"""
        chrome_options = Options()
        
        # Load extension
        chrome_options.add_argument(f'--load-extension={self.extension_path}')
        chrome_options.add_argument('--disable-extensions-except=' + self.extension_path)
        
        # Additional options
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--disable-features=VizDisplayCompositor')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        if headless:
            chrome_options.add_argument('--headless=new')
        
        # Set window size for consistent screenshots
        chrome_options.add_argument('--window-size=1280,720')
        
        # Create service
        service = Service(ChromeDriverManager().install())
        
        # Create driver
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.set_window_size(1280, 720)
        
        return driver
    
    def get_extension_id(self, driver):
        """Get the extension ID from chrome://extensions/"""
        driver.get('chrome://extensions/')
        
        # Execute JavaScript to find extension ID
        extension_id = driver.execute_script("""
            const extensions = document.querySelectorAll('extensions-item');
            for (const ext of extensions) {
                const nameElement = ext.shadowRoot.querySelector('#name');
                if (nameElement && nameElement.textContent.includes('MVAT')) {
                    return ext.id;
                }
            }
            return null;
        """)
        
        return extension_id
```

### **tests/visual/visual_test_runner.py**
```python
import unittest
import time
from PIL import Image, ImageChops
import numpy as np
from selenium_config import SeleniumConfig

class VisualTestRunner(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls.config = SeleniumConfig()
        cls.driver = cls.config.create_driver()
        cls.extension_id = cls.config.get_extension_id(cls.driver)
        
        if not cls.extension_id:
            raise Exception("Could not find MVAT extension")
    
    @classmethod
    def tearDownClass(cls):
        cls.driver.quit()
    
    def take_screenshot(self, name, element_selector=None):
        """Take screenshot of full page or specific element"""
        screenshot_path = f'{self.config.screenshots_dir}/{name}.png'
        
        if element_selector:
            element = self.driver.find_element('css selector', element_selector)
            element.screenshot(screenshot_path)
        else:
            self.driver.save_screenshot(screenshot_path)
        
        return screenshot_path
    
    def compare_screenshots(self, current_path, baseline_path, threshold=0.95):
        """Compare current screenshot with baseline"""
        try:
            current = Image.open(current_path)
            baseline = Image.open(baseline_path)
            
            # Ensure same size
            if current.size != baseline.size:
                current = current.resize(baseline.size)
            
            # Calculate difference
            diff = ImageChops.difference(current, baseline)
            
            # Convert to numpy array for analysis
            diff_array = np.array(diff)
            
            # Calculate similarity percentage
            total_pixels = diff_array.size
            different_pixels = np.count_nonzero(diff_array)
            similarity = 1 - (different_pixels / total_pixels)
            
            return similarity >= threshold
            
        except FileNotFoundError:
            # If baseline doesn't exist, save current as baseline
            current = Image.open(current_path)
            current.save(baseline_path)
            return True
    
    def test_popup_layout(self):
        """Test popup window layout"""
        popup_url = f'chrome-extension://{self.extension_id}/popup.html'
        self.driver.get(popup_url)
        time.sleep(2)
        
        # Take screenshot
        screenshot_path = self.take_screenshot('popup_layout')
        baseline_path = f'{self.config.baselines_dir}/popup_layout.png'
        
        # Compare with baseline
        is_similar = self.compare_screenshots(screenshot_path, baseline_path)
        self.assertTrue(is_similar, "Popup layout differs from baseline")
    
    def test_responsive_design(self):
        """Test responsive design at different screen sizes"""
        sizes = [(1920, 1080), (1366, 768), (768, 1024)]
        
        for width, height in sizes:
            self.driver.set_window_size(width, height)
            popup_url = f'chrome-extension://{self.extension_id}/popup.html'
            self.driver.get(popup_url)
            time.sleep(1)
            
            screenshot_path = self.take_screenshot(f'responsive_{width}x{height}')
            baseline_path = f'{self.config.baselines_dir}/responsive_{width}x{height}.png'
            
            is_similar = self.compare_screenshots(screenshot_path, baseline_path)
            self.assertTrue(is_similar, f"Responsive design at {width}x{height} differs from baseline")

if __name__ == '__main__':
    unittest.main()
```

---

## **🔄 CI/CD CONFIGURATION**

### **.github/workflows/test.yml**
```yaml
name: Comprehensive Testing

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run unit tests
        run: npm run test:unit -- --coverage --watchAll=false
      
      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info

  functional-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run functional tests
        run: npm run test:functional

  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Install Playwright
        run: npx playwright install --with-deps
      
      - name: Build extension
        run: npm run build
      
      - name: Run E2E tests
        run: npx playwright test
      
      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: playwright-report
          path: playwright-report/

  visual-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'npm'
      
      - name: Install dependencies
        run: |
          npm ci
          pip install -r tests/visual/requirements.txt
      
      - name: Build extension
        run: npm run build
      
      - name: Run visual tests
        run: python tests/visual/visual_test_runner.py
      
      - name: Upload visual test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: visual-test-results
          path: tests/visual/reports/
```

This comprehensive testing configuration ensures that every code change is thoroughly validated across all testing dimensions, maintaining the highest quality standards for the MVAT Chrome Extension.
