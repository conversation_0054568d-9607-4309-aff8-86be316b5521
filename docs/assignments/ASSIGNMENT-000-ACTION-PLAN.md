# 📋 **CORE FUNCTIONALITY ACTION PLAN: MVAT Chrome Extension**

## **🎯 PROJECT OVERVIEW**

**Project Name:** MVAT (Multi-VAT) Chrome Extension
**Purpose:** Parse invoices using PDF.js, Tesseract.js, and DeepSeek API, display data in configurable tables, store in JSON format with caching
**Tech Stack:** TailwindCSS 4.0, Vite, React, Node 22 LTS, Chrome Extension Manifest V3
**Architecture:** Detached popup window, comprehensive testing (E2E, functional, Selenium)

**PRIORITY FOCUS:** Core functionality first - document processing, analysis, and display before monetization features.

---

## **📚 EPIC 1: CORE DOCUMENT PROCESSING**

### **Story 1.1: Project Configuration & Build System**
**Priority:** Critical | **Estimate:** 2 days

#### **Task 1.1.1: Initialize Project Structure**
- [ ] Create `package.json` with Node 22 LTS compatibility
- [ ] Setup Vite configuration for Chrome extension
- [ ] Configure TailwindCSS 4.0 with modern features
- [ ] Create Chrome extension manifest.json (Manifest V3)
- [ ] Setup TypeScript/JSDoc for better development experience

**Files to create:**
- `package.json`
- `vite.config.js`
- `tailwind.config.js`
- `manifest.json`
- `tsconfig.json` (for JSDoc support)

#### **Task 1.1.2: Directory Structure Setup**
- [ ] Create complete directory structure following single-purpose principle
- [ ] Setup proper import/export structure
- [ ] Configure path aliases for clean imports

**Directory Structure:**
```
├── src/
│   ├── popup/                    # Main popup application
│   │   ├── components/           # React components
│   │   │   ├── common/          # Reusable components
│   │   │   ├── tables/          # Table components
│   │   │   ├── settings/        # Settings components
│   │   │   └── upload/          # File upload components
│   │   ├── hooks/               # Custom React hooks
│   │   ├── utils/               # Popup-specific utilities
│   │   ├── styles/              # TailwindCSS styles
│   │   ├── App.jsx              # Main React app
│   │   └── main.jsx             # Entry point
│   ├── background/              # Service worker
│   │   ├── background.js        # Main service worker
│   │   └── utils/               # Background utilities
│   ├── content/                 # Content scripts (if needed)
│   ├── shared/                  # Shared utilities and constants
│   │   ├── constants/           # Application constants
│   │   ├── types/               # Type definitions
│   │   └── utils/               # Shared utilities
│   ├── api/                     # Existing API layer (enhanced)
│   ├── core/                    # Existing core functionality (enhanced)
│   └── components/              # Existing components (enhanced)
├── public/                      # Static assets
│   ├── icons/                   # Extension icons
│   └── popup.html               # Popup HTML template
├── tests/                       # Enhanced testing structure
│   ├── e2e/                     # End-to-end tests
│   ├── functional/              # Functional tests
│   ├── selenium/                # Selenium tests with screenshots
│   └── unit/                    # Unit tests
├── docs/                        # Documentation
└── dist/                        # Build output
```

#### **Task 1.1.3: Comprehensive Testing Framework Setup**
- [ ] Setup Jest for unit testing with React Testing Library
- [ ] Configure Playwright for E2E testing with Chrome extension support
- [ ] Setup Selenium WebDriver for visual regression testing
- [ ] Configure Vitest for component testing integration with Vite
- [ ] Create test utilities and mock factories
- [ ] Setup test coverage reporting with Istanbul
- [ ] Configure pre-commit hooks for automatic test execution
- [ ] Setup continuous testing with file watchers

**Testing Framework Structure:**
```
tests/
├── unit/                     # Jest + React Testing Library
│   ├── setup.js             # Test environment setup
│   ├── utils/                # Test utilities and helpers
│   ├── mocks/                # Mock factories and fixtures
│   └── __tests__/            # Unit test files
├── functional/               # Enhanced functional tests
│   ├── api/                  # API integration tests
│   ├── services/             # Service layer tests
│   └── integration/          # Cross-component tests
├── e2e/                      # Playwright E2E tests
│   ├── playwright.config.js # Playwright configuration
│   ├── fixtures/             # Test data and files
│   ├── pages/                # Page object models
│   └── scenarios/            # Test scenarios
├── visual/                   # Selenium visual tests
│   ├── selenium_config.py    # Selenium configuration
│   ├── baselines/            # Baseline screenshots
│   ├── reports/              # Visual test reports
│   └── tests/                # Visual regression tests
└── performance/              # Performance testing
    ├── lighthouse/           # Lighthouse audits
    └── load/                 # Load testing scenarios
```

**Files to create:**
- `jest.config.js`
- `playwright.config.js`
- `tests/unit/setup.js`
- `tests/e2e/extension.setup.js`
- `tests/visual/selenium_runner.py`
- `.github/workflows/test.yml` (CI/CD)

#### **Task 1.1.4: Makefile Enhancement with Testing Integration**
- [ ] Update Makefile with Vite commands
- [ ] Add TailwindCSS build commands
- [ ] Add comprehensive testing commands for all test types
- [ ] Add development and production build targets
- [ ] Integrate pre-commit testing hooks
- [ ] Add test coverage reporting commands
- [ ] Add visual regression testing commands
- [ ] Add performance testing commands

**Enhanced Makefile Commands:**
```makefile
# Testing commands
test-all: test-unit test-functional test-e2e test-visual
test-unit:
	npm run test:unit -- --coverage --watchAll=false
test-functional:
	node tests/test-runner.js && npm run test:functional
test-e2e:
	npx playwright test --project=chrome-extension
test-visual:
	python tests/visual/selenium_runner.py
test-watch:
	npm run test:unit -- --watch
test-coverage:
	npm run test:unit -- --coverage --coverageReporters=html lcov text

# Pre-commit testing
pre-commit: lint test-unit test-functional
	@echo "✅ Pre-commit checks passed"

# Continuous testing
test-ci: test-all
	@echo "🚀 CI testing complete"
```

### **Story 1.2: Testing Framework Implementation**
**Priority:** Critical | **Estimate:** 3 days

#### **Task 1.2.1: Jest Unit Testing Setup**
- [ ] Install and configure Jest with React Testing Library
- [ ] Setup test environment with jsdom
- [ ] Configure module path mapping for clean imports
- [ ] Create test utilities and custom render functions
- [ ] Setup coverage thresholds and reporting
- [ ] Create mock factories for common objects

**Classes/Functions to implement:**
- `TestUtils` class with helper methods
- `MockFactory` for creating test data
- `CustomRender` function with providers
- `setupTests.js` for global test configuration

**Files to create:**
- `jest.config.js`
- `tests/unit/setup.js`
- `tests/unit/utils/testUtils.js`
- `tests/unit/mocks/mockFactory.js`

#### **Task 1.2.2: Playwright E2E Testing Setup**
- [ ] Install and configure Playwright
- [ ] Setup Chrome extension testing configuration
- [ ] Create page object models for extension pages
- [ ] Implement test fixtures for sample documents
- [ ] Setup screenshot and video recording
- [ ] Create test scenarios for user workflows

**Classes/Functions to implement:**
- `ExtensionPage` class for popup interactions
- `DocumentUploadPage` class for file upload testing
- `SettingsPage` class for settings management
- `TestFixtures` for sample data

**Files to create:**
- `playwright.config.js`
- `tests/e2e/pages/ExtensionPage.js`
- `tests/e2e/fixtures/sampleDocuments.js`
- `tests/e2e/scenarios/documentProcessing.spec.js`

#### **Task 1.2.3: Selenium Visual Testing Setup**
- [ ] Install Selenium WebDriver with Python
- [ ] Configure Chrome extension loading in Selenium
- [ ] Setup screenshot capture and comparison
- [ ] Create baseline screenshots for components
- [ ] Implement visual regression testing
- [ ] Setup test reporting with images

**Classes/Functions to implement:**
- `VisualTestRunner` class
- `ScreenshotComparator` class
- `ExtensionTestCase` base class
- `ReportGenerator` for visual test reports

**Files to create:**
- `tests/visual/selenium_config.py`
- `tests/visual/visual_test_runner.py`
- `tests/visual/screenshot_comparator.py`
- `tests/visual/test_cases/popup_visual_tests.py`

#### **Task 1.2.4: Functional Testing Enhancement**
- [ ] Enhance existing test-runner.js with better reporting
- [ ] Add API integration testing
- [ ] Create service layer testing
- [ ] Implement cross-component integration tests
- [ ] Add performance benchmarking
- [ ] Setup test data management

**Classes/Functions to implement:**
- `APITestSuite` class
- `ServiceTestSuite` class
- `IntegrationTestSuite` class
- `PerformanceBenchmark` class

**Files to create:**
- `tests/functional/api/apiTestSuite.js`
- `tests/functional/services/serviceTests.js`
- `tests/functional/integration/crossComponentTests.js`

#### **Task 1.2.5: CI/CD Testing Integration**
- [ ] Setup GitHub Actions workflow for testing
- [ ] Configure test execution on pull requests
- [ ] Setup test result reporting and notifications
- [ ] Implement test parallelization
- [ ] Configure test artifact storage
- [ ] Setup automated visual regression testing

**Files to create:**
- `.github/workflows/test.yml`
- `.github/workflows/visual-regression.yml`
- `scripts/test-setup.sh`
- `scripts/test-cleanup.sh`

#### **Task 1.2.6: Pre-commit Testing Hooks**
- [ ] Setup Husky for Git hooks
- [ ] Configure pre-commit testing pipeline
- [ ] Add lint-staged for incremental testing
- [ ] Setup commit message validation
- [ ] Configure automatic test execution on file changes
- [ ] Add test failure prevention for commits

**Files to create:**
- `.husky/pre-commit`
- `.lintstagedrc.js`
- `scripts/pre-commit-tests.sh`

---

## **📚 EPIC 2: CORE EXTENSION INFRASTRUCTURE**

### **Story 2.1: Chrome Extension Foundation**
**Priority:** Critical | **Estimate:** 3 days

#### **Task 2.1.1: Manifest V3 Configuration**
- [ ] Create manifest.json with proper permissions
- [ ] Configure service worker (background script)
- [ ] Setup popup window configuration for detached mode
- [ ] Configure content security policy

**Checklist:**
- [ ] Permissions: storage, activeTab, scripting, offscreen
- [ ] Service worker registration
- [ ] Popup action configuration
- [ ] Icons and metadata

#### **Task 2.1.2: Service Worker Implementation**
- [ ] Create background service worker
- [ ] Implement extension lifecycle management
- [ ] Setup message passing between popup and background
- [ ] Implement storage management

**Files to create:**
- `src/background/background.js`
- `src/background/utils/messageHandler.js`
- `src/background/utils/storageManager.js`

#### **Task 2.1.3: Detached Popup Window**
- [ ] Implement detached popup window creation
- [ ] Configure window sizing and positioning
- [ ] Handle window state management
- [ ] Implement window focus and visibility controls

**Classes/Functions:**
- `WindowManager` class
- `createDetachedPopup()` function
- `manageWindowState()` function

### **Story 2.2: React Application Setup**
**Priority:** Critical | **Estimate:** 2 days

#### **Task 2.2.1: React App Foundation**
- [ ] Create main React application structure
- [ ] Setup React Router for navigation
- [ ] Implement context providers for state management
- [ ] Create base layout components

**Files to create:**
- `src/popup/App.jsx`
- `src/popup/main.jsx`
- `src/popup/components/Layout/MainLayout.jsx`
- `src/popup/contexts/AppContext.jsx`

#### **Task 2.2.2: TailwindCSS Integration**
- [ ] Configure TailwindCSS 4.0 with Vite
- [ ] Create design system components
- [ ] Setup responsive design utilities
- [ ] Create theme configuration

**Files to create:**
- `src/popup/styles/globals.css`
- `src/popup/styles/components.css`
- `src/popup/components/common/Button.jsx`
- `src/popup/components/common/Card.jsx`

---

## **📚 EPIC 3: DOCUMENT PROCESSING PIPELINE**

### **Story 3.1: PDF Processing with PDF.js**
**Priority:** High | **Estimate:** 3 days

#### **Task 3.1.1: PDF.js Integration**
- [ ] Install and configure PDF.js
- [ ] Create PDF processing service
- [ ] Implement text extraction from PDF
- [ ] Handle multi-page PDF documents

**Classes/Functions:**
- `PDFProcessorService` class
- `extractTextFromPDF()` function
- `processPDFPages()` function
- `validatePDFFile()` function

#### **Task 3.1.2: PDF Processing Enhancement**
- [ ] Enhance existing PDFProcessor component
- [ ] Add progress tracking for large files
- [ ] Implement error handling and recovery
- [ ] Add file validation and security checks

**Files to enhance:**
- `src/components/processors/PDFProcessor.js`

### **Story 3.2: OCR Processing with Tesseract.js**
**Priority:** High | **Estimate:** 3 days

#### **Task 3.2.1: Tesseract.js Integration**
- [ ] Install and configure Tesseract.js
- [ ] Create OCR processing service
- [ ] Implement image-to-text conversion
- [ ] Add language detection and support

**Classes/Functions:**
- `OCRProcessorService` class
- `extractTextFromImage()` function
- `detectLanguage()` function
- `preprocessImage()` function

#### **Task 3.2.2: OCR Processing Enhancement**
- [ ] Enhance existing OCRProcessor component
- [ ] Add image preprocessing for better accuracy
- [ ] Implement confidence scoring
- [ ] Add support for multiple languages (Polish focus)

**Files to enhance:**
- `src/components/processors/OCRProcessor.js`

### **Story 3.3: AI Processing with OpenAI**
**Priority:** High | **Estimate:** 2 days

#### **Task 3.3.1: OpenAI Integration**
- [ ] Install OpenAI JavaScript SDK
- [ ] Create OpenAI processing service
- [ ] Implement structured data extraction
- [ ] Add field validation and correction

**Classes/Functions:**
- `OpenAIProcessorService` class
- `extractStructuredData()` function
- `validateExtractedFields()` function
- `correctFieldValues()` function

#### **Task 3.3.2: DeepSeek API Enhancement**
- [ ] Enhance existing DeepSeekAPI for OpenAI compatibility
- [ ] Add fallback mechanisms
- [ ] Implement response caching
- [ ] Add rate limiting and error handling

**Files to enhance:**
- `src/api/DeepSeekAPI.js`

---

## **📚 EPIC 4: DATA MANAGEMENT & STORAGE**

### **Story 4.1: JSON Storage System**
**Priority:** High | **Estimate:** 2 days

#### **Task 4.1.1: Storage Architecture**
- [ ] Design JSON storage schema
- [ ] Implement Chrome storage API integration
- [ ] Create data versioning system
- [ ] Add data migration utilities

**Classes/Functions:**
- `StorageService` class (enhance existing)
- `DataMigrationService` class
- `SchemaValidator` class

#### **Task 4.1.2: Caching System**
- [ ] Implement intelligent caching for processed documents
- [ ] Add cache invalidation strategies
- [ ] Create cache size management
- [ ] Add cache performance monitoring

**Files to enhance:**
- `src/api/StorageAPI.js`

### **Story 4.2: User Settings Management**
**Priority:** Medium | **Estimate:** 2 days

#### **Task 4.2.1: Settings Schema**
- [ ] Define settings data structure
- [ ] Implement settings validation
- [ ] Create default settings
- [ ] Add settings import/export

**Settings Structure:**
```javascript
{
  company: {
    name: string,
    taxId: string, // NIP in Poland
    address: string,
    email: string,
    phone: string
  },
  display: {
    groupBy: 'year' | 'quarter' | 'month',
    dateFormat: string,
    currency: string,
    language: string
  },
  processing: {
    ocrLanguage: string,
    aiProvider: 'deepseek' | 'openai',
    autoProcess: boolean,
    cacheEnabled: boolean
  }
}
```

#### **Task 4.2.2: Settings UI Components**
- [ ] Create settings form components
- [ ] Implement form validation
- [ ] Add settings persistence
- [ ] Create settings reset functionality

**Files to create:**
- `src/popup/components/settings/CompanySettings.jsx`
- `src/popup/components/settings/DisplaySettings.jsx`
- `src/popup/components/settings/ProcessingSettings.jsx`

---

## **📚 EPIC 5: USER INTERFACE COMPONENTS**

### **Story 5.1: File Upload Interface**
**Priority:** High | **Estimate:** 2 days

#### **Task 5.1.1: Drag & Drop Upload**
- [ ] Create drag and drop upload component
- [ ] Add file type validation
- [ ] Implement upload progress tracking
- [ ] Add multiple file support

**Classes/Functions:**
- `FileUploadComponent` React component
- `validateFileType()` function
- `handleFileUpload()` function
- `trackUploadProgress()` function

#### **Task 5.1.2: Upload Enhancement**
- [ ] Enhance existing DocumentUploadHandler
- [ ] Add visual feedback for upload states
- [ ] Implement file preview functionality
- [ ] Add batch processing capabilities

**Files to enhance:**
- `src/components/ui/DocumentUploadHandler.js`

### **Story 5.2: Data Tables & Views**
**Priority:** High | **Estimate:** 3 days

#### **Task 5.2.1: Invoice Table Component**
- [ ] Create responsive invoice table
- [ ] Implement sorting and filtering
- [ ] Add pagination for large datasets
- [ ] Create export functionality

**Classes/Functions:**
- `InvoiceTableComponent` React component
- `TableSorter` utility class
- `TableFilter` utility class
- `DataExporter` utility class

#### **Task 5.2.2: Grouping & Views**
- [ ] Implement year/quarter/month grouping
- [ ] Create configurable view options
- [ ] Add summary statistics
- [ ] Implement drill-down functionality

**Files to create:**
- `src/popup/components/tables/InvoiceTable.jsx`
- `src/popup/components/tables/GroupedView.jsx`
- `src/popup/components/tables/SummaryStats.jsx`

#### **Task 5.2.3: Table Enhancement**
- [ ] Enhance existing InvoiceTable component
- [ ] Add real-time updates
- [ ] Implement virtual scrolling for performance
- [ ] Add accessibility features

**Files to enhance:**
- `src/components/ui/InvoiceTable.js`

---

## **🔧 TECHNICAL SPECIFICATIONS**

### **Field Extraction Schema (from FIELDS_DRAFT):**
Based on the existing field definitions, the extension will extract:

**Core Invoice Fields:**
- Document identification: `number`, `kind`, `issue_date`, `sell_date`
- Seller information: `seller_name`, `seller_tax_no`, `seller_address`, `seller_bank_account`
- Buyer information: `buyer_name`, `buyer_tax_no`, `buyer_address`, `buyer_email`
- Financial data: `total_net`, `total_vat`, `total_gross`, `currency`, `payment_to`
- Line items: `positions[]` with product details, quantities, prices, tax rates

**Document Types Supported:**
- VAT invoices (`vat`)
- Proforma invoices (`proforma`) 
- Bills (`bill`)
- Receipts (`receipt`)
- Advance invoices (`advance`)
- Final invoices (`final`)
- Correction invoices (`correction`)
- And 20+ other Polish accounting document types

### **Dependencies to Install:**
```json
{
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.8.0",
    "pdfjs-dist": "^3.11.174",
    "tesseract.js": "^4.1.1",
    "openai": "^4.24.1",
    "@tailwindcss/forms": "^0.5.7",
    "@tailwindcss/typography": "^0.5.10"
  },
  "devDependencies": {
    "vite": "^5.0.0",
    "@vitejs/plugin-react": "^4.2.0",
    "tailwindcss": "^4.0.0",

    // Testing frameworks
    "jest": "^29.7.0",
    "vitest": "^1.2.0",
    "@testing-library/react": "^14.1.0",
    "@testing-library/jest-dom": "^6.2.0",
    "@testing-library/user-event": "^14.5.0",
    "playwright": "^1.40.0",
    "@playwright/test": "^1.40.0",

    // Testing utilities
    "jest-environment-jsdom": "^29.7.0",
    "jest-chrome": "^0.8.0",
    "@types/chrome": "^0.0.254",
    "selenium-webdriver": "^4.16.0",
    "pixelmatch": "^5.3.0",
    "pngjs": "^7.0.0",

    // Code quality and hooks
    "husky": "^8.0.3",
    "lint-staged": "^15.2.0",
    "eslint": "^8.56.0",
    "prettier": "^3.1.1",
    "@typescript-eslint/parser": "^6.18.0",

    // Build and development
    "cross-env": "^7.0.3",
    "rimraf": "^5.0.5",
    "concurrently": "^8.2.2",
    "wait-on": "^7.2.0"
  }
}
```

### **Python Dependencies for Visual Testing:**
```txt
# requirements.txt for Selenium visual testing
selenium==4.16.0
Pillow==10.1.0
pytest==7.4.3
pytest-html==4.1.1
webdriver-manager==4.0.1
opencv-python==********
numpy==1.25.2
matplotlib==3.8.2
```

### **Build Configuration:**
- **Vite:** Module bundler with Chrome extension plugin
- **TailwindCSS 4.0:** Utility-first CSS framework
- **Manifest V3:** Latest Chrome extension standard
- **Service Worker:** Background processing
- **Detached Popup:** Independent window mode

---

## **📋 IMPLEMENTATION CHECKPOINTS**

### **Checkpoint 1: Foundation & Testing Complete**
- [ ] Project structure created with proper directory organization
- [ ] Build system working (Vite + TailwindCSS 4.0)
- [ ] Basic Chrome extension loads and manifest V3 configured
- [ ] **Comprehensive testing framework setup:**
  - [ ] Jest unit testing with React Testing Library configured
  - [ ] Playwright E2E testing with Chrome extension support
  - [ ] Selenium visual regression testing setup
  - [ ] Pre-commit hooks and CI/CD pipeline configured
  - [ ] Test coverage reporting (95% target)
  - [ ] All test types running successfully

### **Checkpoint 2: Core Processing & Testing Ready**
- [ ] PDF.js integration working with comprehensive tests
- [ ] Tesseract.js OCR functional with accuracy validation
- [ ] OpenAI/DeepSeek API connected with fallback mechanisms
- [ ] Basic document processing pipeline with error handling
- [ ] **Testing validation:**
  - [ ] Unit tests for all processing services (95% coverage)
  - [ ] Functional tests for API integrations
  - [ ] E2E tests for document processing workflow
  - [ ] Performance benchmarks established

### **Checkpoint 3: UI Components & Visual Testing Functional**
- [ ] React app renders in popup with proper styling
- [ ] File upload working with drag-and-drop support
- [ ] Basic table display with sorting and filtering
- [ ] Settings panel functional with validation
- [ ] **UI testing validation:**
  - [ ] Unit tests for all React components
  - [ ] Visual regression tests with baseline screenshots
  - [ ] Responsive design testing across screen sizes
  - [ ] Accessibility testing compliance

### **Checkpoint 4: Full Integration & E2E Testing**
- [ ] End-to-end document processing workflow complete
- [ ] Data storage and retrieval with caching system
- [ ] All UI components connected and communicating
- [ ] **Comprehensive testing passing:**
  - [ ] All unit tests passing (95%+ coverage)
  - [ ] All functional tests passing
  - [ ] All E2E scenarios passing
  - [ ] Visual regression tests passing
  - [ ] Performance tests meeting benchmarks

### **Checkpoint 5: Production Ready & Quality Assured**
- [ ] Performance optimized and benchmarked
- [ ] Error handling robust with user-friendly messages
- [ ] Documentation complete and up-to-date
- [ ] Extension packaged for distribution
- [ ] **Quality assurance complete:**
  - [ ] Security audit passed
  - [ ] Cross-browser compatibility verified
  - [ ] Load testing completed
  - [ ] User acceptance testing completed
  - [ ] Final test suite execution (100% pass rate)

---

## **🎯 SUCCESS CRITERIA**

1. **Functional Requirements:**
   - Successfully parse PDF invoices using PDF.js
   - Extract text from images using Tesseract.js
   - Structure data using OpenAI/DeepSeek APIs
   - Display data in configurable year/quarter/month views
   - Store all data in JSON format with caching
   - Manage user company settings (name, NIP, address, email, phone)

2. **Technical Requirements:**
   - Chrome extension opens as detached popup window
   - Built with TailwindCSS 4.0, Vite, React, Node 22 LTS
   - All operations accessible via Makefile
   - Comprehensive test coverage (unit, functional, e2e, Selenium)
   - Single-purpose files following DRY principles
   - 2025 JS/UI/UX best practices

3. **Quality Requirements:**
   - 90%+ test coverage
   - All tests passing
   - Performance optimized for large datasets
   - Accessible UI components
   - Comprehensive error handling
   - Complete documentation

**Next Steps:** Begin with Epic 1 - Project Foundation & Setup
