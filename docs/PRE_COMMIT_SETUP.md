# 🔒 **PRE-COMMIT SETUP & QUALITY GATES**

## **📋 OVERVIEW**

This document describes the comprehensive pre-commit hook system that ensures all Epic, Story, Task, and Subtask completions meet the highest quality standards before being committed to the repository.

---

## **🎯 QUALITY GATE PHILOSOPHY**

### **Zero-Failure Policy**
- **All critical tests must pass** before any commit is allowed
- **No exceptions** for Epic, Story, Task, or Subtask completion
- **Comprehensive coverage** including unit, functional, E2E, and visual tests
- **Automated enforcement** through pre-commit hooks

### **Epic/Story/Task Completion Requirements**
Every completion must include:
- ✅ **Epic Reference:** `Epic: B1-SUBSCRIPTION-MONETIZATION`
- ✅ **Story Reference:** `Story: B1.1-SUBSCRIPTION-TIER-MANAGEMENT`
- ✅ **Task Reference:** `Task: B1.1.1-SUBSCRIPTION-DATA-MODELS`
- ✅ **All Tests Passing:** 100% success rate required
- ✅ **Quality Gates:** All automated checks must pass

---

## **🔧 SETUP INSTRUCTIONS**

### **1. Install Dependencies**
```bash
# Install all development dependencies
npm install

# Install Husky hooks
npm run prepare
```

### **2. Verify Pre-commit Hook**
```bash
# Check if pre-commit hook is executable
ls -la .husky/pre-commit

# Should show: -rwxr-xr-x (executable permissions)
```

### **3. Configure Git Commit Template**
```bash
# Set commit message template (already configured)
git config commit.template .gitmessage

# Verify configuration
git config --get commit.template
```

### **4. Test Pre-commit Hook**
```bash
# Make a test change
echo "// Test comment" >> src/test.js

# Stage the change
git add src/test.js

# Attempt commit (will trigger pre-commit hook)
git commit -m "test: verify pre-commit hook functionality"
```

---

## **🚦 QUALITY GATES BREAKDOWN**

### **Step 1: Code Formatting & Linting**
```bash
# Commands executed:
npx lint-staged          # Format staged files
npm run lint             # ESLint all files
npm run format:check     # Prettier format verification
```

**Failure Conditions:**
- ESLint errors or warnings
- Code formatting violations
- Staged files not properly formatted

### **Step 2: Type Checking**
```bash
# Commands executed:
npm run typecheck        # TypeScript type checking
```

**Failure Conditions:**
- TypeScript compilation errors
- Type definition issues
- Missing type annotations

### **Step 3: Unit Tests**
```bash
# Commands executed:
npm run test:unit        # Vitest with coverage
```

**Failure Conditions:**
- Any unit test failures
- Coverage below 90% threshold
- Test timeouts or errors

### **Step 4: Functional Tests**
```bash
# Commands executed:
npm run test:functional  # API and integration tests
```

**Failure Conditions:**
- API integration failures
- Service layer test failures
- Mock configuration issues

### **Step 5: End-to-End Tests**
```bash
# Commands executed:
npm run test:e2e         # Playwright browser tests
```

**Failure Conditions:**
- Browser automation failures
- UI interaction test failures
- Extension loading issues

### **Step 6: Visual Regression Tests**
```bash
# Commands executed:
npm run test:visual      # Selenium visual tests
```

**Failure Conditions:**
- Visual regression detected
- Screenshot comparison failures
- UI layout inconsistencies

### **Step 7: Build Verification**
```bash
# Commands executed:
npm run build           # Production build
```

**Failure Conditions:**
- Build compilation errors
- Asset generation failures
- Bundle size violations

### **Step 8: Security & Quality Analysis**
```bash
# Commands executed:
npm audit --audit-level=high  # Security vulnerability check
```

**Warning Conditions:**
- High-severity vulnerabilities
- Outdated dependencies
- Security policy violations

---

## **📝 COMMIT MESSAGE REQUIREMENTS**

### **Required Format**
```
type(scope): description

[optional body]

Epic: EPIC-ID
Story: STORY-ID
Task: TASK-ID
```

### **Epic/Story/Task Completion Examples**

#### **Epic Completion**
```
feat(subscription): complete subscription monetization system

Implement comprehensive subscription system with tier management,
payment processing, usage monitoring, and billing management.
All stories completed with full test coverage.

Epic: B1-SUBSCRIPTION-MONETIZATION
Business Value: Foundation for freemium model and revenue generation
```

#### **Story Completion**
```
feat(subscription): complete subscription tier management

Implement subscription tier validation, feature gating, usage
tracking, and upgrade workflows. All tasks completed with
comprehensive testing.

Epic: B1-SUBSCRIPTION-MONETIZATION
Story: B1.1-SUBSCRIPTION-TIER-MANAGEMENT
Business Value: Clear upgrade path and revenue optimization
```

#### **Task Completion**
```
feat(subscription): implement subscription data models

Create SubscriptionTier, UserSubscription, and UsageTracker
models with validation, constraints, and audit capabilities.
All subtasks completed.

Epic: B1-SUBSCRIPTION-MONETIZATION
Story: B1.1-SUBSCRIPTION-TIER-MANAGEMENT
Task: B1.1.1-SUBSCRIPTION-DATA-MODELS
```

#### **Subtask Completion**
```
feat(subscription): create SubscriptionTier model

Implement SubscriptionTier model with tier configurations,
feature validation methods, and usage limit checking.

Epic: B1-SUBSCRIPTION-MONETIZATION
Story: B1.1-SUBSCRIPTION-TIER-MANAGEMENT
Task: B1.1.1-SUBSCRIPTION-DATA-MODELS
Subtask: B1.1.1.1-CREATE-SUBSCRIPTION-TIER-MODEL
```

---

## **🔄 TESTING COMMANDS**

### **Individual Test Suites**
```bash
# Unit tests only
npm run test:unit

# Functional tests only
npm run test:functional

# E2E tests only
npm run test:e2e

# Visual tests only
npm run test:visual

# All tests
npm run test:all
```

### **Quality Analysis**
```bash
# Linting
npm run lint

# Formatting
npm run format
npm run format:check

# Type checking
npm run typecheck

# All analysis
npm run analyze
```

### **Coverage Reports**
```bash
# Generate coverage report
npm run test:coverage

# View coverage report
open coverage/index.html
```

---

## **🚨 TROUBLESHOOTING**

### **Pre-commit Hook Not Running**
```bash
# Reinstall Husky
rm -rf .husky
npm run prepare

# Make hook executable
chmod +x .husky/pre-commit
```

### **Tests Failing**
```bash
# Run tests individually to identify issues
npm run test:unit
npm run test:functional
npm run test:e2e
npm run test:visual

# Check specific test output
npm run test:unit -- --reporter=verbose
```

### **Linting Errors**
```bash
# Auto-fix linting issues
npm run lint:fix

# Check specific files
npx eslint src/path/to/file.js --fix
```

### **Formatting Issues**
```bash
# Auto-format all files
npm run format

# Check specific files
npx prettier --write src/path/to/file.js
```

### **TypeScript Errors**
```bash
# Check TypeScript errors
npm run typecheck

# Check specific files
npx tsc --noEmit src/path/to/file.ts
```

### **Build Failures**
```bash
# Clean build
rm -rf dist/
npm run build

# Debug build issues
npm run build -- --debug
```

---

## **⚡ EMERGENCY BYPASS**

### **When to Use**
- **Critical production hotfixes only**
- **Emergency security patches**
- **Infrastructure failures blocking development**

### **How to Bypass**
```bash
# Bypass pre-commit hook (use sparingly)
git commit --no-verify -m "emergency: critical production fix"

# Must include justification in commit message
git commit --no-verify -m "emergency: fix critical security vulnerability

Bypassing pre-commit due to production outage.
Will run full test suite in follow-up commit.

Epic: EMERGENCY-FIX
Justification: Production system down, immediate fix required"
```

### **Post-Bypass Requirements**
1. **Immediate follow-up commit** with full test suite
2. **Incident report** documenting the bypass reason
3. **Process review** to prevent future bypasses
4. **Team notification** of the emergency bypass

---

## **📊 QUALITY METRICS**

### **Success Criteria**
- **100% test pass rate** for all commits
- **90%+ code coverage** maintained
- **Zero critical vulnerabilities** in dependencies
- **Zero linting errors** in codebase
- **100% TypeScript compliance**

### **Monitoring**
- **Daily quality reports** generated automatically
- **Weekly quality reviews** in team meetings
- **Monthly quality metrics** tracked and reported
- **Quarterly quality improvements** planned and implemented

---

## **🎯 BENEFITS**

### **Code Quality**
- **Consistent code style** across the entire codebase
- **Early bug detection** before code reaches main branch
- **Automated quality enforcement** without manual oversight

### **Team Productivity**
- **Reduced debugging time** through early error detection
- **Faster code reviews** with pre-validated code quality
- **Increased confidence** in code deployments

### **Business Value**
- **Reduced production bugs** through comprehensive testing
- **Faster feature delivery** with automated quality gates
- **Lower maintenance costs** through high-quality code

This pre-commit system ensures that every Epic, Story, Task, and Subtask completion meets the highest standards of quality, reliability, and maintainability.
