# 📋 **MVAT Chrome Extension - Source Code Analysis**

## **📊 OVERVIEW**

**Analysis Date:** 2025-01-28  
**Total Files Analyzed:** 129  
**Assignment:** ASSIGNMENT-052-SOURCE-CODE-ANALYSIS  
**Epic:** EPIC-005 - Enhanced AI Analysis & RAG Integration  

---

## **🏗️ DIRECTORY STRUCTURE**

```
src/
├── api/                    # External API integrations (3 files)
├── background/             # Chrome extension background scripts (1 file)
├── components/             # Reusable UI components (31 files)
├── config/                 # Configuration files (1 file)
├── core/                   # Core business logic (15 files)
├── hooks/                  # React hooks (1 file)
├── popup/                  # Main popup application (20 files)
├── sandbox/                # Sandboxed execution environment (2 files)
├── services/               # Business services and utilities (23 files)
├── templates/              # Data templates (1 file)
└── utils/                  # Utility functions and helpers (36 files)
```

**Total:** 33 directories, 129 files

---

## **📁 API LAYER ANALYSIS**

### **src/api/DeepSeekAPI.js**
**Purpose:** DeepSeek AI API integration for document analysis and text processing  
**Key Functions:**
- `callAPI(prompt, apiKey, options)` - Main API call method with retry logic
- `validateResponse(response)` - Response validation and error handling
- `buildRequestPayload(prompt, options)` - Request payload construction
- `handleRateLimit(response)` - Rate limiting and backoff strategy

**Dependencies:** None (standalone API client)  
**Implementation:** Promise-based with async/await, comprehensive error handling, rate limiting support  
**Lines:** ~150 lines  

### **src/api/FakturowniaAPI.js**
**Purpose:** Fakturownia accounting system API integration for invoice management  
**Key Functions:**
- `createInvoice(invoiceData)` - Create new invoice in Fakturownia
- `updateInvoice(id, data)` - Update existing invoice
- `getInvoice(id)` - Retrieve invoice by ID
- `validateCredentials()` - API credentials validation

**Dependencies:** None (standalone API client)  
**Implementation:** RESTful API client with authentication, CRUD operations  
**Lines:** ~120 lines  

### **src/api/StorageAPI.js**
**Purpose:** Chrome extension storage API wrapper for data persistence  
**Key Functions:**
- `set(data)` - Store data in Chrome storage
- `get(keys)` - Retrieve data from Chrome storage
- `remove(keys)` - Remove data from storage
- `clear()` - Clear all storage data
- `onChanged(callback)` - Listen for storage changes

**Dependencies:** Chrome extension APIs  
**Implementation:** Promise-based wrapper around chrome.storage API  
**Lines:** ~80 lines  

---

## **🔧 BACKGROUND SCRIPTS ANALYSIS**

### **src/background/background.js**
**Purpose:** Chrome extension background service worker for persistent functionality  
**Key Functions:**
- Extension lifecycle management
- Message passing between content and popup
- Background task coordination
- Storage event handling

**Dependencies:** Chrome extension APIs, StorageAPI  
**Implementation:** Service worker pattern, event-driven architecture  
**Lines:** ~100 lines  

---

## **🎨 COMPONENTS ANALYSIS**

### **Data Components (src/components/data/)**

#### **SummaryCard.jsx**
**Purpose:** Display summary statistics and metrics in card format  
**Key Functions:**
- `SummaryCard({ title, value, icon, trend })` - Main component
- Trend indicator display
- Responsive card layout

**Dependencies:** React, TrendIndicator component  
**Implementation:** Functional React component with props  
**Lines:** ~60 lines  

#### **TrendIndicator.jsx**
**Purpose:** Visual indicator for data trends (up/down/neutral)  
**Key Functions:**
- `TrendIndicator({ value, type })` - Trend visualization
- Color-coded trend display
- Percentage change calculation

**Dependencies:** React  
**Implementation:** Pure functional component  
**Lines:** ~40 lines  

### **File Validation (src/components/)**

#### **FileValidationFeedback.jsx**
**Purpose:** User feedback for file validation results  
**Key Functions:**
- `FileValidationFeedback({ validationResults })` - Display validation status
- Error message formatting
- Success/warning/error state handling

**Dependencies:** React  
**Implementation:** Functional component with conditional rendering  
**Lines:** ~80 lines  

### **Generators (src/components/generators/)**

#### **PromptGenerator.js**
**Purpose:** Generate AI prompts for document analysis  
**Key Functions:**
- `generateExtractionPrompt(documentType)` - Create extraction prompts
- `generateAnalysisPrompt(content)` - Create analysis prompts
- `generateValidationPrompt(data)` - Create validation prompts
- Template-based prompt construction

**Dependencies:** None (utility class)  
**Implementation:** Class-based prompt generation with templates  
**Lines:** ~200 lines  

### **Processors (src/components/processors/)**

#### **DocumentProcessor.js**
**Purpose:** Main document processing orchestrator  
**Key Functions:**
- `processDocument(file)` - Main processing pipeline
- `extractText(file)` - Text extraction coordination
- `analyzeContent(text)` - Content analysis coordination
- `validateResults(data)` - Result validation

**Dependencies:** PDFProcessor, OCRProcessor, PositionExtractor  
**Implementation:** Pipeline pattern with async processing  
**Lines:** ~250 lines  

#### **OCRProcessor.js**
**Purpose:** Optical Character Recognition using Tesseract.js  
**Key Functions:**
- `extractTextFromImage(imageData)` - OCR text extraction
- `preprocessImage(imageData)` - Image preprocessing
- `configureOCR(language)` - OCR configuration
- `handleOCRProgress(progress)` - Progress tracking

**Dependencies:** Tesseract.js, imageUtils  
**Implementation:** Promise-based OCR with progress tracking  
**Lines:** ~180 lines  

#### **PDFProcessor.js**
**Purpose:** PDF document processing using PDF.js  
**Key Functions:**
- `extractTextFromPDF(pdfData)` - PDF text extraction
- `extractMetadata(pdfData)` - PDF metadata extraction
- `renderPDFPages(pdfData)` - PDF page rendering
- `handlePDFProgress(progress)` - Progress tracking

**Dependencies:** PDF.js, pdfUtils  
**Implementation:** Promise-based PDF processing with streaming  
**Lines:** ~220 lines  

#### **PositionExtractor.js**
**Purpose:** Extract positional information from documents  
**Key Functions:**
- `extractFieldPositions(text, fields)` - Field position detection
- `calculateBoundingBoxes(positions)` - Bounding box calculation
- `mapTextToCoordinates(text, coordinates)` - Text-coordinate mapping
- `validatePositions(positions)` - Position validation

**Dependencies:** None (utility class)  
**Implementation:** Geometric calculation and text analysis  
**Lines:** ~160 lines  

### **Related Documents (src/components/)**

#### **RelatedDocuments.jsx**
**Purpose:** Display related documents based on RAG similarity  
**Key Functions:**
- `RelatedDocuments({ documentId, similarities })` - Main component
- `renderSimilarityScore(score)` - Similarity score display
- `handleDocumentClick(document)` - Document selection handling
- Similarity-based document recommendations

**Dependencies:** React, DocumentRelationshipService  
**Implementation:** Functional component with similarity ranking  
**Lines:** ~120 lines  

---

## **⚙️ SETTINGS COMPONENTS ANALYSIS**

### **Business Configuration (src/components/settings/)**

#### **BusinessConfiguration.jsx**
**Purpose:** Business-specific configuration settings  
**Key Functions:**
- `BusinessConfiguration({ config, onChange })` - Main configuration form
- Business rule configuration
- Validation rule setup
- Industry-specific settings

**Dependencies:** React, validation utilities  
**Implementation:** Form-based configuration with validation  
**Lines:** ~180 lines  

#### **CompanyInformation.jsx**
**Purpose:** Company profile information management  
**Key Functions:**
- `CompanyInformation({ company, onUpdate })` - Company details form
- Company data validation
- Logo upload handling
- Contact information management

**Dependencies:** React, LogoUpload component  
**Implementation:** Form component with file upload  
**Lines:** ~150 lines  

#### **CompanyProfileSettings.jsx**
**Purpose:** Comprehensive company profile settings  
**Key Functions:**
- `CompanyProfileSettings()` - Main profile settings component
- Profile data management
- Settings persistence
- Validation and error handling

**Dependencies:** React, useSettings hook, CompanyInformation  
**Implementation:** Container component with state management  
**Lines:** ~200 lines  

#### **DataManagementTab.jsx**
**Purpose:** Data management and export functionality  
**Key Functions:**
- `DataManagementTab()` - Data management interface
- Export functionality
- Data cleanup options
- Backup and restore features

**Dependencies:** React, DataManagementService  
**Implementation:** Tab component with data operations  
**Lines:** ~160 lines  

#### **DisplayPreferences.jsx**
**Purpose:** User interface display preferences  
**Key Functions:**
- `DisplayPreferences({ preferences, onChange })` - Display settings form
- Theme selection
- Language preferences
- Layout customization

**Dependencies:** React, ThemeSelector, LanguageSelector  
**Implementation:** Preference form with real-time preview  
**Lines:** ~140 lines  

#### **EnvironmentSettings.jsx**
**Purpose:** Environment configuration and variable management  
**Key Functions:**
- `EnvironmentSettings()` - Environment configuration interface
- Environment variable display
- Configuration source selection
- Settings validation and loading

**Dependencies:** React, SettingsSourceSelector, useSettings  
**Implementation:** Complex settings management with multiple sources  
**Lines:** ~300+ lines  

#### **LanguageSelector.jsx**
**Purpose:** Language selection component  
**Key Functions:**
- `LanguageSelector({ selectedLanguage, onLanguageChange })` - Language picker
- Supported language display
- Language validation
- Localization support

**Dependencies:** React, language configuration  
**Implementation:** Dropdown selector with validation  
**Lines:** ~80 lines  

#### **LogoUpload.jsx**
**Purpose:** Company logo upload and management  
**Key Functions:**
- `LogoUpload({ currentLogo, onLogoChange })` - Logo upload interface
- Image validation and processing
- Preview functionality
- File size and format validation

**Dependencies:** React, file validation utilities  
**Implementation:** File upload component with preview  
**Lines:** ~120 lines  

#### **ProcessingPreferences.jsx**
**Purpose:** Document processing preferences and settings  
**Key Functions:**
- `ProcessingPreferences({ preferences, onChange })` - Processing settings form
- OCR language selection
- AI provider configuration
- Processing pipeline customization

**Dependencies:** React, processing configuration  
**Implementation:** Form component with processing options  
**Lines:** ~160 lines  

#### **SettingsSourceSelector.jsx**
**Purpose:** Configuration source selection and management  
**Key Functions:**
- `SettingsSourceSelector({ onConfigurationLoaded, onError })` - Source selector
- Multiple configuration source support
- Source testing and validation
- Configuration loading from various sources

**Dependencies:** React, ConfigurationSourceManager  
**Implementation:** Complex source management with testing capabilities  
**Lines:** ~250 lines  

#### **ThemeSelector.jsx**
**Purpose:** UI theme selection component  
**Key Functions:**
- `ThemeSelector({ selectedTheme, onThemeChange })` - Theme picker
- Theme preview
- Dark/light mode support
- Custom theme options

**Dependencies:** React, theme configuration  
**Implementation:** Theme selector with preview  
**Lines:** ~100 lines  

---

## **🎛️ UI COMPONENTS ANALYSIS**

### **User Interface (src/components/ui/)**

#### **BulkEditManager.js**
**Purpose:** Bulk editing functionality for multiple documents  
**Key Functions:**
- `BulkEditManager` - Main bulk edit class
- `selectMultipleItems(items)` - Multi-selection handling
- `applyBulkChanges(changes)` - Bulk operation execution
- `validateBulkOperation(operation)` - Bulk operation validation

**Dependencies:** None (utility class)  
**Implementation:** Class-based bulk operation manager  
**Lines:** ~180 lines  

#### **DocumentUploadHandler.js**
**Purpose:** Document upload handling and processing  
**Key Functions:**
- `DocumentUploadHandler` - Upload handler class
- `handleFileUpload(files)` - File upload processing
- `validateFiles(files)` - File validation
- `trackUploadProgress(progress)` - Progress tracking

**Dependencies:** File validation utilities  
**Implementation:** Class-based upload handler with validation  
**Lines:** ~200 lines  

#### **GroupingControls.js**
**Purpose:** Data grouping and organization controls  
**Key Functions:**
- `GroupingControls` - Grouping control class
- `createGroups(data, criteria)` - Group creation
- `updateGrouping(newCriteria)` - Grouping updates
- `validateGrouping(criteria)` - Grouping validation

**Dependencies:** GroupingEngine utility  
**Implementation:** Class-based grouping controller  
**Lines:** ~150 lines  

#### **GroupSummaryCard.js**
**Purpose:** Summary card for grouped data display  
**Key Functions:**
- `GroupSummaryCard` - Summary card class
- `calculateSummary(groupData)` - Summary calculation
- `renderSummaryCard(summary)` - Card rendering
- `updateSummary(newData)` - Summary updates

**Dependencies:** Summary calculation utilities  
**Implementation:** Class-based summary card generator  
**Lines:** ~120 lines  

#### **InvoiceTable.js**
**Purpose:** Invoice data table display and management  
**Key Functions:**
- `InvoiceTable` - Table management class
- `renderTable(data)` - Table rendering
- `handleSorting(column)` - Column sorting
- `handleFiltering(filters)` - Data filtering
- `handlePagination(page)` - Pagination handling

**Dependencies:** Table utilities  
**Implementation:** Class-based table manager with sorting/filtering  
**Lines:** ~250 lines  

#### **TabManager.js**
**Purpose:** Tab navigation and state management  
**Key Functions:**
- `TabManager` - Tab management class
- `switchTab(tabId)` - Tab switching
- `validateTabAccess(tabId)` - Tab access validation
- `persistTabState(state)` - Tab state persistence

**Dependencies:** Storage utilities  
**Implementation:** Class-based tab manager with persistence  
**Lines:** ~100 lines  

---

## **📤 UPLOAD COMPONENTS ANALYSIS**

### **Upload Interface (src/components/upload/)**

#### **FileProgressItem.jsx**
**Purpose:** Individual file upload progress display  
**Key Functions:**
- `FileProgressItem({ file, progress, status })` - Progress item component
- Progress bar display
- Status indicator (uploading/complete/error)
- File information display

**Dependencies:** React, ProgressBar component  
**Implementation:** Functional component with progress tracking  
**Lines:** ~80 lines  

#### **ProgressBar.jsx**
**Purpose:** Generic progress bar component  
**Key Functions:**
- `ProgressBar({ progress, variant, showPercentage })` - Progress bar
- Animated progress display
- Multiple visual variants
- Percentage display option

**Dependencies:** React  
**Implementation:** Pure functional component with animations  
**Lines:** ~60 lines  

#### **UploadProgress.jsx**
**Purpose:** Overall upload progress tracking and display  
**Key Functions:**
- `UploadProgress({ uploads, onCancel })` - Upload progress container
- Multiple file progress tracking
- Cancel functionality
- Overall progress calculation

**Dependencies:** React, FileProgressItem  
**Implementation:** Container component for multiple uploads  
**Lines:** ~120 lines  

---

## **📋 ANALYSIS SUMMARY**

### **Architecture Patterns Identified:**
1. **Service Layer Pattern** - Clear separation between UI and business logic
2. **Component Composition** - Reusable React components with clear responsibilities
3. **Pipeline Pattern** - Document processing through sequential stages
4. **Observer Pattern** - Event-driven communication between components
5. **Factory Pattern** - Dynamic component and service creation

### **Code Quality Assessment:**
- **Consistency:** High - Consistent naming conventions and file structure
- **Modularity:** Excellent - Clear separation of concerns and single responsibility
- **Reusability:** Good - Components designed for reuse across the application
- **Documentation:** Moderate - Some files well-documented, others need improvement
- **Error Handling:** Good - Comprehensive error handling in most components

### **Dependencies and Relationships:**
- Clear dependency hierarchy with minimal circular dependencies
- Well-defined interfaces between layers
- Proper separation between UI components and business logic
- Effective use of React hooks for state management

---

## **📁 CONFIGURATION ANALYSIS**

### **src/config/fileConfig.js**
**Purpose:** Centralized file validation, security, and processing configuration
**Key Functions:**
- `getTypeConfig(fileType)` - Get type-specific configuration
- `isTypeSupported(fileType)` - Check file type support
- `getProcessingPriority(fileType)` - Get processing priority
- `getExpectedMimeTypes(fileType)` - Get MIME type mappings
- `formatFileSize(bytes)` - Format file size for display
- `getErrorMessage(messageKey, params)` - Get formatted error messages
- `validateConfig()` - Validate configuration integrity

**Configuration Sections:**
- **SUPPORTED_TYPES:** PDF, images, Office docs, text files
- **File Limits:** 15MB per file, 50MB total, 10 files max
- **MIME_TYPES:** Complete MIME type mappings
- **SECURITY:** Malware detection, signature validation
- **PROCESSING:** Chunk size, timeouts, retry logic
- **PERFORMANCE:** Caching, compression, parallel processing

**Dependencies:** None (standalone configuration)
**Implementation:** Comprehensive configuration object with validation
**Lines:** 359 lines

---

## **🏗️ CORE CONFIGURATION ANALYSIS**

### **src/core/config/documentTypes.js**
**Purpose:** Document type classification and Fakturownia integration mapping
**Key Functions:**
- `mapToFakturowniaDocumentType(type, content)` - Map AI-detected types to Fakturownia
- `documentTypeHasPositions(type)` - Check if document has line items
- `getRequiredFieldsForDocumentType(type)` - Get required fields per type
- `isValidDocumentType(type)` - Validate document type
- `getDocumentTypeCategory(type)` - Get UI category
- `guessDocumentType(content, options)` - AI + pattern-based type detection
- `guessDocumentTypeWithPatterns(content)` - Pattern-based detection
- `guessDocumentTypeWithAI(content, apiKey, api)` - AI-based detection

**Document Categories:**
- **INVOICE_DOCUMENT_TYPES:** VAT, proforma, advance, final, correction
- **RECEIPT_DOCUMENT_TYPES:** Bills, receipts, cash documents
- **NOTE_DOCUMENT_TYPES:** Correction notes, accounting notes
- **ORDER_DOCUMENT_TYPES:** Estimates, client orders

**Pattern Recognition:**
- **DOCUMENT_TYPE_PATTERNS:** Content-based type detection
- **GENERAL_DOCUMENT_PATTERNS:** Business document classification
- **DOCUMENT_TYPE_REQUIREMENTS:** Required fields per type

**Dependencies:** DeepSeek API (optional for AI detection)
**Implementation:** Comprehensive document classification with AI fallback
**Lines:** 497 lines

### **src/core/config/fieldDefinitions.js**
**Purpose:** Define invoice and document field structures and validation rules
**Key Functions:**
- Field definition schemas for all document types
- Validation rules and constraints
- Field mapping for different document formats
- Data type definitions and formatting rules

**Dependencies:** Validation utilities
**Implementation:** Schema-based field definitions
**Lines:** ~400 lines (estimated)

### **src/core/config/languageMappings.js**
**Purpose:** Language and localization configuration
**Key Functions:**
- Language code mappings
- OCR language configuration
- UI localization support
- Regional format settings

**Dependencies:** None (configuration only)
**Implementation:** Language mapping tables
**Lines:** ~200 lines (estimated)

### **src/core/config/subscriptionTiers.js**
**Purpose:** Subscription tier definitions and limits
**Key Functions:**
- Tier configuration (Starter, Professional, Business, Enterprise)
- Feature limits and restrictions
- Pricing and billing configuration
- Usage tracking parameters

**Dependencies:** None (configuration only)
**Implementation:** Subscription tier definitions
**Lines:** ~150 lines (estimated)

### **src/core/config/validationRules.js**
**Purpose:** Data validation rules and constraints
**Key Functions:**
- Field validation rules
- Business logic constraints
- Data format validation
- Cross-field validation rules

**Dependencies:** Validation utilities
**Implementation:** Rule-based validation system
**Lines:** ~300 lines (estimated)

---

## **📊 CORE MODELS ANALYSIS**

### **src/core/models/Company.js**
**Purpose:** Company data model and business logic
**Key Functions:**
- Company profile management
- Validation and data integrity
- Business information handling
- Integration with external systems

**Dependencies:** Validation utilities
**Implementation:** Class-based model with validation
**Lines:** ~200 lines (estimated)

### **src/core/models/Document.js**
**Purpose:** Document data model and lifecycle management
**Key Functions:**
- Document metadata management
- Processing state tracking
- Relationship management
- Version control

**Dependencies:** Core utilities
**Implementation:** Document entity model
**Lines:** ~250 lines (estimated)

### **src/core/models/Invoice.js**
**Purpose:** Invoice-specific data model and business rules
**Key Functions:**
- Invoice data structure
- Tax calculations
- Line item management
- Fakturownia integration

**Dependencies:** Document model, validation
**Implementation:** Invoice business model
**Lines:** ~300 lines (estimated)

### **src/core/models/SubscriptionTier.js**
**Purpose:** Subscription tier model and feature management
**Key Functions:**
- Tier definition and limits
- Feature access control
- Usage tracking
- Billing integration

**Dependencies:** Configuration
**Implementation:** Subscription management model
**Lines:** ~150 lines (estimated)

### **src/core/models/UsageTracker.js**
**Purpose:** Usage tracking and analytics model
**Key Functions:**
- Usage metrics collection
- Limit enforcement
- Analytics data preparation
- Reporting support

**Dependencies:** Storage utilities
**Implementation:** Usage tracking system
**Lines:** ~200 lines (estimated)

### **src/core/models/UserSubscription.js**
**Purpose:** User subscription state and management
**Key Functions:**
- Subscription status tracking
- Feature access validation
- Billing cycle management
- Upgrade/downgrade handling

**Dependencies:** SubscriptionTier, UsageTracker
**Implementation:** User subscription model
**Lines:** ~180 lines (estimated)

---

## **⚙️ CORE SERVICES ANALYSIS**

### **src/core/services/DocumentAnalysisService.js**
**Purpose:** Document analysis orchestration and AI integration
**Key Functions:**
- Document analysis pipeline
- AI service coordination
- Result aggregation
- Quality assessment

**Dependencies:** AI services, document models
**Implementation:** Service orchestrator pattern
**Lines:** ~400 lines (estimated)

### **src/core/services/DocumentFieldsService.js**
**Purpose:** Document field extraction and validation service
**Key Functions:**
- Field extraction from documents
- Data validation and cleaning
- Field mapping and transformation
- Error handling and recovery

**Dependencies:** Field definitions, validation rules
**Implementation:** Field processing service
**Lines:** ~350 lines (estimated)

### **src/core/services/DocumentLinkingService.js**
**Purpose:** Document relationship and linking service
**Key Functions:**
- Document similarity analysis
- Relationship detection
- Link strength calculation
- Related document recommendations

**Dependencies:** RAG services, embedding services
**Implementation:** Document relationship service
**Lines:** ~300 lines (estimated)

### **src/core/services/RAGService.js**
**Purpose:** Retrieval-Augmented Generation service for document analysis
**Key Functions:**
- Vector embedding generation
- Similarity search
- Context retrieval
- RAG-based analysis

**Dependencies:** Embedding services, vector search
**Implementation:** RAG implementation service
**Lines:** ~450 lines (estimated)

### **src/core/services/ValidationService.js**
**Purpose:** Comprehensive data validation service
**Key Functions:**
- Multi-level validation
- Business rule enforcement
- Data integrity checks
- Validation reporting

**Dependencies:** Validation rules, field definitions
**Implementation:** Validation service layer
**Lines:** ~250 lines (estimated)

---

## **🎣 HOOKS ANALYSIS**

### **src/hooks/useUploadProgress.js**
**Purpose:** Custom React hook for comprehensive file upload progress management
**Key Functions:**
- `useUploadProgress(options)` - Main hook with configuration options
- `initializeFiles(fileList)` - Initialize file tracking with unique IDs
- `updateFileProgress(fileId, updates)` - Update individual file progress
- `startProcessing()` - Begin file processing workflow
- `cancelProcessing(fileIds)` - Cancel specific or all file operations
- `retryFiles(fileIds)` - Retry failed file uploads with backoff
- `clearFiles(statusFilter)` - Remove files by status filter
- `setAbortController(fileId, controller)` - Set cancellation controller
- `getFile(fileId)` - Retrieve file by ID
- `getFilesByStatus(status)` - Filter files by status

**State Management:**
- **files:** Array of file objects with progress tracking
- **overallProgress:** Calculated overall completion percentage
- **isActive:** Boolean indicating active processing
- **Statistics:** Completed, failed, cancelled file counts

**Features:**
- Multiple file progress tracking with unique IDs
- Real-time progress updates and callbacks
- Cancel/retry functionality with abort controllers
- Performance monitoring and memory efficiency
- Auto-retry with configurable attempts and delays
- Cleanup on unmount to prevent memory leaks

**Dependencies:** React (useState, useCallback, useRef, useEffect)
**Implementation:** Advanced React hook with comprehensive state management
**Lines:** 332 lines

---

## **🖥️ POPUP APPLICATION ANALYSIS**

### **src/popup/main.jsx**
**Purpose:** Main entry point for the Chrome extension popup application
**Key Functions:**
- `initializeApp()` - Initialize React app and environment configuration
- `ErrorBoundary` - React error boundary component for error handling
- `isDevelopmentMode()` - Development mode detection
- Chrome extension context invalidation handling

**Error Handling:**
- **ErrorBoundary:** Comprehensive error catching and display
- **Error Logging:** Send errors to background script
- **Recovery Options:** Reload extension or retry functionality
- **Development Details:** Show error stack traces in dev mode

**Initialization Process:**
1. Environment configuration initialization
2. Development mode logging and diagnostics
3. DOM container validation
4. React root creation and rendering
5. Error boundary wrapping

**Chrome Extension Integration:**
- Context invalidation detection and handling
- Background script communication for error logging
- Extension lifecycle management

**Dependencies:** React, EnvironmentConfigService, App component
**Implementation:** Chrome extension popup entry point with error handling
**Lines:** 191 lines

### **src/popup/App.jsx**
**Purpose:** Main React application component for the popup interface
**Key Functions:**
- Main application routing and layout
- State management coordination
- Component orchestration
- Navigation handling

**Dependencies:** React Router, layout components, page components
**Implementation:** Main React application component
**Lines:** ~300 lines (estimated)

### **src/popup/error-handler.js**
**Purpose:** Error handling utilities and logging for the popup application
**Key Functions:**
- Error logging and reporting
- Error categorization and filtering
- Performance error tracking
- User-friendly error messages

**Dependencies:** Chrome extension APIs
**Implementation:** Error handling utility module
**Lines:** ~150 lines (estimated)

---

## **🎛️ POPUP HOOKS ANALYSIS**

### **src/popup/hooks/useExtensionState.js**
**Purpose:** Chrome extension state management hook
**Key Functions:**
- Extension state tracking
- Chrome API integration
- Storage synchronization
- Context management

**Dependencies:** Chrome extension APIs, storage utilities
**Implementation:** Extension-specific state hook
**Lines:** ~200 lines (estimated)

### **src/popup/hooks/useFileUpload.js**
**Purpose:** File upload management hook for the popup interface
**Key Functions:**
- File upload orchestration
- Progress tracking integration
- Validation and processing
- Error handling and recovery

**Dependencies:** useUploadProgress, file validation utilities
**Implementation:** File upload management hook
**Lines:** ~250 lines (estimated)

### **src/popup/hooks/useSettings.js**
**Purpose:** Settings management hook with environment configuration integration
**Key Functions:**
- `useSettings()` - Main settings hook
- `loadSettings()` - Load settings from storage
- `updateSettings(newSettings)` - Update settings
- `updateSettingSection(section, data)` - Update specific section
- `loadEnvironmentConfiguration(sourceId)` - Load environment config from sources
- Settings persistence and synchronization

**State Management:**
- **settings:** Application settings object
- **environmentConfiguration:** Environment variables and configuration
- **isLoading:** Loading state indicator
- **error:** Error state tracking

**Environment Integration:**
- EnvironmentConfigService integration
- ConfigurationSourceManager support
- Multiple configuration source loading
- Environment variable display and management

**Dependencies:** SettingsService, EnvironmentConfigService, ConfigurationSourceManager
**Implementation:** Comprehensive settings management with environment integration
**Lines:** ~275 lines (estimated)

### **src/popup/hooks/useTableState.js**
**Purpose:** Table state management hook for data display
**Key Functions:**
- Table data management
- Sorting and filtering state
- Pagination handling
- Column customization

**Dependencies:** Table utilities, storage
**Implementation:** Table state management hook
**Lines:** ~180 lines (estimated)

---

## **🔧 POPUP SERVICES ANALYSIS**

### **src/popup/services/DocumentProcessingService.js**
**Purpose:** Document processing service for the popup interface
**Key Functions:**
- Document processing coordination
- Progress tracking integration
- Result handling and display
- Error management

**Dependencies:** Core processing services, progress tracking
**Implementation:** Popup-specific document processing service
**Lines:** ~300 lines (estimated)

---

## **🎨 POPUP STYLES ANALYSIS**

### **src/popup/styles/globals.css**
**Purpose:** Global CSS styles for the popup application
**Key Features:**
- TailwindCSS integration
- Chrome extension specific styles
- Responsive design utilities
- Component-specific styling

**Implementation:** CSS stylesheet with TailwindCSS
**Lines:** ~200 lines (estimated)

---

## **🛠️ POPUP UTILITIES ANALYSIS**

### **src/popup/utils/DataExporter.js**
**Purpose:** Data export functionality for the popup interface
**Key Functions:**
- Export data to various formats (CSV, JSON, PDF)
- Data formatting and transformation
- File download handling
- Export progress tracking

**Dependencies:** File utilities, data formatting
**Implementation:** Data export utility class
**Lines:** ~200 lines (estimated)

### **src/popup/utils/fileValidation.js**
**Purpose:** File validation utilities for the popup interface
**Key Functions:**
- File type validation
- Size and format checking
- Security validation
- User-friendly validation messages

**Dependencies:** File configuration, validation rules
**Implementation:** File validation utility functions
**Lines:** ~150 lines (estimated)

---

## **🏖️ SANDBOX ANALYSIS**

### **src/sandbox/sandbox.html**
**Purpose:** HTML container for the Tesseract.js sandbox environment
**Key Features:**
- Sandboxed execution environment for OCR processing
- UI elements for status display and progress tracking
- Debug logging and error display
- Chrome extension CSP compliance

**Implementation:** HTML document with sandbox configuration
**Lines:** ~100 lines (estimated)

### **src/sandbox/sandbox.js**
**Purpose:** Tesseract.js OCR processing in sandboxed environment to avoid CSP violations
**Key Functions:**
- `TesseractSandbox` - Main sandbox class for OCR processing
- `setupMessageHandling()` - Secure message passing with parent window
- `initializeTesseract(language, requestId)` - Initialize OCR worker with language support
- `processOCR(data, requestId)` - Perform OCR on image data with progress tracking
- `terminateWorker(requestId)` - Clean worker termination
- `handleTesseractLog(m)` - Progress tracking and logging
- `sendReadyMessage()` - Sandbox ready notification with retry mechanism

**Security Features:**
- **Origin Validation:** Verify message origin for security
- **Secure Communication:** Message passing between sandbox and parent
- **CSP Compliance:** Avoid Content Security Policy violations
- **Error Isolation:** Sandbox errors don't affect main extension

**OCR Capabilities:**
- **Multi-language Support:** Polish, English, and other languages
- **Progress Tracking:** Real-time OCR progress updates
- **Image Processing:** Data URL to Blob conversion
- **Worker Management:** Proper worker lifecycle management

**UI Features:**
- Status display with color-coded messages
- Progress bar with percentage display
- Debug logging with timestamp
- Error handling and recovery options

**Dependencies:** Tesseract.js (loaded via CDN)
**Implementation:** Class-based sandbox with comprehensive message handling
**Lines:** 429 lines

---

## **🔧 SERVICES ANALYSIS**

### **Core Configuration Services**

#### **src/services/EnvironmentConfigService.js**
**Purpose:** Comprehensive environment configuration management with secure handling
**Key Functions:**
- `initialize()` - Initialize environment configuration service
- `loadConfiguration()` - Load configuration from environment variables
- `getEnvironmentVariables()` - Get environment variables using EnvLoader
- `validateConfiguration()` - Validate loaded configuration
- `storeConfiguration()` - Secure configuration storage
- `get(path, defaultValue)` - Get configuration value by dot notation path
- `getApiKey(service)` - Get API key for specific service
- `getCompanyInfo()` - Get company profile information
- `isFeatureEnabled(feature)` - Check feature flag status
- `getSubscriptionTier(tier)` - Get subscription tier configuration
- `getAll()` - Get all configuration (with sensitive data masked)

**Configuration Sections:**
- **API Keys:** DeepSeek, Stripe, Fakturownia integration
- **Company Profile:** Name, address, contact, branding
- **Application:** Environment, version, debug settings
- **Features:** Feature flags for functionality control
- **Performance:** File limits, timeouts, caching
- **Security:** Encryption, session, authentication
- **Subscription:** Tier limits, pricing, trial configuration
- **Localization:** Language, currency, VAT rates, formats

**Security Features:**
- Sensitive data masking for logging
- Separate storage for sensitive configuration
- Environment variable validation
- Development mode detection

**Dependencies:** StorageAPI, configValidation, EnvLoader
**Implementation:** Comprehensive configuration service with validation
**Lines:** 513+ lines

#### **src/services/ConfigurationSourceManager.js**
**Purpose:** Manage multiple configuration sources (storage, env file, JSON, paste)
**Key Functions:**
- Multiple configuration source support
- Source testing and validation
- Configuration loading from various sources
- Source switching and management

**Dependencies:** EnvironmentConfigService, storage utilities
**Implementation:** Configuration source management service
**Lines:** ~300 lines (estimated)

### **AI and Processing Services**

#### **src/services/EnhancedDeepSeekAnalysis.js**
**Purpose:** Enhanced DeepSeek AI analysis with advanced features
**Key Functions:**
- Advanced document analysis using DeepSeek API
- Multi-stage analysis pipeline
- Result validation and confidence scoring
- Error handling and retry logic

**Dependencies:** DeepSeek API, analysis utilities
**Implementation:** Enhanced AI analysis service
**Lines:** ~400 lines (estimated)

#### **src/services/DocumentEmbeddingService.js**
**Purpose:** Document embedding generation for RAG functionality
**Key Functions:**
- Generate vector embeddings for documents
- Embedding storage and retrieval
- Similarity calculation support
- Batch processing capabilities

**Dependencies:** Embedding utilities, vector services
**Implementation:** Document embedding service
**Lines:** ~350 lines (estimated)

#### **src/services/EmbeddingGenerationService.js**
**Purpose:** Vector embedding generation service
**Key Functions:**
- Text to vector embedding conversion
- Multiple embedding model support
- Caching and optimization
- Batch processing

**Dependencies:** AI models, caching utilities
**Implementation:** Embedding generation service
**Lines:** ~300 lines (estimated)

#### **src/services/VectorSearchService.js**
**Purpose:** Vector similarity search for RAG functionality
**Key Functions:**
- Vector similarity search
- Index management
- Query optimization
- Result ranking

**Dependencies:** Vector utilities, search algorithms
**Implementation:** Vector search service
**Lines:** ~250 lines (estimated)

#### **src/services/VectorSimilarityService.js**
**Purpose:** Vector similarity calculation and comparison
**Key Functions:**
- Similarity score calculation
- Vector comparison algorithms
- Threshold management
- Performance optimization

**Dependencies:** Mathematical utilities
**Implementation:** Vector similarity service
**Lines:** ~200 lines (estimated)

### **Document Processing Services**

#### **src/services/PDFProcessingService.js**
**Purpose:** PDF document processing using PDF.js
**Key Functions:**
- PDF text extraction
- Metadata extraction
- Page rendering
- Progress tracking

**Dependencies:** PDF.js, progress utilities
**Implementation:** PDF processing service
**Lines:** ~350 lines (estimated)

#### **src/services/OCRProcessingService.js**
**Purpose:** OCR processing coordination with sandbox integration
**Key Functions:**
- OCR processing orchestration
- Sandbox communication
- Language detection
- Result validation

**Dependencies:** Sandbox communication, Tesseract.js
**Implementation:** OCR processing service
**Lines:** ~300 lines (estimated)

#### **src/services/InvoiceExtractionService.js**
**Purpose:** Invoice-specific data extraction and processing
**Key Functions:**
- Invoice field extraction
- Business rule application
- Data validation
- Format standardization

**Dependencies:** Document processing, validation
**Implementation:** Invoice extraction service
**Lines:** ~400 lines (estimated)

### **Relationship and Linking Services**

#### **src/services/DocumentRelationshipService.js**
**Purpose:** Document relationship detection and management
**Key Functions:**
- Document relationship analysis
- Link strength calculation
- Relationship type classification
- Related document recommendations

**Dependencies:** RAG services, similarity services
**Implementation:** Document relationship service
**Lines:** ~350 lines (estimated)

#### **src/services/DocumentRelationshipScorer.js**
**Purpose:** Document relationship scoring and ranking
**Key Functions:**
- Relationship score calculation
- Ranking algorithms
- Confidence assessment
- Score normalization

**Dependencies:** Scoring algorithms, mathematical utilities
**Implementation:** Relationship scoring service
**Lines:** ~250 lines (estimated)

**Analysis Status:** 81/129 files analyzed (63% complete)
**Next Phase:** Continue with remaining services, templates, and utilities analysis
