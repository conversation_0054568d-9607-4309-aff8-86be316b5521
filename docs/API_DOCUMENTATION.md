# 🔌 **API DOCUMENTATION**

## **📋 API OVERVIEW**

This document provides comprehensive documentation for all APIs used in the MVAT Chrome Extension, including internal APIs, external service integrations, and Chrome Extension APIs.

---

## **🏗️ INTERNAL API ARCHITECTURE**

### **API Service Layer**
The extension uses a unified API service layer that provides consistent interfaces for all data operations.

```javascript
// Base API Service
class APIService {
  constructor() {
    this.storage = new StorageAPI();
    this.deepseek = new DeepSeekAPI();
    this.openai = new OpenAIAPI();
    this.processing = new ProcessingAPI();
  }
  
  async call(endpoint, data, options = {}) {
    try {
      const result = await this[endpoint](data, options);
      return { success: true, data: result, timestamp: Date.now() };
    } catch (error) {
      return { 
        success: false, 
        error: error.message, 
        code: error.code,
        timestamp: Date.now() 
      };
    }
  }
}
```

---

## **💾 STORAGE API**

### **StorageAPI Class**
Handles all Chrome storage operations with caching and validation.

#### **Methods:**

##### **`get(key, defaultValue = null)`**
Retrieves data from Chrome storage.

```javascript
// Usage
const settings = await storageAPI.get('settings', {});
const documents = await storageAPI.get('documents', []);

// Response format
{
  success: true,
  data: { /* stored data */ },
  timestamp: 1706123456789
}
```

##### **`set(key, value, options = {})`**
Stores data in Chrome storage with optional validation.

```javascript
// Usage
await storageAPI.set('settings', userSettings);
await storageAPI.set('documents', documentArray, { validate: true });

// Options
{
  validate: boolean,     // Validate data before storing
  encrypt: boolean,      // Encrypt sensitive data
  compress: boolean,     // Compress large data
  ttl: number           // Time to live in milliseconds
}
```

##### **`remove(key)`**
Removes data from Chrome storage.

```javascript
await storageAPI.remove('cache_key');
```

##### **`clear()`**
Clears all extension data (use with caution).

```javascript
await storageAPI.clear();
```

#### **Document-Specific Methods:**

##### **`saveDocument(document)`**
Saves a processed document with validation.

```javascript
const document = {
  id: 'doc_123',
  fileName: 'invoice.pdf',
  kind: 'vat',
  seller_name: 'Company ABC',
  total_gross: '1230.00',
  // ... other fields from FIELDS_DRAFT
};

await storageAPI.saveDocument(document);
```

##### **`getDocuments(filters = {})`**
Retrieves documents with optional filtering.

```javascript
// Get all documents
const allDocs = await storageAPI.getDocuments();

// Get documents with filters
const filteredDocs = await storageAPI.getDocuments({
  dateFrom: '2025-01-01',
  dateTo: '2025-12-31',
  kind: 'vat',
  seller_name: 'Company ABC'
});
```

##### **`updateDocument(id, updates)`**
Updates an existing document.

```javascript
await storageAPI.updateDocument('doc_123', {
  status: 'paid',
  paid_date: '2025-01-27'
});
```

##### **`deleteDocument(id)`**
Deletes a document by ID.

```javascript
await storageAPI.deleteDocument('doc_123');
```

---

## **🤖 AI PROCESSING APIs**

### **DeepSeekAPI Class**
Enhanced API for DeepSeek integration with OpenAI compatibility.

#### **Methods:**

##### **`callAPI(prompt, apiKey, options = {})`**
Makes API calls to DeepSeek with standardized options.

```javascript
const response = await deepSeekAPI.callAPI(
  'Extract invoice data from this text: ...',
  'your-api-key',
  {
    model: 'deepseek-chat',
    temperature: 0.1,
    max_tokens: 2000,
    response_format: { type: 'json_object' }
  }
);

// Response format
{
  success: true,
  content: '{"seller_name": "Company ABC", ...}',
  usage: { prompt_tokens: 150, completion_tokens: 300 },
  model: 'deepseek-chat'
}
```

##### **`extractJSON(text)`**
Extracts JSON from various text formats.

```javascript
const jsonData = deepSeekAPI.extractJSON(response.content);
// Handles: plain JSON, markdown code blocks, mixed text
```

##### **`processText(text, language, accountingFields, apiKey)`**
Processes OCR text for structured data extraction.

```javascript
const result = await deepSeekAPI.processText(
  ocrText,
  'pol',
  accountingFieldsInstance,
  apiKey
);

// Returns structured invoice data
{
  kind: 'vat',
  number: 'INV-2025-001',
  seller_name: 'Company ABC',
  buyer_name: 'Customer XYZ',
  total_gross: '1230.00',
  positions: [...]
}
```

### **OpenAIAPI Class**
OpenAI integration for fallback processing.

#### **Methods:**

##### **`createCompletion(prompt, options = {})`**
Creates chat completions using OpenAI API.

```javascript
const completion = await openAIAPI.createCompletion(
  'Extract structured data from this invoice text...',
  {
    model: 'gpt-4',
    temperature: 0.1,
    response_format: { type: 'json_object' }
  }
);
```

---

## **📄 DOCUMENT PROCESSING APIs**

### **PDFProcessorService**
Handles PDF text extraction using PDF.js.

#### **Methods:**

##### **`extractText(file)`**
Extracts text from PDF files.

```javascript
const pdfFile = new File([pdfData], 'invoice.pdf', { type: 'application/pdf' });
const extractedText = await pdfProcessor.extractText(pdfFile);

// Returns
{
  text: 'FAKTURA VAT Nr: 123/2025...',
  pages: ['Page 1 text...', 'Page 2 text...'],
  metadata: {
    pageCount: 2,
    title: 'Invoice 123/2025',
    author: 'Company ABC'
  }
}
```

##### **`validatePDF(file)`**
Validates PDF file integrity and security.

```javascript
const isValid = await pdfProcessor.validatePDF(file);
// Returns boolean or throws validation error
```

### **OCRProcessorService**
Handles image OCR using Tesseract.js.

#### **Methods:**

##### **`processImage(imageFile, options = {})`**
Processes images for text extraction.

```javascript
const result = await ocrProcessor.processImage(imageFile, {
  language: 'pol+eng',
  tessedit_char_whitelist: '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyzĄĆĘŁŃÓŚŹŻąćęłńóśźż.,/-: '
});

// Returns
{
  text: 'Extracted text content...',
  confidence: 0.92,
  language: 'pol',
  words: [
    { text: 'FAKTURA', confidence: 0.98, bbox: {...} },
    // ... more words
  ]
}
```

##### **`preprocessImage(imageFile)`**
Preprocesses images for better OCR accuracy.

```javascript
const processedImage = await ocrProcessor.preprocessImage(imageFile);
// Returns processed image blob
```

---

## **⚙️ SETTINGS API**

### **SettingsService**
Manages user settings and preferences.

#### **Settings Schema:**
```javascript
const settingsSchema = {
  company: {
    name: { type: 'string', required: true, maxLength: 100 },
    taxId: { type: 'string', required: true, pattern: /^\d{10}$/ }, // NIP
    address: { type: 'string', required: true, maxLength: 200 },
    email: { type: 'email', required: true },
    phone: { type: 'string', required: false, pattern: /^\+?[\d\s-()]+$/ }
  },
  display: {
    groupBy: { type: 'enum', values: ['year', 'quarter', 'month'], default: 'month' },
    dateFormat: { type: 'string', default: 'DD/MM/YYYY' },
    currency: { type: 'string', default: 'PLN' },
    language: { type: 'string', default: 'pl' }
  },
  processing: {
    ocrLanguage: { type: 'string', default: 'pol' },
    aiProvider: { type: 'enum', values: ['deepseek', 'openai'], default: 'deepseek' },
    autoProcess: { type: 'boolean', default: true },
    cacheEnabled: { type: 'boolean', default: true }
  }
};
```

#### **Methods:**

##### **`getSettings()`**
Retrieves current settings with defaults.

```javascript
const settings = await settingsService.getSettings();
```

##### **`updateSettings(updates)`**
Updates settings with validation.

```javascript
await settingsService.updateSettings({
  company: {
    name: 'New Company Name',
    taxId: '**********'
  },
  display: {
    groupBy: 'quarter'
  }
});
```

##### **`resetSettings()`**
Resets settings to defaults.

```javascript
await settingsService.resetSettings();
```

##### **`exportSettings()`**
Exports settings as JSON.

```javascript
const settingsJSON = await settingsService.exportSettings();
```

##### **`importSettings(settingsJSON)`**
Imports settings from JSON with validation.

```javascript
await settingsService.importSettings(settingsJSON);
```

---

## **🔄 CHROME EXTENSION APIs**

### **Message Passing API**
Handles communication between popup, background, and content scripts.

#### **Message Types:**
```javascript
const MessageTypes = {
  // Document processing
  PROCESS_DOCUMENT: 'process_document',
  DOCUMENT_PROCESSED: 'document_processed',
  PROCESSING_PROGRESS: 'processing_progress',
  
  // Storage operations
  SAVE_DOCUMENT: 'save_document',
  GET_DOCUMENTS: 'get_documents',
  UPDATE_DOCUMENT: 'update_document',
  DELETE_DOCUMENT: 'delete_document',
  
  // Settings
  GET_SETTINGS: 'get_settings',
  UPDATE_SETTINGS: 'update_settings',
  
  // Window management
  CREATE_DETACHED_POPUP: 'create_detached_popup',
  CLOSE_POPUP: 'close_popup',
  
  // Error handling
  ERROR_OCCURRED: 'error_occurred'
};
```

#### **Message Format:**
```javascript
const message = {
  type: MessageTypes.PROCESS_DOCUMENT,
  data: {
    file: fileData,
    options: { language: 'pol' }
  },
  requestId: 'req_123',
  timestamp: Date.now()
};

// Send message
chrome.runtime.sendMessage(message, (response) => {
  if (response.success) {
    console.log('Document processed:', response.data);
  } else {
    console.error('Processing failed:', response.error);
  }
});
```

### **Window Management API**
Manages detached popup windows.

#### **Methods:**

##### **`createDetachedPopup(options = {})`**
Creates a detached popup window.

```javascript
const windowOptions = {
  url: 'popup.html',
  type: 'popup',
  width: 800,
  height: 600,
  left: 100,
  top: 100,
  focused: true
};

const window = await chrome.windows.create(windowOptions);
```

##### **`getPopupWindow()`**
Gets the current popup window if it exists.

```javascript
const popupWindow = await windowManager.getPopupWindow();
```

##### **`closePopupWindow()`**
Closes the popup window.

```javascript
await windowManager.closePopupWindow();
```

---

## **📊 DATA EXPORT API**

### **ExportService**
Handles data export functionality.

#### **Methods:**

##### **`exportToJSON(documents, options = {})`**
Exports documents to JSON format.

```javascript
const exportData = await exportService.exportToJSON(documents, {
  includeMetadata: true,
  dateRange: { from: '2025-01-01', to: '2025-12-31' },
  format: 'pretty' // or 'compact'
});

// Returns
{
  metadata: {
    exportDate: '2025-01-27T10:30:00Z',
    documentCount: 150,
    dateRange: { from: '2025-01-01', to: '2025-12-31' }
  },
  documents: [...]
}
```

##### **`exportToCSV(documents, options = {})`**
Exports documents to CSV format.

```javascript
const csvData = await exportService.exportToCSV(documents, {
  fields: ['number', 'date', 'seller_name', 'total_gross'],
  delimiter: ',',
  includeHeaders: true
});
```

---

## **🔒 SECURITY API**

### **SecurityService**
Handles security-related operations.

#### **Methods:**

##### **`validateInput(input, schema)`**
Validates and sanitizes user input.

```javascript
const validatedData = securityService.validateInput(userInput, {
  companyName: { type: 'string', maxLength: 100, sanitize: true }
});
```

##### **`encryptSensitiveData(data)`**
Encrypts sensitive data before storage.

```javascript
const encryptedData = await securityService.encryptSensitiveData({
  apiKey: 'sensitive-api-key',
  personalData: 'sensitive-info'
});
```

##### **`validateFileUpload(file)`**
Validates uploaded files for security.

```javascript
const isValid = await securityService.validateFileUpload(file);
// Checks file type, size, content, and potential threats
```

---

## **📈 PERFORMANCE API**

### **PerformanceMonitor**
Monitors and reports performance metrics.

#### **Methods:**

##### **`measureOperation(operationName, operation)`**
Measures operation performance.

```javascript
const result = await performanceMonitor.measureOperation(
  'document_processing',
  () => processDocument(file)
);

// Logs performance metrics and returns operation result
```

##### **`getMetrics()`**
Retrieves performance metrics.

```javascript
const metrics = performanceMonitor.getMetrics();
// Returns processing times, memory usage, error rates, etc.
```

---

## **🚨 ERROR HANDLING**

### **Error Response Format:**
```javascript
{
  success: false,
  error: 'Human-readable error message',
  code: 'ERROR_CODE',
  details: {
    field: 'specific field that caused error',
    value: 'invalid value',
    expected: 'expected format'
  },
  timestamp: 1706123456789,
  requestId: 'req_123'
}
```

### **Common Error Codes:**
- `VALIDATION_ERROR`: Input validation failed
- `PROCESSING_ERROR`: Document processing failed
- `STORAGE_ERROR`: Storage operation failed
- `API_ERROR`: External API call failed
- `PERMISSION_ERROR`: Insufficient permissions
- `NETWORK_ERROR`: Network connectivity issue
- `FILE_ERROR`: File operation failed

This API documentation provides comprehensive coverage of all APIs used in the MVAT Chrome Extension, enabling developers to effectively integrate and extend the system.
