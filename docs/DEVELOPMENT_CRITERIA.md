# 📋 **DEVELOPMENT CRITERIA & STANDARDS**

## **🎯 EPIC CRITERIA**

### **Definition**
An Epic represents a large body of work that delivers significant business value and can be broken down into multiple stories. Epics typically span multiple sprints and involve multiple team members.

### **Epic Acceptance Criteria**
- ✅ **Business Value:** Must deliver measurable business value or customer benefit
- ✅ **Size:** Should take 2-8 weeks to complete with a full team
- ✅ **Independence:** Can be developed independently of other epics
- ✅ **Testable:** Has clear acceptance criteria and success metrics
- ✅ **User-Focused:** Written from user/customer perspective
- ✅ **Scope:** Well-defined boundaries and deliverables

### **Epic Structure Requirements**
```markdown
# EPIC [ID]: [Title]
**Business Value:** [Clear value proposition]
**Target Users:** [Primary user segments]
**Success Metrics:** [Measurable outcomes]
**Dependencies:** [Technical/business dependencies]
**Acceptance Criteria:** [High-level requirements]
**Stories:** [List of child stories]
```

### **Epic Quality Gates**
- 📊 **Business Case:** ROI analysis completed
- 🎯 **User Research:** User needs validated
- 🏗️ **Architecture:** Technical approach defined
- 📋 **Story Breakdown:** All stories identified and estimated
- 🧪 **Test Strategy:** Testing approach planned

---

## **📖 STORY CRITERIA**

### **Definition**
A Story represents a specific feature or functionality from the user's perspective. Stories should be completable within a single sprint and deliver incremental value.

### **Story Acceptance Criteria (INVEST)**
- ✅ **Independent:** Can be developed without dependencies on other stories
- ✅ **Negotiable:** Details can be discussed and refined
- ✅ **Valuable:** Delivers value to users or business
- ✅ **Estimable:** Can be estimated with reasonable accuracy
- ✅ **Small:** Completable within one sprint (1-2 weeks)
- ✅ **Testable:** Has clear, verifiable acceptance criteria

### **Story Structure Requirements**
```markdown
## STORY [ID]: [Title]
**As a** [user type]
**I want** [functionality]
**So that** [business value]

**Acceptance Criteria:**
- [ ] Given [context], when [action], then [outcome]
- [ ] [Additional criteria...]

**Technical Requirements:**
- [Technical specifications]

**Definition of Done:**
- [ ] Code implemented and reviewed
- [ ] Unit tests written and passing
- [ ] Integration tests passing
- [ ] Documentation updated
- [ ] User acceptance testing completed
```

### **Story Quality Gates**
- 🎯 **User Value:** Clear user benefit identified
- 📝 **Acceptance Criteria:** Specific, measurable, testable
- 🔧 **Technical Design:** Implementation approach defined
- 📊 **Estimation:** Story points assigned
- 🧪 **Test Cases:** Test scenarios documented

---

## **✅ TASK CRITERIA**

### **Definition**
A Task represents a specific piece of work required to complete a story. Tasks are technical implementation details that developers use to track progress.

### **Task Acceptance Criteria**
- ✅ **Specific:** Clear, unambiguous description of work
- ✅ **Actionable:** Can be started and completed by a developer
- ✅ **Measurable:** Has clear completion criteria
- ✅ **Time-bound:** Can be completed in 1-8 hours
- ✅ **Assignable:** Can be assigned to a specific team member
- ✅ **Trackable:** Progress can be monitored and reported

### **Task Structure Requirements**
```markdown
### TASK [ID]: [Title]
**Component:** [Affected component/module]
**Estimate:** [Hours]
**Assignee:** [Team member]
**Dependencies:** [Other tasks/requirements]

**Description:**
[Detailed description of work to be done]

**Acceptance Criteria:**
- [ ] [Specific completion criteria]
- [ ] [Additional criteria...]

**Technical Notes:**
- [Implementation details]
- [Code patterns to follow]
- [Performance requirements]
```

### **Task Quality Gates**
- 🔧 **Implementation:** Code written and functional
- 📝 **Documentation:** Code documented and commented
- 🧪 **Testing:** Unit tests written and passing
- 👀 **Review:** Code reviewed and approved
- ✅ **Integration:** Changes integrated successfully

---

## **🔧 SUBTASK CRITERIA**

### **Definition**
A Subtask represents granular work items within a task. Subtasks are the smallest trackable units of work and should be completable in 1-2 hours.

### **Subtask Acceptance Criteria**
- ✅ **Atomic:** Cannot be broken down further
- ✅ **Clear:** Unambiguous description of work
- ✅ **Quick:** Completable in 1-2 hours maximum
- ✅ **Verifiable:** Has clear completion criteria
- ✅ **Sequential:** Can be ordered logically
- ✅ **Trackable:** Progress can be monitored

### **Subtask Structure Requirements**
```markdown
#### SUBTASK [ID]: [Title]
**Parent Task:** [Task ID]
**Estimate:** [Minutes/Hours]
**Status:** [Not Started/In Progress/Complete]

**Work Description:**
[Specific work to be performed]

**Completion Criteria:**
- [ ] [Specific deliverable]

**Files Affected:**
- [List of files to be created/modified]
```

### **Subtask Quality Gates**
- ✅ **Completion:** Work finished as specified
- 📝 **Documentation:** Changes documented
- 🧪 **Verification:** Functionality verified
- 💾 **Commit:** Changes committed with proper message

---

## **📊 ESTIMATION GUIDELINES**

### **Epic Estimation (Weeks)**
- **Small Epic:** 2-4 weeks
- **Medium Epic:** 4-6 weeks
- **Large Epic:** 6-8 weeks
- **Extra Large:** >8 weeks (should be split)

### **Story Estimation (Story Points)**
- **1 Point:** Simple, well-understood work (1-2 days)
- **2 Points:** Straightforward with minor complexity (2-3 days)
- **3 Points:** Moderate complexity (3-5 days)
- **5 Points:** Complex with unknowns (5-8 days)
- **8 Points:** Very complex (1-2 weeks, consider splitting)
- **13+ Points:** Too large, must be split

### **Task Estimation (Hours)**
- **1-2 Hours:** Simple implementation
- **2-4 Hours:** Moderate implementation
- **4-6 Hours:** Complex implementation
- **6-8 Hours:** Very complex (consider splitting)
- **8+ Hours:** Too large, must be split

### **Subtask Estimation (Minutes/Hours)**
- **15-30 Minutes:** Quick fixes, simple changes
- **30-60 Minutes:** Standard implementation work
- **1-2 Hours:** Complex implementation
- **2+ Hours:** Too large, should be a task

---

## **🔄 WORKFLOW REQUIREMENTS**

### **Epic Workflow**
1. **Planning:** Business case and user research
2. **Design:** Architecture and story breakdown
3. **Development:** Story implementation
4. **Testing:** Integration and user acceptance testing
5. **Release:** Deployment and monitoring
6. **Review:** Retrospective and lessons learned

### **Story Workflow**
1. **Refinement:** Requirements clarification
2. **Planning:** Task breakdown and estimation
3. **Development:** Implementation and testing
4. **Review:** Code review and acceptance testing
5. **Done:** Meets definition of done criteria

### **Task Workflow**
1. **Assignment:** Developer assigned
2. **Implementation:** Code development
3. **Testing:** Unit and integration tests
4. **Review:** Code review process
5. **Integration:** Merge to main branch

### **Subtask Workflow**
1. **Start:** Work begins
2. **Progress:** Regular updates
3. **Complete:** Work finished
4. **Verify:** Quality check
5. **Close:** Mark as done

---

## **📋 CHANGELOG REQUIREMENTS**

### **Changelog Structure**
```markdown
# CHANGELOG

## [Version] - YYYY-MM-DD

### Added
- [New features]

### Changed
- [Changes to existing functionality]

### Deprecated
- [Soon-to-be removed features]

### Removed
- [Removed features]

### Fixed
- [Bug fixes]

### Security
- [Security improvements]
```

### **Changelog Linking**
- **Epic Level:** Major version changes
- **Story Level:** Minor version changes
- **Task Level:** Patch version changes
- **Subtask Level:** Internal tracking only

### **Git Commit Standards**
```
type(scope): description

[optional body]

[optional footer]

Epic: EPIC-001
Story: STORY-001
Task: TASK-001
```

**Types:** feat, fix, docs, style, refactor, test, chore
**Scope:** Component or module affected
**Description:** Imperative, present tense

### **Pre-Commit Quality Gates**
Every commit must pass all quality gates enforced by pre-commit hooks:

#### **Critical Gates (Commit Blocked if Failed)**
- [ ] **ESLint:** Code linting with zero errors
- [ ] **Prettier:** Code formatting compliance
- [ ] **TypeScript:** Type checking with zero errors
- [ ] **Vitest:** Unit tests with >90% coverage
- [ ] **Build:** Successful build verification

#### **Warning Gates (Commit Allowed with Warnings)**
- [ ] **Functional Tests:** API and integration tests
- [ ] **Playwright E2E:** End-to-end browser tests
- [ ] **Visual Tests:** Selenium visual regression tests
- [ ] **Security Audit:** npm audit for vulnerabilities

#### **Pre-Commit Hook Commands**
```bash
# Run all quality gates
npm run pre-commit

# Individual commands
npm run analyze          # lint + format + typecheck
npm run test:unit       # vitest with coverage
npm run test:functional # functional tests
npm run test:e2e        # playwright tests
npm run test:visual     # selenium tests
npm run build           # build verification
```

#### **Bypass Pre-Commit (Emergency Only)**
```bash
# Only use in emergencies - not recommended
git commit --no-verify -m "emergency fix"
```

---

## **✅ QUALITY ASSURANCE**

### **Definition of Done (DoD)**
- [ ] Code implemented and follows standards
- [ ] Unit tests written and passing (>90% coverage)
- [ ] Integration tests passing
- [ ] Code reviewed and approved
- [ ] Documentation updated
- [ ] Changelog updated
- [ ] No critical security vulnerabilities
- [ ] Performance requirements met
- [ ] Accessibility requirements met
- [ ] User acceptance criteria verified
- [ ] **Pre-commit hooks pass all quality gates**
- [ ] **Git commit includes Epic/Story/Task reference**
- [ ] **All tests pass (vitest, playwright, lint, format, typecheck, analyze)**

### **Review Criteria**
- **Code Quality:** Follows coding standards
- **Test Coverage:** Adequate test coverage
- **Documentation:** Clear and complete
- **Performance:** Meets performance requirements
- **Security:** No security vulnerabilities
- **Usability:** User-friendly interface
