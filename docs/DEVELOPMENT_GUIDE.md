# 🚀 **DEVELOPMENT GUIDE**

## **📋 DEVELOPMENT OVERVIEW**

This guide provides comprehensive instructions for setting up, developing, and maintaining the MVAT Chrome Extension. Follow these guidelines to ensure consistent development practices and high-quality code.

---

## **🛠️ DEVELOPMENT ENVIRONMENT SETUP**

### **Prerequisites:**
- **Node.js:** 22 LTS (Iron)
- **npm:** Latest version (comes with Node.js)
- **Chrome Browser:** Latest stable version
- **Git:** For version control
- **VS Code:** Recommended IDE with extensions

### **Required VS Code Extensions:**
```json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-json"
  ]
}
```

### **Initial Setup:**
```bash
# Clone the repository
git clone <repository-url>
cd mvat

# Install dependencies
make install-deps

# Setup development environment
make setup-dev

# Run initial tests
make test-all

# Start development server
make dev-extension
```

---

## **📁 PROJECT STRUCTURE**

### **Directory Organization:**
```
mvat/
├── src/                          # Source code
│   ├── popup/                    # React popup application
│   │   ├── components/           # React components
│   │   │   ├── common/          # Reusable UI components
│   │   │   ├── tables/          # Table-specific components
│   │   │   ├── settings/        # Settings components
│   │   │   └── upload/          # File upload components
│   │   ├── hooks/               # Custom React hooks
│   │   ├── utils/               # Popup utilities
│   │   ├── styles/              # TailwindCSS styles
│   │   ├── App.jsx              # Main React application
│   │   └── main.jsx             # Entry point
│   ├── background/              # Service worker
│   │   ├── background.js        # Main service worker
│   │   └── utils/               # Background utilities
│   ├── shared/                  # Shared utilities
│   │   ├── constants/           # Application constants
│   │   ├── types/               # Type definitions
│   │   └── utils/               # Shared utility functions
│   ├── api/                     # API layer (existing)
│   ├── core/                    # Core services (existing)
│   └── components/              # Legacy components (existing)
├── public/                      # Static assets
│   ├── icons/                   # Extension icons
│   ├── popup.html               # Popup HTML template
│   └── manifest.json            # Extension manifest
├── tests/                       # Test files
│   ├── unit/                    # Unit tests
│   ├── functional/              # Functional tests
│   ├── e2e/                     # End-to-end tests
│   └── selenium/                # Selenium visual tests
├── docs/                        # Documentation
├── dist/                        # Build output
└── Makefile                     # Build automation
```

### **File Naming Conventions:**
- **Components:** PascalCase (e.g., `InvoiceTable.jsx`)
- **Hooks:** camelCase with `use` prefix (e.g., `useDocumentProcessor.js`)
- **Utilities:** camelCase (e.g., `dateUtils.js`)
- **Constants:** UPPER_SNAKE_CASE (e.g., `API_ENDPOINTS.js`)
- **Types:** PascalCase (e.g., `DocumentTypes.js`)

---

## **🔧 DEVELOPMENT WORKFLOW**

### **Daily Development Process:**

#### **1. Start Development Session:**
```bash
# Pull latest changes
git pull origin main

# Install any new dependencies
make install-deps

# Run tests to ensure baseline
make test-unit

# Start development server
make dev-extension
```

#### **2. Feature Development:**
```bash
# Create feature branch
git checkout -b feature/invoice-table-enhancement

# Make changes following coding standards
# Write tests for new functionality
# Run tests frequently during development
make test-watch
```

#### **3. Before Committing:**
```bash
# Run full test suite
make test-all

# Check code formatting
make lint-fix

# Build extension to verify
make build-extension

# Commit changes
git add .
git commit -m "feat: enhance invoice table with sorting functionality"
```

### **Code Review Process:**
1. **Self Review:** Review your own code before creating PR
2. **Automated Checks:** Ensure all CI checks pass
3. **Peer Review:** At least one team member review
4. **Testing:** Verify all tests pass and coverage is maintained
5. **Documentation:** Update relevant documentation

---

## **📝 CODING STANDARDS**

### **JavaScript/React Standards:**

#### **Component Structure:**
```javascript
// InvoiceTable.jsx
import React, { useState, useEffect, useMemo } from 'react';
import PropTypes from 'prop-types';
import { useDocumentProcessor } from '@popup/hooks/useDocumentProcessor';
import { formatCurrency, formatDate } from '@shared/utils/formatUtils';
import Button from '@popup/components/common/Button';

/**
 * InvoiceTable component for displaying processed invoice data
 * Supports sorting, filtering, and grouping by date periods
 */
const InvoiceTable = ({ 
  documents = [], 
  groupBy = 'month', 
  onDocumentSelect,
  className = '' 
}) => {
  // State management
  const [sortConfig, setSortConfig] = useState({ key: 'date', direction: 'desc' });
  const [filters, setFilters] = useState({});
  
  // Custom hooks
  const { processDocument, isProcessing } = useDocumentProcessor();
  
  // Computed values
  const sortedDocuments = useMemo(() => {
    return sortDocuments(documents, sortConfig);
  }, [documents, sortConfig]);
  
  // Event handlers
  const handleSort = (key) => {
    setSortConfig(prev => ({
      key,
      direction: prev.key === key && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };
  
  // Effects
  useEffect(() => {
    // Component initialization logic
  }, []);
  
  // Render helpers
  const renderTableHeader = () => (
    <thead className="bg-gray-50">
      <tr>
        <th onClick={() => handleSort('number')}>
          Invoice Number
        </th>
        <th onClick={() => handleSort('date')}>
          Date
        </th>
        <th onClick={() => handleSort('amount')}>
          Amount
        </th>
      </tr>
    </thead>
  );
  
  const renderTableBody = () => (
    <tbody>
      {sortedDocuments.map(document => (
        <tr key={document.id} onClick={() => onDocumentSelect(document)}>
          <td>{document.number}</td>
          <td>{formatDate(document.issue_date)}</td>
          <td>{formatCurrency(document.total_gross)}</td>
        </tr>
      ))}
    </tbody>
  );
  
  // Main render
  return (
    <div className={`invoice-table ${className}`}>
      <table className="w-full border-collapse">
        {renderTableHeader()}
        {renderTableBody()}
      </table>
    </div>
  );
};

// PropTypes validation
InvoiceTable.propTypes = {
  documents: PropTypes.arrayOf(PropTypes.object),
  groupBy: PropTypes.oneOf(['year', 'quarter', 'month']),
  onDocumentSelect: PropTypes.func.isRequired,
  className: PropTypes.string
};

export default InvoiceTable;
```

#### **Custom Hook Pattern:**
```javascript
// useDocumentProcessor.js
import { useState, useCallback } from 'react';
import { useStorage } from './useStorage';
import { DocumentProcessingService } from '@core/services/DocumentProcessingService';

/**
 * Custom hook for document processing operations
 * Handles file upload, processing, and state management
 */
export const useDocumentProcessor = () => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingProgress, setProcessingProgress] = useState(0);
  const [error, setError] = useState(null);
  const { saveDocument } = useStorage();
  
  const processDocument = useCallback(async (file) => {
    try {
      setIsProcessing(true);
      setError(null);
      setProcessingProgress(0);
      
      const processor = new DocumentProcessingService();
      
      // Process with progress tracking
      const result = await processor.process(file, {
        onProgress: setProcessingProgress
      });
      
      if (result.success) {
        await saveDocument(result.data);
        return result.data;
      } else {
        throw new Error(result.error);
      }
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setIsProcessing(false);
      setProcessingProgress(0);
    }
  }, [saveDocument]);
  
  return {
    processDocument,
    isProcessing,
    processingProgress,
    error
  };
};
```

### **CSS/TailwindCSS Standards:**

#### **Component Styling:**
```css
/* globals.css */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom component classes */
@layer components {
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-md border border-gray-200 p-6;
  }
  
  .invoice-table {
    @apply w-full border-collapse bg-white rounded-lg overflow-hidden shadow-sm;
  }
  
  .invoice-table th {
    @apply bg-gray-50 px-4 py-3 text-left text-sm font-medium text-gray-700 border-b border-gray-200;
  }
  
  .invoice-table td {
    @apply px-4 py-3 text-sm text-gray-900 border-b border-gray-100;
  }
}

/* Utility classes for specific use cases */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175) transparent;
  }
}
```

#### **Responsive Design Patterns:**
```javascript
// Responsive component example
const ResponsiveInvoiceCard = ({ invoice }) => (
  <div className="
    card
    w-full
    sm:w-1/2
    lg:w-1/3
    xl:w-1/4
    p-4
    sm:p-6
    space-y-3
    sm:space-y-4
  ">
    <h3 className="text-lg sm:text-xl font-semibold truncate">
      {invoice.number}
    </h3>
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm">
      <span className="text-gray-600">Date:</span>
      <span className="font-medium">{formatDate(invoice.date)}</span>
      <span className="text-gray-600">Amount:</span>
      <span className="font-medium text-green-600">
        {formatCurrency(invoice.amount)}
      </span>
    </div>
  </div>
);
```

---

## **🧪 TESTING GUIDELINES**

### **Test-Driven Development (TDD):**

#### **1. Write Test First:**
```javascript
// InvoiceTable.test.jsx
describe('InvoiceTable', () => {
  test('should sort invoices by date in descending order by default', () => {
    const mockInvoices = [
      { id: '1', date: '2025-01-15', number: 'INV-001' },
      { id: '2', date: '2025-01-20', number: 'INV-002' },
      { id: '3', date: '2025-01-10', number: 'INV-003' }
    ];
    
    render(<InvoiceTable documents={mockInvoices} onDocumentSelect={jest.fn()} />);
    
    const rows = screen.getAllByRole('row');
    expect(rows[1]).toHaveTextContent('INV-002'); // Most recent first
    expect(rows[2]).toHaveTextContent('INV-001');
    expect(rows[3]).toHaveTextContent('INV-003'); // Oldest last
  });
});
```

#### **2. Implement Feature:**
```javascript
// Implement the sorting logic in InvoiceTable component
const sortedDocuments = useMemo(() => {
  return [...documents].sort((a, b) => {
    if (sortConfig.direction === 'desc') {
      return new Date(b[sortConfig.key]) - new Date(a[sortConfig.key]);
    }
    return new Date(a[sortConfig.key]) - new Date(b[sortConfig.key]);
  });
}, [documents, sortConfig]);
```

#### **3. Refactor and Optimize:**
```javascript
// Extract sorting logic to utility function
import { sortDocuments } from '@shared/utils/sortUtils';

const sortedDocuments = useMemo(() => {
  return sortDocuments(documents, sortConfig);
}, [documents, sortConfig]);
```

### **Testing Best Practices:**
- **Arrange, Act, Assert:** Structure tests clearly
- **Test Behavior, Not Implementation:** Focus on what the component does
- **Use Descriptive Test Names:** Make test purpose clear
- **Mock External Dependencies:** Isolate component under test
- **Test Edge Cases:** Handle empty states, errors, loading states

---

## **🔧 BUILD AND DEPLOYMENT**

### **Development Build:**
```bash
# Start development server with hot reload
make dev-extension

# Build for development (unminified, with source maps)
make build-dev

# Run specific test types during development
make test-watch
make test-functional
```

### **Production Build:**
```bash
# Clean previous builds
make clean-extension

# Run full test suite
make test-all

# Build optimized production version
make build-extension

# Validate extension package
make validate-extension
```

### **Extension Loading:**
1. Open Chrome and navigate to `chrome://extensions/`
2. Enable "Developer mode"
3. Click "Load unpacked"
4. Select the `dist/` directory
5. Extension should appear in toolbar

---

## **📊 PERFORMANCE GUIDELINES**

### **Code Splitting:**
```javascript
// Lazy load heavy components
const InvoiceTable = lazy(() => import('./components/tables/InvoiceTable'));
const SettingsPanel = lazy(() => import('./components/settings/SettingsPanel'));

// Use Suspense for loading states
<Suspense fallback={<LoadingSpinner />}>
  <InvoiceTable documents={documents} />
</Suspense>
```

### **Memory Management:**
```javascript
// Clean up event listeners and subscriptions
useEffect(() => {
  const handleResize = () => setWindowSize(window.innerWidth);
  window.addEventListener('resize', handleResize);
  
  return () => {
    window.removeEventListener('resize', handleResize);
  };
}, []);

// Use useMemo for expensive calculations
const expensiveCalculation = useMemo(() => {
  return documents.reduce((acc, doc) => acc + parseFloat(doc.amount), 0);
}, [documents]);
```

### **Bundle Optimization:**
```javascript
// vite.config.js
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          pdf: ['pdfjs-dist'],
          ocr: ['tesseract.js']
        }
      }
    }
  }
});
```

---

## **🐛 DEBUGGING GUIDELINES**

### **Chrome Extension Debugging:**
1. **Background Script:** Use Chrome DevTools > Extensions > Service Worker
2. **Popup:** Right-click extension icon > Inspect popup
3. **Storage:** Chrome DevTools > Application > Storage > Extension storage
4. **Console Logs:** Use structured logging with prefixes

### **Debugging Tools:**
```javascript
// Debug utility for development
const debug = {
  log: (message, data) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[MVAT] ${message}`, data);
    }
  },
  
  error: (message, error) => {
    console.error(`[MVAT ERROR] ${message}`, error);
  },
  
  performance: (label, fn) => {
    console.time(`[MVAT PERF] ${label}`);
    const result = fn();
    console.timeEnd(`[MVAT PERF] ${label}`);
    return result;
  }
};
```

---

## **📚 DOCUMENTATION STANDARDS**

### **Code Documentation:**
```javascript
/**
 * Processes uploaded documents through the complete analysis pipeline
 * 
 * @param {File} file - The uploaded document file
 * @param {Object} options - Processing options
 * @param {Function} options.onProgress - Progress callback function
 * @param {string} options.language - OCR language preference
 * @returns {Promise<ProcessedDocument>} The processed document data
 * 
 * @throws {ValidationError} When file validation fails
 * @throws {ProcessingError} When document processing fails
 * 
 * @example
 * const result = await processDocument(file, {
 *   onProgress: (progress) => setProgress(progress),
 *   language: 'pol'
 * });
 */
async function processDocument(file, options = {}) {
  // Implementation
}
```

### **Component Documentation:**
```javascript
/**
 * InvoiceTable - Displays processed invoice data in a sortable, filterable table
 * 
 * Features:
 * - Sorting by any column
 * - Filtering by date range, amount, etc.
 * - Grouping by year/quarter/month
 * - Export functionality
 * - Responsive design
 * 
 * @component
 * @example
 * <InvoiceTable
 *   documents={invoices}
 *   groupBy="month"
 *   onDocumentSelect={handleSelect}
 *   className="custom-table"
 * />
 */
```

This development guide ensures consistent, high-quality development practices across the MVAT Chrome Extension project.
