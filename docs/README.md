# 📄 **MVAT Chrome Extension**

> **Multi-VAT Invoice Processing Chrome Extension**  
> Intelligent document processing with PDF.js, Tesseract.js, and OpenAI integration

[![Build Status](https://img.shields.io/badge/build-passing-brightgreen)](https://github.com/your-repo/mvat)
[![Test Coverage](https://img.shields.io/badge/coverage-95%25-brightgreen)](https://github.com/your-repo/mvat)
[![Chrome Extension](https://img.shields.io/badge/chrome-extension-blue)](https://chrome.google.com/webstore)
[![License](https://img.shields.io/badge/license-MIT-blue)](LICENSE)

---

## **🎯 OVERVIEW**

MVAT is a powerful Chrome extension that automates invoice and accounting document processing for Polish businesses. It combines cutting-edge AI technologies to extract, structure, and organize financial data from PDF documents and scanned images.

### **✨ Key Features**

- 📄 **PDF Processing**: Extract text from PDF invoices using PDF.js
- 🔍 **OCR Capabilities**: Process scanned documents with Tesseract.js
- 🤖 **AI-Powered Extraction**: Structure data using OpenAI/DeepSeek APIs
- 📊 **Smart Organization**: Group data by year/quarter/month views
- 💾 **JSON Storage**: Secure local storage with intelligent caching
- ⚙️ **Company Settings**: Manage business information (NIP, address, contact)
- 🪟 **Detached Popup**: Independent window for better workflow
- 🧪 **Comprehensive Testing**: Unit, functional, E2E, and visual tests

### **🏗️ Technology Stack**

- **Frontend**: React 18, TailwindCSS 4.0, Vite
- **Runtime**: Node.js 22 LTS
- **Extension**: Chrome Manifest V3
- **Processing**: PDF.js, Tesseract.js, OpenAI SDK
- **Testing**: Jest, Playwright, Selenium
- **Build**: Makefile-driven workflow

---

## **🚀 QUICK START**

### **Prerequisites**
- Node.js 22 LTS (Iron)
- Chrome Browser (latest stable)
- Git

### **Installation**

```bash
# Clone the repository
git clone https://github.com/your-repo/mvat.git
cd mvat

# Install dependencies
make install-deps

# Build the extension
make build-extension

# Load in Chrome
# 1. Open chrome://extensions/
# 2. Enable "Developer mode"
# 3. Click "Load unpacked"
# 4. Select the dist/ directory
```

### **Development Setup**

```bash
# Start development server
make dev-extension

# Run tests
make test-all

# Watch mode for development
make test-watch
```

---

## **📚 DOCUMENTATION**

### **📖 Core Documentation**
- [📋 Action Plan](ACTION_PLAN.md) - Comprehensive project roadmap
- [🏗️ Architecture](ARCHITECTURE.md) - System design and structure
- [🚀 Development Guide](DEVELOPMENT_GUIDE.md) - Development workflow and standards
- [🔌 API Documentation](API_DOCUMENTATION.md) - Complete API reference
- [🧪 Testing Strategy](TESTING_STRATEGY.md) - Testing approach and guidelines

### **📁 Project Structure**
```
mvat/
├── src/                          # Source code
│   ├── popup/                    # React popup application
│   │   ├── components/           # UI components
│   │   ├── hooks/               # Custom React hooks
│   │   ├── utils/               # Utilities
│   │   └── styles/              # TailwindCSS styles
│   ├── background/              # Service worker
│   ├── shared/                  # Shared utilities
│   ├── api/                     # API layer
│   └── core/                    # Core services
├── tests/                       # Test files
│   ├── unit/                    # Unit tests
│   ├── functional/              # Functional tests
│   ├── e2e/                     # End-to-end tests
│   └── selenium/                # Visual tests
├── docs/                        # Documentation
├── public/                      # Static assets
└── dist/                        # Build output
```

---

## **🔧 USAGE**

### **Document Processing Workflow**

1. **Upload Document**
   - Click the extension icon to open the popup
   - Drag and drop PDF files or scanned images
   - Or use the file picker to select documents

2. **Automatic Processing**
   - PDF text extraction using PDF.js
   - OCR processing for scanned documents
   - AI-powered data structuring
   - Field validation and correction

3. **Data Organization**
   - View processed invoices in organized tables
   - Group by year, quarter, or month
   - Sort and filter data
   - Export to JSON format

4. **Settings Management**
   - Configure company information
   - Set processing preferences
   - Customize display options

### **Supported Document Types**

Based on Polish accounting standards:
- **VAT Invoices** (`vat`) - Standard VAT invoices
- **Proforma Invoices** (`proforma`) - Proforma invoices
- **Bills** (`bill`) - Service bills
- **Receipts** (`receipt`) - Purchase receipts
- **Advance Invoices** (`advance`) - Advance payment invoices
- **Final Invoices** (`final`) - Final settlement invoices
- **Correction Invoices** (`correction`) - Invoice corrections
- And 20+ other Polish document types

### **Extracted Data Fields**

The extension extracts comprehensive invoice data including:
- Document identification (number, type, dates)
- Seller information (name, NIP, address, bank details)
- Buyer information (name, NIP, address, contact)
- Financial data (amounts, VAT, currency, payment terms)
- Line items (products, quantities, prices, tax rates)
- Additional metadata and validation results

---

## **🧪 TESTING**

### **Test Coverage**
- **Unit Tests**: 95% coverage target
- **Functional Tests**: All API integrations
- **E2E Tests**: Complete user workflows
- **Visual Tests**: UI consistency and regression

### **Running Tests**

```bash
# Run all tests
make test-all

# Individual test types
make test-unit          # Jest unit tests
make test-functional    # API and integration tests
make test-e2e          # Playwright E2E tests
make test-visual       # Selenium visual tests

# Development testing
make test-watch        # Watch mode for unit tests
```

### **Test Structure**
```
tests/
├── unit/              # Component and service tests
├── functional/        # API integration tests
├── e2e/              # End-to-end user workflows
└── selenium/         # Visual regression tests
```

---

## **⚙️ CONFIGURATION**

### **Environment Variables**
```bash
# API Keys (optional - can be set in extension settings)
DEEPSEEK_API_KEY=your_deepseek_api_key
OPENAI_API_KEY=your_openai_api_key

# Development settings
NODE_ENV=development
DEBUG_MODE=true
```

### **Extension Settings**
Configure through the extension popup:
- **Company Information**: Name, NIP, address, email, phone
- **Display Preferences**: Grouping, date format, language
- **Processing Options**: OCR language, AI provider, auto-processing

---

## **🔒 SECURITY & PRIVACY**

### **Data Security**
- All data stored locally in Chrome storage
- No data transmitted to external servers (except AI APIs)
- Sensitive information encrypted before storage
- Secure file validation and processing

### **Privacy Protection**
- No tracking or analytics
- User data remains on local device
- Optional cloud AI processing with user consent
- Transparent data handling practices

### **Permissions**
The extension requires minimal permissions:
- `storage`: Local data storage
- `activeTab`: Access to current tab (if needed)
- `scripting`: Content script injection (if needed)

---

## **🤝 CONTRIBUTING**

### **Development Workflow**
1. Fork the repository
2. Create a feature branch
3. Follow coding standards in [Development Guide](DEVELOPMENT_GUIDE.md)
4. Write comprehensive tests
5. Submit a pull request

### **Code Standards**
- **JavaScript**: ES2022+ with JSDoc
- **React**: Functional components with hooks
- **CSS**: TailwindCSS utility classes
- **Testing**: Jest, React Testing Library, Playwright
- **Documentation**: Comprehensive inline and external docs

### **Pull Request Process**
1. Ensure all tests pass
2. Maintain 95%+ test coverage
3. Update documentation as needed
4. Follow conventional commit messages
5. Request review from maintainers

---

## **📊 PERFORMANCE**

### **Benchmarks**
- **Extension Load Time**: < 500ms
- **Document Processing**: < 10s for typical invoices
- **UI Responsiveness**: < 100ms for interactions
- **Memory Usage**: < 50MB typical, < 100MB peak

### **Optimization Features**
- Code splitting for faster loading
- Lazy loading of heavy components
- Intelligent caching system
- Virtual scrolling for large datasets
- Background processing for heavy operations

---

## **🐛 TROUBLESHOOTING**

### **Common Issues**

**Extension won't load:**
- Check Chrome version compatibility
- Verify all files are in dist/ directory
- Check browser console for errors

**Document processing fails:**
- Verify file format (PDF, JPG, PNG)
- Check file size (< 10MB recommended)
- Ensure API keys are configured

**Poor OCR accuracy:**
- Use high-quality scanned images
- Ensure good contrast and resolution
- Try different OCR language settings

### **Debug Mode**
Enable debug mode for detailed logging:
```javascript
// In browser console
localStorage.setItem('mvat_debug', 'true');
```

---

## **📝 LICENSE**

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

## **🙏 ACKNOWLEDGMENTS**

- **PDF.js**: Mozilla's PDF processing library
- **Tesseract.js**: Pure JavaScript OCR engine
- **OpenAI**: AI-powered text processing
- **TailwindCSS**: Utility-first CSS framework
- **React**: User interface library
- **Vite**: Fast build tool

---

## **📞 SUPPORT**

- **Documentation**: [docs/](docs/)
- **Issues**: [GitHub Issues](https://github.com/your-repo/mvat/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-repo/mvat/discussions)
- **Email**: <EMAIL>

---

**Built with ❤️ for Polish businesses**
