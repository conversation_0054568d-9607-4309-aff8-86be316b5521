# 🏗️ **MVAT CHROME EXTENSION ARCHITECTURE**

## **📋 ARCHITECTURE OVERVIEW**

The MVAT Chrome Extension follows a modern, modular architecture designed for scalability, maintainability, and performance. Built with React, Vite, and TailwindCSS 4.0, it implements a clean separation of concerns with comprehensive testing coverage.

---

## **🎯 ARCHITECTURAL PRINCIPLES**

1. **Single Responsibility Principle:** Each module has one clear purpose
2. **Dependency Injection:** Services are injected for better testability
3. **Event-Driven Architecture:** Loose coupling through event communication
4. **Layered Architecture:** Clear separation between UI, business logic, and data
5. **Progressive Enhancement:** Graceful degradation when services are unavailable
6. **Security First:** Secure data handling and storage practices

---

## **📚 SYSTEM ARCHITECTURE**

### **High-Level Architecture Diagram**
```
┌─────────────────────────────────────────────────────────────┐
│                    MVAT Chrome Extension                     │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Popup Window  │  │ Service Worker  │  │ Content      │ │
│  │   (React App)   │  │  (Background)   │  │ Scripts      │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    Core Processing Layer                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │    PDF.js       │  │  Tesseract.js   │  │   OpenAI     │ │
│  │   Processing    │  │   OCR Engine    │  │   API        │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                      Data Layer                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ Chrome Storage  │  │   JSON Cache    │  │   Settings   │ │
│  │      API        │  │     System      │  │  Management  │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

---

## **🔧 COMPONENT ARCHITECTURE**

### **1. Popup Application (React SPA)**

**Location:** `src/popup/`  
**Purpose:** Main user interface for document processing and data visualization

#### **Component Hierarchy:**
```
App.jsx
├── Layout/
│   ├── MainLayout.jsx
│   ├── Header.jsx
│   ├── Navigation.jsx
│   └── Footer.jsx
├── Pages/
│   ├── Dashboard.jsx
│   ├── Upload.jsx
│   ├── Tables.jsx
│   └── Settings.jsx
├── Components/
│   ├── Common/
│   │   ├── Button.jsx
│   │   ├── Card.jsx
│   │   ├── Modal.jsx
│   │   └── LoadingSpinner.jsx
│   ├── Upload/
│   │   ├── FileUpload.jsx
│   │   ├── DragDropZone.jsx
│   │   └── ProgressBar.jsx
│   ├── Tables/
│   │   ├── InvoiceTable.jsx
│   │   ├── GroupedView.jsx
│   │   ├── SummaryStats.jsx
│   │   └── TableFilters.jsx
│   └── Settings/
│       ├── CompanySettings.jsx
│       ├── DisplaySettings.jsx
│       └── ProcessingSettings.jsx
└── Hooks/
    ├── useStorage.js
    ├── useDocumentProcessor.js
    ├── useSettings.js
    └── useWindowManager.js
```

#### **State Management:**
```javascript
// Context-based state management
const AppContext = {
  // Application state
  documents: Document[],
  settings: Settings,
  processing: ProcessingState,
  
  // Actions
  uploadDocument: (file) => Promise<Document>,
  updateSettings: (settings) => Promise<void>,
  processDocument: (document) => Promise<ProcessedDocument>,
  
  // UI state
  activeTab: string,
  isLoading: boolean,
  errors: Error[]
};
```

### **2. Service Worker (Background Processing)**

**Location:** `src/background/`  
**Purpose:** Handle extension lifecycle, message passing, and background tasks

#### **Service Worker Structure:**
```javascript
// background.js - Main service worker
class BackgroundService {
  constructor() {
    this.messageHandler = new MessageHandler();
    this.storageManager = new StorageManager();
    this.windowManager = new WindowManager();
  }

  async initialize() {
    // Setup event listeners
    chrome.runtime.onInstalled.addListener(this.handleInstall);
    chrome.runtime.onMessage.addListener(this.handleMessage);
    chrome.action.onClicked.addListener(this.handleActionClick);
  }

  async handleActionClick() {
    // Create detached popup window
    await this.windowManager.createDetachedPopup();
  }
}
```

#### **Message Passing Architecture:**
```javascript
// Message types and handlers
const MessageTypes = {
  PROCESS_DOCUMENT: 'process_document',
  SAVE_SETTINGS: 'save_settings',
  GET_DOCUMENTS: 'get_documents',
  EXPORT_DATA: 'export_data'
};

// Bidirectional communication
popup ←→ background ←→ content_scripts
```

### **3. Document Processing Pipeline**

**Location:** `src/core/services/`  
**Purpose:** Orchestrate document analysis and data extraction

#### **Processing Pipeline:**
```javascript
class DocumentProcessingPipeline {
  constructor() {
    this.steps = [
      new FileValidationStep(),
      new PDFExtractionStep(),
      new OCRProcessingStep(),
      new AIAnalysisStep(),
      new DataValidationStep(),
      new StorageStep()
    ];
  }

  async process(file) {
    let context = { file, metadata: {}, extractedData: {} };
    
    for (const step of this.steps) {
      context = await step.execute(context);
      if (context.error) break;
    }
    
    return context;
  }
}
```

#### **Processing Steps:**
1. **File Validation:** Check file type, size, and integrity
2. **PDF Extraction:** Extract text using PDF.js
3. **OCR Processing:** Process images with Tesseract.js
4. **AI Analysis:** Structure data with OpenAI/DeepSeek
5. **Data Validation:** Validate extracted fields
6. **Storage:** Save to Chrome storage with caching

---

## **💾 DATA ARCHITECTURE**

### **Storage Schema:**
```javascript
// Chrome Storage Structure
{
  // User settings
  settings: {
    company: {
      name: string,
      taxId: string,
      address: string,
      email: string,
      phone: string
    },
    display: {
      groupBy: 'year' | 'quarter' | 'month',
      dateFormat: string,
      currency: string,
      language: string
    },
    processing: {
      ocrLanguage: string,
      aiProvider: 'deepseek' | 'openai',
      autoProcess: boolean,
      cacheEnabled: boolean
    }
  },
  
  // Document storage
  documents: {
    [documentId]: {
      // Metadata
      id: string,
      fileName: string,
      fileHash: string,
      uploadDate: string,
      processedDate: string,
      
      // Extracted data (following FIELDS_DRAFT schema)
      kind: string,
      number: string,
      issue_date: string,
      seller_name: string,
      buyer_name: string,
      total_gross: string,
      currency: string,
      positions: Array<Position>,
      
      // Processing metadata
      processingSteps: Array<ProcessingStep>,
      confidence: number,
      validationResults: ValidationResult[]
    }
  },
  
  // Cache for processed data
  cache: {
    [cacheKey]: {
      data: any,
      timestamp: number,
      ttl: number
    }
  },
  
  // Application metadata
  metadata: {
    version: string,
    lastSync: string,
    totalDocuments: number,
    storageUsed: number
  }
}
```

### **Data Flow:**
```
File Upload → Validation → Processing → Extraction → Validation → Storage → UI Update
     ↓              ↓           ↓            ↓            ↓          ↓         ↓
  File API    File Validator  PDF.js/    OpenAI/     Field      Chrome    React
              Security       Tesseract   DeepSeek   Validator   Storage   Context
```

---

## **🔌 API ARCHITECTURE**

### **External API Integration:**

#### **1. PDF.js Integration:**
```javascript
class PDFProcessorService {
  async extractText(file) {
    const pdf = await pdfjsLib.getDocument(file).promise;
    const pages = [];
    
    for (let i = 1; i <= pdf.numPages; i++) {
      const page = await pdf.getPage(i);
      const textContent = await page.getTextContent();
      pages.push(this.extractTextFromPage(textContent));
    }
    
    return pages.join('\n');
  }
}
```

#### **2. Tesseract.js Integration:**
```javascript
class OCRProcessorService {
  async processImage(imageFile) {
    const worker = await createWorker('pol+eng');
    
    const { data: { text, confidence } } = await worker.recognize(imageFile);
    
    await worker.terminate();
    
    return { text, confidence };
  }
}
```

#### **3. OpenAI/DeepSeek Integration:**
```javascript
class AIProcessorService {
  async extractStructuredData(text) {
    const prompt = this.generateExtractionPrompt(text);
    
    const response = await this.apiClient.chat.completions.create({
      model: 'gpt-4',
      messages: [{ role: 'user', content: prompt }],
      response_format: { type: 'json_object' }
    });
    
    return JSON.parse(response.choices[0].message.content);
  }
}
```

### **Internal API Layer:**
```javascript
// Unified API interface
class APIService {
  constructor() {
    this.storage = new StorageAPI();
    this.deepseek = new DeepSeekAPI();
    this.fakturownia = new FakturowniaAPI();
  }
  
  // Standardized response format
  async call(endpoint, data) {
    try {
      const result = await this[endpoint](data);
      return { success: true, data: result };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
}
```

---

## **🎨 UI ARCHITECTURE**

### **Design System:**
```javascript
// TailwindCSS 4.0 Configuration
const designTokens = {
  colors: {
    primary: {
      50: '#eff6ff',
      500: '#3b82f6',
      900: '#1e3a8a'
    },
    semantic: {
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#3b82f6'
    }
  },
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem'
  },
  typography: {
    fontFamily: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
      mono: ['JetBrains Mono', 'monospace']
    }
  }
};
```

### **Component Patterns:**
```javascript
// Compound Component Pattern
const InvoiceTable = {
  Root: InvoiceTableRoot,
  Header: InvoiceTableHeader,
  Body: InvoiceTableBody,
  Row: InvoiceTableRow,
  Cell: InvoiceTableCell,
  Pagination: InvoiceTablePagination
};

// Usage
<InvoiceTable.Root>
  <InvoiceTable.Header>
    <InvoiceTable.Cell>Invoice Number</InvoiceTable.Cell>
    <InvoiceTable.Cell>Date</InvoiceTable.Cell>
    <InvoiceTable.Cell>Amount</InvoiceTable.Cell>
  </InvoiceTable.Header>
  <InvoiceTable.Body>
    {invoices.map(invoice => (
      <InvoiceTable.Row key={invoice.id}>
        <InvoiceTable.Cell>{invoice.number}</InvoiceTable.Cell>
        <InvoiceTable.Cell>{invoice.date}</InvoiceTable.Cell>
        <InvoiceTable.Cell>{invoice.amount}</InvoiceTable.Cell>
      </InvoiceTable.Row>
    ))}
  </InvoiceTable.Body>
  <InvoiceTable.Pagination />
</InvoiceTable.Root>
```

---

## **🔒 SECURITY ARCHITECTURE**

### **Security Measures:**
1. **Content Security Policy:** Strict CSP in manifest.json
2. **Input Validation:** All user inputs validated and sanitized
3. **File Validation:** File type, size, and content validation
4. **API Security:** Secure API key storage and transmission
5. **Data Encryption:** Sensitive data encrypted in storage
6. **Permission Model:** Minimal required permissions

### **Security Implementation:**
```javascript
// Input sanitization
class SecurityService {
  sanitizeInput(input) {
    return DOMPurify.sanitize(input);
  }
  
  validateFile(file) {
    const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png'];
    const maxSize = 10 * 1024 * 1024; // 10MB
    
    if (!allowedTypes.includes(file.type)) {
      throw new Error('Invalid file type');
    }
    
    if (file.size > maxSize) {
      throw new Error('File too large');
    }
  }
  
  encryptSensitiveData(data) {
    // Encrypt sensitive fields before storage
    return this.cryptoService.encrypt(data);
  }
}
```

---

## **📊 PERFORMANCE ARCHITECTURE**

### **Performance Optimizations:**
1. **Code Splitting:** Dynamic imports for large dependencies
2. **Lazy Loading:** Components loaded on demand
3. **Virtual Scrolling:** Efficient rendering of large datasets
4. **Caching Strategy:** Intelligent caching of processed data
5. **Bundle Optimization:** Tree shaking and minification

### **Performance Monitoring:**
```javascript
class PerformanceMonitor {
  measureProcessingTime(operation) {
    const start = performance.now();
    
    return async (...args) => {
      const result = await operation(...args);
      const duration = performance.now() - start;
      
      this.logMetric('processing_time', duration, operation.name);
      return result;
    };
  }
  
  trackMemoryUsage() {
    if (performance.memory) {
      const { usedJSHeapSize, totalJSHeapSize } = performance.memory;
      this.logMetric('memory_usage', usedJSHeapSize / totalJSHeapSize);
    }
  }
}
```

---

## **🧪 TESTING ARCHITECTURE**

### **Testing Strategy Integration:**
- **Unit Tests:** Component and service isolation testing
- **Integration Tests:** API and data flow testing
- **E2E Tests:** Complete user workflow testing
- **Visual Tests:** UI consistency and regression testing

### **Test Architecture:**
```javascript
// Test utilities and mocks
class TestUtils {
  createMockDocument() {
    return {
      id: 'test-doc-1',
      fileName: 'test-invoice.pdf',
      kind: 'vat',
      seller_name: 'Test Company',
      total_gross: '1230.00'
    };
  }
  
  mockAPIResponse(endpoint, response) {
    jest.spyOn(APIService.prototype, endpoint)
        .mockResolvedValue(response);
  }
}
```

This architecture ensures the MVAT Chrome Extension is scalable, maintainable, secure, and performant while following modern development best practices and supporting comprehensive testing strategies.
