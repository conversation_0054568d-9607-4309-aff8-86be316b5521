# 🧠 **MVAT BUSINESS LOGIC SPECIFICATION**

## **📋 OVERVIEW**

This document defines the core business logic that drives the MVAT Chrome Extension, ensuring customer satisfaction while maintaining security, profitability, and scalability.

---

## **🔐 SECURITY-FIRST BUSINESS LOGIC**

### **Data Protection Principles**
```javascript
// Core Security Rules
const SECURITY_RULES = {
  dataStorage: 'LOCAL_ONLY',           // Never store sensitive data on servers
  encryption: 'AES_256_REQUIRED',     // All data encrypted at rest
  transmission: 'TLS_1_3_MINIMUM',    // Secure API communications
  access: 'ZERO_KNOWLEDGE',           // No server-side data access
  audit: 'COMPLETE_LOGGING'           // Full activity tracking
};
```

### **Privacy-by-Design**
- **Local Processing:** All invoice data processed locally
- **Minimal API Calls:** Only text sent to AI services, never full documents
- **User Consent:** Explicit permission for each AI service usage
- **Data Retention:** User-controlled data lifecycle
- **Anonymization:** No personally identifiable information in logs

---

## **💰 SUBSCRIPTION BUSINESS LOGIC**

### **Tier-Based Feature Access**
```javascript
const SUBSCRIPTION_TIERS = {
  STARTER: {
    monthlyInvoices: 10,
    features: ['basic_pdf', 'local_storage', 'community_support'],
    aiCalls: 0,
    price: 0,
    restrictions: ['no_cloud_backup', 'no_api_access']
  },
  
  PROFESSIONAL: {
    monthlyInvoices: 500,
    features: ['ai_extraction', 'cloud_backup', 'email_support', 'export_csv'],
    aiCalls: 1000,
    price: 29,
    restrictions: ['no_api_access', 'single_user']
  },
  
  BUSINESS: {
    monthlyInvoices: 2000,
    features: ['advanced_ai', 'api_access', 'priority_support', 'multi_user'],
    aiCalls: 5000,
    price: 99,
    restrictions: ['no_white_label']
  },
  
  ENTERPRISE: {
    monthlyInvoices: -1, // unlimited
    features: ['all_features', 'white_label', 'dedicated_support', 'sla'],
    aiCalls: -1, // unlimited
    price: 299,
    restrictions: []
  }
};
```

### **Usage Tracking & Limits**
```javascript
class UsageManager {
  async checkInvoiceLimit(userId, tier) {
    const currentUsage = await this.getCurrentMonthUsage(userId);
    const limit = SUBSCRIPTION_TIERS[tier].monthlyInvoices;
    
    if (limit === -1) return { allowed: true }; // unlimited
    
    return {
      allowed: currentUsage < limit,
      remaining: Math.max(0, limit - currentUsage),
      resetDate: this.getNextMonthStart()
    };
  }
  
  async trackInvoiceProcessing(userId, invoiceData) {
    await this.incrementUsage(userId, 'invoices');
    await this.logActivity(userId, 'invoice_processed', {
      timestamp: new Date(),
      documentType: invoiceData.kind,
      processingTime: invoiceData.processingTime
    });
  }
}
```

---

## **🤖 AI SERVICE INTEGRATION LOGIC**

### **Smart AI Provider Selection**
```javascript
class AIProviderManager {
  constructor() {
    this.providers = {
      DEEPSEEK: { cost: 0.001, accuracy: 0.95, speed: 'fast' },
      OPENAI: { cost: 0.003, accuracy: 0.98, speed: 'medium' },
      CLAUDE: { cost: 0.002, accuracy: 0.97, speed: 'fast' }
    };
  }
  
  selectOptimalProvider(documentComplexity, userTier, budget) {
    // Business logic for provider selection
    if (userTier === 'STARTER') return null; // No AI for free tier
    
    if (documentComplexity === 'high' && userTier === 'ENTERPRISE') {
      return 'OPENAI'; // Best accuracy for enterprise
    }
    
    if (budget === 'low' || userTier === 'PROFESSIONAL') {
      return 'DEEPSEEK'; // Cost-effective option
    }
    
    return 'CLAUDE'; // Balanced option
  }
}
```

### **Quality Assurance Logic**
```javascript
class QualityAssurance {
  async validateExtraction(extractedData, originalText) {
    const validationResults = {
      confidence: 0,
      errors: [],
      warnings: [],
      suggestions: []
    };
    
    // Business rule: Required fields validation
    const requiredFields = ['number', 'issue_date', 'seller_name', 'buyer_name'];
    for (const field of requiredFields) {
      if (!extractedData[field]) {
        validationResults.errors.push(`Missing required field: ${field}`);
      }
    }
    
    // Business rule: Financial data consistency
    const calculatedTotal = this.calculateTotal(extractedData.positions);
    const declaredTotal = parseFloat(extractedData.total_gross);
    
    if (Math.abs(calculatedTotal - declaredTotal) > 0.01) {
      validationResults.warnings.push('Total amount mismatch detected');
    }
    
    // Business rule: VAT validation
    if (extractedData.seller_country === 'PL' && !extractedData.seller_tax_no) {
      validationResults.errors.push('Polish sellers must have NIP number');
    }
    
    return validationResults;
  }
}
```

---

## **📊 DATA PROCESSING BUSINESS RULES**

### **Invoice Classification Logic**
```javascript
class InvoiceClassifier {
  classifyDocument(extractedData, documentText) {
    const classification = {
      type: 'unknown',
      confidence: 0,
      businessRules: []
    };
    
    // Business rule: VAT invoice identification
    if (this.hasVATNumber(extractedData) && this.hasVATBreakdown(extractedData)) {
      classification.type = 'vat_invoice';
      classification.confidence = 0.9;
      classification.businessRules.push('VAT_INVOICE_DETECTED');
    }
    
    // Business rule: Proforma identification
    if (documentText.toLowerCase().includes('proforma')) {
      classification.type = 'proforma';
      classification.confidence = 0.8;
      classification.businessRules.push('PROFORMA_KEYWORD_FOUND');
    }
    
    // Business rule: Correction invoice
    if (extractedData.corrected_content_before || extractedData.corrected_content_after) {
      classification.type = 'correction';
      classification.confidence = 0.95;
      classification.businessRules.push('CORRECTION_FIELDS_PRESENT');
    }
    
    return classification;
  }
}
```

### **Financial Calculations**
```javascript
class FinancialCalculator {
  calculateVATBreakdown(positions) {
    const breakdown = {
      net: 0,
      vat: 0,
      gross: 0,
      vatRates: {}
    };
    
    positions.forEach(position => {
      const net = parseFloat(position.total_price_net) || 0;
      const vatRate = parseFloat(position.tax) || 0;
      const vat = net * (vatRate / 100);
      const gross = net + vat;
      
      breakdown.net += net;
      breakdown.vat += vat;
      breakdown.gross += gross;
      
      // Group by VAT rate for reporting
      if (!breakdown.vatRates[vatRate]) {
        breakdown.vatRates[vatRate] = { net: 0, vat: 0, gross: 0 };
      }
      breakdown.vatRates[vatRate].net += net;
      breakdown.vatRates[vatRate].vat += vat;
      breakdown.vatRates[vatRate].gross += gross;
    });
    
    return breakdown;
  }
}
```

---

## **🔄 WORKFLOW AUTOMATION LOGIC**

### **Processing Pipeline**
```javascript
class ProcessingPipeline {
  async processInvoice(file, userSettings) {
    const pipeline = [
      'validateFile',
      'extractText',
      'classifyDocument',
      'extractStructuredData',
      'validateBusinessRules',
      'enhanceWithAI',
      'performQualityCheck',
      'storeResults',
      'generateSummary'
    ];
    
    const result = {
      success: false,
      data: null,
      errors: [],
      warnings: [],
      processingTime: 0
    };
    
    const startTime = Date.now();
    
    for (const step of pipeline) {
      try {
        const stepResult = await this[step](file, userSettings, result);
        if (!stepResult.success) {
          result.errors.push(`Failed at step: ${step}`);
          break;
        }
        result.data = { ...result.data, ...stepResult.data };
      } catch (error) {
        result.errors.push(`Error in ${step}: ${error.message}`);
        break;
      }
    }
    
    result.processingTime = Date.now() - startTime;
    result.success = result.errors.length === 0;
    
    return result;
  }
}
```

---

## **📈 ANALYTICS & INSIGHTS LOGIC**

### **Business Intelligence**
```javascript
class BusinessIntelligence {
  generateInsights(invoiceData, timeRange) {
    const insights = {
      totalRevenue: 0,
      totalExpenses: 0,
      vatSummary: {},
      topSuppliers: [],
      monthlyTrends: [],
      complianceScore: 0
    };
    
    // Business logic: Revenue vs Expenses
    invoiceData.forEach(invoice => {
      if (invoice.income === '1') {
        insights.totalRevenue += parseFloat(invoice.total_gross) || 0;
      } else {
        insights.totalExpenses += parseFloat(invoice.total_gross) || 0;
      }
    });
    
    // Business logic: VAT compliance scoring
    const compliantInvoices = invoiceData.filter(invoice => 
      this.isCompliant(invoice)
    );
    insights.complianceScore = (compliantInvoices.length / invoiceData.length) * 100;
    
    return insights;
  }
  
  isCompliant(invoice) {
    // Business rules for compliance
    const rules = [
      invoice.number && invoice.number.length > 0,
      invoice.issue_date && this.isValidDate(invoice.issue_date),
      invoice.seller_name && invoice.seller_name.length > 0,
      invoice.buyer_name && invoice.buyer_name.length > 0,
      invoice.total_gross && parseFloat(invoice.total_gross) > 0
    ];
    
    return rules.every(rule => rule === true);
  }
}
```

---

## **🛡️ COMPLIANCE & AUDIT LOGIC**

### **Regulatory Compliance**
```javascript
class ComplianceManager {
  validateGDPRCompliance(userData, processingActivity) {
    const compliance = {
      lawfulBasis: 'legitimate_interest', // Invoice processing
      dataMinimization: true,             // Only necessary data
      purposeLimitation: true,            // Specific purpose
      accuracyPrinciple: true,           // Data validation
      storageLimitation: true,           // User-controlled retention
      integrityConfidentiality: true     // Encryption & security
    };
    
    return compliance;
  }
  
  generateAuditTrail(userId, action, data) {
    return {
      timestamp: new Date().toISOString(),
      userId: this.hashUserId(userId),
      action: action,
      dataHash: this.hashData(data),
      ipAddress: this.hashIP(data.ipAddress),
      userAgent: data.userAgent,
      success: data.success,
      errors: data.errors || []
    };
  }
}
```

This business logic ensures that MVAT operates as a secure, compliant, and profitable solution that addresses customer needs while maintaining high standards of data protection and service quality.
