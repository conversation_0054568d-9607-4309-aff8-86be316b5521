>>> lets focus only on accounting documents as specified in the "kind" set: {
	"vat" - faktura VAT
	"proforma" - faktura Proforma
	"bill" - rachunek
	"receipt" - paragon
	"advance" - faktura zaliczkowa
	"final" - faktura końcowa
	"correction" - faktura korekta
	"vat_mp" - faktura MP
	"invoice_other" - inna faktura
	"vat_margin" - faktura marża
	"kp" - kasa przyjmie
	"kw" - kasa wyda
	"estimate" - zamówienie
	"vat_mp" - faktura MP
	"vat_rr" - faktura RR
	"correction_note" - nota korygująca
	"accounting_note" - nota księgowa
	"client_order" - własny dokument nieksięgowy
	"dw" - dowód wewnętrzny
	"wnt" - Wewnątrzwspólnotowe Nabycie Towarów
	"wdt" - Wewnątrzwspólnotowa Dostawa Towarów
	"import_service" - import usług
	"import_service_eu" - import usług z UE
	"import_products" - import towarów - procedura uproszczona
	"export_products" - eksport towarów
}

>>> depending on the "kind" value, do extract specific fields and return json from deepseek: {
"number" : "13/2012", - numer faktury (jeśli nie będzie podany wygeneruje się automatycznie)
"kind" : "vat", - rodzaj faktury (vat, proforma, bill, receipt, advance, correction, vat_mp, invoice_other, vat_margin, kp, kw, final, estimate)
"income" : "1", - faktura przychodowa (1) lub kosztowa (0)
"issue_date" : "2013-01-16", - data wystawienia
"place" : "Warszawa", - miejsce wystawienia
"sell_date" : "2013-01-16", - data sprzedaży (może być data lub miesiąc postaci 2012-12)
"category_id" : "", - id kategorii
"department_id" : "1", - id działu firmy (w menu Ustawienia > Dane firmy należy kliknąć na firmę/dział i ID działu pojawi się w URL); Jeśli nie będzie tego pola oraz nie będzie pola 'seller_name' wtedy będą wstawione domyślne dane Twojej firmy
"accounting_kind": "", - rodzaj wydatku dla faktur kosztowych - (purchases, expenses, media, salary, incident, fuel0, fuel_expl75, fuel_expl100, fixed_assets, fixed_assets50, no_vat_deduction)
"seller_name" : "Radgost Sp. z o.o.", - sprzedawca
"seller_tax_no" : "525-244-57-67", - numer identyfikacji podatkowej sprzedawcy (domyślnie NIP)
"seller_tax_no_kind" : "", - rodzaj numeru identyfikacyjnego sprzedawcy; pole puste (domyślnie) jest interpretowane jako "NIP"; w innym wypadku traktowane jako dowolny wpis własny (np. PESEL, REGON)
"seller_bank_account" : "24 1140 1977 0000 5921 7200 1001", - konto bankowe sprzedawcy
"seller_bank" : "BRE Bank",
"seller_post_code" : "02-548",
"seller_city" : "Warszawa",
"seller_street" : "ul. Olesińska 21",
"seller_country" : "", - kraj sprzedawcy (ISO 3166)
"seller_email" : "<EMAIL>",
"seller_www" : "",
"seller_fax" : "",
"seller_phone" : "",
"seller_bdo_no": "**********", - numer BDO
"use_invoice_issuer": "1",
"invoice_issuer": "Dane Wystawcy",
"client_id" : "-1" - id kupującego (jeśi -1 to klient zostanie utworzony w systemie)
"buyer_name" : "Nazwa klienta" - nabywca
"buyer_tax_no" : "525-244-57-67", - numer identyfikacji podatkowej nabywcy (domyślnie NIP)
"buyer_tax_no_kind" : "", - rodzaj numeru identyfikacyjnego nabywcy; pole puste (domyślnie) jest interpretowane jako "NIP"; w innym wypadku traktowane jako wpis własny (np. PESEL, REGON)
"disable_tax_no_validation" : "",
"buyer_post_code" : "30-314", - kod pocztowy nabywcy
"buyer_city" : "Warszawa", - miasto nabywcy
"buyer_street" : "Nowa 44", - ulica nabywcy
"buyer_country" : "PL", - kraj nabywcy (ISO 3166)
"buyer_note" : "", - dodatkowy opis nabywcy
"buyer_email" : "", - email nabywcy
"recipient_id" : "", - id odbiorcy (id klienta z systemu)
"recipient_name" : "", - nazwa odbiorcy
"recipient_street" : "", - ulica odbiorcy
"recipient_post_code" : "", - kod pocztowy odbiorcy
"recipient_city" : "", - miasto odbiorcy
"recipient_country" : "", - kraj odbiorcy (ISO 3166)
"recipient_email" : "", - e-mail odbiorcy
"recipient_phone" : "", - numer telefonu odbiorcy
"recipient_note" : "", - dodatkowy opis odbiorcy
"additional_info" : "0" - czy wyświetlać dodatkowe pole na pozycjach faktury
"additional_info_desc" : "PKWiU" - nazwa dodatkowej kolumny na pozycjach faktury
"show_discount" : "0" - czy rabat
"payment_type" : "transfer",
"payment_to_kind" : pozwala określić termin płatności. Można tu podać liczbę np.: 5, wówczas mamy 5-dniowy termin płatności. Wpisanie "off" spowoduje, że faktura nie będzie miała wskazanego terminu płatności. Jeśli natomiast podamy "other_date", wówczas sami będziemy mogli zadeklarować ostateczną datę zapłaty poprzez uzupełnienie parametru "payment_to".
"payment_to" : "2013-01-16",
"status" : "issued",
"paid" : "0,00",
"oid" : "zamowienie10021", - numer zamówienia (np z zewnętrznego systemu zamówień)
"oid_unique" : jeśli to pole będzie ustawione na 'yes' wtedy nie system nie pozwoli stworzyc 2 faktur o takim samym OID (może to być przydatne w synchronizacji ze sklepem internetowym)
"warehouse_id" : "1090",
"seller_person" : imię i nazwisko wystawcy np.: "Imię Nazwisko",
"buyer_person": imię i nazwisko odbiorcy np.: "Imię Nazwisko". W przypadku przesłania żądania dla osoby prywatnej ("buyer_company": false) bez podania w nim pola "buyer_person", zostanie ono uzupełnione automatycznie danymi nabywcy ("buyer_first_name" i "buyer_last_name"). Jeśli jednak nie chcemy mieć podpisu odbiorcy na fakturze, możemy w żądaniu przesłać "buyer_person": "".
"buyer_first_name" : "Imie",
"buyer_last_name" : "Nazwisko",
"paid_date" : "",
"currency" : "PLN",
"lang" : "pl",
"use_oss" (wcześniej "use_moss"): "1", - 1 lub 0 w zależności, czy dokument ma zostać  sklasyfikowany jako sprzedaż "OSS" (dowiedz się więcej tutaj: https://fakturownia.pl/oss-zamiast-moss-czy-ciebie-takze-dotkna-zmiany-w-prawie).
"exchange_currency" : "", - przeliczona waluta (przeliczanie sumy i podatku na inną walutę), np. "PLN"
"exchange_kind" : "", - źródło kursu do przeliczenia waluty ("ecb", "nbp", "cbr", "nbu", "nbg", "own")
"exchange_currency_rate" : "", - własny kurs przeliczenia waluty (używany, gdy parametr exchange_kind ustawiony jest na "own")
"invoice_template_id" : "1",
"description" : "", - uwagi na fakturze
"description_footer" : "", - opis umieszczony w stopce faktury
"description_long" : "", - opis umieszczony na odwrocie faktury
"invoice_id" : "" - pole z id powiązanego dokumentu, np. id zamówienia przy zaliczce albo id wzorcowej faktury przy fakturze cyklicznej,
"from_invoice_id" : "" - id faktury na podstawie której faktura została wygenerowana (przydatne np. w przypadku generacji Faktura VAT z Faktury Proforma),
"delivery_date" : "" - data wpłynięcia dokumentu (tylko przy wydatkach),
"buyer_company" : "1" - czy klient jest firmą
"additional_invoice_field" : "" - wartość dodatkowego pola na fakturze, Ustawienia > Ustawienia Konta > Konfiguracja > Faktury i dokumenty > Dodatkowe pole na fakturze
"internal_note" : "" - treść notatki prywatnej na fakturze, niewidoczna na wydruku.
"exclude_from_stock_level" : "" - informacja, czy system powinien liczyć tę fakturę do stanów magazynowych (true np., gdy faktura wystawiona na podstawie paragonu)
"gtu_codes" : [] - wartości kodów GTU produktów zawartych na fakturze - UWAGA - podane wartości nadpiszą wartości kodów GTU pobranych z kart produktów podawanych w pozycjach faktury, wartości tych kodów są nadrzędne dla całej faktury
"procedure_designations" : [] - oznaczenia dotyczące procedur
"positions":
   		"product_id" : "1",
   		"name" : "Fakturownia Start",
   		"additional_info" : "", - dodatkowa informacja na pozycji faktury (np. PKWiU)
   		"discount_percent" : "", - zniżka procentowa (uwaga: aby rabat był wyliczany trzeba ustawić pole: 'show_discount' na '1' oraz przed wywołaniem należy sprawdzić czy w Ustawieniach Konta pole: "Jak obliczać rabat" ustawione jest na "procentowo")
   		"discount" : "", - zniżka kwotowa (uwaga: aby rabat był wyliczany trzeba ustawić pole: 'show_discount' na 1 oraz przed wywołaniem należy sprawdzić czy w Ustawieniach Konta pole: "Jak obliczać rabat" ustawione jest na "kwotowo")
   		"quantity" : "1",
   		"quantity_unit" : "szt",
   		"price_net" : "59,00", - jeśli nie jest podana to zostanie wyliczona
   		"tax" : "23", - może być stawka lub "np" - dla nie podlega, "zw" - dla zwolniona
   		"price_gross" : "72,57", - jeśli nie jest podana to zostanie wyliczona
   		"total_price_net" : "59,00", - jeśli nie jest podana to zostanie wyliczona
   		"total_price_gross" : "72,57",
		"description" : opis pozycji faktury
   		"code" : "" - kod produktu,
                "gtu_code" : "" - kod GTU produktu,
		"lump_sum_tax" : "17", - stawka ryczałtu (możliwa do ustawienia jedynie, gdy wystawiamy fakturę dla działu lub firmy z włączoną opcją "Płatnik zryczałtowanego podatku dochodowego")
"calculating_strategy":
{
  "position": "default" lub "keep_gross" - metoda wyliczania kwot na pozycjach faktury
  "sum": "sum" lub "keep_gross" lub "keep_net" - metoda sumowania kwot z pozycji
  "invoice_form_price_kind": "net" lub "gross" - cena jednostkowa na formatce faktury
},
"split_payment" : "1" - 1 lub 0 w zależności, czy faktura podlega pod split payment, czy nie
"accounting_vat_tax_date": "2020-12-18", Data księgowania podatku VAT (jeśli włączono w ustawieniach)
"accounting_income_tax_date": "2020-12-18", Data księgowania podatku dochodowego (jeśli włączono w ustawieniach)
"skonto_active": "1" - 1 lub 0 w zależności, czy faktura skonto ma być aktywne, czy nie
"skonto_discount_date": "2021-08-13", Termin płatności dla skonta
"skonto_discount_value": "10", Procent upustu
"exempt_tax_kind": "", Podstawa zwolnienia z podatku VAT (stosowana, gdy pozycja ma tax="zw", wcześniej należy również włączyć opcję "Wybór podstawy zastosowania stawki zw (zwolnione z opodatkowania) na fakturze" w ustawieniach konta)
"np_tax_kind": "", Podstawa zastosowania stawki NP (nie podlega) na fakturze
"reverse_charge": false, Odwrotne obciążenie. Oznaczenie faktury jako 'Odwrotne obciążenie' spowoduje wymuszenie na pozycjach (positions) odpowiedniej stawki podatku (tax) w zależności od kraju nabywcy (buyer_country): 'oo' dla 'PL' lub 'np' w pozostałych przypadkach
"corrected_content_before": "", Treść korygowana (pole ma zastosowanie dla faktur korygujących)
"corrected_content_after": "", Treść prawidłowa (pole ma zastosowanie dla faktur korygujących)
"accounting_note_kind": "credit" lub "debit", w zależności, czy Nota księgowa jest obciążąjąca czy uznaniowa (pole ma zastosowanie tylko dla not księgowych)
}

>>> for example, correction would have following fields applicable

"invoice": {
        "kind": "correction",
        "correction_reason": "Zła ilość",
        "invoice_id": "2432393",
        "from_invoice_id": "2432393",
        "client_id": 1,
        "positions":[
            {"name": "Product A1",
            "quantity":-1,
            "total_price_gross":"-10",
            "tax":"23",
            "kind":"correction",
            "correction_before_attributes": {
                "name":"Product A1",
                "quantity":"2",
                "total_price_gross":"20",
                "tax":"23",
                "kind":"correction_before"
            },
            "correction_after_attributes": {
                "name":"Product A2",
                "quantity":"1",
                "total_price_gross":"10",
                "tax":"23",
                "kind":"correction_after"
            }
        }]
    }}'

>>> normal vat invoice would have such fields 

        "invoice": {
            "kind":"vat",
            "income": "0",
            "sell_date": "2013-01-16",
            "issue_date": "2013-01-16",
            "payment_to": "2013-01-23",
            "seller_name": "Wystawca Sp. z o.o.",
            "seller_tax_no": "5252445767",
            "buyer_name": "Klient1 Sp. z o.o.",
            "buyer_email": "<EMAIL>",
            "buyer_tax_no": "5252445767",
            "positions":[
                {"name":"Produkt A1", "tax":23, "total_price_gross":10.23, "quantity":1},
            ]
        }

in here note that: 

W celu wystawienia faktury kosztowej konieczne jest przekazanie w parametrach pola income="0".

Ważne! W przypadku faktur kosztowych wszystkie pola zaczynające się od seller_ zostaną wyświetlone na fakturze w sekcji "Nabywca", a pola zaczynające się od buyer_ pokażą się w sekcji "Sprzedawca".

