{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "checkJs": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@components/*": ["src/components/*"], "@api/*": ["src/api/*"], "@core/*": ["src/core/*"], "@shared/*": ["src/shared/*"], "@popup/*": ["src/popup/*"], "@background/*": ["src/background/*"]}, "types": ["chrome", "jest", "@testing-library/jest-dom"]}, "include": ["src/**/*", "tests/**/*", "vite.config.js", "tailwind.config.js"], "exclude": ["node_modules", "dist", "coverage"]}