#!/usr/bin/env node

/**
 * Build Script for MVAT Chrome Extension
 * Handles building and copying assets for Chrome extension
 */

const fs = require('fs');
const path = require('path');

// Ensure directories exist
function ensureDir(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`✅ Created directory: ${dirPath}`);
  }
}

// Copy file with error handling
function copyFile(src, dest) {
  try {
    const content = fs.readFileSync(src);
    fs.writeFileSync(dest, content);
    console.log(`✅ Copied: ${src} → ${dest}`);
    return true;
  } catch (error) {
    console.warn(`⚠️  Failed to copy ${src}: ${error.message}`);
    return false;
  }
}

// Main build function
function buildExtension() {
  console.log('🔨 Building MVAT Chrome Extension...');
  
  const distDir = path.join(__dirname, '..', 'dist');
  const iconsDir = path.join(distDir, 'icons');
  const publicDir = path.join(__dirname, '..', 'public');
  const publicIconsDir = path.join(publicDir, 'icons');
  
  // Ensure dist directories exist
  ensureDir(distDir);
  ensureDir(iconsDir);
  
  // Copy manifest.json
  const manifestSrc = path.join(__dirname, '..', 'manifest.json');
  const manifestDest = path.join(distDir, 'manifest.json');
  copyFile(manifestSrc, manifestDest);
  
  // Copy popup.html
  const popupSrc = path.join(publicDir, 'popup.html');
  const popupDest = path.join(distDir, 'popup.html');
  copyFile(popupSrc, popupDest);
  
  // Copy icons
  const iconSizes = ['16', '32', '48', '128'];
  let iconsCopied = 0;
  
  iconSizes.forEach(size => {
    const iconSrc = path.join(publicIconsDir, `icon-${size}.png`);
    const iconDest = path.join(iconsDir, `icon-${size}.png`);
    
    if (copyFile(iconSrc, iconDest)) {
      iconsCopied++;
    }
  });
  
  // Copy background script
  const backgroundSrc = path.join(__dirname, '..', 'src', 'background', 'background.js');
  const backgroundDest = path.join(distDir, 'background.js');
  copyFile(backgroundSrc, backgroundDest);
  
  // Create a simple main.jsx for testing
  const mainJsxContent = `
// Simple React app for testing
import React from 'react';
import ReactDOM from 'react-dom/client';

function App() {
  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>MVAT Chrome Extension</h1>
      <p>Extension loaded successfully!</p>
      <p>This is a test build.</p>
    </div>
  );
}

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(<App />);
`;
  
  const mainJsxDest = path.join(distDir, 'main.jsx');
  fs.writeFileSync(mainJsxDest, mainJsxContent);
  console.log(`✅ Created: ${mainJsxDest}`);
  
  console.log('\n📊 Build Summary:');
  console.log(`✅ Manifest: copied`);
  console.log(`✅ Popup HTML: copied`);
  console.log(`✅ Icons: ${iconsCopied}/${iconSizes.length} copied`);
  console.log(`✅ Background script: copied`);
  console.log(`✅ Main script: created`);
  
  if (iconsCopied === iconSizes.length) {
    console.log('\n🎉 Build completed successfully!');
    console.log('📁 Extension files are in the dist/ directory');
    console.log('🔧 Load the extension in Chrome:');
    console.log('   1. Open chrome://extensions/');
    console.log('   2. Enable Developer mode');
    console.log('   3. Click "Load unpacked"');
    console.log('   4. Select the dist/ directory');
  } else {
    console.log('\n⚠️  Build completed with warnings');
    console.log('Some icons may be missing. Extension may still work.');
  }
}

// Run build if called directly
if (require.main === module) {
  buildExtension();
}

module.exports = { buildExtension };
