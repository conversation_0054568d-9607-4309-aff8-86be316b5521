#!/bin/bash

# Setup Python virtual environment for MVAT testing
# This script creates a local virtual environment for Python dependencies

set -e

echo "🐍 Setting up Python virtual environment for MVAT testing..."

# Check if Python 3 is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.8 or higher."
    exit 1
fi

# Check Python version
PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
echo "📋 Python version: $PYTHON_VERSION"

# Create virtual environment directory
VENV_DIR="tests/visual/venv"
echo "📁 Creating virtual environment at: $VENV_DIR"

# Remove existing venv if it exists
if [ -d "$VENV_DIR" ]; then
    echo "🧹 Removing existing virtual environment..."
    rm -rf "$VENV_DIR"
fi

# Create new virtual environment
python3 -m venv "$VENV_DIR"

# Activate virtual environment
source "$VENV_DIR/bin/activate"

# Upgrade pip, setuptools, and wheel
echo "⬆️  Upgrading pip, setuptools, and wheel..."
pip install --upgrade pip setuptools wheel

# Install requirements
echo "📦 Installing Python dependencies..."
pip install -r tests/visual/requirements.txt

echo "✅ Python virtual environment setup complete!"
echo ""
echo "💡 To activate the virtual environment manually:"
echo "   source tests/visual/venv/bin/activate"
echo ""
echo "💡 To deactivate:"
echo "   deactivate"
echo ""
echo "🧪 To run visual tests:"
echo "   make test-visual"
echo "   # or manually:"
echo "   source tests/visual/venv/bin/activate && python tests/visual/visual_test_runner.py"
