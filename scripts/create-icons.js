#!/usr/bin/env node

/**
 * Create Chrome Extension Icons
 * Generates placeholder icons for the MVAT Chrome Extension
 */

const fs = require('fs');
const path = require('path');

// Create a simple SVG icon that can be converted to different sizes
const createSVGIcon = (size) => `
<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4F46E5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7C3AED;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="${size}" height="${size}" rx="${size * 0.2}" fill="url(#grad1)"/>
  <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="${size * 0.3}" font-weight="bold" 
        text-anchor="middle" dominant-baseline="central" fill="white">MVAT</text>
  <circle cx="${size * 0.8}" cy="${size * 0.2}" r="${size * 0.08}" fill="#10B981"/>
</svg>`;

// Convert SVG to PNG using a simple Canvas approach (for Node.js environments with canvas support)
// For now, we'll create SVG files and provide instructions for PNG conversion
const createIconFiles = () => {
  const sizes = [16, 32, 48, 128];
  const publicIconsDir = path.join(__dirname, '..', 'public', 'icons');
  const distIconsDir = path.join(__dirname, '..', 'dist', 'icons');

  // Ensure directories exist
  if (!fs.existsSync(publicIconsDir)) {
    fs.mkdirSync(publicIconsDir, { recursive: true });
  }
  if (!fs.existsSync(distIconsDir)) {
    fs.mkdirSync(distIconsDir, { recursive: true });
  }

  sizes.forEach(size => {
    const svgContent = createSVGIcon(size);
    const filename = `icon-${size}.svg`;
    
    // Save to public directory
    fs.writeFileSync(path.join(publicIconsDir, filename), svgContent);
    
    // Copy to dist directory
    fs.writeFileSync(path.join(distIconsDir, filename), svgContent);
    
    console.log(`✅ Created ${filename} (${size}x${size})`);
  });

  // Create a simple PNG fallback using ASCII art approach
  // This is a temporary solution - in production, you'd use proper image generation
  const createSimplePNG = (size) => {
    // This is a placeholder - you would typically use a library like 'canvas' or 'sharp'
    // For now, we'll create a simple data URL that browsers can handle
    const canvas = `data:image/svg+xml;base64,${Buffer.from(createSVGIcon(size)).toString('base64')}`;
    return canvas;
  };

  // Create PNG files using SVG data URLs (browsers will handle the conversion)
  sizes.forEach(size => {
    const pngFilename = `icon-${size}.png`;
    const svgContent = createSVGIcon(size);
    
    // For development, we'll use SVG files renamed as PNG
    // In production, you'd convert these to actual PNG files
    fs.writeFileSync(path.join(publicIconsDir, pngFilename), svgContent);
    fs.writeFileSync(path.join(distIconsDir, pngFilename), svgContent);
    
    console.log(`✅ Created ${pngFilename} (${size}x${size}) - SVG format`);
  });

  console.log('\n🎨 Icons created successfully!');
  console.log('📝 Note: For production, convert SVG files to PNG using:');
  console.log('   - Online tools like https://convertio.co/svg-png/');
  console.log('   - Command line tools like ImageMagick or Inkscape');
  console.log('   - Node.js libraries like sharp or canvas');
};

// Run the icon creation
if (require.main === module) {
  createIconFiles();
}

module.exports = { createIconFiles };
