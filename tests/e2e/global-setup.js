/**
 * Playwright global setup for Chrome extension testing
 */

import { chromium } from '@playwright/test';
import path from 'path';
import fs from 'fs';

async function globalSetup() {
  console.log('🔧 Setting up Playwright E2E testing environment...');

  // Ensure extension is built
  const distPath = path.resolve('./dist');
  const manifestPath = path.join(distPath, 'manifest.json');

  if (!fs.existsSync(manifestPath)) {
    console.error('❌ Extension not built! Run "npm run build" first.');
    process.exit(1);
  }

  console.log('✅ Extension build found at:', distPath);

  // Test Chrome extension loading
  try {
    const browser = await chromium.launch({
      headless: false,
      args: [
        `--disable-extensions-except=${distPath}`,
        `--load-extension=${distPath}`,
        '--no-sandbox',
        '--disable-setuid-sandbox'
      ]
    });

    const context = await browser.newContext();
    const page = await context.newPage();

    // Verify extension is loaded
    const extensions = await context.backgroundPages();
    console.log(`✅ Extension loaded successfully. Background pages: ${extensions.length}`);

    await browser.close();
  } catch (error) {
    console.error('❌ Failed to load extension:', error);
    process.exit(1);
  }

  console.log('🎉 Playwright setup complete!');
}

export default globalSetup;
