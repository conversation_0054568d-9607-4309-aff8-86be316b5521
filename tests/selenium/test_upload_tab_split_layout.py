#!/usr/bin/env python3
"""
Upload Tab Split Layout Selenium Tests
Tests for the new left-right split layout with processing files on the right
"""

import time
import json
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.chrome.options import Options

class UploadTabSplitLayoutTests:
    def __init__(self):
        self.driver = None
        self.extension_id = None
        self.test_results = {
            'left_right_split': False,
            'recent_uploads_summary_left': False,
            'processing_files_right': False,
            'action_buttons_present': False,
            'vertical_space_utilization': False,
            'overall_layout': False
        }

    def setup_driver(self):
        """Setup Chrome driver with extension loaded"""
        chrome_options = Options()

        # Load extension
        extension_path = os.path.abspath('dist/dev')
        chrome_options.add_argument(f'--load-extension={extension_path}')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')

        # Use Chrome 135 for compatibility
        chrome_binary = 'tests/selenium/chrome-135/chrome-linux64/chrome'
        if os.path.exists(chrome_binary):
            chrome_options.binary_location = chrome_binary

        self.driver = webdriver.Chrome(options=chrome_options)
        self.driver.set_window_size(1200, 800)

        # Get extension ID
        self.driver.get('chrome://extensions/')
        time.sleep(2)

        # Enable developer mode and get extension ID
        try:
            dev_mode = self.driver.find_element(By.ID, 'devMode')
            if not dev_mode.is_selected():
                dev_mode.click()
                time.sleep(1)
        except:
            pass

        # Find MVAT extension using chrome.management API
        try:
            extensions_data = self.driver.execute_script("""
                return new Promise((resolve) => {
                    chrome.management.getAll((extensions) => {
                        resolve(extensions);
                    });
                });
            """)

            for ext in extensions_data:
                if 'MVAT' in ext.get('name', ''):
                    self.extension_id = ext['id']
                    break

        except Exception as e:
            print(f"⚠️ Could not get extensions via management API: {e}")

        if not self.extension_id:
            raise Exception("MVAT extension not found")

        print(f"✅ Extension loaded with ID: {self.extension_id}")

    def navigate_to_popup(self):
        """Navigate to extension popup"""
        popup_url = f'chrome-extension://{self.extension_id}/popup.html'
        self.driver.get(popup_url)

        # Wait for popup to load
        WebDriverWait(self.driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, '[data-testid="mvat-app"], #root'))
        )
        time.sleep(1)

    def test_left_right_split_layout(self):
        """Test that upload tab has proper left-right split"""
        print("📐 Testing left-right split layout...")

        try:
            self.navigate_to_popup()

            # Navigate to upload tab
            upload_tab = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Upload')]"))
            )
            upload_tab.click()
            time.sleep(1)

            # Check for main container with flex layout
            main_container = self.driver.find_element(By.CSS_SELECTOR, '.h-full.flex')

            # Check for left side (upload area)
            left_side = self.driver.find_element(By.CSS_SELECTOR, '.w-1\\/2.flex.flex-col.p-6.border-r')

            # Check for right side (processing files)
            right_side = self.driver.find_element(By.CSS_SELECTOR, '.w-1\\/2.flex.flex-col.p-6')

            # Verify layout structure
            left_width = left_side.size['width']
            right_width = right_side.size['width']

            print(f"📊 Left side width: {left_width}px")
            print(f"📊 Right side width: {right_width}px")

            # Should be roughly equal (50/50 split)
            width_ratio = abs(left_width - right_width) / max(left_width, right_width)
            split_layout_good = width_ratio < 0.1  # Within 10% difference

            self.test_results['left_right_split'] = split_layout_good
            print(f"✅ Left-right split layout: {'PASS' if split_layout_good else 'FAIL'}")

        except Exception as e:
            print(f"❌ Left-right split layout test failed: {e}")
            self.test_results['left_right_split'] = False

    def test_recent_uploads_summary_left(self):
        """Test that recent uploads summary is on the left side"""
        print("📋 Testing recent uploads summary on left side...")

        try:
            self.navigate_to_popup()

            # Navigate to upload tab
            upload_tab = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Upload')]"))
            )
            upload_tab.click()
            time.sleep(1)

            # Add mock data to create recent uploads
            self.driver.execute_script("""
                const mockInvoices = Array.from({ length: 5 }, (_, i) => ({
                    id: `test-invoice-${i}`,
                    filename: `test-invoice-${i}.pdf`,
                    processedAt: new Date(Date.now() - i * 60000).toISOString(),
                    number: `INV-${String(i).padStart(3, '0')}`,
                    total_gross: `${(Math.random() * 1000).toFixed(2)}`,
                    currency: 'PLN'
                }));

                if (window.updateContext) {
                    window.updateContext({ invoices: mockInvoices });
                }
            """)

            time.sleep(2)

            # Check for recent uploads summary in left side
            left_side = self.driver.find_element(By.CSS_SELECTOR, '.w-1\\/2.flex.flex-col.p-6.border-r')

            try:
                summary_section = left_side.find_element(By.XPATH, ".//h3[contains(text(), 'Recent Uploads Summary')]")
                print("✅ Found 'Recent Uploads Summary' section on left side")

                # Check for summary items
                summary_items = left_side.find_elements(By.CSS_SELECTOR, '.p-2.bg-white.border')
                print(f"📊 Found {len(summary_items)} summary items")

                summary_present = len(summary_items) > 0

                self.test_results['recent_uploads_summary_left'] = summary_present
                print(f"✅ Recent uploads summary on left: {'PASS' if summary_present else 'FAIL'}")

            except NoSuchElementException:
                print("⚠️ Recent uploads summary section not found on left side")
                self.test_results['recent_uploads_summary_left'] = False

        except Exception as e:
            print(f"❌ Recent uploads summary test failed: {e}")
            self.test_results['recent_uploads_summary_left'] = False

    def test_processing_files_right(self):
        """Test that processing files with details are on the right side"""
        print("🔄 Testing processing files on right side...")

        try:
            self.navigate_to_popup()

            # Navigate to upload tab
            upload_tab = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Upload')]"))
            )
            upload_tab.click()
            time.sleep(1)

            # Add mock data to create recent uploads
            self.driver.execute_script("""
                const mockInvoices = Array.from({ length: 3 }, (_, i) => ({
                    id: `test-invoice-${i}`,
                    filename: `test-invoice-${i}.pdf`,
                    processedAt: new Date(Date.now() - i * 60000).toISOString(),
                    number: `INV-${String(i).padStart(3, '0')}`,
                    total_gross: `${(Math.random() * 1000).toFixed(2)}`,
                    currency: 'PLN'
                }));

                if (window.updateContext) {
                    window.updateContext({ invoices: mockInvoices });
                }
            """)

            time.sleep(2)

            # Check for processing files in right side
            right_side = self.driver.find_element(By.CSS_SELECTOR, '.w-1\\/2.flex.flex-col.p-6:not(.border-r)')

            try:
                uploads_section = right_side.find_element(By.XPATH, ".//h3[contains(text(), 'Recent Uploads')]")
                print("✅ Found 'Recent Uploads' section on right side")

                # Check for detailed file cards
                file_cards = right_side.find_elements(By.CSS_SELECTOR, '.p-4.bg-white.border')
                print(f"📊 Found {len(file_cards)} detailed file cards")

                # Check for validation results sections
                validation_sections = right_side.find_elements(By.XPATH, ".//div[contains(text(), 'Validation Results')]")
                print(f"📊 Found {len(validation_sections)} validation sections")

                # Check for security scan sections
                security_sections = right_side.find_elements(By.XPATH, ".//div[contains(text(), 'Security Scan')]")
                print(f"📊 Found {len(security_sections)} security sections")

                processing_files_present = len(file_cards) > 0 and len(validation_sections) > 0

                self.test_results['processing_files_right'] = processing_files_present
                print(f"✅ Processing files on right: {'PASS' if processing_files_present else 'FAIL'}")

            except NoSuchElementException:
                print("⚠️ Processing files section not found on right side")
                self.test_results['processing_files_right'] = False

        except Exception as e:
            print(f"❌ Processing files test failed: {e}")
            self.test_results['processing_files_right'] = False

    def test_action_buttons_present(self):
        """Test that action buttons are present in file details"""
        print("🔘 Testing action buttons in file details...")

        try:
            self.navigate_to_popup()

            # Navigate to upload tab
            upload_tab = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Upload')]"))
            )
            upload_tab.click()
            time.sleep(1)

            # Add mock data
            self.driver.execute_script("""
                const mockInvoices = [{
                    id: 'test-invoice-1',
                    filename: 'test-invoice.pdf',
                    processedAt: new Date().toISOString(),
                    number: 'INV-001',
                    total_gross: '1000.00',
                    currency: 'PLN'
                }];

                if (window.updateContext) {
                    window.updateContext({ invoices: mockInvoices });
                }
            """)

            time.sleep(2)

            # Check for action buttons
            expected_buttons = ['Stop', 'Delete', 'Cancel', 'Repeat', 'Pipeline']
            found_buttons = []

            for button_text in expected_buttons:
                try:
                    button = self.driver.find_element(By.XPATH, f"//button[contains(text(), '{button_text}')]")
                    found_buttons.append(button_text)
                    print(f"✅ Found '{button_text}' button")
                except NoSuchElementException:
                    print(f"❌ Missing '{button_text}' button")

            all_buttons_present = len(found_buttons) == len(expected_buttons)

            self.test_results['action_buttons_present'] = all_buttons_present
            print(f"✅ Action buttons present: {'PASS' if all_buttons_present else 'FAIL'} ({len(found_buttons)}/{len(expected_buttons)})")

        except Exception as e:
            print(f"❌ Action buttons test failed: {e}")
            self.test_results['action_buttons_present'] = False

    def test_vertical_space_utilization(self):
        """Test that recent uploads use available vertical space"""
        print("📏 Testing vertical space utilization...")

        try:
            self.navigate_to_popup()

            # Navigate to upload tab
            upload_tab = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Upload')]"))
            )
            upload_tab.click()
            time.sleep(1)

            # Add many mock files to test scrolling
            self.driver.execute_script("""
                const mockInvoices = Array.from({ length: 15 }, (_, i) => ({
                    id: `test-invoice-${i}`,
                    filename: `test-invoice-${i}.pdf`,
                    processedAt: new Date(Date.now() - i * 60000).toISOString(),
                    number: `INV-${String(i).padStart(3, '0')}`,
                    total_gross: `${(Math.random() * 1000).toFixed(2)}`,
                    currency: 'PLN'
                }));

                if (window.updateContext) {
                    window.updateContext({ invoices: mockInvoices });
                }
            """)

            time.sleep(2)

            # Check for scrollable containers
            scroll_containers = self.driver.find_elements(By.CSS_SELECTOR, '.extension-scroll')

            if scroll_containers:
                print(f"✅ Found {len(scroll_containers)} scrollable container(s)")

                # Test scrolling in the first container
                container = scroll_containers[0]
                initial_scroll = container.get_attribute('scrollTop')

                # Scroll down
                self.driver.execute_script("arguments[0].scrollTop = arguments[0].scrollHeight", container)
                time.sleep(0.5)

                final_scroll = container.get_attribute('scrollTop')

                # Check if scrolling occurred
                scrolling_works = int(final_scroll) > int(initial_scroll)

                print(f"📊 Initial scroll: {initial_scroll}, Final scroll: {final_scroll}")

                self.test_results['vertical_space_utilization'] = scrolling_works
                print(f"✅ Vertical space utilization: {'PASS' if scrolling_works else 'FAIL'}")
            else:
                print("⚠️ No scrollable containers found")
                self.test_results['vertical_space_utilization'] = False

        except Exception as e:
            print(f"❌ Vertical space utilization test failed: {e}")
            self.test_results['vertical_space_utilization'] = False

    def run_all_tests(self):
        """Run all upload tab split layout tests"""
        print("🚀 Starting Upload Tab Split Layout Tests")
        print("=" * 60)

        try:
            self.setup_driver()

            # Run individual tests
            self.test_left_right_split_layout()
            self.test_recent_uploads_summary_left()
            self.test_processing_files_right()
            self.test_action_buttons_present()
            self.test_vertical_space_utilization()

            # Calculate overall layout score
            passed_tests = sum(self.test_results.values())
            total_tests = len(self.test_results)
            overall_score = (passed_tests / total_tests) * 100

            self.test_results['overall_layout'] = overall_score >= 80

            print("\n" + "=" * 60)
            print("📊 TEST RESULTS SUMMARY")
            print("=" * 60)

            for test_name, result in self.test_results.items():
                if test_name != 'overall_layout':
                    status = "✅ PASS" if result else "❌ FAIL"
                    print(f"{test_name.replace('_', ' ').title()}: {status}")

            print(f"\nOverall Score: {overall_score:.1f}% ({passed_tests}/{total_tests} tests passed)")
            print(f"Overall Layout: {'✅ PASS' if self.test_results['overall_layout'] else '❌ FAIL'}")

            # Save results
            with open('tests/selenium/screenshots/upload_tab_split_test_results.json', 'w') as f:
                json.dump({
                    'timestamp': time.time(),
                    'results': self.test_results,
                    'score': overall_score
                }, f, indent=2)

            return self.test_results['overall_layout']

        except Exception as e:
            print(f"❌ Test suite failed: {e}")
            return False

        finally:
            if self.driver:
                self.driver.quit()

if __name__ == "__main__":
    tester = UploadTabSplitLayoutTests()
    success = tester.run_all_tests()
    exit(0 if success else 1)
