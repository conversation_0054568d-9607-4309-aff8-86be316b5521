#!/usr/bin/env python3
"""
Screenshot utilities for Selenium testing
Provides enhanced screenshot capture and comparison functionality
"""

import os
import time
import datetime
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
import json

class ScreenshotUtils:
    def __init__(self, base_dir="tests/selenium/screenshots"):
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(exist_ok=True)
        self.baselines_dir = self.base_dir / "baselines"
        self.baselines_dir.mkdir(exist_ok=True)
        self.reports_dir = self.base_dir / "reports"
        self.reports_dir.mkdir(exist_ok=True)
        
    def capture_with_metadata(self, driver, test_name, description="", metadata=None):
        """Capture screenshot with enhanced metadata"""
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{test_name}_{timestamp}.png"
        filepath = self.base_dir / filename
        
        try:
            # Capture screenshot
            driver.save_screenshot(str(filepath))
            
            # Add metadata overlay if requested
            if metadata:
                self._add_metadata_overlay(filepath, metadata)
            
            # Save metadata file
            metadata_file = filepath.with_suffix('.json')
            screenshot_metadata = {
                "timestamp": datetime.datetime.now().isoformat(),
                "test_name": test_name,
                "description": description,
                "filename": filename,
                "window_size": driver.get_window_size(),
                "url": driver.current_url,
                "title": driver.title,
                "metadata": metadata or {}
            }
            
            with open(metadata_file, 'w') as f:
                json.dump(screenshot_metadata, f, indent=2)
            
            print(f"📸 Enhanced screenshot saved: {filename}")
            if description:
                print(f"   📝 {description}")
                
            return str(filepath)
            
        except Exception as e:
            print(f"❌ Failed to capture enhanced screenshot: {e}")
            return None
    
    def _add_metadata_overlay(self, image_path, metadata):
        """Add metadata overlay to screenshot"""
        try:
            # Open image
            img = Image.open(image_path)
            draw = ImageDraw.Draw(img)
            
            # Try to use a font, fallback to default
            try:
                font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 12)
            except:
                font = ImageFont.load_default()
            
            # Add timestamp overlay
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            text = f"Captured: {timestamp}"
            
            # Add metadata text
            if metadata:
                for key, value in metadata.items():
                    text += f" | {key}: {value}"
            
            # Draw text with background
            text_bbox = draw.textbbox((0, 0), text, font=font)
            text_width = text_bbox[2] - text_bbox[0]
            text_height = text_bbox[3] - text_bbox[1]
            
            # Position at bottom of image
            x = 10
            y = img.height - text_height - 10
            
            # Draw background rectangle
            draw.rectangle([x-5, y-2, x+text_width+5, y+text_height+2], fill=(0, 0, 0, 128))
            
            # Draw text
            draw.text((x, y), text, fill=(255, 255, 255), font=font)
            
            # Save modified image
            img.save(image_path)
            
        except Exception as e:
            print(f"⚠️ Failed to add metadata overlay: {e}")
    
    def compare_with_baseline(self, current_image_path, baseline_name, threshold=0.05):
        """Compare current screenshot with baseline"""
        baseline_path = self.baselines_dir / f"{baseline_name}.png"
        
        if not baseline_path.exists():
            print(f"📋 No baseline found for {baseline_name}, creating new baseline")
            # Copy current image as baseline
            current_img = Image.open(current_image_path)
            current_img.save(baseline_path)
            return True, 0.0, "New baseline created"
        
        try:
            # Load images
            current_img = Image.open(current_image_path)
            baseline_img = Image.open(baseline_path)
            
            # Resize to same dimensions if needed
            if current_img.size != baseline_img.size:
                print(f"⚠️ Size mismatch: current {current_img.size} vs baseline {baseline_img.size}")
                baseline_img = baseline_img.resize(current_img.size)
            
            # Convert to RGB if needed
            if current_img.mode != 'RGB':
                current_img = current_img.convert('RGB')
            if baseline_img.mode != 'RGB':
                baseline_img = baseline_img.convert('RGB')
            
            # Calculate difference
            diff_pixels = 0
            total_pixels = current_img.width * current_img.height
            
            current_pixels = list(current_img.getdata())
            baseline_pixels = list(baseline_img.getdata())
            
            for i, (curr_pixel, base_pixel) in enumerate(zip(current_pixels, baseline_pixels)):
                # Calculate RGB difference
                r_diff = abs(curr_pixel[0] - base_pixel[0])
                g_diff = abs(curr_pixel[1] - base_pixel[1])
                b_diff = abs(curr_pixel[2] - base_pixel[2])
                
                # If any channel differs by more than 10, count as different
                if r_diff > 10 or g_diff > 10 or b_diff > 10:
                    diff_pixels += 1
            
            difference_ratio = diff_pixels / total_pixels
            
            # Generate comparison report
            self._generate_comparison_report(
                current_image_path, baseline_path, baseline_name, 
                difference_ratio, threshold
            )
            
            is_similar = difference_ratio <= threshold
            status = "PASS" if is_similar else "FAIL"
            
            print(f"🔍 Visual comparison ({baseline_name}): {difference_ratio:.2%} difference (threshold: {threshold:.1%}) - {status}")
            
            return is_similar, difference_ratio, f"Difference: {difference_ratio:.2%}"
            
        except Exception as e:
            print(f"❌ Failed to compare with baseline: {e}")
            return False, 1.0, str(e)
    
    def _generate_comparison_report(self, current_path, baseline_path, test_name, difference, threshold):
        """Generate visual comparison report"""
        try:
            # Create side-by-side comparison
            current_img = Image.open(current_path)
            baseline_img = Image.open(baseline_path)
            
            # Ensure same size
            if current_img.size != baseline_img.size:
                baseline_img = baseline_img.resize(current_img.size)
            
            # Create comparison image
            comparison_width = current_img.width * 2 + 20
            comparison_height = current_img.height + 60
            comparison_img = Image.new('RGB', (comparison_width, comparison_height), (255, 255, 255))
            
            # Paste images
            comparison_img.paste(baseline_img, (0, 30))
            comparison_img.paste(current_img, (current_img.width + 20, 30))
            
            # Add labels
            draw = ImageDraw.Draw(comparison_img)
            try:
                font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", 14)
            except:
                font = ImageFont.load_default()
            
            draw.text((10, 5), "BASELINE", fill=(0, 0, 0), font=font)
            draw.text((current_img.width + 30, 5), "CURRENT", fill=(0, 0, 0), font=font)
            
            # Add difference info
            status_color = (0, 128, 0) if difference <= threshold else (255, 0, 0)
            status_text = f"Difference: {difference:.2%} (Threshold: {threshold:.1%})"
            draw.text((10, comparison_height - 25), status_text, fill=status_color, font=font)
            
            # Save comparison report
            report_path = self.reports_dir / f"{test_name}_comparison.png"
            comparison_img.save(report_path)
            
            print(f"📊 Comparison report saved: {report_path.name}")
            
        except Exception as e:
            print(f"⚠️ Failed to generate comparison report: {e}")
    
    def create_test_summary_image(self, test_results, output_name="test_summary"):
        """Create visual summary of test results"""
        try:
            # Image dimensions
            width = 800
            height = 600
            img = Image.new('RGB', (width, height), (255, 255, 255))
            draw = ImageDraw.Draw(img)
            
            # Try to load fonts
            try:
                title_font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", 24)
                header_font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", 16)
                text_font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 12)
            except:
                title_font = ImageFont.load_default()
                header_font = ImageFont.load_default()
                text_font = ImageFont.load_default()
            
            # Title
            title = "Chrome Extension Test Results"
            draw.text((50, 30), title, fill=(0, 0, 0), font=title_font)
            
            # Timestamp
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            draw.text((50, 70), f"Generated: {timestamp}", fill=(100, 100, 100), font=text_font)
            
            # Summary stats
            passed_tests = sum(1 for _, passed, _ in test_results if passed)
            total_tests = len(test_results)
            success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
            
            summary_text = f"Success Rate: {passed_tests}/{total_tests} ({success_rate:.1f}%)"
            summary_color = (0, 128, 0) if success_rate >= 75 else (255, 0, 0)
            draw.text((50, 110), summary_text, fill=summary_color, font=header_font)
            
            # Individual test results
            y_pos = 150
            for test_name, passed, details in test_results:
                status_text = "✓ PASS" if passed else "✗ FAIL"
                status_color = (0, 128, 0) if passed else (255, 0, 0)
                
                draw.text((50, y_pos), status_text, fill=status_color, font=text_font)
                draw.text((120, y_pos), test_name, fill=(0, 0, 0), font=text_font)
                
                # Details on next line
                if details and len(details) < 60:
                    draw.text((120, y_pos + 15), details, fill=(100, 100, 100), font=text_font)
                    y_pos += 35
                else:
                    y_pos += 25
                
                if y_pos > height - 50:
                    break
            
            # Save summary image
            summary_path = self.reports_dir / f"{output_name}.png"
            img.save(summary_path)
            
            print(f"📊 Test summary image saved: {summary_path.name}")
            return str(summary_path)
            
        except Exception as e:
            print(f"❌ Failed to create test summary image: {e}")
            return None
    
    def cleanup_old_screenshots(self, days_old=7):
        """Clean up screenshots older than specified days"""
        try:
            cutoff_time = time.time() - (days_old * 24 * 60 * 60)
            cleaned_count = 0
            
            for file_path in self.base_dir.glob("*.png"):
                if file_path.stat().st_mtime < cutoff_time:
                    file_path.unlink()
                    # Also remove associated JSON metadata
                    json_path = file_path.with_suffix('.json')
                    if json_path.exists():
                        json_path.unlink()
                    cleaned_count += 1
            
            if cleaned_count > 0:
                print(f"🧹 Cleaned up {cleaned_count} old screenshots")
            
        except Exception as e:
            print(f"⚠️ Failed to cleanup old screenshots: {e}")

# Utility functions for easy import
def capture_screenshot(driver, test_name, description="", metadata=None):
    """Quick screenshot capture function"""
    utils = ScreenshotUtils()
    return utils.capture_with_metadata(driver, test_name, description, metadata)

def compare_screenshot(current_path, baseline_name, threshold=0.05):
    """Quick screenshot comparison function"""
    utils = ScreenshotUtils()
    return utils.compare_with_baseline(current_path, baseline_name, threshold)
