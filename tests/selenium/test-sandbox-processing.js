#!/usr/bin/env node

/**
 * Selenium Test for Tesseract.js Sandbox Processing
 * Tests the sandbox implementation for OCR processing
 */

const { Builder, By, until } = require('selenium-webdriver');
const chrome = require('selenium-webdriver/chrome');
const fs = require('fs');
const path = require('path');

class SandboxProcessingTest {
  constructor() {
    this.driver = null;
    this.testResults = {
      sandboxLoading: false,
      sandboxCommunication: false,
      ocrProcessing: false,
      noCSPViolations: true,
      errors: []
    };
  }

  async setup() {
    console.log('🔧 Setting up Chrome driver for sandbox testing...');

    const options = new chrome.Options();
    options.addArguments('--no-sandbox');
    options.addArguments('--disable-dev-shm-usage');
    options.addArguments('--disable-web-security');
    options.addArguments('--allow-running-insecure-content');
    options.addArguments('--disable-features=VizDisplayCompositor');

    // Enable extension loading
    const extensionPath = path.resolve(__dirname, '../../dist');
    options.addArguments(`--load-extension=${extensionPath}`);

    this.driver = await new Builder()
      .forBrowser('chrome')
      .setChromeOptions(options)
      .build();

    console.log('✅ Chrome driver set up successfully');
  }

  async testSandboxLoading() {
    console.log('\n🧪 Test 1: Sandbox Loading');

    try {
      // Navigate to sandbox directly
      const sandboxUrl = `file://${path.resolve(__dirname, '../../dist/sandbox/sandbox.html')}`;
      await this.driver.get(sandboxUrl);

      // Wait for sandbox to load
      await this.driver.wait(until.titleContains('MVAT Tesseract.js Sandbox'), 10000);

      // Check if sandbox elements are present
      const statusElement = await this.driver.findElement(By.id('status'));
      const statusText = await statusElement.getText();

      console.log(`📝 Sandbox status: ${statusText}`);

      if (statusText.includes('Sandbox ready') || statusText.includes('Initializing')) {
        this.testResults.sandboxLoading = true;
        console.log('✅ Sandbox loaded successfully');
      } else {
        console.log('❌ Sandbox not ready');
      }

      // Take screenshot
      const screenshot = await this.driver.takeScreenshot();
      const screenshotPath = path.join(__dirname, 'screenshots', `sandbox_loading_${this.getTimestamp()}.png`);
      fs.writeFileSync(screenshotPath, screenshot, 'base64');
      console.log(`📸 Screenshot saved: ${path.basename(screenshotPath)}`);

    } catch (error) {
      console.log(`❌ Sandbox loading test failed: ${error.message}`);
      this.testResults.errors.push(`Sandbox loading: ${error.message}`);
    }
  }

  async testSandboxCommunication() {
    console.log('\n🧪 Test 2: Sandbox Communication');

    try {
      // Navigate to popup
      const popupUrl = `file://${path.resolve(__dirname, '../../dist/popup.html')}`;
      await this.driver.get(popupUrl);

      // Wait for React app to load
      await this.driver.wait(until.elementLocated(By.id('root')), 10000);

      // Check console for sandbox communication
      const logs = await this.driver.manage().logs().get('browser');
      const sandboxLogs = logs.filter(log =>
        log.message.includes('Sandbox') ||
        log.message.includes('sandbox') ||
        log.message.includes('SANDBOX_READY')
      );

      console.log(`📝 Found ${sandboxLogs.length} sandbox-related logs`);

      if (sandboxLogs.length > 0) {
        this.testResults.sandboxCommunication = true;
        console.log('✅ Sandbox communication detected');
        sandboxLogs.forEach(log => console.log(`   📋 ${log.message}`));
      } else {
        console.log('⚠️ No sandbox communication logs found');
      }

      // Take screenshot
      const screenshot = await this.driver.takeScreenshot();
      const screenshotPath = path.join(__dirname, 'screenshots', `sandbox_communication_${this.getTimestamp()}.png`);
      fs.writeFileSync(screenshotPath, screenshot, 'base64');
      console.log(`📸 Screenshot saved: ${path.basename(screenshotPath)}`);

    } catch (error) {
      console.log(`❌ Sandbox communication test failed: ${error.message}`);
      this.testResults.errors.push(`Sandbox communication: ${error.message}`);
    }
  }

  async testCSPViolations() {
    console.log('\n🧪 Test 3: CSP Violations Check');

    try {
      // Get all console logs
      const logs = await this.driver.manage().logs().get('browser');

      // Check for CSP violations
      const cspViolations = logs.filter(log =>
        log.message.includes('Content Security Policy') ||
        log.message.includes('CSP') ||
        log.message.includes('Refused to load') ||
        log.message.includes('cdn.jsdelivr.net')
      );

      console.log(`📝 Found ${cspViolations.length} CSP-related logs`);

      if (cspViolations.length === 0) {
        console.log('✅ No CSP violations detected');
      } else {
        this.testResults.noCSPViolations = false;
        console.log('❌ CSP violations found:');
        cspViolations.forEach(log => console.log(`   📋 ${log.message}`));
      }

      // Check for Tesseract-specific errors
      const tesseractErrors = logs.filter(log =>
        log.message.includes('tesseract') ||
        log.message.includes('Tesseract') ||
        log.message.includes('worker.min.js')
      );

      console.log(`📝 Found ${tesseractErrors.length} Tesseract-related logs`);
      tesseractErrors.forEach(log => console.log(`   📋 ${log.message}`));

    } catch (error) {
      console.log(`❌ CSP violations check failed: ${error.message}`);
      this.testResults.errors.push(`CSP check: ${error.message}`);
    }
  }

  async testOCRProcessing() {
    console.log('\n🧪 Test 4: OCR Processing Simulation');

    try {
      // Navigate back to sandbox
      const sandboxUrl = `file://${path.resolve(__dirname, '../../dist/sandbox/sandbox.html')}`;
      await this.driver.get(sandboxUrl);

      // Wait for sandbox to be ready
      await this.driver.wait(until.titleContains('MVAT Tesseract.js Sandbox'), 10000);

      // Execute JavaScript to test sandbox functionality
      const testResult = await this.driver.executeScript(`
        return new Promise((resolve) => {
          try {
            // Check if Tesseract is available
            if (typeof Tesseract !== 'undefined') {
              resolve({
                success: true,
                tesseractAvailable: true,
                message: 'Tesseract.js loaded successfully in sandbox'
              });
            } else {
              resolve({
                success: false,
                tesseractAvailable: false,
                message: 'Tesseract.js not available in sandbox'
              });
            }
          } catch (error) {
            resolve({
              success: false,
              error: error.message
            });
          }
        });
      `);

      console.log(`📝 OCR test result: ${JSON.stringify(testResult)}`);

      if (testResult.success && testResult.tesseractAvailable) {
        this.testResults.ocrProcessing = true;
        console.log('✅ OCR processing environment ready');
      } else {
        console.log('❌ OCR processing environment not ready');
      }

      // Take screenshot
      const screenshot = await this.driver.takeScreenshot();
      const screenshotPath = path.join(__dirname, 'screenshots', `ocr_processing_${this.getTimestamp()}.png`);
      fs.writeFileSync(screenshotPath, screenshot, 'base64');
      console.log(`📸 Screenshot saved: ${path.basename(screenshotPath)}`);

    } catch (error) {
      console.log(`❌ OCR processing test failed: ${error.message}`);
      this.testResults.errors.push(`OCR processing: ${error.message}`);
    }
  }

  async generateReport() {
    console.log('\n📊 GENERATING SANDBOX TEST REPORT');
    console.log('============================================================');

    const totalTests = 4;
    const passedTests = Object.values(this.testResults).filter(result => result === true).length;
    const successRate = (passedTests / totalTests * 100).toFixed(1);

    console.log(`📈 Overall Success Rate: ${passedTests}/${totalTests} (${successRate}%)`);
    console.log('\n📋 Detailed Results:');
    console.log(`   ${this.testResults.sandboxLoading ? '✅' : '❌'} Sandbox Loading: ${this.testResults.sandboxLoading ? 'PASS' : 'FAIL'}`);
    console.log(`   ${this.testResults.sandboxCommunication ? '✅' : '❌'} Sandbox Communication: ${this.testResults.sandboxCommunication ? 'PASS' : 'FAIL'}`);
    console.log(`   ${this.testResults.noCSPViolations ? '✅' : '❌'} No CSP Violations: ${this.testResults.noCSPViolations ? 'PASS' : 'FAIL'}`);
    console.log(`   ${this.testResults.ocrProcessing ? '✅' : '❌'} OCR Processing Ready: ${this.testResults.ocrProcessing ? 'PASS' : 'FAIL'}`);

    if (this.testResults.errors.length > 0) {
      console.log('\n❌ Errors encountered:');
      this.testResults.errors.forEach(error => console.log(`   • ${error}`));
    }

    // Save JSON report
    const reportPath = path.join(__dirname, 'screenshots', 'sandbox_test_report.json');
    fs.writeFileSync(reportPath, JSON.stringify({
      timestamp: new Date().toISOString(),
      successRate: successRate,
      results: this.testResults
    }, null, 2));

    console.log(`📄 JSON report saved: ${path.basename(reportPath)}`);

    return passedTests === totalTests;
  }

  getTimestamp() {
    const now = new Date();
    return now.toISOString().replace(/[:.]/g, '').slice(0, 15);
  }

  async cleanup() {
    if (this.driver) {
      await this.driver.quit();
      console.log('🧹 Chrome driver cleaned up');
    }
  }

  async run() {
    try {
      await this.setup();
      await this.testSandboxLoading();
      await this.testSandboxCommunication();
      await this.testCSPViolations();
      await this.testOCRProcessing();

      const success = await this.generateReport();
      return success ? 0 : 1;
    } catch (error) {
      console.error('❌ Test suite failed:', error);
      return 1;
    } finally {
      await this.cleanup();
    }
  }
}

// Run the test if called directly
if (require.main === module) {
  const test = new SandboxProcessingTest();
  test.run().then(exitCode => {
    process.exit(exitCode);
  }).catch(error => {
    console.error('❌ Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = SandboxProcessingTest;
