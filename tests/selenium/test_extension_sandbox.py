#!/usr/bin/env python3
"""
Test Chrome extension sandbox communication
Loads the extension properly in Chrome and tests sandbox communication
"""

import time
import os
import tempfile
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC

def test_extension_sandbox():
    """Test sandbox communication in actual Chrome extension context"""
    
    # Setup Chrome with extension loaded
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-web-security')
    
    # Load the extension
    extension_path = '/W/B2B/cloudforge/chrome-extensions/mvat/dist'
    chrome_options.add_argument(f'--load-extension={extension_path}')
    chrome_options.add_argument('--disable-extensions-except={extension_path}')
    
    driver = webdriver.Chrome(options=chrome_options)

    try:
        print('🔍 Testing extension sandbox communication...')
        
        # Navigate to extensions page to get extension ID
        driver.get('chrome://extensions/')
        time.sleep(2)
        
        # Enable developer mode if not already enabled
        try:
            dev_mode_toggle = driver.find_element(By.CSS_SELECTOR, '#devMode')
            if not dev_mode_toggle.is_selected():
                dev_mode_toggle.click()
                time.sleep(1)
        except:
            print('⚠️ Could not toggle developer mode')
        
        # Find the MVAT extension
        extension_cards = driver.find_elements(By.CSS_SELECTOR, 'extensions-item')
        mvat_extension_id = None
        
        for card in extension_cards:
            try:
                name_element = card.find_element(By.CSS_SELECTOR, '#name')
                if 'MVAT' in name_element.text:
                    # Get extension ID from the card
                    extension_id = card.get_attribute('id')
                    mvat_extension_id = extension_id
                    print(f'✅ Found MVAT extension: {extension_id}')
                    break
            except:
                continue
        
        if not mvat_extension_id:
            print('❌ MVAT extension not found')
            return False
        
        # Navigate to extension popup
        popup_url = f'chrome-extension://{mvat_extension_id}/popup.html'
        print(f'🔗 Opening popup: {popup_url}')
        driver.get(popup_url)
        
        # Wait for popup to load
        time.sleep(3)
        
        print('📋 Popup loaded, checking React app...')
        
        # Check if React app is loaded
        try:
            root_element = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.ID, 'root'))
            )
            print('✅ React root element found')
        except:
            print('❌ React root element not found')
            return False
        
        # Wait for React app to initialize
        time.sleep(5)
        
        # Check console logs
        logs = driver.get_log('browser')
        print(f'📊 Initial console logs: {len(logs)} entries')
        
        initialization_logs = []
        error_logs = []
        
        for log in logs:
            message = log['message']
            level = log['level']
            
            if any(keyword in message.lower() for keyword in ['mvat', 'initialized', 'loaded', 'ready']):
                initialization_logs.append(f"[{level}] {message}")
                
            if level == 'SEVERE' or 'error' in message.lower():
                error_logs.append(f"[{level}] {message}")
        
        print(f'🚀 Initialization logs: {len(initialization_logs)}')
        for log in initialization_logs:
            print(f'   {log}')
            
        print(f'❌ Error logs: {len(error_logs)}')
        for log in error_logs:
            print(f'   {log}')
        
        # Try to navigate to upload page
        try:
            upload_link = driver.find_element(By.XPATH, '//*[contains(text(), \"Upload\")]')
            print(f'📤 Found upload link: {upload_link.text}')
            upload_link.click()
            time.sleep(2)
            print('✅ Navigated to upload page')
        except:
            print('⚠️ Upload link not found, checking if already on upload page')
        
        # Look for file input
        try:
            file_input = driver.find_element(By.CSS_SELECTOR, 'input[type=\"file\"]')
            print('✅ File input found')
            
            # Create a simple test file
            test_content = '''INVOICE
Invoice Number: TEST-001
Date: 2025-01-01
From: Test Company
To: Test Customer
Amount: $100.00
VAT: $20.00
Total: $120.00'''
            
            temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False)
            temp_file.write(test_content)
            temp_file.close()
            
            print(f'📄 Created test file: {temp_file.name}')
            
            # Upload the file
            print('📤 Uploading test file...')
            file_input.send_keys(temp_file.name)
            
            # Wait for processing
            time.sleep(5)
            
            # Check for sandbox activity
            new_logs = driver.get_log('browser')
            processing_logs = new_logs[len(logs):]
            
            sandbox_logs = []
            communication_logs = []
            
            for log in processing_logs:
                message = log['message']
                level = log['level']
                
                if any(keyword in message.lower() for keyword in ['sandbox', 'tesseract', 'ocr']):
                    sandbox_logs.append(f"[{level}] {message}")
                
                if any(keyword in message.lower() for keyword in ['message', 'postmessage', 'ready', 'communication']):
                    communication_logs.append(f"[{level}] {message}")
            
            print(f'📦 Sandbox activity logs: {len(sandbox_logs)}')
            for log in sandbox_logs:
                print(f'   {log}')
                
            print(f'📨 Communication logs: {len(communication_logs)}')
            for log in communication_logs:
                print(f'   {log}')
            
            # Check for iframes
            iframes = driver.find_elements(By.TAG_NAME, 'iframe')
            print(f'📦 Found {len(iframes)} iframes')
            
            for i, iframe in enumerate(iframes):
                src = iframe.get_attribute('src')
                sandbox = iframe.get_attribute('sandbox')
                print(f'   iframe {i}: src={src}, sandbox={sandbox}')
            
            # Cleanup
            os.unlink(temp_file.name)
            
            # Take screenshot
            driver.save_screenshot('/W/B2B/cloudforge/chrome-extensions/mvat/tests/selenium/screenshots/extension_sandbox_test.png')
            print('📸 Extension sandbox test screenshot saved')
            
            # Determine result
            has_sandbox_activity = len(sandbox_logs) > 0 or len(iframes) > 0
            has_communication = len(communication_logs) > 0
            
            if has_sandbox_activity or has_communication:
                print('✅ Extension sandbox test PASSED - sandbox activity detected')
                return True
            else:
                print('⚠️ Extension sandbox test INCONCLUSIVE - no clear sandbox activity')
                return False
                
        except Exception as e:
            print(f'❌ File upload test failed: {e}')
            return False
        
    except Exception as e:
        print(f'❌ Extension test failed: {e}')
        return False
        
    finally:
        driver.quit()

if __name__ == '__main__':
    success = test_extension_sandbox()
    exit(0 if success else 1)
