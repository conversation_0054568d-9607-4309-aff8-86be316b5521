#!/usr/bin/env python3
"""
Pipeline UI Improvements Selenium Tests
Tests for styling consistency, performance, and user experience improvements
"""

import time
import json
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON>river<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.chrome.options import Options

class PipelineUITests:
    def __init__(self):
        self.driver = None
        self.extension_id = None
        self.test_results = {
            'styling_consistency': False,
            'drag_drop_performance': False,
            'resize_performance': False,
            'pipeline_window_functionality': False,
            'recent_uploads_layout': False,
            'overall_performance': False
        }
        
    def setup_driver(self):
        """Setup Chrome driver with extension loaded"""
        chrome_options = Options()
        
        # Load extension
        extension_path = os.path.abspath('dist/dev')
        chrome_options.add_argument(f'--load-extension={extension_path}')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        # Use Chrome 135 for compatibility
        chrome_binary = 'tests/selenium/chrome-135/chrome-linux64/chrome'
        if os.path.exists(chrome_binary):
            chrome_options.binary_location = chrome_binary
            
        self.driver = webdriver.Chrome(options=chrome_options)
        self.driver.set_window_size(1200, 800)
        
        # Get extension ID
        self.driver.get('chrome://extensions/')
        time.sleep(2)
        
        # Enable developer mode and get extension ID
        try:
            dev_mode = self.driver.find_element(By.ID, 'devMode')
            if not dev_mode.is_selected():
                dev_mode.click()
                time.sleep(1)
        except:
            pass
            
        # Find MVAT extension
        extensions = self.driver.find_elements(By.CSS_SELECTOR, 'extensions-item')
        for ext in extensions:
            try:
                name = ext.find_element(By.CSS_SELECTOR, '#name').text
                if 'MVAT' in name:
                    self.extension_id = ext.get_attribute('id')
                    break
            except:
                continue
                
        if not self.extension_id:
            raise Exception("MVAT extension not found")
            
        print(f"✅ Extension loaded with ID: {self.extension_id}")
        
    def navigate_to_popup(self):
        """Navigate to extension popup"""
        popup_url = f'chrome-extension://{self.extension_id}/popup.html'
        self.driver.get(popup_url)
        
        # Wait for popup to load
        WebDriverWait(self.driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, '[data-testid="mvat-app"], #root'))
        )
        time.sleep(1)
        
    def test_styling_consistency(self):
        """Test that pipeline styling matches extension design"""
        print("🎨 Testing styling consistency...")
        
        try:
            self.navigate_to_popup()
            
            # Navigate to upload tab
            upload_tab = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Upload')]"))
            )
            upload_tab.click()
            time.sleep(1)
            
            # Check for consistent color scheme
            pipeline_elements = self.driver.find_elements(By.CSS_SELECTOR, '[class*="pipeline"], [class*="Pipeline"]')
            
            consistent_styling = True
            for element in pipeline_elements:
                # Check computed styles
                font_family = self.driver.execute_script(
                    "return window.getComputedStyle(arguments[0]).fontFamily", element
                )
                
                # Should use Inter font family
                if 'Inter' not in font_family and 'system-ui' not in font_family:
                    print(f"❌ Inconsistent font family: {font_family}")
                    consistent_styling = False
                    
            # Check button styling consistency
            buttons = self.driver.find_elements(By.CSS_SELECTOR, 'button')
            for button in buttons[:5]:  # Check first 5 buttons
                border_radius = self.driver.execute_script(
                    "return window.getComputedStyle(arguments[0]).borderRadius", button
                )
                # Should have consistent border radius
                if border_radius not in ['0px', '4px', '6px', '8px', '0.25rem', '0.375rem', '0.5rem']:
                    print(f"⚠️ Unusual border radius: {border_radius}")
                    
            self.test_results['styling_consistency'] = consistent_styling
            print(f"✅ Styling consistency: {'PASS' if consistent_styling else 'FAIL'}")
            
        except Exception as e:
            print(f"❌ Styling consistency test failed: {e}")
            self.test_results['styling_consistency'] = False
            
    def test_drag_drop_performance(self):
        """Test drag and drop event performance"""
        print("🖱️ Testing drag and drop performance...")
        
        try:
            self.navigate_to_popup()
            
            # Navigate to upload tab
            upload_tab = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Upload')]"))
            )
            upload_tab.click()
            time.sleep(1)
            
            # Find drag drop area
            drag_drop_area = WebDriverWait(self.driver, 5).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, '[data-testid="drag-drop-upload"], .dropzone, [class*="drag"]'))
            )
            
            # Test multiple drag events and measure performance
            event_times = []
            for i in range(5):
                start_time = time.time()
                
                # Simulate drag enter
                self.driver.execute_script("""
                    var event = new DragEvent('dragenter', {
                        bubbles: true,
                        cancelable: true,
                        dataTransfer: new DataTransfer()
                    });
                    arguments[0].dispatchEvent(event);
                """, drag_drop_area)
                
                time.sleep(0.05)  # 50ms delay
                
                # Simulate drag leave
                self.driver.execute_script("""
                    var event = new DragEvent('dragleave', {
                        bubbles: true,
                        cancelable: true
                    });
                    arguments[0].dispatchEvent(event);
                """, drag_drop_area)
                
                end_time = time.time()
                event_times.append((end_time - start_time) * 1000)  # Convert to ms
                
            avg_time = sum(event_times) / len(event_times)
            max_time = max(event_times)
            
            print(f"📊 Drag event times: {[f'{t:.1f}ms' for t in event_times]}")
            print(f"📊 Average: {avg_time:.1f}ms, Max: {max_time:.1f}ms")
            
            # Performance should be good (under 100ms average, 150ms max)
            performance_good = avg_time < 100 and max_time < 150
            
            self.test_results['drag_drop_performance'] = performance_good
            print(f"✅ Drag drop performance: {'PASS' if performance_good else 'FAIL'}")
            
        except Exception as e:
            print(f"❌ Drag drop performance test failed: {e}")
            self.test_results['drag_drop_performance'] = False
            
    def test_resize_performance(self):
        """Test popup resize performance"""
        print("📏 Testing resize performance...")
        
        try:
            self.navigate_to_popup()
            
            # Test different window sizes
            sizes = [(400, 600), (600, 800), (800, 600), (400, 600)]
            resize_times = []
            
            for width, height in sizes:
                start_time = time.time()
                
                self.driver.set_window_size(width, height)
                time.sleep(0.1)  # Allow for layout
                
                # Check that elements are still visible
                try:
                    WebDriverWait(self.driver, 2).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, '#root, [data-testid="mvat-app"]'))
                    )
                except TimeoutException:
                    print(f"⚠️ Elements not visible after resize to {width}x{height}")
                    
                end_time = time.time()
                resize_times.append((end_time - start_time) * 1000)
                
            avg_resize_time = sum(resize_times) / len(resize_times)
            max_resize_time = max(resize_times)
            
            print(f"📊 Resize times: {[f'{t:.1f}ms' for t in resize_times]}")
            print(f"📊 Average: {avg_resize_time:.1f}ms, Max: {max_resize_time:.1f}ms")
            
            # Resize should be fast (under 200ms average, 300ms max)
            performance_good = avg_resize_time < 200 and max_resize_time < 300
            
            self.test_results['resize_performance'] = performance_good
            print(f"✅ Resize performance: {'PASS' if performance_good else 'FAIL'}")
            
        except Exception as e:
            print(f"❌ Resize performance test failed: {e}")
            self.test_results['resize_performance'] = False
            
    def test_pipeline_window_functionality(self):
        """Test pipeline window opening functionality"""
        print("🪟 Testing pipeline window functionality...")
        
        try:
            self.navigate_to_popup()
            
            # Navigate to upload tab
            upload_tab = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Upload')]"))
            )
            upload_tab.click()
            time.sleep(1)
            
            # Add mock data to create recent uploads
            self.driver.execute_script("""
                // Mock invoice data
                window.mockInvoice = {
                    id: 'test-invoice-1',
                    filename: 'test-invoice.pdf',
                    processedAt: new Date().toISOString(),
                    number: 'INV-001',
                    total_gross: '1000.00',
                    currency: 'PLN'
                };
                
                // Try to update context if available
                if (window.updateContext) {
                    window.updateContext({ invoices: [window.mockInvoice] });
                }
            """)
            
            time.sleep(2)
            
            # Look for pipeline buttons
            pipeline_buttons = self.driver.find_elements(By.XPATH, "//button[contains(text(), 'Pipeline')]")
            
            if pipeline_buttons:
                print(f"📋 Found {len(pipeline_buttons)} pipeline button(s)")
                
                # Test clicking pipeline button
                start_time = time.time()
                pipeline_buttons[0].click()
                
                # Check for window.open call or other indicators
                time.sleep(0.5)
                
                end_time = time.time()
                click_time = (end_time - start_time) * 1000
                
                print(f"📊 Pipeline button click time: {click_time:.1f}ms")
                
                # Should respond quickly (under 500ms)
                performance_good = click_time < 500
                
                self.test_results['pipeline_window_functionality'] = performance_good
                print(f"✅ Pipeline window functionality: {'PASS' if performance_good else 'FAIL'}")
            else:
                print("⚠️ No pipeline buttons found - may need mock data")
                self.test_results['pipeline_window_functionality'] = False
                
        except Exception as e:
            print(f"❌ Pipeline window functionality test failed: {e}")
            self.test_results['pipeline_window_functionality'] = False
            
    def test_recent_uploads_layout(self):
        """Test recent uploads layout and space utilization"""
        print("📋 Testing recent uploads layout...")
        
        try:
            self.navigate_to_popup()
            
            # Navigate to upload tab
            upload_tab = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Upload')]"))
            )
            upload_tab.click()
            time.sleep(1)
            
            # Add multiple mock invoices
            self.driver.execute_script("""
                const mockInvoices = Array.from({ length: 10 }, (_, i) => ({
                    id: `test-invoice-${i}`,
                    filename: `test-invoice-${i}.pdf`,
                    processedAt: new Date(Date.now() - i * 60000).toISOString(),
                    number: `INV-${String(i).padStart(3, '0')}`,
                    total_gross: `${(Math.random() * 1000).toFixed(2)}`,
                    currency: 'PLN'
                }));
                
                if (window.updateContext) {
                    window.updateContext({ invoices: mockInvoices });
                }
            """)
            
            time.sleep(2)
            
            # Check for recent uploads section
            try:
                recent_uploads = self.driver.find_element(By.XPATH, "//*[contains(text(), 'Recent Uploads')]")
                print("✅ Recent uploads section found")
                
                # Check for scrollable container
                scroll_containers = self.driver.find_elements(By.CSS_SELECTOR, '.extension-scroll, [class*="overflow-y-auto"]')
                
                if scroll_containers:
                    print(f"✅ Found {len(scroll_containers)} scrollable container(s)")
                    
                    # Test scrolling performance
                    container = scroll_containers[0]
                    start_time = time.time()
                    
                    # Scroll down and up
                    self.driver.execute_script("arguments[0].scrollTop = arguments[0].scrollHeight", container)
                    time.sleep(0.1)
                    self.driver.execute_script("arguments[0].scrollTop = 0", container)
                    
                    end_time = time.time()
                    scroll_time = (end_time - start_time) * 1000
                    
                    print(f"📊 Scroll performance: {scroll_time:.1f}ms")
                    
                    # Should scroll smoothly (under 200ms)
                    performance_good = scroll_time < 200
                    
                    self.test_results['recent_uploads_layout'] = performance_good
                    print(f"✅ Recent uploads layout: {'PASS' if performance_good else 'FAIL'}")
                else:
                    print("⚠️ No scrollable containers found")
                    self.test_results['recent_uploads_layout'] = False
                    
            except NoSuchElementException:
                print("⚠️ Recent uploads section not found")
                self.test_results['recent_uploads_layout'] = False
                
        except Exception as e:
            print(f"❌ Recent uploads layout test failed: {e}")
            self.test_results['recent_uploads_layout'] = False
            
    def run_all_tests(self):
        """Run all UI improvement tests"""
        print("🚀 Starting Pipeline UI Improvement Tests")
        print("=" * 60)
        
        try:
            self.setup_driver()
            
            # Run individual tests
            self.test_styling_consistency()
            self.test_drag_drop_performance()
            self.test_resize_performance()
            self.test_pipeline_window_functionality()
            self.test_recent_uploads_layout()
            
            # Calculate overall performance
            passed_tests = sum(self.test_results.values())
            total_tests = len(self.test_results)
            overall_score = (passed_tests / total_tests) * 100
            
            self.test_results['overall_performance'] = overall_score >= 80
            
            print("\n" + "=" * 60)
            print("📊 TEST RESULTS SUMMARY")
            print("=" * 60)
            
            for test_name, result in self.test_results.items():
                if test_name != 'overall_performance':
                    status = "✅ PASS" if result else "❌ FAIL"
                    print(f"{test_name.replace('_', ' ').title()}: {status}")
                    
            print(f"\nOverall Score: {overall_score:.1f}% ({passed_tests}/{total_tests} tests passed)")
            print(f"Overall Performance: {'✅ PASS' if self.test_results['overall_performance'] else '❌ FAIL'}")
            
            # Save results
            with open('tests/selenium/screenshots/pipeline_ui_test_results.json', 'w') as f:
                json.dump({
                    'timestamp': time.time(),
                    'results': self.test_results,
                    'score': overall_score
                }, f, indent=2)
                
            return self.test_results['overall_performance']
            
        except Exception as e:
            print(f"❌ Test suite failed: {e}")
            return False
            
        finally:
            if self.driver:
                self.driver.quit()

if __name__ == "__main__":
    tester = PipelineUITests()
    success = tester.run_all_tests()
    exit(0 if success else 1)
