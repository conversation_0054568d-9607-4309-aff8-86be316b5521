#!/usr/bin/env python3
"""
Right Side Collapsible Cards Selenium Tests
Tests for the collapsible cards on the right side of the upload tab
"""

import time
import json
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.chrome.options import Options

class RightSideCollapsibleCardsTests:
    def __init__(self):
        self.driver = None
        self.extension_id = None
        self.test_results = {
            'cards_folded_by_default': False,
            'expand_functionality': False,
            'collapse_functionality': False,
            'arrow_rotation': False,
            'details_visibility': False,
            'overall_success': False
        }
        
    def setup_driver(self):
        """Setup Chrome driver with extension loaded"""
        chrome_options = Options()
        
        # Load extension
        extension_path = os.path.abspath('dist/dev')
        chrome_options.add_argument(f'--load-extension={extension_path}')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        # Use Chrome 135 for compatibility
        chrome_binary = 'tests/selenium/chrome-135/chrome-linux64/chrome'
        if os.path.exists(chrome_binary):
            chrome_options.binary_location = chrome_binary
            
        self.driver = webdriver.Chrome(options=chrome_options)
        self.driver.set_window_size(1200, 800)
        
        # Get extension ID using chrome.management API
        self.driver.get('chrome://extensions/')
        time.sleep(2)
        
        try:
            extensions_data = self.driver.execute_script("""
                return new Promise((resolve) => {
                    chrome.management.getAll((extensions) => {
                        resolve(extensions);
                    });
                });
            """)
            
            for ext in extensions_data:
                if 'MVAT' in ext.get('name', ''):
                    self.extension_id = ext['id']
                    break
                    
        except Exception as e:
            print(f"⚠️ Could not get extensions via management API: {e}")
            
        if not self.extension_id:
            raise Exception("MVAT extension not found")
            
        print(f"✅ Extension loaded with ID: {self.extension_id}")
        
    def navigate_to_popup(self):
        """Navigate to extension popup"""
        popup_url = f'chrome-extension://{self.extension_id}/popup.html'
        self.driver.get(popup_url)
        
        # Wait for popup to load
        WebDriverWait(self.driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, '[data-testid="mvat-app"], #root'))
        )
        time.sleep(1)
        
    def add_mock_invoices(self):
        """Add mock invoice data for testing"""
        self.driver.execute_script("""
            const mockInvoices = Array.from({ length: 3 }, (_, i) => ({
                id: `test-invoice-${i}`,
                filename: `test-invoice-${i}.pdf`,
                processedAt: new Date(Date.now() - i * 60000).toISOString(),
                number: `INV-${String(i).padStart(3, '0')}`,
                total_gross: `${(Math.random() * 1000).toFixed(2)}`,
                currency: 'PLN'
            }));
            
            if (window.updateContext) {
                window.updateContext({ invoices: mockInvoices });
            }
        """)
        time.sleep(2)
        
    def test_cards_folded_by_default(self):
        """Test that cards on the right side are folded by default"""
        print("📁 Testing cards folded by default...")
        
        try:
            self.navigate_to_popup()
            
            # Navigate to upload tab
            upload_tab = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Upload')]"))
            )
            upload_tab.click()
            time.sleep(1)
            
            # Add mock data
            self.add_mock_invoices()
            
            # Check for cards on the right side
            cards = self.driver.find_elements(By.CSS_SELECTOR, '.w-1\\/2:last-child .p-4.bg-white.border')
            print(f"📊 Found {len(cards)} cards on right side")
            
            if len(cards) > 0:
                # Check if detailed sections are hidden by default
                folded_count = 0
                for card in cards:
                    # Look for validation results, security scan, file information sections
                    detail_sections = card.find_elements(By.XPATH, ".//div[contains(text(), 'Validation Results') or contains(text(), 'Security Scan') or contains(text(), 'File Information')]")
                    if len(detail_sections) == 0:
                        folded_count += 1
                
                cards_folded = folded_count == len(cards)
                self.test_results['cards_folded_by_default'] = cards_folded
                print(f"✅ Cards folded by default: {'PASS' if cards_folded else 'FAIL'} ({folded_count}/{len(cards)} folded)")
            else:
                print("❌ No cards found on right side")
                self.test_results['cards_folded_by_default'] = False
                
        except Exception as e:
            print(f"❌ Cards folded by default test failed: {e}")
            self.test_results['cards_folded_by_default'] = False
            
    def test_expand_functionality(self):
        """Test that clicking a card header expands it"""
        print("📂 Testing expand functionality...")
        
        try:
            self.navigate_to_popup()
            
            # Navigate to upload tab
            upload_tab = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Upload')]"))
            )
            upload_tab.click()
            time.sleep(1)
            
            # Add mock data
            self.add_mock_invoices()
            
            # Find first card and click its header
            cards = self.driver.find_elements(By.CSS_SELECTOR, '.w-1\\/2:last-child .p-4.bg-white.border')
            
            if len(cards) > 0:
                first_card = cards[0]
                
                # Find clickable header (should have cursor-pointer class)
                clickable_header = first_card.find_element(By.CSS_SELECTOR, '.cursor-pointer')
                
                # Take screenshot before clicking
                self.driver.save_screenshot('tests/selenium/screenshots/card_before_expand.png')
                
                clickable_header.click()
                time.sleep(1)  # Allow for animation
                
                # Take screenshot after clicking
                self.driver.save_screenshot('tests/selenium/screenshots/card_after_expand.png')
                
                # Check if detailed sections are now visible
                detail_sections = first_card.find_elements(By.XPATH, ".//div[contains(text(), 'Validation Results') or contains(text(), 'Security Scan') or contains(text(), 'File Information')]")
                
                expanded_successfully = len(detail_sections) > 0
                self.test_results['expand_functionality'] = expanded_successfully
                print(f"✅ Expand functionality: {'PASS' if expanded_successfully else 'FAIL'} ({len(detail_sections)} sections visible)")
            else:
                print("❌ No cards found to test expansion")
                self.test_results['expand_functionality'] = False
                
        except Exception as e:
            print(f"❌ Expand functionality test failed: {e}")
            self.test_results['expand_functionality'] = False
            
    def run_all_tests(self):
        """Run all right side collapsible cards tests"""
        print("🚀 Starting Right Side Collapsible Cards Tests")
        print("=" * 60)
        
        try:
            self.setup_driver()
            
            # Run individual tests
            self.test_cards_folded_by_default()
            self.test_expand_functionality()
            
            # Calculate overall score
            passed_tests = sum(self.test_results.values())
            total_tests = len([k for k in self.test_results.keys() if k != 'overall_success'])
            overall_score = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
            
            self.test_results['overall_success'] = overall_score >= 80
            
            print("\n" + "=" * 60)
            print("📊 TEST RESULTS SUMMARY")
            print("=" * 60)
            
            for test_name, result in self.test_results.items():
                if test_name != 'overall_success':
                    status = "✅ PASS" if result else "❌ FAIL"
                    print(f"{test_name.replace('_', ' ').title()}: {status}")
                    
            print(f"\nOverall Score: {overall_score:.1f}% ({passed_tests}/{total_tests} tests passed)")
            print(f"Overall Success: {'✅ PASS' if self.test_results['overall_success'] else '❌ FAIL'}")
            
            # Save results
            with open('tests/selenium/screenshots/right_side_cards_test_results.json', 'w') as f:
                json.dump({
                    'timestamp': time.time(),
                    'results': self.test_results,
                    'score': overall_score
                }, f, indent=2)
                
            return self.test_results['overall_success']
            
        except Exception as e:
            print(f"❌ Test suite failed: {e}")
            return False
            
        finally:
            if self.driver:
                self.driver.quit()

if __name__ == "__main__":
    tester = RightSideCollapsibleCardsTests()
    success = tester.run_all_tests()
    exit(0 if success else 1)
