#!/usr/bin/env python3
"""
Test configuration for Selenium Chrome extension tests
Centralized configuration management for all test settings
"""

import os
from pathlib import Path

class TestConfig:
    """Configuration class for Selenium tests"""
    
    # Base paths
    PROJECT_ROOT = Path(__file__).parent.parent.parent
    EXTENSION_PATH = PROJECT_ROOT / "dist"
    TESTS_PATH = PROJECT_ROOT / "tests"
    SELENIUM_PATH = TESTS_PATH / "selenium"
    
    # Screenshot and reporting paths
    SCREENSHOTS_DIR = SELENIUM_PATH / "screenshots"
    BASELINES_DIR = SCREENSHOTS_DIR / "baselines"
    REPORTS_DIR = SCREENSHOTS_DIR / "reports"
    
    # Chrome configuration
    CHROME_BINARY_PATHS = [
        "/usr/bin/google-chrome",
        "/usr/bin/chromium-browser",
        "/usr/bin/chromium",
        "/opt/google/chrome/chrome",
        "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",  # macOS
    ]
    
    # Chrome options
    CHROME_OPTIONS = [
        "--no-sandbox",
        "--disable-dev-shm-usage",
        "--disable-web-security",
        "--allow-running-insecure-content",
        "--disable-features=VizDisplayCompositor",
        "--disable-gpu",
        "--window-size=1200,800",
        "--enable-logging",
        "--log-level=0",
    ]
    
    # Test timeouts (seconds)
    DEFAULT_TIMEOUT = 15
    ELEMENT_TIMEOUT = 10
    PAGE_LOAD_TIMEOUT = 30
    SCREENSHOT_DELAY = 2
    
    # Test thresholds
    UI_VISIBILITY_THRESHOLD = 0.8  # 80% of critical elements must be visible
    CONSOLE_ERROR_THRESHOLD = 5    # Max 5 console errors allowed
    VISUAL_DIFF_THRESHOLD = 0.05   # 5% visual difference threshold
    SUCCESS_RATE_THRESHOLD = 0.75  # 75% overall success rate required
    
    # Critical UI elements that must be present
    CRITICAL_UI_ELEMENTS = [
        ("dropArea", "Drop Area", True),
        ("tab-main", "Main Tab", True),
        ("tab-settings", "Settings Tab", True),
        ("tab-debug", "Debug Tab", True),
    ]
    
    # Optional UI elements (won't fail test if missing)
    OPTIONAL_UI_ELEMENTS = [
        ("fileInput", "File Input", False),  # Hidden by default
        ("processedData", "Processed Data", False),  # Only visible after processing
        ("errorMessage", "Error Message", False),  # Only visible on errors
    ]
    
    # Test categories and their weights for scoring
    TEST_CATEGORIES = {
        "Extension Loading": 0.3,      # 30% weight
        "UI State Verification": 0.25, # 25% weight
        "Functionality Verification": 0.25, # 25% weight
        "Console Error Check": 0.2,    # 20% weight
    }
    
    # Environment-specific settings
    @classmethod
    def get_chrome_binary(cls):
        """Find available Chrome binary"""
        for path in cls.CHROME_BINARY_PATHS:
            if os.path.exists(path):
                return path
        return None
    
    @classmethod
    def is_extension_built(cls):
        """Check if extension is built and ready"""
        required_files = [
            cls.EXTENSION_PATH / "manifest.json",
            cls.EXTENSION_PATH / "popup.html",
        ]
        return all(f.exists() for f in required_files)
    
    @classmethod
    def get_popup_url(cls):
        """Get popup URL for testing"""
        popup_path = cls.EXTENSION_PATH / "popup.html"
        return f"file://{popup_path.absolute()}"
    
    @classmethod
    def setup_directories(cls):
        """Ensure all required directories exist"""
        directories = [
            cls.SCREENSHOTS_DIR,
            cls.BASELINES_DIR,
            cls.REPORTS_DIR,
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    @classmethod
    def get_test_metadata(cls):
        """Get metadata for test runs"""
        return {
            "project_root": str(cls.PROJECT_ROOT),
            "extension_path": str(cls.EXTENSION_PATH),
            "chrome_binary": cls.get_chrome_binary(),
            "extension_built": cls.is_extension_built(),
            "popup_url": cls.get_popup_url(),
        }

# Test data for various scenarios
class TestData:
    """Test data and scenarios for different test cases"""
    
    # Sample file data for upload testing
    SAMPLE_FILES = {
        "valid_pdf": {
            "name": "sample_invoice.pdf",
            "type": "application/pdf",
            "size": 1024 * 100,  # 100KB
        },
        "valid_image": {
            "name": "sample_receipt.jpg",
            "type": "image/jpeg",
            "size": 1024 * 50,   # 50KB
        },
        "invalid_file": {
            "name": "document.txt",
            "type": "text/plain",
            "size": 1024,
        },
        "large_file": {
            "name": "large_document.pdf",
            "type": "application/pdf",
            "size": 1024 * 1024 * 15,  # 15MB (over limit)
        }
    }
    
    # Expected error messages
    ERROR_MESSAGES = {
        "file_too_large": "File size exceeds maximum limit",
        "invalid_file_type": "Invalid file type",
        "upload_failed": "Upload failed",
        "processing_error": "Processing error",
    }
    
    # Test scenarios
    SCENARIOS = {
        "happy_path": {
            "description": "Normal file upload and processing",
            "files": ["valid_pdf"],
            "expected_result": "success",
        },
        "invalid_file": {
            "description": "Upload invalid file type",
            "files": ["invalid_file"],
            "expected_result": "error",
        },
        "large_file": {
            "description": "Upload file exceeding size limit",
            "files": ["large_file"],
            "expected_result": "error",
        },
        "multiple_files": {
            "description": "Upload multiple valid files",
            "files": ["valid_pdf", "valid_image"],
            "expected_result": "success",
        }
    }

# Environment detection
class Environment:
    """Environment detection and configuration"""
    
    @staticmethod
    def is_ci():
        """Check if running in CI environment"""
        ci_indicators = [
            "CI", "CONTINUOUS_INTEGRATION", "GITHUB_ACTIONS",
            "TRAVIS", "CIRCLECI", "JENKINS_URL"
        ]
        return any(os.getenv(indicator) for indicator in ci_indicators)
    
    @staticmethod
    def is_headless_required():
        """Check if headless mode is required"""
        return Environment.is_ci() or os.getenv("HEADLESS", "false").lower() == "true"
    
    @staticmethod
    def get_display():
        """Get display configuration"""
        return os.getenv("DISPLAY", ":0")
    
    @classmethod
    def get_chrome_options_for_env(cls):
        """Get Chrome options based on environment"""
        options = TestConfig.CHROME_OPTIONS.copy()
        
        if cls.is_headless_required():
            options.extend([
                "--headless",
                "--disable-extensions-except=" + str(TestConfig.EXTENSION_PATH),
                "--load-extension=" + str(TestConfig.EXTENSION_PATH),
            ])
        
        if cls.is_ci():
            options.extend([
                "--no-first-run",
                "--disable-default-apps",
                "--disable-popup-blocking",
                "--disable-translate",
                "--disable-background-timer-throttling",
                "--disable-renderer-backgrounding",
                "--disable-device-discovery-notifications",
            ])
        
        return options

# Utility functions
def validate_config():
    """Validate test configuration"""
    issues = []
    
    # Check Chrome binary
    if not TestConfig.get_chrome_binary():
        issues.append("Chrome binary not found")
    
    # Check extension build
    if not TestConfig.is_extension_built():
        issues.append("Extension not built - run 'make build-extension'")
    
    # Check required directories
    TestConfig.setup_directories()
    
    return issues

def print_config_summary():
    """Print configuration summary"""
    print("🔧 Test Configuration Summary")
    print("=" * 40)
    print(f"Project Root: {TestConfig.PROJECT_ROOT}")
    print(f"Extension Path: {TestConfig.EXTENSION_PATH}")
    print(f"Chrome Binary: {TestConfig.get_chrome_binary()}")
    print(f"Extension Built: {TestConfig.is_extension_built()}")
    print(f"Popup URL: {TestConfig.get_popup_url()}")
    print(f"CI Environment: {Environment.is_ci()}")
    print(f"Headless Required: {Environment.is_headless_required()}")
    print("=" * 40)

if __name__ == "__main__":
    # Validate and print configuration when run directly
    print_config_summary()
    
    issues = validate_config()
    if issues:
        print("\n❌ Configuration Issues:")
        for issue in issues:
            print(f"   - {issue}")
    else:
        print("\n✅ Configuration is valid")
