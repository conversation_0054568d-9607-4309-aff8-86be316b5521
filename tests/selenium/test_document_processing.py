#!/usr/bin/env python3
"""
Document Processing Test for MVAT Chrome Extension
Tests the actual document processing functionality with sample PDF files
"""

import os
import sys
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON>river<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.action_chains import Action<PERSON>hains

def setup_chrome_driver():
    """Setup Chrome driver with extension loaded"""
    print("🔧 Setting up Chrome driver for document processing test...")
    
    # Extension path
    extension_path = os.path.abspath("dist/dev")
    
    # Chrome options
    chrome_options = Options()
    chrome_options.add_argument(f"--load-extension={extension_path}")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--allow-running-insecure-content")
    
    # Use Chrome 135 binary
    chrome_binary_path = "tests/selenium/chrome-135/chrome-linux64/chrome"
    if os.path.exists(chrome_binary_path):
        chrome_options.binary_location = chrome_binary_path
        print(f"✅ Using Chrome binary: {chrome_binary_path}")
    
    # Create driver
    driver = webdriver.Chrome(options=chrome_options)
    return driver

def get_extension_id(driver):
    """Get the extension ID from chrome://extensions/"""
    driver.get("chrome://extensions/")
    time.sleep(2)
    
    # Enable developer mode
    try:
        dev_mode_toggle = driver.find_element(By.CSS_SELECTOR, "extensions-manager")
        driver.execute_script("""
            const extensionsManager = document.querySelector('extensions-manager');
            const toolbar = extensionsManager.shadowRoot.querySelector('extensions-toolbar');
            const devModeToggle = toolbar.shadowRoot.querySelector('#devMode');
            if (!devModeToggle.checked) {
                devModeToggle.click();
            }
        """)
        time.sleep(1)
    except Exception as e:
        print(f"⚠️ Could not enable developer mode: {e}")
    
    # Get extension ID via management API
    extension_info = driver.execute_script("""
        return new Promise((resolve) => {
            chrome.management.getAll((extensions) => {
                const mvat = extensions.find(ext => 
                    ext.name.includes('MVAT') || 
                    ext.name.includes('Multi-VAT')
                );
                resolve(mvat ? mvat.id : null);
            });
        });
    """)
    
    return extension_info

def test_document_processing(driver, extension_id):
    """Test document processing functionality"""
    print(f"\n🧪 Testing document processing with extension ID: {extension_id}")
    
    # Navigate to extension popup
    popup_url = f"chrome-extension://{extension_id}/popup.html"
    driver.get(popup_url)
    time.sleep(3)
    
    print("📄 Popup loaded, checking for upload area...")
    
    # Check if upload area exists
    try:
        upload_area = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "[data-testid='drag-drop-area'], .drag-drop-area, .upload-area"))
        )
        print("✅ Upload area found")
    except Exception as e:
        print(f"⚠️ Upload area not found, checking page content...")
        page_source = driver.page_source
        print(f"📝 Page source length: {len(page_source)} characters")
        
        # Look for any drag-drop related elements
        drag_elements = driver.find_elements(By.CSS_SELECTOR, "*[class*='drag'], *[class*='drop'], *[class*='upload']")
        print(f"📝 Found {len(drag_elements)} drag/drop related elements")
        
        # Check for navigation tabs
        nav_tabs = driver.find_elements(By.CSS_SELECTOR, "nav, .nav, .tabs, [role='navigation']")
        print(f"📝 Found {len(nav_tabs)} navigation elements")
        
        if nav_tabs:
            # Try to click on upload tab
            for tab in nav_tabs:
                if 'upload' in tab.text.lower() or 'document' in tab.text.lower():
                    print(f"🔄 Clicking on tab: {tab.text}")
                    tab.click()
                    time.sleep(2)
                    break
    
    # Check console logs for errors
    print("\n🔍 Checking console logs...")
    logs = driver.get_log('browser')
    error_count = 0
    warning_count = 0
    
    for log in logs:
        if log['level'] == 'SEVERE':
            error_count += 1
            print(f"❌ ERROR: {log['message']}")
        elif log['level'] == 'WARNING':
            warning_count += 1
            if 'generateUploadId' in log['message']:
                print(f"🚨 CRITICAL: {log['message']}")
    
    print(f"📊 Console logs: {error_count} errors, {warning_count} warnings")
    
    # Try to simulate file drop (if upload area exists)
    try:
        upload_area = driver.find_element(By.CSS_SELECTOR, "[data-testid='drag-drop-area'], .drag-drop-area, .upload-area, body")
        
        # Simulate file drop using JavaScript
        sample_pdf_path = os.path.abspath("docs/data/samples/invoices/input/327_K_08_23_PCM.pdf")
        
        if os.path.exists(sample_pdf_path):
            print(f"📄 Simulating file drop: {sample_pdf_path}")
            
            # Create a file input element and trigger file selection
            driver.execute_script("""
                // Create file input
                const fileInput = document.createElement('input');
                fileInput.type = 'file';
                fileInput.accept = '.pdf';
                fileInput.style.display = 'none';
                document.body.appendChild(fileInput);
                
                // Create a File object (simulation)
                const fileName = '327_K_08_23_PCM.pdf';
                console.log('🧪 Simulating file drop for:', fileName);
                
                // Trigger any existing file handlers
                const dropEvent = new DragEvent('drop', {
                    bubbles: true,
                    cancelable: true,
                    dataTransfer: new DataTransfer()
                });
                
                document.body.dispatchEvent(dropEvent);
            """)
            
            time.sleep(5)
            
            # Check for processing indicators
            processing_indicators = driver.find_elements(By.CSS_SELECTOR, 
                ".processing, .loading, .spinner, [class*='process'], [class*='load']")
            
            if processing_indicators:
                print(f"✅ Found {len(processing_indicators)} processing indicators")
            else:
                print("⚠️ No processing indicators found")
        
    except Exception as e:
        print(f"⚠️ File drop simulation failed: {e}")
    
    # Final console log check
    print("\n🔍 Final console log check...")
    final_logs = driver.get_log('browser')
    
    generateUploadId_errors = []
    for log in final_logs:
        if 'generateUploadId' in log['message'] and log['level'] == 'SEVERE':
            generateUploadId_errors.append(log['message'])
    
    if generateUploadId_errors:
        print("🚨 CRITICAL: generateUploadId errors found:")
        for error in generateUploadId_errors:
            print(f"   ❌ {error}")
        return False
    else:
        print("✅ No generateUploadId errors found")
        return True

def main():
    """Main test function"""
    print("🧪 MVAT Chrome Extension - Document Processing Test")
    print("=" * 60)
    
    driver = None
    try:
        # Setup Chrome driver
        driver = setup_chrome_driver()
        
        # Get extension ID
        extension_id = get_extension_id(driver)
        if not extension_id:
            print("❌ Could not find MVAT extension")
            return False
        
        print(f"✅ MVAT extension found: {extension_id}")
        
        # Test document processing
        success = test_document_processing(driver, extension_id)
        
        if success:
            print("\n✅ Document processing test PASSED")
            print("🎯 Core functionality appears to be working!")
        else:
            print("\n❌ Document processing test FAILED")
            print("🚨 Critical bug still exists!")
        
        return success
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False
    
    finally:
        if driver:
            driver.quit()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
