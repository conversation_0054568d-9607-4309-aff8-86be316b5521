#!/usr/bin/env python3
"""
Enhanced Pipeline UI Test Suite
Tests the new multi-layout pipeline visualization with live console logs
"""

import os
import sys
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

class EnhancedPipelineUITest:
    def __init__(self):
        self.driver = None
        self.wait = None
        self.test_results = []
        self.extension_path = os.path.join(os.path.dirname(__file__), '..', '..', 'dist', 'dev')
        
    def setup_driver(self):
        """Setup Chrome driver with extension loaded"""
        chrome_options = Options()
        chrome_options.add_argument(f"--load-extension={self.extension_path}")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--allow-running-insecure-content")
        
        self.driver = webdriver.Chrome(options=chrome_options)
        self.wait = WebDriverWait(self.driver, 10)
        
    def teardown_driver(self):
        """Clean up driver"""
        if self.driver:
            self.driver.quit()
            
    def log_result(self, test_name, success, message="", screenshot_name=None):
        """Log test result"""
        result = {
            'test': test_name,
            'success': success,
            'message': message,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        if screenshot_name and self.driver:
            try:
                screenshot_path = f"tests/screenshots/{screenshot_name}.png"
                os.makedirs(os.path.dirname(screenshot_path), exist_ok=True)
                self.driver.save_screenshot(screenshot_path)
                result['screenshot'] = screenshot_path
            except Exception as e:
                result['screenshot_error'] = str(e)
                
        self.test_results.append(result)
        print(f"{'✅' if success else '❌'} {test_name}: {message}")
        
    def open_extension_popup(self):
        """Open the extension popup"""
        try:
            # Get extension ID
            self.driver.get("chrome://extensions/")
            time.sleep(2)
            
            # Find extension and get ID
            extension_cards = self.driver.find_elements(By.TAG_NAME, "extensions-item")
            extension_id = None
            
            for card in extension_cards:
                try:
                    name_element = card.find_element(By.CSS_SELECTOR, "#name")
                    if "MVAT" in name_element.text or "Multi-VAT" in name_element.text:
                        extension_id = card.get_attribute("id")
                        break
                except:
                    continue
                    
            if not extension_id:
                raise Exception("Extension not found")
                
            # Open popup
            popup_url = f"chrome-extension://{extension_id}/popup.html"
            self.driver.get(popup_url)
            time.sleep(2)
            
            return True
            
        except Exception as e:
            self.log_result("open_extension_popup", False, f"Failed to open popup: {str(e)}")
            return False
            
    def test_compact_layout(self):
        """Test compact layout in popup"""
        try:
            # Navigate to upload page
            upload_tab = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Upload')]"))
            )
            upload_tab.click()
            time.sleep(1)
            
            # Check if drag-drop area exists
            drag_drop = self.wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "[data-testid='drag-drop-upload']"))
            )
            
            # Check compact pipeline is not visible initially
            pipeline_elements = self.driver.find_elements(By.CLASS_NAME, "enhanced-pipeline-placeholder")
            
            self.log_result("test_compact_layout", True, 
                          f"Compact layout loaded successfully. Pipeline elements: {len(pipeline_elements)}", 
                          "compact_layout")
            return True
            
        except Exception as e:
            self.log_result("test_compact_layout", False, f"Compact layout test failed: {str(e)}")
            return False
            
    def test_layout_controls(self):
        """Test layout control buttons"""
        try:
            # Look for layout control buttons (they should appear when file is selected)
            panel_button = self.driver.find_elements(By.XPATH, "//button[contains(text(), 'Panel')]")
            full_button = self.driver.find_elements(By.XPATH, "//button[contains(text(), 'Full')]")
            
            # Initially buttons might not be visible (no file selected)
            self.log_result("test_layout_controls", True, 
                          f"Layout controls check: Panel buttons: {len(panel_button)}, Full buttons: {len(full_button)}", 
                          "layout_controls")
            return True
            
        except Exception as e:
            self.log_result("test_layout_controls", False, f"Layout controls test failed: {str(e)}")
            return False
            
    def test_console_logs_structure(self):
        """Test console logs structure and components"""
        try:
            # Check for console logger components in the DOM
            console_elements = self.driver.find_elements(By.CLASS_NAME, "console-logger")
            live_console_elements = self.driver.find_elements(By.CLASS_NAME, "live-console-logger")
            
            # Check for log-related elements
            log_containers = self.driver.find_elements(By.CSS_SELECTOR, "[class*='log']")
            
            self.log_result("test_console_logs_structure", True, 
                          f"Console structure: Console elements: {len(console_elements)}, Live console: {len(live_console_elements)}, Log containers: {len(log_containers)}", 
                          "console_structure")
            return True
            
        except Exception as e:
            self.log_result("test_console_logs_structure", False, f"Console logs structure test failed: {str(e)}")
            return False
            
    def test_pipeline_steps_display(self):
        """Test pipeline steps display"""
        try:
            # Look for pipeline step elements
            step_cards = self.driver.find_elements(By.CSS_SELECTOR, "[class*='pipeline-step']")
            step_elements = self.driver.find_elements(By.CSS_SELECTOR, "[class*='step']")
            
            # Check for step icons and names
            step_icons = self.driver.find_elements(By.CSS_SELECTOR, "[class*='icon']")
            
            self.log_result("test_pipeline_steps_display", True, 
                          f"Pipeline steps: Step cards: {len(step_cards)}, Step elements: {len(step_elements)}, Icons: {len(step_icons)}", 
                          "pipeline_steps")
            return True
            
        except Exception as e:
            self.log_result("test_pipeline_steps_display", False, f"Pipeline steps display test failed: {str(e)}")
            return False
            
    def test_responsive_design(self):
        """Test responsive design at different viewport sizes"""
        try:
            # Test different viewport sizes
            viewports = [
                (400, 600),   # Popup size
                (800, 600),   # Small desktop
                (1200, 800),  # Medium desktop
                (1920, 1080)  # Large desktop
            ]
            
            results = []
            for width, height in viewports:
                self.driver.set_window_size(width, height)
                time.sleep(1)
                
                # Check if layout adapts
                body = self.driver.find_element(By.TAG_NAME, "body")
                body_width = body.size['width']
                body_height = body.size['height']
                
                results.append(f"{width}x{height} -> {body_width}x{body_height}")
                
            self.log_result("test_responsive_design", True, 
                          f"Responsive design test completed: {', '.join(results)}", 
                          "responsive_design")
            return True
            
        except Exception as e:
            self.log_result("test_responsive_design", False, f"Responsive design test failed: {str(e)}")
            return False
            
    def test_error_handling(self):
        """Test error handling and console error detection"""
        try:
            # Get browser console logs
            logs = self.driver.get_log('browser')
            
            # Filter for errors
            errors = [log for log in logs if log['level'] == 'SEVERE']
            warnings = [log for log in logs if log['level'] == 'WARNING']
            
            # Check for specific error patterns
            critical_errors = []
            for error in errors:
                message = error['message'].lower()
                if any(pattern in message for pattern in ['uncaught', 'failed to load', 'syntax error']):
                    critical_errors.append(error['message'])
                    
            success = len(critical_errors) == 0
            message = f"Console check: {len(errors)} errors, {len(warnings)} warnings, {len(critical_errors)} critical"
            
            if critical_errors:
                message += f". Critical errors: {'; '.join(critical_errors[:3])}"
                
            self.log_result("test_error_handling", success, message, "error_handling")
            return success
            
        except Exception as e:
            self.log_result("test_error_handling", False, f"Error handling test failed: {str(e)}")
            return False
            
    def run_all_tests(self):
        """Run all enhanced pipeline UI tests"""
        print("🚀 Starting Enhanced Pipeline UI Test Suite")
        print("=" * 60)
        
        try:
            self.setup_driver()
            
            if not self.open_extension_popup():
                return False
                
            # Run all tests
            tests = [
                self.test_compact_layout,
                self.test_layout_controls,
                self.test_console_logs_structure,
                self.test_pipeline_steps_display,
                self.test_responsive_design,
                self.test_error_handling
            ]
            
            for test in tests:
                try:
                    test()
                    time.sleep(1)  # Brief pause between tests
                except Exception as e:
                    self.log_result(test.__name__, False, f"Test execution failed: {str(e)}")
                    
        finally:
            self.teardown_driver()
            
        # Print summary
        print("\n" + "=" * 60)
        print("📊 Test Results Summary")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        # Save detailed results
        results_file = "tests/results/enhanced_pipeline_ui_test_results.json"
        os.makedirs(os.path.dirname(results_file), exist_ok=True)
        
        with open(results_file, 'w') as f:
            json.dump({
                'summary': {
                    'total': total_tests,
                    'passed': passed_tests,
                    'failed': failed_tests,
                    'success_rate': (passed_tests/total_tests)*100
                },
                'tests': self.test_results
            }, f, indent=2)
            
        print(f"\n📄 Detailed results saved to: {results_file}")
        
        return failed_tests == 0

if __name__ == "__main__":
    tester = EnhancedPipelineUITest()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
