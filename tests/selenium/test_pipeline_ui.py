#!/usr/bin/env python3
"""
Test script for the new pipeline UI functionality
Tests drag & drop with pipeline visualization
"""

import os
import sys
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import Action<PERSON>hains

def setup_chrome_driver():
    """Setup Chrome driver with extension loaded"""
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--allow-running-insecure-content")

    # Load extension
    extension_path = os.path.abspath("dist/dev")
    chrome_options.add_argument(f"--load-extension={extension_path}")

    # Use Chrome 135 binary
    chrome_binary = "tests/selenium/chrome-135/chrome-linux64/chrome"
    if os.path.exists(chrome_binary):
        chrome_options.binary_location = chrome_binary

    driver = webdriver.Chrome(options=chrome_options)
    return driver

def test_pipeline_ui():
    """Test the new pipeline UI functionality"""
    driver = setup_chrome_driver()

    try:
        print("🧪 Testing Pipeline UI Functionality")
        print("=" * 50)

        # Get extension ID
        driver.get("chrome://extensions/")
        time.sleep(2)

        # Enable developer mode
        try:
            dev_mode_toggle = driver.find_element(By.CSS_SELECTOR, "#devMode")
            if not dev_mode_toggle.is_selected():
                dev_mode_toggle.click()
                time.sleep(1)
        except:
            print("⚠️ Could not toggle developer mode")

        # Get extension ID via management API
        extension_id = driver.execute_script("""
            return new Promise((resolve) => {
                chrome.management.getAll((extensions) => {
                    const mvat = extensions.find(ext => ext.name.includes('MVAT'));
                    resolve(mvat ? mvat.id : null);
                });
            });
        """)

        if not extension_id:
            print("❌ Could not find MVAT extension")
            return False

        print(f"✅ Found MVAT extension: {extension_id}")

        # Navigate to popup
        popup_url = f"chrome-extension://{extension_id}/popup.html"
        driver.get(popup_url)
        time.sleep(3)

        print("📄 Popup loaded, checking UI elements...")

        # Check if upload area is visible
        upload_area = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "[data-testid='upload-area']"))
        )
        print("✅ Upload area found")

        # Check if drag-drop component is visible
        try:
            drag_drop = driver.find_element(By.CSS_SELECTOR, "[data-testid='drag-drop-upload']")
            print("✅ Drag & Drop component found")
        except:
            print("⚠️ Drag & Drop component not found, checking page structure...")
            # Print page source for debugging
            page_source = driver.page_source
            print("📄 Page contains drag-drop elements:", "drag" in page_source.lower())
            print("📄 Page contains upload elements:", "upload" in page_source.lower())

            # Try alternative selectors
            upload_elements = driver.find_elements(By.XPATH, "//*[contains(@class, 'upload') or contains(@class, 'drag') or contains(@class, 'drop')]")
            print(f"📄 Found {len(upload_elements)} upload-related elements")

            for i, elem in enumerate(upload_elements[:3]):
                try:
                    print(f"  Element {i+1}: {elem.tag_name} - {elem.get_attribute('class')}")
                except:
                    pass

        # Take screenshot of initial state
        driver.save_screenshot("tests/selenium/screenshots/pipeline_ui_initial.png")
        print("📸 Initial state screenshot saved")

        # Test file upload with a real sample file
        print("🔄 Testing file upload to trigger pipeline...")

        # Use a sample invoice file
        sample_file = "data/samples/invoices/input/4956_LO_12_23_T.pdf"
        if os.path.exists(sample_file):
            print(f"📄 Using sample file: {sample_file}")

            # Find the file input element
            file_input = driver.find_element(By.CSS_SELECTOR, "input[type='file']")

            # Upload the file
            file_input.send_keys(os.path.abspath(sample_file))
            time.sleep(3)  # Wait for file processing to start

        else:
            print("⚠️ Sample file not found, using JavaScript simulation...")
            # Create a mock file object and trigger the pipeline
            driver.execute_script("""
                // Create a mock file
                const mockFile = new File(['mock pdf content'], 'test-invoice.pdf', {
                    type: 'application/pdf'
                });

                // Find the upload component and trigger file selection
                const uploadComponent = document.querySelector('[data-testid="drag-drop-upload"]');
                if (uploadComponent) {
                    // Trigger the file selection event
                    const event = new CustomEvent('drop', {
                        detail: { files: [mockFile] }
                    });
                    uploadComponent.dispatchEvent(event);
                }
            """)

        time.sleep(2)

        # Check if pipeline UI appears
        try:
            pipeline_button = WebDriverWait(driver, 5).until(
                EC.presence_of_element_located((By.XPATH, "//button[contains(text(), 'Pipeline')]"))
            )
            print("✅ Pipeline button found")

            # Click to show pipeline
            pipeline_button.click()
            time.sleep(2)

            # Check for pipeline visualization
            pipeline_viz = driver.find_element(By.XPATH, "//h3[contains(text(), 'Multi-Step Processing Pipeline')]")
            print("✅ Pipeline visualization found")

            # Take screenshot of pipeline UI
            driver.save_screenshot("tests/selenium/screenshots/pipeline_ui_active.png")
            print("📸 Pipeline UI screenshot saved")

            # Check for pipeline steps
            step_cards = driver.find_elements(By.CSS_SELECTOR, ".pipeline-step, [class*='step'], [class*='pipeline']")
            print(f"📋 Found {len(step_cards)} pipeline-related elements")

            # Check for action buttons
            action_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Rerun') or contains(text(), 'View') or contains(text(), 'Run')]")
            print(f"🔘 Found {len(action_buttons)} action buttons")

            return True

        except Exception as e:
            print(f"⚠️ Pipeline UI not found or not triggered: {e}")

            # Take screenshot for debugging
            driver.save_screenshot("tests/selenium/screenshots/pipeline_ui_debug.png")
            print("📸 Debug screenshot saved")

            # Check page source for pipeline elements
            page_source = driver.page_source
            if "Pipeline" in page_source:
                print("✅ Pipeline text found in page source")
            else:
                print("❌ No pipeline text found in page source")

            return False

    except Exception as e:
        print(f"❌ Test failed: {e}")
        driver.save_screenshot("tests/selenium/screenshots/pipeline_ui_error.png")
        return False

    finally:
        driver.quit()

if __name__ == "__main__":
    success = test_pipeline_ui()
    if success:
        print("\n✅ Pipeline UI test completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Pipeline UI test failed!")
        sys.exit(1)
