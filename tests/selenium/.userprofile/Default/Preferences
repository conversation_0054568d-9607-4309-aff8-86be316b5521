{"NewTabPage": {"PrevNavigationTime": "*****************"}, "accessibility": {"captions": {"headless_caption_enabled": false, "live_caption_language": "en-US"}}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": false}, "autocomplete": {"retention_policy_last_version": 137}, "autofill": {"last_version_deduped": 137}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"check_default_browser": false, "window_placement": {"bottom": 850, "left": 10, "maximized": false, "right": 1210, "top": 50, "work_area_bottom": 1032, "work_area_left": 0, "work_area_right": 1920, "work_area_top": 40}}, "commerce_daily_metrics_last_update_time": "*****************", "default_search_provider": {"guid": ""}, "distribution": {"import_bookmarks": false, "import_history": false, "import_search_engine": false, "make_chrome_default_for_user": false, "skip_first_run_ui": true}, "dns_prefetching": {"enabled": false}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "4a4c4f1e-40ca-4d55-9de6-8bbc712b5feb", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "settings": {"developer_mode": true}, "theme": {"system_theme": 1}, "ui": {"developer_mode": true, "developer_mode_enabled": true}}, "gaia_cookie": {"changed_time": **********.025499, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_data": "[\"gaia.l.a.r\",[]]"}, "gcm": {"product_category_for_subtypes": "com.chrome.linux"}, "google": {"services": {"signin_scoped_device_id": "36f0677a-0b92-4258-80c2-0a2be56b977e"}}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "LensOverlay": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "*****************", "recent_session_start_times": ["*****************"], "session_last_active_time": "*****************", "session_start_time": "*****************"}, "intl": {"selected_languages": "en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"*************": {}}}, "media": {"engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "M+NED6CMnG/z+QV5AkWLyW8Jdprk06RrARhwYuJCga/txy7qtMfV3hMtOO6Qic3ygxpdhQXzLIhJ5eMc92X4/A=="}, "net": {"network_prediction_options": 0}, "ntp": {"num_personal_suggestions": 1}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "predictionmodelfetcher": {"last_fetch_attempt": "*****************", "last_fetch_success": "*****************"}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PRICE_TRACKING": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false}, "pinned_tabs": [], "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "background_password_check": {"check_fri_weight": 9, "check_interval": "*************", "check_mon_weight": 8, "check_sat_weight": 6, "check_sun_weight": 6, "check_thu_weight": 9, "check_tue_weight": 9, "check_wed_weight": 9, "next_check_time": "*****************"}, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"chrome://extensions/,*": {"last_modified": "13394363362895027", "setting": {"lastEngagementTime": 1.3394363362895004e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 4.5, "rawScore": 4.5}}, "chrome://newtab/,*": {"last_modified": "13394363362815956", "setting": {"lastEngagementTime": 1.3394363362815944e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 4.5, "rawScore": 4.5}}, "chrome://settings/,*": {"last_modified": "13394363367434173", "setting": {"lastEngagementTime": 1.3394363367434152e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 4.5, "rawScore": 4.5}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pattern_pairs": {"https://*,*": {"media-stream": {"audio": "<PERSON><PERSON><PERSON>", "video": "<PERSON><PERSON><PERSON>"}}}, "pref_version": 1}, "creation_time": "13394363018075861", "default_content_setting_values": {"geolocation": 1, "notifications": 2}, "default_content_settings": {"geolocation": 1, "mouselock": 1, "notifications": 1, "popups": 0, "ppapi-broker": 1}, "did_work_around_bug_364820109_default": true, "did_work_around_bug_364820109_exceptions": true, "exit_type": "SessionEnded", "family_member_role": "not_in_family", "last_engagement_time": "13394363367434152", "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "Your Chrome", "password_manager_enabled": false, "safety_hub_menu_notifications": {"extensions": {"isCurrentlyActive": false, "result": {"timestamp": "13394363364527593", "triggeringExtensions": []}}, "notification-permissions": {"isCurrentlyActive": false, "result": {"notificationPermissions": [], "timestamp": "13394363362957791"}}, "passwords": {"isCurrentlyActive": false, "result": {"passwordCheckOrigins": [], "timestamp": "13394363362961283"}}, "safe-browsing": {"isCurrentlyActive": false, "onlyShowAfterTime": "13394449418492261", "result": {"safeBrowsingStatus": 4, "timestamp": "13394363364527572"}}, "unused-site-permissions": {"isCurrentlyActive": false, "result": {"permissions": [], "timestamp": "13394363362682200"}}}}, "protection": {"macs": {"browser": {"show_home_button": "9DDE23BD288B95F7CE675BBD01A9E2B63A7624B8C3CDB431097FDF3F63AB4E51"}, "default_search_provider_data": {"template_url_data": "705F2D2FDD2FF483A1A9E675DFD71CCB223E81A2CEBF5D20C031A68B0020CF77"}, "enterprise_signin": {"policy_recovery_token": "591DA1FC050B131B34673892259777A173A67541C1F956250F1D29B9ED8E6EA2"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "3A4278124F91D0AC3BAFC601B82967EBB9632D9178FA3CDA2D5C6EC704F75543", "developer_mode": "0CAB62E686E927A87B8A818F77592D58BED9FB19320FA3AD234C0F0F6368DD60", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "CB8BBEF19EC5B482C6EE8507F1BE18B0E6130228B9F3B14A128D269CF8D02793"}, "ui": {"developer_mode": "F5B1C8DDE6014DCC262C5DE71A9D778595F34F57A24A0C45FB0673C95073973D"}}, "google": {"services": {"account_id": "07620F46EF9994C94D86883494C13E89DC6509B3D4E8978B2E18F6776C85CDBF", "last_signed_in_username": "EBF4B854EB3CF2662D69B0EDE4D83BFBE3E506F21605395D28B48B2A5C01067F", "last_username": "C202CF3B01A560B8B7D71D3B0076B61126EF72F4B11D79B3EA6E3661DB757E93"}}, "homepage": "B2A199504AEACAAD5C3A7BB4A96D9C3A9536D7A29672EB4DA3B9552B8D39C49C", "homepage_is_newtabpage": "306C67E79E036278678ED45B3C668C4421665A206FC4B97F053015981C8BAAE2", "media": {"storage_id_salt": "C29149AE129B959FDEB0CA9E54B924BF0A8BAF533937C017ADFBC9AA2FC7BC0C"}, "pinned_tabs": "14F8B2B035A86C0AEA5637DFD2AA7F5BDEADD0AAFF13141260E56C9477047715", "prefs": {"preference_reset_time": "7B22235E8A603BE387D81441C8C88F0C4E591567147FA05BE235C96189AC4490"}, "safebrowsing": {"incidents_sent": "F1827D0C55798CE7843DAF5DDEAB06A9BB2F9628970A5DCDA2543102436E4749"}, "search_provider_overrides": "99AC1EA12DA6196886F08A934B3B5006A725063DF41E9D0EE38F1FCFFDFDD5B0", "session": {"restore_on_startup": "74E1D625EF359DDAF159A835BC3731F9BCEC2AFE542FE783845A6292F572D0F5", "startup_urls": "D7174760A7168B445632139CD74E389AA027590889201AF1A252FFDE27B0531D"}}}, "safebrowsing": {"enabled": false, "event_timestamps": {}, "metrics_last_log_time": "13394363018", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "search": {"suggest_enabled": false}, "segmentation_platform": {"client_result_prefs": "ClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQ7+z+z6LD5RcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADEIrt/s+iw+UX", "uma_in_sql_start_time": "13394363018195706"}, "sessions": {"event_log": [{"crashed": false, "time": "13394363018195417", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13394363046459093", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394363362631479", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "*****************", "type": 2, "window_count": 1}, {"crashed": false, "time": "*****************", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "*****************", "type": 2, "window_count": 1}], "session_data_status": 1}, "settings": {"force_google_safesearch": false}, "signin": {"allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["en-US"], "dictionary": ""}, "sync": {"passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true}, "translate": {"enabled": false}, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "137"}}