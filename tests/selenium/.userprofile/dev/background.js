chrome.runtime.onInstalled.addListener((details) => {
  console.log("🚀 MVAT Extension installed/updated:", details.reason);
  if (details.reason === "install") {
    initializeExtension();
  } else if (details.reason === "update") {
    handleExtensionUpdate(details.previousVersion);
  }
});
chrome.runtime.onStartup.addListener(() => {
  console.log("🔄 MVAT Extension startup");
});
chrome.action.onClicked.addListener(async (tab) => {
  console.log("🖱️ Extension icon clicked, opening detached popup window");
  try {
    const existingWindows = await chrome.windows.getAll({
      windowTypes: ["popup"],
      populate: false
    });
    const existingPopup = existingWindows.find(
      (window) => window.type === "popup" && window.width === 420 && window.height === 650
    );
    if (existingPopup) {
      await chrome.windows.update(existingPopup.id, { focused: true });
      console.log("📋 Focused existing MVAT popup window");
    } else {
      const popup = await chrome.windows.create({
        url: chrome.runtime.getURL("popup.html"),
        type: "popup",
        width: 420,
        height: 650,
        left: 100,
        top: 100,
        focused: true
      });
      console.log("🪟 Created new MVAT popup window:", popup.id);
    }
  } catch (error) {
    console.error("❌ Failed to open popup window:", error);
    try {
      await chrome.tabs.create({
        url: chrome.runtime.getURL("popup.html"),
        active: true
      });
      console.log("📑 Opened MVAT in new tab as fallback");
    } catch (tabError) {
      console.error("❌ Failed to open in tab:", tabError);
    }
  }
});
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log("📨 Background received message:", message.type, sender.tab?.id);
  switch (message.type) {
    case "PING":
      sendResponse({ status: "pong", timestamp: Date.now() });
      break;
    case "ERROR_LOG":
      handleErrorLog(message.error, sender);
      sendResponse({ status: "logged" });
      break;
    case "STORAGE_GET":
      handleStorageGet(message.keys).then((result) => sendResponse({ status: "success", data: result })).catch((error) => sendResponse({ status: "error", error: error.message }));
      return true;
    case "STORAGE_SET":
      handleStorageSet(message.data).then(() => sendResponse({ status: "success" })).catch((error) => sendResponse({ status: "error", error: error.message }));
      return true;
    case "STORAGE_CLEAR":
      handleStorageClear().then(() => sendResponse({ status: "success" })).catch((error) => sendResponse({ status: "error", error: error.message }));
      return true;
    case "PROCESS_DOCUMENT":
      handleDocumentProcessing(message.file, message.options).then((result) => sendResponse({ status: "success", data: result })).catch((error) => sendResponse({ status: "error", error: error.message }));
      return true;
    default:
      console.warn("Unknown message type:", message.type);
      sendResponse({ status: "error", error: "Unknown message type" });
  }
});
async function initializeExtension() {
  try {
    console.log("🔧 Initializing MVAT extension...");
    const defaultSettings = {
      company: {
        name: "",
        taxId: "",
        address: "",
        email: "",
        phone: ""
      },
      display: {
        groupBy: "month",
        dateFormat: "DD/MM/YYYY",
        currency: "PLN",
        language: "pl"
      },
      processing: {
        ocrLanguage: "pol",
        aiProvider: "deepseek",
        autoProcess: true,
        cacheEnabled: true
      }
    };
    await chrome.storage.local.set({
      settings: defaultSettings,
      invoices: [],
      cache: {},
      version: chrome.runtime.getManifest().version,
      installedAt: (/* @__PURE__ */ new Date()).toISOString()
    });
    console.log("✅ MVAT extension initialized successfully");
  } catch (error) {
    console.error("❌ Failed to initialize extension:", error);
  }
}
async function handleExtensionUpdate(previousVersion) {
  try {
    console.log(`🔄 Updating MVAT from version ${previousVersion}`);
    const data = await chrome.storage.local.get();
    await chrome.storage.local.set({
      version: chrome.runtime.getManifest().version,
      updatedAt: (/* @__PURE__ */ new Date()).toISOString()
    });
    console.log("✅ MVAT extension updated successfully");
  } catch (error) {
    console.error("❌ Failed to update extension:", error);
  }
}
function handleErrorLog(error, sender) {
  const errorLog = {
    timestamp: (/* @__PURE__ */ new Date()).toISOString(),
    error,
    sender: {
      tab: sender.tab?.id,
      url: sender.url,
      origin: sender.origin
    },
    version: chrome.runtime.getManifest().version
  };
  console.error("🚨 MVAT Error:", errorLog);
  chrome.storage.local.get(["errorLogs"]).then(({ errorLogs = [] }) => {
    errorLogs.unshift(errorLog);
    if (errorLogs.length > 10) {
      errorLogs = errorLogs.slice(0, 10);
    }
    chrome.storage.local.set({ errorLogs });
  });
}
async function handleStorageGet(keys) {
  try {
    if (keys) {
      return await chrome.storage.local.get(keys);
    }
    return await chrome.storage.local.get();
  } catch (error) {
    console.error("Storage get error:", error);
    throw error;
  }
}
async function handleStorageSet(data) {
  try {
    await chrome.storage.local.set(data);
    console.log("📦 Storage updated:", Object.keys(data));
  } catch (error) {
    console.error("Storage set error:", error);
    throw error;
  }
}
async function handleStorageClear() {
  try {
    await chrome.storage.local.clear();
    console.log("🧹 Storage cleared");
    await initializeExtension();
  } catch (error) {
    console.error("Storage clear error:", error);
    throw error;
  }
}
async function handleDocumentProcessing(file, options = {}) {
  try {
    console.log("📄 Processing document:", file.name, options);
    return {
      success: true,
      message: "Document processing not yet implemented",
      file: {
        name: file.name,
        size: file.size,
        type: file.type
      },
      timestamp: (/* @__PURE__ */ new Date()).toISOString()
    };
  } catch (error) {
    console.error("Document processing error:", error);
    throw error;
  }
}
chrome.storage.onChanged.addListener((changes, namespace) => {
  console.log("📦 Storage changed:", namespace, Object.keys(changes));
  chrome.runtime.sendMessage({
    type: "STORAGE_CHANGED",
    changes,
    namespace
  }).catch(() => {
  });
});
let keepAliveInterval;
function keepServiceWorkerAlive() {
  keepAliveInterval = setInterval(() => {
    chrome.runtime.getPlatformInfo(() => {
    });
  }, 2e4);
}
function stopKeepAlive() {
  if (keepAliveInterval) {
    clearInterval(keepAliveInterval);
    keepAliveInterval = null;
  }
}
keepServiceWorkerAlive();
chrome.runtime.onSuspend.addListener(() => {
  console.log("💤 MVAT Extension suspending");
  stopKeepAlive();
});
console.log("🎉 MVAT Background Service Worker loaded");
//# sourceMappingURL=background.js.map
