<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>MVAT - Multi-VAT Invoice Processor</title>
  <meta name="description" content="Parse invoices using PDF.js, Tesseract.js, and OpenAI">
  <link rel="stylesheet" href="assets/popup-DnhGktNg.css">
  <style>
    /* Loading spinner styles for initial load */
    .loading-spinner {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 400px;
      padding: 2rem;
    }
    /* Loading handled by React components */
  </style>
</head>
<body class="w-screen">
  <div id="root">
    <!-- Loading state -->
    <div class="loading-spinner">
      <div class="spinner"></div>
      <div class="loading-text">Loading MVAT...</div>
    </div>
  </div>
  <script type="module" src="popup.js"></script>
</body>
</html>