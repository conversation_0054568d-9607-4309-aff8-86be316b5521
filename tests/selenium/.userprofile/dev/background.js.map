{"version": 3, "file": "background.js", "sources": ["../../src/background/background.js"], "sourcesContent": ["/**\n * MVAT Chrome Extension Background Service Worker\n * Handles extension lifecycle, storage, and communication between components\n */\n\n// Extension lifecycle\nchrome.runtime.onInstalled.addListener((details) => {\n  console.log('🚀 MVAT Extension installed/updated:', details.reason);\n\n  if (details.reason === 'install') {\n    // First time installation\n    initializeExtension();\n  } else if (details.reason === 'update') {\n    // Extension updated\n    handleExtensionUpdate(details.previousVersion);\n  }\n});\n\nchrome.runtime.onStartup.addListener(() => {\n  console.log('🔄 MVAT Extension startup');\n});\n\n// Action button click handler - Open detached popup window\nchrome.action.onClicked.addListener(async (tab) => {\n  console.log('🖱️ Extension icon clicked, opening detached popup window');\n\n  try {\n    // Check if popup window already exists\n    const existingWindows = await chrome.windows.getAll({\n      windowTypes: ['popup'],\n      populate: false\n    });\n\n    const existingPopup = existingWindows.find(window =>\n      window.type === 'popup' &&\n      window.width === 420 &&\n      window.height === 650\n    );\n\n    if (existingPopup) {\n      // Focus existing popup window\n      await chrome.windows.update(existingPopup.id, { focused: true });\n      console.log('📋 Focused existing MVAT popup window');\n    } else {\n      // Create new detached popup window\n      const popup = await chrome.windows.create({\n        url: chrome.runtime.getURL('popup.html'),\n        type: 'popup',\n        width: 420,\n        height: 650,\n        left: 100,\n        top: 100,\n        focused: true\n      });\n\n      console.log('🪟 Created new MVAT popup window:', popup.id);\n    }\n  } catch (error) {\n    console.error('❌ Failed to open popup window:', error);\n\n    // Fallback: try to open in new tab\n    try {\n      await chrome.tabs.create({\n        url: chrome.runtime.getURL('popup.html'),\n        active: true\n      });\n      console.log('📑 Opened MVAT in new tab as fallback');\n    } catch (tabError) {\n      console.error('❌ Failed to open in tab:', tabError);\n    }\n  }\n});\n\n// Message handling\nchrome.runtime.onMessage.addListener((message, sender, sendResponse) => {\n  console.log('📨 Background received message:', message.type, sender.tab?.id);\n\n  switch (message.type) {\n    case 'PING':\n      sendResponse({ status: 'pong', timestamp: Date.now() });\n      break;\n\n    case 'ERROR_LOG':\n      handleErrorLog(message.error, sender);\n      sendResponse({ status: 'logged' });\n      break;\n\n    case 'STORAGE_GET':\n      handleStorageGet(message.keys)\n        .then(result => sendResponse({ status: 'success', data: result }))\n        .catch(error => sendResponse({ status: 'error', error: error.message }));\n      return true; // Keep message channel open for async response\n\n    case 'STORAGE_SET':\n      handleStorageSet(message.data)\n        .then(() => sendResponse({ status: 'success' }))\n        .catch(error => sendResponse({ status: 'error', error: error.message }));\n      return true;\n\n    case 'STORAGE_CLEAR':\n      handleStorageClear()\n        .then(() => sendResponse({ status: 'success' }))\n        .catch(error => sendResponse({ status: 'error', error: error.message }));\n      return true;\n\n    case 'PROCESS_DOCUMENT':\n      handleDocumentProcessing(message.file, message.options)\n        .then(result => sendResponse({ status: 'success', data: result }))\n        .catch(error => sendResponse({ status: 'error', error: error.message }));\n      return true;\n\n    default:\n      console.warn('Unknown message type:', message.type);\n      sendResponse({ status: 'error', error: 'Unknown message type' });\n  }\n});\n\n// Initialize extension on first install\nasync function initializeExtension() {\n  try {\n    console.log('🔧 Initializing MVAT extension...');\n\n    // Set default settings\n    const defaultSettings = {\n      company: {\n        name: '',\n        taxId: '',\n        address: '',\n        email: '',\n        phone: ''\n      },\n      display: {\n        groupBy: 'month',\n        dateFormat: 'DD/MM/YYYY',\n        currency: 'PLN',\n        language: 'pl'\n      },\n      processing: {\n        ocrLanguage: 'pol',\n        aiProvider: 'deepseek',\n        autoProcess: true,\n        cacheEnabled: true\n      }\n    };\n\n    await chrome.storage.local.set({\n      settings: defaultSettings,\n      invoices: [],\n      cache: {},\n      version: chrome.runtime.getManifest().version,\n      installedAt: new Date().toISOString()\n    });\n\n    console.log('✅ MVAT extension initialized successfully');\n\n  } catch (error) {\n    console.error('❌ Failed to initialize extension:', error);\n  }\n}\n\n// Handle extension updates\nasync function handleExtensionUpdate(previousVersion) {\n  try {\n    console.log(`🔄 Updating MVAT from version ${previousVersion}`);\n\n    // Get current data\n    const data = await chrome.storage.local.get();\n\n    // Perform any necessary migrations here\n    // Example: if (previousVersion < '2.0.0') { /* migrate data */ }\n\n    // Update version\n    await chrome.storage.local.set({\n      version: chrome.runtime.getManifest().version,\n      updatedAt: new Date().toISOString()\n    });\n\n    console.log('✅ MVAT extension updated successfully');\n\n  } catch (error) {\n    console.error('❌ Failed to update extension:', error);\n  }\n}\n\n// Error logging\nfunction handleErrorLog(error, sender) {\n  const errorLog = {\n    timestamp: new Date().toISOString(),\n    error: error,\n    sender: {\n      tab: sender.tab?.id,\n      url: sender.url,\n      origin: sender.origin\n    },\n    version: chrome.runtime.getManifest().version\n  };\n\n  console.error('🚨 MVAT Error:', errorLog);\n\n  // Store error for debugging (keep last 10 errors)\n  chrome.storage.local.get(['errorLogs']).then(({ errorLogs = [] }) => {\n    errorLogs.unshift(errorLog);\n    if (errorLogs.length > 10) {\n      errorLogs = errorLogs.slice(0, 10);\n    }\n\n    chrome.storage.local.set({ errorLogs });\n  });\n}\n\n// Storage operations\nasync function handleStorageGet(keys) {\n  try {\n    if (keys) {\n      return await chrome.storage.local.get(keys);\n    }\n    return await chrome.storage.local.get();\n\n  } catch (error) {\n    console.error('Storage get error:', error);\n    throw error;\n  }\n}\n\nasync function handleStorageSet(data) {\n  try {\n    await chrome.storage.local.set(data);\n    console.log('📦 Storage updated:', Object.keys(data));\n  } catch (error) {\n    console.error('Storage set error:', error);\n    throw error;\n  }\n}\n\nasync function handleStorageClear() {\n  try {\n    await chrome.storage.local.clear();\n    console.log('🧹 Storage cleared');\n\n    // Reinitialize with defaults\n    await initializeExtension();\n  } catch (error) {\n    console.error('Storage clear error:', error);\n    throw error;\n  }\n}\n\n// Document processing (placeholder for future implementation)\nasync function handleDocumentProcessing(file, options = {}) {\n  try {\n    console.log('📄 Processing document:', file.name, options);\n\n    // This will be implemented with PDF.js, Tesseract.js, and OpenAI integration\n    // For now, return a mock response\n    return {\n      success: true,\n      message: 'Document processing not yet implemented',\n      file: {\n        name: file.name,\n        size: file.size,\n        type: file.type\n      },\n      timestamp: new Date().toISOString()\n    };\n\n  } catch (error) {\n    console.error('Document processing error:', error);\n    throw error;\n  }\n}\n\n// Storage change listener\nchrome.storage.onChanged.addListener((changes, namespace) => {\n  console.log('📦 Storage changed:', namespace, Object.keys(changes));\n\n  // Notify popup about storage changes\n  chrome.runtime.sendMessage({\n    type: 'STORAGE_CHANGED',\n    changes: changes,\n    namespace: namespace\n  }).catch(() => {\n    // Popup might not be open, ignore error\n  });\n});\n\n// Keep service worker alive\nlet keepAliveInterval;\n\nfunction keepServiceWorkerAlive() {\n  keepAliveInterval = setInterval(() => {\n    chrome.runtime.getPlatformInfo(() => {\n      // This keeps the service worker active\n    });\n  }, 20000); // Every 20 seconds\n}\n\nfunction stopKeepAlive() {\n  if (keepAliveInterval) {\n    clearInterval(keepAliveInterval);\n    keepAliveInterval = null;\n  }\n}\n\n// Start keep alive when extension starts\nkeepServiceWorkerAlive();\n\n// Clean up on suspend\nchrome.runtime.onSuspend.addListener(() => {\n  console.log('💤 MVAT Extension suspending');\n  stopKeepAlive();\n});\n\nconsole.log('🎉 MVAT Background Service Worker loaded');\n"], "names": [], "mappings": "AAMA,OAAO,QAAQ,YAAY,YAAY,CAAC,YAAY;AAClD,UAAQ,IAAI,wCAAwC,QAAQ,MAAM;AAElE,MAAI,QAAQ,WAAW,WAAW;AAEhC,wBAAqB;AAAA,EACzB,WAAa,QAAQ,WAAW,UAAU;AAEtC,0BAAsB,QAAQ,eAAe;AAAA,EACjD;AACA,CAAC;AAED,OAAO,QAAQ,UAAU,YAAY,MAAM;AACzC,UAAQ,IAAI,2BAA2B;AACzC,CAAC;AAGD,OAAO,OAAO,UAAU,YAAY,OAAO,QAAQ;AACjD,UAAQ,IAAI,2DAA2D;AAEvE,MAAI;AAEF,UAAM,kBAAkB,MAAM,OAAO,QAAQ,OAAO;AAAA,MAClD,aAAa,CAAC,OAAO;AAAA,MACrB,UAAU;AAAA,IAChB,CAAK;AAED,UAAM,gBAAgB,gBAAgB;AAAA,MAAK,YACzC,OAAO,SAAS,WAChB,OAAO,UAAU,OACjB,OAAO,WAAW;AAAA,IACnB;AAED,QAAI,eAAe;AAEjB,YAAM,OAAO,QAAQ,OAAO,cAAc,IAAI,EAAE,SAAS,MAAM;AAC/D,cAAQ,IAAI,uCAAuC;AAAA,IACzD,OAAW;AAEL,YAAM,QAAQ,MAAM,OAAO,QAAQ,OAAO;AAAA,QACxC,KAAK,OAAO,QAAQ,OAAO,YAAY;AAAA,QACvC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,KAAK;AAAA,QACL,SAAS;AAAA,MACjB,CAAO;AAED,cAAQ,IAAI,qCAAqC,MAAM,EAAE;AAAA,IAC/D;AAAA,EACG,SAAQ,OAAO;AACd,YAAQ,MAAM,kCAAkC,KAAK;AAGrD,QAAI;AACF,YAAM,OAAO,KAAK,OAAO;AAAA,QACvB,KAAK,OAAO,QAAQ,OAAO,YAAY;AAAA,QACvC,QAAQ;AAAA,MAChB,CAAO;AACD,cAAQ,IAAI,uCAAuC;AAAA,IACpD,SAAQ,UAAU;AACjB,cAAQ,MAAM,4BAA4B,QAAQ;AAAA,IACxD;AAAA,EACA;AACA,CAAC;AAGD,OAAO,QAAQ,UAAU,YAAY,CAAC,SAAS,QAAQ,iBAAiB;AACtE,UAAQ,IAAI,mCAAmC,QAAQ,MAAM,OAAO,KAAK,EAAE;AAE3E,UAAQ,QAAQ,MAAI;AAAA,IAClB,KAAK;AACH,mBAAa,EAAE,QAAQ,QAAQ,WAAW,KAAK,IAAG,GAAI;AACtD;AAAA,IAEF,KAAK;AACH,qBAAe,QAAQ,OAAO,MAAM;AACpC,mBAAa,EAAE,QAAQ,UAAU;AACjC;AAAA,IAEF,KAAK;AACH,uBAAiB,QAAQ,IAAI,EAC1B,KAAK,YAAU,aAAa,EAAE,QAAQ,WAAW,MAAM,QAAQ,CAAC,EAChE,MAAM,WAAS,aAAa,EAAE,QAAQ,SAAS,OAAO,MAAM,QAAO,CAAE,CAAC;AACzE,aAAO;AAAA,IAET,KAAK;AACH,uBAAiB,QAAQ,IAAI,EAC1B,KAAK,MAAM,aAAa,EAAE,QAAQ,UAAS,CAAE,CAAC,EAC9C,MAAM,WAAS,aAAa,EAAE,QAAQ,SAAS,OAAO,MAAM,QAAO,CAAE,CAAC;AACzE,aAAO;AAAA,IAET,KAAK;AACH,yBAAkB,EACf,KAAK,MAAM,aAAa,EAAE,QAAQ,UAAS,CAAE,CAAC,EAC9C,MAAM,WAAS,aAAa,EAAE,QAAQ,SAAS,OAAO,MAAM,QAAO,CAAE,CAAC;AACzE,aAAO;AAAA,IAET,KAAK;AACH,+BAAyB,QAAQ,MAAM,QAAQ,OAAO,EACnD,KAAK,YAAU,aAAa,EAAE,QAAQ,WAAW,MAAM,QAAQ,CAAC,EAChE,MAAM,WAAS,aAAa,EAAE,QAAQ,SAAS,OAAO,MAAM,QAAO,CAAE,CAAC;AACzE,aAAO;AAAA,IAET;AACE,cAAQ,KAAK,yBAAyB,QAAQ,IAAI;AAClD,mBAAa,EAAE,QAAQ,SAAS,OAAO,uBAAsB,CAAE;AAAA,EACrE;AACA,CAAC;AAGD,eAAe,sBAAsB;AACnC,MAAI;AACF,YAAQ,IAAI,mCAAmC;AAG/C,UAAM,kBAAkB;AAAA,MACtB,SAAS;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,QACT,OAAO;AAAA,QACP,OAAO;AAAA,MACR;AAAA,MACD,SAAS;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,UAAU;AAAA,MACX;AAAA,MACD,YAAY;AAAA,QACV,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,cAAc;AAAA,MACtB;AAAA,IACK;AAED,UAAM,OAAO,QAAQ,MAAM,IAAI;AAAA,MAC7B,UAAU;AAAA,MACV,UAAU,CAAE;AAAA,MACZ,OAAO,CAAE;AAAA,MACT,SAAS,OAAO,QAAQ,YAAa,EAAC;AAAA,MACtC,cAAa,oBAAI,KAAI,GAAG,YAAW;AAAA,IACzC,CAAK;AAED,YAAQ,IAAI,2CAA2C;AAAA,EAExD,SAAQ,OAAO;AACd,YAAQ,MAAM,qCAAqC,KAAK;AAAA,EAC5D;AACA;AAGA,eAAe,sBAAsB,iBAAiB;AACpD,MAAI;AACF,YAAQ,IAAI,iCAAiC,eAAe,EAAE;AAG9D,UAAM,OAAO,MAAM,OAAO,QAAQ,MAAM,IAAK;AAM7C,UAAM,OAAO,QAAQ,MAAM,IAAI;AAAA,MAC7B,SAAS,OAAO,QAAQ,YAAa,EAAC;AAAA,MACtC,YAAW,oBAAI,KAAI,GAAG,YAAW;AAAA,IACvC,CAAK;AAED,YAAQ,IAAI,uCAAuC;AAAA,EAEpD,SAAQ,OAAO;AACd,YAAQ,MAAM,iCAAiC,KAAK;AAAA,EACxD;AACA;AAGA,SAAS,eAAe,OAAO,QAAQ;AACrC,QAAM,WAAW;AAAA,IACf,YAAW,oBAAI,KAAM,GAAC,YAAa;AAAA,IACnC;AAAA,IACA,QAAQ;AAAA,MACN,KAAK,OAAO,KAAK;AAAA,MACjB,KAAK,OAAO;AAAA,MACZ,QAAQ,OAAO;AAAA,IAChB;AAAA,IACD,SAAS,OAAO,QAAQ,cAAc;AAAA,EACvC;AAED,UAAQ,MAAM,kBAAkB,QAAQ;AAGxC,SAAO,QAAQ,MAAM,IAAI,CAAC,WAAW,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY,CAAE,EAAA,MAAO;AACnE,cAAU,QAAQ,QAAQ;AAC1B,QAAI,UAAU,SAAS,IAAI;AACzB,kBAAY,UAAU,MAAM,GAAG,EAAE;AAAA,IACvC;AAEI,WAAO,QAAQ,MAAM,IAAI,EAAE,UAAS,CAAE;AAAA,EAC1C,CAAG;AACH;AAGA,eAAe,iBAAiB,MAAM;AACpC,MAAI;AACF,QAAI,MAAM;AACR,aAAO,MAAM,OAAO,QAAQ,MAAM,IAAI,IAAI;AAAA,IAChD;AACI,WAAO,MAAM,OAAO,QAAQ,MAAM,IAAK;AAAA,EAExC,SAAQ,OAAO;AACd,YAAQ,MAAM,sBAAsB,KAAK;AACzC,UAAM;AAAA,EACV;AACA;AAEA,eAAe,iBAAiB,MAAM;AACpC,MAAI;AACF,UAAM,OAAO,QAAQ,MAAM,IAAI,IAAI;AACnC,YAAQ,IAAI,uBAAuB,OAAO,KAAK,IAAI,CAAC;AAAA,EACrD,SAAQ,OAAO;AACd,YAAQ,MAAM,sBAAsB,KAAK;AACzC,UAAM;AAAA,EACV;AACA;AAEA,eAAe,qBAAqB;AAClC,MAAI;AACF,UAAM,OAAO,QAAQ,MAAM,MAAO;AAClC,YAAQ,IAAI,oBAAoB;AAGhC,UAAM,oBAAqB;AAAA,EAC5B,SAAQ,OAAO;AACd,YAAQ,MAAM,wBAAwB,KAAK;AAC3C,UAAM;AAAA,EACV;AACA;AAGA,eAAe,yBAAyB,MAAM,UAAU,IAAI;AAC1D,MAAI;AACF,YAAQ,IAAI,2BAA2B,KAAK,MAAM,OAAO;AAIzD,WAAO;AAAA,MACL,SAAS;AAAA,MACT,SAAS;AAAA,MACT,MAAM;AAAA,QACJ,MAAM,KAAK;AAAA,QACX,MAAM,KAAK;AAAA,QACX,MAAM,KAAK;AAAA,MACZ;AAAA,MACD,YAAW,oBAAI,KAAI,GAAG,YAAW;AAAA,IAClC;AAAA,EAEF,SAAQ,OAAO;AACd,YAAQ,MAAM,8BAA8B,KAAK;AACjD,UAAM;AAAA,EACV;AACA;AAGA,OAAO,QAAQ,UAAU,YAAY,CAAC,SAAS,cAAc;AAC3D,UAAQ,IAAI,uBAAuB,WAAW,OAAO,KAAK,OAAO,CAAC;AAGlE,SAAO,QAAQ,YAAY;AAAA,IACzB,MAAM;AAAA,IACN;AAAA,IACA;AAAA,EACJ,CAAG,EAAE,MAAM,MAAM;AAAA,EAEjB,CAAG;AACH,CAAC;AAGD,IAAI;AAEJ,SAAS,yBAAyB;AAChC,sBAAoB,YAAY,MAAM;AACpC,WAAO,QAAQ,gBAAgB,MAAM;AAAA,IAEzC,CAAK;AAAA,EACF,GAAE,GAAK;AACV;AAEA,SAAS,gBAAgB;AACvB,MAAI,mBAAmB;AACrB,kBAAc,iBAAiB;AAC/B,wBAAoB;AAAA,EACxB;AACA;AAGA,uBAAwB;AAGxB,OAAO,QAAQ,UAAU,YAAY,MAAM;AACzC,UAAQ,IAAI,8BAA8B;AAC1C,gBAAe;AACjB,CAAC;AAED,QAAQ,IAAI,0CAA0C;"}