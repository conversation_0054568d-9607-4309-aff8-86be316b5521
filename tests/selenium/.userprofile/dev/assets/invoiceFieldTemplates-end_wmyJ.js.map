{"version": 3, "file": "invoiceFieldTemplates-end_wmyJ.js", "sources": ["../../../src/templates/invoiceFieldTemplates.js"], "sourcesContent": ["/**\n * Invoice Field Templates - Structured templates for AI extraction\n * Defines field structures, descriptions, and validation rules for different invoice types\n *\n * Templates support:\n * - Multiple languages (Polish, English, German, French)\n * - Different invoice types (standard, simplified, proforma)\n * - Field validation rules and descriptions\n * - Localized field mappings\n */\n\n/**\n * Standard Polish invoice template\n */\nconst standardPolishTemplate = {\n  fields: {\n    // Basic invoice information\n    invoice_number: null,\n    issue_date: null,\n    due_date: null,\n    sale_date: null,\n\n    // Seller information\n    seller_name: null,\n    seller_address: null,\n    seller_city: null,\n    seller_postal_code: null,\n    seller_country: null,\n    seller_tax_id: null,\n    seller_vat_id: null,\n\n    // Buyer information\n    buyer_name: null,\n    buyer_address: null,\n    buyer_city: null,\n    buyer_postal_code: null,\n    buyer_country: null,\n    buyer_tax_id: null,\n    buyer_vat_id: null,\n\n    // Financial information\n    total_net: null,\n    total_vat: null,\n    total_gross: null,\n    currency: null,\n\n    // VAT breakdown\n    vat_rates: [],\n    vat_amounts: [],\n    net_amounts: [],\n\n    // Payment information\n    payment_method: null,\n    payment_terms: null,\n    bank_account: null,\n\n    // Additional information\n    description: null,\n    notes: null,\n    reference_number: null\n  },\n\n  descriptions: [\n    { field: 'invoice_number', description: 'Unique invoice number or identifier' },\n    { field: 'issue_date', description: 'Date when invoice was issued' },\n    { field: 'due_date', description: 'Payment due date' },\n    { field: 'sale_date', description: 'Date of sale or service delivery' },\n    { field: 'seller_name', description: 'Full name of the selling company' },\n    { field: 'seller_tax_id', description: 'Seller tax identification number (NIP)' },\n    { field: 'seller_vat_id', description: 'Seller VAT identification number' },\n    { field: 'buyer_name', description: 'Full name of the buying company' },\n    { field: 'buyer_tax_id', description: 'Buyer tax identification number (NIP)' },\n    { field: 'total_net', description: 'Total amount excluding VAT' },\n    { field: 'total_vat', description: 'Total VAT amount' },\n    { field: 'total_gross', description: 'Total amount including VAT' },\n    { field: 'currency', description: 'Currency code (PLN, EUR, USD, etc.)' },\n    { field: 'vat_rates', description: 'Array of VAT rates applied (e.g., [0.23, 0.08])' },\n    { field: 'payment_method', description: 'Method of payment (transfer, cash, card, etc.)' }\n  ],\n\n  validationRules: [\n    { field: 'invoice_number', rule: 'Required, must be non-empty string' },\n    { field: 'issue_date', rule: 'Required, must be valid date in YYYY-MM-DD format' },\n    { field: 'seller_name', rule: 'Required, must be non-empty string' },\n    { field: 'buyer_name', rule: 'Required, must be non-empty string' },\n    { field: 'total_gross', rule: 'Required, must be positive number' },\n    { field: 'currency', rule: 'Required, must be valid currency code' },\n    { field: 'total_net', rule: 'Must be positive number if present' },\n    { field: 'total_vat', rule: 'Must be non-negative number if present' },\n    { field: 'vat_rates', rule: 'Must be array of numbers between 0 and 1' }\n  ]\n};\n\n/**\n * Standard English invoice template\n */\nconst standardEnglishTemplate = {\n  ...standardPolishTemplate,\n  descriptions: [\n    { field: 'invoice_number', description: 'Unique invoice number or identifier' },\n    { field: 'issue_date', description: 'Date when invoice was issued' },\n    { field: 'due_date', description: 'Payment due date' },\n    { field: 'sale_date', description: 'Date of sale or service delivery' },\n    { field: 'seller_name', description: 'Full name of the selling company' },\n    { field: 'seller_tax_id', description: 'Seller tax identification number' },\n    { field: 'seller_vat_id', description: 'Seller VAT identification number' },\n    { field: 'buyer_name', description: 'Full name of the buying company' },\n    { field: 'buyer_tax_id', description: 'Buyer tax identification number' },\n    { field: 'total_net', description: 'Total amount excluding VAT/tax' },\n    { field: 'total_vat', description: 'Total VAT/tax amount' },\n    { field: 'total_gross', description: 'Total amount including VAT/tax' },\n    { field: 'currency', description: 'Currency code (USD, EUR, GBP, etc.)' },\n    { field: 'vat_rates', description: 'Array of tax rates applied (e.g., [0.20, 0.05])' },\n    { field: 'payment_method', description: 'Method of payment (wire transfer, check, card, etc.)' }\n  ]\n};\n\n/**\n * Simplified invoice template (for smaller invoices)\n */\nconst simplifiedTemplate = {\n  fields: {\n    invoice_number: null,\n    issue_date: null,\n    seller_name: null,\n    buyer_name: null,\n    total_gross: null,\n    currency: null,\n    description: null,\n    vat_rate: null,\n    payment_method: null\n  },\n\n  descriptions: [\n    { field: 'invoice_number', description: 'Invoice number' },\n    { field: 'issue_date', description: 'Issue date' },\n    { field: 'seller_name', description: 'Seller company name' },\n    { field: 'buyer_name', description: 'Buyer company name' },\n    { field: 'total_gross', description: 'Total amount to pay' },\n    { field: 'currency', description: 'Currency' },\n    { field: 'description', description: 'Service or product description' },\n    { field: 'vat_rate', description: 'VAT rate applied' },\n    { field: 'payment_method', description: 'Payment method' }\n  ],\n\n  validationRules: [\n    { field: 'invoice_number', rule: 'Required' },\n    { field: 'seller_name', rule: 'Required' },\n    { field: 'total_gross', rule: 'Required, positive number' },\n    { field: 'currency', rule: 'Required' }\n  ]\n};\n\n/**\n * Proforma invoice template\n */\nconst proformaTemplate = {\n  ...standardPolishTemplate,\n  fields: {\n    ...standardPolishTemplate.fields,\n    proforma_number: null,\n    validity_date: null,\n    final_invoice_reference: null\n  },\n\n  descriptions: [\n    ...standardPolishTemplate.descriptions,\n    { field: 'proforma_number', description: 'Proforma invoice number' },\n    { field: 'validity_date', description: 'Proforma validity date' },\n    { field: 'final_invoice_reference', description: 'Reference to final invoice if issued' }\n  ]\n};\n\n/**\n * Export all templates\n */\nexport const invoiceFieldTemplates = {\n  // Standard templates by language\n  standard_pol: standardPolishTemplate,\n  standard_eng: standardEnglishTemplate,\n  standard_deu: standardPolishTemplate, // Use Polish template for German (similar structure)\n  standard_fra: standardPolishTemplate, // Use Polish template for French (similar structure)\n\n  // Specialized templates\n  simplified_pol: simplifiedTemplate,\n  simplified_eng: simplifiedTemplate,\n  proforma_pol: proformaTemplate,\n  proforma_eng: proformaTemplate,\n\n  // Default fallback\n  default: standardPolishTemplate\n};\n\n/**\n * Get template by type and language\n * @param {string} type - Template type (standard, simplified, proforma)\n * @param {string} language - Language code (pol, eng, deu, fra)\n * @returns {Object} - Template object\n */\nexport function getTemplate(type = 'standard', language = 'pol') {\n  const key = `${type}_${language}`;\n  return invoiceFieldTemplates[key] || invoiceFieldTemplates.default;\n}\n\n/**\n * Get all available template types\n * @returns {Array} - Array of template type names\n */\nexport function getAvailableTemplates() {\n  return Object.keys(invoiceFieldTemplates).filter(key => key !== 'default');\n}\n\n/**\n * Get supported languages for a template type\n * @param {string} type - Template type\n * @returns {Array} - Array of language codes\n */\nexport function getSupportedLanguages(type = 'standard') {\n  return Object.keys(invoiceFieldTemplates)\n    .filter(key => key.startsWith(`${type}_`))\n    .map(key => key.split('_')[1]);\n}\n\n// Self-test functionality for Node.js environment\nif (typeof module !== 'undefined' && require.main === module) {\n  console.log('🧪 Testing invoiceFieldTemplates...');\n\n  // Test template retrieval\n  const polishTemplate = getTemplate('standard', 'pol');\n  console.log('✅ Polish template retrieved:', polishTemplate ? 'success' : 'failed');\n\n  const englishTemplate = getTemplate('standard', 'eng');\n  console.log('✅ English template retrieved:', englishTemplate ? 'success' : 'failed');\n\n  const simplifiedTemplate = getTemplate('simplified', 'pol');\n  console.log('✅ Simplified template retrieved:', simplifiedTemplate ? 'success' : 'failed');\n\n  // Test available templates\n  const availableTemplates = getAvailableTemplates();\n  console.log('✅ Available templates:', availableTemplates.length, 'found');\n\n  // Test supported languages\n  const supportedLanguages = getSupportedLanguages('standard');\n  console.log('✅ Supported languages for standard:', supportedLanguages);\n\n  // Test template structure\n  const template = getTemplate('standard', 'pol');\n  const hasRequiredFields = template.fields && template.descriptions && template.validationRules;\n  console.log('✅ Template structure validation:', hasRequiredFields ? 'passed' : 'failed');\n\n  console.log('🎉 All invoiceFieldTemplates tests passed!');\n}\n"], "names": ["simplifiedTemplate"], "mappings": "AAcA,MAAM,yBAAyB;AAAA,EAC7B,QAAQ;AAAA;AAAA,IAEN,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,WAAW;AAAA;AAAA,IAGX,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,oBAAoB;AAAA,IACpB,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,eAAe;AAAA;AAAA,IAGf,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,cAAc;AAAA,IACd,cAAc;AAAA;AAAA,IAGd,WAAW;AAAA,IACX,WAAW;AAAA,IACX,aAAa;AAAA,IACb,UAAU;AAAA;AAAA,IAGV,WAAW,CAAE;AAAA,IACb,aAAa,CAAE;AAAA,IACf,aAAa,CAAE;AAAA;AAAA,IAGf,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,cAAc;AAAA;AAAA,IAGd,aAAa;AAAA,IACb,OAAO;AAAA,IACP,kBAAkB;AAAA,EACnB;AAAA,EAED,cAAc;AAAA,IACZ,EAAE,OAAO,kBAAkB,aAAa,sCAAuC;AAAA,IAC/E,EAAE,OAAO,cAAc,aAAa,+BAAgC;AAAA,IACpE,EAAE,OAAO,YAAY,aAAa,mBAAoB;AAAA,IACtD,EAAE,OAAO,aAAa,aAAa,mCAAoC;AAAA,IACvE,EAAE,OAAO,eAAe,aAAa,mCAAoC;AAAA,IACzE,EAAE,OAAO,iBAAiB,aAAa,yCAA0C;AAAA,IACjF,EAAE,OAAO,iBAAiB,aAAa,mCAAoC;AAAA,IAC3E,EAAE,OAAO,cAAc,aAAa,kCAAmC;AAAA,IACvE,EAAE,OAAO,gBAAgB,aAAa,wCAAyC;AAAA,IAC/E,EAAE,OAAO,aAAa,aAAa,6BAA8B;AAAA,IACjE,EAAE,OAAO,aAAa,aAAa,mBAAoB;AAAA,IACvD,EAAE,OAAO,eAAe,aAAa,6BAA8B;AAAA,IACnE,EAAE,OAAO,YAAY,aAAa,sCAAuC;AAAA,IACzE,EAAE,OAAO,aAAa,aAAa,kDAAmD;AAAA,IACtF,EAAE,OAAO,kBAAkB,aAAa,iDAAgD;AAAA,EACzF;AAAA,EAED,iBAAiB;AAAA,IACf,EAAE,OAAO,kBAAkB,MAAM,qCAAsC;AAAA,IACvE,EAAE,OAAO,cAAc,MAAM,oDAAqD;AAAA,IAClF,EAAE,OAAO,eAAe,MAAM,qCAAsC;AAAA,IACpE,EAAE,OAAO,cAAc,MAAM,qCAAsC;AAAA,IACnE,EAAE,OAAO,eAAe,MAAM,oCAAqC;AAAA,IACnE,EAAE,OAAO,YAAY,MAAM,wCAAyC;AAAA,IACpE,EAAE,OAAO,aAAa,MAAM,qCAAsC;AAAA,IAClE,EAAE,OAAO,aAAa,MAAM,yCAA0C;AAAA,IACtE,EAAE,OAAO,aAAa,MAAM,2CAA0C;AAAA,EAC1E;AACA;AAKA,MAAM,0BAA0B;AAAA,EAC9B,GAAG;AAAA,EACH,cAAc;AAAA,IACZ,EAAE,OAAO,kBAAkB,aAAa,sCAAuC;AAAA,IAC/E,EAAE,OAAO,cAAc,aAAa,+BAAgC;AAAA,IACpE,EAAE,OAAO,YAAY,aAAa,mBAAoB;AAAA,IACtD,EAAE,OAAO,aAAa,aAAa,mCAAoC;AAAA,IACvE,EAAE,OAAO,eAAe,aAAa,mCAAoC;AAAA,IACzE,EAAE,OAAO,iBAAiB,aAAa,mCAAoC;AAAA,IAC3E,EAAE,OAAO,iBAAiB,aAAa,mCAAoC;AAAA,IAC3E,EAAE,OAAO,cAAc,aAAa,kCAAmC;AAAA,IACvE,EAAE,OAAO,gBAAgB,aAAa,kCAAmC;AAAA,IACzE,EAAE,OAAO,aAAa,aAAa,iCAAkC;AAAA,IACrE,EAAE,OAAO,aAAa,aAAa,uBAAwB;AAAA,IAC3D,EAAE,OAAO,eAAe,aAAa,iCAAkC;AAAA,IACvE,EAAE,OAAO,YAAY,aAAa,sCAAuC;AAAA,IACzE,EAAE,OAAO,aAAa,aAAa,kDAAmD;AAAA,IACtF,EAAE,OAAO,kBAAkB,aAAa,uDAAsD;AAAA,EAClG;AACA;AAKA,MAAM,qBAAqB;AAAA,EACzB,QAAQ;AAAA,IACN,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,UAAU;AAAA,IACV,aAAa;AAAA,IACb,UAAU;AAAA,IACV,gBAAgB;AAAA,EACjB;AAAA,EAED,cAAc;AAAA,IACZ,EAAE,OAAO,kBAAkB,aAAa,iBAAkB;AAAA,IAC1D,EAAE,OAAO,cAAc,aAAa,aAAc;AAAA,IAClD,EAAE,OAAO,eAAe,aAAa,sBAAuB;AAAA,IAC5D,EAAE,OAAO,cAAc,aAAa,qBAAsB;AAAA,IAC1D,EAAE,OAAO,eAAe,aAAa,sBAAuB;AAAA,IAC5D,EAAE,OAAO,YAAY,aAAa,WAAY;AAAA,IAC9C,EAAE,OAAO,eAAe,aAAa,iCAAkC;AAAA,IACvE,EAAE,OAAO,YAAY,aAAa,mBAAoB;AAAA,IACtD,EAAE,OAAO,kBAAkB,aAAa,iBAAgB;AAAA,EACzD;AAAA,EAED,iBAAiB;AAAA,IACf,EAAE,OAAO,kBAAkB,MAAM,WAAY;AAAA,IAC7C,EAAE,OAAO,eAAe,MAAM,WAAY;AAAA,IAC1C,EAAE,OAAO,eAAe,MAAM,4BAA6B;AAAA,IAC3D,EAAE,OAAO,YAAY,MAAM,WAAU;AAAA,EACzC;AACA;AAKA,MAAM,mBAAmB;AAAA,EACvB,GAAG;AAAA,EACH,QAAQ;AAAA,IACN,GAAG,uBAAuB;AAAA,IAC1B,iBAAiB;AAAA,IACjB,eAAe;AAAA,IACf,yBAAyB;AAAA,EAC1B;AAAA,EAED,cAAc;AAAA,IACZ,GAAG,uBAAuB;AAAA,IAC1B,EAAE,OAAO,mBAAmB,aAAa,0BAA2B;AAAA,IACpE,EAAE,OAAO,iBAAiB,aAAa,yBAA0B;AAAA,IACjE,EAAE,OAAO,2BAA2B,aAAa,uCAAsC;AAAA,EAC3F;AACA;AAKY,MAAC,wBAAwB;AAAA;AAAA,EAEnC,cAAc;AAAA,EACd,cAAc;AAAA,EACd,cAAc;AAAA;AAAA,EACd,cAAc;AAAA;AAAA;AAAA,EAGd,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,cAAc;AAAA;AAAA,EAGd,SAAS;AACX;AAQO,SAAS,YAAY,OAAO,YAAY,WAAW,OAAO;AAC/D,QAAM,MAAM,GAAG,IAAI,IAAI,QAAQ;AAC/B,SAAO,sBAAsB,GAAG,KAAK,sBAAsB;AAC7D;AAMO,SAAS,wBAAwB;AACtC,SAAO,OAAO,KAAK,qBAAqB,EAAE,OAAO,SAAO,QAAQ,SAAS;AAC3E;AAOO,SAAS,sBAAsB,OAAO,YAAY;AACvD,SAAO,OAAO,KAAK,qBAAqB,EACrC,OAAO,SAAO,IAAI,WAAW,GAAG,IAAI,GAAG,CAAC,EACxC,IAAI,SAAO,IAAI,MAAM,GAAG,EAAE,CAAC,CAAC;AACjC;AAGA,IAAI,OAAO,WAAW,eAAe,QAAQ,SAAS,QAAQ;AAC5D,UAAQ,IAAI,qCAAqC;AAGjD,QAAM,iBAAiB,YAAY,YAAY,KAAK;AACpD,UAAQ,IAAI,gCAAgC,iBAAiB,YAAY,QAAQ;AAEjF,QAAM,kBAAkB,YAAY,YAAY,KAAK;AACrD,UAAQ,IAAI,iCAAiC,kBAAkB,YAAY,QAAQ;AAEnF,QAAMA,sBAAqB,YAAY,cAAc,KAAK;AAC1D,UAAQ,IAAI,oCAAoCA,sBAAqB,YAAY,QAAQ;AAGzF,QAAM,qBAAqB,sBAAuB;AAClD,UAAQ,IAAI,0BAA0B,mBAAmB,QAAQ,OAAO;AAGxE,QAAM,qBAAqB,sBAAsB,UAAU;AAC3D,UAAQ,IAAI,uCAAuC,kBAAkB;AAGrE,QAAM,WAAW,YAAY,YAAY,KAAK;AAC9C,QAAM,oBAAoB,SAAS,UAAU,SAAS,gBAAgB,SAAS;AAC/E,UAAQ,IAAI,oCAAoC,oBAAoB,WAAW,QAAQ;AAEvF,UAAQ,IAAI,4CAA4C;AAC1D;"}