const COMMON_FIELDS = [
  "number",
  "issue_date",
  "place",
  "sell_date",
  "seller_name",
  "seller_tax_no",
  "seller_bank_account",
  "seller_bank",
  "seller_post_code",
  "seller_city",
  "seller_street",
  "seller_country",
  "buyer_name",
  "buyer_tax_no",
  "buyer_post_code",
  "buyer_city",
  "buyer_street",
  "buyer_country",
  "payment_type",
  "payment_to",
  "currency",
  "positions"
];
const DOCUMENT_KINDS = {
  "vat": "VAT invoice",
  "proforma": "Proforma invoice",
  "bill": "Bill",
  "receipt": "Receipt",
  "advance": "Advance invoice",
  "final": "Final invoice",
  "correction": "Correction invoice",
  "vat_mp": "MP invoice",
  "invoice_other": "Other invoice",
  "vat_margin": "Margin invoice",
  "kp": "Cash receipt",
  "kw": "Cash payment",
  "estimate": "Estimate/Order",
  "vat_rr": "RR invoice",
  "correction_note": "Correction note",
  "accounting_note": "Accounting note",
  "client_order": "Client order document",
  "dw": "Internal document",
  "wnt": "Intra-community acquisition",
  "wdt": "Intra-community supply",
  "import_service": "Import of services",
  "import_service_eu": "Import of services from EU",
  "import_products": "Import of goods - simplified procedure",
  "export_products": "Export of goods"
};
const DOCUMENT_KIND_FIELDS = {
  // VAT Invoice specific fields
  "vat": [
    "income",
    "split_payment",
    "buyer_email",
    "seller_tax_no_kind",
    "buyer_tax_no_kind",
    "status",
    "paid",
    "gtu_codes",
    "procedure_designations"
  ],
  // Proforma Invoice specific fields
  "proforma": [
    "income",
    "buyer_email",
    "status",
    "oid"
  ],
  // Bill specific fields
  "bill": [
    "income",
    "buyer_email",
    "status",
    "paid"
  ],
  // Receipt specific fields
  "receipt": [
    "income",
    "status",
    "paid"
  ],
  // Advance Invoice specific fields
  "advance": [
    "income",
    "invoice_id",
    "split_payment",
    "buyer_email"
  ],
  // Final Invoice specific fields
  "final": [
    "income",
    "from_invoice_id",
    "split_payment",
    "buyer_email"
  ],
  // Correction Invoice specific fields
  "correction": [
    "income",
    "invoice_id",
    "from_invoice_id",
    "corrected_content_before",
    "corrected_content_after",
    "buyer_email"
  ],
  // VAT MP Invoice specific fields
  "vat_mp": [
    "income",
    "split_payment",
    "buyer_email"
  ],
  // Other Invoice specific fields
  "invoice_other": [
    "income",
    "buyer_email"
  ],
  // Margin Invoice specific fields
  "vat_margin": [
    "income",
    "buyer_email"
  ],
  // Cash Receipt specific fields
  "kp": [
    "income",
    "status",
    "paid"
  ],
  // Cash Payment specific fields
  "kw": [
    "income",
    "status",
    "paid"
  ],
  // Estimate specific fields
  "estimate": [
    "income",
    "buyer_email",
    "oid"
  ],
  // VAT RR Invoice specific fields
  "vat_rr": [
    "income",
    "split_payment",
    "buyer_email"
  ],
  // Correction Note specific fields
  "correction_note": [
    "invoice_id",
    "from_invoice_id",
    "corrected_content_before",
    "corrected_content_after"
  ],
  // Accounting Note specific fields
  "accounting_note": [
    "accounting_note_kind",
    "buyer_email"
  ],
  // Client Order specific fields
  "client_order": [
    "buyer_email",
    "oid",
    "oid_unique"
  ],
  // Internal Document specific fields
  "dw": [
    "description",
    "description_long"
  ],
  // Intra-community Acquisition specific fields
  "wnt": [
    "income",
    "buyer_email",
    "seller_country",
    "buyer_country",
    "reverse_charge"
  ],
  // Intra-community Supply specific fields
  "wdt": [
    "income",
    "buyer_email",
    "seller_country",
    "buyer_country"
  ],
  // Import Services specific fields
  "import_service": [
    "income",
    "seller_country",
    "buyer_country",
    "reverse_charge"
  ],
  // Import Services EU specific fields
  "import_service_eu": [
    "income",
    "seller_country",
    "buyer_country",
    "reverse_charge"
  ],
  // Import Products specific fields
  "import_products": [
    "income",
    "seller_country",
    "buyer_country"
  ],
  // Export Products specific fields
  "export_products": [
    "income",
    "seller_country",
    "buyer_country"
  ]
};
const FIELD_DESCRIPTIONS = {
  "number": "invoice/document number following continuous numbering within fiscal year",
  "kind": "type of accounting document according to VAT law",
  "income": "accounting classification for revenue recognition: 1=income/sales revenue (company as seller), 0=expense/purchase cost (company as buyer)",
  "issue_date": "document issue date (mandatory for VAT purposes)",
  "place": "place of invoice issuance (legally required)",
  "sell_date": "sale/service execution date (VAT obligation date)",
  "category_id": "accounting category for automatic bookkeeping",
  "department_id": "cost center/profit center",
  "accounting_kind": "cost classification for accounting records and VAT settlements",
  "seller_name": "full seller name according to business registry",
  "seller_tax_no": "seller's tax ID (without PL prefix, format: **********)",
  "seller_tax_no_kind": "type of identification number (NIP/REGON/PESEL)",
  "seller_bank_account": "seller's bank account number (IBAN or domestic format)",
  "seller_bank": "name of bank holding seller's account",
  "seller_post_code": "seller's postal code (format XX-XXX)",
  "seller_city": "seller's city",
  "seller_street": "seller's address (street, number)",
  "seller_country": "seller's country code (PL for Poland, ISO 3166 format)",
  "seller_email": "seller's email address for correspondence",
  "seller_bdo_no": "number in VAT White List (BDO)",
  "use_invoice_issuer": "whether document is issued by authorized person",
  "invoice_issuer": "details of person issuing the document",
  "client_id": "buyer identifier in accounting system",
  "buyer_name": "full buyer name according to registration documents",
  "buyer_tax_no": "buyer's tax ID (without PL prefix, format: **********)",
  "buyer_tax_no_kind": "buyer's identification number type",
  "disable_tax_no_validation": "disable tax ID verification in VAT White List",
  "buyer_post_code": "buyer's postal code (format XX-XXX)",
  "buyer_city": "buyer's city",
  "buyer_street": "buyer's address (street, number)",
  "buyer_country": "buyer's country code (PL for Poland, ISO 3166 format)",
  "buyer_note": "additional information about buyer",
  "buyer_email": "buyer's email",
  "recipient_id": "recipient ID (client ID from system)",
  "recipient_name": "recipient name",
  "recipient_street": "recipient street",
  "recipient_post_code": "recipient postal code",
  "recipient_city": "recipient city",
  "recipient_country": "recipient country",
  "additional_info": "additional information about transaction",
  "additional_info_desc": "description of additional information",
  "product_id": "product identifier in system",
  "show_discount": "whether to display discount information",
  "payment_type": "payment method (transfer/cash/card/etc.)",
  "payment_to": "payment deadline date",
  "payment_to_kind": "payment deadline type (days/date/immediate)",
  "bank_account": "bank account for payment",
  "bank_account_id": "bank account ID in system",
  "currency": "transaction currency (PLN/EUR/USD/etc.)",
  "lang": "document language code",
  "exchange_currency": "exchange rate currency",
  "exchange_kind": "exchange rate type",
  "exchange_currency_rate": "currency exchange rate",
  "exchange_date": "exchange rate date",
  "internal_note": "internal note (not printed on document)",
  "invoice_id": "related invoice ID for corrections/advances",
  "from_invoice_id": "source invoice ID for final invoices",
  "oid": "order ID reference",
  "oid_unique": "unique order identifier",
  "warehouse_id": "warehouse identifier",
  "seller_person": "seller contact person",
  "buyer_first_name": "buyer first name",
  "buyer_company": "buyer company flag",
  "description": "document description",
  "description_footer": "footer description",
  "description_long": "detailed description",
  "invoice_template_id": "template ID for document formatting",
  "description_long_footer": "detailed footer description",
  "status": "document status (draft/sent/paid/etc.)",
  "paid": "payment status flag",
  "oid2": "secondary order ID",
  "warehouse_id2": "secondary warehouse ID",
  "exchange_note": "exchange rate note",
  "accounting_note_kind": "type of accounting note",
  "corrected_content_before": "content before correction",
  "corrected_content_after": "content after correction",
  "split_payment": "split payment mechanism flag",
  "gtu_codes": "GTU procedure codes for VAT",
  "procedure_designations": "procedure designations for VAT",
  "reverse_charge": "reverse charge mechanism flag",
  "positions": "array of invoice line items/positions"
};
const FIELDS_WITH_FIXED_VALUES = {
  "income": ["0", "1"],
  "payment_type": ["transfer", "cash", "card", "blik", "paypal", "other"],
  "currency": ["PLN", "EUR", "USD", "GBP", "CHF"]
};
const POSITION_FIELDS = {
  "name": "product/service name",
  "description": "detailed description of product/service",
  "additional_info": "additional information about position",
  "quantity": "quantity of items",
  "quantity_unit": "unit of measure (pcs/kg/m/etc.)",
  "price_net": "net unit price (without VAT)",
  "tax": "VAT rate percentage (23/8/5/0/zw/np)",
  "price_gross": "gross unit price (with VAT)",
  "total_price_net": "total net amount for position",
  "total_price_gross": "total gross amount for position",
  "tax_amount": "VAT amount for position",
  "code": "product code/SKU",
  "discount": "discount percentage",
  "discount_percent": "discount as percentage",
  "product_id": "product ID in system",
  "kind": "position type (normal/correction/etc.)"
};
const VAT_RATES = ["23", "8", "5", "0", "zw", "np"];
const DOCUMENT_TYPES_WITH_POSITIONS$1 = [
  "vat",
  "proforma",
  "bill",
  "receipt",
  "advance",
  "final",
  "correction",
  "vat_mp",
  "invoice_other",
  "vat_margin",
  "wdt",
  "wnt",
  "import_service",
  "import_service_eu",
  "import_products",
  "export_products"
];
const REQUIRED_FIELDS = {
  "common": ["kind", "number", "issue_date", "seller_name", "buyer_name"],
  "correction": ["invoice_id", "from_invoice_id", "corrected_content_before", "corrected_content_after"]
};
const ADDITIONAL_DOCUMENT_FIELDS = [
  "document_name",
  "total_net",
  "total_vat",
  "total_gross",
  "accounting_date",
  "contract_reference",
  "summary",
  "seller_phone",
  "buyer_phone",
  "income_type",
  "document_type",
  "ocr_used"
];
const ADDITIONAL_FIELD_DESCRIPTIONS = {
  "document_name": "full document name from header (e.g. 'VAT INVOICE', 'CORRECTION INVOICE')",
  "total_net": "control sum of net values (VAT tax base)",
  "total_vat": "control sum of VAT tax due (for JPK_VAT settlement)",
  "total_gross": "control sum to pay (gross amount = net + VAT)",
  "accounting_date": "accounting date for VAT records (may differ from issue date)",
  "contract_reference": "contract/order number for linking with commercial documentation",
  "summary": "synthetic description of document content for accounting purposes",
  "seller_phone": "seller's phone for document-related contact",
  "buyer_phone": "buyer's phone for contact",
  "income_type": "income classification for tax and accounting purposes",
  "document_type": "document type according to VAT classification",
  "ocr_used": "technical information about data extraction method",
  "payment_to_kind": "method of determining payment deadline (days/date/immediate)"
};
if (typeof require !== "undefined" && require.main === module) {
  console.log("🧪 Running local tests for fieldDefinitions...");
  console.log("✅ Test 1: Document kinds validation");
  console.log("📝 Available document kinds:", Object.keys(DOCUMENT_KINDS).length);
  console.log("📝 Sample document kinds:", Object.keys(DOCUMENT_KINDS).slice(0, 5));
  console.log("\n✅ Test 2: Common fields structure");
  console.log("📝 Common fields count:", COMMON_FIELDS.length);
  console.log("📝 Common fields sample:", COMMON_FIELDS.slice(0, 5));
  console.log("\n✅ Test 3: Document kind specific fields");
  const vatFields = DOCUMENT_KIND_FIELDS.vat;
  console.log("📝 VAT invoice specific fields:", vatFields);
  const correctionFields = DOCUMENT_KIND_FIELDS.correction;
  console.log("📝 Correction invoice specific fields:", correctionFields);
  console.log("\n✅ Test 4: Field descriptions validation");
  const sampleFields = ["number", "income", "seller_name", "buyer_name", "total_gross"];
  sampleFields.forEach((field) => {
    const description = FIELD_DESCRIPTIONS[field];
    console.log(`📝 ${field}: ${description ? description.substring(0, 50) + "..." : "NO DESCRIPTION"}`);
  });
  console.log("\n✅ Test 5: Fields with fixed values");
  console.log("📝 Income values:", FIELDS_WITH_FIXED_VALUES.income);
  console.log("📝 Currency values:", FIELDS_WITH_FIXED_VALUES.currency);
  console.log("📝 Payment type values:", FIELDS_WITH_FIXED_VALUES.payment_type);
  console.log("\n✅ Test 6: Position fields validation");
  console.log("📝 Position fields count:", Object.keys(POSITION_FIELDS).length);
  console.log("📝 Position fields:", Object.keys(POSITION_FIELDS));
  console.log("\n✅ Test 7: VAT rates validation");
  console.log("📝 Available VAT rates:", VAT_RATES);
  console.log("\n✅ Test 8: Document types with positions");
  console.log("📝 Document types with positions count:", DOCUMENT_TYPES_WITH_POSITIONS$1.length);
  console.log("📝 Sample types with positions:", DOCUMENT_TYPES_WITH_POSITIONS$1.slice(0, 5));
  console.log("\n✅ Test 9: Required fields validation");
  console.log("📝 Common required fields:", REQUIRED_FIELDS.common);
  console.log("📝 Correction required fields:", REQUIRED_FIELDS.correction);
  console.log("\n✅ Test 10: Additional document fields");
  console.log("📝 Additional fields count:", ADDITIONAL_DOCUMENT_FIELDS.length);
  console.log("📝 Additional fields sample:", ADDITIONAL_DOCUMENT_FIELDS.slice(0, 5));
  console.log("\n✅ Test 11: Field integrity check");
  let missingDescriptions = [];
  COMMON_FIELDS.forEach((field) => {
    if (!FIELD_DESCRIPTIONS[field] && !ADDITIONAL_FIELD_DESCRIPTIONS[field]) {
      missingDescriptions.push(field);
    }
  });
  if (missingDescriptions.length > 0) {
    console.log("⚠️  Fields missing descriptions:", missingDescriptions);
  } else {
    console.log("✅ All common fields have descriptions");
  }
  console.log("\n✅ Test 12: Document type coverage check");
  const allDocumentTypes = Object.keys(DOCUMENT_KINDS);
  const typesWithFields = Object.keys(DOCUMENT_KIND_FIELDS);
  const missingFieldDefinitions = allDocumentTypes.filter((type) => !typesWithFields.includes(type));
  if (missingFieldDefinitions.length > 0) {
    console.log("⚠️  Document types missing field definitions:", missingFieldDefinitions);
  } else {
    console.log("✅ All document types have field definitions");
  }
  console.log("\n🎉 All tests completed for fieldDefinitions");
}
const FAKTUROWNIA_DOCUMENT_TYPES = {
  "vat": "faktura VAT",
  "proforma": "faktura Proforma",
  "bill": "rachunek",
  "receipt": "paragon",
  "advance": "faktura zaliczkowa",
  "final": "faktura końcowa",
  "correction": "faktura korekta",
  "vat_mp": "faktura MP",
  "invoice_other": "inna faktura",
  "vat_margin": "faktura marża",
  "kp": "kasa przyjmie",
  "kw": "kasa wyda",
  "estimate": "zamówienie",
  "vat_rr": "faktura RR",
  "correction_note": "nota korygująca",
  "accounting_note": "nota księgowa",
  "client_order": "własny dokument nieksięgowy",
  "dw": "dowód wewnętrzny",
  "wnt": "Wewnątrzwspólnotowe Nabycie Towarów",
  "wdt": "Wewnątrzwspólnotowa Dostawa Towarów",
  "import_service": "import usług",
  "import_service_eu": "import usług z UE",
  "import_products": "import towarów - procedura uproszczona",
  "export_products": "eksport towarów"
};
const VALID_DOCUMENT_TYPES = [
  "vat",
  "proforma",
  "bill",
  "receipt",
  "advance",
  "final",
  "correction",
  "vat_mp",
  "invoice_other",
  "vat_margin",
  "correction_note",
  "accounting_note",
  "client_order",
  "dw",
  "wnt",
  "wdt",
  "import_service",
  "import_service_eu",
  "import_products",
  "export_products",
  "kp",
  "kw",
  "estimate",
  "vat_rr"
];
const DOCUMENT_TYPE_PATTERNS = {
  proforma: ["proforma", "pro forma", "pro-forma"],
  advance: ["zaliczk", "advance"],
  final: ["końcow", "final"],
  correction: ["korekt", "correction", "credit note"],
  vat_margin: ["marża", "margin"],
  vat_mp: ["mp", "mały podatnik"],
  vat_rr: [" rr", "rolnik"],
  wdt: ["wdt", "wewnątrzwspólnotowa dostawa"],
  wnt: ["wnt", "wewnątrzwspólnotowe nabycie"],
  import_service: ["import usług", "import of services"],
  import_service_eu: ["import usług", "ue", "eu"],
  import_products: ["import towarów", "import of goods"],
  export_products: ["eksport", "export"],
  bill: ["rachunek"],
  receipt: ["paragon"],
  correction_note: ["nota korygująca"],
  accounting_note: ["nota księgowa", "obciążen"],
  estimate: ["zamówienie", "order"],
  dw: ["dowód wewnętrzny"],
  client_order: ["własny dokument"]
};
const DOCUMENT_TYPE_REQUIREMENTS = {
  correction: ["invoice_id", "from_invoice_id", "corrected_content_before", "corrected_content_after"],
  advance: ["invoice_id"],
  final: ["from_invoice_id"],
  accounting_note: ["accounting_note_kind"],
  wnt: ["seller_country", "buyer_country", "reverse_charge"],
  wdt: ["seller_country", "buyer_country"],
  import_service: ["seller_country", "buyer_country", "reverse_charge"],
  import_service_eu: ["seller_country", "buyer_country", "reverse_charge"],
  import_products: ["seller_country", "buyer_country"],
  export_products: ["seller_country", "buyer_country"]
};
const DOCUMENT_TYPES_WITH_POSITIONS = [
  "vat",
  "proforma",
  "bill",
  "receipt",
  "advance",
  "final",
  "correction",
  "vat_mp",
  "invoice_other",
  "vat_margin",
  "wdt",
  "wnt",
  "import_service",
  "import_service_eu",
  "import_products",
  "export_products",
  "estimate"
];
const INVOICE_DOCUMENT_TYPES = [
  "vat",
  "proforma",
  "advance",
  "final",
  "correction",
  "vat_mp",
  "invoice_other",
  "vat_margin",
  "vat_rr",
  "wdt",
  "wnt",
  "import_service",
  "import_service_eu",
  "import_products",
  "export_products"
];
const RECEIPT_DOCUMENT_TYPES = [
  "bill",
  "receipt",
  "kp",
  "kw"
];
const NOTE_DOCUMENT_TYPES = [
  "correction_note",
  "accounting_note",
  "dw"
];
const ORDER_DOCUMENT_TYPES = [
  "estimate",
  "client_order"
];
const DEFAULT_DOCUMENT_TYPE = "vat";
const DOCUMENT_TYPE_CATEGORIES = {
  invoices: {
    label: "Invoices",
    types: INVOICE_DOCUMENT_TYPES
  },
  receipts: {
    label: "Receipts & Bills",
    types: RECEIPT_DOCUMENT_TYPES
  },
  notes: {
    label: "Notes & Documents",
    types: NOTE_DOCUMENT_TYPES
  },
  orders: {
    label: "Orders & Estimates",
    types: ORDER_DOCUMENT_TYPES
  }
};
function mapToFakturowniaDocumentType(documentType, documentContent = "") {
  const type = documentType.toLowerCase();
  const content = documentContent.toLowerCase();
  if (VALID_DOCUMENT_TYPES.includes(type)) {
    return type;
  }
  if (type === "invoice") {
    for (const [docType, patterns] of Object.entries(DOCUMENT_TYPE_PATTERNS)) {
      if (patterns.some((pattern) => content.includes(pattern))) {
        return docType;
      }
    }
    return "vat";
  }
  const fallbackMappings = {
    "invoice": "vat",
    "bill": "bill",
    "receipt": "receipt",
    "correction_note": "correction_note",
    "accounting_note": "accounting_note",
    "estimate": "estimate",
    "order": "estimate",
    "internal_document": "dw",
    "client_order": "client_order"
  };
  return fallbackMappings[type] || DEFAULT_DOCUMENT_TYPE;
}
function documentTypeHasPositions(documentType) {
  return DOCUMENT_TYPES_WITH_POSITIONS.includes(documentType.toLowerCase());
}
function getRequiredFieldsForDocumentType(documentType) {
  return DOCUMENT_TYPE_REQUIREMENTS[documentType.toLowerCase()] || [];
}
function isValidDocumentType(documentType) {
  return VALID_DOCUMENT_TYPES.includes(documentType.toLowerCase());
}
function getDocumentTypeCategory(documentType) {
  const type = documentType.toLowerCase();
  for (const [category, config] of Object.entries(DOCUMENT_TYPE_CATEGORIES)) {
    if (config.types.includes(type)) {
      return category;
    }
  }
  return "invoices";
}
function getDocumentTypeDescription(documentType) {
  return FAKTUROWNIA_DOCUMENT_TYPES[documentType.toLowerCase()] || documentType;
}
const GENERAL_DOCUMENT_PATTERNS = {
  invoice: [
    "invoice",
    "faktura",
    "payment",
    "płatność",
    "zapłata",
    "vat",
    "tax",
    "podatek"
  ],
  contract: [
    "contract",
    "umowa",
    "parties",
    "strony",
    "agree",
    "zgadza"
  ],
  financial_report: [
    "financial",
    "finansowy",
    "report",
    "raport",
    "sprawozdanie",
    "balance",
    "bilans",
    "profit",
    "zysk"
  ],
  agreement: [
    "agreement",
    "porozumienie",
    "terms",
    "warunki"
  ],
  legal: [
    "legal",
    "prawny",
    "law",
    "prawo",
    "court",
    "sąd"
  ],
  amendment: [
    "amendment",
    "aneks",
    "addendum",
    "załącznik",
    "supplement",
    "uzupełnienie"
  ],
  notice: [
    "notice",
    "zawiadomienie",
    "notification",
    "powiadomienie",
    "announcement",
    "ogłoszenie"
  ],
  court_order: [
    "court order",
    "nakaz sądowy",
    "judgment",
    "wyrok",
    "ruling",
    "orzeczenie",
    "verdict",
    "werdykt"
  ],
  registration: [
    "registration",
    "rejestracja",
    "certificate of incorporation",
    "świadectwo rejestracji",
    "business registry",
    "rejestr przedsiębiorców"
  ],
  email: [
    "from:",
    "to:",
    "subject:",
    "temat:",
    "@"
  ],
  tax_declaration: [
    "tax declaration",
    "deklaracja podatkowa",
    "tax return",
    "zeznanie podatkowe",
    "pit",
    "cit",
    "vat",
    "declaration",
    "deklaracja"
  ],
  government_application: [
    "application",
    "wniosek",
    "government",
    "rząd",
    "ministry",
    "ministerstwo",
    "agency",
    "agencja"
  ],
  corporate_application: [
    "application",
    "wniosek",
    "corporate",
    "korporacyjny",
    "company",
    "firma"
  ],
  resolution: [
    "resolution",
    "uchwała",
    "board resolution",
    "uchwała zarządu",
    "shareholder resolution",
    "uchwała wspólników"
  ]
};
function guessDocumentTypeWithPatterns(content) {
  const lowerContent = content.toLowerCase();
  for (const [docType, patterns] of Object.entries(GENERAL_DOCUMENT_PATTERNS)) {
    let matchCount = 0;
    let requiredMatches = 1;
    if (["invoice", "contract", "financial_report"].includes(docType)) {
      requiredMatches = 2;
    }
    for (const pattern of patterns) {
      if (lowerContent.includes(pattern)) {
        matchCount++;
        if (matchCount >= requiredMatches) {
          return docType;
        }
      }
    }
  }
  return "business_document";
}
if (typeof process !== "undefined" && process.argv && process.argv[1] && process.argv[1].includes("documentTypes.js")) {
  console.log("🧪 Running local tests for documentTypes...");
  console.log("✅ Test 1: Document type mapping");
  console.log("📝 VAT invoice mapping:", mapToFakturowniaDocumentType("invoice"));
  console.log("📝 Proforma mapping:", mapToFakturowniaDocumentType("proforma"));
  console.log("📝 Unknown type mapping:", mapToFakturowniaDocumentType("unknown"));
  console.log("\n✅ Test 2: Document type validation");
  console.log("📝 VAT is valid:", isValidDocumentType("vat"));
  console.log("📝 Unknown is valid:", isValidDocumentType("unknown"));
  console.log("📝 Proforma is valid:", isValidDocumentType("proforma"));
  console.log("\n✅ Test 3: Position requirements");
  console.log("📝 VAT has positions:", documentTypeHasPositions("vat"));
  console.log("📝 Receipt has positions:", documentTypeHasPositions("receipt"));
  console.log("📝 Note has positions:", documentTypeHasPositions("accounting_note"));
  console.log("\n✅ Test 4: Required fields");
  console.log("📝 Correction required fields:", getRequiredFieldsForDocumentType("correction"));
  console.log("📝 VAT required fields:", getRequiredFieldsForDocumentType("vat"));
  console.log("📝 WNT required fields:", getRequiredFieldsForDocumentType("wnt"));
  console.log("\n✅ Test 5: Document categories");
  console.log("📝 VAT category:", getDocumentTypeCategory("vat"));
  console.log("📝 Receipt category:", getDocumentTypeCategory("receipt"));
  console.log("📝 Note category:", getDocumentTypeCategory("accounting_note"));
  console.log("\n✅ Test 6: Document descriptions");
  console.log("📝 VAT description:", getDocumentTypeDescription("vat"));
  console.log("📝 Proforma description:", getDocumentTypeDescription("proforma"));
  console.log("📝 Unknown description:", getDocumentTypeDescription("unknown"));
  console.log("\n✅ Test 7: Pattern-based document type guessing");
  const invoiceContent = "FAKTURA VAT nr FV/2025/001 Total amount: 1230.00 PLN";
  const contractContent = "CONTRACT between parties Company A and Company B agree to terms";
  const emailContent = "From: <EMAIL> To: <EMAIL> Subject: Meeting";
  console.log("📝 Invoice content type:", guessDocumentTypeWithPatterns(invoiceContent));
  console.log("📝 Contract content type:", guessDocumentTypeWithPatterns(contractContent));
  console.log("📝 Email content type:", guessDocumentTypeWithPatterns(emailContent));
  console.log("\n✅ Test 8: Document type constants");
  console.log("📝 Valid document types count:", VALID_DOCUMENT_TYPES.length);
  console.log("📝 Invoice types count:", INVOICE_DOCUMENT_TYPES.length);
  console.log("📝 Receipt types count:", RECEIPT_DOCUMENT_TYPES.length);
  console.log("📝 Default document type:", DEFAULT_DOCUMENT_TYPE);
  console.log("\n✅ Test 9: Content-based mapping");
  const proformaContent = "FAKTURA PROFORMA nr PRO/2025/001";
  const correctionContent = "FAKTURA KOREKTA do faktury FV/2025/001";
  console.log("📝 Proforma content mapping:", mapToFakturowniaDocumentType("invoice", proformaContent));
  console.log("📝 Correction content mapping:", mapToFakturowniaDocumentType("invoice", correctionContent));
  console.log("\n✅ Test 10: Category structure");
  Object.entries(DOCUMENT_TYPE_CATEGORIES).forEach(([category, config]) => {
    console.log(`📝 Category ${category}: ${config.label} (${config.types.length} types)`);
  });
  console.log("\n🎉 All tests completed for documentTypes");
  console.log("📋 Document types configuration is working correctly");
}
class PromptGenerator {
  constructor() {
    this.fieldDescriptions = FIELD_DESCRIPTIONS;
    this.documentKindFields = DOCUMENT_KIND_FIELDS;
    this.commonFields = COMMON_FIELDS;
    this.positionFields = POSITION_FIELDS;
    this.documentTypesWithPositions = DOCUMENT_TYPES_WITH_POSITIONS;
  }
  /**
   * Generate document analysis prompt for determining document type
   * @param {string} documentContent - Document content
   * @param {Object} languageMapping - Language mapping instance
   * @returns {Object} - Prompt configuration
   */
  generateDocumentAnalysisPrompt(documentContent, languageMapping) {
    const detectedLang = languageMapping ? languageMapping.detectDocumentLanguageCode(documentContent) : "pl";
    const systemPrompt = `You are an expert accounting document analyst specializing in Polish and EU business documents.

TASK: Analyze the document and determine its type, provide description, and extract basic information.

CRITICAL INSTRUCTIONS:
1. Field names in JSON output must remain in English as defined in the structure
2. Field values should be in the detected language (${detectedLang})
3. Use exact document type codes from the provided list
4. Provide accurate descriptions based on document content

DOCUMENT TYPES:
- vat: VAT invoice
- proforma: Proforma invoice
- bill: Bill/Receipt
- correction: Correction invoice
- advance: Advance invoice
- final: Final invoice
- estimate: Estimate/Order
- accounting_note: Accounting note
- And other standard business document types

RESPONSE FORMAT: Return only valid JSON object with this structure:
{
  "documentKind": "document_type_code",
  "description": "Brief description of the document",
  "summary": "Key information summary",
  "confidence": "high|medium|low"
}`;
    const prompt = `Analyze this document and determine its type:

${documentContent}

Determine the document type and provide analysis in the specified JSON format.`;
    return {
      prompt,
      systemPrompt,
      options: {
        temperature: 0.1,
        max_tokens: 1e3
      }
    };
  }
  /**
   * Generate metadata extraction prompt
   * @param {string} documentType - Document type
   * @param {string} documentContent - Document content
   * @param {string} language - Document language
   * @param {string} companyContext - Company context
   * @param {string} ragContext - RAG context
   * @returns {Object} - Prompt configuration
   */
  generateMetadataExtractionPrompt(documentType, documentContent, language = "pl", companyContext = "", ragContext = "") {
    this.getFieldsForDocumentType(documentType);
    const structure = this.generateExpectedJsonStructure(documentType);
    const systemPrompt = `You are an expert accounting document analyst with deep knowledge of Polish accounting and VAT regulations.

TASK: Extract structured data from ${documentType} document.

CRITICAL INSTRUCTIONS:
1. Field names in JSON output must remain in English as defined in the structure
2. Field values should be extracted in the original document language (${language})
3. Use exact field names from the provided structure
4. For income field: 1=company as seller (revenue), 0=company as buyer (expense)
5. Extract all monetary amounts with proper decimal formatting
6. Ensure date formats are YYYY-MM-DD

${companyContext}

${ragContext}

EXPECTED JSON STRUCTURE:
${JSON.stringify(structure, null, 2)}`;
    const prompt = `Extract all relevant data from this ${documentType} document:

${documentContent}

Return the extracted data in the exact JSON structure provided above.`;
    return {
      prompt,
      systemPrompt,
      options: {
        temperature: 0.1,
        max_tokens: 2e3
      }
    };
  }
  /**
   * Generate positions extraction prompt
   * @param {string} documentType - Document type
   * @param {string} documentContent - Document content
   * @param {string} language - Document language
   * @param {Object} basicData - Basic document data
   * @param {string} ocrTableData - OCR table data
   * @returns {Object} - Prompt configuration
   */
  generatePositionsPrompt(documentType, documentContent, language = "pl", basicData = null, ocrTableData = "") {
    if (!this.documentTypeHasPositions(documentType)) {
      return null;
    }
    const positionStructure = this.generatePositionStructure(documentType);
    const systemPrompt = `You are an expert at extracting line items from ${documentType} documents.

TASK: Extract all positions/line items from the document.

CRITICAL INSTRUCTIONS:
1. Field names must remain in English as defined in the structure
2. Extract values in the original document language (${language})
3. Ensure numeric values use decimal notation (e.g., 123.45)
4. For correction documents, extract both before_positions and after_positions
5. Validate totals against document totals when possible

POSITION STRUCTURE:
${JSON.stringify(positionStructure, null, 2)}`;
    let prompt = `Extract all line items/positions from this ${documentType} document:

${documentContent}`;
    if (ocrTableData) {
      prompt += `

Additional table data from OCR:
${ocrTableData}`;
    }
    if (basicData) {
      prompt += `

Document totals for validation:
- Total Net: ${basicData.total_net || "N/A"}
- Total VAT: ${basicData.total_vat || "N/A"}
- Total Gross: ${basicData.total_gross || "N/A"}`;
    }
    prompt += "\n\nReturn the positions in the specified JSON structure.";
    return {
      prompt,
      systemPrompt,
      options: {
        temperature: 0.1,
        max_tokens: 3e3
      }
    };
  }
  /**
   * Generate context-aware document prompt (from DocumentFields)
   * @param {string} documentType - Document type
   * @param {string} documentContent - Document content
   * @param {string} language - Document language
   * @param {string} companyContext - Company context
   * @param {string} ragContext - RAG context
   * @param {Object} documentTypeResult - Document type analysis result
   * @returns {string} - Generated prompt
   */
  generateContextAwareDocumentPrompt(documentType, documentContent, language, companyContext, ragContext, documentTypeResult) {
    const structure = this.generateExpectedJsonStructure(documentType);
    let prompt = `You are an expert accounting document analyst. Extract structured data from this ${documentType} document.

CRITICAL INSTRUCTIONS:
1. Field names in JSON must remain in English
2. Field values should be in the detected language (${language})
3. For income field: determine if company is buyer (0) or seller (1)
4. Extract all monetary amounts accurately
5. Use YYYY-MM-DD format for dates

${companyContext}

${ragContext}`;
    if (documentTypeResult && documentTypeResult.description) {
      prompt += `

Document Analysis Context:
- Type: ${documentTypeResult.documentType}
- Description: ${documentTypeResult.description}
- Summary: ${documentTypeResult.summary || "N/A"}`;
    }
    prompt += `

EXPECTED JSON STRUCTURE:
${JSON.stringify(structure, null, 2)}

DOCUMENT CONTENT:
${documentContent}

Extract the data and return it in the exact JSON structure above.`;
    return prompt;
  }
  /**
   * Get fields for a specific document type
   * @param {string} documentType - Document type
   * @returns {Array} - Array of field names
   */
  getFieldsForDocumentType(documentType) {
    const commonFields = [...this.commonFields];
    const specificFields = this.documentKindFields[documentType] || [];
    return [...commonFields, ...specificFields];
  }
  /**
   * Generate expected JSON structure for document type
   * @param {string} documentType - Document type
   * @returns {Object} - JSON structure
   */
  generateExpectedJsonStructure(documentType) {
    const fields = this.getFieldsForDocumentType(documentType);
    const structure = {};
    fields.forEach((field) => {
      if (field === "positions") {
        if (this.documentTypeHasPositions(documentType)) {
          structure.positions = [this.generatePositionStructure(documentType)];
        }
      } else {
        const description = this.fieldDescriptions[field] || `${field} value`;
        structure[field] = `(${description})`;
      }
    });
    return structure;
  }
  /**
   * Generate position structure for document type
   * @param {string} documentType - Document type
   * @returns {Object} - Position structure
   */
  generatePositionStructure(documentType) {
    const structure = {};
    Object.keys(this.positionFields).forEach((field) => {
      const description = this.positionFields[field];
      structure[field] = `(${description})`;
    });
    if (documentType === "correction") {
      structure.kind = "(position type: normal|correction)";
      structure.correction_before_attributes = "(original position data)";
      structure.correction_after_attributes = "(corrected position data)";
    }
    return structure;
  }
  /**
   * Check if document type has positions
   * @param {string} documentType - Document type
   * @returns {boolean} - Whether document type has positions
   */
  documentTypeHasPositions(documentType) {
    return this.documentTypesWithPositions.includes(documentType.toLowerCase());
  }
  /**
   * Generate validation prompt for extracted data
   * @param {Object} extractedData - Extracted data
   * @param {string} documentContent - Original document content
   * @returns {Object} - Validation prompt configuration
   */
  generateValidationPrompt(extractedData, documentContent) {
    const systemPrompt = `You are a data validation expert for accounting documents.

TASK: Validate the extracted data against the original document and identify any inconsistencies.

VALIDATION CHECKS:
1. Verify all monetary amounts match the document
2. Check date formats and consistency
3. Validate company information accuracy
4. Ensure position totals match document totals
5. Check for missing critical information

RESPONSE FORMAT: Return JSON with validation results:
{
  "valid": true/false,
  "errors": ["list of errors found"],
  "warnings": ["list of warnings"],
  "suggestions": ["list of improvement suggestions"]
}`;
    const prompt = `Validate this extracted data against the original document:

EXTRACTED DATA:
${JSON.stringify(extractedData, null, 2)}

ORIGINAL DOCUMENT:
${documentContent}

Perform thorough validation and return results in the specified format.`;
    return {
      prompt,
      systemPrompt,
      options: {
        temperature: 0.1,
        max_tokens: 1500
      }
    };
  }
}
const promptGenerator = new PromptGenerator();
if (typeof window !== "undefined") {
  window.PromptGenerator = PromptGenerator;
  window.promptGenerator = promptGenerator;
}
if (typeof process !== "undefined" && process.argv && process.argv[1] && process.argv[1].includes("PromptGenerator.js")) {
  console.log("🧪 Running local tests for PromptGenerator...");
  const generator = new PromptGenerator();
  const mockLanguageMapping = {
    detectDocumentLanguageCode: () => "pl"
  };
  const sampleCompanyData = {
    name: "Test Sp. z o.o.",
    taxId: "**********",
    address: "ul. Testowa 123",
    city: "Warszawa",
    postCode: "00-001"
  };
  const sampleDocumentText = `
    FAKTURA VAT
    Numer: FV/2025/001
    Data wystawienia: 2025-01-27
    Sprzedawca: ABC Sp. z o.o.
    NIP: **********
    Nabywca: XYZ Sp. z o.o.
    NIP: 0987654321
    `;
  console.log("✅ Test 1: Document analysis prompt generation");
  try {
    const analysisPrompt = generator.generateDocumentAnalysisPrompt(sampleDocumentText, mockLanguageMapping);
    console.log("📝 Generated document analysis prompt");
    console.log("📊 System prompt length:", analysisPrompt.systemPrompt.length);
    console.log("📊 User prompt length:", analysisPrompt.prompt.length);
    console.log("📊 Temperature:", analysisPrompt.options.temperature);
  } catch (error) {
    console.log("❌ Error in document analysis prompt generation:", error.message);
  }
  console.log("\n✅ Test 2: Metadata extraction prompt generation");
  try {
    const companyContext = `Company: ${sampleCompanyData.name} (${sampleCompanyData.taxId})`;
    const metadataPrompt = generator.generateMetadataExtractionPrompt("vat", sampleDocumentText, "pl", companyContext);
    console.log("📝 Generated metadata extraction prompt");
    console.log("📊 System prompt length:", metadataPrompt.systemPrompt.length);
    console.log("📊 User prompt length:", metadataPrompt.prompt.length);
    console.log("📊 Max tokens:", metadataPrompt.options.max_tokens);
  } catch (error) {
    console.log("❌ Error in metadata prompt generation:", error.message);
  }
  console.log("\n✅ Test 3: Position extraction prompt generation");
  try {
    const basicData = { total_net: "1000.00", total_vat: "230.00", total_gross: "1230.00" };
    const positionPrompt = generator.generatePositionsPrompt("vat", sampleDocumentText, "pl", basicData);
    console.log("📝 Generated position extraction prompt");
    console.log("📊 System prompt length:", positionPrompt.systemPrompt.length);
    console.log("📊 User prompt length:", positionPrompt.prompt.length);
    console.log("📊 Max tokens:", positionPrompt.options.max_tokens);
  } catch (error) {
    console.log("❌ Error in position prompt generation:", error.message);
  }
  console.log("\n✅ Test 4: JSON structure generation");
  try {
    const vatStructure = generator.generateExpectedJsonStructure("vat");
    const correctionStructure = generator.generateExpectedJsonStructure("correction");
    console.log("📝 Generated JSON structures");
    console.log("📊 VAT structure fields:", Object.keys(vatStructure).length);
    console.log("📊 Correction structure fields:", Object.keys(correctionStructure).length);
    console.log("📊 VAT has positions:", "positions" in vatStructure);
  } catch (error) {
    console.log("❌ Error in JSON structure generation:", error.message);
  }
  console.log("\n✅ Test 5: Document type position checking");
  try {
    const vatHasPositions = generator.documentTypeHasPositions("vat");
    const billHasPositions = generator.documentTypeHasPositions("bill");
    const unknownHasPositions = generator.documentTypeHasPositions("unknown");
    console.log("📝 Document type position checking");
    console.log("📊 VAT has positions:", vatHasPositions);
    console.log("📊 Bill has positions:", billHasPositions);
    console.log("📊 Unknown has positions:", unknownHasPositions);
  } catch (error) {
    console.log("❌ Error in document type checking:", error.message);
  }
  console.log("\n✅ Test 6: Validation prompt generation");
  try {
    const extractedData = {
      kind: "vat",
      number: "FV/2025/001",
      total_net: "1000.00",
      total_gross: "1230.00"
    };
    const validationPrompt = generator.generateValidationPrompt(extractedData, sampleDocumentText);
    console.log("📝 Generated validation prompt");
    console.log("📊 System prompt length:", validationPrompt.systemPrompt.length);
    console.log("📊 User prompt length:", validationPrompt.prompt.length);
  } catch (error) {
    console.log("❌ Error in validation prompt generation:", error.message);
  }
  console.log("\n🎉 All tests completed for PromptGenerator");
}
export {
  PromptGenerator,
  promptGenerator as default
};
//# sourceMappingURL=PromptGenerator-B5Y2Pl6c.js.map
