{"version": 3, "file": "PromptGenerator-B5Y2Pl6c.js", "sources": ["../../../src/core/config/fieldDefinitions.js", "../../../src/core/config/documentTypes.js", "../../../src/components/shared/utilities/PromptGenerator.js"], "sourcesContent": ["/**\n * Field Definitions Configuration\n * Extracted from core/prompt_generators/accounting_fields.js\n * Contains all field definitions, document types, and validation rules\n */\n\n// Common fields that appear in most accounting documents\nexport const COMMON_FIELDS = [\n  'number',\n  'issue_date',\n  'place',\n  'sell_date',\n  'seller_name',\n  'seller_tax_no',\n  'seller_bank_account',\n  'seller_bank',\n  'seller_post_code',\n  'seller_city',\n  'seller_street',\n  'seller_country',\n  'buyer_name',\n  'buyer_tax_no',\n  'buyer_post_code',\n  'buyer_city',\n  'buyer_street',\n  'buyer_country',\n  'payment_type',\n  'payment_to',\n  'currency',\n  'positions'\n];\n\n// Document kinds with their descriptions\nexport const DOCUMENT_KINDS = {\n  'vat': 'VAT invoice',\n  'proforma': 'Proforma invoice',\n  'bill': 'Bill',\n  'receipt': 'Receipt',\n  'advance': 'Advance invoice',\n  'final': 'Final invoice',\n  'correction': 'Correction invoice',\n  'vat_mp': 'MP invoice',\n  'invoice_other': 'Other invoice',\n  'vat_margin': 'Margin invoice',\n  'kp': 'Cash receipt',\n  'kw': 'Cash payment',\n  'estimate': 'Estimate/Order',\n  'vat_rr': 'RR invoice',\n  'correction_note': 'Correction note',\n  'accounting_note': 'Accounting note',\n  'client_order': 'Client order document',\n  'dw': 'Internal document',\n  'wnt': 'Intra-community acquisition',\n  'wdt': 'Intra-community supply',\n  'import_service': 'Import of services',\n  'import_service_eu': 'Import of services from EU',\n  'import_products': 'Import of goods - simplified procedure',\n  'export_products': 'Export of goods'\n};\n\n// Specific fields for each document kind\nexport const DOCUMENT_KIND_FIELDS = {\n  // VAT Invoice specific fields\n  'vat': [\n    'income',\n    'split_payment',\n    'buyer_email',\n    'seller_tax_no_kind',\n    'buyer_tax_no_kind',\n    'status',\n    'paid',\n    'gtu_codes',\n    'procedure_designations'\n  ],\n\n  // Proforma Invoice specific fields\n  'proforma': [\n    'income',\n    'buyer_email',\n    'status',\n    'oid'\n  ],\n\n  // Bill specific fields\n  'bill': [\n    'income',\n    'buyer_email',\n    'status',\n    'paid'\n  ],\n\n  // Receipt specific fields\n  'receipt': [\n    'income',\n    'status',\n    'paid'\n  ],\n\n  // Advance Invoice specific fields\n  'advance': [\n    'income',\n    'invoice_id',\n    'split_payment',\n    'buyer_email'\n  ],\n\n  // Final Invoice specific fields\n  'final': [\n    'income',\n    'from_invoice_id',\n    'split_payment',\n    'buyer_email'\n  ],\n\n  // Correction Invoice specific fields\n  'correction': [\n    'income',\n    'invoice_id',\n    'from_invoice_id',\n    'corrected_content_before',\n    'corrected_content_after',\n    'buyer_email'\n  ],\n\n  // VAT MP Invoice specific fields\n  'vat_mp': [\n    'income',\n    'split_payment',\n    'buyer_email'\n  ],\n\n  // Other Invoice specific fields\n  'invoice_other': [\n    'income',\n    'buyer_email'\n  ],\n\n  // Margin Invoice specific fields\n  'vat_margin': [\n    'income',\n    'buyer_email'\n  ],\n\n  // Cash Receipt specific fields\n  'kp': [\n    'income',\n    'status',\n    'paid'\n  ],\n\n  // Cash Payment specific fields\n  'kw': [\n    'income',\n    'status',\n    'paid'\n  ],\n\n  // Estimate specific fields\n  'estimate': [\n    'income',\n    'buyer_email',\n    'oid'\n  ],\n\n  // VAT RR Invoice specific fields\n  'vat_rr': [\n    'income',\n    'split_payment',\n    'buyer_email'\n  ],\n\n  // Correction Note specific fields\n  'correction_note': [\n    'invoice_id',\n    'from_invoice_id',\n    'corrected_content_before',\n    'corrected_content_after'\n  ],\n\n  // Accounting Note specific fields\n  'accounting_note': [\n    'accounting_note_kind',\n    'buyer_email'\n  ],\n\n  // Client Order specific fields\n  'client_order': [\n    'buyer_email',\n    'oid',\n    'oid_unique'\n  ],\n\n  // Internal Document specific fields\n  'dw': [\n    'description',\n    'description_long'\n  ],\n\n  // Intra-community Acquisition specific fields\n  'wnt': [\n    'income',\n    'buyer_email',\n    'seller_country',\n    'buyer_country',\n    'reverse_charge'\n  ],\n\n  // Intra-community Supply specific fields\n  'wdt': [\n    'income',\n    'buyer_email',\n    'seller_country',\n    'buyer_country'\n  ],\n\n  // Import Services specific fields\n  'import_service': [\n    'income',\n    'seller_country',\n    'buyer_country',\n    'reverse_charge'\n  ],\n\n  // Import Services EU specific fields\n  'import_service_eu': [\n    'income',\n    'seller_country',\n    'buyer_country',\n    'reverse_charge'\n  ],\n\n  // Import Products specific fields\n  'import_products': [\n    'income',\n    'seller_country',\n    'buyer_country'\n  ],\n\n  // Export Products specific fields\n  'export_products': [\n    'income',\n    'seller_country',\n    'buyer_country'\n  ]\n};\n\n// Field descriptions with accounting expertise\nexport const FIELD_DESCRIPTIONS = {\n  'number': 'invoice/document number following continuous numbering within fiscal year',\n  'kind': 'type of accounting document according to VAT law',\n  'income': 'accounting classification for revenue recognition: 1=income/sales revenue (company as seller), 0=expense/purchase cost (company as buyer)',\n  'issue_date': 'document issue date (mandatory for VAT purposes)',\n  'place': 'place of invoice issuance (legally required)',\n  'sell_date': 'sale/service execution date (VAT obligation date)',\n  'category_id': 'accounting category for automatic bookkeeping',\n  'department_id': 'cost center/profit center',\n  'accounting_kind': 'cost classification for accounting records and VAT settlements',\n  'seller_name': 'full seller name according to business registry',\n  'seller_tax_no': \"seller's tax ID (without PL prefix, format: **********)\",\n  'seller_tax_no_kind': 'type of identification number (NIP/REGON/PESEL)',\n  'seller_bank_account': \"seller's bank account number (IBAN or domestic format)\",\n  'seller_bank': \"name of bank holding seller's account\",\n  'seller_post_code': \"seller's postal code (format XX-XXX)\",\n  'seller_city': \"seller's city\",\n  'seller_street': \"seller's address (street, number)\",\n  'seller_country': \"seller's country code (PL for Poland, ISO 3166 format)\",\n  'seller_email': \"seller's email address for correspondence\",\n  'seller_bdo_no': 'number in VAT White List (BDO)',\n  'use_invoice_issuer': 'whether document is issued by authorized person',\n  'invoice_issuer': 'details of person issuing the document',\n  'client_id': 'buyer identifier in accounting system',\n  'buyer_name': 'full buyer name according to registration documents',\n  'buyer_tax_no': \"buyer's tax ID (without PL prefix, format: **********)\",\n  'buyer_tax_no_kind': \"buyer's identification number type\",\n  'disable_tax_no_validation': 'disable tax ID verification in VAT White List',\n  'buyer_post_code': \"buyer's postal code (format XX-XXX)\",\n  'buyer_city': \"buyer's city\",\n  'buyer_street': \"buyer's address (street, number)\",\n  'buyer_country': \"buyer's country code (PL for Poland, ISO 3166 format)\",\n  'buyer_note': 'additional information about buyer',\n  'buyer_email': \"buyer's email\",\n  'recipient_id': 'recipient ID (client ID from system)',\n  'recipient_name': 'recipient name',\n  'recipient_street': 'recipient street',\n  'recipient_post_code': 'recipient postal code',\n  'recipient_city': 'recipient city',\n  'recipient_country': 'recipient country',\n  'additional_info': 'additional information about transaction',\n  'additional_info_desc': 'description of additional information',\n  'product_id': 'product identifier in system',\n  'show_discount': 'whether to display discount information',\n  'payment_type': 'payment method (transfer/cash/card/etc.)',\n  'payment_to': 'payment deadline date',\n  'payment_to_kind': 'payment deadline type (days/date/immediate)',\n  'bank_account': 'bank account for payment',\n  'bank_account_id': 'bank account ID in system',\n  'currency': 'transaction currency (PLN/EUR/USD/etc.)',\n  'lang': 'document language code',\n  'exchange_currency': 'exchange rate currency',\n  'exchange_kind': 'exchange rate type',\n  'exchange_currency_rate': 'currency exchange rate',\n  'exchange_date': 'exchange rate date',\n  'internal_note': 'internal note (not printed on document)',\n  'invoice_id': 'related invoice ID for corrections/advances',\n  'from_invoice_id': 'source invoice ID for final invoices',\n  'oid': 'order ID reference',\n  'oid_unique': 'unique order identifier',\n  'warehouse_id': 'warehouse identifier',\n  'seller_person': 'seller contact person',\n  'buyer_first_name': 'buyer first name',\n  'buyer_company': 'buyer company flag',\n  'description': 'document description',\n  'description_footer': 'footer description',\n  'description_long': 'detailed description',\n  'invoice_template_id': 'template ID for document formatting',\n  'description_long_footer': 'detailed footer description',\n  'status': 'document status (draft/sent/paid/etc.)',\n  'paid': 'payment status flag',\n  'oid2': 'secondary order ID',\n  'warehouse_id2': 'secondary warehouse ID',\n  'exchange_note': 'exchange rate note',\n  'accounting_note_kind': 'type of accounting note',\n  'corrected_content_before': 'content before correction',\n  'corrected_content_after': 'content after correction',\n  'split_payment': 'split payment mechanism flag',\n  'gtu_codes': 'GTU procedure codes for VAT',\n  'procedure_designations': 'procedure designations for VAT',\n  'reverse_charge': 'reverse charge mechanism flag',\n  'positions': 'array of invoice line items/positions'\n};\n\n// Fields with fixed/predefined values\nexport const FIELDS_WITH_FIXED_VALUES = {\n  'income': ['0', '1'],\n  'use_invoice_issuer': ['0', '1'],\n  'disable_tax_no_validation': ['0', '1'],\n  'buyer_company': ['0', '1'],\n  'paid': ['0', '1'],\n  'split_payment': ['0', '1'],\n  'reverse_charge': ['0', '1'],\n  'show_discount': ['0', '1'],\n  'status': ['draft', 'sent', 'paid', 'partial', 'overdue'],\n  'payment_type': ['transfer', 'cash', 'card', 'blik', 'paypal', 'other'],\n  'currency': ['PLN', 'EUR', 'USD', 'GBP', 'CHF'],\n  'lang': ['pl', 'en', 'de', 'fr', 'es', 'it'],\n  'seller_tax_no_kind': ['nip', 'regon', 'pesel', 'eu_vat_id'],\n  'buyer_tax_no_kind': ['nip', 'regon', 'pesel', 'eu_vat_id'],\n  'accounting_kind': ['cost', 'revenue', 'asset', 'liability', 'equity'],\n  'accounting_note_kind': ['correction', 'adjustment', 'reclassification', 'other']\n};\n\n// Position/line item field definitions\nexport const POSITION_FIELDS = {\n  'name': 'product/service name',\n  'description': 'detailed description of product/service',\n  'additional_info': 'additional information about position',\n  'quantity': 'quantity of items',\n  'quantity_unit': 'unit of measure (pcs/kg/m/etc.)',\n  'price_net': 'net unit price (without VAT)',\n  'tax': 'VAT rate percentage (23/8/5/0/zw/np)',\n  'price_gross': 'gross unit price (with VAT)',\n  'total_price_net': 'total net amount for position',\n  'total_price_gross': 'total gross amount for position',\n  'tax_amount': 'VAT amount for position',\n  'code': 'product code/SKU',\n  'discount': 'discount percentage',\n  'discount_percent': 'discount as percentage',\n  'product_id': 'product ID in system',\n  'kind': 'position type (normal/correction/etc.)'\n};\n\n// VAT rates allowed in Poland\nexport const VAT_RATES = ['23', '8', '5', '0', 'zw', 'np'];\n\n// Document types that typically have positions/line items\nexport const DOCUMENT_TYPES_WITH_POSITIONS = [\n  'vat', 'proforma', 'bill', 'receipt', 'advance', 'final', 'correction',\n  'vat_mp', 'invoice_other', 'vat_margin', 'wdt', 'wnt', 'import_service',\n  'import_service_eu', 'import_products', 'export_products'\n];\n\n// Required fields for each document type\nexport const REQUIRED_FIELDS = {\n  'common': ['kind', 'number', 'issue_date', 'seller_name', 'buyer_name'],\n  'correction': ['invoice_id', 'from_invoice_id', 'corrected_content_before', 'corrected_content_after'],\n  'advance': ['invoice_id'],\n  'final': ['invoice_id'],\n  'accounting_note': ['accounting_note_kind']\n};\n\n// Additional fields from DocumentFields (non-overlapping with AccountingFields)\nexport const ADDITIONAL_DOCUMENT_FIELDS = [\n  'document_name',\n  'total_net',\n  'total_vat',\n  'total_gross',\n  'accounting_date',\n  'contract_reference',\n  'summary',\n  'seller_phone',\n  'buyer_phone',\n  'income_type',\n  'document_type',\n  'ocr_used'\n];\n\n// Additional field descriptions (from DocumentFields)\nexport const ADDITIONAL_FIELD_DESCRIPTIONS = {\n  'document_name': \"full document name from header (e.g. 'VAT INVOICE', 'CORRECTION INVOICE')\",\n  'total_net': 'control sum of net values (VAT tax base)',\n  'total_vat': 'control sum of VAT tax due (for JPK_VAT settlement)',\n  'total_gross': 'control sum to pay (gross amount = net + VAT)',\n  'accounting_date': 'accounting date for VAT records (may differ from issue date)',\n  'contract_reference': 'contract/order number for linking with commercial documentation',\n  'summary': 'synthetic description of document content for accounting purposes',\n  'seller_phone': \"seller's phone for document-related contact\",\n  'buyer_phone': \"buyer's phone for contact\",\n  'income_type': 'income classification for tax and accounting purposes',\n  'document_type': 'document type according to VAT classification',\n  'ocr_used': 'technical information about data extraction method',\n  'payment_to_kind': 'method of determining payment deadline (days/date/immediate)'\n};\n\n// Additional fields with fixed values (from DocumentFields)\nexport const ADDITIONAL_FIELDS_WITH_FIXED_VALUES = {\n  'income_type': ['income', 'expense', 'other'],\n  'payment_to_kind': ['days', 'date', 'immediate', 'on_delivery'],\n  'ocr_used': ['true', 'false']\n};\n\n// Document types that require additional fields\nexport const DOCUMENT_TYPES_REQUIRING_ADDITIONAL_FIELDS = [\n  'vat', 'proforma', 'bill', 'receipt', 'advance', 'final',\n  'correction', 'vat_mp', 'invoice_other', 'vat_margin',\n  'kp', 'kw', 'estimate', 'vat_rr', 'wdt', 'ue', 'accounting_note'\n];\n\n// Required additional fields by document type\nexport const REQUIRED_ADDITIONAL_FIELDS = {\n  'vat': ['document_name', 'total_gross'],\n  'proforma': ['document_name', 'total_gross'],\n  'bill': ['document_name', 'total_gross'],\n  'receipt': ['document_name', 'total_gross'],\n  'default': ['document_name']\n};\n\n// ===== LOCAL TESTING (Node.js equivalent of Python's if __name__ == '__main__') =====\nif (typeof require !== 'undefined' && require.main === module) {\n  console.log('🧪 Running local tests for fieldDefinitions...');\n\n  console.log('✅ Test 1: Document kinds validation');\n  console.log('📝 Available document kinds:', Object.keys(DOCUMENT_KINDS).length);\n  console.log('📝 Sample document kinds:', Object.keys(DOCUMENT_KINDS).slice(0, 5));\n\n  console.log('\\n✅ Test 2: Common fields structure');\n  console.log('📝 Common fields count:', COMMON_FIELDS.length);\n  console.log('📝 Common fields sample:', COMMON_FIELDS.slice(0, 5));\n\n  console.log('\\n✅ Test 3: Document kind specific fields');\n  const vatFields = DOCUMENT_KIND_FIELDS.vat;\n  console.log('📝 VAT invoice specific fields:', vatFields);\n\n  const correctionFields = DOCUMENT_KIND_FIELDS.correction;\n  console.log('📝 Correction invoice specific fields:', correctionFields);\n\n  console.log('\\n✅ Test 4: Field descriptions validation');\n  const sampleFields = ['number', 'income', 'seller_name', 'buyer_name', 'total_gross'];\n  sampleFields.forEach(field => {\n    const description = FIELD_DESCRIPTIONS[field];\n    console.log(`📝 ${field}: ${description ? description.substring(0, 50) + '...' : 'NO DESCRIPTION'}`);\n  });\n\n  console.log('\\n✅ Test 5: Fields with fixed values');\n  console.log('📝 Income values:', FIELDS_WITH_FIXED_VALUES.income);\n  console.log('📝 Currency values:', FIELDS_WITH_FIXED_VALUES.currency);\n  console.log('📝 Payment type values:', FIELDS_WITH_FIXED_VALUES.payment_type);\n\n  console.log('\\n✅ Test 6: Position fields validation');\n  console.log('📝 Position fields count:', Object.keys(POSITION_FIELDS).length);\n  console.log('📝 Position fields:', Object.keys(POSITION_FIELDS));\n\n  console.log('\\n✅ Test 7: VAT rates validation');\n  console.log('📝 Available VAT rates:', VAT_RATES);\n\n  console.log('\\n✅ Test 8: Document types with positions');\n  console.log('📝 Document types with positions count:', DOCUMENT_TYPES_WITH_POSITIONS.length);\n  console.log('📝 Sample types with positions:', DOCUMENT_TYPES_WITH_POSITIONS.slice(0, 5));\n\n  console.log('\\n✅ Test 9: Required fields validation');\n  console.log('📝 Common required fields:', REQUIRED_FIELDS.common);\n  console.log('📝 Correction required fields:', REQUIRED_FIELDS.correction);\n\n  console.log('\\n✅ Test 10: Additional document fields');\n  console.log('📝 Additional fields count:', ADDITIONAL_DOCUMENT_FIELDS.length);\n  console.log('📝 Additional fields sample:', ADDITIONAL_DOCUMENT_FIELDS.slice(0, 5));\n\n  console.log('\\n✅ Test 11: Field integrity check');\n  let missingDescriptions = [];\n  COMMON_FIELDS.forEach(field => {\n    if (!FIELD_DESCRIPTIONS[field] && !ADDITIONAL_FIELD_DESCRIPTIONS[field]) {\n      missingDescriptions.push(field);\n    }\n  });\n\n  if (missingDescriptions.length > 0) {\n    console.log('⚠️  Fields missing descriptions:', missingDescriptions);\n  } else {\n    console.log('✅ All common fields have descriptions');\n  }\n\n  console.log('\\n✅ Test 12: Document type coverage check');\n  const allDocumentTypes = Object.keys(DOCUMENT_KINDS);\n  const typesWithFields = Object.keys(DOCUMENT_KIND_FIELDS);\n  const missingFieldDefinitions = allDocumentTypes.filter(type => !typesWithFields.includes(type));\n\n  if (missingFieldDefinitions.length > 0) {\n    console.log('⚠️  Document types missing field definitions:', missingFieldDefinitions);\n  } else {\n    console.log('✅ All document types have field definitions');\n  }\n\n  console.log('\\n🎉 All tests completed for fieldDefinitions');\n}\n", "/**\n * Document Types Configuration\n * Extracted from core/prompt_generators/document_types.js\n * Contains document type mappings and validation rules\n */\n\n// Fakturownia document types with descriptions\nexport const FAKTUROWNIA_DOCUMENT_TYPES = {\n  'vat': 'faktura VAT',\n  'proforma': 'faktura Proforma',\n  'bill': 'rachunek',\n  'receipt': 'paragon',\n  'advance': 'faktura zaliczkowa',\n  'final': 'faktura końcowa',\n  'correction': 'faktura korekta',\n  'vat_mp': 'faktura MP',\n  'invoice_other': 'inna faktura',\n  'vat_margin': 'faktura marża',\n  'kp': 'kasa przyjmie',\n  'kw': 'kasa wyda',\n  'estimate': 'zamówienie',\n  'vat_rr': 'faktura RR',\n  'correction_note': 'nota korygująca',\n  'accounting_note': 'nota księgowa',\n  'client_order': 'własny dokument nieksięgowy',\n  'dw': 'dowód wewnętrzny',\n  'wnt': 'Wewnątrzwspólnotowe Nabycie Towarów',\n  'wdt': 'Wewnątrzwspólnotowa Dostawa Towarów',\n  'import_service': 'import usług',\n  'import_service_eu': 'import usług z UE',\n  'import_products': 'import towarów - procedura uproszczona',\n  'export_products': 'eksport towarów'\n};\n\n// Valid document types for validation\nexport const VALID_DOCUMENT_TYPES = [\n  'vat', 'proforma', 'bill', 'receipt', 'advance', 'final', 'correction',\n  'vat_mp', 'invoice_other', 'vat_margin', 'correction_note', 'accounting_note',\n  'client_order', 'dw', 'wnt', 'wdt', 'import_service', 'import_service_eu',\n  'import_products', 'export_products', 'kp', 'kw', 'estimate', 'vat_rr'\n];\n\n// Document type detection patterns for content analysis\nexport const DOCUMENT_TYPE_PATTERNS = {\n  proforma: ['proforma', 'pro forma', 'pro-forma'],\n  advance: ['zaliczk', 'advance'],\n  final: ['końcow', 'final'],\n  correction: ['korekt', 'correction', 'credit note'],\n  vat_margin: ['marża', 'margin'],\n  vat_mp: ['mp', 'mały podatnik'],\n  vat_rr: [' rr', 'rolnik'],\n  wdt: ['wdt', 'wewnątrzwspólnotowa dostawa'],\n  wnt: ['wnt', 'wewnątrzwspólnotowe nabycie'],\n  import_service: ['import usług', 'import of services'],\n  import_service_eu: ['import usług', 'ue', 'eu'],\n  import_products: ['import towarów', 'import of goods'],\n  export_products: ['eksport', 'export'],\n  bill: ['rachunek'],\n  receipt: ['paragon'],\n  correction_note: ['nota korygująca'],\n  accounting_note: ['nota księgowa', 'obciążen'],\n  estimate: ['zamówienie', 'order'],\n  dw: ['dowód wewnętrzny'],\n  client_order: ['własny dokument']\n};\n\n// Document types that require specific fields\nexport const DOCUMENT_TYPE_REQUIREMENTS = {\n  correction: ['invoice_id', 'from_invoice_id', 'corrected_content_before', 'corrected_content_after'],\n  advance: ['invoice_id'],\n  final: ['from_invoice_id'],\n  accounting_note: ['accounting_note_kind'],\n  wnt: ['seller_country', 'buyer_country', 'reverse_charge'],\n  wdt: ['seller_country', 'buyer_country'],\n  import_service: ['seller_country', 'buyer_country', 'reverse_charge'],\n  import_service_eu: ['seller_country', 'buyer_country', 'reverse_charge'],\n  import_products: ['seller_country', 'buyer_country'],\n  export_products: ['seller_country', 'buyer_country']\n};\n\n// Document types that typically have line items/positions\nexport const DOCUMENT_TYPES_WITH_POSITIONS = [\n  'vat', 'proforma', 'bill', 'receipt', 'advance', 'final', 'correction',\n  'vat_mp', 'invoice_other', 'vat_margin', 'wdt', 'wnt', 'import_service',\n  'import_service_eu', 'import_products', 'export_products', 'estimate'\n];\n\n// Document types that are invoices (for classification purposes)\nexport const INVOICE_DOCUMENT_TYPES = [\n  'vat', 'proforma', 'advance', 'final', 'correction', 'vat_mp',\n  'invoice_other', 'vat_margin', 'vat_rr', 'wdt', 'wnt',\n  'import_service', 'import_service_eu', 'import_products', 'export_products'\n];\n\n// Document types that are receipts/bills (for classification purposes)\nexport const RECEIPT_DOCUMENT_TYPES = [\n  'bill', 'receipt', 'kp', 'kw'\n];\n\n// Document types that are notes (for classification purposes)\nexport const NOTE_DOCUMENT_TYPES = [\n  'correction_note', 'accounting_note', 'dw'\n];\n\n// Document types that are orders/estimates (for classification purposes)\nexport const ORDER_DOCUMENT_TYPES = [\n  'estimate', 'client_order'\n];\n\n// Default document type for fallback\nexport const DEFAULT_DOCUMENT_TYPE = 'vat';\n\n// Document type categories for UI grouping\nexport const DOCUMENT_TYPE_CATEGORIES = {\n  invoices: {\n    label: 'Invoices',\n    types: INVOICE_DOCUMENT_TYPES\n  },\n  receipts: {\n    label: 'Receipts & Bills',\n    types: RECEIPT_DOCUMENT_TYPES\n  },\n  notes: {\n    label: 'Notes & Documents',\n    types: NOTE_DOCUMENT_TYPES\n  },\n  orders: {\n    label: 'Orders & Estimates',\n    types: ORDER_DOCUMENT_TYPES\n  }\n};\n\n/**\n * Map document type detected by AI to Fakturownia document type\n * @param {string} documentType - Document type detected by AI\n * @param {string} documentContent - Document content for additional analysis\n * @returns {string} - Fakturownia document type\n */\nexport function mapToFakturowniaDocumentType(documentType, documentContent = '') {\n  const type = documentType.toLowerCase();\n  const content = documentContent.toLowerCase();\n\n  // Direct mapping for exact matches\n  if (VALID_DOCUMENT_TYPES.includes(type)) {\n    return type;\n  }\n\n  // Pattern-based mapping for content analysis\n  if (type === 'invoice') {\n    // Check for specific invoice types in the content\n    for (const [docType, patterns] of Object.entries(DOCUMENT_TYPE_PATTERNS)) {\n      if (patterns.some(pattern => content.includes(pattern))) {\n        return docType;\n      }\n    }\n    // Default to VAT invoice if no specific pattern found\n    return 'vat';\n  }\n\n  // Fallback mappings for common variations\n  const fallbackMappings = {\n    'invoice': 'vat',\n    'bill': 'bill',\n    'receipt': 'receipt',\n    'correction_note': 'correction_note',\n    'accounting_note': 'accounting_note',\n    'estimate': 'estimate',\n    'order': 'estimate',\n    'internal_document': 'dw',\n    'client_order': 'client_order'\n  };\n\n  return fallbackMappings[type] || DEFAULT_DOCUMENT_TYPE;\n}\n\n/**\n * Check if document type has positions/line items\n * @param {string} documentType - Document type\n * @returns {boolean} - Whether the document type has positions\n */\nexport function documentTypeHasPositions(documentType) {\n  return DOCUMENT_TYPES_WITH_POSITIONS.includes(documentType.toLowerCase());\n}\n\n/**\n * Get required fields for a document type\n * @param {string} documentType - Document type\n * @returns {Array} - Array of required field names\n */\nexport function getRequiredFieldsForDocumentType(documentType) {\n  return DOCUMENT_TYPE_REQUIREMENTS[documentType.toLowerCase()] || [];\n}\n\n/**\n * Validate document type\n * @param {string} documentType - Document type to validate\n * @returns {boolean} - Whether the document type is valid\n */\nexport function isValidDocumentType(documentType) {\n  return VALID_DOCUMENT_TYPES.includes(documentType.toLowerCase());\n}\n\n/**\n * Get document type category\n * @param {string} documentType - Document type\n * @returns {string} - Category name\n */\nexport function getDocumentTypeCategory(documentType) {\n  const type = documentType.toLowerCase();\n\n  for (const [category, config] of Object.entries(DOCUMENT_TYPE_CATEGORIES)) {\n    if (config.types.includes(type)) {\n      return category;\n    }\n  }\n\n  return 'invoices'; // Default category\n}\n\n/**\n * Get human-readable description for document type\n * @param {string} documentType - Document type\n * @returns {string} - Human-readable description\n */\nexport function getDocumentTypeDescription(documentType) {\n  return FAKTUROWNIA_DOCUMENT_TYPES[documentType.toLowerCase()] || documentType;\n}\n\n// Enhanced document type patterns for general business documents\n// Merged from components/guessDocumentType.js\nexport const GENERAL_DOCUMENT_PATTERNS = {\n  invoice: [\n    'invoice', 'faktura', 'payment', 'płatność', 'zapłata', 'vat', 'tax', 'podatek'\n  ],\n  contract: [\n    'contract', 'umowa', 'parties', 'strony', 'agree', 'zgadza'\n  ],\n  financial_report: [\n    'financial', 'finansowy', 'report', 'raport', 'sprawozdanie',\n    'balance', 'bilans', 'profit', 'zysk'\n  ],\n  agreement: [\n    'agreement', 'porozumienie', 'terms', 'warunki'\n  ],\n  legal: [\n    'legal', 'prawny', 'law', 'prawo', 'court', 'sąd'\n  ],\n  amendment: [\n    'amendment', 'aneks', 'addendum', 'załącznik', 'supplement', 'uzupełnienie'\n  ],\n  notice: [\n    'notice', 'zawiadomienie', 'notification', 'powiadomienie',\n    'announcement', 'ogłoszenie'\n  ],\n  court_order: [\n    'court order', 'nakaz sądowy', 'judgment', 'wyrok',\n    'ruling', 'orzeczenie', 'verdict', 'werdykt'\n  ],\n  registration: [\n    'registration', 'rejestracja', 'certificate of incorporation',\n    'świadectwo rejestracji', 'business registry', 'rejestr przedsiębiorców'\n  ],\n  email: [\n    'from:', 'to:', 'subject:', 'temat:', '@'\n  ],\n  tax_declaration: [\n    'tax declaration', 'deklaracja podatkowa', 'tax return', 'zeznanie podatkowe',\n    'pit', 'cit', 'vat', 'declaration', 'deklaracja'\n  ],\n  government_application: [\n    'application', 'wniosek', 'government', 'rząd', 'ministry', 'ministerstwo',\n    'agency', 'agencja'\n  ],\n  corporate_application: [\n    'application', 'wniosek', 'corporate', 'korporacyjny', 'company', 'firma'\n  ],\n  resolution: [\n    'resolution', 'uchwała', 'board resolution', 'uchwała zarządu',\n    'shareholder resolution', 'uchwała wspólników'\n  ]\n};\n\n/**\n * Guess document type from content using pattern matching\n * Merged from components/guessDocumentType.js\n * @param {string} content - Document content\n * @returns {string} - Guessed document type\n */\nexport function guessDocumentTypeWithPatterns(content) {\n  const lowerContent = content.toLowerCase();\n\n  // Check each pattern category\n  for (const [docType, patterns] of Object.entries(GENERAL_DOCUMENT_PATTERNS)) {\n    let matchCount = 0;\n    let requiredMatches = 1;\n\n    // For complex document types, require multiple pattern matches\n    if (['invoice', 'contract', 'financial_report'].includes(docType)) {\n      requiredMatches = 2;\n    }\n\n    for (const pattern of patterns) {\n      if (lowerContent.includes(pattern)) {\n        matchCount++;\n        if (matchCount >= requiredMatches) {\n          return docType;\n        }\n      }\n    }\n  }\n\n  // Default to business document if no specific pattern found\n  return 'business_document';\n}\n\n/**\n * Guess document type from content using AI\n * Merged from components/guessDocumentType.js\n * @param {string} content - Document content\n * @param {string} apiKey - DeepSeek API key\n * @param {Object} deepSeekAPI - DeepSeek API instance\n * @returns {Promise<string>} - Guessed document type\n */\nexport async function guessDocumentTypeWithAI(content, apiKey, deepSeekAPI = null) {\n  try {\n    if (!apiKey) {\n      console.log('No DeepSeek API key provided, falling back to pattern matching');\n      return guessDocumentTypeWithPatterns(content);\n    }\n\n    if (!deepSeekAPI) {\n      console.warn('DeepSeek API instance not provided, falling back to pattern matching');\n      return guessDocumentTypeWithPatterns(content);\n    }\n\n    // Prepare content sample for analysis\n    const contentSample = content.substring(0, 2000);\n\n    // Valid document types for AI response validation\n    const validTypes = [\n      'invoice', 'contract', 'financial_report', 'agreement', 'legal',\n      'amendment', 'notice', 'court_order', 'registration', 'email',\n      'tax_declaration', 'government_application', 'corporate_application',\n      'resolution', 'business_document'\n    ];\n\n    const prompt = `Analyze the following document text and determine its type.\nRespond with ONLY ONE of these document types:\n${validTypes.map(type => `- ${type}`).join('\\n')}\n\nDocument text sample:\n${contentSample}`;\n\n    const systemPrompt = 'You are an expert document analyst. Identify the document type from the provided text sample. Respond with ONLY the document type, nothing else.';\n\n    // Call DeepSeek API\n    const response = await deepSeekAPI.callAPI(prompt, apiKey, {\n      temperature: 0.1,\n      systemPrompt,\n      max_tokens: 50\n    });\n\n    if (response.success) {\n      const documentType = response.content.trim().toLowerCase();\n\n      // Validate response\n      if (validTypes.includes(documentType)) {\n        console.log(`AI detected document type: ${documentType}`);\n        return documentType;\n      }\n      // Try to extract valid type from response\n      for (const type of validTypes) {\n        if (response.content.toLowerCase().includes(type)) {\n          console.log(`AI detected document type (extracted): ${type}`);\n          return type;\n        }\n      }\n\n      console.log('AI response did not contain valid document type, falling back to pattern matching');\n      return guessDocumentTypeWithPatterns(content);\n\n    }\n    console.error('Error calling DeepSeek API:', response.error);\n    return guessDocumentTypeWithPatterns(content);\n\n  } catch (error) {\n    console.error('Error in AI document type detection:', error);\n    return guessDocumentTypeWithPatterns(content);\n  }\n}\n\n/**\n * Main function to guess document type from content\n * Merged from components/guessDocumentType.js\n * @param {string} content - Document content\n * @param {Object} options - Options object\n * @param {string} options.apiKey - DeepSeek API key\n * @param {Object} options.deepSeekAPI - DeepSeek API instance\n * @param {boolean} options.useAI - Whether to use AI detection (default: true)\n * @returns {Promise<string>} - Guessed document type\n */\nexport async function guessDocumentType(content, options = {}) {\n  try {\n    const {\n      apiKey = null,\n      deepSeekAPI = null,\n      useAI = true\n    } = options;\n\n    if (useAI && apiKey && deepSeekAPI) {\n      // Use AI-based document type detection\n      return await guessDocumentTypeWithAI(content, apiKey, deepSeekAPI);\n    }\n    // Fall back to pattern matching\n    return guessDocumentTypeWithPatterns(content);\n\n  } catch (error) {\n    console.error('Error in document type detection:', error);\n    return guessDocumentTypeWithPatterns(content);\n  }\n}\n\n// ===== LOCAL TESTING (Node.js equivalent of Python's if __name__ == '__main__') =====\nif (typeof process !== 'undefined' && process.argv && process.argv[1] && process.argv[1].includes('documentTypes.js')) {\n  console.log('🧪 Running local tests for documentTypes...');\n\n  // Test 1: Document type mapping\n  console.log('✅ Test 1: Document type mapping');\n  console.log('📝 VAT invoice mapping:', mapToFakturowniaDocumentType('invoice'));\n  console.log('📝 Proforma mapping:', mapToFakturowniaDocumentType('proforma'));\n  console.log('📝 Unknown type mapping:', mapToFakturowniaDocumentType('unknown'));\n\n  // Test 2: Document type validation\n  console.log('\\n✅ Test 2: Document type validation');\n  console.log('📝 VAT is valid:', isValidDocumentType('vat'));\n  console.log('📝 Unknown is valid:', isValidDocumentType('unknown'));\n  console.log('📝 Proforma is valid:', isValidDocumentType('proforma'));\n\n  // Test 3: Position requirements\n  console.log('\\n✅ Test 3: Position requirements');\n  console.log('📝 VAT has positions:', documentTypeHasPositions('vat'));\n  console.log('📝 Receipt has positions:', documentTypeHasPositions('receipt'));\n  console.log('📝 Note has positions:', documentTypeHasPositions('accounting_note'));\n\n  // Test 4: Required fields\n  console.log('\\n✅ Test 4: Required fields');\n  console.log('📝 Correction required fields:', getRequiredFieldsForDocumentType('correction'));\n  console.log('📝 VAT required fields:', getRequiredFieldsForDocumentType('vat'));\n  console.log('📝 WNT required fields:', getRequiredFieldsForDocumentType('wnt'));\n\n  // Test 5: Document categories\n  console.log('\\n✅ Test 5: Document categories');\n  console.log('📝 VAT category:', getDocumentTypeCategory('vat'));\n  console.log('📝 Receipt category:', getDocumentTypeCategory('receipt'));\n  console.log('📝 Note category:', getDocumentTypeCategory('accounting_note'));\n\n  // Test 6: Document descriptions\n  console.log('\\n✅ Test 6: Document descriptions');\n  console.log('📝 VAT description:', getDocumentTypeDescription('vat'));\n  console.log('📝 Proforma description:', getDocumentTypeDescription('proforma'));\n  console.log('📝 Unknown description:', getDocumentTypeDescription('unknown'));\n\n  // Test 7: Pattern-based document type guessing\n  console.log('\\n✅ Test 7: Pattern-based document type guessing');\n  const invoiceContent = 'FAKTURA VAT nr FV/2025/001 Total amount: 1230.00 PLN';\n  const contractContent = 'CONTRACT between parties Company A and Company B agree to terms';\n  const emailContent = 'From: <EMAIL> To: <EMAIL> Subject: Meeting';\n\n  console.log('📝 Invoice content type:', guessDocumentTypeWithPatterns(invoiceContent));\n  console.log('📝 Contract content type:', guessDocumentTypeWithPatterns(contractContent));\n  console.log('📝 Email content type:', guessDocumentTypeWithPatterns(emailContent));\n\n  // Test 8: Document type constants\n  console.log('\\n✅ Test 8: Document type constants');\n  console.log('📝 Valid document types count:', VALID_DOCUMENT_TYPES.length);\n  console.log('📝 Invoice types count:', INVOICE_DOCUMENT_TYPES.length);\n  console.log('📝 Receipt types count:', RECEIPT_DOCUMENT_TYPES.length);\n  console.log('📝 Default document type:', DEFAULT_DOCUMENT_TYPE);\n\n  // Test 9: Content-based mapping\n  console.log('\\n✅ Test 9: Content-based mapping');\n  const proformaContent = 'FAKTURA PROFORMA nr PRO/2025/001';\n  const correctionContent = 'FAKTURA KOREKTA do faktury FV/2025/001';\n\n  console.log('📝 Proforma content mapping:', mapToFakturowniaDocumentType('invoice', proformaContent));\n  console.log('📝 Correction content mapping:', mapToFakturowniaDocumentType('invoice', correctionContent));\n\n  // Test 10: Category structure\n  console.log('\\n✅ Test 10: Category structure');\n  Object.entries(DOCUMENT_TYPE_CATEGORIES).forEach(([category, config]) => {\n    console.log(`📝 Category ${category}: ${config.label} (${config.types.length} types)`);\n  });\n\n  console.log('\\n🎉 All tests completed for documentTypes');\n  console.log('📋 Document types configuration is working correctly');\n}\n", "/**\n * PromptGenerator - Unified prompt generation service\n * Consolidates functionality from core/prompt_generators/accounting_fields.js\n * and core/prompt_generators/document_fields.js\n */\n\nimport { FIELD_DESCRIPTIONS, DOCUMENT_KIND_FIELDS, COMMON_FIELDS, POSITION_FIELDS } from '../../../core/config/fieldDefinitions.js';\nimport { DOCUMENT_TYPES_WITH_POSITIONS, mapToFakturowniaDocumentType } from '../../../core/config/documentTypes.js';\n\nexport class PromptGenerator {\n  constructor() {\n    this.fieldDescriptions = FIELD_DESCRIPTIONS;\n    this.documentKindFields = DOCUMENT_KIND_FIELDS;\n    this.commonFields = COMMON_FIELDS;\n    this.positionFields = POSITION_FIELDS;\n    this.documentTypesWithPositions = DOCUMENT_TYPES_WITH_POSITIONS;\n  }\n\n  /**\n   * Generate document analysis prompt for determining document type\n   * @param {string} documentContent - Document content\n   * @param {Object} languageMapping - Language mapping instance\n   * @returns {Object} - Prompt configuration\n   */\n  generateDocumentAnalysisPrompt(documentContent, languageMapping) {\n    const detectedLang = languageMapping ? languageMapping.detectDocumentLanguageCode(documentContent) : 'pl';\n\n    const systemPrompt = `You are an expert accounting document analyst specializing in Polish and EU business documents.\n\nTASK: Analyze the document and determine its type, provide description, and extract basic information.\n\nCRITICAL INSTRUCTIONS:\n1. Field names in JSON output must remain in English as defined in the structure\n2. Field values should be in the detected language (${detectedLang})\n3. Use exact document type codes from the provided list\n4. Provide accurate descriptions based on document content\n\nDOCUMENT TYPES:\n- vat: VAT invoice\n- proforma: Proforma invoice\n- bill: Bill/Receipt\n- correction: Correction invoice\n- advance: Advance invoice\n- final: Final invoice\n- estimate: Estimate/Order\n- accounting_note: Accounting note\n- And other standard business document types\n\nRESPONSE FORMAT: Return only valid JSON object with this structure:\n{\n  \"documentKind\": \"document_type_code\",\n  \"description\": \"Brief description of the document\",\n  \"summary\": \"Key information summary\",\n  \"confidence\": \"high|medium|low\"\n}`;\n\n    const prompt = `Analyze this document and determine its type:\n\n${documentContent}\n\nDetermine the document type and provide analysis in the specified JSON format.`;\n\n    return {\n      prompt,\n      systemPrompt,\n      options: {\n        temperature: 0.1,\n        max_tokens: 1000\n      }\n    };\n  }\n\n  /**\n   * Generate metadata extraction prompt\n   * @param {string} documentType - Document type\n   * @param {string} documentContent - Document content\n   * @param {string} language - Document language\n   * @param {string} companyContext - Company context\n   * @param {string} ragContext - RAG context\n   * @returns {Object} - Prompt configuration\n   */\n  generateMetadataExtractionPrompt(documentType, documentContent, language = 'pl', companyContext = '', ragContext = '') {\n    const fields = this.getFieldsForDocumentType(documentType);\n    const structure = this.generateExpectedJsonStructure(documentType);\n\n    const systemPrompt = `You are an expert accounting document analyst with deep knowledge of Polish accounting and VAT regulations.\n\nTASK: Extract structured data from ${documentType} document.\n\nCRITICAL INSTRUCTIONS:\n1. Field names in JSON output must remain in English as defined in the structure\n2. Field values should be extracted in the original document language (${language})\n3. Use exact field names from the provided structure\n4. For income field: 1=company as seller (revenue), 0=company as buyer (expense)\n5. Extract all monetary amounts with proper decimal formatting\n6. Ensure date formats are YYYY-MM-DD\n\n${companyContext}\n\n${ragContext}\n\nEXPECTED JSON STRUCTURE:\n${JSON.stringify(structure, null, 2)}`;\n\n    const prompt = `Extract all relevant data from this ${documentType} document:\n\n${documentContent}\n\nReturn the extracted data in the exact JSON structure provided above.`;\n\n    return {\n      prompt,\n      systemPrompt,\n      options: {\n        temperature: 0.1,\n        max_tokens: 2000\n      }\n    };\n  }\n\n  /**\n   * Generate positions extraction prompt\n   * @param {string} documentType - Document type\n   * @param {string} documentContent - Document content\n   * @param {string} language - Document language\n   * @param {Object} basicData - Basic document data\n   * @param {string} ocrTableData - OCR table data\n   * @returns {Object} - Prompt configuration\n   */\n  generatePositionsPrompt(documentType, documentContent, language = 'pl', basicData = null, ocrTableData = '') {\n    if (!this.documentTypeHasPositions(documentType)) {\n      return null;\n    }\n\n    const positionStructure = this.generatePositionStructure(documentType);\n\n    const systemPrompt = `You are an expert at extracting line items from ${documentType} documents.\n\nTASK: Extract all positions/line items from the document.\n\nCRITICAL INSTRUCTIONS:\n1. Field names must remain in English as defined in the structure\n2. Extract values in the original document language (${language})\n3. Ensure numeric values use decimal notation (e.g., 123.45)\n4. For correction documents, extract both before_positions and after_positions\n5. Validate totals against document totals when possible\n\nPOSITION STRUCTURE:\n${JSON.stringify(positionStructure, null, 2)}`;\n\n    let prompt = `Extract all line items/positions from this ${documentType} document:\n\n${documentContent}`;\n\n    if (ocrTableData) {\n      prompt += `\\n\\nAdditional table data from OCR:\\n${ocrTableData}`;\n    }\n\n    if (basicData) {\n      prompt += `\\n\\nDocument totals for validation:\n- Total Net: ${basicData.total_net || 'N/A'}\n- Total VAT: ${basicData.total_vat || 'N/A'}\n- Total Gross: ${basicData.total_gross || 'N/A'}`;\n    }\n\n    prompt += '\\n\\nReturn the positions in the specified JSON structure.';\n\n    return {\n      prompt,\n      systemPrompt,\n      options: {\n        temperature: 0.1,\n        max_tokens: 3000\n      }\n    };\n  }\n\n  /**\n   * Generate context-aware document prompt (from DocumentFields)\n   * @param {string} documentType - Document type\n   * @param {string} documentContent - Document content\n   * @param {string} language - Document language\n   * @param {string} companyContext - Company context\n   * @param {string} ragContext - RAG context\n   * @param {Object} documentTypeResult - Document type analysis result\n   * @returns {string} - Generated prompt\n   */\n  generateContextAwareDocumentPrompt(documentType, documentContent, language, companyContext, ragContext, documentTypeResult) {\n    const structure = this.generateExpectedJsonStructure(documentType);\n\n    let prompt = `You are an expert accounting document analyst. Extract structured data from this ${documentType} document.\n\nCRITICAL INSTRUCTIONS:\n1. Field names in JSON must remain in English\n2. Field values should be in the detected language (${language})\n3. For income field: determine if company is buyer (0) or seller (1)\n4. Extract all monetary amounts accurately\n5. Use YYYY-MM-DD format for dates\n\n${companyContext}\n\n${ragContext}`;\n\n    if (documentTypeResult && documentTypeResult.description) {\n      prompt += `\\n\\nDocument Analysis Context:\n- Type: ${documentTypeResult.documentType}\n- Description: ${documentTypeResult.description}\n- Summary: ${documentTypeResult.summary || 'N/A'}`;\n    }\n\n    prompt += `\\n\\nEXPECTED JSON STRUCTURE:\n${JSON.stringify(structure, null, 2)}\n\nDOCUMENT CONTENT:\n${documentContent}\n\nExtract the data and return it in the exact JSON structure above.`;\n\n    return prompt;\n  }\n\n  /**\n   * Get fields for a specific document type\n   * @param {string} documentType - Document type\n   * @returns {Array} - Array of field names\n   */\n  getFieldsForDocumentType(documentType) {\n    const commonFields = [...this.commonFields];\n    const specificFields = this.documentKindFields[documentType] || [];\n    return [...commonFields, ...specificFields];\n  }\n\n  /**\n   * Generate expected JSON structure for document type\n   * @param {string} documentType - Document type\n   * @returns {Object} - JSON structure\n   */\n  generateExpectedJsonStructure(documentType) {\n    const fields = this.getFieldsForDocumentType(documentType);\n    const structure = {};\n\n    fields.forEach(field => {\n      if (field === 'positions') {\n        if (this.documentTypeHasPositions(documentType)) {\n          structure.positions = [this.generatePositionStructure(documentType)];\n        }\n      } else {\n        const description = this.fieldDescriptions[field] || `${field} value`;\n        structure[field] = `(${description})`;\n      }\n    });\n\n    return structure;\n  }\n\n  /**\n   * Generate position structure for document type\n   * @param {string} documentType - Document type\n   * @returns {Object} - Position structure\n   */\n  generatePositionStructure(documentType) {\n    const structure = {};\n\n    Object.keys(this.positionFields).forEach(field => {\n      const description = this.positionFields[field];\n      structure[field] = `(${description})`;\n    });\n\n    // Add correction-specific fields for correction documents\n    if (documentType === 'correction') {\n      structure.kind = '(position type: normal|correction)';\n      structure.correction_before_attributes = '(original position data)';\n      structure.correction_after_attributes = '(corrected position data)';\n    }\n\n    return structure;\n  }\n\n  /**\n   * Check if document type has positions\n   * @param {string} documentType - Document type\n   * @returns {boolean} - Whether document type has positions\n   */\n  documentTypeHasPositions(documentType) {\n    return this.documentTypesWithPositions.includes(documentType.toLowerCase());\n  }\n\n  /**\n   * Generate validation prompt for extracted data\n   * @param {Object} extractedData - Extracted data\n   * @param {string} documentContent - Original document content\n   * @returns {Object} - Validation prompt configuration\n   */\n  generateValidationPrompt(extractedData, documentContent) {\n    const systemPrompt = `You are a data validation expert for accounting documents.\n\nTASK: Validate the extracted data against the original document and identify any inconsistencies.\n\nVALIDATION CHECKS:\n1. Verify all monetary amounts match the document\n2. Check date formats and consistency\n3. Validate company information accuracy\n4. Ensure position totals match document totals\n5. Check for missing critical information\n\nRESPONSE FORMAT: Return JSON with validation results:\n{\n  \"valid\": true/false,\n  \"errors\": [\"list of errors found\"],\n  \"warnings\": [\"list of warnings\"],\n  \"suggestions\": [\"list of improvement suggestions\"]\n}`;\n\n    const prompt = `Validate this extracted data against the original document:\n\nEXTRACTED DATA:\n${JSON.stringify(extractedData, null, 2)}\n\nORIGINAL DOCUMENT:\n${documentContent}\n\nPerform thorough validation and return results in the specified format.`;\n\n    return {\n      prompt,\n      systemPrompt,\n      options: {\n        temperature: 0.1,\n        max_tokens: 1500\n      }\n    };\n  }\n}\n\n// Create singleton instance\nconst promptGenerator = new PromptGenerator();\n\n// Export for ES modules\nexport default promptGenerator;\n\n// Export to window object for non-module scripts\nif (typeof window !== 'undefined') {\n  window.PromptGenerator = PromptGenerator;\n  window.promptGenerator = promptGenerator;\n}\n\n// Local test function\nfunction testPromptGenerator() {\n  console.log('=== PromptGenerator Local Test ===');\n\n  try {\n    const generator = new PromptGenerator();\n\n    // Test 1: Document analysis prompt\n    console.log('Test 1: Document analysis prompt');\n    const mockLanguageMapping = {\n      detectDocumentLanguageCode: () => 'pl'\n    };\n    const analysisPrompt = generator.generateDocumentAnalysisPrompt('FAKTURA VAT', mockLanguageMapping);\n    console.log('✓ Document analysis prompt generated');\n    console.log('System prompt length:', analysisPrompt.systemPrompt.length);\n\n    // Test 2: Metadata extraction prompt\n    console.log('\\nTest 2: Metadata extraction prompt');\n    const metadataPrompt = generator.generateMetadataExtractionPrompt('vat', 'Sample content', 'pl');\n    console.log('✓ Metadata extraction prompt generated');\n    console.log('Prompt length:', metadataPrompt.prompt.length);\n\n    // Test 3: Positions prompt\n    console.log('\\nTest 3: Positions prompt');\n    const positionsPrompt = generator.generatePositionsPrompt('vat', 'Sample content', 'pl');\n    console.log('✓ Positions prompt generated');\n    console.log('Has positions for VAT:', generator.documentTypeHasPositions('vat'));\n\n    // Test 4: JSON structure generation\n    console.log('\\nTest 4: JSON structure generation');\n    const structure = generator.generateExpectedJsonStructure('vat');\n    console.log('✓ JSON structure generated');\n    console.log('Structure fields:', Object.keys(structure).length);\n\n    // Test 5: Position structure\n    console.log('\\nTest 5: Position structure');\n    const positionStructure = generator.generatePositionStructure('vat');\n    console.log('✓ Position structure generated');\n    console.log('Position fields:', Object.keys(positionStructure).length);\n\n    console.log('\\n✅ All PromptGenerator tests passed!');\n    return true;\n\n  } catch (error) {\n    console.error('❌ PromptGenerator test failed:', error);\n    return false;\n  }\n}\n\n// Run test if in browser environment\nif (typeof window !== 'undefined' && window.location) {\n  // Uncomment to run test\n  // testPromptGenerator();\n}\n\n// ===== LOCAL TESTING (Node.js equivalent of Python's if __name__ == '__main__') =====\nif (typeof process !== 'undefined' && process.argv && process.argv[1] && process.argv[1].includes('PromptGenerator.js')) {\n  console.log('🧪 Running local tests for PromptGenerator...');\n\n  // Create test instance\n  const generator = new PromptGenerator();\n\n  // Mock language mapping\n  const mockLanguageMapping = {\n    detectDocumentLanguageCode: () => 'pl'\n  };\n\n  // Test data\n  const sampleCompanyData = {\n    name: 'Test Sp. z o.o.',\n    taxId: '**********',\n    address: 'ul. Testowa 123',\n    city: 'Warszawa',\n    postCode: '00-001'\n  };\n\n  const sampleDocumentText = `\n    FAKTURA VAT\n    Numer: FV/2025/001\n    Data wystawienia: 2025-01-27\n    Sprzedawca: ABC Sp. z o.o.\n    NIP: **********\n    Nabywca: XYZ Sp. z o.o.\n    NIP: 0987654321\n    `;\n\n  console.log('✅ Test 1: Document analysis prompt generation');\n  try {\n    const analysisPrompt = generator.generateDocumentAnalysisPrompt(sampleDocumentText, mockLanguageMapping);\n    console.log('📝 Generated document analysis prompt');\n    console.log('📊 System prompt length:', analysisPrompt.systemPrompt.length);\n    console.log('📊 User prompt length:', analysisPrompt.prompt.length);\n    console.log('📊 Temperature:', analysisPrompt.options.temperature);\n  } catch (error) {\n    console.log('❌ Error in document analysis prompt generation:', error.message);\n  }\n\n  console.log('\\n✅ Test 2: Metadata extraction prompt generation');\n  try {\n    const companyContext = `Company: ${sampleCompanyData.name} (${sampleCompanyData.taxId})`;\n    const metadataPrompt = generator.generateMetadataExtractionPrompt('vat', sampleDocumentText, 'pl', companyContext);\n    console.log('📝 Generated metadata extraction prompt');\n    console.log('📊 System prompt length:', metadataPrompt.systemPrompt.length);\n    console.log('📊 User prompt length:', metadataPrompt.prompt.length);\n    console.log('📊 Max tokens:', metadataPrompt.options.max_tokens);\n  } catch (error) {\n    console.log('❌ Error in metadata prompt generation:', error.message);\n  }\n\n  console.log('\\n✅ Test 3: Position extraction prompt generation');\n  try {\n    const basicData = { total_net: '1000.00', total_vat: '230.00', total_gross: '1230.00' };\n    const positionPrompt = generator.generatePositionsPrompt('vat', sampleDocumentText, 'pl', basicData);\n    console.log('📝 Generated position extraction prompt');\n    console.log('📊 System prompt length:', positionPrompt.systemPrompt.length);\n    console.log('📊 User prompt length:', positionPrompt.prompt.length);\n    console.log('📊 Max tokens:', positionPrompt.options.max_tokens);\n  } catch (error) {\n    console.log('❌ Error in position prompt generation:', error.message);\n  }\n\n  console.log('\\n✅ Test 4: JSON structure generation');\n  try {\n    const vatStructure = generator.generateExpectedJsonStructure('vat');\n    const correctionStructure = generator.generateExpectedJsonStructure('correction');\n    console.log('📝 Generated JSON structures');\n    console.log('📊 VAT structure fields:', Object.keys(vatStructure).length);\n    console.log('📊 Correction structure fields:', Object.keys(correctionStructure).length);\n    console.log('📊 VAT has positions:', 'positions' in vatStructure);\n  } catch (error) {\n    console.log('❌ Error in JSON structure generation:', error.message);\n  }\n\n  console.log('\\n✅ Test 5: Document type position checking');\n  try {\n    const vatHasPositions = generator.documentTypeHasPositions('vat');\n    const billHasPositions = generator.documentTypeHasPositions('bill');\n    const unknownHasPositions = generator.documentTypeHasPositions('unknown');\n    console.log('📝 Document type position checking');\n    console.log('📊 VAT has positions:', vatHasPositions);\n    console.log('📊 Bill has positions:', billHasPositions);\n    console.log('📊 Unknown has positions:', unknownHasPositions);\n  } catch (error) {\n    console.log('❌ Error in document type checking:', error.message);\n  }\n\n  console.log('\\n✅ Test 6: Validation prompt generation');\n  try {\n    const extractedData = {\n      kind: 'vat',\n      number: 'FV/2025/001',\n      total_net: '1000.00',\n      total_gross: '1230.00'\n    };\n    const validationPrompt = generator.generateValidationPrompt(extractedData, sampleDocumentText);\n    console.log('📝 Generated validation prompt');\n    console.log('📊 System prompt length:', validationPrompt.systemPrompt.length);\n    console.log('📊 User prompt length:', validationPrompt.prompt.length);\n  } catch (error) {\n    console.log('❌ Error in validation prompt generation:', error.message);\n  }\n\n  console.log('\\n🎉 All tests completed for PromptGenerator');\n}\n"], "names": ["DOCUMENT_TYPES_WITH_POSITIONS"], "mappings": "AAOO,MAAM,gBAAgB;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAGO,MAAM,iBAAiB;AAAA,EAC5B,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,WAAW;AAAA,EACX,SAAS;AAAA,EACT,cAAc;AAAA,EACd,UAAU;AAAA,EACV,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,MAAM;AAAA,EACN,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,gBAAgB;AAAA,EAChB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA,EACP,kBAAkB;AAAA,EAClB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,mBAAmB;AACrB;AAGO,MAAM,uBAAuB;AAAA;AAAA,EAElC,OAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA;AAAA,EAGD,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA;AAAA,EAGD,QAAQ;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA;AAAA,EAGD,WAAW;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA;AAAA,EAGD,WAAW;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA;AAAA,EAGD,SAAS;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA;AAAA,EAGD,cAAc;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA;AAAA,EAGD,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA;AAAA,EAGD,iBAAiB;AAAA,IACf;AAAA,IACA;AAAA,EACD;AAAA;AAAA,EAGD,cAAc;AAAA,IACZ;AAAA,IACA;AAAA,EACD;AAAA;AAAA,EAGD,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA;AAAA,EAGD,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA;AAAA,EAGD,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA;AAAA,EAGD,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA;AAAA,EAGD,mBAAmB;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA;AAAA,EAGD,mBAAmB;AAAA,IACjB;AAAA,IACA;AAAA,EACD;AAAA;AAAA,EAGD,gBAAgB;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA;AAAA,EAGD,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACD;AAAA;AAAA,EAGD,OAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA;AAAA,EAGD,OAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA;AAAA,EAGD,kBAAkB;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA;AAAA,EAGD,qBAAqB;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA;AAAA,EAGD,mBAAmB;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA;AAAA,EAGD,mBAAmB;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACA;AAGO,MAAM,qBAAqB;AAAA,EAChC,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,cAAc;AAAA,EACd,SAAS;AAAA,EACT,aAAa;AAAA,EACb,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,aAAa;AAAA,EACb,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,kBAAkB;AAAA,EAClB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,mBAAmB;AAAA,EACnB,gBAAgB;AAAA,EAChB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,qBAAqB;AAAA,EACrB,iBAAiB;AAAA,EACjB,0BAA0B;AAAA,EAC1B,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,mBAAmB;AAAA,EACnB,OAAO;AAAA,EACP,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,oBAAoB;AAAA,EACpB,iBAAiB;AAAA,EACjB,eAAe;AAAA,EACf,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,2BAA2B;AAAA,EAC3B,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,EAClB,aAAa;AACf;AAGO,MAAM,2BAA2B;AAAA,EACtC,UAAU,CAAC,KAAK,GAAG;AAAA,EASnB,gBAAgB,CAAC,YAAY,QAAQ,QAAQ,QAAQ,UAAU,OAAO;AAAA,EACtE,YAAY,CAAC,OAAO,OAAO,OAAO,OAAO,KAAK;AAMhD;AAGO,MAAM,kBAAkB;AAAA,EAC7B,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb,OAAO;AAAA,EACP,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,cAAc;AAAA,EACd,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,oBAAoB;AAAA,EACpB,cAAc;AAAA,EACd,QAAQ;AACV;AAGO,MAAM,YAAY,CAAC,MAAM,KAAK,KAAK,KAAK,MAAM,IAAI;AAGlD,MAAMA,kCAAgC;AAAA,EAC3C;AAAA,EAAO;AAAA,EAAY;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAW;AAAA,EAAS;AAAA,EAC1D;AAAA,EAAU;AAAA,EAAiB;AAAA,EAAc;AAAA,EAAO;AAAA,EAAO;AAAA,EACvD;AAAA,EAAqB;AAAA,EAAmB;AAC1C;AAGO,MAAM,kBAAkB;AAAA,EAC7B,UAAU,CAAC,QAAQ,UAAU,cAAc,eAAe,YAAY;AAAA,EACtE,cAAc,CAAC,cAAc,mBAAmB,4BAA4B,yBAAyB;AAIvG;AAGO,MAAM,6BAA6B;AAAA,EACxC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAGO,MAAM,gCAAgC;AAAA,EAC3C,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb,aAAa;AAAA,EACb,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,sBAAsB;AAAA,EACtB,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,YAAY;AAAA,EACZ,mBAAmB;AACrB;AA0BA,IAAI,OAAO,YAAY,eAAe,QAAQ,SAAS,QAAQ;AAC7D,UAAQ,IAAI,gDAAgD;AAE5D,UAAQ,IAAI,qCAAqC;AACjD,UAAQ,IAAI,gCAAgC,OAAO,KAAK,cAAc,EAAE,MAAM;AAC9E,UAAQ,IAAI,6BAA6B,OAAO,KAAK,cAAc,EAAE,MAAM,GAAG,CAAC,CAAC;AAEhF,UAAQ,IAAI,qCAAqC;AACjD,UAAQ,IAAI,2BAA2B,cAAc,MAAM;AAC3D,UAAQ,IAAI,4BAA4B,cAAc,MAAM,GAAG,CAAC,CAAC;AAEjE,UAAQ,IAAI,2CAA2C;AACvD,QAAM,YAAY,qBAAqB;AACvC,UAAQ,IAAI,mCAAmC,SAAS;AAExD,QAAM,mBAAmB,qBAAqB;AAC9C,UAAQ,IAAI,0CAA0C,gBAAgB;AAEtE,UAAQ,IAAI,2CAA2C;AACvD,QAAM,eAAe,CAAC,UAAU,UAAU,eAAe,cAAc,aAAa;AACpF,eAAa,QAAQ,WAAS;AAC5B,UAAM,cAAc,mBAAmB,KAAK;AAC5C,YAAQ,IAAI,MAAM,KAAK,KAAK,cAAc,YAAY,UAAU,GAAG,EAAE,IAAI,QAAQ,gBAAgB,EAAE;AAAA,EACvG,CAAG;AAED,UAAQ,IAAI,sCAAsC;AAClD,UAAQ,IAAI,qBAAqB,yBAAyB,MAAM;AAChE,UAAQ,IAAI,uBAAuB,yBAAyB,QAAQ;AACpE,UAAQ,IAAI,2BAA2B,yBAAyB,YAAY;AAE5E,UAAQ,IAAI,wCAAwC;AACpD,UAAQ,IAAI,6BAA6B,OAAO,KAAK,eAAe,EAAE,MAAM;AAC5E,UAAQ,IAAI,uBAAuB,OAAO,KAAK,eAAe,CAAC;AAE/D,UAAQ,IAAI,kCAAkC;AAC9C,UAAQ,IAAI,2BAA2B,SAAS;AAEhD,UAAQ,IAAI,2CAA2C;AACvD,UAAQ,IAAI,2CAA2CA,gCAA8B,MAAM;AAC3F,UAAQ,IAAI,mCAAmCA,gCAA8B,MAAM,GAAG,CAAC,CAAC;AAExF,UAAQ,IAAI,wCAAwC;AACpD,UAAQ,IAAI,8BAA8B,gBAAgB,MAAM;AAChE,UAAQ,IAAI,kCAAkC,gBAAgB,UAAU;AAExE,UAAQ,IAAI,yCAAyC;AACrD,UAAQ,IAAI,+BAA+B,2BAA2B,MAAM;AAC5E,UAAQ,IAAI,gCAAgC,2BAA2B,MAAM,GAAG,CAAC,CAAC;AAElF,UAAQ,IAAI,oCAAoC;AAChD,MAAI,sBAAsB,CAAE;AAC5B,gBAAc,QAAQ,WAAS;AAC7B,QAAI,CAAC,mBAAmB,KAAK,KAAK,CAAC,8BAA8B,KAAK,GAAG;AACvE,0BAAoB,KAAK,KAAK;AAAA,IACpC;AAAA,EACA,CAAG;AAED,MAAI,oBAAoB,SAAS,GAAG;AAClC,YAAQ,IAAI,oCAAoC,mBAAmB;AAAA,EACvE,OAAS;AACL,YAAQ,IAAI,uCAAuC;AAAA,EACvD;AAEE,UAAQ,IAAI,2CAA2C;AACvD,QAAM,mBAAmB,OAAO,KAAK,cAAc;AACnD,QAAM,kBAAkB,OAAO,KAAK,oBAAoB;AACxD,QAAM,0BAA0B,iBAAiB,OAAO,UAAQ,CAAC,gBAAgB,SAAS,IAAI,CAAC;AAE/F,MAAI,wBAAwB,SAAS,GAAG;AACtC,YAAQ,IAAI,iDAAiD,uBAAuB;AAAA,EACxF,OAAS;AACL,YAAQ,IAAI,6CAA6C;AAAA,EAC7D;AAEE,UAAQ,IAAI,+CAA+C;AAC7D;ACngBO,MAAM,6BAA6B;AAAA,EACxC,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,WAAW;AAAA,EACX,SAAS;AAAA,EACT,cAAc;AAAA,EACd,UAAU;AAAA,EACV,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,MAAM;AAAA,EACN,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,gBAAgB;AAAA,EAChB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA,EACP,kBAAkB;AAAA,EAClB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,mBAAmB;AACrB;AAGO,MAAM,uBAAuB;AAAA,EAClC;AAAA,EAAO;AAAA,EAAY;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAW;AAAA,EAAS;AAAA,EAC1D;AAAA,EAAU;AAAA,EAAiB;AAAA,EAAc;AAAA,EAAmB;AAAA,EAC5D;AAAA,EAAgB;AAAA,EAAM;AAAA,EAAO;AAAA,EAAO;AAAA,EAAkB;AAAA,EACtD;AAAA,EAAmB;AAAA,EAAmB;AAAA,EAAM;AAAA,EAAM;AAAA,EAAY;AAChE;AAGO,MAAM,yBAAyB;AAAA,EACpC,UAAU,CAAC,YAAY,aAAa,WAAW;AAAA,EAC/C,SAAS,CAAC,WAAW,SAAS;AAAA,EAC9B,OAAO,CAAC,UAAU,OAAO;AAAA,EACzB,YAAY,CAAC,UAAU,cAAc,aAAa;AAAA,EAClD,YAAY,CAAC,SAAS,QAAQ;AAAA,EAC9B,QAAQ,CAAC,MAAM,eAAe;AAAA,EAC9B,QAAQ,CAAC,OAAO,QAAQ;AAAA,EACxB,KAAK,CAAC,OAAO,6BAA6B;AAAA,EAC1C,KAAK,CAAC,OAAO,6BAA6B;AAAA,EAC1C,gBAAgB,CAAC,gBAAgB,oBAAoB;AAAA,EACrD,mBAAmB,CAAC,gBAAgB,MAAM,IAAI;AAAA,EAC9C,iBAAiB,CAAC,kBAAkB,iBAAiB;AAAA,EACrD,iBAAiB,CAAC,WAAW,QAAQ;AAAA,EACrC,MAAM,CAAC,UAAU;AAAA,EACjB,SAAS,CAAC,SAAS;AAAA,EACnB,iBAAiB,CAAC,iBAAiB;AAAA,EACnC,iBAAiB,CAAC,iBAAiB,UAAU;AAAA,EAC7C,UAAU,CAAC,cAAc,OAAO;AAAA,EAChC,IAAI,CAAC,kBAAkB;AAAA,EACvB,cAAc,CAAC,iBAAiB;AAClC;AAGO,MAAM,6BAA6B;AAAA,EACxC,YAAY,CAAC,cAAc,mBAAmB,4BAA4B,yBAAyB;AAAA,EACnG,SAAS,CAAC,YAAY;AAAA,EACtB,OAAO,CAAC,iBAAiB;AAAA,EACzB,iBAAiB,CAAC,sBAAsB;AAAA,EACxC,KAAK,CAAC,kBAAkB,iBAAiB,gBAAgB;AAAA,EACzD,KAAK,CAAC,kBAAkB,eAAe;AAAA,EACvC,gBAAgB,CAAC,kBAAkB,iBAAiB,gBAAgB;AAAA,EACpE,mBAAmB,CAAC,kBAAkB,iBAAiB,gBAAgB;AAAA,EACvE,iBAAiB,CAAC,kBAAkB,eAAe;AAAA,EACnD,iBAAiB,CAAC,kBAAkB,eAAe;AACrD;AAGO,MAAM,gCAAgC;AAAA,EAC3C;AAAA,EAAO;AAAA,EAAY;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAW;AAAA,EAAS;AAAA,EAC1D;AAAA,EAAU;AAAA,EAAiB;AAAA,EAAc;AAAA,EAAO;AAAA,EAAO;AAAA,EACvD;AAAA,EAAqB;AAAA,EAAmB;AAAA,EAAmB;AAC7D;AAGO,MAAM,yBAAyB;AAAA,EACpC;AAAA,EAAO;AAAA,EAAY;AAAA,EAAW;AAAA,EAAS;AAAA,EAAc;AAAA,EACrD;AAAA,EAAiB;AAAA,EAAc;AAAA,EAAU;AAAA,EAAO;AAAA,EAChD;AAAA,EAAkB;AAAA,EAAqB;AAAA,EAAmB;AAC5D;AAGO,MAAM,yBAAyB;AAAA,EACpC;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAM;AAC3B;AAGO,MAAM,sBAAsB;AAAA,EACjC;AAAA,EAAmB;AAAA,EAAmB;AACxC;AAGO,MAAM,uBAAuB;AAAA,EAClC;AAAA,EAAY;AACd;AAGO,MAAM,wBAAwB;AAG9B,MAAM,2BAA2B;AAAA,EACtC,UAAU;AAAA,IACR,OAAO;AAAA,IACP,OAAO;AAAA,EACR;AAAA,EACD,UAAU;AAAA,IACR,OAAO;AAAA,IACP,OAAO;AAAA,EACR;AAAA,EACD,OAAO;AAAA,IACL,OAAO;AAAA,IACP,OAAO;AAAA,EACR;AAAA,EACD,QAAQ;AAAA,IACN,OAAO;AAAA,IACP,OAAO;AAAA,EACX;AACA;AAQO,SAAS,6BAA6B,cAAc,kBAAkB,IAAI;AAC/E,QAAM,OAAO,aAAa,YAAa;AACvC,QAAM,UAAU,gBAAgB,YAAa;AAG7C,MAAI,qBAAqB,SAAS,IAAI,GAAG;AACvC,WAAO;AAAA,EACX;AAGE,MAAI,SAAS,WAAW;AAEtB,eAAW,CAAC,SAAS,QAAQ,KAAK,OAAO,QAAQ,sBAAsB,GAAG;AACxE,UAAI,SAAS,KAAK,aAAW,QAAQ,SAAS,OAAO,CAAC,GAAG;AACvD,eAAO;AAAA,MACf;AAAA,IACA;AAEI,WAAO;AAAA,EACX;AAGE,QAAM,mBAAmB;AAAA,IACvB,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,mBAAmB;AAAA,IACnB,mBAAmB;AAAA,IACnB,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,EACjB;AAED,SAAO,iBAAiB,IAAI,KAAK;AACnC;AAOO,SAAS,yBAAyB,cAAc;AACrD,SAAO,8BAA8B,SAAS,aAAa,YAAW,CAAE;AAC1E;AAOO,SAAS,iCAAiC,cAAc;AAC7D,SAAO,2BAA2B,aAAa,YAAW,CAAE,KAAK,CAAE;AACrE;AAOO,SAAS,oBAAoB,cAAc;AAChD,SAAO,qBAAqB,SAAS,aAAa,YAAW,CAAE;AACjE;AAOO,SAAS,wBAAwB,cAAc;AACpD,QAAM,OAAO,aAAa,YAAa;AAEvC,aAAW,CAAC,UAAU,MAAM,KAAK,OAAO,QAAQ,wBAAwB,GAAG;AACzE,QAAI,OAAO,MAAM,SAAS,IAAI,GAAG;AAC/B,aAAO;AAAA,IACb;AAAA,EACA;AAEE,SAAO;AACT;AAOO,SAAS,2BAA2B,cAAc;AACvD,SAAO,2BAA2B,aAAa,YAAW,CAAE,KAAK;AACnE;AAIO,MAAM,4BAA4B;AAAA,EACvC,SAAS;AAAA,IACP;AAAA,IAAW;AAAA,IAAW;AAAA,IAAW;AAAA,IAAY;AAAA,IAAW;AAAA,IAAO;AAAA,IAAO;AAAA,EACvE;AAAA,EACD,UAAU;AAAA,IACR;AAAA,IAAY;AAAA,IAAS;AAAA,IAAW;AAAA,IAAU;AAAA,IAAS;AAAA,EACpD;AAAA,EACD,kBAAkB;AAAA,IAChB;AAAA,IAAa;AAAA,IAAa;AAAA,IAAU;AAAA,IAAU;AAAA,IAC9C;AAAA,IAAW;AAAA,IAAU;AAAA,IAAU;AAAA,EAChC;AAAA,EACD,WAAW;AAAA,IACT;AAAA,IAAa;AAAA,IAAgB;AAAA,IAAS;AAAA,EACvC;AAAA,EACD,OAAO;AAAA,IACL;AAAA,IAAS;AAAA,IAAU;AAAA,IAAO;AAAA,IAAS;AAAA,IAAS;AAAA,EAC7C;AAAA,EACD,WAAW;AAAA,IACT;AAAA,IAAa;AAAA,IAAS;AAAA,IAAY;AAAA,IAAa;AAAA,IAAc;AAAA,EAC9D;AAAA,EACD,QAAQ;AAAA,IACN;AAAA,IAAU;AAAA,IAAiB;AAAA,IAAgB;AAAA,IAC3C;AAAA,IAAgB;AAAA,EACjB;AAAA,EACD,aAAa;AAAA,IACX;AAAA,IAAe;AAAA,IAAgB;AAAA,IAAY;AAAA,IAC3C;AAAA,IAAU;AAAA,IAAc;AAAA,IAAW;AAAA,EACpC;AAAA,EACD,cAAc;AAAA,IACZ;AAAA,IAAgB;AAAA,IAAe;AAAA,IAC/B;AAAA,IAA0B;AAAA,IAAqB;AAAA,EAChD;AAAA,EACD,OAAO;AAAA,IACL;AAAA,IAAS;AAAA,IAAO;AAAA,IAAY;AAAA,IAAU;AAAA,EACvC;AAAA,EACD,iBAAiB;AAAA,IACf;AAAA,IAAmB;AAAA,IAAwB;AAAA,IAAc;AAAA,IACzD;AAAA,IAAO;AAAA,IAAO;AAAA,IAAO;AAAA,IAAe;AAAA,EACrC;AAAA,EACD,wBAAwB;AAAA,IACtB;AAAA,IAAe;AAAA,IAAW;AAAA,IAAc;AAAA,IAAQ;AAAA,IAAY;AAAA,IAC5D;AAAA,IAAU;AAAA,EACX;AAAA,EACD,uBAAuB;AAAA,IACrB;AAAA,IAAe;AAAA,IAAW;AAAA,IAAa;AAAA,IAAgB;AAAA,IAAW;AAAA,EACnE;AAAA,EACD,YAAY;AAAA,IACV;AAAA,IAAc;AAAA,IAAW;AAAA,IAAoB;AAAA,IAC7C;AAAA,IAA0B;AAAA,EAC9B;AACA;AAQO,SAAS,8BAA8B,SAAS;AACrD,QAAM,eAAe,QAAQ,YAAa;AAG1C,aAAW,CAAC,SAAS,QAAQ,KAAK,OAAO,QAAQ,yBAAyB,GAAG;AAC3E,QAAI,aAAa;AACjB,QAAI,kBAAkB;AAGtB,QAAI,CAAC,WAAW,YAAY,kBAAkB,EAAE,SAAS,OAAO,GAAG;AACjE,wBAAkB;AAAA,IACxB;AAEI,eAAW,WAAW,UAAU;AAC9B,UAAI,aAAa,SAAS,OAAO,GAAG;AAClC;AACA,YAAI,cAAc,iBAAiB;AACjC,iBAAO;AAAA,QACjB;AAAA,MACA;AAAA,IACA;AAAA,EACA;AAGE,SAAO;AACT;AA8GA,IAAI,OAAO,YAAY,eAAe,QAAQ,QAAQ,QAAQ,KAAK,CAAC,KAAK,QAAQ,KAAK,CAAC,EAAE,SAAS,kBAAkB,GAAG;AACrH,UAAQ,IAAI,6CAA6C;AAGzD,UAAQ,IAAI,iCAAiC;AAC7C,UAAQ,IAAI,2BAA2B,6BAA6B,SAAS,CAAC;AAC9E,UAAQ,IAAI,wBAAwB,6BAA6B,UAAU,CAAC;AAC5E,UAAQ,IAAI,4BAA4B,6BAA6B,SAAS,CAAC;AAG/E,UAAQ,IAAI,sCAAsC;AAClD,UAAQ,IAAI,oBAAoB,oBAAoB,KAAK,CAAC;AAC1D,UAAQ,IAAI,wBAAwB,oBAAoB,SAAS,CAAC;AAClE,UAAQ,IAAI,yBAAyB,oBAAoB,UAAU,CAAC;AAGpE,UAAQ,IAAI,mCAAmC;AAC/C,UAAQ,IAAI,yBAAyB,yBAAyB,KAAK,CAAC;AACpE,UAAQ,IAAI,6BAA6B,yBAAyB,SAAS,CAAC;AAC5E,UAAQ,IAAI,0BAA0B,yBAAyB,iBAAiB,CAAC;AAGjF,UAAQ,IAAI,6BAA6B;AACzC,UAAQ,IAAI,kCAAkC,iCAAiC,YAAY,CAAC;AAC5F,UAAQ,IAAI,2BAA2B,iCAAiC,KAAK,CAAC;AAC9E,UAAQ,IAAI,2BAA2B,iCAAiC,KAAK,CAAC;AAG9E,UAAQ,IAAI,iCAAiC;AAC7C,UAAQ,IAAI,oBAAoB,wBAAwB,KAAK,CAAC;AAC9D,UAAQ,IAAI,wBAAwB,wBAAwB,SAAS,CAAC;AACtE,UAAQ,IAAI,qBAAqB,wBAAwB,iBAAiB,CAAC;AAG3E,UAAQ,IAAI,mCAAmC;AAC/C,UAAQ,IAAI,uBAAuB,2BAA2B,KAAK,CAAC;AACpE,UAAQ,IAAI,4BAA4B,2BAA2B,UAAU,CAAC;AAC9E,UAAQ,IAAI,2BAA2B,2BAA2B,SAAS,CAAC;AAG5E,UAAQ,IAAI,kDAAkD;AAC9D,QAAM,iBAAiB;AACvB,QAAM,kBAAkB;AACxB,QAAM,eAAe;AAErB,UAAQ,IAAI,4BAA4B,8BAA8B,cAAc,CAAC;AACrF,UAAQ,IAAI,6BAA6B,8BAA8B,eAAe,CAAC;AACvF,UAAQ,IAAI,0BAA0B,8BAA8B,YAAY,CAAC;AAGjF,UAAQ,IAAI,qCAAqC;AACjD,UAAQ,IAAI,kCAAkC,qBAAqB,MAAM;AACzE,UAAQ,IAAI,2BAA2B,uBAAuB,MAAM;AACpE,UAAQ,IAAI,2BAA2B,uBAAuB,MAAM;AACpE,UAAQ,IAAI,6BAA6B,qBAAqB;AAG9D,UAAQ,IAAI,mCAAmC;AAC/C,QAAM,kBAAkB;AACxB,QAAM,oBAAoB;AAE1B,UAAQ,IAAI,gCAAgC,6BAA6B,WAAW,eAAe,CAAC;AACpG,UAAQ,IAAI,kCAAkC,6BAA6B,WAAW,iBAAiB,CAAC;AAGxG,UAAQ,IAAI,iCAAiC;AAC7C,SAAO,QAAQ,wBAAwB,EAAE,QAAQ,CAAC,CAAC,UAAU,MAAM,MAAM;AACvE,YAAQ,IAAI,eAAe,QAAQ,KAAK,OAAO,KAAK,KAAK,OAAO,MAAM,MAAM,SAAS;AAAA,EACzF,CAAG;AAED,UAAQ,IAAI,4CAA4C;AACxD,UAAQ,IAAI,sDAAsD;AACpE;ACteO,MAAM,gBAAgB;AAAA,EAC3B,cAAc;AACZ,SAAK,oBAAoB;AACzB,SAAK,qBAAqB;AAC1B,SAAK,eAAe;AACpB,SAAK,iBAAiB;AACtB,SAAK,6BAA6B;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQE,+BAA+B,iBAAiB,iBAAiB;AAC/D,UAAM,eAAe,kBAAkB,gBAAgB,2BAA2B,eAAe,IAAI;AAErG,UAAM,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sDAM6B,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuB9D,UAAM,SAAS;AAAA;AAAA,EAEjB,eAAe;AAAA;AAAA;AAIb,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,SAAS;AAAA,QACP,aAAa;AAAA,QACb,YAAY;AAAA,MACpB;AAAA,IACK;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWE,iCAAiC,cAAc,iBAAiB,WAAW,MAAM,iBAAiB,IAAI,aAAa,IAAI;AACtG,SAAK,yBAAyB,YAAY;AACzD,UAAM,YAAY,KAAK,8BAA8B,YAAY;AAEjE,UAAM,eAAe;AAAA;AAAA,qCAEY,YAAY;AAAA;AAAA;AAAA;AAAA,yEAIwB,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM/E,cAAc;AAAA;AAAA,EAEd,UAAU;AAAA;AAAA;AAAA,EAGV,KAAK,UAAU,WAAW,MAAM,CAAC,CAAC;AAEhC,UAAM,SAAS,uCAAuC,YAAY;AAAA;AAAA,EAEpE,eAAe;AAAA;AAAA;AAIb,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,SAAS;AAAA,QACP,aAAa;AAAA,QACb,YAAY;AAAA,MACpB;AAAA,IACK;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWE,wBAAwB,cAAc,iBAAiB,WAAW,MAAM,YAAY,MAAM,eAAe,IAAI;AAC3G,QAAI,CAAC,KAAK,yBAAyB,YAAY,GAAG;AAChD,aAAO;AAAA,IACb;AAEI,UAAM,oBAAoB,KAAK,0BAA0B,YAAY;AAErE,UAAM,eAAe,mDAAmD,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uDAMjC,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM7D,KAAK,UAAU,mBAAmB,MAAM,CAAC,CAAC;AAExC,QAAI,SAAS,8CAA8C,YAAY;AAAA;AAAA,EAEzE,eAAe;AAEb,QAAI,cAAc;AAChB,gBAAU;AAAA;AAAA;AAAA,EAAwC,YAAY;AAAA,IACpE;AAEI,QAAI,WAAW;AACb,gBAAU;AAAA;AAAA;AAAA,eACD,UAAU,aAAa,KAAK;AAAA,eAC5B,UAAU,aAAa,KAAK;AAAA,iBAC1B,UAAU,eAAe,KAAK;AAAA,IAC/C;AAEI,cAAU;AAEV,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,SAAS;AAAA,QACP,aAAa;AAAA,QACb,YAAY;AAAA,MACpB;AAAA,IACK;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYE,mCAAmC,cAAc,iBAAiB,UAAU,gBAAgB,YAAY,oBAAoB;AAC1H,UAAM,YAAY,KAAK,8BAA8B,YAAY;AAEjE,QAAI,SAAS,oFAAoF,YAAY;AAAA;AAAA;AAAA;AAAA,sDAI3D,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5D,cAAc;AAAA;AAAA,EAEd,UAAU;AAER,QAAI,sBAAsB,mBAAmB,aAAa;AACxD,gBAAU;AAAA;AAAA;AAAA,UACN,mBAAmB,YAAY;AAAA,iBACxB,mBAAmB,WAAW;AAAA,aAClC,mBAAmB,WAAW,KAAK;AAAA,IAChD;AAEI,cAAU;AAAA;AAAA;AAAA,EACZ,KAAK,UAAU,WAAW,MAAM,CAAC,CAAC;AAAA;AAAA;AAAA,EAGlC,eAAe;AAAA;AAAA;AAIb,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOE,yBAAyB,cAAc;AACrC,UAAM,eAAe,CAAC,GAAG,KAAK,YAAY;AAC1C,UAAM,iBAAiB,KAAK,mBAAmB,YAAY,KAAK,CAAE;AAClE,WAAO,CAAC,GAAG,cAAc,GAAG,cAAc;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOE,8BAA8B,cAAc;AAC1C,UAAM,SAAS,KAAK,yBAAyB,YAAY;AACzD,UAAM,YAAY,CAAE;AAEpB,WAAO,QAAQ,WAAS;AACtB,UAAI,UAAU,aAAa;AACzB,YAAI,KAAK,yBAAyB,YAAY,GAAG;AAC/C,oBAAU,YAAY,CAAC,KAAK,0BAA0B,YAAY,CAAC;AAAA,QAC7E;AAAA,MACA,OAAa;AACL,cAAM,cAAc,KAAK,kBAAkB,KAAK,KAAK,GAAG,KAAK;AAC7D,kBAAU,KAAK,IAAI,IAAI,WAAW;AAAA,MAC1C;AAAA,IACA,CAAK;AAED,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOE,0BAA0B,cAAc;AACtC,UAAM,YAAY,CAAE;AAEpB,WAAO,KAAK,KAAK,cAAc,EAAE,QAAQ,WAAS;AAChD,YAAM,cAAc,KAAK,eAAe,KAAK;AAC7C,gBAAU,KAAK,IAAI,IAAI,WAAW;AAAA,IACxC,CAAK;AAGD,QAAI,iBAAiB,cAAc;AACjC,gBAAU,OAAO;AACjB,gBAAU,+BAA+B;AACzC,gBAAU,8BAA8B;AAAA,IAC9C;AAEI,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOE,yBAAyB,cAAc;AACrC,WAAO,KAAK,2BAA2B,SAAS,aAAa,YAAW,CAAE;AAAA,EAC9E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQE,yBAAyB,eAAe,iBAAiB;AACvD,UAAM,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmBrB,UAAM,SAAS;AAAA;AAAA;AAAA,EAGjB,KAAK,UAAU,eAAe,MAAM,CAAC,CAAC;AAAA;AAAA;AAAA,EAGtC,eAAe;AAAA;AAAA;AAIb,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,SAAS;AAAA,QACP,aAAa;AAAA,QACb,YAAY;AAAA,MACpB;AAAA,IACK;AAAA,EACL;AACA;AAGK,MAAC,kBAAkB,IAAI,gBAAe;AAM3C,IAAI,OAAO,WAAW,aAAa;AACjC,SAAO,kBAAkB;AACzB,SAAO,kBAAkB;AAC3B;AA0DA,IAAI,OAAO,YAAY,eAAe,QAAQ,QAAQ,QAAQ,KAAK,CAAC,KAAK,QAAQ,KAAK,CAAC,EAAE,SAAS,oBAAoB,GAAG;AACvH,UAAQ,IAAI,+CAA+C;AAG3D,QAAM,YAAY,IAAI,gBAAiB;AAGvC,QAAM,sBAAsB;AAAA,IAC1B,4BAA4B,MAAM;AAAA,EACnC;AAGD,QAAM,oBAAoB;AAAA,IACxB,MAAM;AAAA,IACN,OAAO;AAAA,IACP,SAAS;AAAA,IACT,MAAM;AAAA,IACN,UAAU;AAAA,EACX;AAED,QAAM,qBAAqB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAU3B,UAAQ,IAAI,+CAA+C;AAC3D,MAAI;AACF,UAAM,iBAAiB,UAAU,+BAA+B,oBAAoB,mBAAmB;AACvG,YAAQ,IAAI,uCAAuC;AACnD,YAAQ,IAAI,4BAA4B,eAAe,aAAa,MAAM;AAC1E,YAAQ,IAAI,0BAA0B,eAAe,OAAO,MAAM;AAClE,YAAQ,IAAI,mBAAmB,eAAe,QAAQ,WAAW;AAAA,EAClE,SAAQ,OAAO;AACd,YAAQ,IAAI,mDAAmD,MAAM,OAAO;AAAA,EAChF;AAEE,UAAQ,IAAI,mDAAmD;AAC/D,MAAI;AACF,UAAM,iBAAiB,YAAY,kBAAkB,IAAI,KAAK,kBAAkB,KAAK;AACrF,UAAM,iBAAiB,UAAU,iCAAiC,OAAO,oBAAoB,MAAM,cAAc;AACjH,YAAQ,IAAI,yCAAyC;AACrD,YAAQ,IAAI,4BAA4B,eAAe,aAAa,MAAM;AAC1E,YAAQ,IAAI,0BAA0B,eAAe,OAAO,MAAM;AAClE,YAAQ,IAAI,kBAAkB,eAAe,QAAQ,UAAU;AAAA,EAChE,SAAQ,OAAO;AACd,YAAQ,IAAI,0CAA0C,MAAM,OAAO;AAAA,EACvE;AAEE,UAAQ,IAAI,mDAAmD;AAC/D,MAAI;AACF,UAAM,YAAY,EAAE,WAAW,WAAW,WAAW,UAAU,aAAa,UAAW;AACvF,UAAM,iBAAiB,UAAU,wBAAwB,OAAO,oBAAoB,MAAM,SAAS;AACnG,YAAQ,IAAI,yCAAyC;AACrD,YAAQ,IAAI,4BAA4B,eAAe,aAAa,MAAM;AAC1E,YAAQ,IAAI,0BAA0B,eAAe,OAAO,MAAM;AAClE,YAAQ,IAAI,kBAAkB,eAAe,QAAQ,UAAU;AAAA,EAChE,SAAQ,OAAO;AACd,YAAQ,IAAI,0CAA0C,MAAM,OAAO;AAAA,EACvE;AAEE,UAAQ,IAAI,uCAAuC;AACnD,MAAI;AACF,UAAM,eAAe,UAAU,8BAA8B,KAAK;AAClE,UAAM,sBAAsB,UAAU,8BAA8B,YAAY;AAChF,YAAQ,IAAI,8BAA8B;AAC1C,YAAQ,IAAI,4BAA4B,OAAO,KAAK,YAAY,EAAE,MAAM;AACxE,YAAQ,IAAI,mCAAmC,OAAO,KAAK,mBAAmB,EAAE,MAAM;AACtF,YAAQ,IAAI,yBAAyB,eAAe,YAAY;AAAA,EACjE,SAAQ,OAAO;AACd,YAAQ,IAAI,yCAAyC,MAAM,OAAO;AAAA,EACtE;AAEE,UAAQ,IAAI,6CAA6C;AACzD,MAAI;AACF,UAAM,kBAAkB,UAAU,yBAAyB,KAAK;AAChE,UAAM,mBAAmB,UAAU,yBAAyB,MAAM;AAClE,UAAM,sBAAsB,UAAU,yBAAyB,SAAS;AACxE,YAAQ,IAAI,oCAAoC;AAChD,YAAQ,IAAI,yBAAyB,eAAe;AACpD,YAAQ,IAAI,0BAA0B,gBAAgB;AACtD,YAAQ,IAAI,6BAA6B,mBAAmB;AAAA,EAC7D,SAAQ,OAAO;AACd,YAAQ,IAAI,sCAAsC,MAAM,OAAO;AAAA,EACnE;AAEE,UAAQ,IAAI,0CAA0C;AACtD,MAAI;AACF,UAAM,gBAAgB;AAAA,MACpB,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,IACd;AACD,UAAM,mBAAmB,UAAU,yBAAyB,eAAe,kBAAkB;AAC7F,YAAQ,IAAI,gCAAgC;AAC5C,YAAQ,IAAI,4BAA4B,iBAAiB,aAAa,MAAM;AAC5E,YAAQ,IAAI,0BAA0B,iBAAiB,OAAO,MAAM;AAAA,EACrE,SAAQ,OAAO;AACd,YAAQ,IAAI,4CAA4C,MAAM,OAAO;AAAA,EACzE;AAEE,UAAQ,IAAI,8CAA8C;AAC5D;"}