/**
 * MVAT Tesseract.js Sandbox Script
 * Handles OCR processing in a sandboxed environment to avoid CSP violations
 *
 * Features:
 * - Secure message passing with parent window
 * - Progress tracking and error handling
 * - Multiple language support
 * - Image preprocessing capabilities
 * - Performance monitoring
 */

class TesseractSandbox {
  constructor() {
    this.worker = null;
    this.initialized = false;
    this.currentLanguage = null;
    this.processingQueue = [];
    this.isProcessing = false;

    // UI elements
    this.statusEl = document.getElementById('status');
    this.progressContainer = document.getElementById('progress-container');
    this.progressFill = document.getElementById('progress-fill');
    this.progressText = document.getElementById('progress-text');
    this.logsEl = document.getElementById('logs');
    this.logEntries = document.getElementById('log-entries');
    this.debugInfo = document.getElementById('debug-info');
    this.debugContent = document.getElementById('debug-content');

    this.setupMessageHandling();
    this.updateStatus('🔄 Sandbox ready, waiting for initialization...', 'info');
  }

  /**
   * Setup message handling between sandbox and parent window
   */
  setupMessageHandling() {
    window.addEventListener('message', async (event) => {
      // Verify origin for security
      if (!this.isValidOrigin(event.origin)) {
        this.log('❌ Invalid origin: ' + event.origin, 'error');
        return;
      }

      const { type, data, requestId } = event.data;

      try {
        switch (type) {
          case 'INIT_TESSERACT':
            await this.initializeTesseract(data.language, requestId);
            break;

          case 'PROCESS_OCR':
            await this.processOCR(data, requestId);
            break;

          case 'TERMINATE_WORKER':
            await this.terminateWorker(requestId);
            break;

          case 'GET_STATUS':
            this.sendResponse(requestId, {
              initialized: this.initialized,
              currentLanguage: this.currentLanguage,
              isProcessing: this.isProcessing,
              queueLength: this.processingQueue.length
            });
            break;

          default:
            this.log('❌ Unknown message type: ' + type, 'error');
            this.sendError(requestId, 'Unknown message type: ' + type);
        }
      } catch (error) {
        this.log('❌ Error processing message: ' + error.message, 'error');
        this.sendError(requestId, error.message);
      }
    });

    // Delay sending ready message to ensure parent is ready to receive it
    this.sendReadyMessage();
  }

  /**
   * Send SANDBOX_READY message with retry mechanism
   */
  sendReadyMessage() {
    let attempts = 0;
    const maxAttempts = 10;
    const retryDelay = 500; // 500ms between attempts

    const sendReady = () => {
      attempts++;
      console.log(`🔄 Sending SANDBOX_READY message (attempt ${attempts}/${maxAttempts})`);

      try {
        this.sendMessage('SANDBOX_READY', {
          timestamp: Date.now(),
          attempt: attempts,
          tesseractLoaded: typeof Tesseract !== 'undefined'
        });

        this.log(`✅ SANDBOX_READY message sent (attempt ${attempts})`, 'success');
      } catch (error) {
        this.log(`❌ Failed to send SANDBOX_READY message: ${error.message}`, 'error');

        if (attempts < maxAttempts) {
          setTimeout(sendReady, retryDelay);
        } else {
          this.log('❌ Max attempts reached for SANDBOX_READY message', 'error');
        }
      }
    };

    // Send immediately and then retry if needed
    sendReady();

    // Also retry every 2 seconds for the first 10 seconds
    const retryInterval = setInterval(() => {
      if (attempts >= maxAttempts) {
        clearInterval(retryInterval);
        return;
      }
      sendReady();
    }, 2000);

    // Clear interval after 10 seconds
    setTimeout(() => {
      clearInterval(retryInterval);
    }, 10000);
  }

  /**
   * Validate message origin for security
   */
  isValidOrigin(origin) {
    // Allow chrome-extension:// origins and local file:// for development
    return origin.startsWith('chrome-extension://') ||
           origin.startsWith('file://') ||
           origin === 'null'; // For local file access
  }

  /**
   * Initialize Tesseract worker with specified language
   */
  async initializeTesseract(language = 'pol+eng', requestId) {
    try {
      this.updateStatus('🔄 Initializing Tesseract.js...', 'info');
      this.showProgress(0);

      // Check if Tesseract is available
      if (typeof Tesseract === 'undefined') {
        throw new Error('Tesseract.js is not loaded in sandbox environment');
      }

      if (this.worker && this.currentLanguage === language) {
        this.log('✅ Tesseract already initialized for ' + language, 'success');
        this.sendResponse(requestId, { success: true, language });
        return;
      }

      // Terminate existing worker if different language
      if (this.worker && this.currentLanguage !== language) {
        await this.worker.terminate();
        this.worker = null;
        this.initialized = false;
      }

      this.log('🔧 Creating Tesseract worker for language: ' + language);

      // Create worker with progress tracking and proper configuration
      this.worker = await Tesseract.createWorker(language, 1, {
        logger: (m) => this.handleTesseractLog(m),
        // Configure worker to use CDN files (more reliable in Chrome extension context)
        workerPath: 'https://unpkg.com/tesseract.js@5/dist/worker.min.js',
        corePath: 'https://unpkg.com/tesseract.js@5/dist/tesseract-core-simd.wasm.js',
        // Allow blob workers for better performance
        workerBlobURL: true
      });

      this.currentLanguage = language;
      this.initialized = true;

      this.updateStatus('✅ Tesseract.js initialized successfully', 'success');
      this.hideProgress();
      this.log('✅ Tesseract worker ready for ' + language, 'success');

      this.sendResponse(requestId, {
        success: true,
        language,
        timestamp: Date.now()
      });

    } catch (error) {
      this.updateStatus('❌ Failed to initialize Tesseract.js', 'error');
      this.log('❌ Initialization error: ' + error.message, 'error');
      this.sendError(requestId, 'Failed to initialize Tesseract: ' + error.message);
    }
  }

  /**
   * Process OCR on provided image data
   */
  async processOCR(data, requestId) {
    try {
      const { imageData, options = {} } = data;
      const {
        language = this.currentLanguage || 'pol+eng',
        confidence = 60,
        preprocessImage = true
      } = options;

      if (!this.initialized || !this.worker) {
        await this.initializeTesseract(language, null);
      }

      this.isProcessing = true;
      this.updateStatus('🔄 Processing OCR...', 'info');
      this.showProgress(0);
      this.log('🔍 Starting OCR processing...');

      // Convert base64 image data to blob if needed
      let imageInput = imageData;
      if (typeof imageData === 'string' && imageData.startsWith('data:')) {
        imageInput = this.dataURLToBlob(imageData);
      }

      // Perform OCR with progress tracking
      const result = await this.worker.recognize(imageInput, {
        logger: (m) => this.handleTesseractLog(m)
      });

      this.isProcessing = false;
      this.updateStatus('✅ OCR processing completed', 'success');
      this.hideProgress();

      const response = {
        text: result.data.text,
        confidence: result.data.confidence,
        words: result.data.words?.length || 0,
        processingTime: Date.now(),
        language: this.currentLanguage
      };

      this.log('✅ OCR completed: ' + response.words + ' words, ' +
               response.confidence.toFixed(1) + '% confidence', 'success');

      this.sendResponse(requestId, response);

    } catch (error) {
      this.isProcessing = false;
      this.updateStatus('❌ OCR processing failed', 'error');
      this.log('❌ OCR error: ' + error.message, 'error');
      this.sendError(requestId, 'OCR processing failed: ' + error.message);
    }
  }

  /**
   * Terminate Tesseract worker
   */
  async terminateWorker(requestId) {
    try {
      if (this.worker) {
        await this.worker.terminate();
        this.worker = null;
        this.initialized = false;
        this.currentLanguage = null;
        this.log('✅ Tesseract worker terminated', 'success');
      }

      this.updateStatus('🔄 Worker terminated', 'info');
      this.sendResponse(requestId, { success: true });

    } catch (error) {
      this.log('❌ Error terminating worker: ' + error.message, 'error');
      this.sendError(requestId, 'Failed to terminate worker: ' + error.message);
    }
  }

  /**
   * Handle Tesseract.js log messages
   */
  handleTesseractLog(m) {
    if (m.status === 'recognizing text') {
      const progress = Math.round(m.progress * 100);
      this.showProgress(progress);
      this.log(`🔍 Recognizing text: ${progress}%`);
    } else if (m.status) {
      this.log(`📋 ${m.status}: ${m.progress ? Math.round(m.progress * 100) + '%' : ''}`);
    }
  }

  /**
   * Convert data URL to Blob
   */
  dataURLToBlob(dataURL) {
    const arr = dataURL.split(',');
    const mime = arr[0].match(/:(.*?);/)[1];
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new Blob([u8arr], { type: mime });
  }

  /**
   * Send message to parent window
   */
  sendMessage(type, data) {
    try {
      const message = { type, data };
      console.log('📤 Sending message to parent:', message);

      // Check if we have a parent window
      if (!window.parent) {
        throw new Error('No parent window available');
      }

      // Send message with detailed logging
      window.parent.postMessage(message, '*');
      console.log(`✅ Message sent successfully: ${type}`);

    } catch (error) {
      console.error(`❌ Failed to send message ${type}:`, error);
      this.log(`❌ Message send error: ${error.message}`, 'error');
      throw error;
    }
  }

  /**
   * Send response to specific request
   */
  sendResponse(requestId, data) {
    this.sendMessage('RESPONSE', { requestId, data });
  }

  /**
   * Send error response
   */
  sendError(requestId, error) {
    this.sendMessage('ERROR', { requestId, error });
  }

  /**
   * Update status display
   */
  updateStatus(message, type = 'info') {
    this.statusEl.textContent = message;
    this.statusEl.className = `status ${type}`;
  }

  /**
   * Show progress bar
   */
  showProgress(percent) {
    this.progressContainer.classList.remove('hidden');
    this.progressFill.style.width = percent + '%';
    this.progressText.textContent = percent + '%';
  }

  /**
   * Hide progress bar
   */
  hideProgress() {
    this.progressContainer.classList.add('hidden');
  }

  /**
   * Add log entry
   */
  log(message, type = 'info') {
    console.log('[Sandbox]', message);

    this.logsEl.classList.remove('hidden');
    const entry = document.createElement('div');
    entry.className = `log-entry ${type}`;
    entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
    this.logEntries.appendChild(entry);

    // Keep only last 50 log entries
    while (this.logEntries.children.length > 50) {
      this.logEntries.removeChild(this.logEntries.firstChild);
    }

    // Auto-scroll to bottom
    this.logEntries.scrollTop = this.logEntries.scrollHeight;
  }
}

// Initialize sandbox when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  console.log('🔄 DOM loaded, preparing sandbox initialization...');

  // Wait for both DOM and potential parent window setup
  setTimeout(() => {
    console.log('🔄 Starting sandbox initialization...');
    console.log('📋 Tesseract.js available:', typeof Tesseract !== 'undefined');
    console.log('📋 Parent window available:', !!window.parent);
    console.log('📋 Window location:', window.location.href);

    if (typeof Tesseract !== 'undefined') {
      console.log('✅ Tesseract.js detected, initializing sandbox');
      window.tesseractSandbox = new TesseractSandbox();
    } else {
      console.warn('⚠️ Tesseract.js not available, initializing sandbox anyway');
      window.tesseractSandbox = new TesseractSandbox();
    }
  }, 2000); // Increased delay to ensure parent is ready
});

// Also try to initialize when window loads (backup)
window.addEventListener('load', () => {
  console.log('🔄 Window load event triggered');

  // Only initialize if not already done
  if (!window.tesseractSandbox) {
    console.log('🔄 Backup initialization triggered...');
    setTimeout(() => {
      if (!window.tesseractSandbox) {
        console.log('🔄 Creating sandbox from window load event');
        window.tesseractSandbox = new TesseractSandbox();
      }
    }, 1000);
  }
});
