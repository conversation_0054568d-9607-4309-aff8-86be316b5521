<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>MVAT Tesseract.js Sandbox</title>
  <meta name="description" content="Sandboxed environment for Tesseract.js OCR processing">
  
  <style>
    body {
      margin: 0;
      padding: 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f5f5f5;
      color: #333;
    }
    
    .sandbox-container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .status {
      padding: 10px;
      border-radius: 4px;
      margin: 10px 0;
      font-weight: 500;
    }
    
    .status.info {
      background: #e3f2fd;
      color: #1976d2;
      border: 1px solid #bbdefb;
    }
    
    .status.success {
      background: #e8f5e8;
      color: #2e7d32;
      border: 1px solid #c8e6c9;
    }
    
    .status.error {
      background: #ffebee;
      color: #c62828;
      border: 1px solid #ffcdd2;
    }
    
    .progress-bar {
      width: 100%;
      height: 8px;
      background: #e0e0e0;
      border-radius: 4px;
      overflow: hidden;
      margin: 10px 0;
    }
    
    .progress-fill {
      height: 100%;
      background: #4caf50;
      transition: width 0.3s ease;
      width: 0%;
    }
    
    .log-entry {
      padding: 5px 10px;
      margin: 2px 0;
      border-radius: 3px;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      background: #f8f9fa;
      border-left: 3px solid #dee2e6;
    }
    
    .log-entry.error {
      background: #fff5f5;
      border-left-color: #f56565;
      color: #c53030;
    }
    
    .log-entry.success {
      background: #f0fff4;
      border-left-color: #48bb78;
      color: #2f855a;
    }
    
    .hidden {
      display: none;
    }
  </style>
</head>
<body>
  <div class="sandbox-container">
    <h1>🔒 MVAT Tesseract.js Sandbox</h1>
    <p>Secure environment for OCR processing with Tesseract.js</p>
    
    <div id="status" class="status info">
      🔄 Initializing sandbox environment...
    </div>
    
    <div id="progress-container" class="hidden">
      <div class="progress-bar">
        <div id="progress-fill" class="progress-fill"></div>
      </div>
      <div id="progress-text">0%</div>
    </div>
    
    <div id="logs" class="hidden">
      <h3>Processing Logs</h3>
      <div id="log-entries"></div>
    </div>
    
    <div id="debug-info" class="hidden">
      <h3>Debug Information</h3>
      <pre id="debug-content"></pre>
    </div>
  </div>

  <!-- Load Tesseract.js from local assets -->
  <script>
    // Load Tesseract.js dynamically to avoid CSP issues
    (function() {
      // Create script element for Tesseract.js
      const script = document.createElement('script');
      script.onload = function() {
        console.log('✅ Tesseract.js loaded successfully in sandbox');
        // Load sandbox.js after Tesseract.js is ready
        const sandboxScript = document.createElement('script');
        sandboxScript.src = 'sandbox.js';
        document.head.appendChild(sandboxScript);
      };
      script.onerror = function() {
        console.error('❌ Failed to load Tesseract.js in sandbox');
        // Try to load sandbox.js anyway
        const sandboxScript = document.createElement('script');
        sandboxScript.src = 'sandbox.js';
        document.head.appendChild(sandboxScript);
      };

      // Try to load from different sources
      const tesseractSources = [
        '../assets/tesseract.min.js', // Local build
        'https://unpkg.com/tesseract.js@4.1.4/dist/tesseract.min.js', // Fallback CDN
        'https://cdn.jsdelivr.net/npm/tesseract.js@4.1.4/dist/tesseract.min.js' // Alternative CDN
      ];

      let sourceIndex = 0;

      function tryLoadTesseract() {
        if (sourceIndex >= tesseractSources.length) {
          console.error('❌ All Tesseract.js sources failed, loading sandbox anyway');
          script.onerror();
          return;
        }

        script.src = tesseractSources[sourceIndex];
        console.log(`🔄 Trying to load Tesseract.js from: ${script.src}`);
        sourceIndex++;
        document.head.appendChild(script);
      }

      // Override onerror to try next source
      script.onerror = function() {
        console.warn(`⚠️ Failed to load from: ${script.src}`);
        script.remove();
        const newScript = document.createElement('script');
        newScript.onload = script.onload;
        newScript.onerror = function() {
          tryLoadTesseract();
        };
        script = newScript;
        tryLoadTesseract();
      };

      tryLoadTesseract();
    })();
  </script>
</body>
</html>
