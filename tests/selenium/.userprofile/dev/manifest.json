{"manifest_version": 3, "name": "MVAT - Multi-VAT Invoice Processor", "version": "1.2.1", "description": "Parse invoices using PDF.js, Tesseract.js, and OpenAI. Display data in configurable tables with JSON storage and caching.", "permissions": ["storage", "activeTab", "scripting", "offscreen", "windows"], "host_permissions": ["https://api.openai.com/*", "https://api.deepseek.com/*"], "background": {"service_worker": "background.js", "type": "module"}, "action": {"default_title": "MVAT Invoice Processor", "default_popup": "popup.html", "default_icon": {"16": "icons/icon-16.png", "32": "icons/icon-32.png", "48": "icons/icon-48.png", "128": "icons/icon-128.png"}}, "icons": {"16": "icons/icon-16.png", "32": "icons/icon-32.png", "48": "icons/icon-48.png", "128": "icons/icon-128.png"}, "content_security_policy": {"extension_pages": "script-src 'self' 'wasm-unsafe-eval'; object-src 'self'; style-src 'self' 'unsafe-inline'; connect-src 'self' https://api.openai.com https://api.deepseek.com;", "sandbox": "sandbox allow-scripts allow-forms allow-popups allow-modals; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://unpkg.com https://cdn.jsdelivr.net; object-src 'self'; child-src 'self' blob:; worker-src 'self' blob: data:;"}, "sandbox": {"pages": ["sandbox/sandbox.html"]}, "web_accessible_resources": [{"resources": ["assets/*", "icons/*", "sandbox/*"], "matches": ["<all_urls>"]}], "minimum_chrome_version": "88"}