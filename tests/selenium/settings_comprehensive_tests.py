#!/usr/bin/env python3
"""
Comprehensive Selenium tests for Chrome extension settings page
Specifically targets "Test All" button error and all UI interactions
"""

import os
import time
import json
import datetime
from pathlib import Path
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from selenium.webdriver.common.action_chains import ActionChains

try:
    from webdriver_manager.chrome import ChromeDriverManager
    WEBDRIVER_MANAGER_AVAILABLE = True
except ImportError:
    WEBDRIVER_MANAGER_AVAILABLE = False

class SettingsComprehensiveTests:
    def __init__(self):
        self.driver = None
        self.extension_id = None
        self.wait = None
        self.extension_path = None
        # Handle both running from project root and from tests/selenium directory
        current_dir = Path.cwd()
        if current_dir.name == "selenium":
            self.screenshots_dir = current_dir / "screenshots"
            self.extension_path = current_dir.parent.parent / "dist" / "build"
        else:
            self.screenshots_dir = current_dir / "tests" / "selenium" / "screenshots"
            self.extension_path = current_dir / "dist" / "build"
        self.screenshots_dir.mkdir(parents=True, exist_ok=True)
        self.test_results = []
        self.console_errors = []

    def setup_driver(self):
        """Set up Chrome driver with extension loaded"""
        print("🔧 Setting up Chrome driver for settings testing...")

        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--allow-running-insecure-content")
        chrome_options.add_argument("--disable-features=VizDisplayCompositor")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--headless")  # Run in headless mode
        chrome_options.add_argument("--window-size=1400,1000")
        chrome_options.add_argument("--disable-extensions-except")
        chrome_options.add_argument("--disable-default-apps")
        chrome_options.add_argument("--no-first-run")

        # Load extension from dist/build directory
        self.extension_path = self.extension_path.resolve()
        if not self.extension_path.exists():
            print(f"❌ Extension not found at {self.extension_path}")
            print("💡 Run 'make build-extension' first")
            return False

        chrome_options.add_argument(f"--load-extension={self.extension_path}")

        # Enable extension logging
        chrome_options.add_argument("--enable-logging")
        chrome_options.add_argument("--log-level=0")

        try:
            # Try to find Chrome binary
            chrome_binary = None
            possible_paths = [
                "/usr/bin/google-chrome",
                "/usr/bin/chromium-browser",
                "/usr/bin/chromium",
                "/opt/google/chrome/chrome"
            ]

            for path in possible_paths:
                if os.path.exists(path):
                    chrome_binary = path
                    break

            if chrome_binary:
                chrome_options.binary_location = chrome_binary
                print(f"✅ Using Chrome binary: {chrome_binary}")

            # Use webdriver-manager if available, otherwise use system chromedriver
            if WEBDRIVER_MANAGER_AVAILABLE:
                service = Service(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            else:
                self.driver = webdriver.Chrome(options=chrome_options)

            self.wait = WebDriverWait(self.driver, 20)

            print("✅ Chrome driver set up successfully")
            return True

        except Exception as e:
            print(f"❌ Failed to set up Chrome driver: {e}")
            return False

    def capture_screenshot(self, test_name, description=""):
        """Capture screenshot with timestamp"""
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"settings_{test_name}_{timestamp}.png"
        filepath = self.screenshots_dir / filename

        try:
            self.driver.save_screenshot(str(filepath))
            print(f"📸 Screenshot saved: {filename}")
            if description:
                print(f"   📝 {description}")
            return str(filepath)
        except Exception as e:
            print(f"❌ Failed to capture screenshot: {e}")
            return None

    def get_console_errors(self):
        """Get and store console errors"""
        try:
            logs = self.driver.get_log('browser')
            error_logs = [log for log in logs if log['level'] in ['SEVERE', 'ERROR']]
            self.console_errors.extend(error_logs)
            return error_logs
        except Exception as e:
            print(f"⚠️ Could not retrieve console logs: {e}")
            return []

    def test_extension_popup_loading(self):
        """Test that Chrome extension popup loads properly"""
        print("\n🚀 Test 1: Extension Popup Loading")

        try:
            # Navigate to popup directly
            popup_path = self.extension_path / "popup.html"
            popup_url = f"file://{popup_path}"
            print(f"🔗 Loading popup: {popup_url}")

            self.driver.get(popup_url)
            time.sleep(8)  # Give more time for React to load

            # Capture initial screenshot
            self.capture_screenshot("popup_loading", "Initial popup load")

            # Check for React root element
            try:
                root_element = self.wait.until(
                    EC.presence_of_element_located((By.ID, "root"))
                )
                print("✅ React root element found")

                # Wait for app to fully load
                time.sleep(5)

                # Check if app loaded (look for main navigation or content)
                page_source = self.driver.page_source
                if "Settings" in page_source or "nav-tab" in page_source:
                    print("✅ App appears to be loaded with navigation")
                    self.test_results.append(("Extension Popup Loading", True, "Popup loaded successfully"))
                    return True
                else:
                    print("⚠️ App loaded but navigation not found")
                    self.test_results.append(("Extension Popup Loading", False, "Navigation not found"))
                    return False

            except TimeoutException:
                print("❌ React root element not found")
                self.test_results.append(("Extension Popup Loading", False, "Root element not found"))
                return False

        except Exception as e:
            print(f"❌ Test 1 FAILED: {e}")
            self.test_results.append(("Extension Popup Loading", False, str(e)))
            return False

    def test_settings_page_navigation(self):
        """Test navigation to settings page"""
        print("\n⚙️ Test 2: Settings Page Navigation")

        try:
            # Look for Settings tab/button
            settings_selectors = [
                "button[data-tab='settings']",
                ".nav-tab[data-tab='settings']",
                "button:contains('Settings')",
                ".tab-button:contains('Settings')",
                "[role='tab']:contains('Settings')"
            ]

            settings_button = None
            for selector in settings_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements and elements[0].is_displayed():
                        settings_button = elements[0]
                        print(f"✅ Found settings button with selector: {selector}")
                        break
                except:
                    continue

            if not settings_button:
                # Try to find by text content
                try:
                    settings_button = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Settings')]")
                    print("✅ Found settings button by text content")
                except:
                    print("❌ Settings button not found")
                    self.capture_screenshot("settings_not_found", "Settings button not found")
                    self.test_results.append(("Settings Page Navigation", False, "Settings button not found"))
                    return False

            # Click settings button
            try:
                actions = ActionChains(self.driver)
                actions.move_to_element(settings_button).click().perform()
                time.sleep(3)

                print("✅ Settings button clicked")
                self.capture_screenshot("settings_clicked", "Settings page accessed")

                # Verify settings page loaded
                page_source = self.driver.page_source
                if "Configuration" in page_source or "API Keys" in page_source or "Environment" in page_source:
                    print("✅ Settings page loaded successfully")
                    self.test_results.append(("Settings Page Navigation", True, "Settings page accessible"))
                    return True
                else:
                    print("❌ Settings page content not found")
                    self.test_results.append(("Settings Page Navigation", False, "Settings content not found"))
                    return False

            except Exception as e:
                print(f"❌ Failed to click settings button: {e}")
                self.test_results.append(("Settings Page Navigation", False, f"Click failed: {e}"))
                return False

        except Exception as e:
            print(f"❌ Test 2 FAILED: {e}")
            self.test_results.append(("Settings Page Navigation", False, str(e)))
            return False

    def test_configuration_tab_and_test_all_button(self):
        """Test Configuration tab and specifically the Test All button"""
        print("\n🧪 Test 3: Configuration Tab and Test All Button")

        try:
            # Look for Configuration tab
            config_selectors = [
                "button[data-tab='configuration']",
                ".tab-button:contains('Configuration')",
                "button:contains('Configuration')",
                "[role='tab']:contains('Configuration')"
            ]

            config_button = None
            for selector in config_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements and elements[0].is_displayed():
                        config_button = elements[0]
                        print(f"✅ Found configuration tab with selector: {selector}")
                        break
                except:
                    continue

            if not config_button:
                try:
                    config_button = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Configuration')]")
                    print("✅ Found configuration tab by text content")
                except:
                    print("❌ Configuration tab not found")
                    self.capture_screenshot("config_tab_not_found", "Configuration tab not found")
                    self.test_results.append(("Configuration Tab Access", False, "Configuration tab not found"))
                    return False

            # Click configuration tab
            try:
                actions = ActionChains(self.driver)
                actions.move_to_element(config_button).click().perform()
                time.sleep(3)

                print("✅ Configuration tab clicked")
                self.capture_screenshot("config_tab_clicked", "Configuration tab accessed")

            except Exception as e:
                print(f"❌ Failed to click configuration tab: {e}")
                self.test_results.append(("Configuration Tab Access", False, f"Click failed: {e}"))
                return False

            # Now look for Test All button
            test_all_selectors = [
                "button:contains('Test All')",
                ".test-all-button",
                "button[data-action='test-all']",
                ".btn:contains('Test All')"
            ]

            test_all_button = None
            for selector in test_all_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements and elements[0].is_displayed():
                        test_all_button = elements[0]
                        print(f"✅ Found Test All button with selector: {selector}")
                        break
                except:
                    continue

            if not test_all_button:
                try:
                    test_all_button = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Test All')]")
                    print("✅ Found Test All button by text content")
                except:
                    print("❌ Test All button not found")
                    self.capture_screenshot("test_all_not_found", "Test All button not found")
                    self.test_results.append(("Test All Button", False, "Test All button not found"))
                    return False

            # Clear previous console errors
            self.get_console_errors()

            # Click Test All button and monitor for errors
            try:
                print("🔍 Clicking Test All button and monitoring for errors...")
                actions = ActionChains(self.driver)
                actions.move_to_element(test_all_button).click().perform()
                time.sleep(5)  # Wait for test execution

                # Check for console errors after clicking
                new_errors = self.get_console_errors()

                self.capture_screenshot("test_all_clicked", "Test All button clicked")

                # Check for specific errors mentioned by user
                error_found = False
                for error in new_errors:
                    error_msg = error['message'].lower()
                    if 'buildconfiguration is not a function' in error_msg or 'failed to fetch' in error_msg:
                        print(f"❌ CRITICAL ERROR DETECTED: {error['message']}")
                        error_found = True

                if error_found:
                    print("❌ Test All button produced expected errors")
                    self.test_results.append(("Test All Button Error Detection", True, "Expected errors caught"))
                    return True
                elif new_errors:
                    print(f"⚠️ Test All button produced {len(new_errors)} console errors")
                    for error in new_errors[:3]:  # Show first 3 errors
                        print(f"   ERROR: {error['message'][:100]}...")
                    self.test_results.append(("Test All Button Error Detection", True, f"{len(new_errors)} errors detected"))
                    return True
                else:
                    print("✅ Test All button executed without console errors")
                    self.test_results.append(("Test All Button Functionality", True, "No console errors"))
                    return True

            except Exception as e:
                print(f"❌ Failed to click Test All button: {e}")
                self.test_results.append(("Test All Button", False, f"Click failed: {e}"))
                return False

        except Exception as e:
            print(f"❌ Test 3 FAILED: {e}")
            self.test_results.append(("Configuration Tab and Test All", False, str(e)))
            return False

    def test_all_settings_buttons(self):
        """Test all buttons in settings page"""
        print("\n🔘 Test 4: All Settings Buttons")

        try:
            # Define all expected buttons in settings
            expected_buttons = [
                ("Reload Configuration", "reload"),
                ("Load Settings", "load"),
                ("JSON Input", "json"),
                ("Test All", "test-all"),
                ("Save", "save"),
                ("Reset", "reset"),
                ("Export", "export"),
                ("Import", "import")
            ]

            buttons_found = 0
            buttons_working = 0

            for button_name, button_type in expected_buttons:
                try:
                    # Try multiple selectors for each button
                    selectors = [
                        f"button:contains('{button_name}')",
                        f".btn-{button_type}",
                        f"button[data-action='{button_type}']",
                        f".{button_type}-button"
                    ]

                    button_element = None
                    for selector in selectors:
                        try:
                            elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                            if elements and elements[0].is_displayed():
                                button_element = elements[0]
                                break
                        except:
                            continue

                    if not button_element:
                        try:
                            button_element = self.driver.find_element(By.XPATH, f"//button[contains(text(), '{button_name}')]")
                        except:
                            pass

                    if button_element:
                        buttons_found += 1
                        print(f"✅ Found button: {button_name}")

                        # Test if button is clickable
                        try:
                            if button_element.is_enabled():
                                # Clear console errors before click
                                self.get_console_errors()

                                # Click button
                                actions = ActionChains(self.driver)
                                actions.move_to_element(button_element).click().perform()
                                time.sleep(2)

                                # Check for errors after click
                                new_errors = self.get_console_errors()
                                if new_errors:
                                    print(f"⚠️ Button '{button_name}' produced {len(new_errors)} console errors")
                                else:
                                    print(f"✅ Button '{button_name}' clicked successfully")
                                    buttons_working += 1
                            else:
                                print(f"⚠️ Button '{button_name}' found but disabled")
                        except Exception as e:
                            print(f"❌ Button '{button_name}' click failed: {str(e)[:50]}...")
                    else:
                        print(f"❌ Button '{button_name}' not found")

                except Exception as e:
                    print(f"❌ Error testing button '{button_name}': {str(e)[:50]}...")

            self.capture_screenshot("all_buttons_tested", f"Tested {buttons_found} buttons")

            success_rate = (buttons_working / len(expected_buttons)) * 100 if expected_buttons else 0
            print(f"📊 Button Test Results: {buttons_working}/{len(expected_buttons)} working ({success_rate:.1f}%)")

            if success_rate >= 50:
                self.test_results.append(("All Settings Buttons", True, f"{success_rate:.1f}% buttons working"))
                return True
            else:
                self.test_results.append(("All Settings Buttons", False, f"Only {success_rate:.1f}% buttons working"))
                return False

        except Exception as e:
            print(f"❌ Test 4 FAILED: {e}")
            self.test_results.append(("All Settings Buttons", False, str(e)))
            return False

    def test_error_handling_and_user_feedback(self):
        """Test error handling and user feedback in settings"""
        print("\n🚨 Test 5: Error Handling and User Feedback")

        try:
            # Look for error display areas
            error_selectors = [
                ".error-message",
                ".alert-error",
                ".notification-error",
                ".loading-errors",
                "[role='alert']"
            ]

            error_areas_found = 0
            for selector in error_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        error_areas_found += len(elements)
                        print(f"✅ Found error display area: {selector}")

                        # Check if error area has content
                        for element in elements:
                            if element.text.strip():
                                print(f"   📝 Error content: {element.text[:100]}...")
                except:
                    continue

            # Look for loading indicators
            loading_selectors = [
                ".loading",
                ".spinner",
                ".loading-spinner",
                "[data-loading='true']"
            ]

            loading_indicators = 0
            for selector in loading_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        loading_indicators += len(elements)
                        print(f"✅ Found loading indicator: {selector}")
                except:
                    continue

            # Look for success feedback
            success_selectors = [
                ".success-message",
                ".alert-success",
                ".notification-success"
            ]

            success_areas = 0
            for selector in success_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        success_areas += len(elements)
                        print(f"✅ Found success feedback area: {selector}")
                except:
                    continue

            self.capture_screenshot("error_handling", "Error handling and feedback areas")

            total_feedback_areas = error_areas_found + loading_indicators + success_areas
            print(f"📊 User Feedback Areas: {total_feedback_areas} total ({error_areas_found} error, {loading_indicators} loading, {success_areas} success)")

            if total_feedback_areas >= 3:
                self.test_results.append(("Error Handling and User Feedback", True, f"{total_feedback_areas} feedback areas found"))
                return True
            else:
                self.test_results.append(("Error Handling and User Feedback", False, f"Only {total_feedback_areas} feedback areas found"))
                return False

        except Exception as e:
            print(f"❌ Test 5 FAILED: {e}")
            self.test_results.append(("Error Handling and User Feedback", False, str(e)))
            return False

    def generate_comprehensive_report(self):
        """Generate comprehensive test report"""
        print("\n📊 GENERATING COMPREHENSIVE SETTINGS TEST REPORT")
        print("=" * 70)

        passed_tests = sum(1 for _, passed, _ in self.test_results if passed)
        total_tests = len(self.test_results)
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0

        print(f"📈 Overall Success Rate: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
        print(f"🐛 Total Console Errors Detected: {len(self.console_errors)}")
        print("\n📋 Detailed Results:")

        for test_name, passed, details in self.test_results:
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f"   {status} {test_name}: {details}")

        if self.console_errors:
            print(f"\n🚨 Console Errors Found ({len(self.console_errors)}):")
            for i, error in enumerate(self.console_errors[:5]):  # Show first 5 errors
                print(f"   {i+1}. [{error['level']}] {error['message'][:100]}...")

        print(f"\n📸 Screenshots saved in: {self.screenshots_dir}")

        # Generate JSON report
        report_data = {
            "timestamp": datetime.datetime.now().isoformat(),
            "test_type": "comprehensive_settings",
            "success_rate": success_rate,
            "passed_tests": passed_tests,
            "total_tests": total_tests,
            "console_errors_count": len(self.console_errors),
            "console_errors": [
                {
                    "level": error['level'],
                    "message": error['message'],
                    "timestamp": error.get('timestamp', '')
                }
                for error in self.console_errors
            ],
            "results": [
                {
                    "test_name": name,
                    "passed": passed,
                    "details": details
                }
                for name, passed, details in self.test_results
            ]
        }

        report_file = self.screenshots_dir / "settings_comprehensive_report.json"
        with open(report_file, 'w') as f:
            json.dump(report_data, f, indent=2)

        print(f"📄 JSON report saved: {report_file}")

        return success_rate >= 60  # 60% success rate required for settings

    def cleanup(self):
        """Clean up resources"""
        if self.driver:
            try:
                self.driver.quit()
                print("🧹 Chrome driver cleaned up")
            except:
                pass

    def run_all_tests(self):
        """Run all comprehensive settings tests"""
        print("🧪 COMPREHENSIVE CHROME EXTENSION SETTINGS TESTING")
        print("=" * 70)
        print("🎯 Purpose: Test all settings page interactions and error handling")
        print("📅 Timestamp:", datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        print("🔍 Focus: Test All button error and comprehensive UI coverage")
        print("=" * 70)

        if not self.setup_driver():
            return False

        try:
            # Run all tests in sequence
            test_methods = [
                self.test_extension_popup_loading,
                self.test_settings_page_navigation,
                self.test_configuration_tab_and_test_all_button,
                self.test_all_settings_buttons,
                self.test_error_handling_and_user_feedback
            ]

            for test_method in test_methods:
                try:
                    test_method()
                except Exception as e:
                    print(f"❌ Test method {test_method.__name__} failed: {e}")
                    self.test_results.append((test_method.__name__, False, str(e)))

            # Generate final report
            success = self.generate_comprehensive_report()

            return success

        finally:
            self.cleanup()

if __name__ == "__main__":
    tester = SettingsComprehensiveTests()
    success = tester.run_all_tests()
    exit(0 if success else 1)
