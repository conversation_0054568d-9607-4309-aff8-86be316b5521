#!/usr/bin/env python3
"""
Multi-Step Pipeline Testing with Selenium
Tests the document processing pipeline functionality
"""

import os
import sys
import time
import json
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON>riverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains

class MultiStepPipelineTest:
    def __init__(self):
        self.driver = None
        self.extension_id = None
        self.test_results = []
        self.screenshots_dir = "tests/selenium/screenshots"
        
        # Ensure screenshots directory exists
        os.makedirs(self.screenshots_dir, exist_ok=True)
        
        print("🧪 MULTI-STEP PIPELINE TESTING")
        print("=" * 60)
        print("🎯 Purpose: Test document processing pipeline functionality")
        print(f"📅 Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)

    def setup_chrome_driver(self):
        """Set up Chrome driver with extension loaded"""
        print("🔧 Setting up Chrome driver for pipeline testing...")
        
        # Extension path
        extension_path = os.path.abspath("dist/dev")
        if not os.path.exists(extension_path):
            raise Exception(f"Extension not found at {extension_path}. Run 'make dev-extension' first.")
        
        print(f"✅ Loading extension from: {extension_path}")
        
        # Chrome options
        chrome_options = Options()
        chrome_options.add_argument(f"--load-extension={extension_path}")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--allow-running-insecure-content")
        chrome_options.add_argument("--disable-features=VizDisplayCompositor")
        
        # Use Chrome 135 if available
        chrome_binary = "tests/selenium/chrome-135/chrome-linux64/chrome"
        if os.path.exists(chrome_binary):
            chrome_options.binary_location = os.path.abspath(chrome_binary)
            print(f"✅ Using Chrome 135 binary: {chrome_binary}")
        
        # Create driver
        self.driver = webdriver.Chrome(options=chrome_options)
        self.driver.set_window_size(1200, 800)
        
        # Get extension ID
        self.driver.get("chrome://extensions/")
        time.sleep(2)
        
        # Enable developer mode and get extension ID
        try:
            dev_mode_toggle = self.driver.find_element(By.CSS_SELECTOR, "#devMode")
            if not dev_mode_toggle.is_selected():
                dev_mode_toggle.click()
                time.sleep(1)
        except:
            pass
        
        # Get extension ID via management API
        extension_info = self.driver.execute_script("""
            return new Promise((resolve) => {
                chrome.management.getAll((extensions) => {
                    const mvat = extensions.find(ext => ext.name.includes('MVAT'));
                    resolve(mvat ? mvat.id : null);
                });
            });
        """)
        
        if extension_info:
            self.extension_id = extension_info
            print(f"✅ Extension ID: {self.extension_id}")
        else:
            raise Exception("Could not find MVAT extension")

    def navigate_to_upload_page(self):
        """Navigate to the upload page"""
        print("🚀 Navigating to upload page...")
        
        popup_url = f"chrome-extension://{self.extension_id}/popup.html"
        self.driver.get(popup_url)
        
        # Wait for page to load
        WebDriverWait(self.driver, 10).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        # Click on Upload tab if not already active
        try:
            upload_tab = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Upload') or contains(@class, 'upload')]"))
            )
            upload_tab.click()
            time.sleep(1)
        except:
            print("⚠️ Upload tab not found or already active")
        
        # Take screenshot
        screenshot_path = os.path.join(self.screenshots_dir, f"upload_page_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
        self.driver.save_screenshot(screenshot_path)
        print(f"📸 Screenshot saved: {os.path.basename(screenshot_path)}")

    def enable_debug_mode(self):
        """Enable debug mode to see pipeline steps"""
        print("🐛 Enabling debug mode...")
        
        try:
            debug_button = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Debug') or contains(text(), 'Show Debug')]"))
            )
            debug_button.click()
            time.sleep(1)
            
            # Verify debug container is visible
            debug_container = self.driver.find_element(By.CLASS_NAME, "debug-container")
            if debug_container.is_displayed():
                print("✅ Debug mode enabled successfully")
                return True
            else:
                print("⚠️ Debug container not visible")
                return False
                
        except Exception as e:
            print(f"⚠️ Could not enable debug mode: {e}")
            return False

    def test_file_upload_pipeline(self):
        """Test file upload with multi-step pipeline"""
        print("📄 Testing file upload with multi-step pipeline...")
        
        # Path to test file
        test_file_path = os.path.abspath("docs/data/samples/invoices/input/327_K_08_23_PCM.pdf")
        if not os.path.exists(test_file_path):
            print(f"❌ Test file not found: {test_file_path}")
            return False
        
        print(f"📁 Using test file: {test_file_path}")
        
        try:
            # Find file input element
            file_input = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='file']"))
            )
            
            # Upload file
            file_input.send_keys(test_file_path)
            print("✅ File uploaded via input element")
            
            # Wait for processing to start
            time.sleep(2)
            
            # Monitor processing logs
            processing_started = False
            max_wait_time = 30  # 30 seconds max wait
            start_time = time.time()
            
            while time.time() - start_time < max_wait_time:
                # Check console logs for processing updates
                logs = self.driver.get_log('browser')
                for log in logs:
                    message = log['message']
                    if 'Processing Started' in message or 'Pipeline' in message:
                        processing_started = True
                        print(f"📊 Processing log: {message}")
                
                # Check if processing is complete
                try:
                    # Look for success indicators in the UI
                    success_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'completed') or contains(text(), 'success') or contains(text(), '✅')]")
                    if success_elements:
                        print("✅ Processing appears to be complete")
                        break
                except:
                    pass
                
                time.sleep(1)
            
            # Take screenshot after processing
            screenshot_path = os.path.join(self.screenshots_dir, f"after_upload_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
            self.driver.save_screenshot(screenshot_path)
            print(f"📸 Screenshot saved: {os.path.basename(screenshot_path)}")
            
            # Check for processed results
            return self.verify_processing_results()
            
        except Exception as e:
            print(f"❌ File upload failed: {e}")
            return False

    def verify_processing_results(self):
        """Verify that processing results are correct"""
        print("🔍 Verifying processing results...")
        
        try:
            # Check for document table or results display
            results_found = False
            
            # Look for table rows or result cards
            table_rows = self.driver.find_elements(By.CSS_SELECTOR, "tr, .invoice-card, .document-row")
            if table_rows:
                print(f"📊 Found {len(table_rows)} result elements")
                results_found = True
            
            # Check for specific field values
            page_source = self.driver.page_source.lower()
            
            # Expected values from the test file
            expected_values = [
                "327_k_08_23_pcm.pdf",
                "7631.93",
                "pln"
            ]
            
            found_values = []
            for value in expected_values:
                if value.lower() in page_source:
                    found_values.append(value)
                    print(f"✅ Found expected value: {value}")
                else:
                    print(f"❌ Missing expected value: {value}")
            
            # Check for "Unknown" values (should be replaced by pipeline)
            unknown_count = page_source.count("unknown")
            if unknown_count > 0:
                print(f"⚠️ Found {unknown_count} 'Unknown' values - pipeline may not be working correctly")
            else:
                print("✅ No 'Unknown' values found - pipeline appears to be extracting data correctly")
            
            # Success criteria: at least 2/3 expected values found and no unknown values
            success = len(found_values) >= 2 and unknown_count == 0
            
            if success:
                print("✅ Processing results verification PASSED")
            else:
                print("❌ Processing results verification FAILED")
            
            return success
            
        except Exception as e:
            print(f"❌ Results verification failed: {e}")
            return False

    def test_debug_interface(self):
        """Test the debug interface functionality"""
        print("🐛 Testing debug interface...")
        
        try:
            # Check if debug container is visible
            debug_container = self.driver.find_element(By.CLASS_NAME, "debug-container")
            if not debug_container.is_displayed():
                print("❌ Debug container not visible")
                return False
            
            # Look for pipeline step buttons
            step_buttons = self.driver.find_elements(By.XPATH, "//button[contains(text(), 'Run Step') or contains(text(), 'Pipeline')]")
            print(f"🔧 Found {len(step_buttons)} debug control buttons")
            
            # Look for step results display
            step_results = self.driver.find_elements(By.CSS_SELECTOR, ".step-result, .debug-result, pre")
            print(f"📊 Found {len(step_results)} result display elements")
            
            # Take screenshot of debug interface
            screenshot_path = os.path.join(self.screenshots_dir, f"debug_interface_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
            self.driver.save_screenshot(screenshot_path)
            print(f"📸 Debug interface screenshot: {os.path.basename(screenshot_path)}")
            
            return len(step_buttons) > 0 or len(step_results) > 0
            
        except Exception as e:
            print(f"❌ Debug interface test failed: {e}")
            return False

    def run_all_tests(self):
        """Run all pipeline tests"""
        try:
            self.setup_chrome_driver()
            
            # Test 1: Navigate to upload page
            self.navigate_to_upload_page()
            self.test_results.append(("Navigation", True))
            
            # Test 2: Enable debug mode
            debug_enabled = self.enable_debug_mode()
            self.test_results.append(("Debug Mode", debug_enabled))
            
            # Test 3: Test file upload and pipeline
            upload_success = self.test_file_upload_pipeline()
            self.test_results.append(("File Upload Pipeline", upload_success))
            
            # Test 4: Test debug interface
            debug_interface_success = self.test_debug_interface()
            self.test_results.append(("Debug Interface", debug_interface_success))
            
            # Generate report
            self.generate_report()
            
        except Exception as e:
            print(f"❌ Test execution failed: {e}")
            self.test_results.append(("Test Execution", False))
        finally:
            if self.driver:
                self.driver.quit()

    def generate_report(self):
        """Generate test report"""
        print("\n📊 GENERATING PIPELINE TEST REPORT")
        print("=" * 60)
        
        passed_tests = sum(1 for _, result in self.test_results if result)
        total_tests = len(self.test_results)
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        print(f"📈 Overall Success Rate: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
        print("\n📋 Detailed Results:")
        
        for test_name, result in self.test_results:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"   {status} {test_name}")
        
        # Save JSON report
        report_data = {
            "timestamp": datetime.now().isoformat(),
            "test_type": "multi_step_pipeline",
            "success_rate": success_rate,
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "results": [{"test": name, "passed": result} for name, result in self.test_results]
        }
        
        report_path = os.path.join(self.screenshots_dir, "pipeline_test_report.json")
        with open(report_path, 'w') as f:
            json.dump(report_data, f, indent=2)
        
        print(f"📄 JSON report saved: {report_path}")
        print(f"📸 Screenshots saved in: {self.screenshots_dir}")

if __name__ == "__main__":
    test = MultiStepPipelineTest()
    test.run_all_tests()
