#!/usr/bin/env python3
"""
Enhanced Selenium tests for Chrome extension state verification
Provides comprehensive testing as first step in any assignment workflow
"""

import os
import time
import json
import datetime
from pathlib import Path
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.common.action_chains import ActionChains

try:
    from webdriver_manager.chrome import ChromeDriverManager
    WEBDRIVER_MANAGER_AVAILABLE = True
    # Chrome WebDriver version 135 required for --load-extension support
    # Chrome 136+ removed this flag, so we use Chrome 135 + WebDriver 135
    # See: https://github.com/SeleniumHQ/selenium/issues/15788
    CHROME_DRIVER_VERSION = "135.0.7049.97"  # Compatible with Chrome 135.0.7049.97
    CHROME_135_BINARY_PATH = None
except ImportError:
    WEBDRIVER_MANAGER_AVAILABLE = False
    CHROME_DRIVER_VERSION = None
    CHROME_135_BINARY_PATH = None

class ExtensionStateTests:
    def __init__(self):
        self.driver = None
        self.extension_id = None
        self.wait = None
        self.extension_path = None
        # Handle both running from project root and from tests/selenium directory
        current_dir = Path.cwd()
        if current_dir.name == "selenium":
            self.screenshots_dir = current_dir / "screenshots"
            # Use dist/dev for development testing
            self.extension_path = current_dir.parent.parent / "dist" / "dev"
            self.chrome_135_path = current_dir / "chrome-135" / "chrome-linux64" / "chrome"
        else:
            self.screenshots_dir = current_dir / "tests" / "selenium" / "screenshots"
            # Use dist/dev for development testing
            self.extension_path = current_dir / "dist" / "dev"
            self.chrome_135_path = current_dir / "tests" / "selenium" / "chrome-135" / "chrome-linux64" / "chrome"
        self.screenshots_dir.mkdir(parents=True, exist_ok=True)
        self.test_results = []

    def setup_driver(self):
        """Set up Chrome driver with extension loaded"""
        print("🔧 Setting up Chrome driver for extension testing...")

        chrome_options = Options()
        # Essential Chrome arguments for extension testing
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--allow-running-insecure-content")
        chrome_options.add_argument("--disable-features=VizDisplayCompositor")
        chrome_options.add_argument("--disable-gpu")

        # HARD REQUIREMENT: NEVER use headless mode - extensions require visible browser
        # Headless mode prevents proper extension loading and chrome-extension:// URLs
        # chrome_options.add_argument("--headless")  # FORBIDDEN for extension testing
        chrome_options.add_argument("--window-size=1200,800")
        chrome_options.add_argument("--disable-default-apps")
        chrome_options.add_argument("--no-first-run")

        # HARD REQUIREMENT: Enable developer mode and extensions
        chrome_options.add_argument("--enable-extensions")
        # Note: Don't use --disable-extensions-except when using add_extension()

        # HARD REQUIREMENT: Force extension loading
        chrome_options.add_argument("--disable-extensions-file-access-check")
        chrome_options.add_argument("--enable-extension-activity-logging")
        chrome_options.add_argument("--extension-content-verification=bootstrap")
        chrome_options.add_argument("--allow-running-insecure-content")
        chrome_options.add_argument("--disable-features=TranslateUI")
        chrome_options.add_argument("--disable-ipc-flooding-protection")

        # Enable remote debugging for extension inspection
        chrome_options.add_argument("--remote-debugging-port=9222")

        # Additional flags for proper extension support
        chrome_options.add_argument("--disable-background-timer-throttling")
        chrome_options.add_argument("--disable-backgrounding-occluded-windows")
        chrome_options.add_argument("--disable-renderer-backgrounding")

        # HARD REQUIREMENT: Enable developer mode via WebDriver preferences
        prefs = {
            "extensions.ui.developer_mode": True,
            "extensions.ui.developer_mode_enabled": True,
            "extensions.settings": {
                "developer_mode": True
            },
            "profile.default_content_setting_values.notifications": 2,
            "profile.default_content_settings.popups": 0
        }
        chrome_options.add_experimental_option("prefs", prefs)

        # HARD REQUIREMENT: Use detach mode to keep browser open for debugging
        chrome_options.add_experimental_option("detach", True)


        # Load extension from dist directory
        self.extension_path = self.extension_path.resolve()
        if not self.extension_path.exists():
            print(f"❌ Extension not found at {self.extension_path}")
            print("💡 Run 'make build-extension' first")
            return False

        # Verify manifest.json exists
        manifest_path = self.extension_path / "manifest.json"
        if not manifest_path.exists():
            print(f"❌ manifest.json not found at {manifest_path}")
            return False

        print(f"✅ Loading extension from: {self.extension_path}")
        print(f"✅ Manifest found at: {manifest_path}")

        # HARD REQUIREMENT: Use temporary user data directory for clean extension loading
        import tempfile
        temp_dir = tempfile.mkdtemp(prefix="chrome_selenium_")
        chrome_options.add_argument(f"--user-data-dir={temp_dir}")
        print(f"✅ Using temporary Chrome profile: {temp_dir}")


        # HARD REQUIREMENT: Create ZIP file and load extension via chrome_options.add_extension()
        extension_zip_path = self.create_extension_zip()
        if not extension_zip_path:
            print("❌ Failed to create extension ZIP file")
            return False

        # CRITICAL: Use --load-extension flag with Chrome 135 (still supported)
        # Chrome 136+ removed this flag, but Chrome 135 + WebDriver 135 still supports it
        # See: https://github.com/SeleniumHQ/selenium/issues/15788
        if self.chrome_135_path.exists():
            # Use --load-extension flag with Chrome 135
            chrome_options.add_argument(f'--load-extension={self.extension_path}')
            print(f"✅ Extension loaded via --load-extension flag: {self.extension_path}")
        else:
            # Fallback to add_extension() method for newer Chrome versions
            chrome_options.add_extension(extension_zip_path)
            print(f"✅ Extension loaded via add_extension() method: {extension_zip_path}")
            # Add argument to disable the extension command line switch restriction
            chrome_options.add_argument("--disable-features=DisableLoadExtensionCommandLineSwitch")

        # Enable extension logging
        chrome_options.add_argument("--enable-logging")
        chrome_options.add_argument("--log-level=0")
        chrome_options.add_argument("--verbose")

        try:
            # CRITICAL: Use Chrome 135 binary that supports --load-extension
            chrome_binary = None

            # First, try to use Chrome 135 if available
            if self.chrome_135_path.exists():
                chrome_binary = str(self.chrome_135_path)
                print(f"✅ Using Chrome 135 binary (supports --load-extension): {chrome_binary}")
            else:
                print("⚠️ Chrome 135 not found. Trying system Chrome (may not support --load-extension)")
                print("💡 Run 'make install-chrome-135' to install Chrome 135 for testing")

                # Fallback to system Chrome
                possible_paths = [
                    "/usr/bin/google-chrome",
                    "/usr/bin/chromium-browser",
                    "/usr/bin/chromium",
                    "/opt/google/chrome/chrome"
                ]

                for path in possible_paths:
                    if os.path.exists(path):
                        chrome_binary = path
                        break

            if chrome_binary:
                chrome_options.binary_location = chrome_binary
                print(f"✅ Using Chrome binary: {chrome_binary}")

            # Use webdriver-manager if available, otherwise use system chromedriver
            # CRITICAL: Use Chrome WebDriver version 135 to support --load-extension with Chrome 135
            if WEBDRIVER_MANAGER_AVAILABLE and CHROME_DRIVER_VERSION and self.chrome_135_path.exists():
                print(f"🔧 Installing Chrome WebDriver version {CHROME_DRIVER_VERSION} (supports --load-extension)")
                service = Service(ChromeDriverManager(driver_version=CHROME_DRIVER_VERSION).install())
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            elif WEBDRIVER_MANAGER_AVAILABLE:
                print("⚠️ Using latest Chrome WebDriver (may not support --load-extension)")
                service = Service(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            else:
                print("⚠️ Using system Chrome WebDriver (version unknown)")
                self.driver = webdriver.Chrome(options=chrome_options)
            # Verify Chrome WebDriver version supports --load-extension
            try:
                chrome_version = self.driver.capabilities['browserVersion']
                chrome_major_version = int(chrome_version.split('.')[0])
                print(f"📋 Chrome browser version: {chrome_version}")

                if chrome_major_version >= 136:
                    print("⚠️ WARNING: Chrome version >= 136 may not support --load-extension flag")
                    print("⚠️ Consider using Chrome version < 136 for extension testing")
                else:
                    print(f"✅ Chrome version {chrome_version} supports --load-extension flag")
            except Exception as e:
                print(f"⚠️ Could not determine Chrome version: {e}")

            self.driver.get("chrome://extensions/")

            self.wait = WebDriverWait(self.driver, 15)

            # Check for any immediate console errors related to extension loading
            time.sleep(5)  # Give Chrome more time to load extensions
            try:
                logs = self.driver.get_log('browser')
                if logs:
                    print(f"📝 Found {len(logs)} browser console logs")
                    extension_errors = [log for log in logs if 'extension' in log['message'].lower() or 'manifest' in log['message'].lower() or 'load' in log['message'].lower()]
                    if extension_errors:
                        print("⚠️ Extension/loading related logs found:")
                        for error in extension_errors[:5]:  # Show first 5 errors
                            print(f"   {error['level']}: {error['message'][:150]}...")
                    else:
                        print("📝 No extension-specific errors in console logs")
                        # Show first few logs for debugging
                        for log in logs[:3]:
                            print(f"   {log['level']}: {log['message'][:100]}...")
                else:
                    print("📝 No browser console logs found")
            except Exception as e:
                print(f"⚠️ Could not check extension loading logs: {e}")

            print("✅ Chrome driver set up successfully")
            return True

        except Exception as e:
            print(f"❌ Failed to set up Chrome driver: {e}")
            return False

    def create_extension_zip(self):
        """Create a ZIP file of the extension for loading into Chrome

        HARD REQUIREMENT: chrome_options.add_extension() requires a ZIP file, not a directory
        """
        import zipfile
        import os

        try:
            # Create ZIP file path
            zip_path = self.extension_path.parent / f"{self.extension_path.name}.zip"

            print(f"🔧 Creating extension ZIP file: {zip_path}")

            # Create zipped extension
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zf:
                # Walk through all files in the extension directory
                for root, dirs, files in os.walk(self.extension_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        # Calculate relative path for the ZIP archive
                        arcname = os.path.relpath(file_path, self.extension_path)
                        zf.write(file_path, arcname)

            print(f"✅ Extension ZIP created successfully: {zip_path}")
            print(f"📦 ZIP file size: {zip_path.stat().st_size} bytes")

            return str(zip_path)

        except Exception as e:
            print(f"❌ Failed to create extension ZIP: {e}")
            return None

    def get_extension_id(self):
        """Get the extension ID from the extensions page"""
        try:
            # Navigate to extensions page
            self.driver.get("chrome://extensions/")
            time.sleep(3)

            # HARD REQUIREMENT: Enable developer mode - try multiple selectors for different Chrome versions
            developer_mode_enabled = False
            dev_mode_selectors = [
                "#devMode",  # Chrome 88+
                "extensions-toggle-row[label='Developer mode'] cr-toggle",  # Chrome 90+
                "cr-toggle[aria-label='Developer mode']",  # Chrome 92+
                "[aria-label='Developer mode']",  # Generic
                "extensions-toggle-row cr-toggle"  # Fallback
            ]

            for selector in dev_mode_selectors:
                try:
                    dev_mode_toggle = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if dev_mode_toggle:
                        # Check if it's already enabled
                        is_enabled = dev_mode_toggle.get_attribute("checked") == "true" or \
                                   dev_mode_toggle.get_attribute("aria-pressed") == "true" or \
                                   "checked" in dev_mode_toggle.get_attribute("class") or ""

                        if not is_enabled:
                            dev_mode_toggle.click()
                            time.sleep(3)  # Wait for UI to update
                            print(f"✅ Developer mode enabled via selector: {selector}")
                        else:
                            print(f"✅ Developer mode already enabled via selector: {selector}")

                        developer_mode_enabled = True
                        break
                except Exception as e:
                    continue

            if not developer_mode_enabled:
                print("⚠️ Could not enable developer mode - trying JavaScript approach")
                try:
                    # Try to enable via JavaScript
                    self.driver.execute_script("""
                        const toggles = document.querySelectorAll('cr-toggle, input[type="checkbox"]');
                        for (let toggle of toggles) {
                            const label = toggle.getAttribute('aria-label') || toggle.parentElement.textContent || '';
                            if (label.toLowerCase().includes('developer')) {
                                if (!toggle.checked) {
                                    toggle.click();
                                    console.log('Developer mode enabled via JavaScript');
                                }
                                return true;
                            }
                        }
                        return false;
                    """)
                    print("✅ Developer mode enabled via JavaScript")
                    developer_mode_enabled = True
                except Exception as e:
                    print(f"❌ HARD REQUIREMENT FAILED: Could not enable developer mode: {e}")

            # Wait for extensions to load after enabling developer mode
            if developer_mode_enabled:
                time.sleep(5)

            # Look for MVAT extension in multiple ways
            page_source = self.driver.page_source

            # Method 1: Look for extension cards
            try:
                extension_cards = self.driver.find_elements(By.CSS_SELECTOR, "extensions-item")
                print(f"📝 Found {len(extension_cards)} extension cards")

                for card in extension_cards:
                    try:
                        name_element = card.find_element(By.CSS_SELECTOR, "#name")
                        name = name_element.text.lower()
                        if "mvat" in name or "multi-vat" in name:
                            # Try to get the extension ID from the card
                            card_html = card.get_attribute('outerHTML')
                            import re
                            id_match = re.search(r'id="([a-z]{32})"', card_html)
                            if id_match:
                                self.extension_id = id_match.group(1)
                                print(f"✅ Extension ID found from card: {self.extension_id}")
                                return self.extension_id
                    except:
                        continue
            except Exception as e:
                print(f"⚠️ Could not find extension cards: {e}")

            # Method 2: Use chrome.management API to get all extensions
            try:
                print("🔍 Method 2: Using chrome.management API...")
                extensions_info = self.driver.execute_script("""
                    return new Promise((resolve) => {
                        if (chrome && chrome.management) {
                            chrome.management.getAll((extensions) => {
                                const extensionList = extensions.map(ext => ({
                                    id: ext.id,
                                    name: ext.name,
                                    enabled: ext.enabled,
                                    type: ext.type,
                                    installType: ext.installType,
                                    version: ext.version
                                }));
                                resolve(extensionList);
                            });
                        } else {
                            resolve([]);
                        }
                    });
                """)

                if extensions_info and len(extensions_info) > 0:
                    print(f"📝 Found {len(extensions_info)} total extensions via management API")
                    for ext in extensions_info:
                        print(f"   - {ext['name']} (ID: {ext['id']}, Type: {ext.get('type', 'unknown')}, Install: {ext.get('installType', 'unknown')})")
                        # Look for our extension by characteristics
                        if (ext.get('installType') == 'development' or
                            'mvat' in ext['name'].lower() or
                            'chrome extension' in ext['name'].lower()):
                            self.extension_id = ext['id']
                            print(f"✅ MVAT extension found via management API: {ext['name']} (ID: {self.extension_id})")
                            return self.extension_id
                else:
                    print("⚠️ No extensions found via management API")
            except Exception as e:
                print(f"⚠️ Management API check failed: {e}")

            # Method 3: Look for extension ID patterns in page source
            import re
            extension_id_patterns = [
                r'chrome-extension://([a-z]{32})',
                r'"id":"([a-z]{32})"',
                r'id="([a-z]{32})"'
            ]

            for pattern in extension_id_patterns:
                matches = re.findall(pattern, page_source.lower())
                if matches:
                    self.extension_id = matches[0]
                    print(f"✅ Extension ID found via pattern: {self.extension_id}")
                    return self.extension_id

            print("⚠️ Extension ID not found in extensions page")
            print(f"📝 Page source length: {len(page_source)} characters")

            # Debug: Check if MVAT is mentioned at all
            if "mvat" in page_source.lower():
                print("✅ MVAT extension name found in page source")
            else:
                print("❌ MVAT extension name NOT found in page source")

                # HARD REQUIREMENT: Try to manually load the extension via JavaScript
                print("🔄 HARD REQUIREMENT: Attempting to manually load extension via JavaScript...")
                try:
                    # Try to inject the extension loading via JavaScript
                    load_result = self.driver.execute_script(f"""
                        // Try to load extension programmatically
                        if (typeof chrome !== 'undefined' && chrome.management) {{
                            try {{
                                // This won't work in normal pages, but worth trying
                                chrome.management.setEnabled('{self.extension_path}', true);
                                return 'management_api_attempted';
                            }} catch (e) {{
                                return 'management_api_failed: ' + e.message;
                            }}
                        }}

                        // Try to find and click load unpacked button
                        const loadButtons = document.querySelectorAll('button, cr-button, [role="button"]');
                        for (let button of loadButtons) {{
                            const text = button.textContent || button.innerText || '';
                            if (text.toLowerCase().includes('load unpacked') ||
                                text.toLowerCase().includes('load') && text.toLowerCase().includes('unpacked')) {{
                                button.click();
                                return 'load_unpacked_clicked';
                            }}
                        }}

                        return 'no_load_button_found';
                    """)
                    print(f"📝 Extension loading attempt result: {load_result}")

                    # Wait for potential extension loading
                    time.sleep(3)

                    # Refresh the page to see if extension appeared
                    self.driver.refresh()
                    time.sleep(2)

                except Exception as e:
                    print(f"⚠️ JavaScript extension loading failed: {e}")

            # HARD REQUIREMENT: Since extension is loaded via ZIP, try to find it in extensions list
            print("🔄 HARD REQUIREMENT: Searching for loaded extension in extensions list...")

            # Method 1: Look for extension cards with better selectors
            try:
                # Wait a bit more for extensions to load
                time.sleep(3)
                self.driver.refresh()
                time.sleep(2)

                # Try different selectors for extension cards
                extension_selectors = [
                    "extensions-item",
                    ".extension-item",
                    "[id*='extension']",
                    "extensions-detail-view",
                    "cr-card"
                ]

                for selector in extension_selectors:
                    try:
                        extension_cards = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        if extension_cards:
                            print(f"📝 Found {len(extension_cards)} extension cards with selector: {selector}")

                            for card in extension_cards:
                                try:
                                    card_html = card.get_attribute('outerHTML')
                                    if 'mvat' in card_html.lower() or 'multi-vat' in card_html.lower():
                                        # Try to extract extension ID from the card
                                        import re
                                        id_patterns = [
                                            r'id="([a-z]{32})"',
                                            r'extension-id="([a-z]{32})"',
                                            r'data-extension-id="([a-z]{32})"',
                                            r'chrome-extension://([a-z]{32})',
                                        ]

                                        for pattern in id_patterns:
                                            matches = re.findall(pattern, card_html)
                                            if matches:
                                                extension_id = matches[0]
                                                print(f"✅ Extension ID found from card: {extension_id}")
                                                self.extension_id = extension_id
                                                return extension_id
                                except Exception as e:
                                    continue
                    except Exception as e:
                        continue

            except Exception as e:
                print(f"⚠️ Extension card search failed: {e}")

            # Method 2: Try to access the extension directly by guessing common extension IDs
            print("🔄 HARD REQUIREMENT: Attempting to access extension directly...")
            try:
                # Since the extension is loaded via --load-extension, try to access it directly
                # First, try to navigate to the extension popup and see if we can get the ID from there

                # Try to access the extension popup directly using a common pattern
                # Extensions loaded via --load-extension often get predictable IDs
                test_urls = [
                    f"chrome-extension://*/popup.html",  # Wildcard approach
                ]

                # Try to get all extension IDs from chrome.management API in a new tab
                original_window = self.driver.current_window_handle
                self.driver.execute_script("window.open('about:blank');")
                self.driver.switch_to.window(self.driver.window_handles[-1])

                # Try to get all extensions via management API
                extensions_info = self.driver.execute_script("""
                    return new Promise((resolve) => {
                        if (chrome && chrome.management) {
                            chrome.management.getAll((extensions) => {
                                resolve(extensions.map(ext => ({
                                    id: ext.id,
                                    name: ext.name,
                                    enabled: ext.enabled,
                                    type: ext.type,
                                    installType: ext.installType
                                })));
                            });
                        } else {
                            resolve([]);
                        }
                    });
                """)

                if extensions_info and len(extensions_info) > 0:
                    print(f"📝 Found {len(extensions_info)} extensions via management API in new tab")
                    for ext in extensions_info:
                        print(f"   - {ext['name']} (ID: {ext['id']}, Type: {ext.get('type', 'unknown')}, Install: {ext.get('installType', 'unknown')})")
                        # Look for development extensions or MVAT
                        if (ext.get('installType') == 'development' or
                            'mvat' in ext['name'].lower() or
                            ext.get('type') == 'extension'):
                            extension_id = ext['id']
                            print(f"✅ Extension found via management API: {ext['name']} (ID: {extension_id})")
                            self.extension_id = extension_id
                            self.driver.close()
                            self.driver.switch_to.window(original_window)
                            return extension_id

                self.driver.close()
                self.driver.switch_to.window(original_window)

            except Exception as e:
                print(f"⚠️ Direct extension access failed: {e}")
                # Make sure we're back to the original window
                try:
                    self.driver.switch_to.window(original_window)
                except:
                    pass

            # Method 3: Try to find extension by accessing the popup directly with a generated ID
            print("🔄 HARD REQUIREMENT: Attempting to find extension by testing popup access...")
            try:
                # Since we loaded the extension via --load-extension, try to find it by testing popup access
                # Navigate back to chrome://extensions/ and look for any extension
                self.driver.get("chrome://extensions/")
                time.sleep(3)

                # Try to find ANY extension ID in the page source and test if it's ours
                page_source = self.driver.page_source
                import re

                # Look for any extension IDs in the page
                all_extension_ids = re.findall(r'[a-z]{32}', page_source)
                unique_ids = list(set(all_extension_ids))

                print(f"📝 Found {len(unique_ids)} potential extension IDs in page source")

                for potential_id in unique_ids:
                    try:
                        # Test if this ID corresponds to our extension
                        test_url = f"chrome-extension://{potential_id}/popup.html"
                        print(f"🔍 Testing extension ID: {potential_id}")

                        # Try to navigate to the popup
                        self.driver.get(test_url)
                        time.sleep(2)

                        # Check if we can access chrome.runtime and if it matches
                        runtime_id = self.driver.execute_script("""
                            if (chrome && chrome.runtime && chrome.runtime.id) {
                                return chrome.runtime.id;
                            }
                            return null;
                        """)

                        if runtime_id == potential_id:
                            # Check if this looks like our extension
                            page_title = self.driver.title
                            page_source = self.driver.page_source

                            # Look for MVAT-specific content
                            if ('mvat' in page_source.lower() or
                                'multi-vat' in page_source.lower() or
                                'root' in page_source or  # React root element
                                len(page_source) > 1000):  # Substantial content

                                print(f"✅ Extension found! ID: {potential_id}")
                                print(f"   Title: {page_title}")
                                print(f"   Content length: {len(page_source)} characters")
                                self.extension_id = potential_id
                                return potential_id

                    except Exception as e:
                        # This ID doesn't work, try the next one
                        continue

            except Exception as e:
                print(f"⚠️ Extension ID testing failed: {e}")

            print("❌ Extension ID not found - extension may not have loaded properly")
            return None

        except Exception as e:
            print(f"❌ Failed to get extension ID: {e}")
            return None

    def capture_screenshot(self, test_name, description=""):
        """Capture screenshot with timestamp"""
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{test_name}_{timestamp}.png"
        filepath = self.screenshots_dir / filename

        try:
            self.driver.save_screenshot(str(filepath))
            print(f"📸 Screenshot saved: {filename}")
            if description:
                print(f"   📝 {description}")
            return str(filepath)
        except Exception as e:
            print(f"❌ Failed to capture screenshot: {e}")
            return None

    def test_extension_loading(self):
        """Test that Chrome extension loads properly as a real Chrome extension

        HARD REQUIREMENTS (NO EXCEPTIONS):
        - Must enable developer mode via WebDriver options ✅ IMPLEMENTED
        - Must verify extension ID and proper loading in chrome://extensions/ ✅ REQUIRED
        - Must ONLY test extension popup via chrome-extension:// URL ✅ REQUIRED
        - Must verify Chrome extension context (chrome.runtime available) ✅ REQUIRED

        FORBIDDEN: Testing via file:// URLs is strictly forbidden
        REQUIRED: Must use chrome-extension://[extension-id]/popup.html URLs only
        """
        print("\n🚀 Test 1: Extension Loading")

        try:
            # HARD REQUIREMENT: Get the extension ID - this MUST succeed
            extension_id = self.get_extension_id()

            # Capture screenshot of extensions page
            self.capture_screenshot("extension_loading", "Extensions page loaded")

            # HARD REQUIREMENT: Extension must be loaded and accessible
            # Note: Extensions loaded via --load-extension may not always appear visually in chrome://extensions/
            # but are still properly loaded and accessible via chrome.management API
            page_source = self.driver.page_source.lower()
            if "mvat" in page_source or "multi-vat" in page_source:
                print("✅ MVAT extension detected in extensions page")
                extension_found = True
            elif extension_id:
                # If we found the extension ID via management API, it's properly loaded
                print("✅ MVAT extension loaded via --load-extension flag (verified by management API)")
                extension_found = True
            else:
                print("❌ MVAT extension NOT found - HARD REQUIREMENT FAILED")
                extension_found = False

            # HARD REQUIREMENT: Must ONLY use chrome-extension:// URL - NO FALLBACKS
            if extension_id:
                popup_url = f"chrome-extension://{extension_id}/popup.html"
                print(f"✅ HARD REQUIREMENT MET: Testing popup via chrome-extension:// URL: {popup_url}")
            else:
                print("❌ HARD REQUIREMENT FAILED: Extension ID not found - cannot use chrome-extension:// URL")
                print("❌ FORBIDDEN: file:// URL testing is not allowed")
                print("❌ Test 1 FAILED: Extension not loaded as proper Chrome extension")
                self.test_results.append(("Extension Loading", False, "Extension ID not found - chrome-extension:// URL required"))
                return False

            self.driver.get(popup_url)
            time.sleep(8)  # Give more time for extension context to initialize

            # Check if JavaScript is working by looking for any script execution
            try:
                # Try to execute a simple JavaScript command
                result = self.driver.execute_script("return document.readyState;")
                print(f"📝 Document ready state: {result}")

                # Check if main.js loaded
                scripts = self.driver.execute_script("return document.scripts.length;")
                print(f"📝 Number of scripts loaded: {scripts}")

                # HARD REQUIREMENT: Must be in Chrome extension context with chrome.runtime available
                chrome_context = self.driver.execute_script("""
                    return {
                        chromeAvailable: typeof chrome !== 'undefined',
                        chromeRuntime: typeof chrome !== 'undefined' && typeof chrome.runtime !== 'undefined',
                        extensionId: typeof chrome !== 'undefined' && chrome.runtime ? chrome.runtime.id : null,
                        isExtensionContext: !!(typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id)
                    };
                """)
                print(f"📝 Chrome extension context: {chrome_context}")

                # HARD REQUIREMENT: Must verify Chrome extension context - NO EXCEPTIONS
                if not chrome_context.get('isExtensionContext'):
                    print("❌ HARD REQUIREMENT FAILED: Not running in Chrome extension context")
                    print("❌ chrome.runtime not available - extension not properly loaded")
                    print("❌ Test 1 FAILED: Chrome extension context required")
                    self.test_results.append(("Extension Loading", False, "Not running in Chrome extension context"))
                    return False
                else:
                    print("✅ HARD REQUIREMENT MET: Running in Chrome extension context")
                    print(f"✅ Extension ID from runtime: {chrome_context.get('extensionId')}")

                # Check if MVAT JavaScript functions are available
                mvat_functions = self.driver.execute_script("""
                    return {
                        initializeMVAT: typeof initializeMVAT !== 'undefined',
                        createMainApp: typeof createMainApp !== 'undefined',
                        testStorage: typeof testStorage !== 'undefined'
                    };
                """)
                print(f"📝 MVAT functions available: {mvat_functions}")

                # Check if DOM content loaded event fired
                dom_loaded = self.driver.execute_script("return document.readyState === 'complete';")
                print(f"📝 DOM loaded: {dom_loaded}")

            except Exception as e:
                print(f"⚠️ JavaScript execution test failed: {e}")

            # Capture popup screenshot
            self.capture_screenshot("popup_loaded", "Popup page accessed")

            # Check if popup loaded successfully
            title = self.driver.title
            print(f"📄 Popup title: {title}")

            # Wait for React app to load - look for root element first
            root_element_found = False
            try:
                root_element = self.wait.until(
                    EC.presence_of_element_located((By.ID, "root"))
                )
                print("✅ Root element found")
                root_element_found = True

                # Wait a bit more for React to render
                time.sleep(8)

                # Check if app loaded successfully (no loading spinner)
                page_source = self.driver.page_source
                if "Loading MVAT..." in page_source:
                    print("⚠️ App still loading...")
                    time.sleep(3)
                    # Get updated page source
                    page_source = self.driver.page_source

                # Debug: Print some page source info
                print(f"📝 Page source length: {len(page_source)} characters")
                if "error" in page_source.lower():
                    print("⚠️ Error detected in page source")
                if "nav-tab" in page_source:
                    print("✅ Navigation tabs found in source")
                else:
                    print("❌ Navigation tabs NOT found in source")

                print("✅ React app appears to be loaded")

            except TimeoutException:
                print("❌ Root element not found")
                root_element_found = False

            # HARD REQUIREMENT: All requirements must be met - NO EXCEPTIONS
            if extension_found and extension_id and root_element_found:
                print("✅ Test 1 PASSED: All hard requirements met")
                print("  ✅ Extension loaded in chrome://extensions/")
                print("  ✅ Extension ID verified")
                print("  ✅ chrome-extension:// URL working")
                print("  ✅ Chrome extension context active")
                print("  ✅ React app functional")
                self.test_results.append(("Extension Loading", True, "Extension loaded as proper Chrome extension"))
                return True
            else:
                print("❌ Test 1 FAILED: Hard requirements not met")
                if not extension_found:
                    print("  ❌ Extension not found in chrome://extensions/")
                if not extension_id:
                    print("  ❌ Extension ID not verified")
                if not root_element_found:
                    print("  ❌ Extension UI not functional")
                self.test_results.append(("Extension Loading", False, "Hard requirements not met"))
                return False

        except Exception as e:
            print(f"❌ Test 1 FAILED: {e}")
            self.test_results.append(("Extension Loading", False, str(e)))
            return False

    def test_ui_state_verification(self):
        """Test comprehensive UI state verification"""
        print("\n🎯 Test 2: UI State Verification")

        try:
            # HARD REQUIREMENT: Must ONLY use chrome-extension:// URL - NO EXCEPTIONS
            if self.extension_id:
                popup_url = f"chrome-extension://{self.extension_id}/popup.html"
                print(f"✅ HARD REQUIREMENT: Using chrome-extension:// URL: {popup_url}")
            else:
                print("❌ HARD REQUIREMENT FAILED: Extension ID not available")
                print("❌ FORBIDDEN: file:// URL testing is not allowed")
                print("❌ Test 2 FAILED: chrome-extension:// URL required")
                self.test_results.append(("UI State Verification", False, "Extension ID not available - chrome-extension:// URL required"))
                return False

            self.driver.get(popup_url)
            time.sleep(8)  # Give more time for React to load and initialize

            # Debug: Check what's actually in the page
            page_source = self.driver.page_source
            print(f"📝 Current page source length: {len(page_source)} characters")

            # Look for key indicators
            if "Loading MVAT..." in page_source:
                print("⚠️ App still in loading state")
                time.sleep(5)  # Wait more
                page_source = self.driver.page_source

            if "Initialization Error" in page_source:
                print("❌ App initialization error detected")
            elif "popup-container" in page_source:
                print("✅ Popup container found in source")
            else:
                print("❌ Popup container NOT found in source")

            # Test critical UI elements based on actual React app structure
            critical_elements = [
                # Look for main app container (React app uses popup-container)
                (".popup-container", "Popup Container", True, "css"),
                # Look for MVAT app container
                (".mvat-app", "MVAT App Container", True, "css"),
                # Look for header
                (".mvat-header", "MVAT Header", True, "css"),
                # Look for navigation tabs
                (".nav-tab", "Navigation Tabs", True, "css"),
                # Look for main content area
                (".popup-content", "Main Content Area", True, "css"),
                # Look for footer
                (".popup-footer", "Footer", True, "css"),
                # Look for any buttons (React app has navigation buttons)
                ("button", "Interactive Buttons", False, "tag"),
            ]

            visible_count = 0
            total_critical = sum(1 for _, _, required, _ in critical_elements if required)

            for selector, element_name, required, selector_type in critical_elements:
                try:
                    # Choose the right selector method
                    if selector_type == "css":
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    elif selector_type == "tag":
                        elements = self.driver.find_elements(By.TAG_NAME, selector)
                    else:  # default to ID
                        elements = self.driver.find_elements(By.ID, selector)

                    if elements:
                        # Check if at least one element is visible
                        visible_elements = [el for el in elements if el.is_displayed()]
                        if visible_elements:
                            print(f"✅ {element_name} is visible ({len(visible_elements)} found)")
                            if required:
                                visible_count += 1
                        else:
                            status = "⚠️" if not required else "❌"
                            print(f"{status} {element_name} exists but not visible ({len(elements)} found)")
                    else:
                        status = "⚠️" if not required else "❌"
                        print(f"{status} {element_name} not found")

                except Exception as e:
                    status = "⚠️" if not required else "❌"
                    print(f"{status} {element_name} error: {str(e)[:50]}...")

            # Capture UI state screenshot
            self.capture_screenshot("ui_state", f"UI elements verification - {visible_count}/{total_critical} visible")

            success_rate = (visible_count / total_critical) * 100 if total_critical > 0 else 0
            print(f"📊 Critical UI Elements: {visible_count}/{total_critical} ({success_rate:.1f}%)")

            if success_rate >= 80:
                print("✅ Test 2 PASSED: UI state verification successful")
                self.test_results.append(("UI State Verification", True, f"{success_rate:.1f}% elements visible"))
                return True
            else:
                print("❌ Test 2 FAILED: Too many critical UI elements missing")
                self.test_results.append(("UI State Verification", False, f"Only {success_rate:.1f}% elements visible"))
                return False

        except Exception as e:
            print(f"❌ Test 2 FAILED: {e}")
            self.test_results.append(("UI State Verification", False, str(e)))
            return False

    def test_functionality_verification(self):
        """Test basic functionality verification"""
        print("\n⚙️ Test 3: Functionality Verification")

        try:
            # Test button functionality - look for navigation buttons in React app
            test_buttons = [
                (".nav-tab", "Navigation Tab"),
                ("button", "Any Button")
            ]
            button_results = []

            for button_selector, button_name in test_buttons:
                try:
                    button_elements = self.driver.find_elements(By.CSS_SELECTOR, button_selector)

                    if button_elements:
                        button = button_elements[0]

                        # Check if button is visible and enabled
                        is_visible = button.is_displayed()
                        is_enabled = button.is_enabled()

                        if is_visible and is_enabled:
                            # Click the button
                            actions = ActionChains(self.driver)
                            actions.move_to_element(button).click().perform()
                            time.sleep(2)  # Wait for action to complete

                            print(f"✅ Button '{button_name}' clicked successfully")
                            button_results.append(True)
                        else:
                            print(f"⚠️ Button '{button_name}' found but not interactive (visible: {is_visible}, enabled: {is_enabled})")
                            button_results.append(False)
                    else:
                        print(f"❌ Button '{button_name}' not found")
                        button_results.append(False)

                except Exception as e:
                    print(f"❌ Button '{button_name}' interaction failed: {str(e)[:100]}...")
                    button_results.append(False)

            # Capture functionality test screenshot
            self.capture_screenshot("functionality_test", "Button interaction testing completed")

            # Test additional UI interactions
            try:
                # Look for main content area to verify React app is working
                content_area = self.driver.find_elements(By.CSS_SELECTOR, ".popup-content")

                if content_area:
                    content_text = content_area[0].text
                    if content_text and len(content_text) > 10:
                        print("✅ Main content area shows activity")
                    else:
                        print(f"⚠️ Main content area found but minimal content: {content_text[:50]}...")
                else:
                    print("⚠️ Main content area not found")

            except Exception as e:
                print(f"⚠️ Content area check failed: {str(e)[:100]}...")

            success_count = sum(button_results)
            total_tests = len(button_results)

            if success_count >= total_tests * 0.8:
                print("✅ Test 3 PASSED: Functionality verification successful")
                self.test_results.append(("Functionality Verification", True, f"{success_count}/{total_tests} interactions working"))
                return True
            else:
                print("❌ Test 3 FAILED: Multiple functionality issues detected")
                self.test_results.append(("Functionality Verification", False, f"Only {success_count}/{total_tests} interactions working"))
                return False

        except Exception as e:
            print(f"❌ Test 3 FAILED: {e}")
            self.test_results.append(("Functionality Verification", False, str(e)))
            return False

    def test_console_errors(self):
        """Test for JavaScript console errors"""
        print("\n🐛 Test 4: Console Error Check")

        try:
            # Get browser console logs
            logs = self.driver.get_log('browser')

            # Filter for different log levels
            error_logs = [log for log in logs if log['level'] in ['SEVERE', 'ERROR']]
            warning_logs = [log for log in logs if log['level'] == 'WARNING']
            info_logs = [log for log in logs if log['level'] == 'INFO']

            print(f"📊 Console logs: {len(error_logs)} errors, {len(warning_logs)} warnings, {len(info_logs)} info")

            # Show all logs for debugging
            if logs:
                print("📝 All console logs:")
                for i, log in enumerate(logs[:10]):  # Show first 10 logs
                    level = log['level']
                    message = log['message'][:150] + "..." if len(log['message']) > 150 else log['message']
                    print(f"   {i+1}. [{level}] {message}")

            if error_logs:
                print("⚠️ Console errors found:")
                for i, log in enumerate(error_logs[:5]):  # Show first 5 errors
                    print(f"   ERROR {i+1}: {log['message']}")

                # Capture screenshot with errors
                self.capture_screenshot("console_errors", f"{len(error_logs)} console errors detected")

                if len(error_logs) > 5:
                    print("❌ Test 4 FAILED: Too many console errors")
                    self.test_results.append(("Console Error Check", False, f"{len(error_logs)} errors found"))
                    return False
                else:
                    print("⚠️ Test 4 WARNING: Some console errors found but within tolerance")
                    self.test_results.append(("Console Error Check", True, f"{len(error_logs)} errors (within tolerance)"))
                    return True
            else:
                print("✅ No severe console errors detected")
                self.capture_screenshot("no_console_errors", "Clean console - no errors")
                self.test_results.append(("Console Error Check", True, "No console errors"))
                return True

        except Exception as e:
            print(f"❌ Test 4 FAILED: {e}")
            self.test_results.append(("Console Error Check", False, str(e)))
            return False

    def generate_test_report(self):
        """Generate comprehensive test report"""
        print("\n📊 GENERATING TEST REPORT")
        print("=" * 60)

        passed_tests = sum(1 for _, passed, _ in self.test_results if passed)
        total_tests = len(self.test_results)
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0

        print(f"📈 Overall Success Rate: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
        print("\n📋 Detailed Results:")

        for test_name, passed, details in self.test_results:
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f"   {status} {test_name}: {details}")

        print(f"\n📸 Screenshots saved in: {self.screenshots_dir}")

        # Generate JSON report
        report_data = {
            "timestamp": datetime.datetime.now().isoformat(),
            "success_rate": success_rate,
            "passed_tests": passed_tests,
            "total_tests": total_tests,
            "results": [
                {
                    "test_name": name,
                    "passed": passed,
                    "details": details
                }
                for name, passed, details in self.test_results
            ]
        }

        report_file = self.screenshots_dir / "test_report.json"
        with open(report_file, 'w') as f:
            json.dump(report_data, f, indent=2)

        print(f"📄 JSON report saved: {report_file}")

        return success_rate >= 75  # 75% success rate required

    def run_all_tests(self):
        """Run all extension state tests"""
        print("🧪 CHROME EXTENSION STATE VERIFICATION")
        print("=" * 60)
        print("🎯 Purpose: Verify extension state before development work")
        print("📅 Timestamp:", datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        print("=" * 60)

        if not self.setup_driver():
            return False

        tests = [
            self.test_extension_loading,
            self.test_ui_state_verification,
            self.test_functionality_verification,
            self.test_console_errors,
        ]

        for test in tests:
            try:
                test()
                time.sleep(1)  # Brief pause between tests
            except Exception as e:
                print(f"❌ Test failed with exception: {e}")
                self.test_results.append((test.__name__, False, str(e)))

        return self.generate_test_report()

    def cleanup(self):
        """Clean up resources"""
        if self.driver:
            self.driver.quit()
            print("🧹 Chrome driver cleaned up")

if __name__ == "__main__":
    tester = ExtensionStateTests()
    try:
        success = tester.run_all_tests()
        exit_code = 0 if success else 1
    except KeyboardInterrupt:
        print("\n⚠️ Tests interrupted by user")
        exit_code = 2
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        exit_code = 3
    finally:
        tester.cleanup()

    exit(exit_code)
