#!/usr/bin/env python3
"""
Analytics Dashboard Test
Tests the new analytics dashboard functionality in the Chrome extension.
"""

import os
import sys
import time
import json
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Add the parent directory to the path to import shared utilities
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def setup_chrome_driver():
    """Set up Chrome driver with extension loaded"""
    print("🔧 Setting up Chrome driver for analytics dashboard testing...")
    
    # Extension path
    extension_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../dist/dev'))
    print(f"✅ Loading extension from: {extension_path}")
    
    # Chrome options
    chrome_options = Options()
    chrome_options.add_argument(f"--load-extension={extension_path}")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1200,800")
    
    # Use local Chrome binary if available
    chrome_binary_path = os.path.join(os.path.dirname(__file__), 'chrome-135/chrome-linux64/chrome')
    if os.path.exists(chrome_binary_path):
        chrome_options.binary_location = chrome_binary_path
        print(f"✅ Using Chrome binary: {chrome_binary_path}")
    
    try:
        driver = webdriver.Chrome(options=chrome_options)
        print("✅ Chrome driver set up successfully")
        return driver
    except Exception as e:
        print(f"❌ Failed to set up Chrome driver: {e}")
        return None

def get_extension_id(driver):
    """Get the extension ID from chrome://extensions"""
    try:
        driver.get("chrome://extensions/")
        time.sleep(2)
        
        # Enable developer mode
        try:
            dev_mode_toggle = driver.find_element(By.CSS_SELECTOR, "#devMode")
            if not dev_mode_toggle.is_selected():
                dev_mode_toggle.click()
                time.sleep(1)
        except:
            pass
        
        # Get extension ID via JavaScript
        extension_id = driver.execute_script("""
            return new Promise((resolve) => {
                chrome.management.getAll((extensions) => {
                    const mvatExt = extensions.find(ext => 
                        ext.name.includes('MVAT') || 
                        ext.name.includes('Multi-VAT')
                    );
                    resolve(mvatExt ? mvatExt.id : null);
                });
            });
        """)
        
        if extension_id:
            print(f"✅ Extension ID found: {extension_id}")
            return extension_id
        else:
            print("❌ Extension ID not found")
            return None
            
    except Exception as e:
        print(f"❌ Error getting extension ID: {e}")
        return None

def test_analytics_dashboard(driver, extension_id):
    """Test the analytics dashboard functionality"""
    print("\n🎯 Testing Analytics Dashboard")
    print("=" * 50)
    
    try:
        # Navigate to extension popup
        popup_url = f"chrome-extension://{extension_id}/popup.html"
        print(f"📱 Navigating to popup: {popup_url}")
        driver.get(popup_url)
        time.sleep(3)
        
        # Wait for React app to load
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "[data-testid='mvat-app-container']"))
        )
        print("✅ Extension popup loaded successfully")
        
        # Look for Analytics navigation tab
        analytics_tab = None
        try:
            # Try to find analytics tab by text
            analytics_tab = driver.find_element(By.XPATH, "//a[contains(text(), 'Analytics') or contains(@href, '/analytics')]")
            print("✅ Analytics tab found in navigation")
        except NoSuchElementException:
            # Try to find by data-testid or other attributes
            try:
                analytics_tab = driver.find_element(By.CSS_SELECTOR, "[data-testid='analytics-tab'], [href*='analytics']")
                print("✅ Analytics tab found via CSS selector")
            except NoSuchElementException:
                print("❌ Analytics tab not found in navigation")
                return False
        
        # Click on Analytics tab
        if analytics_tab:
            print("🖱️ Clicking Analytics tab...")
            analytics_tab.click()
            time.sleep(3)
            
            # Check if we're on the analytics page
            current_url = driver.current_url
            if 'analytics' in current_url or driver.find_elements(By.CSS_SELECTOR, "[data-testid='analytics-page']"):
                print("✅ Successfully navigated to Analytics page")
                
                # Test analytics page elements
                return test_analytics_page_elements(driver)
            else:
                print("❌ Failed to navigate to Analytics page")
                return False
        
        return False
        
    except Exception as e:
        print(f"❌ Error testing analytics dashboard: {e}")
        return False

def test_analytics_page_elements(driver):
    """Test specific elements on the analytics page"""
    print("\n📊 Testing Analytics Page Elements")
    print("-" * 30)
    
    elements_to_check = [
        ("Analytics Page Container", "[data-testid='analytics-page']"),
        ("Metrics Cards", "[data-testid='metrics-cards']"),
        ("Analytics Charts", "[data-testid='analytics-charts']"),
        ("Detailed Metrics", "[data-testid='detailed-metrics']"),
        ("Refresh Button", "[data-testid='refresh-analytics']"),
        ("Export Button", "[data-testid='export-analytics']")
    ]
    
    found_elements = 0
    total_elements = len(elements_to_check)
    
    for element_name, selector in elements_to_check:
        try:
            element = driver.find_element(By.CSS_SELECTOR, selector)
            if element.is_displayed():
                print(f"✅ {element_name} is visible")
                found_elements += 1
            else:
                print(f"⚠️ {element_name} found but not visible")
        except NoSuchElementException:
            print(f"❌ {element_name} not found")
    
    # Test refresh functionality
    try:
        refresh_button = driver.find_element(By.CSS_SELECTOR, "[data-testid='refresh-analytics']")
        print("🔄 Testing refresh functionality...")
        refresh_button.click()
        time.sleep(2)
        print("✅ Refresh button clicked successfully")
    except Exception as e:
        print(f"⚠️ Could not test refresh functionality: {e}")
    
    # Check for any analytics data or loading states
    try:
        # Look for loading indicators or data
        loading_indicators = driver.find_elements(By.CSS_SELECTOR, ".animate-spin, .loading, [data-testid*='loading']")
        data_elements = driver.find_elements(By.CSS_SELECTOR, ".chart, .metric, .analytics-data")
        
        if loading_indicators:
            print("🔄 Analytics loading indicators found")
        if data_elements:
            print("📊 Analytics data elements found")
        
    except Exception as e:
        print(f"⚠️ Error checking analytics data: {e}")
    
    success_rate = (found_elements / total_elements) * 100
    print(f"\n📈 Analytics Elements Found: {found_elements}/{total_elements} ({success_rate:.1f}%)")
    
    return found_elements >= (total_elements * 0.5)  # At least 50% of elements should be found

def take_screenshot(driver, filename):
    """Take a screenshot for debugging"""
    try:
        screenshot_dir = os.path.join(os.path.dirname(__file__), 'screenshots')
        os.makedirs(screenshot_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        screenshot_path = os.path.join(screenshot_dir, f"{filename}_{timestamp}.png")
        
        driver.save_screenshot(screenshot_path)
        print(f"📸 Screenshot saved: {os.path.basename(screenshot_path)}")
        return screenshot_path
    except Exception as e:
        print(f"❌ Failed to save screenshot: {e}")
        return None

def main():
    """Main test function"""
    print("🧪 ANALYTICS DASHBOARD TEST")
    print("=" * 60)
    print(f"🎯 Purpose: Test new analytics dashboard functionality")
    print(f"📅 Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    driver = None
    try:
        # Set up Chrome driver
        driver = setup_chrome_driver()
        if not driver:
            print("❌ Failed to set up Chrome driver")
            return False
        
        # Get extension ID
        extension_id = get_extension_id(driver)
        if not extension_id:
            print("❌ Could not get extension ID")
            return False
        
        # Take initial screenshot
        take_screenshot(driver, "analytics_test_start")
        
        # Test analytics dashboard
        success = test_analytics_dashboard(driver, extension_id)
        
        # Take final screenshot
        take_screenshot(driver, "analytics_test_end")
        
        # Print results
        print("\n" + "=" * 60)
        print("📊 ANALYTICS DASHBOARD TEST RESULTS")
        print("=" * 60)
        
        if success:
            print("✅ ANALYTICS DASHBOARD TEST PASSED")
            print("   - Analytics tab found and accessible")
            print("   - Analytics page elements loaded correctly")
            print("   - Basic functionality working")
        else:
            print("❌ ANALYTICS DASHBOARD TEST FAILED")
            print("   - Check screenshots for debugging")
            print("   - Verify analytics implementation")
        
        return success
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        if driver:
            take_screenshot(driver, "analytics_test_error")
        return False
        
    finally:
        if driver:
            driver.quit()
            print("🧹 Chrome driver cleaned up")

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
