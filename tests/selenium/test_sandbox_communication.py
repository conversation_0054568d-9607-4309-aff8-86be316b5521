#!/usr/bin/env python3
"""
Test sandbox communication functionality
Tests the sandbox communication timeout fix by simulating file upload
"""

import time
import os
import tempfile
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def create_test_pdf():
    """Create a simple test PDF file"""
    # Create a simple text file that we can use for testing
    content = """
    INVOICE
    Invoice Number: TEST-001
    Date: 2025-01-01
    
    From: Test Company
    To: Test Customer
    
    Amount: $100.00
    VAT: $20.00
    Total: $120.00
    """
    
    # Create temporary file
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False)
    temp_file.write(content)
    temp_file.close()
    
    return temp_file.name

def test_sandbox_communication():
    """Test sandbox communication by uploading a file"""
    
    # Setup Chrome with extension
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-web-security')
    chrome_options.add_argument('--allow-running-insecure-content')
    chrome_options.add_argument('--allow-file-access-from-files')

    driver = webdriver.Chrome(options=chrome_options)

    try:
        print('🔍 Testing sandbox communication via file upload...')
        driver.get('file:///W/B2B/cloudforge/chrome-extensions/mvat/dist/popup.html')
        
        # Wait for page to load
        time.sleep(3)
        
        print('📋 Page loaded, looking for file input...')
        
        # Find file input
        file_input = driver.find_element(By.CSS_SELECTOR, 'input[type="file"]')
        print(f'✅ Found file input: {file_input.tag_name}')
        
        # Create test file
        test_file = create_test_pdf()
        print(f'📄 Created test file: {test_file}')
        
        # Upload file
        print('📤 Uploading test file...')
        file_input.send_keys(test_file)
        
        # Wait a bit for processing to start
        time.sleep(2)
        
        # Check console logs for sandbox communication
        print('📊 Checking console logs for sandbox activity...')
        logs = driver.get_log('browser')
        
        sandbox_logs = []
        communication_logs = []
        error_logs = []
        
        for log in logs:
            message = log['message']
            level = log['level']
            
            if 'sandbox' in message.lower():
                sandbox_logs.append(f"[{level}] {message}")
            
            if any(keyword in message.lower() for keyword in ['message', 'postmessage', 'ready', 'communication']):
                communication_logs.append(f"[{level}] {message}")
                
            if level == 'SEVERE' or 'error' in message.lower():
                error_logs.append(f"[{level}] {message}")
        
        print(f'📦 Sandbox-related logs: {len(sandbox_logs)}')
        for log in sandbox_logs:
            print(f'   {log}')
            
        print(f'📨 Communication logs: {len(communication_logs)}')
        for log in communication_logs:
            print(f'   {log}')
            
        print(f'❌ Error logs: {len(error_logs)}')
        for log in error_logs:
            print(f'   {log}')
        
        # Check for iframes (sandbox should create one)
        iframes = driver.find_elements(By.TAG_NAME, 'iframe')
        print(f'📦 Found {len(iframes)} iframes')
        
        for i, iframe in enumerate(iframes):
            src = iframe.get_attribute('src')
            sandbox = iframe.get_attribute('sandbox')
            print(f'   iframe {i}: src={src}, sandbox={sandbox}')
        
        # Wait longer for processing
        print('⏳ Waiting for processing to complete...')
        time.sleep(10)
        
        # Check logs again
        final_logs = driver.get_log('browser')
        new_logs = final_logs[len(logs):]
        
        print(f'📊 New logs after processing: {len(new_logs)}')
        for log in new_logs:
            level = log['level']
            message = log['message']
            print(f'   [{level}] {message}')
        
        # Take screenshot
        driver.save_screenshot('/W/B2B/cloudforge/chrome-extensions/mvat/tests/selenium/screenshots/sandbox_communication_test.png')
        print('📸 Sandbox communication test screenshot saved')
        
        # Cleanup
        os.unlink(test_file)
        
        # Determine test result
        has_sandbox_activity = len(sandbox_logs) > 0 or len(iframes) > 0
        has_errors = len(error_logs) > 0
        
        if has_sandbox_activity and not has_errors:
            print('✅ Sandbox communication test PASSED')
            return True
        elif has_sandbox_activity and has_errors:
            print('⚠️ Sandbox communication test PARTIAL - activity detected but with errors')
            return False
        else:
            print('❌ Sandbox communication test FAILED - no sandbox activity detected')
            return False
        
    except Exception as e:
        print(f'❌ Test failed with exception: {e}')
        return False
        
    finally:
        driver.quit()

if __name__ == '__main__':
    success = test_sandbox_communication()
    exit(0 if success else 1)
