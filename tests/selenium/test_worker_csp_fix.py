#!/usr/bin/env python3
"""
Test Web Worker CSP fix for Tesseract.js
Tests that the CSP violation for Web Workers has been resolved
"""

import time
import os
import tempfile
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def create_test_file():
    """Create a simple test file for upload"""
    content = """
    INVOICE TEST DOCUMENT
    Invoice Number: TEST-001
    Date: 2025-01-01
    
    From: Test Company
    To: Test Customer
    
    Amount: $100.00
    VAT: $20.00
    Total: $120.00
    """
    
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False)
    temp_file.write(content)
    temp_file.close()
    
    return temp_file.name

def test_worker_csp_fix():
    """Test that Web Worker CSP violations have been fixed"""
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-web-security')
    chrome_options.add_argument('--allow-file-access-from-files')

    driver = webdriver.Chrome(options=chrome_options)

    try:
        print('🔍 Testing Web Worker CSP fix...')
        driver.get('file:///W/B2B/cloudforge/chrome-extensions/mvat/dist/popup.html')
        
        # Wait for page to load
        time.sleep(5)
        
        print('📋 Page loaded, checking for file input...')
        
        # Find file input
        try:
            file_input = driver.find_element(By.CSS_SELECTOR, 'input[type="file"]')
            print(f'✅ Found file input: {file_input.tag_name}')
            
            # Create test file
            test_file = create_test_file()
            print(f'📄 Created test file: {test_file}')
            
            # Upload file
            print('📤 Uploading test file to trigger document processing...')
            file_input.send_keys(test_file)
            
            # Wait for processing to start
            time.sleep(3)
            
            # Check console logs for CSP violations
            print('📊 Checking console logs for CSP violations...')
            logs = driver.get_log('browser')
            
            csp_violations = []
            worker_errors = []
            sandbox_activity = []
            tesseract_activity = []
            
            for log in logs:
                message = log['message']
                level = log['level']
                
                # Check for CSP violations
                if 'content security policy' in message.lower() or 'csp' in message.lower():
                    csp_violations.append(f"[{level}] {message}")
                
                # Check for worker-related errors
                if 'worker' in message.lower() and ('error' in message.lower() or 'failed' in message.lower()):
                    worker_errors.append(f"[{level}] {message}")
                
                # Check for sandbox activity
                if 'sandbox' in message.lower():
                    sandbox_activity.append(f"[{level}] {message}")
                
                # Check for Tesseract activity
                if 'tesseract' in message.lower():
                    tesseract_activity.append(f"[{level}] {message}")
            
            print(f'🚫 CSP violations: {len(csp_violations)}')
            for violation in csp_violations:
                print(f'   {violation}')
                
            print(f'⚠️ Worker errors: {len(worker_errors)}')
            for error in worker_errors:
                print(f'   {error}')
                
            print(f'📦 Sandbox activity: {len(sandbox_activity)}')
            for activity in sandbox_activity[-3:]:  # Show last 3
                print(f'   {activity}')
                
            print(f'🔧 Tesseract activity: {len(tesseract_activity)}')
            for activity in tesseract_activity[-3:]:  # Show last 3
                print(f'   {activity}')
            
            # Wait longer for processing
            print('⏳ Waiting for processing to complete...')
            time.sleep(10)
            
            # Check logs again
            final_logs = driver.get_log('browser')
            new_logs = final_logs[len(logs):]
            
            print(f'📊 New logs after processing: {len(new_logs)}')
            
            new_csp_violations = []
            new_worker_errors = []
            
            for log in new_logs:
                message = log['message']
                level = log['level']
                
                if 'content security policy' in message.lower() or 'csp' in message.lower():
                    new_csp_violations.append(f"[{level}] {message}")
                
                if 'worker' in message.lower() and ('error' in message.lower() or 'failed' in message.lower()):
                    new_worker_errors.append(f"[{level}] {message}")
                
                # Show all new logs for debugging
                print(f'   [{level}] {message}')
            
            # Take screenshot
            driver.save_screenshot('/W/B2B/cloudforge/chrome-extensions/mvat/tests/selenium/screenshots/worker_csp_test.png')
            print('📸 Worker CSP test screenshot saved')
            
            # Cleanup
            os.unlink(test_file)
            
            # Determine test result
            total_csp_violations = len(csp_violations) + len(new_csp_violations)
            total_worker_errors = len(worker_errors) + len(new_worker_errors)
            
            print(f'\n📊 FINAL RESULTS:')
            print(f'   Total CSP violations: {total_csp_violations}')
            print(f'   Total worker errors: {total_worker_errors}')
            print(f'   Sandbox activity detected: {len(sandbox_activity) > 0}')
            print(f'   Tesseract activity detected: {len(tesseract_activity) > 0}')
            
            if total_csp_violations == 0 and total_worker_errors == 0:
                print('✅ Web Worker CSP test PASSED - No CSP violations or worker errors')
                return True
            elif total_csp_violations == 0 and total_worker_errors > 0:
                print('⚠️ Web Worker CSP test PARTIAL - No CSP violations but worker errors detected')
                return False
            else:
                print('❌ Web Worker CSP test FAILED - CSP violations still present')
                return False
                
        except Exception as e:
            print(f'❌ File input test failed: {e}')
            
            # Still check console logs for CSP violations
            logs = driver.get_log('browser')
            csp_violations = [log for log in logs if 'content security policy' in log['message'].lower()]
            
            print(f'📊 Console CSP violations: {len(csp_violations)}')
            for violation in csp_violations:
                print(f'   [{violation["level"]}] {violation["message"]}')
            
            return len(csp_violations) == 0
        
    except Exception as e:
        print(f'❌ Test failed with exception: {e}')
        return False
        
    finally:
        driver.quit()

if __name__ == '__main__':
    success = test_worker_csp_fix()
    exit(0 if success else 1)
