#!/usr/bin/env python3
"""
Collapsible Recent Uploads Selenium Tests
Tests for the collapsible recent uploads functionality that is folded by default
"""

import time
import json
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.chrome.options import Options

class CollapsibleRecentUploadsTests:
    def __init__(self):
        self.driver = None
        self.extension_id = None
        self.test_results = {
            'folded_by_default': False,
            'expand_functionality': False,
            'collapse_functionality': False,
            'arrow_rotation': False,
            'content_visibility': False,
            'overall_collapsible': False
        }
        
    def setup_driver(self):
        """Setup Chrome driver with extension loaded"""
        chrome_options = Options()
        
        # Load extension
        extension_path = os.path.abspath('dist/dev')
        chrome_options.add_argument(f'--load-extension={extension_path}')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        # Use Chrome 135 for compatibility
        chrome_binary = 'tests/selenium/chrome-135/chrome-linux64/chrome'
        if os.path.exists(chrome_binary):
            chrome_options.binary_location = chrome_binary
            
        self.driver = webdriver.Chrome(options=chrome_options)
        self.driver.set_window_size(1200, 800)
        
        # Get extension ID using chrome.management API
        self.driver.get('chrome://extensions/')
        time.sleep(2)
        
        try:
            extensions_data = self.driver.execute_script("""
                return new Promise((resolve) => {
                    chrome.management.getAll((extensions) => {
                        resolve(extensions);
                    });
                });
            """)
            
            for ext in extensions_data:
                if 'MVAT' in ext.get('name', ''):
                    self.extension_id = ext['id']
                    break
                    
        except Exception as e:
            print(f"⚠️ Could not get extensions via management API: {e}")
            
        if not self.extension_id:
            raise Exception("MVAT extension not found")
            
        print(f"✅ Extension loaded with ID: {self.extension_id}")
        
    def navigate_to_popup(self):
        """Navigate to extension popup"""
        popup_url = f'chrome-extension://{self.extension_id}/popup.html'
        self.driver.get(popup_url)
        
        # Wait for popup to load
        WebDriverWait(self.driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, '[data-testid="mvat-app"], #root'))
        )
        time.sleep(1)
        
    def add_mock_invoices(self):
        """Add mock invoice data for testing"""
        self.driver.execute_script("""
            const mockInvoices = Array.from({ length: 5 }, (_, i) => ({
                id: `test-invoice-${i}`,
                filename: `test-invoice-${i}.pdf`,
                processedAt: new Date(Date.now() - i * 60000).toISOString(),
                number: `INV-${String(i).padStart(3, '0')}`,
                total_gross: `${(Math.random() * 1000).toFixed(2)}`,
                currency: 'PLN'
            }));
            
            if (window.updateContext) {
                window.updateContext({ invoices: mockInvoices });
            }
        """)
        time.sleep(2)
        
    def test_folded_by_default(self):
        """Test that recent uploads section is folded by default"""
        print("📁 Testing folded by default state...")
        
        try:
            self.navigate_to_popup()
            
            # Navigate to upload tab
            upload_tab = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Upload')]"))
            )
            upload_tab.click()
            time.sleep(1)
            
            # Add mock data
            self.add_mock_invoices()
            
            # Check for recent uploads header
            try:
                header = self.driver.find_element(By.XPATH, "//h3[contains(text(), 'Recent Uploads Summary')]")
                print("✅ Found 'Recent Uploads Summary' header")
                
                # Check that content is not visible (folded state)
                content_elements = self.driver.find_elements(By.CSS_SELECTOR, '.extension-scroll')
                
                # In folded state, the scrollable content should not be visible
                content_visible = False
                for element in content_elements:
                    if element.is_displayed() and element.size['height'] > 0:
                        # Check if this is the recent uploads content by looking for invoice items
                        invoice_items = element.find_elements(By.XPATH, ".//span[contains(text(), 'test-invoice')]")
                        if invoice_items:
                            content_visible = True
                            break
                
                folded_by_default = not content_visible
                
                self.test_results['folded_by_default'] = folded_by_default
                print(f"✅ Folded by default: {'PASS' if folded_by_default else 'FAIL'}")
                
            except NoSuchElementException:
                print("❌ Recent uploads header not found")
                self.test_results['folded_by_default'] = False
                
        except Exception as e:
            print(f"❌ Folded by default test failed: {e}")
            self.test_results['folded_by_default'] = False
            
    def test_expand_functionality(self):
        """Test that clicking the header expands the section"""
        print("📂 Testing expand functionality...")
        
        try:
            self.navigate_to_popup()
            
            # Navigate to upload tab
            upload_tab = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Upload')]"))
            )
            upload_tab.click()
            time.sleep(1)
            
            # Add mock data
            self.add_mock_invoices()
            
            # Find and click the header to expand
            try:
                header_clickable = self.driver.find_element(By.CSS_SELECTOR, '.cursor-pointer')
                
                # Take screenshot before clicking
                self.driver.save_screenshot('tests/selenium/screenshots/before_expand.png')
                
                header_clickable.click()
                time.sleep(1)  # Allow for animation
                
                # Take screenshot after clicking
                self.driver.save_screenshot('tests/selenium/screenshots/after_expand.png')
                
                # Check that content is now visible
                content_elements = self.driver.find_elements(By.CSS_SELECTOR, '.extension-scroll')
                
                content_visible = False
                for element in content_elements:
                    if element.is_displayed() and element.size['height'] > 0:
                        # Check if this contains invoice items
                        invoice_items = element.find_elements(By.XPATH, ".//span[contains(text(), 'test-invoice')]")
                        if invoice_items:
                            content_visible = True
                            print(f"✅ Found {len(invoice_items)} invoice items after expansion")
                            break
                
                self.test_results['expand_functionality'] = content_visible
                print(f"✅ Expand functionality: {'PASS' if content_visible else 'FAIL'}")
                
            except NoSuchElementException:
                print("❌ Clickable header not found")
                self.test_results['expand_functionality'] = False
                
        except Exception as e:
            print(f"❌ Expand functionality test failed: {e}")
            self.test_results['expand_functionality'] = False
            
    def test_collapse_functionality(self):
        """Test that clicking the header again collapses the section"""
        print("📁 Testing collapse functionality...")
        
        try:
            self.navigate_to_popup()
            
            # Navigate to upload tab
            upload_tab = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Upload')]"))
            )
            upload_tab.click()
            time.sleep(1)
            
            # Add mock data
            self.add_mock_invoices()
            
            # Find and click the header to expand first
            header_clickable = self.driver.find_element(By.CSS_SELECTOR, '.cursor-pointer')
            header_clickable.click()
            time.sleep(1)  # Allow for expansion
            
            # Click again to collapse
            header_clickable.click()
            time.sleep(1)  # Allow for collapse animation
            
            # Take screenshot after collapsing
            self.driver.save_screenshot('tests/selenium/screenshots/after_collapse.png')
            
            # Check that content is no longer visible
            content_elements = self.driver.find_elements(By.CSS_SELECTOR, '.extension-scroll')
            
            content_visible = False
            for element in content_elements:
                if element.is_displayed() and element.size['height'] > 0:
                    # Check if this contains invoice items
                    invoice_items = element.find_elements(By.XPATH, ".//span[contains(text(), 'test-invoice')]")
                    if invoice_items:
                        content_visible = True
                        break
            
            collapsed_successfully = not content_visible
            
            self.test_results['collapse_functionality'] = collapsed_successfully
            print(f"✅ Collapse functionality: {'PASS' if collapsed_successfully else 'FAIL'}")
            
        except Exception as e:
            print(f"❌ Collapse functionality test failed: {e}")
            self.test_results['collapse_functionality'] = False
            
    def test_arrow_rotation(self):
        """Test that the arrow rotates when expanding/collapsing"""
        print("🔄 Testing arrow rotation...")
        
        try:
            self.navigate_to_popup()
            
            # Navigate to upload tab
            upload_tab = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Upload')]"))
            )
            upload_tab.click()
            time.sleep(1)
            
            # Add mock data
            self.add_mock_invoices()
            
            # Find the arrow element
            try:
                arrow = self.driver.find_element(By.XPATH, "//span[contains(text(), '▶')]")
                
                # Get initial transform/rotation
                initial_classes = arrow.get_attribute('class')
                print(f"📊 Initial arrow classes: {initial_classes}")
                
                # Click to expand
                header_clickable = self.driver.find_element(By.CSS_SELECTOR, '.cursor-pointer')
                header_clickable.click()
                time.sleep(1)
                
                # Get classes after expansion
                expanded_classes = arrow.get_attribute('class')
                print(f"📊 Expanded arrow classes: {expanded_classes}")
                
                # Check if rotation class is applied
                rotation_applied = 'rotate-90' in expanded_classes
                
                self.test_results['arrow_rotation'] = rotation_applied
                print(f"✅ Arrow rotation: {'PASS' if rotation_applied else 'FAIL'}")
                
            except NoSuchElementException:
                print("❌ Arrow element not found")
                self.test_results['arrow_rotation'] = False
                
        except Exception as e:
            print(f"❌ Arrow rotation test failed: {e}")
            self.test_results['arrow_rotation'] = False
            
    def test_content_visibility(self):
        """Test that content is properly shown/hidden based on state"""
        print("👁️ Testing content visibility...")
        
        try:
            self.navigate_to_popup()
            
            # Navigate to upload tab
            upload_tab = WebDriverWait(self.driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Upload')]"))
            )
            upload_tab.click()
            time.sleep(1)
            
            # Add mock data
            self.add_mock_invoices()
            
            # Test initial state (should be hidden)
            initial_content = self.driver.find_elements(By.XPATH, "//span[contains(text(), 'test-invoice')]")
            initially_hidden = len(initial_content) == 0
            
            # Expand
            header_clickable = self.driver.find_element(By.CSS_SELECTOR, '.cursor-pointer')
            header_clickable.click()
            time.sleep(1)
            
            # Test expanded state (should be visible)
            expanded_content = self.driver.find_elements(By.XPATH, "//span[contains(text(), 'test-invoice')]")
            expanded_visible = len(expanded_content) > 0
            
            # Collapse again
            header_clickable.click()
            time.sleep(1)
            
            # Test collapsed state (should be hidden again)
            collapsed_content = self.driver.find_elements(By.XPATH, "//span[contains(text(), 'test-invoice')]")
            collapsed_hidden = len(collapsed_content) == 0
            
            print(f"📊 Initial: {len(initial_content)} items, Expanded: {len(expanded_content)} items, Collapsed: {len(collapsed_content)} items")
            
            content_visibility_correct = initially_hidden and expanded_visible and collapsed_hidden
            
            self.test_results['content_visibility'] = content_visibility_correct
            print(f"✅ Content visibility: {'PASS' if content_visibility_correct else 'FAIL'}")
            
        except Exception as e:
            print(f"❌ Content visibility test failed: {e}")
            self.test_results['content_visibility'] = False
            
    def run_all_tests(self):
        """Run all collapsible recent uploads tests"""
        print("🚀 Starting Collapsible Recent Uploads Tests")
        print("=" * 60)
        
        try:
            self.setup_driver()
            
            # Run individual tests
            self.test_folded_by_default()
            self.test_expand_functionality()
            self.test_collapse_functionality()
            self.test_arrow_rotation()
            self.test_content_visibility()
            
            # Calculate overall score
            passed_tests = sum(self.test_results.values())
            total_tests = len(self.test_results)
            overall_score = (passed_tests / total_tests) * 100
            
            self.test_results['overall_collapsible'] = overall_score >= 80
            
            print("\n" + "=" * 60)
            print("📊 TEST RESULTS SUMMARY")
            print("=" * 60)
            
            for test_name, result in self.test_results.items():
                if test_name != 'overall_collapsible':
                    status = "✅ PASS" if result else "❌ FAIL"
                    print(f"{test_name.replace('_', ' ').title()}: {status}")
                    
            print(f"\nOverall Score: {overall_score:.1f}% ({passed_tests}/{total_tests} tests passed)")
            print(f"Overall Collapsible: {'✅ PASS' if self.test_results['overall_collapsible'] else '❌ FAIL'}")
            
            # Save results
            with open('tests/selenium/screenshots/collapsible_uploads_test_results.json', 'w') as f:
                json.dump({
                    'timestamp': time.time(),
                    'results': self.test_results,
                    'score': overall_score
                }, f, indent=2)
                
            return self.test_results['overall_collapsible']
            
        except Exception as e:
            print(f"❌ Test suite failed: {e}")
            return False
            
        finally:
            if self.driver:
                self.driver.quit()

if __name__ == "__main__":
    tester = CollapsibleRecentUploadsTests()
    success = tester.run_all_tests()
    exit(0 if success else 1)
