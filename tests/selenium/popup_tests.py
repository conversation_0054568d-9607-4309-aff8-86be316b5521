#!/usr/bin/env python3
"""
Selenium tests for Chrome extension popup functionality
Tests the critical fixes implemented in the action plan
"""

import os
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException

class PopupTests:
    def __init__(self):
        self.driver = None
        self.extension_id = None
        self.wait = None
        
    def setup_driver(self):
        """Set up Chrome driver with extension loaded"""
        print("🔧 Setting up Chrome driver...")
        
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--allow-running-insecure-content")
        
        # Load extension from current directory
        extension_path = os.path.abspath(".")
        chrome_options.add_argument(f"--load-extension={extension_path}")
        
        # Enable extension logging
        chrome_options.add_argument("--enable-logging")
        chrome_options.add_argument("--log-level=0")
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.wait = WebDriverWait(self.driver, 10)
            
            # Get extension ID
            self.driver.get("chrome://extensions/")
            time.sleep(2)
            
            # Find extension ID (this is a simplified approach)
            # In real tests, you'd parse the extensions page properly
            print("✅ Chrome driver set up successfully")
            return True
            
        except Exception as e:
            print(f"❌ Failed to set up Chrome driver: {e}")
            return False
    
    def test_popup_opens(self):
        """Test that popup opens without errors"""
        print("\n📋 Test 1: Popup Opens")
        
        try:
            # Navigate to popup.html directly
            popup_url = f"file://{os.path.abspath('popup.html')}"
            print(f"🔗 Opening popup: {popup_url}")
            
            self.driver.get(popup_url)
            time.sleep(3)  # Wait for initialization
            
            # Check if popup loaded
            title = self.driver.title
            print(f"📄 Page title: {title}")
            
            # Check for critical elements
            drop_area = self.wait.until(
                EC.presence_of_element_located((By.ID, "dropArea"))
            )
            print("✅ Drop area found")
            
            file_input = self.driver.find_element(By.ID, "fileInput")
            print("✅ File input found")
            
            # Check for console errors
            logs = self.driver.get_log('browser')
            error_logs = [log for log in logs if log['level'] == 'SEVERE']
            
            if error_logs:
                print("⚠️ Console errors found:")
                for log in error_logs[:5]:  # Show first 5 errors
                    print(f"   {log['message']}")
            else:
                print("✅ No severe console errors")
            
            print("✅ Test 1 PASSED: Popup opens successfully")
            return True
            
        except TimeoutException:
            print("❌ Test 1 FAILED: Popup elements not found (timeout)")
            return False
        except Exception as e:
            print(f"❌ Test 1 FAILED: {e}")
            return False
    
    def test_drag_drop_area(self):
        """Test drag and drop area functionality"""
        print("\n🎯 Test 2: Drag & Drop Area")
        
        try:
            # Check if drop area is visible and interactive
            drop_area = self.driver.find_element(By.ID, "dropArea")
            
            # Check if drop area is displayed
            if not drop_area.is_displayed():
                print("❌ Drop area is not visible")
                return False
            
            print("✅ Drop area is visible")
            
            # Check drop area styling
            drop_area_classes = drop_area.get_attribute("class")
            print(f"📝 Drop area classes: {drop_area_classes}")
            
            # Simulate hover (to test highlight functionality)
            from selenium.webdriver.common.action_chains import ActionChains
            actions = ActionChains(self.driver)
            actions.move_to_element(drop_area).perform()
            time.sleep(1)
            
            print("✅ Drop area hover simulation completed")
            
            # Check if file input is properly connected
            file_input = self.driver.find_element(By.ID, "fileInput")
            if file_input.get_attribute("accept") == ".pdf":
                print("✅ File input accepts PDF files")
            else:
                print("⚠️ File input accept attribute not set correctly")
            
            print("✅ Test 2 PASSED: Drag & drop area functional")
            return True
            
        except Exception as e:
            print(f"❌ Test 2 FAILED: {e}")
            return False
    
    def test_ui_elements_visible(self):
        """Test that all critical UI elements are visible"""
        print("\n👁️ Test 3: UI Elements Visibility")
        
        critical_elements = [
            ("dropArea", "Drop Area"),
            ("fileInput", "File Input"),
            ("tab-main", "Main Tab"),
            ("tab-settings", "Settings Tab"),
            ("tab-debug", "Debug Tab"),
        ]
        
        visible_count = 0
        total_count = len(critical_elements)
        
        for element_id, element_name in critical_elements:
            try:
                element = self.driver.find_element(By.ID, element_id)
                if element.is_displayed():
                    print(f"✅ {element_name} is visible")
                    visible_count += 1
                else:
                    print(f"⚠️ {element_name} exists but not visible")
            except NoSuchElementException:
                print(f"❌ {element_name} not found")
        
        success_rate = (visible_count / total_count) * 100
        print(f"📊 UI Visibility: {visible_count}/{total_count} ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            print("✅ Test 3 PASSED: UI elements sufficiently visible")
            return True
        else:
            print("❌ Test 3 FAILED: Too many UI elements missing")
            return False
    
    def test_css_loading(self):
        """Test that CSS styles are properly loaded"""
        print("\n🎨 Test 4: CSS Loading")
        
        try:
            # Check if main CSS variables are applied
            body = self.driver.find_element(By.TAG_NAME, "body")
            body_styles = self.driver.execute_script(
                "return window.getComputedStyle(arguments[0])", body
            )
            
            font_family = body_styles.get('font-family', '')
            if 'Segoe UI' in font_family or 'Arial' in font_family:
                print("✅ CSS font family applied correctly")
            else:
                print(f"⚠️ Unexpected font family: {font_family}")
            
            # Check drop area styling
            drop_area = self.driver.find_element(By.ID, "dropArea")
            drop_styles = self.driver.execute_script(
                "return window.getComputedStyle(arguments[0])", drop_area
            )
            
            border_style = drop_styles.get('border-style', '')
            if 'dashed' in border_style:
                print("✅ Drop area has dashed border")
            else:
                print(f"⚠️ Drop area border style: {border_style}")
            
            print("✅ Test 4 PASSED: CSS loading functional")
            return True
            
        except Exception as e:
            print(f"❌ Test 4 FAILED: {e}")
            return False
    
    def run_all_tests(self):
        """Run all tests and return summary"""
        print("🧪 Starting Chrome Extension Popup Tests")
        print("=" * 50)
        
        if not self.setup_driver():
            return False
        
        tests = [
            self.test_popup_opens,
            self.test_drag_drop_area,
            self.test_ui_elements_visible,
            self.test_css_loading,
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                if test():
                    passed += 1
                time.sleep(1)  # Brief pause between tests
            except Exception as e:
                print(f"❌ Test failed with exception: {e}")
        
        print("\n" + "=" * 50)
        print(f"📊 Test Summary: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 ALL TESTS PASSED!")
        elif passed >= total * 0.75:
            print("⚠️ Most tests passed - minor issues detected")
        else:
            print("❌ Multiple test failures - major issues detected")
        
        return passed == total
    
    def cleanup(self):
        """Clean up resources"""
        if self.driver:
            self.driver.quit()
            print("🧹 Driver cleaned up")

if __name__ == "__main__":
    tester = PopupTests()
    try:
        success = tester.run_all_tests()
        exit_code = 0 if success else 1
    except KeyboardInterrupt:
        print("\n⚠️ Tests interrupted by user")
        exit_code = 2
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        exit_code = 3
    finally:
        tester.cleanup()
    
    exit(exit_code)
