import { vi } from 'vitest';

// Test helpers and mocks
export const mockPdfText = 'Test PDF content';
export const mockOcrText = 'Test OCR content';

export const createMockPdfFile = (content = mockPdfText) => {
  return new Blob(['%PDF-1.4\n' + content], { type: 'application/pdf' });
};

export const mockPdfResult = {
  text: mockPdfText,
  pages: 1,
  metadata: {
    Title: 'Test PDF',
    Author: 'Test Author'
  }
};

export const mockOcrResult = {
  text: mockOcrText,
  lines: [
    {
      text: mockOcrText,
      confidence: 0.95,
      bbox: { x0: 0, y0: 0, x1: 100, y1: 20 }
    }
  ],
  words: [
    {
      text: 'Test',
      confidence: 0.95,
      bbox: { x0: 0, y0: 0, x1: 40, y1: 20 }
    },
    {
      text: 'OCR',
      confidence: 0.95,
      bbox: { x0: 45, y0: 0, x1: 70, y1: 20 }
    },
    {
      text: 'content',
      confidence: 0.95,
      bbox: { x0: 75, y0: 0, x1: 100, y1: 20 }
    }
  ],
  confidence: 0.95,
  language: 'eng'
};

export const mockDeepSeekResult = {
  fields: {
    invoice_number: { value: 'INV-001', confidence: 0.95 },
    date: { value: '2024-01-01', confidence: 0.92 },
    total_amount: { value: '1000.00', confidence: 0.98 }
  },
  document_type: 'invoice',
  confidence_scores: {
    overall: 0.93,
    fields: {
      invoice_number: 0.95,
      date: 0.92,
      total_amount: 0.98
    }
  }
};

class MockFileReader {
  static EMPTY = 0;
  static LOADING = 1;
  static DONE = 2;

  constructor() {
    this.onload = null;
    this.onerror = null;
    this.readyState = MockFileReader.EMPTY;
  }

  readAsDataURL() {
    this.readyState = MockFileReader.LOADING;
    setTimeout(() => {
      this.readyState = MockFileReader.DONE;
      if (this.onload) {
        this.onload({ target: { result: 'data:application/pdf;base64,test' } });
      }
    });
  }
}

export const setupTestMocks = () => {
  // Mock FileReader
  global.FileReader = MockFileReader;

  // Mock fetch
  global.fetch = vi.fn().mockImplementation(() =>
    Promise.resolve({
      ok: true,
      json: () => Promise.resolve(mockDeepSeekResult)
    })
  );

  // Mock console.error to prevent noise in tests
  console.error = vi.fn();
};

export const clearTestMocks = () => {
  vi.clearAllMocks();
};
