/**
 * Comprehensive Test Suite for ConsolidatedFileValidationService
 * Tests all validation functionality including edge cases and error handling
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import ConsolidatedFileValidationService from '../../src/services/ConsolidatedFileValidationService.js';

// Mock dependencies
vi.mock('../../src/utils/securityChecks.js', () => ({
  performSecurityChecks: vi.fn().mockResolvedValue({
    isValid: true,
    errors: [],
    warnings: []
  })
}));

vi.mock('../../src/utils/pdfUtils.js', () => ({
  isPDFFile: vi.fn(),
  validatePDFFile: vi.fn(),
  analyzeInvoiceContent: vi.fn()
}));

describe('ConsolidatedFileValidationService', () => {
  let validationService;

  beforeEach(() => {
    validationService = new ConsolidatedFileValidationService();
    vi.clearAllMocks();
  });

  // Helper function to create mock files
  const createMockFile = (name, size, type, content = 'mock content') => {
    const file = new File([content], name, { type });
    Object.defineProperty(file, 'size', { value: size });
    return file;
  };

  describe('Single File Validation', () => {
    it('should validate a valid PDF file', async () => {
      const mockFile = createMockFile('test.pdf', 1024 * 1024, 'application/pdf');

      const result = await validationService.validateFile(mockFile);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.metadata.name).toBe('test.pdf');
      expect(result.metadata.type).toBe('application/pdf');
    });

    it('should reject files that are too large', async () => {
      const mockFile = createMockFile('large.pdf', 20 * 1024 * 1024, 'application/pdf');

      const result = await validationService.validateFile(mockFile);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain(expect.stringContaining('too large'));
    });

    it('should reject unsupported file types', async () => {
      const mockFile = createMockFile('test.exe', 1024, 'application/x-executable');

      const result = await validationService.validateFile(mockFile);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain(expect.stringContaining('not supported'));
    });

    it('should handle validation errors gracefully', async () => {
      const result = await validationService.validateFile(null);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain(expect.stringContaining('Invalid file object'));
    });
  });

  describe('Multiple Files Validation', () => {
    it('should validate multiple valid files', async () => {
      const files = [
        createMockFile('test1.pdf', 1024, 'application/pdf'),
        createMockFile('test2.jpg', 2048, 'image/jpeg')
      ];

      const result = await validationService.validateFiles(files);

      expect(result.isValid).toBe(true);
      expect(result.validFiles).toHaveLength(2);
      expect(result.invalidFiles).toHaveLength(0);
    });

    it('should handle mixed valid and invalid files', async () => {
      const files = [
        createMockFile('valid.pdf', 1024, 'application/pdf'),
        createMockFile('invalid.exe', 1024, 'application/x-executable')
      ];

      const result = await validationService.validateFiles(files);

      expect(result.isValid).toBe(false);
      expect(result.validFiles).toHaveLength(1);
      expect(result.invalidFiles).toHaveLength(1);
    });

    it('should respect file count limits', async () => {
      const files = Array.from({ length: 15 }, (_, i) =>
        createMockFile(`test${i}.pdf`, 1024, 'application/pdf')
      );

      const result = await validationService.validateFiles(files, { maxFiles: 10 });

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain(expect.stringContaining('too many files'));
    });
  });

  describe('Performance Metrics', () => {
    it('should track performance metrics', async () => {
      const mockFile = createMockFile('test.pdf', 1024, 'application/pdf');

      await validationService.validateFile(mockFile);

      const metrics = validationService.getPerformanceMetrics();
      expect(metrics.totalValidations).toBe(1);
      expect(metrics.totalTime).toBeGreaterThan(0);
    });

    it('should clear cache when requested', () => {
      validationService.clearCache();

      const metrics = validationService.getPerformanceMetrics();
      expect(metrics.totalValidations).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Configuration Options', () => {
    it('should respect custom configuration', () => {
      const customService = new ConsolidatedFileValidationService({
        maxFileSize: 5 * 1024 * 1024, // 5MB
        enableSecurityScanning: false
      });

      expect(customService.config.maxFileSize).toBe(5 * 1024 * 1024);
      expect(customService.config.enableSecurityScanning).toBe(false);
    });
  });
});
