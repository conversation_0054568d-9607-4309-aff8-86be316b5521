/**
 * Grouping Integration Tests
 * Tests for the complete grouping functionality
 *
 * Epic: EPIC-003 - Data Display & Visualization
 * Story: STORY-3.2 - Grouping & Aggregation
 * Task: TASK-3.2.1 - Grouping Logic
 */

import { describe, it, expect } from 'vitest';
import { AggregationCalculator } from '../../src/utils/AggregationCalculator.js';
import { DateGrouping } from '../../src/utils/DateGrouping.js';

describe('Grouping Integration Tests', () => {
  describe('AggregationCalculator', () => {
    it('should calculate basic aggregations correctly', () => {
      const calculator = new AggregationCalculator();
      const values = [10, 20, 30, 40, 50];

      expect(calculator.calculate(values, 'sum')).toBe(150);
      expect(calculator.calculate(values, 'count')).toBe(5);
      expect(calculator.calculate(values, 'average')).toBe(30);
      expect(calculator.calculate(values, 'min')).toBe(10);
      expect(calculator.calculate(values, 'max')).toBe(50);
      expect(calculator.calculate(values, 'median')).toBe(30);
    });

    it('should handle edge cases', () => {
      const calculator = new AggregationCalculator();

      expect(calculator.calculate([], 'sum')).toBeNull();
      expect(calculator.calculate([42], 'average')).toBe(42);
      expect(calculator.calculate([1, 'invalid', 3], 'sum')).toBe(4);
    });

    it('should calculate summary statistics', () => {
      const calculator = new AggregationCalculator();
      const values = [1, 2, 3, 4, 5];

      const summary = calculator.calculateSummary(values);

      expect(summary.count).toBe(5);
      expect(summary.sum).toBe(15);
      expect(summary.average).toBe(3);
      expect(summary.min).toBe(1);
      expect(summary.max).toBe(5);
      expect(summary.median).toBe(3);
      expect(summary.range).toBe(4);
    });
  });

  describe('DateGrouping', () => {
    it('should generate correct date keys', () => {
      const dateGrouping = new DateGrouping();
      const testDate = new Date('2024-03-15T10:30:00Z');

      expect(dateGrouping.getYearKey(testDate)).toBe('2024');
      expect(dateGrouping.getMonthKey(testDate)).toBe('2024-03');
      expect(dateGrouping.getDayKey(testDate)).toBe('2024-03-15');
    });

    it('should handle fiscal years correctly', () => {
      const dateGrouping = new DateGrouping();
      const marchDate = new Date('2024-03-15T10:30:00Z');
      const aprilDate = new Date('2024-04-15T10:30:00Z');

      // Fiscal year starting in April
      expect(dateGrouping.getYearKey(marchDate, 4)).toBe('FY2024');
      expect(dateGrouping.getYearKey(aprilDate, 4)).toBe('FY2025');
    });

    it('should calculate date ranges correctly', () => {
      const dateGrouping = new DateGrouping();

      const yearRange = dateGrouping.getYearDateRange('2024', 1);
      expect(yearRange.start.getFullYear()).toBe(2024);
      expect(yearRange.start.getMonth()).toBe(0); // January
      expect(yearRange.end.getFullYear()).toBe(2024);
      expect(yearRange.end.getMonth()).toBe(11); // December

      const monthRange = dateGrouping.getMonthDateRange('2024-03');
      expect(monthRange.start.getMonth()).toBe(2); // March (0-based)
      expect(monthRange.start.getDate()).toBe(1);
      expect(monthRange.end.getMonth()).toBe(2); // March
      expect(monthRange.end.getDate()).toBe(31); // Last day of March
    });
  });

  describe('Invoice Data Processing', () => {
    it('should process sample invoice data correctly', () => {
      const calculator = new AggregationCalculator();
      const dateGrouping = new DateGrouping();

      // Sample invoice data
      const invoices = [
        {
          id: '1',
          issue_date: '2024-01-15',
          total_net: 100,
          total_vat: 23,
          total_gross: 123,
          currency: 'PLN'
        },
        {
          id: '2',
          issue_date: '2024-01-20',
          total_net: 200,
          total_vat: 46,
          total_gross: 246,
          currency: 'PLN'
        },
        {
          id: '3',
          issue_date: '2024-02-10',
          total_net: 150,
          total_vat: 34.5,
          total_gross: 184.5,
          currency: 'EUR'
        }
      ];

      // Group by month
      const groups = {};
      for (const invoice of invoices) {
        const date = new Date(invoice.issue_date);
        const monthKey = dateGrouping.getMonthKey(date);

        if (!groups[monthKey]) {
          groups[monthKey] = [];
        }
        groups[monthKey].push(invoice);
      }

      expect(Object.keys(groups)).toHaveLength(2);
      expect(groups['2024-01']).toHaveLength(2);
      expect(groups['2024-02']).toHaveLength(1);

      // Calculate aggregations for January
      const janInvoices = groups['2024-01'];
      const janTotals = janInvoices.map(inv => inv.total_gross);

      expect(calculator.calculate(janTotals, 'sum')).toBe(369);
      expect(calculator.calculate(janTotals, 'count')).toBe(2);
      expect(calculator.calculate(janTotals, 'average')).toBe(184.5);
    });

    it('should handle multiple currencies', () => {
      const calculator = new AggregationCalculator();

      const invoices = [
        { total_gross: 100, currency: 'PLN' },
        { total_gross: 200, currency: 'PLN' },
        { total_gross: 50, currency: 'EUR' },
        { total_gross: 75, currency: 'EUR' }
      ];

      // Group by currency
      const currencyGroups = {};
      for (const invoice of invoices) {
        const currency = invoice.currency;
        if (!currencyGroups[currency]) {
          currencyGroups[currency] = [];
        }
        currencyGroups[currency].push(invoice.total_gross);
      }

      expect(calculator.calculate(currencyGroups.PLN, 'sum')).toBe(300);
      expect(calculator.calculate(currencyGroups.EUR, 'sum')).toBe(125);
      expect(calculator.calculate(currencyGroups.PLN, 'count')).toBe(2);
      expect(calculator.calculate(currencyGroups.EUR, 'count')).toBe(2);
    });
  });

  describe('Performance and Edge Cases', () => {
    it('should handle large datasets efficiently', () => {
      const calculator = new AggregationCalculator();

      // Generate large dataset
      const largeDataset = Array.from({ length: 10000 }, (_, i) => i + 1);

      const startTime = performance.now();
      const sum = calculator.calculate(largeDataset, 'sum');
      const endTime = performance.now();

      expect(sum).toBe(50005000); // Sum of 1 to 10000
      expect(endTime - startTime).toBeLessThan(100); // Should be fast
    });

    it('should handle empty and invalid data gracefully', () => {
      const calculator = new AggregationCalculator();
      const dateGrouping = new DateGrouping();

      expect(calculator.calculate([], 'sum')).toBeNull();
      expect(calculator.calculate([null, undefined, 'invalid'], 'sum')).toBeNull();

      expect(() => {
        dateGrouping.getCustomKey(new Date(), null);
      }).toThrow();
    });

    it('should validate configuration correctly', () => {
      const dateGrouping = new DateGrouping();

      const validConfig = { fiscalYearStart: 6, weekStartsOn: 1 };
      const validation = dateGrouping.validateConfig(validConfig);
      expect(validation.valid).toBe(true);

      const invalidConfig = { fiscalYearStart: 15, weekStartsOn: 8 };
      const invalidValidation = dateGrouping.validateConfig(invalidConfig);
      expect(invalidValidation.valid).toBe(false);
      expect(invalidValidation.errors.length).toBeGreaterThan(0);
    });
  });

  describe('Real-world Scenarios', () => {
    it('should handle quarterly reporting correctly', () => {
      const dateGrouping = new DateGrouping();
      const calculator = new AggregationCalculator();

      const quarterlyInvoices = [
        { issue_date: '2024-01-15', total_gross: 1000 },
        { issue_date: '2024-02-15', total_gross: 1500 },
        { issue_date: '2024-03-15', total_gross: 2000 },
        { issue_date: '2024-04-15', total_gross: 1200 },
        { issue_date: '2024-05-15', total_gross: 1800 },
        { issue_date: '2024-06-15', total_gross: 2200 }
      ];

      // Group by quarter
      const quarterGroups = {};
      for (const invoice of quarterlyInvoices) {
        const date = new Date(invoice.issue_date);
        const quarterKey = dateGrouping.getQuarterKey(date);

        if (!quarterGroups[quarterKey]) {
          quarterGroups[quarterKey] = [];
        }
        quarterGroups[quarterKey].push(invoice.total_gross);
      }

      expect(quarterGroups['2024-Q1']).toHaveLength(3);
      expect(quarterGroups['2024-Q2']).toHaveLength(3);

      const q1Total = calculator.calculate(quarterGroups['2024-Q1'], 'sum');
      const q2Total = calculator.calculate(quarterGroups['2024-Q2'], 'sum');

      expect(q1Total).toBe(4500);
      expect(q2Total).toBe(5200);
    });

    it('should support custom date ranges', () => {
      const dateGrouping = new DateGrouping();

      const customRanges = [
        {
          key: 'H1_2024',
          start: '2024-01-01',
          end: '2024-06-30'
        },
        {
          key: 'H2_2024',
          start: '2024-07-01',
          end: '2024-12-31'
        }
      ];

      const h1Date = new Date('2024-03-15');
      const h2Date = new Date('2024-09-15');

      expect(dateGrouping.getCustomKey(h1Date, customRanges)).toBe('H1_2024');
      expect(dateGrouping.getCustomKey(h2Date, customRanges)).toBe('H2_2024');

      const h1Range = dateGrouping.getCustomDateRange('H1_2024', customRanges);
      expect(h1Range.start).toEqual(new Date('2024-01-01'));
      expect(h1Range.end).toEqual(new Date('2024-06-30'));
    });
  });
});
