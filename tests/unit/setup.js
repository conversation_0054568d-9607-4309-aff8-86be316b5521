// Jest setup file

// Mock PDF.js
jest.mock('pdfjs-dist', () => ({
  getDocument: jest.fn().mockImplementation(() => ({
    promise: Promise.resolve({
      numPages: 1,
      getPage: jest.fn().mockImplementation(() => ({
        getTextContent: jest.fn().mockResolvedValue({
          items: [{ str: 'Test PDF content' }]
        })
      })),
      getMetadata: jest.fn().mockResolvedValue({
        info: {
          Title: 'Test PDF',
          Author: 'Test Author'
        }
      }),
      getOutline: jest.fn().mockResolvedValue([]),
      _pdfInfo: {
        encrypted: false
      }
    })
  })),
  GlobalWorkerOptions: {
    workerSrc: ''
  },
  version: '2.0.0'
}));

// Mock Tesseract.js
jest.mock('tesseract.js', () => ({
  createWorker: jest.fn().mockImplementation(() => ({
    loadLanguage: jest.fn().mockResolvedValue(undefined),
    initialize: jest.fn().mockResolvedValue(undefined),
    recognize: jest.fn().mockResolvedValue({
      data: {
        text: 'Test OCR content',
        words: [{ text: 'Test', confidence: 0.95 }],
        lines: [{ text: 'Test OCR content', confidence: 0.95 }],
        confidence: 0.95
      }
    }),
    terminate: jest.fn().mockResolvedValue(undefined)
  })),
  createScheduler: jest.fn().mockImplementation(() => ({
    addWorker: jest.fn(),
    addJob: jest.fn().mockImplementation(() => ({
      data: {
        text: 'Test OCR content',
        words: [{ text: 'Test', confidence: 0.95 }],
        lines: [{ text: 'Test OCR content', confidence: 0.95 }],
        confidence: 0.95
      }
    })),
    terminate: jest.fn()
  }))
}));

// Mock fetch for DeepSeek API calls
global.fetch = jest.fn().mockImplementation(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve({
      fields: {
        invoice_number: { value: 'INV-001', confidence: 0.95 },
        date: { value: '2024-01-01', confidence: 0.92 }
      },
      document_type: 'invoice',
      confidence_scores: {
        overall: 0.93,
        fields: { invoice_number: 0.95, date: 0.92 }
      }
    })
  })
);

// Mock FileReader
class MockFileReader {
  readAsDataURL() {
    setTimeout(() => {
      this.onload({ target: { result: 'data:application/pdf;base64,test' } });
    });
  }
}

global.FileReader = MockFileReader;

// Mock Blob
global.Blob = class {
  constructor(content, options = {}) {
    this.content = content;
    this.type = options.type || '';
  }

  arrayBuffer() {
    return Promise.resolve(new ArrayBuffer(8));
  }
};

// Add missing DOM types
global.TextDecoder = require('util').TextDecoder;
global.TextEncoder = require('util').TextEncoder;

// Suppress console.error in tests
console.error = jest.fn();

// Clear all mocks after each test
afterEach(() => {
  jest.clearAllMocks();
});
