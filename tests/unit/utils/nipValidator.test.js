import { describe, it, expect } from 'vitest';
import {
  cleanNip,
  formatNip,
  validateNip,
  isValidNip,
  generateExampleNip,
  getNipValidationRules
} from '../../../src/utils/nipValidator.js';

describe('NIP Validator', () => {
  describe('cleanNip', () => {
    it('should remove all non-digit characters', () => {
      expect(cleanNip('123-456-78-90')).toBe('1234567890');
      expect(cleanNip('123 456 78 90')).toBe('1234567890');
      expect(cleanNip('123.456.78.90')).toBe('1234567890');
      expect(cleanNip('abc123def456ghi')).toBe('123456');
    });

    it('should handle empty and invalid inputs', () => {
      expect(cleanNip('')).toBe('');
      expect(cleanNip(null)).toBe('');
      expect(cleanNip(undefined)).toBe('');
      expect(cleanNip(123)).toBe('');
    });

    it('should preserve digits only', () => {
      expect(cleanNip('1234567890')).toBe('1234567890');
      expect(cleanNip('0000000000')).toBe('0000000000');
    });
  });

  describe('formatNip', () => {
    it('should format valid 10-digit NIP', () => {
      expect(formatNip('1234567890')).toBe('123-456-78-90');
      expect(formatNip('0123456789')).toBe('012-345-67-89');
    });

    it('should return original input for invalid length', () => {
      expect(formatNip('123456789')).toBe('123456789'); // 9 digits
      expect(formatNip('12345678901')).toBe('12345678901'); // 11 digits
      expect(formatNip('')).toBe('');
    });

    it('should clean and format mixed input', () => {
      expect(formatNip('123-456-78-90')).toBe('123-456-78-90');
      expect(formatNip('123 456 78 90')).toBe('123-456-78-90');
    });
  });

  describe('validateNip', () => {
    it('should validate correct NIP numbers', () => {
      // Generate a valid NIP for testing
      const exampleNip = generateExampleNip();
      const cleanExample = cleanNip(exampleNip);

      const result = validateNip(cleanExample);
      expect(result.valid).toBe(true);
      expect(result.error).toBeNull();
      expect(result.formatted).toBe(exampleNip);
      expect(result.cleaned).toBe(cleanExample);
    });

    it('should reject invalid inputs', () => {
      const testCases = [
        { input: '', expectedError: 'NIP cannot be empty' },
        { input: null, expectedError: 'NIP is required' },
        { input: undefined, expectedError: 'NIP is required' },
        { input: 123, expectedError: 'NIP is required' }
      ];

      testCases.forEach(({ input, expectedError }) => {
        const result = validateNip(input);
        expect(result.valid).toBe(false);
        expect(result.error).toBe(expectedError);
      });
    });

    it('should reject NIPs with wrong length', () => {
      const result1 = validateNip('123456789'); // 9 digits
      expect(result1.valid).toBe(false);
      expect(result1.error).toBe('NIP must contain exactly 10 digits');

      const result2 = validateNip('12345678901'); // 11 digits
      expect(result2.valid).toBe(false);
      expect(result2.error).toBe('NIP must contain exactly 10 digits');
    });

    it('should reject NIPs with all zeros', () => {
      const result = validateNip('0000000000');
      expect(result.valid).toBe(false);
      expect(result.error).toBe('NIP cannot consist only of zeros');
    });

    it('should reject NIPs with same digit repeated', () => {
      const result1 = validateNip('1111111111');
      expect(result1.valid).toBe(false);
      expect(result1.error).toBe('NIP cannot consist of the same digit repeated');

      const result2 = validateNip('5555555555');
      expect(result2.valid).toBe(false);
      expect(result2.error).toBe('NIP cannot consist of the same digit repeated');
    });

    it('should reject NIPs with invalid checksum', () => {
      // Use a NIP with wrong checksum
      const result = validateNip('1234567890'); // Last digit should be different
      expect(result.valid).toBe(false);
      expect(result.error).toContain('Invalid NIP checksum');
    });

    it('should handle formatted input', () => {
      const exampleNip = generateExampleNip();
      const result = validateNip(exampleNip);
      expect(result.valid).toBe(true);
      expect(result.formatted).toBe(exampleNip);
    });
  });

  describe('isValidNip', () => {
    it('should return boolean for valid NIP', () => {
      const exampleNip = generateExampleNip();
      expect(isValidNip(exampleNip)).toBe(true);
    });

    it('should return false for invalid NIP', () => {
      expect(isValidNip('1234567890')).toBe(false);
      expect(isValidNip('0000000000')).toBe(false);
      expect(isValidNip('123456789')).toBe(false);
      expect(isValidNip('')).toBe(false);
      expect(isValidNip(null)).toBe(false);
    });
  });

  describe('generateExampleNip', () => {
    it('should generate a valid NIP', () => {
      const exampleNip = generateExampleNip();
      expect(typeof exampleNip).toBe('string');
      expect(exampleNip).toMatch(/^\d{3}-\d{3}-\d{2}-\d{2}$/);
      expect(isValidNip(exampleNip)).toBe(true);
    });

    it('should generate consistent example', () => {
      const nip1 = generateExampleNip();
      const nip2 = generateExampleNip();
      expect(nip1).toBe(nip2); // Should be deterministic
    });
  });

  describe('getNipValidationRules', () => {
    it('should return array of validation rules', () => {
      const rules = getNipValidationRules();
      expect(Array.isArray(rules)).toBe(true);
      expect(rules.length).toBeGreaterThan(0);

      // Check that rules contain expected content
      const rulesText = rules.join(' ');
      expect(rulesText).toContain('10 digits');
      expect(rulesText).toContain('checksum');
      expect(rulesText).toContain('XXX-XXX-XX-XX');
    });
  });

  describe('Edge Cases', () => {
    it('should handle whitespace-only input', () => {
      const result = validateNip('   ');
      expect(result.valid).toBe(false);
      expect(result.error).toBe('NIP cannot be empty');
    });

    it('should handle mixed alphanumeric input', () => {
      const result = validateNip('abc123def456');
      expect(result.valid).toBe(false);
      expect(result.error).toBe('NIP must contain exactly 10 digits');
    });

    it('should handle very long input', () => {
      const longInput = '1'.repeat(100);
      const result = validateNip(longInput);
      expect(result.valid).toBe(false);
      expect(result.error).toBe('NIP must contain exactly 10 digits');
    });
  });

  describe('Checksum Algorithm', () => {
    it('should correctly calculate checksum for known examples', () => {
      // Test with a few manually calculated examples
      // Note: These are theoretical examples for testing the algorithm

      // We can't easily test specific checksums without knowing the internal algorithm,
      // but we can test that the validation is consistent
      const exampleNip = generateExampleNip();
      const cleaned = cleanNip(exampleNip);

      // If we modify the last digit, it should become invalid
      const modifiedNip = cleaned.slice(0, 9) + ((parseInt(cleaned[9]) + 1) % 10);
      const result = validateNip(modifiedNip);
      expect(result.valid).toBe(false);
      expect(result.error).toContain('Invalid NIP checksum');
    });
  });

  describe('Performance', () => {
    it('should validate NIPs quickly', () => {
      const start = performance.now();

      // Validate 1000 NIPs
      for (let i = 0; i < 1000; i++) {
        validateNip('1234567890');
      }

      const end = performance.now();
      const duration = end - start;

      // Should complete in reasonable time (less than 100ms for 1000 validations)
      expect(duration).toBeLessThan(100);
    });
  });
});

// Integration test with real-world scenarios
describe('NIP Validator Integration', () => {
  it('should handle complete workflow', () => {
    const userInput = '123 456 78 90';

    // Clean the input
    const cleaned = cleanNip(userInput);
    expect(cleaned).toBe('1234567890');

    // Validate
    const validation = validateNip(cleaned);
    expect(validation.valid).toBe(false); // This specific number is invalid

    // Test with valid example
    const validNip = generateExampleNip();
    const validCleaned = cleanNip(validNip);
    const validValidation = validateNip(validCleaned);

    expect(validValidation.valid).toBe(true);
    expect(validValidation.formatted).toBe(validNip);

    // Test formatting
    const formatted = formatNip(validCleaned);
    expect(formatted).toBe(validNip);
  });
});
