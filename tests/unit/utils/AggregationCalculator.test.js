/**
 * AggregationCalculator Unit Tests
 * Tests for statistical calculation utilities
 *
 * Epic: EPIC-003 - Data Display & Visualization
 * Story: STORY-3.2 - Grouping & Aggregation
 * Task: TASK-3.2.1 - Grouping Logic
 */

import { AggregationCalculator } from '../../../src/utils/AggregationCalculator.js';

describe('AggregationCalculator', () => {
  let calculator;
  let testValues;

  beforeEach(() => {
    calculator = new AggregationCalculator();
    testValues = [10, 20, 30, 40, 50];
  });

  describe('Configuration', () => {
    it('should initialize with default settings', () => {
      expect(calculator.precision).toBe(2);
      expect(calculator.supportedOperations).toContain('sum');
      expect(calculator.supportedOperations).toContain('average');
    });

    it('should configure precision', () => {
      calculator.configure({ precision: 4 });
      expect(calculator.precision).toBe(4);
    });

    it('should return supported operations', () => {
      const operations = calculator.getSupportedOperations();
      expect(operations).toContain('sum');
      expect(operations).toContain('count');
      expect(operations).toContain('average');
      expect(operations).toContain('min');
      expect(operations).toContain('max');
      expect(operations).toContain('median');
    });
  });

  describe('Input Validation', () => {
    it('should validate correct inputs', () => {
      const validation = calculator.validateInputs([1, 2, 3], 'sum');
      expect(validation.valid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should reject non-array values', () => {
      const validation = calculator.validateInputs('not-array', 'sum');
      expect(validation.valid).toBe(false);
      expect(validation.errors).toContain('Values must be an array');
    });

    it('should reject non-string operation', () => {
      const validation = calculator.validateInputs([1, 2, 3], 123);
      expect(validation.valid).toBe(false);
      expect(validation.errors).toContain('Operation must be a string');
    });

    it('should reject unsupported operation', () => {
      const validation = calculator.validateInputs([1, 2, 3], 'unsupported');
      expect(validation.valid).toBe(false);
      expect(validation.errors).toContain('Unsupported operation: unsupported');
    });
  });

  describe('Basic Calculations', () => {
    it('should calculate sum correctly', () => {
      const result = calculator.calculate(testValues, 'sum');
      expect(result).toBe(150);
    });

    it('should calculate count correctly', () => {
      const result = calculator.calculate(testValues, 'count');
      expect(result).toBe(5);
    });

    it('should calculate average correctly', () => {
      const result = calculator.calculate(testValues, 'average');
      expect(result).toBe(30);
    });

    it('should calculate minimum correctly', () => {
      const result = calculator.calculate(testValues, 'min');
      expect(result).toBe(10);
    });

    it('should calculate maximum correctly', () => {
      const result = calculator.calculate(testValues, 'max');
      expect(result).toBe(50);
    });

    it('should calculate median correctly for odd count', () => {
      const result = calculator.calculate(testValues, 'median');
      expect(result).toBe(30);
    });

    it('should calculate median correctly for even count', () => {
      const evenValues = [10, 20, 30, 40];
      const result = calculator.calculate(evenValues, 'median');
      expect(result).toBe(25); // (20 + 30) / 2
    });
  });

  describe('Advanced Calculations', () => {
    it('should calculate variance correctly', () => {
      const result = calculator.calculate(testValues, 'variance');
      expect(result).toBeCloseTo(250, 1); // Sample variance
    });

    it('should calculate standard deviation correctly', () => {
      const result = calculator.calculate(testValues, 'stddev');
      expect(result).toBeCloseTo(15.81, 1);
    });

    it('should calculate mode for single mode', () => {
      const valuesWithMode = [1, 2, 2, 3, 4];
      const result = calculator.calculate(valuesWithMode, 'mode');
      expect(result).toBe(2);
    });

    it('should calculate mode for multiple modes', () => {
      const valuesWithMultipleModes = [1, 1, 2, 2, 3];
      const result = calculator.calculate(valuesWithMultipleModes, 'mode');
      expect(Array.isArray(result)).toBe(true);
      expect(result).toContain(1);
      expect(result).toContain(2);
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty array', () => {
      const result = calculator.calculate([], 'sum');
      expect(result).toBeNull();
    });

    it('should handle array with non-numeric values', () => {
      const mixedValues = [10, 'abc', 20, null, 30, undefined];
      const result = calculator.calculate(mixedValues, 'sum');
      expect(result).toBe(60); // Only numeric values: 10 + 20 + 30
    });

    it('should handle single value', () => {
      const singleValue = [42];
      expect(calculator.calculate(singleValue, 'sum')).toBe(42);
      expect(calculator.calculate(singleValue, 'average')).toBe(42);
      expect(calculator.calculate(singleValue, 'min')).toBe(42);
      expect(calculator.calculate(singleValue, 'max')).toBe(42);
      expect(calculator.calculate(singleValue, 'median')).toBe(42);
      expect(calculator.calculate(singleValue, 'variance')).toBe(0);
    });

    it('should handle negative values', () => {
      const negativeValues = [-10, -5, 0, 5, 10];
      expect(calculator.calculate(negativeValues, 'sum')).toBe(0);
      expect(calculator.calculate(negativeValues, 'average')).toBe(0);
      expect(calculator.calculate(negativeValues, 'min')).toBe(-10);
      expect(calculator.calculate(negativeValues, 'max')).toBe(10);
    });

    it('should handle decimal values', () => {
      const decimalValues = [1.1, 2.2, 3.3];
      const result = calculator.calculate(decimalValues, 'sum');
      expect(result).toBeCloseTo(6.6, 2);
    });
  });

  describe('Multiple Calculations', () => {
    it('should calculate multiple operations at once', () => {
      const operations = ['sum', 'count', 'average', 'min', 'max'];
      const results = calculator.calculateMultiple(testValues, operations);

      expect(results.sum).toBe(150);
      expect(results.count).toBe(5);
      expect(results.average).toBe(30);
      expect(results.min).toBe(10);
      expect(results.max).toBe(50);
    });

    it('should handle errors in multiple calculations', () => {
      const operations = ['sum', 'invalid_operation'];
      const results = calculator.calculateMultiple(testValues, operations);

      expect(results.sum).toBe(150);
      expect(results.invalid_operation).toBeNull();
    });
  });

  describe('Percentiles', () => {
    it('should calculate standard percentiles', () => {
      const values = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
      const percentiles = calculator.calculatePercentiles(values);

      expect(percentiles.p25).toBeDefined();
      expect(percentiles.p50).toBeDefined(); // Median
      expect(percentiles.p75).toBeDefined();
      expect(percentiles.p90).toBeDefined();
      expect(percentiles.p95).toBeDefined();
      expect(percentiles.p99).toBeDefined();
    });

    it('should calculate custom percentiles', () => {
      const values = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
      const percentiles = calculator.calculatePercentiles(values, [10, 50, 90]);

      expect(percentiles.p10).toBeDefined();
      expect(percentiles.p50).toBeDefined();
      expect(percentiles.p90).toBeDefined();
      expect(percentiles.p25).toBeUndefined();
    });

    it('should handle empty array for percentiles', () => {
      const percentiles = calculator.calculatePercentiles([]);
      expect(Object.keys(percentiles)).toHaveLength(0);
    });

    it('should ignore invalid percentile values', () => {
      const values = [1, 2, 3, 4, 5];
      const percentiles = calculator.calculatePercentiles(values, [-10, 50, 150]);

      expect(percentiles.p50).toBeDefined();
      expect(percentiles['p-10']).toBeUndefined();
      expect(percentiles.p150).toBeUndefined();
    });
  });

  describe('Quartiles', () => {
    it('should calculate quartiles correctly', () => {
      const values = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
      const quartiles = calculator.calculateQuartiles(values);

      expect(quartiles.q1).toBeDefined();
      expect(quartiles.q2).toBeDefined(); // Median
      expect(quartiles.q3).toBeDefined();
      expect(quartiles.iqr).toBeDefined(); // Interquartile range
      expect(quartiles.iqr).toBe(quartiles.q3 - quartiles.q1);
    });
  });

  describe('Summary Statistics', () => {
    it('should calculate complete summary', () => {
      const summary = calculator.calculateSummary(testValues);

      expect(summary.count).toBe(5);
      expect(summary.sum).toBe(150);
      expect(summary.average).toBe(30);
      expect(summary.min).toBe(10);
      expect(summary.max).toBe(50);
      expect(summary.median).toBe(30);
      expect(summary.variance).toBeDefined();
      expect(summary.stddev).toBeDefined();
      expect(summary.q1).toBeDefined();
      expect(summary.q2).toBeDefined();
      expect(summary.q3).toBeDefined();
      expect(summary.iqr).toBeDefined();
      expect(summary.range).toBe(40); // max - min
      expect(summary.coefficientOfVariation).toBeDefined();
    });

    it('should return empty summary for empty array', () => {
      const summary = calculator.calculateSummary([]);
      const emptySummary = calculator.getEmptySummary();

      expect(summary).toEqual(emptySummary);
    });

    it('should return empty summary for non-numeric array', () => {
      const summary = calculator.calculateSummary(['a', 'b', 'c']);
      const emptySummary = calculator.getEmptySummary();

      expect(summary).toEqual(emptySummary);
    });
  });

  describe('Result Rounding', () => {
    it('should round results to configured precision', () => {
      calculator.configure({ precision: 2 });
      const result = calculator.roundResult(3.14159);
      expect(result).toBe(3.14);
    });

    it('should handle zero precision', () => {
      calculator.configure({ precision: 0 });
      const result = calculator.roundResult(3.14159);
      expect(result).toBe(3);
    });

    it('should handle invalid numbers', () => {
      expect(calculator.roundResult(NaN)).toBe(0);
      expect(calculator.roundResult('not-a-number')).toBe(0);
      expect(calculator.roundResult(null)).toBe(0);
      expect(calculator.roundResult(undefined)).toBe(0);
    });
  });

  describe('Error Handling', () => {
    it('should throw error for unsupported operation', () => {
      expect(() => {
        calculator.calculate(testValues, 'unsupported');
      }).toThrow('Unsupported operation: unsupported');
    });

    it('should throw error for non-array input', () => {
      expect(() => {
        calculator.calculate('not-array', 'sum');
      }).toThrow('Values must be an array');
    });
  });
});
