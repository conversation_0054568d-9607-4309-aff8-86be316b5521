import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import DataExporter from '../../../src/popup/utils/DataExporter';

// Mock DOM APIs
const mockCreateObjectURL = vi.fn();
const mockRevokeObjectURL = vi.fn();
const mockClick = vi.fn();
const mockAppendChild = vi.fn();
const mockRemoveChild = vi.fn();

global.URL = {
  createObjectURL: mockCreateObjectURL,
  revokeObjectURL: mockRevokeObjectURL
};

global.Blob = vi.fn().mockImplementation((content, options) => ({
  content,
  options
}));

// Mock document
const mockElement = {
  href: '',
  download: '',
  click: mockClick
};

Object.defineProperty(global, 'document', {
  value: {
    createElement: vi.fn().mockReturnValue(mockElement),
    body: {
      appendChild: mockAppendChild,
      removeChild: mockRemoveChild
    }
  },
  writable: true
});

describe('DataExporter', () => {
  const mockInvoices = [
    {
      id: '1',
      number: 'INV-001',
      date: '2024-01-15',
      seller_name: 'Company A',
      seller_tax_no: '1234567890',
      buyer_name: 'Company B',
      buyer_tax_no: '0987654321',
      total_net: '100.00',
      total_vat: '23.00',
      total_gross: '123.00',
      currency: 'PLN',
      extractionMethod: 'pdf_text',
      filename: 'invoice1.pdf',
      fileSize: 1024000,
      processedAt: '2024-01-15T10:00:00Z'
    },
    {
      id: '2',
      number: 'INV-002',
      date: '2024-01-20',
      seller_name: 'Company C',
      seller_tax_no: '1111111111',
      buyer_name: 'Company D',
      buyer_tax_no: '2222222222',
      total_net: '200.00',
      total_vat: '46.00',
      total_gross: '246.00',
      currency: 'PLN',
      extractionMethod: 'ocr',
      filename: 'invoice2.pdf',
      fileSize: 2048000,
      processedAt: '2024-01-20T10:00:00Z'
    }
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    mockCreateObjectURL.mockReturnValue('blob:mock-url');
    // Reset mock element properties
    mockElement.href = '';
    mockElement.download = '';
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('exportToCSV', () => {
    it('throws error for empty invoice list', async () => {
      await expect(DataExporter.exportToCSV([])).rejects.toThrow('No invoices to export');
    });

    it('throws error for null invoice list', async () => {
      await expect(DataExporter.exportToCSV(null)).rejects.toThrow('No invoices to export');
    });

    it('escapes CSV fields correctly', () => {
      const fieldWithComma = 'Company, Inc.';
      const fieldWithQuotes = 'Company "Best" Ltd.';
      const fieldWithNewline = 'Company\nMultiline';

      expect(DataExporter.escapeCSVField(fieldWithComma)).toBe('"Company, Inc."');
      expect(DataExporter.escapeCSVField(fieldWithQuotes)).toBe('"Company ""Best"" Ltd."');
      expect(DataExporter.escapeCSVField(fieldWithNewline)).toBe('"Company\nMultiline"');
    });

    it('handles null and undefined fields', () => {
      expect(DataExporter.escapeCSVField(null)).toBe('');
      expect(DataExporter.escapeCSVField(undefined)).toBe('');
    });
  });

  describe('exportToJSON', () => {
    it('throws error for empty invoice list', async () => {
      await expect(DataExporter.exportToJSON([])).rejects.toThrow('No invoices to export');
    });
  });

  describe('exportSummaryToCSV', () => {
    it('throws error for empty invoice list', async () => {
      await expect(DataExporter.exportSummaryToCSV([])).rejects.toThrow('No invoices to export');
    });
  });

  describe('groupInvoices', () => {
    it('groups invoices by month correctly', () => {
      const groups = DataExporter.groupInvoices(mockInvoices, 'month');

      expect(groups).toHaveProperty('2024-01');
      expect(groups['2024-01']).toHaveProperty('count', 2);
      expect(groups['2024-01']).toHaveProperty('totalNet', 300);
      expect(groups['2024-01']).toHaveProperty('totalVat', 69);
      expect(groups['2024-01']).toHaveProperty('totalGross', 369);
    });

    it('groups invoices by year correctly', () => {
      const groups = DataExporter.groupInvoices(mockInvoices, 'year');

      expect(groups).toHaveProperty('2024');
      expect(groups['2024']).toHaveProperty('count', 2);
    });

    it('groups invoices by quarter correctly', () => {
      const groups = DataExporter.groupInvoices(mockInvoices, 'quarter');

      expect(groups).toHaveProperty('2024 Q1');
      expect(groups['2024 Q1']).toHaveProperty('count', 2);
    });
  });

  describe('getGroupKey', () => {
    const testInvoice = {
      date: '2024-01-15',
      processedAt: '2024-01-15T10:00:00Z'
    };

    it('generates correct group key for year', () => {
      expect(DataExporter.getGroupKey(testInvoice, 'year')).toBe('2024');
    });

    it('generates correct group key for quarter', () => {
      expect(DataExporter.getGroupKey(testInvoice, 'quarter')).toBe('2024 Q1');
    });

    it('generates correct group key for month', () => {
      expect(DataExporter.getGroupKey(testInvoice, 'month')).toBe('2024-01');
    });

    it('generates correct group key for week', () => {
      const groupKey = DataExporter.getGroupKey(testInvoice, 'week');
      expect(groupKey).toMatch(/2024 W\d+/);
    });

    it('generates correct group key for day', () => {
      expect(DataExporter.getGroupKey(testInvoice, 'day')).toBe('2024-01-15');
    });

    it('falls back to processedAt when date is missing', () => {
      const invoiceWithoutDate = {
        processedAt: '2024-01-15T10:00:00Z'
      };
      expect(DataExporter.getGroupKey(invoiceWithoutDate, 'year')).toBe('2024');
    });
  });

  describe('getWeekNumber', () => {
    it('calculates week number correctly', () => {
      const date = new Date('2024-01-15');
      const weekNumber = DataExporter.getWeekNumber(date);
      expect(typeof weekNumber).toBe('number');
      expect(weekNumber).toBeGreaterThan(0);
      expect(weekNumber).toBeLessThanOrEqual(53);
    });
  });

  describe('formatNumber', () => {
    it('formats numbers with correct decimal places', () => {
      expect(DataExporter.formatNumber(123.456)).toBe('123.46');
      expect(DataExporter.formatNumber(123.456, 1)).toBe('123.5');
      expect(DataExporter.formatNumber('123.456')).toBe('123.46');
    });

    it('handles invalid numbers', () => {
      expect(DataExporter.formatNumber(null)).toBe('0.00');
      expect(DataExporter.formatNumber(undefined)).toBe('0.00');
      expect(DataExporter.formatNumber('invalid')).toBe('0.00');
    });
  });

});
