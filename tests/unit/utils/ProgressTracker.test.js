import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import ProgressTracker, {
  DEFAULT_STAGES,
  estimateFileProcessingTime
} from '../../../src/utils/ProgressTracker.js';

describe('ProgressTracker', () => {
  let tracker;

  beforeEach(() => {
    vi.useFakeTimers();
    tracker = new ProgressTracker();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('Initialization', () => {
    it('initializes with default stages', () => {
      expect(tracker.stages).toEqual(DEFAULT_STAGES);
      expect(tracker.stageOrder).toEqual(Object.keys(DEFAULT_STAGES));
      expect(tracker.currentStage).toBe(0);
      expect(tracker.stageProgress).toBe(0);
    });

    it('initializes with custom stages', () => {
      const customStages = {
        step1: { weight: 0.5, name: 'Step 1' },
        step2: { weight: 0.5, name: 'Step 2' }
      };

      const customTracker = new ProgressTracker(customStages);
      expect(customTracker.stages).toEqual(customStages);
      expect(customTracker.stageOrder).toEqual(['step1', 'step2']);
    });
  });

  describe('Progress Tracking', () => {
    it('starts tracking correctly', () => {
      const startTime = Date.now();
      vi.setSystemTime(startTime);

      tracker.start();

      expect(tracker.startTime).toBe(startTime);
      expect(tracker.currentStage).toBe(0);
      expect(tracker.stageProgress).toBe(0);
      expect(tracker.stageStartTimes[tracker.stageOrder[0]]).toBe(startTime);
    });

    it('updates stage progress correctly', () => {
      tracker.start();

      const overallProgress = tracker.updateStageProgress(50);

      expect(tracker.stageProgress).toBe(50);
      expect(overallProgress).toBeGreaterThan(0);
    });

    it('clamps progress to valid range', () => {
      tracker.start();

      tracker.updateStageProgress(-10);
      expect(tracker.stageProgress).toBe(0);

      tracker.updateStageProgress(150);
      expect(tracker.stageProgress).toBe(100);
    });
  });

  describe('Stage Management', () => {
    it('moves to next stage correctly', () => {
      tracker.start();
      const initialStage = tracker.currentStage;

      tracker.nextStage();

      expect(tracker.currentStage).toBe(initialStage + 1);
      expect(tracker.stageProgress).toBe(0);
    });

    it('moves to specific stage by name', () => {
      tracker.start();

      tracker.nextStage('analyzing');

      const analyzingIndex = tracker.stageOrder.indexOf('analyzing');
      expect(tracker.currentStage).toBe(analyzingIndex);
    });

    it('records stage completion times', () => {
      const startTime = Date.now();
      vi.setSystemTime(startTime);

      tracker.start();

      vi.advanceTimersByTime(1000);
      tracker.nextStage();

      const firstStageName = tracker.stageOrder[0];
      expect(tracker.stageDurations[firstStageName]).toBe(1000);
    });

    it('does not exceed maximum stage index', () => {
      tracker.start();
      const maxStage = tracker.stageOrder.length - 1;

      // Move to last stage
      for (let i = 0; i < tracker.stageOrder.length; i++) {
        tracker.nextStage();
      }

      expect(tracker.currentStage).toBe(maxStage);
    });
  });

  describe('Progress Calculation', () => {
    it('calculates overall progress correctly', () => {
      tracker.start();

      // Complete first stage (weight: 0.2)
      tracker.updateStageProgress(100);
      tracker.nextStage();

      // Half complete second stage (weight: 0.1)
      tracker.updateStageProgress(50);

      const expectedProgress = (0.2 * 100) + (0.1 * 50);
      expect(tracker.calculateOverallProgress()).toBe(expectedProgress);
    });

    it('returns 100% when all stages complete', () => {
      tracker.start();

      // Complete all stages
      tracker.stageOrder.forEach(() => {
        tracker.updateStageProgress(100);
        tracker.nextStage();
      });

      expect(tracker.calculateOverallProgress()).toBe(100);
    });

    it('handles stages with zero weight', () => {
      const customStages = {
        stage1: { weight: 0, name: 'Stage 1' },
        stage2: { weight: 1, name: 'Stage 2' }
      };

      const customTracker = new ProgressTracker(customStages);
      customTracker.start();

      customTracker.updateStageProgress(100);
      customTracker.nextStage();
      customTracker.updateStageProgress(50);

      expect(customTracker.calculateOverallProgress()).toBe(50);
    });
  });

  describe('Current Stage Information', () => {
    it('returns current stage information', () => {
      tracker.start();
      tracker.updateStageProgress(75);

      const currentStage = tracker.getCurrentStage();

      expect(currentStage.name).toBe('uploading');
      expect(currentStage.displayName).toBe('Uploading');
      expect(currentStage.progress).toBe(75);
      expect(currentStage.weight).toBe(0.2);
    });

    it('returns complete stage when finished', () => {
      tracker.start();

      // Move past all stages
      tracker.currentStage = tracker.stageOrder.length;

      const currentStage = tracker.getCurrentStage();

      expect(currentStage.name).toBe('complete');
      expect(currentStage.displayName).toBe('Complete');
      expect(currentStage.progress).toBe(100);
    });
  });

  describe('Time Estimation', () => {
    it('estimates remaining time correctly', () => {
      const startTime = Date.now();
      vi.setSystemTime(startTime);

      tracker.start();

      // Simulate 2 seconds elapsed with 50% progress
      vi.advanceTimersByTime(2000);
      tracker.updateStageProgress(50);

      // Move to next stage to get some overall progress
      tracker.nextStage();
      tracker.updateStageProgress(0);

      const remainingTime = tracker.estimateRemainingTime();
      expect(remainingTime).toBeGreaterThan(0);
    });

    it('returns null for no elapsed time', () => {
      const remainingTime = tracker.estimateRemainingTime();
      expect(remainingTime).toBeNull();
    });

    it('returns null for zero progress', () => {
      tracker.start();

      const remainingTime = tracker.estimateRemainingTime();
      expect(remainingTime).toBeNull();
    });
  });

  describe('Performance Metrics', () => {
    it('provides comprehensive performance metrics', () => {
      const startTime = Date.now();
      vi.setSystemTime(startTime);

      tracker.start();

      vi.advanceTimersByTime(3000);
      tracker.updateStageProgress(100);
      tracker.nextStage();

      const metrics = tracker.getPerformanceMetrics();

      expect(metrics.totalElapsedTime).toBe(3);
      expect(metrics.overallProgress).toBeGreaterThan(0);
      expect(metrics.currentStage).toBeDefined();
      expect(metrics.stageDurations).toBeDefined();
    });

    it('calculates average stage time correctly', () => {
      tracker.start();

      vi.advanceTimersByTime(1000);
      tracker.nextStage();

      vi.advanceTimersByTime(2000);
      tracker.nextStage();

      const avgTime = tracker.getAverageStageTime();
      expect(avgTime).toBe(1.5); // (1000 + 2000) / 2 / 1000
    });
  });

  describe('Completion Detection', () => {
    it('detects completion correctly', () => {
      tracker.start();

      expect(tracker.isComplete()).toBe(false);

      // Complete all stages
      tracker.stageOrder.forEach(() => {
        tracker.updateStageProgress(100);
        tracker.nextStage();
      });

      expect(tracker.isComplete()).toBe(true);
    });
  });

  describe('Reset Functionality', () => {
    it('resets to initial state', () => {
      tracker.start();
      tracker.updateStageProgress(50);
      tracker.nextStage();

      tracker.reset();

      expect(tracker.currentStage).toBe(0);
      expect(tracker.stageProgress).toBe(0);
      expect(tracker.startTime).toBeNull();
      expect(tracker.stageStartTimes).toEqual({});
      expect(tracker.stageDurations).toEqual({});
    });
  });

  describe('Progress Callback', () => {
    it('creates progress callback correctly', () => {
      const mockCallback = vi.fn();
      tracker.start();

      const progressCallback = tracker.createProgressCallback(mockCallback);

      progressCallback(75);

      expect(mockCallback).toHaveBeenCalledWith({
        overallProgress: expect.any(Number),
        stageProgress: 75,
        currentStage: expect.any(Object),
        metrics: expect.any(Object),
        isComplete: false
      });
    });

    it('handles stage changes in callback', () => {
      const mockCallback = vi.fn();
      tracker.start();

      const progressCallback = tracker.createProgressCallback(mockCallback);

      progressCallback(50, 'analyzing');

      const analyzingIndex = tracker.stageOrder.indexOf('analyzing');
      expect(tracker.currentStage).toBe(analyzingIndex);
      expect(tracker.stageProgress).toBe(50);
    });
  });
});

describe('estimateFileProcessingTime', () => {
  it('estimates time for PDF files', () => {
    const pdfFile = new File(['content'], 'test.pdf', { type: 'application/pdf' });
    Object.defineProperty(pdfFile, 'size', { value: 1024 * 1024 }); // 1MB

    const estimatedTime = estimateFileProcessingTime(pdfFile);
    expect(estimatedTime).toBe(2); // 2 seconds per MB for PDF
  });

  it('estimates time for image files', () => {
    const imageFile = new File(['content'], 'test.jpg', { type: 'image/jpeg' });
    Object.defineProperty(imageFile, 'size', { value: 1024 * 1024 }); // 1MB

    const estimatedTime = estimateFileProcessingTime(imageFile);
    expect(estimatedTime).toBe(8); // 5 * 1.5 complexity multiplier
  });

  it('applies complexity multiplier for large files', () => {
    const largeFile = new File(['content'], 'test.pdf', { type: 'application/pdf' });
    Object.defineProperty(largeFile, 'size', { value: 6 * 1024 * 1024 }); // 6MB

    const estimatedTime = estimateFileProcessingTime(largeFile);
    expect(estimatedTime).toBe(14); // 6 * 2 * 1.2 = 14.4, rounded to 14
  });

  it('returns minimum 1 second', () => {
    const tinyFile = new File([''], 'tiny.pdf', { type: 'application/pdf' });
    Object.defineProperty(tinyFile, 'size', { value: 100 }); // 100 bytes

    const estimatedTime = estimateFileProcessingTime(tinyFile);
    expect(estimatedTime).toBe(1);
  });

  it('handles unknown file types', () => {
    const unknownFile = new File(['content'], 'test.xyz', { type: 'application/unknown' });
    Object.defineProperty(unknownFile, 'size', { value: 1024 * 1024 }); // 1MB

    const estimatedTime = estimateFileProcessingTime(unknownFile);
    expect(estimatedTime).toBe(3); // Default 3 seconds per MB
  });

  it('returns 0 for null file', () => {
    const estimatedTime = estimateFileProcessingTime(null);
    expect(estimatedTime).toBe(0);
  });
});
