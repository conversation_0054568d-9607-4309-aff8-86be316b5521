/**
 * GroupingEngine Unit Tests
 * Tests for core grouping and aggregation logic
 *
 * Epic: EPIC-003 - Data Display & Visualization
 * Story: STORY-3.2 - Grouping & Aggregation
 * Task: TASK-3.2.1 - Grouping Logic
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { GroupingEngine } from '../../../src/utils/GroupingEngine.js';

// Mock dependencies
vi.mock('../../../src/utils/ProcessingLogger.js', () => ({
  ProcessingLogger: vi.fn().mockImplementation(() => ({
    configure: vi.fn(),
    info: vi.fn(),
    debug: vi.fn(),
    error: vi.fn(),
    warn: vi.fn()
  }))
}));

vi.mock('../../../src/utils/DateGrouping.js', () => ({
  DateGrouping: vi.fn().mockImplementation(() => ({
    configure: vi.fn(),
    getYearKey: vi.fn((date) => date.getFullYear().toString()),
    getQuarterKey: vi.fn((date) => `${date.getFullYear()}-Q${Math.ceil((date.getMonth() + 1) / 3)}`),
    getMonthKey: vi.fn((date) => `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`),
    getWeekKey: vi.fn((date) => `${date.getFullYear()}-W01`),
    getDayKey: vi.fn((date) => date.toISOString().split('T')[0]),
    getDateRangeForGroup: vi.fn(() => ({ start: new Date(), end: new Date() }))
  }))
}));

vi.mock('../../../src/utils/AggregationCalculator.js', () => ({
  AggregationCalculator: vi.fn().mockImplementation(() => ({
    configure: vi.fn(),
    calculate: vi.fn((values, operation) => {
      switch (operation) {
        case 'sum': return values.reduce((a, b) => a + b, 0);
        case 'count': return values.length;
        case 'average': return values.reduce((a, b) => a + b, 0) / values.length;
        case 'min': return Math.min(...values);
        case 'max': return Math.max(...values);
        case 'median': return values.sort((a, b) => a - b)[Math.floor(values.length / 2)];
        default: return 0;
      }
    })
  }))
}));

describe('GroupingEngine', () => {
  let groupingEngine;
  let mockInvoices;

  beforeEach(() => {
    groupingEngine = new GroupingEngine();

    // Sample invoice data
    mockInvoices = [
      {
        id: '1',
        issue_date: '2024-01-15',
        total_net: 100,
        total_vat: 23,
        total_gross: 123,
        currency: 'PLN',
        seller_name: 'Company A'
      },
      {
        id: '2',
        issue_date: '2024-01-20',
        total_net: 200,
        total_vat: 46,
        total_gross: 246,
        currency: 'PLN',
        seller_name: 'Company B'
      },
      {
        id: '3',
        issue_date: '2024-02-10',
        total_net: 150,
        total_vat: 34.5,
        total_gross: 184.5,
        currency: 'EUR',
        seller_name: 'Company C'
      }
    ];
  });

  describe('Configuration', () => {
    it('should configure with default settings', () => {
      expect(groupingEngine.supportedGroupTypes).toContain('year');
      expect(groupingEngine.supportedGroupTypes).toContain('month');
      expect(groupingEngine.supportedAggregations).toContain('sum');
      expect(groupingEngine.cacheMaxAge).toBe(5 * 60 * 1000);
    });

    it('should configure with custom settings', () => {
      const config = {
        cacheMaxAge: 10 * 60 * 1000,
        logging: { logLevel: 'debug' }
      };

      groupingEngine.configure(config);
      expect(groupingEngine.cacheMaxAge).toBe(10 * 60 * 1000);
    });
  });

  describe('Input Validation', () => {
    it('should validate correct inputs', () => {
      const validation = groupingEngine.validateInputs(mockInvoices, {
        groupBy: 'month',
        aggregations: ['sum', 'count']
      });

      expect(validation.valid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should reject invalid inputs', () => {
      const validation = groupingEngine.validateInputs('not-an-array', {
        groupBy: 'invalid',
        aggregations: 'not-an-array'
      });

      expect(validation.valid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
    });

    it('should reject empty invoice array', () => {
      const validation = groupingEngine.validateInputs([], {});

      expect(validation.valid).toBe(false);
      expect(validation.errors).toContain('Invoice array cannot be empty');
    });

    it('should reject unsupported groupBy type', () => {
      const validation = groupingEngine.validateInputs(mockInvoices, {
        groupBy: 'unsupported'
      });

      expect(validation.valid).toBe(false);
      expect(validation.errors).toContain('Unsupported groupBy type: unsupported');
    });
  });

  describe('Invoice Validation and Normalization', () => {
    it('should validate valid invoices', () => {
      const isValid = groupingEngine.isValidInvoice(mockInvoices[0], 'issue_date');
      expect(isValid).toBe(true);
    });

    it('should reject invoices without date', () => {
      const invalidInvoice = { ...mockInvoices[0] };
      delete invalidInvoice.issue_date;

      const isValid = groupingEngine.isValidInvoice(invalidInvoice, 'issue_date');
      expect(isValid).toBe(false);
    });

    it('should reject invoices without numeric data', () => {
      const invalidInvoice = {
        id: '1',
        issue_date: '2024-01-15'
        // No numeric fields
      };

      const isValid = groupingEngine.isValidInvoice(invalidInvoice, 'issue_date');
      expect(isValid).toBe(false);
    });

    it('should normalize invoice data correctly', () => {
      const normalized = groupingEngine.normalizeInvoice(mockInvoices[0], 'issue_date');

      expect(normalized.groupingDate).toBeInstanceOf(Date);
      expect(normalized.total_net).toBe(100);
      expect(normalized.currency).toBe('PLN');
      expect(normalized.groupingMetadata).toBeDefined();
    });

    it('should handle missing currency', () => {
      const invoiceWithoutCurrency = { ...mockInvoices[0] };
      delete invoiceWithoutCurrency.currency;

      const normalized = groupingEngine.normalizeInvoice(invoiceWithoutCurrency, 'issue_date');
      expect(normalized.currency).toBe('PLN');
    });
  });

  describe('Data Preparation', () => {
    it('should prepare valid invoices', () => {
      const prepared = groupingEngine.prepareInvoiceData(mockInvoices, 'issue_date', 'test-op');

      expect(prepared).toHaveLength(3);
      expect(prepared[0].groupingDate).toBeInstanceOf(Date);
      expect(prepared[0].groupingMetadata).toBeDefined();
    });

    it('should filter out invalid invoices', () => {
      const mixedInvoices = [
        ...mockInvoices,
        { id: '4' }, // Invalid - no date or numeric data
        { id: '5', issue_date: 'invalid-date' } // Invalid - bad date
      ];

      const prepared = groupingEngine.prepareInvoiceData(mixedInvoices, 'issue_date', 'test-op');
      expect(prepared).toHaveLength(3); // Only valid invoices
    });
  });

  describe('Currency Grouping', () => {
    it('should group invoices by currency', () => {
      const currencyGroups = groupingEngine.groupByCurrency(mockInvoices);

      expect(currencyGroups.PLN).toHaveLength(2);
      expect(currencyGroups.EUR).toHaveLength(1);
    });

    it('should handle invoices without currency', () => {
      const invoicesWithoutCurrency = mockInvoices.map(inv => {
        const copy = { ...inv };
        delete copy.currency;
        return copy;
      });

      const currencyGroups = groupingEngine.groupByCurrency(invoicesWithoutCurrency);
      expect(currencyGroups.PLN).toHaveLength(3); // Default to PLN
    });
  });

  describe('Cache Management', () => {
    it('should generate consistent cache keys', () => {
      const options = { groupBy: 'month' };
      const key1 = groupingEngine.generateCacheKey(mockInvoices, options);
      const key2 = groupingEngine.generateCacheKey(mockInvoices, options);

      expect(key1).toBe(key2);
    });

    it('should generate different cache keys for different data', () => {
      const options = { groupBy: 'month' };
      const key1 = groupingEngine.generateCacheKey(mockInvoices, options);
      const key2 = groupingEngine.generateCacheKey(mockInvoices.slice(0, 1), options);

      expect(key1).not.toBe(key2);
    });

    it('should store and retrieve cached results', () => {
      const cacheKey = 'test-key';
      const testData = { test: 'data' };

      groupingEngine.setCachedResult(cacheKey, testData);
      const retrieved = groupingEngine.getCachedResult(cacheKey);

      expect(retrieved).toEqual(testData);
    });

    it('should return null for expired cache', () => {
      const cacheKey = 'test-key';
      const testData = { test: 'data' };

      // Set cache with very short max age
      groupingEngine.cacheMaxAge = 1; // 1ms
      groupingEngine.setCachedResult(cacheKey, testData);

      // Wait for expiration
      setTimeout(() => {
        const retrieved = groupingEngine.getCachedResult(cacheKey);
        expect(retrieved).toBeNull();
      }, 10);
    });
  });

  describe('Hash Functions', () => {
    it('should generate consistent hashes', () => {
      const hash1 = groupingEngine.simpleHash('test string');
      const hash2 = groupingEngine.simpleHash('test string');

      expect(hash1).toBe(hash2);
    });

    it('should generate different hashes for different strings', () => {
      const hash1 = groupingEngine.simpleHash('test string 1');
      const hash2 = groupingEngine.simpleHash('test string 2');

      expect(hash1).not.toBe(hash2);
    });

    it('should hash objects consistently', () => {
      const obj = { key: 'value', number: 42 };
      const hash1 = groupingEngine.hashObject(obj);
      const hash2 = groupingEngine.hashObject(obj);

      expect(hash1).toBe(hash2);
    });

    it('should hash invoice arrays', () => {
      const hash1 = groupingEngine.hashInvoices(mockInvoices);
      const hash2 = groupingEngine.hashInvoices(mockInvoices);

      expect(hash1).toBe(hash2);
    });
  });

  describe('Operation ID Generation', () => {
    it('should generate unique operation IDs', () => {
      const id1 = groupingEngine.generateOperationId();
      const id2 = groupingEngine.generateOperationId();

      expect(id1).not.toBe(id2);
      expect(id1).toMatch(/^grp_\d+_[a-z0-9]+$/);
    });
  });

  describe('Error Handling', () => {
    it('should handle errors gracefully in groupInvoices', async () => {
      // Test with invalid inputs
      await expect(groupingEngine.groupInvoices('invalid', {}))
        .rejects.toThrow('Invalid inputs');
    });

    it('should handle missing dependencies gracefully', () => {
      // This tests the robustness of the class when dependencies fail
      expect(() => new GroupingEngine()).not.toThrow();
    });
  });
});
