/**
 * @vitest-environment node
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { UploadTracker } from '../../../src/utils/UploadTracker.js';

describe('UploadTracker', () => {
  let tracker;
  let mockFile;

  beforeEach(() => {
    tracker = new UploadTracker();
    mockFile = {
      name: 'test.pdf',
      size: 1024,
      type: 'application/pdf',
      lastModified: Date.now()
    };
  });

  describe('UUID Generation', () => {
    it('should generate valid UUIDs', () => {
      const uuid1 = tracker.generateUploadId();
      const uuid2 = tracker.generateUploadId();

      expect(uuid1).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/);
      expect(uuid2).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/);
      expect(uuid1).not.toBe(uuid2);
    });

    it('should generate short IDs correctly', () => {
      const uuid = 'abcd1234-5678-4abc-9def-123456789012';
      const shortId = tracker.getShortId(uuid);

      expect(shortId).toBe('abcd1234');
      expect(shortId).toHaveLength(8);
    });
  });

  describe('Upload Lifecycle', () => {
    it('should start upload tracking', () => {
      const uploadId = tracker.startUpload(mockFile, { option: 'value' });

      expect(uploadId).toBeDefined();
      expect(tracker.getActiveUploads()).toHaveLength(1);

      const upload = tracker.getUpload(uploadId);
      expect(upload).toMatchObject({
        uploadId,
        status: 'started',
        file: {
          name: 'test.pdf',
          size: 1024,
          type: 'application/pdf'
        },
        options: { option: 'value' }
      });
    });

    it('should update upload stages', () => {
      const uploadId = tracker.startUpload(mockFile);

      tracker.updateStage(uploadId, 'processing', { progress: 50 });
      tracker.updateStage(uploadId, 'analysis', { confidence: 0.95 });

      const upload = tracker.getUpload(uploadId);
      expect(upload.stages).toHaveLength(2);
      expect(upload.currentStage).toBe('analysis');
      expect(upload.stages[0]).toMatchObject({
        stage: 'processing',
        progress: 50
      });
    });

    it('should complete upload successfully', () => {
      const uploadId = tracker.startUpload(mockFile);

      tracker.completeUpload(uploadId, {
        success: true,
        data: { extracted: 'text' }
      });

      expect(tracker.getActiveUploads()).toHaveLength(0);
      expect(tracker.getCompletedUploads()).toHaveLength(1);

      const upload = tracker.getUpload(uploadId);
      expect(upload.status).toBe('completed');
      expect(upload.result.success).toBe(true);
      expect(upload.totalDurationMs).toBeGreaterThanOrEqual(0);
    });

    it('should handle upload failure', () => {
      const uploadId = tracker.startUpload(mockFile);
      const error = new Error('Processing failed');

      tracker.failUpload(uploadId, error);

      expect(tracker.getActiveUploads()).toHaveLength(0);
      expect(tracker.getCompletedUploads()).toHaveLength(1);

      const upload = tracker.getUpload(uploadId);
      expect(upload.status).toBe('failed');
      expect(upload.error.message).toBe('Processing failed');
      expect(upload.error.stack).toBeDefined();
    });
  });

  describe('Statistics', () => {
    beforeEach(() => {
      // Create some test uploads
      const upload1 = tracker.startUpload(mockFile);
      const upload2 = tracker.startUpload(mockFile);
      const upload3 = tracker.startUpload(mockFile);

      tracker.completeUpload(upload1, { success: true });
      tracker.failUpload(upload2, 'Error');
      // upload3 remains active
    });

    it('should calculate statistics correctly', () => {
      const stats = tracker.getStatistics();

      expect(stats).toMatchObject({
        active: 1,
        completed: 2,
        total: 3,
        successful: 1,
        failed: 1,
        successRate: '50.0'
      });
      expect(stats.averageDurationMs).toBeGreaterThanOrEqual(0);
    });

    it('should format duration correctly', () => {
      expect(tracker.formatDuration(500)).toBe('500ms');
      expect(tracker.formatDuration(1500)).toBe('1.5s');
      expect(tracker.formatDuration(65000)).toBe('1m 5s');
    });
  });

  describe('Session Management', () => {
    it('should generate and maintain session ID', () => {
      const sessionId1 = tracker.getSessionId();
      const sessionId2 = tracker.getSessionId();

      expect(sessionId1).toBe(sessionId2);
      expect(sessionId1).toMatch(/^session-\d+-[a-z0-9]+$/);
    });
  });

  describe('Data Export', () => {
    it('should export specific upload data', () => {
      const uploadId = tracker.startUpload(mockFile);
      tracker.completeUpload(uploadId, { success: true });

      const exportData = tracker.exportData(uploadId);

      expect(exportData.uploadId).toBe(uploadId);
      expect(exportData.status).toBe('completed');
    });

    it('should export all data', () => {
      const uploadId = tracker.startUpload(mockFile);
      tracker.completeUpload(uploadId, { success: true });

      const exportData = tracker.exportData();

      expect(exportData.statistics).toBeDefined();
      expect(exportData.activeUploads).toBeInstanceOf(Array);
      expect(exportData.completedUploads).toBeInstanceOf(Array);
      expect(exportData.sessionId).toBeDefined();
      expect(exportData.exportTime).toBeDefined();
    });
  });

  describe('Memory Management', () => {
    it('should clean up old uploads', () => {
      const uploadId = tracker.startUpload(mockFile);
      tracker.completeUpload(uploadId, { success: true });

      // Mock old timestamp
      const upload = tracker.getUpload(uploadId);
      upload.startTime = new Date(Date.now() - 7200000).toISOString(); // 2 hours ago

      tracker.cleanup(3600000); // 1 hour max age

      expect(tracker.getUpload(uploadId)).toBeNull();
    });

    it('should clear all data', () => {
      const uploadId = tracker.startUpload(mockFile);
      tracker.completeUpload(uploadId, { success: true });

      tracker.clearAll();

      expect(tracker.getActiveUploads()).toHaveLength(0);
      expect(tracker.getCompletedUploads()).toHaveLength(0);
      expect(tracker.getUpload(uploadId)).toBeNull();
    });
  });

  describe('Error Handling', () => {
    it('should handle operations on non-existent uploads', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      tracker.updateStage('non-existent', 'test');
      tracker.completeUpload('non-existent', {});
      tracker.failUpload('non-existent', 'error');

      expect(consoleSpy).toHaveBeenCalledTimes(3);
      consoleSpy.mockRestore();
    });

    it('should handle string errors in failUpload', () => {
      const uploadId = tracker.startUpload(mockFile);

      tracker.failUpload(uploadId, 'String error message');

      const upload = tracker.getUpload(uploadId);
      expect(upload.error.message).toBe('String error message');
      expect(upload.error.stack).toBeUndefined();
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty file objects', () => {
      const emptyFile = { name: '', size: 0, type: '', lastModified: 0 };

      expect(() => {
        tracker.startUpload(emptyFile);
      }).not.toThrow();
    });

    it('should handle missing options', () => {
      expect(() => {
        tracker.startUpload(mockFile);
      }).not.toThrow();
    });

    it('should handle zero statistics', () => {
      const stats = tracker.getStatistics();

      expect(stats.successRate).toBe(0);
      expect(stats.averageDurationMs).toBe(0);
    });
  });
});
