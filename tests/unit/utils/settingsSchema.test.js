import { describe, it, expect } from 'vitest';
import {
  DEFAULT_SETTINGS,
  SETTINGS_SCHEMA,
  validateSettings,
  sanitizeSettings,
  mergeWithDefaults
} from '../../../src/utils/settingsSchema.js';

describe('settingsSchema', () => {
  describe('DEFAULT_SETTINGS', () => {
    it('should have all required sections', () => {
      expect(DEFAULT_SETTINGS).toHaveProperty('company');
      expect(DEFAULT_SETTINGS).toHaveProperty('display');
      expect(DEFAULT_SETTINGS).toHaveProperty('processing');
      expect(DEFAULT_SETTINGS).toHaveProperty('apiKeys');
    });

    it('should have correct default values', () => {
      expect(DEFAULT_SETTINGS.display.currency).toBe('PLN');
      expect(DEFAULT_SETTINGS.display.language).toBe('pl');
      expect(DEFAULT_SETTINGS.display.theme).toBe('light');
      expect(DEFAULT_SETTINGS.processing.ocrLanguage).toBe('pol');
      expect(DEFAULT_SETTINGS.processing.aiProvider).toBe('deepseek');
      expect(DEFAULT_SETTINGS.processing.autoProcess).toBe(true);
    });

    it('should have empty strings for company fields', () => {
      expect(DEFAULT_SETTINGS.company.name).toBe('');
      expect(DEFAULT_SETTINGS.company.email).toBe('');
      expect(DEFAULT_SETTINGS.company.taxId).toBe('');
    });

    it('should have empty API keys', () => {
      expect(DEFAULT_SETTINGS.apiKeys.deepseek).toBe('');
      expect(DEFAULT_SETTINGS.apiKeys.openai).toBe('');
      expect(DEFAULT_SETTINGS.apiKeys.fakturownia).toBe('');
      expect(DEFAULT_SETTINGS.apiKeys.infakt).toBe('');
    });
  });

  describe('SETTINGS_SCHEMA', () => {
    it('should define all required sections', () => {
      expect(SETTINGS_SCHEMA).toHaveProperty('company');
      expect(SETTINGS_SCHEMA).toHaveProperty('display');
      expect(SETTINGS_SCHEMA).toHaveProperty('processing');
      expect(SETTINGS_SCHEMA).toHaveProperty('apiKeys');
    });

    it('should mark display and processing as required', () => {
      expect(SETTINGS_SCHEMA.display.required).toBe(true);
      expect(SETTINGS_SCHEMA.processing.required).toBe(true);
    });

    it('should mark company and apiKeys as optional', () => {
      expect(SETTINGS_SCHEMA.company.required).toBe(false);
      expect(SETTINGS_SCHEMA.apiKeys.required).toBe(false);
    });

    it('should define enum values for display options', () => {
      expect(SETTINGS_SCHEMA.display.properties.currency.enum).toContain('PLN');
      expect(SETTINGS_SCHEMA.display.properties.currency.enum).toContain('EUR');
      expect(SETTINGS_SCHEMA.display.properties.language.enum).toContain('pl');
      expect(SETTINGS_SCHEMA.display.properties.language.enum).toContain('en');
    });
  });

  describe('validateSettings', () => {
    it('should validate correct settings', () => {
      const validSettings = {
        company: {
          name: 'Test Company',
          email: '<EMAIL>'
        },
        display: {
          currency: 'PLN',
          language: 'pl',
          theme: 'light'
        },
        processing: {
          ocrLanguage: 'pol',
          aiProvider: 'deepseek',
          autoProcess: true
        },
        apiKeys: {
          deepseek: 'sk-test-key'
        }
      };

      const result = validateSettings(validSettings);
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject non-object settings', () => {
      const result = validateSettings('invalid');
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Settings must be an object');
    });

    it('should require display section', () => {
      const settingsWithoutDisplay = {
        company: { name: 'Test' },
        processing: { ocrLanguage: 'pol' }
      };

      const result = validateSettings(settingsWithoutDisplay);
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Missing required section: display');
    });

    it('should require processing section', () => {
      const settingsWithoutProcessing = {
        company: { name: 'Test' },
        display: { currency: 'PLN' }
      };

      const result = validateSettings(settingsWithoutProcessing);
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Missing required section: processing');
    });

    it('should validate email format', () => {
      const settingsWithInvalidEmail = {
        company: { email: 'invalid-email' },
        display: { currency: 'PLN' },
        processing: { ocrLanguage: 'pol' }
      };

      const result = validateSettings(settingsWithInvalidEmail);
      expect(result.valid).toBe(false);
      expect(result.errors.some(error => error.includes('invalid email format'))).toBe(true);
    });

    it('should validate enum values', () => {
      const settingsWithInvalidCurrency = {
        display: { currency: 'INVALID' },
        processing: { ocrLanguage: 'pol' }
      };

      const result = validateSettings(settingsWithInvalidCurrency);
      expect(result.valid).toBe(false);
      expect(result.errors.some(error => error.includes('must be one of'))).toBe(true);
    });

    it('should validate string length limits', () => {
      const settingsWithLongName = {
        company: { name: 'x'.repeat(201) }, // Exceeds maxLength of 200
        display: { currency: 'PLN' },
        processing: { ocrLanguage: 'pol' }
      };

      const result = validateSettings(settingsWithLongName);
      expect(result.valid).toBe(false);
      expect(result.errors.some(error => error.includes('exceeds maximum length'))).toBe(true);
    });

    it('should validate type mismatches', () => {
      const settingsWithWrongTypes = {
        display: { currency: 123 }, // Should be string
        processing: { autoProcess: 'yes' } // Should be boolean
      };

      const result = validateSettings(settingsWithWrongTypes);
      expect(result.valid).toBe(false);
      expect(result.errors.some(error => error.includes('expected string, got number'))).toBe(true);
      expect(result.errors.some(error => error.includes('expected boolean, got string'))).toBe(true);
    });

    it('should allow missing optional sections', () => {
      const minimalSettings = {
        display: { currency: 'PLN' },
        processing: { ocrLanguage: 'pol' }
      };

      const result = validateSettings(minimalSettings);
      expect(result.valid).toBe(true);
    });
  });

  describe('sanitizeSettings', () => {
    it('should apply default values for missing properties', () => {
      const incompleteSettings = {
        display: { currency: 'EUR' },
        processing: { ocrLanguage: 'eng' }
      };

      const sanitized = sanitizeSettings(incompleteSettings);

      expect(sanitized.display.currency).toBe('EUR');
      expect(sanitized.display.language).toBe('pl'); // Default value
      expect(sanitized.processing.ocrLanguage).toBe('eng');
      expect(sanitized.processing.autoProcess).toBe(true); // Default value
    });

    it('should remove invalid values and use defaults', () => {
      const invalidSettings = {
        display: {
          currency: 'INVALID_CURRENCY',
          language: 'pl'
        },
        processing: {
          ocrLanguage: 'pol',
          autoProcess: 'invalid_boolean'
        }
      };

      const sanitized = sanitizeSettings(invalidSettings);

      expect(sanitized.display.currency).toBe('PLN'); // Default due to invalid value
      expect(sanitized.display.language).toBe('pl'); // Valid value preserved
      expect(sanitized.processing.ocrLanguage).toBe('pol'); // Valid value preserved
      expect(sanitized.processing.autoProcess).toBe(true); // Default due to invalid value
    });

    it('should preserve valid values', () => {
      const validSettings = {
        company: { name: 'Test Company' },
        display: { currency: 'EUR', language: 'en' },
        processing: { ocrLanguage: 'eng', autoProcess: false }
      };

      const sanitized = sanitizeSettings(validSettings);

      expect(sanitized.company.name).toBe('Test Company');
      expect(sanitized.display.currency).toBe('EUR');
      expect(sanitized.display.language).toBe('en');
      expect(sanitized.processing.ocrLanguage).toBe('eng');
      expect(sanitized.processing.autoProcess).toBe(false);
    });

    it('should handle empty input', () => {
      const sanitized = sanitizeSettings({});

      expect(sanitized).toHaveProperty('company');
      expect(sanitized).toHaveProperty('display');
      expect(sanitized).toHaveProperty('processing');
      expect(sanitized).toHaveProperty('apiKeys');
    });
  });

  describe('mergeWithDefaults', () => {
    it('should merge user settings with defaults', () => {
      const userSettings = {
        company: { name: 'User Company' },
        display: { currency: 'EUR' }
      };

      const merged = mergeWithDefaults(userSettings);

      expect(merged.company.name).toBe('User Company');
      expect(merged.company.email).toBe(''); // From defaults
      expect(merged.display.currency).toBe('EUR');
      expect(merged.display.language).toBe('pl'); // From defaults
      expect(merged.processing).toEqual(DEFAULT_SETTINGS.processing); // Entire section from defaults
    });

    it('should use custom defaults when provided', () => {
      const userSettings = {
        display: { currency: 'EUR' }
      };

      const customDefaults = {
        company: { name: 'Custom Default' },
        display: { currency: 'USD', language: 'en' },
        processing: { ocrLanguage: 'eng' }
      };

      const merged = mergeWithDefaults(userSettings, customDefaults);

      expect(merged.company.name).toBe('Custom Default');
      expect(merged.display.currency).toBe('EUR'); // User override
      expect(merged.display.language).toBe('en'); // Custom default
      expect(merged.processing.ocrLanguage).toBe('eng');
    });

    it('should handle nested object merging', () => {
      const userSettings = {
        company: { name: 'Test' },
        display: { currency: 'EUR' }
      };

      const merged = mergeWithDefaults(userSettings);

      expect(merged.company).toEqual({
        ...DEFAULT_SETTINGS.company,
        name: 'Test'
      });
    });

    it('should handle non-object sections', () => {
      const userSettings = {
        display: 'invalid',
        processing: { ocrLanguage: 'pol' }
      };

      const merged = mergeWithDefaults(userSettings);

      expect(merged.display).toBe('invalid'); // Preserved as-is
      expect(merged.processing.ocrLanguage).toBe('pol');
    });
  });

  describe('Edge Cases', () => {
    it('should handle null and undefined values', () => {
      const settingsWithNulls = {
        company: { name: null, email: undefined },
        display: { currency: 'PLN' },
        processing: { ocrLanguage: 'pol' }
      };

      const result = validateSettings(settingsWithNulls);
      expect(result.valid).toBe(true); // null/undefined are allowed
    });

    it('should handle empty strings', () => {
      const settingsWithEmptyStrings = {
        company: { name: '', email: '' },
        display: { currency: 'PLN' },
        processing: { ocrLanguage: 'pol' }
      };

      const result = validateSettings(settingsWithEmptyStrings);
      expect(result.valid).toBe(true);
    });

    it('should handle very large objects', () => {
      const largeSettings = {
        company: {
          name: 'x'.repeat(200), // At the limit
          logo: 'x'.repeat(99999) // Large but within limit
        },
        display: { currency: 'PLN' },
        processing: { ocrLanguage: 'pol' }
      };

      const result = validateSettings(largeSettings);
      expect(result.valid).toBe(true);
    });
  });
});
