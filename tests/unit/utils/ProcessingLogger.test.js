/**
 * @vitest-environment node
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { ProcessingLogger } from '../../../src/utils/ProcessingLogger.js';

describe('ProcessingLogger', () => {
  let logger;
  let consoleSpy;

  beforeEach(() => {
    logger = new ProcessingLogger();
    consoleSpy = {
      debug: vi.spyOn(console, 'debug').mockImplementation(() => {}),
      info: vi.spyOn(console, 'info').mockImplementation(() => {}),
      warn: vi.spyOn(console, 'warn').mockImplementation(() => {}),
      error: vi.spyOn(console, 'error').mockImplementation(() => {}),
      log: vi.spyOn(console, 'log').mockImplementation(() => {})
    };
  });

  describe('Configuration', () => {
    it('should have default configuration', () => {
      expect(logger.logLevel).toBe('info');
      expect(logger.enablePerformanceTracking).toBe(true);
      expect(logger.enableConsoleOutput).toBe(true);
    });

    it('should allow configuration updates', () => {
      logger.configure({
        logLevel: 'debug',
        enablePerformanceTracking: false,
        enableConsoleOutput: false
      });

      expect(logger.logLevel).toBe('debug');
      expect(logger.enablePerformanceTracking).toBe(false);
      expect(logger.enableConsoleOutput).toBe(false);
    });
  });

  describe('Log Level Filtering', () => {
    it('should respect log level filtering', () => {
      logger.configure({ logLevel: 'warn' });

      logger.debug('test', 'debug message', 'test-uuid');
      logger.info('test', 'info message', 'test-uuid');
      logger.warn('test', 'warn message', 'test-uuid');
      logger.error('test', 'error message', 'test-uuid');

      expect(consoleSpy.debug).not.toHaveBeenCalled();
      expect(consoleSpy.info).not.toHaveBeenCalled();
      expect(consoleSpy.warn).toHaveBeenCalled();
      expect(consoleSpy.error).toHaveBeenCalled();
    });
  });

  describe('Log Entry Formatting', () => {
    it('should format log entries correctly', () => {
      const uploadId = 'test-uuid-123';
      const logEntry = logger.formatLogEntry('info', 'test_stage', 'test message', uploadId, {
        extra: 'data'
      });

      expect(logEntry).toMatchObject({
        level: 'INFO',
        stage: 'test_stage',
        uploadId: uploadId,
        message: 'test message',
        extra: 'data'
      });
      expect(logEntry.timestamp).toBeDefined();
    });

    it('should store log entries in history', () => {
      const uploadId = 'test-uuid-123';
      logger.info('test_stage', 'test message', uploadId);

      const logs = logger.getLogsForUpload(uploadId);
      expect(logs).toHaveLength(1);
      expect(logs[0].message).toBe('test message');
    });
  });

  describe('Performance Tracking', () => {
    it('should track performance timers', () => {
      const uploadId = 'test-uuid-123';
      const stage = 'test_stage';

      logger.startTimer(stage, uploadId);

      // Simulate some processing time
      const duration = logger.endTimer(stage, uploadId);

      expect(duration).toBeGreaterThanOrEqual(0);
      expect(consoleSpy.info).toHaveBeenCalledWith(
        expect.stringContaining('Performance timer completed'),
        expect.any(String)
      );
    });

    it('should handle missing timers gracefully', () => {
      const duration = logger.endTimer('nonexistent', 'test-uuid');

      expect(duration).toBe(0);
      expect(consoleSpy.warn).toHaveBeenCalled();
    });

    it('should disable performance tracking when configured', () => {
      logger.configure({ enablePerformanceTracking: false });

      logger.startTimer('test', 'test-uuid');
      const duration = logger.endTimer('test', 'test-uuid');

      expect(duration).toBe(0);
    });
  });

  describe('Log Queries', () => {
    beforeEach(() => {
      logger.info('stage1', 'message1', 'uuid1');
      logger.warn('stage2', 'message2', 'uuid1');
      logger.error('stage1', 'message3', 'uuid2');
    });

    it('should get logs by upload ID', () => {
      const logs = logger.getLogsForUpload('uuid1');
      expect(logs).toHaveLength(2);
      expect(logs[0].message).toBe('message1');
      expect(logs[1].message).toBe('message2');
    });

    it('should get logs by stage', () => {
      const logs = logger.getLogsByStage('stage1');
      expect(logs).toHaveLength(2);
      expect(logs[0].uploadId).toBe('uuid1');
      expect(logs[1].uploadId).toBe('uuid2');
    });

    it('should generate upload summary', () => {
      const summary = logger.getUploadSummary('uuid1');

      expect(summary).toMatchObject({
        uploadId: 'uuid1',
        totalLogs: 2,
        stages: ['stage1', 'stage2'],
        hasErrors: false,
        hasWarnings: true
      });
      expect(summary.levels).toMatchObject({
        INFO: 1,
        WARN: 1
      });
    });
  });

  describe('Memory Management', () => {
    it('should clear logs for specific upload', () => {
      logger.info('test', 'message', 'uuid1');
      logger.info('test', 'message', 'uuid2');

      logger.clearLogsForUpload('uuid1');

      expect(logger.getLogsForUpload('uuid1')).toHaveLength(0);
      expect(logger.getLogsForUpload('uuid2')).toHaveLength(1);
    });

    it('should clear all logs', () => {
      logger.info('test', 'message', 'uuid1');
      logger.info('test', 'message', 'uuid2');

      logger.clearAllLogs();

      expect(logger.getLogsForUpload('uuid1')).toHaveLength(0);
      expect(logger.getLogsForUpload('uuid2')).toHaveLength(0);
    });
  });

  describe('Console Output', () => {
    it('should disable console output when configured', () => {
      logger.configure({ enableConsoleOutput: false });

      logger.info('test', 'message', 'test-uuid');

      expect(consoleSpy.info).not.toHaveBeenCalled();
    });

    it('should use appropriate console methods for different levels', () => {
      // Set log level to debug to ensure all levels are logged
      logger.configure({ logLevel: 'debug' });

      logger.debug('test', 'debug message', 'test-uuid');
      logger.info('test', 'info message', 'test-uuid');
      logger.warn('test', 'warn message', 'test-uuid');
      logger.error('test', 'error message', 'test-uuid');

      expect(consoleSpy.debug).toHaveBeenCalled();
      expect(consoleSpy.info).toHaveBeenCalled();
      expect(consoleSpy.warn).toHaveBeenCalled();
      expect(consoleSpy.error).toHaveBeenCalled();
    });
  });

  describe('Edge Cases', () => {
    it('should handle null upload ID gracefully', () => {
      expect(() => {
        logger.info('test', 'message', null);
      }).not.toThrow();
    });

    it('should handle empty messages', () => {
      logger.info('test', '', 'test-uuid');
      const logs = logger.getLogsForUpload('test-uuid');
      expect(logs[0].message).toBe('');
    });

    it('should handle large data objects', () => {
      const largeData = { data: 'x'.repeat(10000) };
      expect(() => {
        logger.info('test', 'message', 'test-uuid', largeData);
      }).not.toThrow();
    });
  });
});
