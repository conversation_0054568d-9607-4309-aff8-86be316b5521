/**
 * DateGrouping Unit Tests
 * Tests for date-based grouping utilities
 *
 * Epic: EPIC-003 - Data Display & Visualization
 * Story: STORY-3.2 - Grouping & Aggregation
 * Task: TASK-3.2.1 - Grouping Logic
 */

import { DateGrouping } from '../../../src/utils/DateGrouping.js';

describe('DateGrouping', () => {
  let dateGrouping;
  let testDate;

  beforeEach(() => {
    dateGrouping = new DateGrouping();
    testDate = new Date('2024-03-15T10:30:00Z'); // March 15, 2024
  });

  describe('Configuration', () => {
    it('should initialize with default settings', () => {
      expect(dateGrouping.fiscalYearStart).toBe(1);
      expect(dateGrouping.weekStartsOn).toBe(1);
      expect(dateGrouping.dateFormat).toBe('yyyy-MM-dd');
    });

    it('should configure with custom settings', () => {
      const config = {
        fiscalYearStart: 4, // April
        weekStartsOn: 0, // Sunday
        dateFormat: 'dd/MM/yyyy'
      };

      dateGrouping.configure(config);

      expect(dateGrouping.fiscalYearStart).toBe(4);
      expect(dateGrouping.weekStartsOn).toBe(0);
      expect(dateGrouping.dateFormat).toBe('dd/MM/yyyy');
    });

    it('should validate configuration', () => {
      const validConfig = { fiscalYearStart: 6, weekStartsOn: 1 };
      const validation = dateGrouping.validateConfig(validConfig);

      expect(validation.valid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should reject invalid configuration', () => {
      const invalidConfig = { fiscalYearStart: 15, weekStartsOn: 8 };
      const validation = dateGrouping.validateConfig(invalidConfig);

      expect(validation.valid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
    });
  });

  describe('Year Grouping', () => {
    it('should generate calendar year key', () => {
      const yearKey = dateGrouping.getYearKey(testDate, 1);
      expect(yearKey).toBe('2024');
    });

    it('should generate fiscal year key', () => {
      const fiscalYearKey = dateGrouping.getYearKey(testDate, 4); // April start
      expect(fiscalYearKey).toBe('FY2024'); // March is before April, so FY2024
    });

    it('should handle fiscal year boundary correctly', () => {
      const aprilDate = new Date('2024-04-15T10:30:00Z');
      const fiscalYearKey = dateGrouping.getYearKey(aprilDate, 4); // April start
      expect(fiscalYearKey).toBe('FY2025'); // April is start of FY2025
    });

    it('should get year date range for calendar year', () => {
      const dateRange = dateGrouping.getYearDateRange('2024', 1);

      expect(dateRange.start.getFullYear()).toBe(2024);
      expect(dateRange.start.getMonth()).toBe(0); // January
      expect(dateRange.end.getFullYear()).toBe(2024);
      expect(dateRange.end.getMonth()).toBe(11); // December
    });

    it('should get year date range for fiscal year', () => {
      const dateRange = dateGrouping.getYearDateRange('FY2024', 4); // April start

      expect(dateRange.start.getFullYear()).toBe(2023);
      expect(dateRange.start.getMonth()).toBe(3); // April
      expect(dateRange.end.getFullYear()).toBe(2024);
      expect(dateRange.end.getMonth()).toBe(2); // March (end of fiscal year)
    });
  });

  describe('Quarter Grouping', () => {
    it('should generate calendar quarter key', () => {
      const quarterKey = dateGrouping.getQuarterKey(testDate, 1);
      expect(quarterKey).toBe('2024-Q1'); // March is Q1
    });

    it('should generate fiscal quarter key', () => {
      const fiscalQuarterKey = dateGrouping.getQuarterKey(testDate, 4); // April start
      expect(fiscalQuarterKey).toBe('FY2024-Q4'); // March is Q4 of fiscal year
    });

    it('should calculate fiscal quarter correctly', () => {
      const aprilDate = new Date('2024-04-15T10:30:00Z');
      const julyDate = new Date('2024-07-15T10:30:00Z');

      expect(dateGrouping.getFiscalQuarter(aprilDate, 4)).toBe(1); // Q1 of fiscal year
      expect(dateGrouping.getFiscalQuarter(julyDate, 4)).toBe(2); // Q2 of fiscal year
    });

    it('should get quarter date range for calendar quarter', () => {
      const dateRange = dateGrouping.getQuarterDateRange('2024-Q1', 1);

      expect(dateRange.start.getMonth()).toBe(0); // January
      expect(dateRange.end.getMonth()).toBe(2); // March
    });
  });

  describe('Month Grouping', () => {
    it('should generate month key', () => {
      const monthKey = dateGrouping.getMonthKey(testDate);
      expect(monthKey).toBe('2024-03');
    });

    it('should get month date range', () => {
      const dateRange = dateGrouping.getMonthDateRange('2024-03');

      expect(dateRange.start.getMonth()).toBe(2); // March (0-based)
      expect(dateRange.start.getDate()).toBe(1);
      expect(dateRange.end.getMonth()).toBe(2); // March
      expect(dateRange.end.getDate()).toBe(31); // Last day of March
    });
  });

  describe('Week Grouping', () => {
    it('should generate week key', () => {
      const weekKey = dateGrouping.getWeekKey(testDate);
      expect(weekKey).toMatch(/^2024-W\d{2}$/);
    });

    it('should get week date range', () => {
      const weekKey = '2024-W11';
      const dateRange = dateGrouping.getWeekDateRange(weekKey);

      expect(dateRange.start).toBeInstanceOf(Date);
      expect(dateRange.end).toBeInstanceOf(Date);
      expect(dateRange.end.getTime()).toBeGreaterThan(dateRange.start.getTime());
    });
  });

  describe('Day Grouping', () => {
    it('should generate day key', () => {
      const dayKey = dateGrouping.getDayKey(testDate);
      expect(dayKey).toBe('2024-03-15');
    });

    it('should get day date range', () => {
      const dateRange = dateGrouping.getDayDateRange('2024-03-15');

      expect(dateRange.start.getDate()).toBe(15);
      expect(dateRange.start.getHours()).toBe(0);
      expect(dateRange.end.getDate()).toBe(15);
      expect(dateRange.end.getHours()).toBe(23);
    });
  });

  describe('Custom Grouping', () => {
    const customRanges = [
      {
        key: 'Q1_2024',
        start: '2024-01-01',
        end: '2024-03-31'
      },
      {
        key: 'Q2_2024',
        start: '2024-04-01',
        end: '2024-06-30'
      }
    ];

    it('should generate custom key for date in range', () => {
      const customKey = dateGrouping.getCustomKey(testDate, customRanges);
      expect(customKey).toBe('Q1_2024');
    });

    it('should return "other" for date not in any range', () => {
      const outOfRangeDate = new Date('2024-12-15T10:30:00Z');
      const customKey = dateGrouping.getCustomKey(outOfRangeDate, customRanges);
      expect(customKey).toBe('other');
    });

    it('should throw error for missing custom ranges', () => {
      expect(() => {
        dateGrouping.getCustomKey(testDate, null);
      }).toThrow('Custom ranges must be provided as an array');
    });

    it('should get custom date range', () => {
      const dateRange = dateGrouping.getCustomDateRange('Q1_2024', customRanges);

      expect(dateRange.start).toEqual(new Date('2024-01-01'));
      expect(dateRange.end).toEqual(new Date('2024-03-31'));
    });

    it('should return null range for unknown custom key', () => {
      const dateRange = dateGrouping.getCustomDateRange('UNKNOWN', customRanges);

      expect(dateRange.start).toBeNull();
      expect(dateRange.end).toBeNull();
    });
  });

  describe('Fiscal Year Calculations', () => {
    it('should calculate fiscal year correctly', () => {
      const marchDate = new Date('2024-03-15T10:30:00Z');
      const aprilDate = new Date('2024-04-15T10:30:00Z');

      // Fiscal year starts in April
      expect(dateGrouping.getFiscalYear(marchDate, 4)).toBe(2024);
      expect(dateGrouping.getFiscalYear(aprilDate, 4)).toBe(2025);
    });

    it('should calculate fiscal quarter correctly', () => {
      const dates = [
        new Date('2024-04-15T10:30:00Z'), // Q1
        new Date('2024-07-15T10:30:00Z'), // Q2
        new Date('2024-10-15T10:30:00Z'), // Q3
        new Date('2024-01-15T10:30:00Z') // Q4
      ];

      const expectedQuarters = [1, 2, 3, 4];

      dates.forEach((date, index) => {
        expect(dateGrouping.getFiscalQuarter(date, 4)).toBe(expectedQuarters[index]);
      });
    });
  });

  describe('Date Range for Groups', () => {
    it('should get date range for different group types', () => {
      const testCases = [
        { groupBy: 'year', groupKey: '2024' },
        { groupBy: 'quarter', groupKey: '2024-Q1' },
        { groupBy: 'month', groupKey: '2024-03' },
        { groupBy: 'week', groupKey: '2024-W11' },
        { groupBy: 'day', groupKey: '2024-03-15' }
      ];

      testCases.forEach(({ groupBy, groupKey }) => {
        const dateRange = dateGrouping.getDateRangeForGroup(groupKey, groupBy);

        expect(dateRange.start).toBeInstanceOf(Date);
        expect(dateRange.end).toBeInstanceOf(Date);
        expect(dateRange.end.getTime()).toBeGreaterThanOrEqual(dateRange.start.getTime());
      });
    });

    it('should throw error for unsupported group type', () => {
      expect(() => {
        dateGrouping.getDateRangeForGroup('test', 'unsupported');
      }).toThrow('Unsupported groupBy type: unsupported');
    });
  });

  describe('Edge Cases', () => {
    it('should handle leap year correctly', () => {
      const leapYearDate = new Date('2024-02-29T10:30:00Z');
      const monthKey = dateGrouping.getMonthKey(leapYearDate);
      expect(monthKey).toBe('2024-02');

      const dateRange = dateGrouping.getMonthDateRange('2024-02');
      expect(dateRange.end.getDate()).toBe(29); // February 29 in leap year
    });

    it('should handle year boundaries correctly', () => {
      const newYearDate = new Date('2024-01-01T00:00:00Z');
      const yearKey = dateGrouping.getYearKey(newYearDate);
      expect(yearKey).toBe('2024');
    });

    it('should handle invalid dates gracefully', () => {
      const invalidDate = new Date('invalid');
      // Invalid dates should throw an error, which is expected behavior
      expect(() => {
        dateGrouping.getMonthKey(invalidDate);
      }).toThrow();
    });
  });
});
