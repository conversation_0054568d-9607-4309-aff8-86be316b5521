/**
 * Test utilities for Jest
 * Provides testing helpers and mock factory
 */

const { MockFactory } = require('../mocks/mockFactory.js');

/**
 * Custom render function with providers
 */
export function renderWithProviders(ui, options = {}) {
  const {
    initialEntries = ['/'],
    ...renderOptions
  } = options;

  function Wrapper({ children }) {
    return (
      <BrowserRouter>
        {children}
      </BrowserRouter>
    );
  }

  return {
    user: userEvent.setup(),
    ...render(ui, { wrapper: Wrapper, ...renderOptions })
  };
}

/**
 * Custom render for popup components
 */
export function renderPopupComponent(ui, options = {}) {
  const mockSettings = MockFactory.createMockUserSettings();

  // Mock Chrome extension context
  const mockContext = {
    settings: mockSettings,
    updateSettings: jest.fn(),
    invoices: [],
    addInvoice: jest.fn(),
    removeInvoice: jest.fn(),
    isProcessing: false,
    setProcessing: jest.fn()
  };

  function PopupWrapper({ children }) {
    return (
      <div className="popup-container">
        {children}
      </div>
    );
  }

  return {
    user: userEvent.setup(),
    mockContext,
    ...render(ui, { wrapper: PopupWrapper, ...options })
  };
}

/**
 * Test utilities class
 */
export class TestUtils {
  /**
   * Wait for element to appear
   */
  static async waitForElement(selector, timeout = 5000) {
    return waitFor(() => {
      const element = screen.getByTestId(selector) || screen.getByRole(selector);
      expect(element).toBeInTheDocument();
      return element;
    }, { timeout });
  }

  /**
   * Wait for element to disappear
   */
  static async waitForElementToDisappear(selector, timeout = 5000) {
    return waitFor(() => {
      const element = screen.queryByTestId(selector) || screen.queryByRole(selector);
      expect(element).not.toBeInTheDocument();
    }, { timeout });
  }

  /**
   * Simulate file upload
   */
  static async simulateFileUpload(input, files) {
    const user = userEvent.setup();

    if (typeof files === 'string') {
      // Create mock file from filename
      files = [MockFactory.createMockPDFFile(files)];
    } else if (!Array.isArray(files)) {
      files = [files];
    }

    await user.upload(input, files);

    return files;
  }

  /**
   * Simulate drag and drop
   */
  static async simulateDragAndDrop(dropZone, files) {
    if (typeof files === 'string') {
      files = [MockFactory.createMockPDFFile(files)];
    } else if (!Array.isArray(files)) {
      files = [files];
    }

    const dataTransfer = {
      files,
      items: files.map(file => ({
        kind: 'file',
        type: file.type,
        getAsFile: () => file
      })),
      types: ['Files']
    };

    fireEvent.dragEnter(dropZone, { dataTransfer });
    fireEvent.dragOver(dropZone, { dataTransfer });
    fireEvent.drop(dropZone, { dataTransfer });

    return files;
  }

  /**
   * Mock Chrome storage operations
   */
  static mockChromeStorage(data = {}) {
    const mockData = { ...MockFactory.createMockStorageData(), ...data };

    chrome.storage.local.get.mockImplementation((keys, callback) => {
      let result = {};

      if (typeof keys === 'string') {
        result[keys] = mockData[keys];
      } else if (Array.isArray(keys)) {
        keys.forEach(key => {
          result[key] = mockData[key];
        });
      } else if (keys === null || keys === undefined) {
        result = mockData;
      } else if (typeof keys === 'object') {
        Object.keys(keys).forEach(key => {
          result[key] = mockData[key] !== undefined ? mockData[key] : keys[key];
        });
      }

      if (callback) { callback(result); }
      return Promise.resolve(result);
    });

    chrome.storage.local.set.mockImplementation((items, callback) => {
      Object.assign(mockData, items);
      if (callback) { callback(); }
      return Promise.resolve();
    });

    return mockData;
  }

  /**
   * Mock API responses
   */
  static mockAPIResponse(url, response, options = {}) {
    const { method = 'GET', status = 200, delay = 0 } = options;

    fetch.mockImplementation((requestUrl, requestOptions = {}) => {
      if (requestUrl.includes(url) && (requestOptions.method || 'GET') === method) {
        return new Promise(resolve => {
          setTimeout(() => {
            resolve(MockFactory.createMockAPIResponse(response, status < 400));
          }, delay);
        });
      }

      // Default mock response
      return Promise.resolve(MockFactory.createMockAPIResponse({}, false));
    });
  }

  /**
   * Create mock component with props
   */
  static createMockComponent(name, defaultProps = {}) {
    const MockComponent = jest.fn((props) => {
      return React.createElement('div', {
        'data-testid': `mock-${name.toLowerCase()}`,
        'data-props': JSON.stringify({ ...defaultProps, ...props })
      }, `Mock ${name}`);
    });

    MockComponent.displayName = `Mock${name}`;
    return MockComponent;
  }

  /**
   * Assert component props
   */
  static assertComponentProps(component, expectedProps) {
    const calls = component.mock.calls;
    const lastCall = calls[calls.length - 1];
    const actualProps = lastCall ? lastCall[0] : {};

    Object.keys(expectedProps).forEach(key => {
      expect(actualProps[key]).toEqual(expectedProps[key]);
    });
  }

  /**
   * Wait for async operations
   */
  static async waitForAsync(ms = 0) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Flush all promises
   */
  static async flushPromises() {
    return new Promise(resolve => setImmediate(resolve));
  }

  /**
   * Create mock event
   */
  static createMockEvent(type, properties = {}) {
    return {
      type,
      preventDefault: jest.fn(),
      stopPropagation: jest.fn(),
      target: { value: '' },
      currentTarget: { value: '' },
      ...properties
    };
  }

  /**
   * Assert error handling
   */
  static async assertErrorHandling(asyncFunction, expectedError) {
    let thrownError;

    try {
      await asyncFunction();
    } catch (error) {
      thrownError = error;
    }

    expect(thrownError).toBeDefined();
    if (expectedError) {
      expect(thrownError.message).toContain(expectedError);
    }

    return thrownError;
  }

  /**
   * Mock console methods for testing
   */
  static mockConsole() {
    const originalConsole = global.console;
    const mockConsole = {
      log: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
      info: jest.fn(),
      debug: jest.fn()
    };

    global.console = { ...originalConsole, ...mockConsole };

    return {
      restore: () => {
        global.console = originalConsole;
      },
      mocks: mockConsole
    };
  }

  /**
   * Create test environment cleanup
   */
  static createCleanup() {
    const cleanupFunctions = [];

    return {
      add: (fn) => cleanupFunctions.push(fn),
      run: () => {
        cleanupFunctions.forEach(fn => {
          try {
            fn();
          } catch (error) {
            console.error('Cleanup error:', error);
          }
        });
        cleanupFunctions.length = 0;
      }
    };
  }
}

// Export for CommonJS
module.exports = {
  TestUtils,
  MockFactory
};
