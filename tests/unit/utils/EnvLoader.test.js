/**
 * @file EnvLoader.test.js
 * @description Unit tests for EnvLoader utility
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { envLoader } from '../../../src/utils/EnvLoader.js';

describe('EnvLoader', () => {
  beforeEach(() => {
    // Reset the envLoader state
    envLoader.envVars = {};
    envLoader.loadPromise = null;
    envLoader.ready = false;
  });

  describe('Environment Variable Loading', () => {
    it('should load environment variables from window.__MVAT_ENV__', async () => {
      // Mock window.__MVAT_ENV__
      global.window = {
        __MVAT_ENV__: {
          DEEPSEEK_API_KEY: 'test-api-key',
          COMPANY_NAME: 'Test Company',
          FEATURE_SUBSCRIPTION_SYSTEM: 'true'
        }
      };

      await envLoader.initialize();

      expect(envLoader.isReady()).toBe(true);
      expect(envLoader.get('DEEPSEEK_API_KEY')).toBe('test-api-key');
      expect(envLoader.get('COMPANY_NAME')).toBe('Test Company');
      expect(envLoader.get('FEATURE_SUBSCRIPTION_SYSTEM')).toBe('true');
    });

    it('should load environment variables from import.meta.env', async () => {
      // Mock import.meta.env
      global.import = {
        meta: {
          env: {
            DEEPSEEK_API_KEY: 'meta-api-key',
            COMPANY_NAME: 'Meta Company',
            FEATURE_SUBSCRIPTION_SYSTEM: 'false'
          }
        }
      };

      await envLoader.initialize();

      expect(envLoader.isReady()).toBe(true);
      expect(envLoader.get('DEEPSEEK_API_KEY')).toBe('meta-api-key');
      expect(envLoader.get('COMPANY_NAME')).toBe('Meta Company');
      expect(envLoader.get('FEATURE_SUBSCRIPTION_SYSTEM')).toBe('false');
    });

    it('should prioritize window.__MVAT_ENV__ over import.meta.env', async () => {
      // Mock both sources
      global.window = {
        __MVAT_ENV__: {
          DEEPSEEK_API_KEY: 'window-api-key'
        }
      };

      global.import = {
        meta: {
          env: {
            DEEPSEEK_API_KEY: 'meta-api-key'
          }
        }
      };

      await envLoader.initialize();

      expect(envLoader.get('DEEPSEEK_API_KEY')).toBe('window-api-key');
    });

    it('should return all environment variables', async () => {
      global.window = {
        __MVAT_ENV__: {
          DEEPSEEK_API_KEY: 'test-key',
          COMPANY_NAME: 'Test Company',
          FEATURE_SUBSCRIPTION_SYSTEM: 'true'
        }
      };

      await envLoader.initialize();

      const allVars = envLoader.getAll();
      expect(allVars).toEqual({
        DEEPSEEK_API_KEY: 'test-key',
        COMPANY_NAME: 'Test Company',
        FEATURE_SUBSCRIPTION_SYSTEM: 'true'
      });
    });

    it('should handle missing environment variables gracefully', async () => {
      global.window = {};
      global.import = { meta: { env: {} } };

      await envLoader.initialize();

      expect(envLoader.isReady()).toBe(true);
      expect(envLoader.get('NONEXISTENT_VAR')).toBeUndefined();
      expect(envLoader.getAll()).toEqual({});
    });

    it('should use fallback values when provided', async () => {
      global.window = {};
      global.import = { meta: { env: {} } };

      await envLoader.initialize();

      expect(envLoader.get('NONEXISTENT_VAR', 'fallback')).toBe('fallback');
    });

    it('should only initialize once', async () => {
      global.window = {
        __MVAT_ENV__: {
          DEEPSEEK_API_KEY: 'test-key'
        }
      };

      const promise1 = envLoader.initialize();
      const promise2 = envLoader.initialize();

      expect(promise1).toBe(promise2);

      await promise1;
      expect(envLoader.get('DEEPSEEK_API_KEY')).toBe('test-key');
    });
  });

  describe('Known Environment Variables', () => {
    it('should recognize known environment variable patterns', () => {
      const knownVars = [
        'DEEPSEEK_API_KEY',
        'COMPANY_NAME',
        'STRIPE_PUBLISHABLE_KEY',
        'FAKTUROWNIA_API_TOKEN',
        'FEATURE_SUBSCRIPTION_SYSTEM',
        'NODE_ENV',
        'APP_VERSION'
      ];

      knownVars.forEach(varName => {
        expect(envLoader._isKnownEnvVar(varName)).toBe(true);
      });
    });

    it('should not recognize unknown environment variables', () => {
      const unknownVars = [
        'RANDOM_VAR',
        'UNKNOWN_SETTING',
        'CUSTOM_CONFIG'
      ];

      unknownVars.forEach(varName => {
        expect(envLoader._isKnownEnvVar(varName)).toBe(false);
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle errors during initialization gracefully', async () => {
      // Mock a scenario that throws an error
      const originalConsoleWarn = console.warn;
      console.warn = vi.fn();

      // Create a problematic environment
      Object.defineProperty(global, 'window', {
        get() {
          throw new Error('Window access error');
        }
      });

      await envLoader.initialize();

      expect(envLoader.isReady()).toBe(true);
      expect(envLoader.getAll()).toEqual({});
      expect(console.warn).toHaveBeenCalled();

      console.warn = originalConsoleWarn;
    });
  });
});
