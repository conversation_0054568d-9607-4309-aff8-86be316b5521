/**
 * Enhanced Data Flow Console Logging Test
 * Tests the new enhanced logging functionality for PDF.js → Tesseract.js → DeepSeek API data flow
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { processingLogger } from '../../../src/utils/ProcessingLogger.js';

describe('Enhanced Data Flow Console Logging', () => {
  let mockConsole;
  let uploadId;

  beforeEach(() => {
    // Mock console methods
    mockConsole = {
      log: vi.fn(),
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      group: vi.fn(),
      groupEnd: vi.fn()
    };

    // Replace global console
    global.console = mockConsole;

    // Generate test upload ID
    uploadId = `test-upload-${Date.now()}`;

    // Configure logger for testing
    processingLogger.configure({
      enableConsoleOutput: true,
      enableConsoleGrouping: true,
      enableMemoryTracking: true,
      dataPreviewLength: 100
    });
  });

  describe('Data Sanitization', () => {
    it('should sanitize API keys and tokens', () => {
      const sensitiveData = {
        apiKey: 'sk-1234567890abcdef1234567890abcdef12345678',
        token: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9',
        content: 'This is safe content'
      };

      const sanitized = processingLogger.sanitizeData(sensitiveData);

      expect(sanitized.apiKey).toBe('[REDACTED]');
      expect(sanitized.token).toBe('Bearer [REDACTED]');
      expect(sanitized.content).toBe('This is safe content');
    });

    it('should sanitize long tokens in strings', () => {
      const text = 'API key: sk-1234567890abcdef1234567890abcdef12345678 and some other text';
      const sanitized = processingLogger.sanitizeData(text);

      expect(sanitized).toContain('[REDACTED_API_KEY]');
      expect(sanitized).not.toContain('sk-1234567890abcdef1234567890abcdef12345678');
    });
  });

  describe('Data Preview Creation', () => {
    it('should create truncated preview for long text', () => {
      const longText = 'A'.repeat(300);
      const preview = processingLogger.createDataPreview(longText, 100);

      expect(preview).toHaveLength(115); // 100 + '... [truncated]'.length
      expect(preview).toEndWith('... [truncated]');
    });

    it('should return full text for short content', () => {
      const shortText = 'Short content';
      const preview = processingLogger.createDataPreview(shortText, 100);

      expect(preview).toBe(shortText);
    });

    it('should handle object data', () => {
      const data = { key: 'value', number: 42 };
      const preview = processingLogger.createDataPreview(data, 100);

      expect(preview).toContain('"key": "value"');
      expect(preview).toContain('"number": 42');
    });
  });

  describe('Memory Usage Tracking', () => {
    it('should return memory usage when available', () => {
      // Mock performance.memory
      global.performance = {
        memory: {
          usedJSHeapSize: 10485760, // 10MB
          totalJSHeapSize: 20971520, // 20MB
          jsHeapSizeLimit: 2147483648 // 2GB
        }
      };

      const memoryUsage = processingLogger.getMemoryUsage();

      expect(memoryUsage).toBeDefined();
      expect(memoryUsage.usedMB).toBe(10);
      expect(memoryUsage.totalMB).toBe(20);
    });

    it('should return null when memory API not available', () => {
      global.performance = {};

      const memoryUsage = processingLogger.getMemoryUsage();

      expect(memoryUsage).toBeNull();
    });
  });

  describe('Enhanced Logging Methods', () => {
    it('should log data extraction with content preview', () => {
      const extractedText = 'This is extracted text from PDF processing';
      const metadata = {
        totalTextLength: extractedText.length,
        pagesProcessed: 3,
        extractionDurationMs: 1500
      };

      processingLogger.logDataExtraction('pdf_extraction', extractedText, uploadId, metadata);

      // Verify console grouping was used
      expect(mockConsole.group).toHaveBeenCalledWith('📊 pdf_extraction - Data Extraction');
      expect(mockConsole.groupEnd).toHaveBeenCalled();

      // Verify log was created
      const logs = processingLogger.getLogsForUpload(uploadId);
      expect(logs).toHaveLength(1);
      expect(logs[0].stage).toBe('pdf_extraction');
      expect(logs[0].data.dataPreview).toContain('This is extracted text');
    });

    it('should log processing stage transformation', () => {
      const input = 'Raw input data';
      const output = 'Processed output data';
      const metrics = {
        processingDurationMs: 2000,
        confidence: 95
      };

      processingLogger.logProcessingStage('ocr_processing', input, output, uploadId, metrics);

      // Verify console grouping was used
      expect(mockConsole.group).toHaveBeenCalledWith('🔄 ocr_processing - Processing Transformation');
      expect(mockConsole.groupEnd).toHaveBeenCalled();

      // Verify log was created with input/output data
      const logs = processingLogger.getLogsForUpload(uploadId);
      expect(logs).toHaveLength(1);
      expect(logs[0].data.inputPreview).toContain('Raw input data');
      expect(logs[0].data.outputPreview).toContain('Processed output data');
    });

    it('should log API interaction with sanitized data', () => {
      const request = {
        prompt: 'Analyze this document',
        apiKey: 'sk-secret123456789',
        model: 'deepseek-chat'
      };
      const response = {
        content: 'Analysis result',
        model: 'deepseek-chat'
      };
      const timing = {
        processingDurationMs: 3000,
        estimatedTokens: 150
      };

      processingLogger.logAPIInteraction(request, response, uploadId, timing);

      // Verify console grouping was used
      expect(mockConsole.group).toHaveBeenCalledWith('🌐 API Interaction');
      expect(mockConsole.groupEnd).toHaveBeenCalled();

      // Verify log was created and API key was sanitized
      const logs = processingLogger.getLogsForUpload(uploadId);
      expect(logs).toHaveLength(1);
      expect(logs[0].data.requestPreview).not.toContain('sk-secret123456789');
      expect(logs[0].data.requestPreview).toContain('[REDACTED]');
      expect(logs[0].data.responsePreview).toContain('Analysis result');
    });
  });

  describe('Console Grouping Configuration', () => {
    it('should not use console grouping when disabled', () => {
      processingLogger.configure({ enableConsoleGrouping: false });

      processingLogger.logDataExtraction('test_stage', 'test data', uploadId);

      expect(mockConsole.group).not.toHaveBeenCalled();
      expect(mockConsole.groupEnd).not.toHaveBeenCalled();
    });

    it('should use console grouping when enabled', () => {
      processingLogger.configure({ enableConsoleGrouping: true });

      processingLogger.logDataExtraction('test_stage', 'test data', uploadId);

      expect(mockConsole.group).toHaveBeenCalled();
      expect(mockConsole.groupEnd).toHaveBeenCalled();
    });
  });

  describe('Data Size Tracking', () => {
    it('should track data sizes correctly', () => {
      const testData = 'A'.repeat(1000); // 1000 characters

      processingLogger.logDataExtraction('test_stage', testData, uploadId);

      const logs = processingLogger.getLogsForUpload(uploadId);
      expect(logs[0].data.dataSize).toBe(1000);
      expect(logs[0].data.dataType).toBe('string');
    });

    it('should handle object data size calculation', () => {
      const testData = { key: 'value', array: [1, 2, 3] };

      processingLogger.logDataExtraction('test_stage', testData, uploadId);

      const logs = processingLogger.getLogsForUpload(uploadId);
      expect(logs[0].data.dataSize).toBeGreaterThan(0);
      expect(logs[0].data.dataType).toBe('object');
    });
  });

  describe('Performance Impact', () => {
    it('should have minimal performance impact for large data', () => {
      const largeData = 'A'.repeat(100000); // 100KB of data

      const startTime = performance.now();
      processingLogger.logDataExtraction('performance_test', largeData, uploadId);
      const endTime = performance.now();

      const duration = endTime - startTime;
      expect(duration).toBeLessThan(100); // Should complete in less than 100ms
    });
  });
});
