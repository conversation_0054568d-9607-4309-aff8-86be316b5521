import { renderHook, act } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { useUploadProgress } from '../../../src/hooks/useUploadProgress.js';

describe('useUploadProgress Hook', () => {
  const mockOnProgress = vi.fn();
  const mockOnComplete = vi.fn();
  const mockOnError = vi.fn();
  const mockOnCancel = vi.fn();

  const sampleFiles = [
    new File(['content1'], 'file1.pdf', { type: 'application/pdf' }),
    new File(['content2'], 'file2.jpg', { type: 'image/jpeg' }),
    new File(['content3'], 'file3.png', { type: 'image/png' })
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('Initialization', () => {
    it('initializes with default state', () => {
      const { result } = renderHook(() => useUploadProgress());

      expect(result.current.files).toEqual([]);
      expect(result.current.overallProgress).toBe(0);
      expect(result.current.isActive).toBe(false);
      expect(result.current.totalFiles).toBe(0);
      expect(result.current.completedFiles).toBe(0);
      expect(result.current.failedFiles).toBe(0);
      expect(result.current.cancelledFiles).toBe(0);
    });

    it('accepts custom options', () => {
      const options = {
        maxFiles: 5,
        onProgress: mockOnProgress,
        onComplete: mockOnComplete,
        onError: mockOnError,
        onCancel: mockOnCancel
      };

      const { result } = renderHook(() => useUploadProgress(options));

      // Should initialize with default state regardless of options
      expect(result.current.files).toEqual([]);
      expect(result.current.totalFiles).toBe(0);
    });
  });

  describe('File Initialization', () => {
    it('initializes files correctly', () => {
      const { result } = renderHook(() => useUploadProgress({ maxFiles: 5 }));

      act(() => {
        const initializedFiles = result.current.initializeFiles(sampleFiles);
        expect(initializedFiles).toHaveLength(3);
        expect(initializedFiles[0]).toMatchObject({
          name: 'file1.pdf',
          size: expect.any(Number),
          type: 'application/pdf',
          progress: 0,
          status: 'pending',
          stage: 'uploading'
        });
      });

      expect(result.current.files).toHaveLength(3);
      expect(result.current.totalFiles).toBe(3);
      expect(result.current.overallProgress).toBe(0);
    });

    it('respects maxFiles limit', () => {
      const { result } = renderHook(() => useUploadProgress({ maxFiles: 2 }));

      act(() => {
        result.current.initializeFiles(sampleFiles);
      });

      expect(result.current.files).toHaveLength(2);
      expect(result.current.totalFiles).toBe(2);
    });

    it('generates unique file IDs', () => {
      const { result } = renderHook(() => useUploadProgress());

      act(() => {
        result.current.initializeFiles(sampleFiles);
      });

      const fileIds = result.current.files.map(f => f.id);
      const uniqueIds = new Set(fileIds);
      expect(uniqueIds.size).toBe(fileIds.length);
    });
  });

  describe('Progress Updates', () => {
    it('updates individual file progress', () => {
      const { result } = renderHook(() => useUploadProgress({ onProgress: mockOnProgress }));

      act(() => {
        result.current.initializeFiles(sampleFiles);
      });

      const fileId = result.current.files[0].id;

      act(() => {
        result.current.updateFileProgress(fileId, {
          progress: 50,
          status: 'uploading'
        });
      });

      const updatedFile = result.current.files.find(f => f.id === fileId);
      expect(updatedFile.progress).toBe(50);
      expect(updatedFile.status).toBe('uploading');
      expect(mockOnProgress).toHaveBeenCalled();
    });

    it('calculates overall progress correctly', () => {
      const { result } = renderHook(() => useUploadProgress());

      act(() => {
        result.current.initializeFiles(sampleFiles);
      });

      const fileIds = result.current.files.map(f => f.id);

      act(() => {
        result.current.updateFileProgress(fileIds[0], { progress: 100 });
        result.current.updateFileProgress(fileIds[1], { progress: 50 });
        result.current.updateFileProgress(fileIds[2], { progress: 0 });
      });

      // (100 + 50 + 0) / 3 = 50
      expect(result.current.overallProgress).toBe(50);
    });

    it('updates file counts correctly', () => {
      const { result } = renderHook(() => useUploadProgress());

      act(() => {
        result.current.initializeFiles(sampleFiles);
      });

      const fileIds = result.current.files.map(f => f.id);

      act(() => {
        result.current.updateFileProgress(fileIds[0], { status: 'complete' });
        result.current.updateFileProgress(fileIds[1], { status: 'error' });
        result.current.updateFileProgress(fileIds[2], { status: 'uploading' });
      });

      expect(result.current.completedFiles).toBe(1);
      expect(result.current.failedFiles).toBe(1);
      expect(result.current.isActive).toBe(true); // One file still uploading
    });

    it('sets timestamps correctly', () => {
      const { result } = renderHook(() => useUploadProgress());

      act(() => {
        result.current.initializeFiles(sampleFiles);
      });

      const fileId = result.current.files[0].id;

      act(() => {
        result.current.updateFileProgress(fileId, { status: 'uploading' });
      });

      let file = result.current.files.find(f => f.id === fileId);
      expect(file.startTime).toBeTruthy();
      expect(file.endTime).toBeFalsy();

      act(() => {
        result.current.updateFileProgress(fileId, { status: 'complete' });
      });

      file = result.current.files.find(f => f.id === fileId);
      expect(file.endTime).toBeTruthy();
    });
  });

  describe('Processing Control', () => {
    it('starts processing correctly', () => {
      const { result } = renderHook(() => useUploadProgress());

      act(() => {
        result.current.initializeFiles(sampleFiles);
        result.current.startProcessing();
      });

      expect(result.current.isActive).toBe(true);
      result.current.files.forEach(file => {
        expect(file.status).toBe('uploading');
      });
    });

    it('cancels processing correctly', () => {
      const { result } = renderHook(() => useUploadProgress({ onCancel: mockOnCancel }));

      act(() => {
        result.current.initializeFiles(sampleFiles);
        result.current.startProcessing();
      });

      const fileIds = result.current.files.map(f => f.id);

      act(() => {
        result.current.cancelProcessing(fileIds);
      });

      result.current.files.forEach(file => {
        expect(file.status).toBe('cancelled');
        expect(file.error).toBe('Processing cancelled by user');
      });

      expect(mockOnCancel).toHaveBeenCalledWith(fileIds);
    });

    it('cancels all files when no specific IDs provided', () => {
      const { result } = renderHook(() => useUploadProgress());

      act(() => {
        result.current.initializeFiles(sampleFiles);
        result.current.startProcessing();
        result.current.cancelProcessing();
      });

      result.current.files.forEach(file => {
        expect(file.status).toBe('cancelled');
      });
    });
  });

  describe('Retry Functionality', () => {
    it('retries failed files', () => {
      const { result } = renderHook(() => useUploadProgress({ retryAttempts: 3 }));

      act(() => {
        result.current.initializeFiles(sampleFiles);
      });

      const fileId = result.current.files[0].id;

      // Mark file as failed
      act(() => {
        result.current.updateFileProgress(fileId, {
          status: 'error',
          error: 'Processing failed'
        });
      });

      // Retry the file
      act(() => {
        result.current.retryFiles([fileId]);
      });

      const file = result.current.files.find(f => f.id === fileId);
      expect(file.status).toBe('pending');
      expect(file.error).toBeNull();
      expect(file.progress).toBe(0);
    });

    it('retries all failed files when no IDs specified', () => {
      const { result } = renderHook(() => useUploadProgress());

      act(() => {
        result.current.initializeFiles(sampleFiles);
      });

      const fileIds = result.current.files.map(f => f.id);

      // Mark some files as failed
      act(() => {
        result.current.updateFileProgress(fileIds[0], { status: 'error' });
        result.current.updateFileProgress(fileIds[1], { status: 'error' });
        result.current.updateFileProgress(fileIds[2], { status: 'complete' });
      });

      act(() => {
        result.current.retryFiles();
      });

      expect(result.current.files[0].status).toBe('pending');
      expect(result.current.files[1].status).toBe('pending');
      expect(result.current.files[2].status).toBe('complete'); // Should not change
    });

    it('respects retry attempt limits', () => {
      const { result } = renderHook(() => useUploadProgress({ retryAttempts: 1 }));

      act(() => {
        result.current.initializeFiles([sampleFiles[0]]);
      });

      const fileId = result.current.files[0].id;

      // First retry
      act(() => {
        result.current.updateFileProgress(fileId, { status: 'error' });
        result.current.retryFiles([fileId]);
      });

      expect(result.current.files[0].status).toBe('pending');

      // Second retry (should be ignored due to limit)
      act(() => {
        result.current.updateFileProgress(fileId, { status: 'error' });
        result.current.retryFiles([fileId]);
      });

      expect(result.current.files[0].status).toBe('error'); // Should remain error
    });

    it('handles retry delay', () => {
      const { result } = renderHook(() => useUploadProgress({ retryDelay: 1000 }));

      act(() => {
        result.current.initializeFiles([sampleFiles[0]]);
      });

      const fileId = result.current.files[0].id;

      act(() => {
        result.current.updateFileProgress(fileId, { status: 'error' });
        result.current.retryFiles([fileId]);
      });

      // Should be pending immediately
      expect(result.current.files[0].status).toBe('pending');

      // After delay, should start uploading
      act(() => {
        vi.advanceTimersByTime(1000);
      });

      expect(result.current.files[0].status).toBe('uploading');
    });
  });

  describe('Clear Functionality', () => {
    it('clears all files', () => {
      const { result } = renderHook(() => useUploadProgress());

      act(() => {
        result.current.initializeFiles(sampleFiles);
        result.current.clearFiles();
      });

      expect(result.current.files).toEqual([]);
      expect(result.current.totalFiles).toBe(0);
      expect(result.current.overallProgress).toBe(0);
    });

    it('clears files by status filter', () => {
      const { result } = renderHook(() => useUploadProgress());

      act(() => {
        result.current.initializeFiles(sampleFiles);
      });

      const fileIds = result.current.files.map(f => f.id);

      act(() => {
        result.current.updateFileProgress(fileIds[0], { status: 'complete' });
        result.current.updateFileProgress(fileIds[1], { status: 'error' });
        result.current.updateFileProgress(fileIds[2], { status: 'uploading' });
      });

      act(() => {
        result.current.clearFiles(['complete', 'error']);
      });

      expect(result.current.files).toHaveLength(1);
      expect(result.current.files[0].status).toBe('uploading');
    });
  });

  describe('Utility Functions', () => {
    it('gets file by ID', () => {
      const { result } = renderHook(() => useUploadProgress());

      act(() => {
        result.current.initializeFiles(sampleFiles);
      });

      const fileId = result.current.files[0].id;
      const file = result.current.getFile(fileId);

      expect(file).toBeTruthy();
      expect(file.id).toBe(fileId);
    });

    it('gets files by status', () => {
      const { result } = renderHook(() => useUploadProgress());

      act(() => {
        result.current.initializeFiles(sampleFiles);
      });

      const fileIds = result.current.files.map(f => f.id);

      act(() => {
        result.current.updateFileProgress(fileIds[0], { status: 'complete' });
        result.current.updateFileProgress(fileIds[1], { status: 'error' });
      });

      const completedFiles = result.current.getFilesByStatus('complete');
      const errorFiles = result.current.getFilesByStatus('error');

      expect(completedFiles).toHaveLength(1);
      expect(errorFiles).toHaveLength(1);
    });
  });

  describe('Auto-completion Detection', () => {
    it('triggers onComplete when all files are processed', () => {
      const { result } = renderHook(() => useUploadProgress({ onComplete: mockOnComplete }));

      act(() => {
        result.current.initializeFiles(sampleFiles);
        result.current.startProcessing();
      });

      const fileIds = result.current.files.map(f => f.id);

      act(() => {
        result.current.updateFileProgress(fileIds[0], { status: 'complete' });
        result.current.updateFileProgress(fileIds[1], { status: 'complete' });
        result.current.updateFileProgress(fileIds[2], { status: 'error' });
      });

      expect(result.current.isActive).toBe(false);
      expect(mockOnComplete).toHaveBeenCalledWith({
        files: expect.any(Array),
        completedFiles: 2,
        failedFiles: 1,
        totalFiles: 3,
        overallProgress: expect.any(Number)
      });
    });
  });
});
