import { describe, it, expect, beforeEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useTableState } from '../../../src/popup/hooks/useTableState.js';

describe('useTableState Hook', () => {
  const mockData = [
    {
      id: '1',
      filename: 'invoice1.pdf',
      number: 'INV-001',
      seller_name: 'Company A',
      buyer_name: 'Company B',
      total_gross: 100.50,
      date: '2024-01-15',
      processedAt: '2024-01-15T10:00:00Z'
    },
    {
      id: '2',
      filename: 'invoice2.pdf',
      number: 'INV-002',
      seller_name: 'Company C',
      buyer_name: 'Company D',
      total_gross: 250.75,
      date: '2024-01-20',
      processedAt: '2024-01-20T14:30:00Z'
    },
    {
      id: '3',
      filename: 'receipt3.pdf',
      number: 'REC-003',
      seller_name: 'Store E',
      buyer_name: 'Customer F',
      total_gross: 75.25,
      date: '2024-01-10',
      processedAt: '2024-01-10T09:15:00Z'
    }
  ];

  describe('Initialization', () => {
    it('should initialize with default values', () => {
      const { result } = renderHook(() => useTableState(mockData));

      expect(result.current.sortField).toBe('processedAt');
      expect(result.current.sortDirection).toBe('desc');
      expect(result.current.filterText).toBe('');
      expect(result.current.currentPage).toBe(1);
      expect(result.current.pageSize).toBe(50);
    });

    it('should initialize with custom options', () => {
      const options = {
        initialSortField: 'filename',
        initialSortDirection: 'asc',
        initialPageSize: 25,
        initialFilterText: 'test'
      };

      const { result } = renderHook(() => useTableState(mockData, options));

      expect(result.current.sortField).toBe('filename');
      expect(result.current.sortDirection).toBe('asc');
      expect(result.current.pageSize).toBe(25);
      expect(result.current.filterText).toBe('test');
    });
  });

  describe('Filtering', () => {
    it('should filter data by filename', () => {
      const { result } = renderHook(() => useTableState(mockData));

      act(() => {
        result.current.handleFilter('invoice1');
      });

      expect(result.current.filteredData).toHaveLength(1);
      expect(result.current.filteredData[0].filename).toBe('invoice1.pdf');
    });

    it('should filter data by seller name', () => {
      const { result } = renderHook(() => useTableState(mockData));

      act(() => {
        result.current.handleFilter('Company A');
      });

      expect(result.current.filteredData).toHaveLength(1);
      expect(result.current.filteredData[0].seller_name).toBe('Company A');
    });

    it('should filter data case-insensitively', () => {
      const { result } = renderHook(() => useTableState(mockData));

      act(() => {
        result.current.handleFilter('COMPANY');
      });

      expect(result.current.filteredData).toHaveLength(2);
    });

    it('should return all data when filter is empty', () => {
      const { result } = renderHook(() => useTableState(mockData));

      act(() => {
        result.current.handleFilter('test');
      });

      expect(result.current.filteredData).toHaveLength(0);

      act(() => {
        result.current.handleFilter('');
      });

      expect(result.current.filteredData).toHaveLength(3);
    });
  });

  describe('Sorting', () => {
    it('should sort by string fields', () => {
      const { result } = renderHook(() => useTableState(mockData));

      act(() => {
        result.current.handleSort('filename');
      });

      expect(result.current.sortField).toBe('filename');
      expect(result.current.sortDirection).toBe('asc');
      expect(result.current.sortedData[0].filename).toBe('invoice1.pdf');
    });

    it('should sort by numeric fields', () => {
      const { result } = renderHook(() => useTableState(mockData));

      act(() => {
        result.current.handleSort('total_gross');
      });

      expect(result.current.sortField).toBe('total_gross');
      expect(result.current.sortDirection).toBe('asc');
      expect(result.current.sortedData[0].total_gross).toBe(75.25);
    });

    it('should sort by date fields', () => {
      const { result } = renderHook(() => useTableState(mockData));

      act(() => {
        result.current.handleSort('date');
      });

      expect(result.current.sortField).toBe('date');
      expect(result.current.sortDirection).toBe('asc');
      expect(result.current.sortedData[0].date).toBe('2024-01-10');
    });

    it('should toggle sort direction when clicking same field', () => {
      const { result } = renderHook(() => useTableState(mockData));

      act(() => {
        result.current.handleSort('filename');
      });

      expect(result.current.sortDirection).toBe('asc');

      act(() => {
        result.current.handleSort('filename');
      });

      expect(result.current.sortDirection).toBe('desc');
    });
  });

  describe('Pagination', () => {
    const largeData = Array.from({ length: 100 }, (_, i) => ({
      id: `${i + 1}`,
      filename: `file${i + 1}.pdf`,
      number: `INV-${String(i + 1).padStart(3, '0')}`,
      total_gross: (i + 1) * 10,
      processedAt: new Date(2024, 0, i + 1).toISOString()
    }));

    it('should paginate data correctly', () => {
      const { result } = renderHook(() => useTableState(largeData, { initialPageSize: 10 }));

      expect(result.current.data).toHaveLength(10);
      expect(result.current.pagination.totalItems).toBe(100);
      expect(result.current.pagination.totalPages).toBe(10);
      expect(result.current.pagination.currentPage).toBe(1);
    });

    it('should navigate to next page', () => {
      const { result } = renderHook(() => useTableState(largeData, { initialPageSize: 10 }));

      act(() => {
        result.current.goToNextPage();
      });

      expect(result.current.pagination.currentPage).toBe(2);
      expect(result.current.data[0].filename).toBe('file11.pdf');
    });

    it('should navigate to previous page', () => {
      const { result } = renderHook(() => useTableState(largeData, { initialPageSize: 10 }));

      act(() => {
        result.current.handlePageChange(3);
      });

      expect(result.current.pagination.currentPage).toBe(3);

      act(() => {
        result.current.goToPrevPage();
      });

      expect(result.current.pagination.currentPage).toBe(2);
    });

    it('should change page size', () => {
      const { result } = renderHook(() => useTableState(largeData, { initialPageSize: 10 }));

      act(() => {
        result.current.handlePageSizeChange(25);
      });

      expect(result.current.pageSize).toBe(25);
      expect(result.current.data).toHaveLength(25);
      expect(result.current.pagination.totalPages).toBe(4);
      expect(result.current.pagination.currentPage).toBe(1);
    });

    it('should reset to first page when filtering', () => {
      const { result } = renderHook(() => useTableState(largeData, { initialPageSize: 10 }));

      act(() => {
        result.current.handlePageChange(5);
      });

      expect(result.current.pagination.currentPage).toBe(5);

      act(() => {
        result.current.handleFilter('file1');
      });

      expect(result.current.pagination.currentPage).toBe(1);
    });
  });

  describe('Reset functionality', () => {
    it('should reset all state to initial values', () => {
      const { result } = renderHook(() => useTableState(mockData));

      // Change some state
      act(() => {
        result.current.handleSort('filename');
        result.current.handleFilter('test');
        result.current.handlePageChange(2);
        result.current.handlePageSizeChange(25);
      });

      // Reset
      act(() => {
        result.current.resetTable();
      });

      expect(result.current.sortField).toBe('processedAt');
      expect(result.current.sortDirection).toBe('desc');
      expect(result.current.filterText).toBe('');
      expect(result.current.currentPage).toBe(1);
      expect(result.current.pageSize).toBe(50);
    });
  });

  describe('Edge cases', () => {
    it('should handle empty data', () => {
      const { result } = renderHook(() => useTableState([]));

      expect(result.current.data).toHaveLength(0);
      expect(result.current.pagination.totalItems).toBe(0);
      expect(result.current.pagination.totalPages).toBe(0);
    });

    it('should handle null/undefined data', () => {
      const { result } = renderHook(() => useTableState(null));

      expect(result.current.data).toHaveLength(0);
      expect(result.current.pagination.totalItems).toBe(0);
    });

    it('should handle page navigation beyond bounds', () => {
      const { result } = renderHook(() => useTableState(mockData, { initialPageSize: 2 }));

      act(() => {
        result.current.handlePageChange(10); // Beyond max pages
      });

      expect(result.current.pagination.currentPage).toBe(2); // Should clamp to max page

      act(() => {
        result.current.handlePageChange(-1); // Below min page
      });

      expect(result.current.pagination.currentPage).toBe(1); // Should clamp to min page
    });
  });
});
