/**
 * Unit Tests for PDFProcessingService
 * Tests PDF.js integration and text extraction functionality
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { PDFProcessingService } from '../../../src/services/PDFProcessingService.js';

// Mock PDF.js
const mockPDFJS = {
  GlobalWorkerOptions: {
    workerSrc: ''
  },
  getDocument: vi.fn()
};

// Mock Chrome runtime
const mockChrome = {
  runtime: {
    getURL: vi.fn((path) => `chrome-extension://test-id/${path}`)
  }
};

// Setup global mocks
global.chrome = mockChrome;
vi.mock('pdfjs-dist', () => mockPDFJS);

describe('PDFProcessingService', () => {
  let service;
  let mockFile;
  let mockPDF;
  let mockPage;

  beforeEach(() => {
    service = new PDFProcessingService();

    // Mock file
    mockFile = {
      name: 'test-invoice.pdf',
      type: 'application/pdf',
      size: 1024 * 1024, // 1MB
      arrayBuffer: vi.fn().mockResolvedValue(new ArrayBuffer(1024))
    };

    // Mock PDF page
    mockPage = {
      getTextContent: vi.fn().mockResolvedValue({
        items: [
          { str: 'FAKTURA' },
          { str: 'Nr:' },
          { str: 'FV/2024/001' },
          { str: 'Kwota:' },
          { str: '1234.56' },
          { str: 'PLN' }
        ]
      }),
      cleanup: vi.fn()
    };

    // Mock PDF document
    mockPDF = {
      numPages: 1,
      getPage: vi.fn().mockResolvedValue(mockPage),
      getMetadata: vi.fn().mockResolvedValue({
        info: {
          Title: 'Test Invoice',
          Author: 'Test Company',
          CreationDate: new Date().toISOString()
        }
      })
    };

    // Mock PDF.js getDocument
    mockPDFJS.getDocument.mockReturnValue({
      promise: Promise.resolve(mockPDF)
    });

    // Reset mocks
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('initialization', () => {
    it('should initialize successfully', async () => {
      await service.initialize();
      expect(service.isReady()).toBe(true);
    });

    it('should configure worker URL for Chrome extension', async () => {
      await service.initialize();
      expect(mockChrome.runtime.getURL).toHaveBeenCalledWith('assets/pdf.worker.min.js');
    });

    it('should handle initialization errors', async () => {
      mockChrome.runtime.getURL.mockImplementation(() => {
        throw new Error('Chrome runtime error');
      });

      await expect(service.initialize()).rejects.toThrow('PDF service initialization failed');
    });
  });

  describe('file validation', () => {
    it('should validate correct PDF file', () => {
      expect(() => service.validatePDFFile(mockFile)).not.toThrow();
    });

    it('should reject non-PDF files', () => {
      const invalidFile = { ...mockFile, type: 'image/jpeg' };
      expect(() => service.validatePDFFile(invalidFile)).toThrow('Invalid file type');
    });

    it('should reject oversized files', () => {
      const largeFile = { ...mockFile, size: 20 * 1024 * 1024 }; // 20MB
      expect(() => service.validatePDFFile(largeFile)).toThrow('File too large');
    });

    it('should reject empty files', () => {
      const emptyFile = { ...mockFile, size: 0 };
      expect(() => service.validatePDFFile(emptyFile)).toThrow('File is empty');
    });

    it('should reject null/undefined files', () => {
      expect(() => service.validatePDFFile(null)).toThrow('No file provided');
      expect(() => service.validatePDFFile(undefined)).toThrow('No file provided');
    });
  });

  describe('text extraction', () => {
    it('should extract text from PDF successfully', async () => {
      const result = await service.extractText(mockFile);

      expect(result.success).toBe(true);
      expect(result.text).toContain('FAKTURA');
      expect(result.text).toContain('FV/2024/001');
      expect(result.text).toContain('1234.56');
      expect(result.metadata.extractionMethod).toBe('pdf_text');
      expect(result.metadata.numPages).toBe(1);
    });

    it('should handle progress callbacks', async () => {
      const progressCallback = vi.fn();

      await service.extractText(mockFile, { onProgress: progressCallback });

      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          stage: 'loading',
          progress: 0
        })
      );

      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          stage: 'complete',
          progress: 100
        })
      );
    });

    it('should handle multi-page PDFs', async () => {
      // Mock multi-page PDF
      mockPDF.numPages = 3;
      mockPDF.getPage.mockImplementation((pageNum) => ({
        ...mockPage,
        getTextContent: vi.fn().mockResolvedValue({
          items: [{ str: `Page ${pageNum} content` }]
        })
      }));

      const result = await service.extractText(mockFile);

      expect(result.success).toBe(true);
      expect(result.text).toContain('Page 1 content');
      expect(result.text).toContain('Page 2 content');
      expect(result.text).toContain('Page 3 content');
      expect(result.metadata.numPages).toBe(3);
    });

    it('should respect maxPages limit', async () => {
      mockPDF.numPages = 10;

      const result = await service.extractText(mockFile, { maxPages: 5 });

      expect(result.metadata.numPages).toBe(5);
      expect(result.metadata.totalPages).toBe(10);
    });

    it('should handle PDF loading errors', async () => {
      mockPDFJS.getDocument.mockReturnValue({
        promise: Promise.reject(new Error('Invalid PDF'))
      });

      const result = await service.extractText(mockFile);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid PDF');
    });

    it('should handle page extraction errors gracefully', async () => {
      mockPage.getTextContent.mockRejectedValue(new Error('Page error'));

      const result = await service.extractText(mockFile);

      expect(result.success).toBe(true);
      expect(result.text).toBe(''); // Should continue with empty text
    });
  });

  describe('text quality assessment', () => {
    it('should assess high quality text', () => {
      const highQualityText = 'FAKTURA Nr: FV/2024/001 Sprzedawca: Test Company Nabywca: Client Company Kwota netto: 1000.00 PLN VAT: 230.00 PLN Razem: 1230.00 PLN';

      const quality = service.assessTextQuality(highQualityText);

      expect(quality.score).toBeGreaterThan(70);
      expect(quality.isGoodQuality).toBe(true);
      expect(quality.hasInvoiceContent).toBe(true);
      expect(quality.foundKeywords).toBeGreaterThan(3);
    });

    it('should assess low quality text', () => {
      const lowQualityText = 'abc def';

      const quality = service.assessTextQuality(lowQualityText);

      expect(quality.score).toBeLessThan(50);
      expect(quality.isGoodQuality).toBe(false);
      expect(quality.hasInvoiceContent).toBe(false);
    });

    it('should handle empty text', () => {
      const quality = service.assessTextQuality('');

      expect(quality.score).toBe(0);
      expect(quality.isGoodQuality).toBe(false);
      expect(quality.foundKeywords).toBe(0);
    });
  });

  describe('metadata extraction', () => {
    it('should extract PDF metadata successfully', async () => {
      const result = await service.getMetadata(mockFile);

      expect(result.success).toBe(true);
      expect(result.metadata.numPages).toBe(1);
      expect(result.metadata.title).toBe('Test Invoice');
      expect(result.metadata.author).toBe('Test Company');
      expect(result.metadata.fileName).toBe('test-invoice.pdf');
    });

    it('should handle metadata extraction errors', async () => {
      mockPDF.getMetadata.mockRejectedValue(new Error('Metadata error'));

      const result = await service.getMetadata(mockFile);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Metadata error');
    });
  });

  describe('configuration management', () => {
    it('should return current configuration', () => {
      const config = service.getConfig();

      expect(config).toHaveProperty('maxFileSize');
      expect(config).toHaveProperty('maxPages');
      expect(config).toHaveProperty('textExtractionTimeout');
    });

    it('should update configuration', () => {
      const newConfig = { maxFileSize: 5 * 1024 * 1024 }; // 5MB

      service.updateConfig(newConfig);
      const config = service.getConfig();

      expect(config.maxFileSize).toBe(5 * 1024 * 1024);
    });

    it('should preserve existing config when updating', () => {
      const originalMaxPages = service.getConfig().maxPages;

      service.updateConfig({ maxFileSize: 5 * 1024 * 1024 });
      const config = service.getConfig();

      expect(config.maxPages).toBe(originalMaxPages);
      expect(config.maxFileSize).toBe(5 * 1024 * 1024);
    });
  });

  describe('error handling', () => {
    it('should handle file reading errors', async () => {
      mockFile.arrayBuffer.mockRejectedValue(new Error('File read error'));

      const result = await service.extractText(mockFile);

      expect(result.success).toBe(false);
      expect(result.error).toContain('File read error');
    });

    it('should handle worker initialization errors', async () => {
      // Reset service to uninitialized state
      service.initialized = false;
      mockChrome.runtime.getURL.mockImplementation(() => {
        throw new Error('Worker error');
      });

      const result = await service.extractText(mockFile);

      expect(result.success).toBe(false);
      expect(result.error).toContain('PDF service initialization failed');
    });
  });

  describe('memory optimization', () => {
    it('should clean up page resources', async () => {
      await service.extractText(mockFile);

      expect(mockPage.cleanup).toHaveBeenCalled();
    });

    it('should handle cleanup errors gracefully', async () => {
      mockPage.cleanup.mockImplementation(() => {
        throw new Error('Cleanup error');
      });

      const result = await service.extractText(mockFile);

      // Should still succeed despite cleanup error
      expect(result.success).toBe(true);
    });
  });
});
