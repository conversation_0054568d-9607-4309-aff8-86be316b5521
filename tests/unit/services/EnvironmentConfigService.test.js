/**
 * Unit Tests for EnvironmentConfigService
 *
 * ASSIGNMENT-039: Environment Configuration Setup
 * Epic: EPIC-B01 - Subscription & Monetization System
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { EnvironmentConfigService } from '../../../src/services/EnvironmentConfigService.js';

// Mock StorageAPI
const mockStorageAPI = {
  get: vi.fn(),
  set: vi.fn()
};

vi.mock('../../../src/api/StorageAPI.js', () => ({
  StorageAPI: mockStorageAPI
}));

// Mock config validation
vi.mock('../../../src/utils/configValidation.js', () => ({
  validateConfig: vi.fn(() => ({ isValid: true, errors: [] }))
}));

describe('EnvironmentConfigService', () => {
  let service;

  beforeEach(() => {
    service = new EnvironmentConfigService();
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('constructor', () => {
    it('should initialize with default values', () => {
      expect(service.config).toEqual({});
      expect(service.isLoaded).toBe(false);
      expect(service.encryptionKey).toBeNull();
      expect(service.storageKey).toBe('mvat_environment_config');
    });
  });

  describe('initialize', () => {
    it('should initialize successfully with valid configuration', async () => {
      mockStorageAPI.get.mockResolvedValue({});
      mockStorageAPI.set.mockResolvedValue();

      const result = await service.initialize();

      expect(result).toBe(true);
      expect(service.isLoaded).toBe(true);
      expect(service.config).toBeDefined();
    });

    it('should handle initialization failure gracefully', async () => {
      mockStorageAPI.get.mockRejectedValue(new Error('Storage error'));

      const result = await service.initialize();

      expect(result).toBe(false);
      expect(service.isLoaded).toBe(false);
    });

    it('should load configuration with proper structure', async () => {
      mockStorageAPI.get.mockResolvedValue({});
      mockStorageAPI.set.mockResolvedValue();

      await service.initialize();

      expect(service.config).toHaveProperty('apiKeys');
      expect(service.config).toHaveProperty('company');
      expect(service.config).toHaveProperty('app');
      expect(service.config).toHaveProperty('features');
      expect(service.config).toHaveProperty('subscription');
      expect(service.config).toHaveProperty('localization');
    });
  });

  describe('get', () => {
    beforeEach(async () => {
      mockStorageAPI.get.mockResolvedValue({});
      mockStorageAPI.set.mockResolvedValue();
      await service.initialize();
    });

    it('should return configuration value by path', () => {
      service.config = {
        company: {
          name: 'Test Company'
        }
      };

      const result = service.get('company.name');
      expect(result).toBe('Test Company');
    });

    it('should return default value for non-existent path', () => {
      const result = service.get('non.existent.path', 'default');
      expect(result).toBe('default');
    });

    it('should return null for non-existent path without default', () => {
      const result = service.get('non.existent.path');
      expect(result).toBeNull();
    });

    it('should handle nested object access', () => {
      service.config = {
        company: {
          address: {
            city: 'Warsaw'
          }
        }
      };

      const result = service.get('company.address.city');
      expect(result).toBe('Warsaw');
    });

    it('should warn when configuration not loaded', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      service.isLoaded = false;

      const result = service.get('company.name', 'default');

      expect(result).toBe('default');
      expect(consoleSpy).toHaveBeenCalledWith('⚠️ Configuration not loaded yet');

      consoleSpy.mockRestore();
    });
  });

  describe('getApiKey', () => {
    beforeEach(async () => {
      mockStorageAPI.get.mockResolvedValue({});
      mockStorageAPI.set.mockResolvedValue();
      await service.initialize();
    });

    it('should return API key for valid service', () => {
      service.config = {
        apiKeys: {
          deepseek: {
            key: 'sk-test-key'
          }
        }
      };

      const result = service.getApiKey('deepseek');
      expect(result).toBe('sk-test-key');
    });

    it('should return null for non-existent service', () => {
      const result = service.getApiKey('nonexistent');
      expect(result).toBeNull();
    });
  });

  describe('getCompanyInfo', () => {
    beforeEach(async () => {
      mockStorageAPI.get.mockResolvedValue({});
      mockStorageAPI.set.mockResolvedValue();
      await service.initialize();
    });

    it('should return company information', () => {
      const companyInfo = {
        name: 'Test Company',
        nip: '1234567890'
      };
      service.config = { company: companyInfo };

      const result = service.getCompanyInfo();
      expect(result).toEqual(companyInfo);
    });

    it('should return empty object if no company info', () => {
      const result = service.getCompanyInfo();
      expect(result).toEqual({});
    });
  });

  describe('isFeatureEnabled', () => {
    beforeEach(async () => {
      mockStorageAPI.get.mockResolvedValue({});
      mockStorageAPI.set.mockResolvedValue();
      await service.initialize();
    });

    it('should return true for enabled feature', () => {
      service.config = {
        features: {
          subscriptionSystem: true
        }
      };

      const result = service.isFeatureEnabled('subscriptionSystem');
      expect(result).toBe(true);
    });

    it('should return false for disabled feature', () => {
      service.config = {
        features: {
          subscriptionSystem: false
        }
      };

      const result = service.isFeatureEnabled('subscriptionSystem');
      expect(result).toBe(false);
    });

    it('should return false for non-existent feature', () => {
      const result = service.isFeatureEnabled('nonexistent');
      expect(result).toBe(false);
    });
  });

  describe('getSubscriptionTier', () => {
    beforeEach(async () => {
      mockStorageAPI.get.mockResolvedValue({});
      mockStorageAPI.set.mockResolvedValue();
      await service.initialize();
    });

    it('should return subscription tier configuration', () => {
      const tierConfig = {
        invoiceLimit: 500,
        priceMonthly: 2900
      };
      service.config = {
        subscription: {
          tiers: {
            professional: tierConfig
          }
        }
      };

      const result = service.getSubscriptionTier('professional');
      expect(result).toEqual(tierConfig);
    });

    it('should return empty object for non-existent tier', () => {
      const result = service.getSubscriptionTier('nonexistent');
      expect(result).toEqual({});
    });
  });

  describe('getAll', () => {
    beforeEach(async () => {
      mockStorageAPI.get.mockResolvedValue({});
      mockStorageAPI.set.mockResolvedValue();
      await service.initialize();
    });

    it('should return masked configuration', () => {
      service.config = {
        apiKeys: {
          deepseek: {
            key: 'sk-secret-key'
          }
        },
        security: {
          encryptionKey: 'secret-encryption-key'
        },
        company: {
          name: 'Test Company'
        }
      };

      const result = service.getAll();

      expect(result.apiKeys.deepseek.key).toBe('***masked***');
      expect(result.security.encryptionKey).toBe('***masked***');
      expect(result.company.name).toBe('Test Company');
    });

    it('should return empty object when not loaded', () => {
      service.isLoaded = false;
      const result = service.getAll();
      expect(result).toEqual({});
    });
  });

  describe('reload', () => {
    it('should reset loaded state and reinitialize', async () => {
      mockStorageAPI.get.mockResolvedValue({});
      mockStorageAPI.set.mockResolvedValue();

      // First initialization
      await service.initialize();
      expect(service.isLoaded).toBe(true);

      // Reload
      const result = await service.reload();

      expect(result).toBe(true);
      expect(service.isLoaded).toBe(true);
    });
  });

  describe('loadEnvironmentFromFile', () => {
    it('should return environment variables with expected structure', async () => {
      const envVars = await service.loadEnvironmentFromFile();

      expect(envVars).toHaveProperty('DEEPSEEK_API_KEY');
      expect(envVars).toHaveProperty('COMPANY_NAME');
      expect(envVars).toHaveProperty('FEATURE_SUBSCRIPTION_SYSTEM');
      expect(envVars.COMPANY_NAME).toBe('MVAT Solutions');
      expect(envVars.FEATURE_SUBSCRIPTION_SYSTEM).toBe('true');
    });
  });

  describe('getDefaultEnvironmentVariables', () => {
    it('should return default environment variables', () => {
      const defaults = service.getDefaultEnvironmentVariables();

      expect(defaults).toHaveProperty('COMPANY_NAME');
      expect(defaults).toHaveProperty('DEEPSEEK_API_KEY');
      expect(defaults).toHaveProperty('FEATURE_SUBSCRIPTION_SYSTEM');
      expect(defaults.COMPANY_NAME).toBe('MVAT Solutions');
    });
  });

  describe('getPublicConfiguration', () => {
    beforeEach(async () => {
      mockStorageAPI.get.mockResolvedValue({});
      mockStorageAPI.set.mockResolvedValue();
      await service.initialize();
    });

    it('should return only public configuration sections', () => {
      service.config = {
        apiKeys: { deepseek: { key: 'secret' } },
        security: { encryptionKey: 'secret' },
        company: { name: 'Test' },
        app: { version: '1.0.0' },
        features: { subscriptionSystem: true }
      };

      const publicConfig = service.getPublicConfiguration();

      expect(publicConfig).toHaveProperty('company');
      expect(publicConfig).toHaveProperty('app');
      expect(publicConfig).toHaveProperty('features');
      expect(publicConfig).not.toHaveProperty('apiKeys');
      expect(publicConfig).not.toHaveProperty('security');
    });
  });

  describe('getSensitiveConfiguration', () => {
    beforeEach(async () => {
      mockStorageAPI.get.mockResolvedValue({});
      mockStorageAPI.set.mockResolvedValue();
      await service.initialize();
    });

    it('should return only sensitive configuration sections', () => {
      service.config = {
        apiKeys: { deepseek: { key: 'secret' } },
        security: { encryptionKey: 'secret' },
        company: { name: 'Test' },
        app: { version: '1.0.0' }
      };

      const sensitiveConfig = service.getSensitiveConfiguration();

      expect(sensitiveConfig).toHaveProperty('apiKeys');
      expect(sensitiveConfig).toHaveProperty('security');
      expect(sensitiveConfig).not.toHaveProperty('company');
      expect(sensitiveConfig).not.toHaveProperty('app');
    });
  });
});
