/**
 * Unit Tests for ConfigurationSourceManager
 *
 * Tests multi-source configuration loading functionality
 *
 * ASSIGNMENT-048: Settings Loading Enhancement
 * Epic: EPIC-005 - Enhanced AI Analysis & RAG Integration
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { ConfigurationSourceManager, CONFIG_SOURCES } from '../../../src/services/ConfigurationSourceManager.js';

// Mock dependencies
vi.mock('../../../src/services/EnvironmentConfigService.js', () => ({
  environmentConfig: {
    isLoaded: true,
    initialize: vi.fn().mockResolvedValue(undefined),
    getAll: vi.fn().mockReturnValue({
      company: { name: 'Test Company' },
      apiKeys: { deepseek: { key: 'test-key' } }
    }),
    _buildConfiguration: vi.fn().mockReturnValue({
      company: { name: 'Env Company' },
      apiKeys: { deepseek: { key: 'env-key' } }
    })
  }
}));

// Mock Chrome API
global.chrome = {
  storage: {
    local: {
      get: vi.fn()
    }
  },
  runtime: {
    lastError: null
  }
};

// Mock fetch
global.fetch = vi.fn();

describe('ConfigurationSourceManager', () => {
  let manager;

  beforeEach(() => {
    manager = new ConfigurationSourceManager();
    vi.clearAllMocks();
  });

  describe('🧪 Constructor and Basic Properties', () => {
    it('should initialize with default values', () => {
      expect(manager.getCurrentSource()).toBe(CONFIG_SOURCES.ENVIRONMENT_SERVICE);
      expect(manager.loadedConfig).toBeNull();
      expect(manager.isLoading).toBe(false);
      expect(manager.lastLoadTime).toBeNull();
    });

    it('should have all expected configuration sources', () => {
      const sources = manager.getAvailableSources();
      expect(sources).toHaveLength(4);

      const sourceIds = sources.map(s => s.id);
      expect(sourceIds).toContain(CONFIG_SOURCES.ENVIRONMENT_SERVICE);
      expect(sourceIds).toContain(CONFIG_SOURCES.CHROME_STORAGE);
      expect(sourceIds).toContain(CONFIG_SOURCES.ENV_FILE);
      expect(sourceIds).toContain(CONFIG_SOURCES.JSON_CONFIG);
    });
  });

  describe('🔧 Source Management', () => {
    it('should set current source successfully', () => {
      manager.setCurrentSource(CONFIG_SOURCES.CHROME_STORAGE);
      expect(manager.getCurrentSource()).toBe(CONFIG_SOURCES.CHROME_STORAGE);
    });

    it('should throw error for invalid source', () => {
      expect(() => {
        manager.setCurrentSource('invalid_source');
      }).toThrow('Invalid configuration source: invalid_source');
    });

    it('should return loading status', () => {
      const status = manager.getLoadingStatus();
      expect(status).toHaveProperty('isLoading');
      expect(status).toHaveProperty('currentSource');
      expect(status).toHaveProperty('lastLoadTime');
      expect(status).toHaveProperty('hasConfig');
      expect(status).toHaveProperty('errors');
    });
  });

  describe('🔄 Environment Service Loading', () => {
    it('should load from EnvironmentConfigService successfully', async () => {
      const config = await manager.loadConfiguration();

      expect(config).toEqual({
        company: { name: 'Test Company' },
        apiKeys: { deepseek: { key: 'test-key' } }
      });
      expect(manager.loadedConfig).toBe(config);
      expect(manager.lastLoadTime).toBeInstanceOf(Date);
    });

    it('should handle EnvironmentConfigService initialization', async () => {
      const { environmentConfig } = await import('../../../src/services/EnvironmentConfigService.js');
      environmentConfig.isLoaded = false;

      await manager.loadConfiguration();

      expect(environmentConfig.initialize).toHaveBeenCalled();
      expect(environmentConfig.getAll).toHaveBeenCalled();
    });
  });

  describe('💾 Chrome Storage Loading', () => {
    beforeEach(() => {
      manager.setCurrentSource(CONFIG_SOURCES.CHROME_STORAGE);
    });

    it('should load from Chrome storage successfully', async () => {
      const mockConfig = { company: { name: 'Chrome Company' } };
      chrome.storage.local.get.mockImplementation((keys, callback) => {
        callback({ environment_config: mockConfig });
      });

      const config = await manager.loadConfiguration();

      expect(config).toEqual(mockConfig);
      expect(chrome.storage.local.get).toHaveBeenCalledWith(['environment_config', 'settings'], expect.any(Function));
    });

    it('should handle Chrome storage errors', async () => {
      chrome.runtime.lastError = { message: 'Storage error' };
      chrome.storage.local.get.mockImplementation((keys, callback) => {
        callback({});
      });

      await expect(manager.loadConfiguration()).rejects.toThrow('Storage error');
    });

    it('should handle missing Chrome storage API', async () => {
      const originalChrome = global.chrome;
      global.chrome = {};

      await expect(manager.loadConfiguration()).rejects.toThrow('Chrome storage API not available');

      global.chrome = originalChrome;
    });
  });

  describe('📄 .env File Loading', () => {
    beforeEach(() => {
      manager.setCurrentSource(CONFIG_SOURCES.ENV_FILE);
    });

    it('should load from window.__MVAT_ENV__', async () => {
      global.window = {
        __MVAT_ENV__: {
          COMPANY_NAME: 'Window Company',
          DEEPSEEK_API_KEY: 'window-key'
        }
      };

      const config = await manager.loadConfiguration();

      expect(config).toEqual({
        company: { name: 'Env Company' },
        apiKeys: { deepseek: { key: 'env-key' } }
      });
    });

    it('should fetch .env file when window.__MVAT_ENV__ not available', async () => {
      global.window = {};
      global.fetch.mockResolvedValue({
        ok: true,
        text: () => Promise.resolve('COMPANY_NAME=Fetched Company\nDEEPSEEK_API_KEY=fetched-key')
      });

      const config = await manager.loadConfiguration();

      expect(fetch).toHaveBeenCalledWith('/.env');
      expect(config).toEqual({
        company: { name: 'Env Company' },
        apiKeys: { deepseek: { key: 'env-key' } }
      });
    });

    it('should handle .env file parsing', () => {
      const envContent = `
# Comment line
COMPANY_NAME="Test Company"
DEEPSEEK_API_KEY=sk-test123
EMPTY_LINE=

QUOTED_VALUE='Single Quotes'
`;

      const parsed = manager._parseEnvContent(envContent);

      expect(parsed).toEqual({
        COMPANY_NAME: 'Test Company',
        DEEPSEEK_API_KEY: 'sk-test123',
        EMPTY_LINE: '',
        QUOTED_VALUE: 'Single Quotes'
      });
    });

    it('should throw error when no environment variables found', async () => {
      global.window = {};
      global.fetch.mockRejectedValue(new Error('Fetch failed'));

      await expect(manager.loadConfiguration()).rejects.toThrow('No environment variables found');
    });
  });

  describe('📋 JSON Config Loading', () => {
    beforeEach(() => {
      manager.setCurrentSource(CONFIG_SOURCES.JSON_CONFIG);
    });

    it('should load from JSON config file successfully', async () => {
      const mockConfig = { company: { name: 'JSON Company' } };
      global.fetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockConfig)
      });

      const config = await manager.loadConfiguration();

      expect(config).toEqual(mockConfig);
      expect(fetch).toHaveBeenCalledWith('/config.json');
    });

    it('should handle JSON config fetch errors', async () => {
      global.fetch.mockResolvedValue({
        ok: false,
        status: 404,
        statusText: 'Not Found'
      });

      await expect(manager.loadConfiguration()).rejects.toThrow('Failed to load JSON config: HTTP 404: Not Found');
    });

    it('should handle JSON parsing errors', async () => {
      global.fetch.mockResolvedValue({
        ok: true,
        json: () => Promise.reject(new Error('Invalid JSON'))
      });

      await expect(manager.loadConfiguration()).rejects.toThrow('Failed to load JSON config: Invalid JSON');
    });
  });

  describe('🧪 Testing All Sources', () => {
    it('should test all sources and return results', async () => {
      // Mock successful loads for all sources
      const { environmentConfig } = await import('../../../src/services/EnvironmentConfigService.js');
      chrome.storage.local.get.mockImplementation((keys, callback) => {
        callback({ environment_config: {} });
      });
      global.window = { __MVAT_ENV__: { TEST: 'value' } };
      global.fetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({})
      });

      const results = await manager.testAllSources();

      expect(results).toHaveProperty(CONFIG_SOURCES.ENVIRONMENT_SERVICE);
      expect(results).toHaveProperty(CONFIG_SOURCES.CHROME_STORAGE);
      expect(results).toHaveProperty(CONFIG_SOURCES.ENV_FILE);
      expect(results).toHaveProperty(CONFIG_SOURCES.JSON_CONFIG);

      // Should restore original source
      expect(manager.getCurrentSource()).toBe(CONFIG_SOURCES.ENVIRONMENT_SERVICE);
    });

    it('should handle errors during source testing', async () => {
      // Mock Chrome storage error
      chrome.runtime.lastError = { message: 'Test error' };
      chrome.storage.local.get.mockImplementation((keys, callback) => {
        callback({});
      });

      const results = await manager.testAllSources();

      expect(results[CONFIG_SOURCES.CHROME_STORAGE]).toHaveProperty('success', false);
      expect(results[CONFIG_SOURCES.CHROME_STORAGE]).toHaveProperty('error');
    });

    it('should handle _buildConfiguration error gracefully', async () => {
      // Test the specific error that was reported
      manager.setCurrentSource(CONFIG_SOURCES.ENV_FILE);
      global.window = { __MVAT_ENV__: { COMPANY_NAME: 'Test' } };

      const results = await manager.testAllSources();

      // Should not throw the "_buildConfiguration is not a function" error anymore
      expect(results[CONFIG_SOURCES.ENV_FILE]).toHaveProperty('success');
      if (results[CONFIG_SOURCES.ENV_FILE].success) {
        expect(results[CONFIG_SOURCES.ENV_FILE]).toHaveProperty('loadTime');
        expect(results[CONFIG_SOURCES.ENV_FILE]).toHaveProperty('configSize');
      } else {
        expect(results[CONFIG_SOURCES.ENV_FILE]).toHaveProperty('error');
        expect(results[CONFIG_SOURCES.ENV_FILE].error).not.toContain('_buildConfiguration is not a function');
      }
    });

    it('should provide detailed error information', async () => {
      // Mock fetch to fail for JSON config
      global.fetch.mockRejectedValue(new Error('Failed to fetch'));

      const results = await manager.testAllSources();

      expect(results[CONFIG_SOURCES.JSON_CONFIG]).toHaveProperty('success', false);
      expect(results[CONFIG_SOURCES.JSON_CONFIG]).toHaveProperty('error');
      expect(results[CONFIG_SOURCES.JSON_CONFIG]).toHaveProperty('errorType');
      expect(results[CONFIG_SOURCES.JSON_CONFIG]).toHaveProperty('timestamp');
      expect(results[CONFIG_SOURCES.JSON_CONFIG].error).toContain('Failed to load JSON config');
    });
  });

  describe('🔄 Loading State Management', () => {
    it('should prevent concurrent loading', async () => {
      manager.isLoading = true;

      const config = await manager.loadConfiguration();

      expect(config).toBeNull(); // Should return cached config (null in this case)
    });

    it('should track loading errors', async () => {
      manager.setCurrentSource(CONFIG_SOURCES.CHROME_STORAGE);
      chrome.runtime.lastError = { message: 'Test error' };
      chrome.storage.local.get.mockImplementation((keys, callback) => {
        callback({});
      });

      try {
        await manager.loadConfiguration();
      } catch (error) {
        // Expected to throw
      }

      const status = manager.getLoadingStatus();
      expect(status.errors).toHaveProperty(CONFIG_SOURCES.CHROME_STORAGE);
      expect(status.errors[CONFIG_SOURCES.CHROME_STORAGE]).toHaveProperty('error');
      expect(status.errors[CONFIG_SOURCES.CHROME_STORAGE]).toHaveProperty('timestamp');
    });
  });
});

console.log('✅ ConfigurationSourceManager unit tests completed');
