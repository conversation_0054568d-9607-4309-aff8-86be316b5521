/**
 * Unit Tests for PDFMetadataExtractor
 * Tests comprehensive PDF metadata extraction functionality
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { PDFMetadataExtractor } from '../../../src/services/PDFMetadataExtractor.js';

// Mock PDF.js
const mockPDFJS = {
  getDocument: vi.fn()
};

// Mock PDF document
const mockPDF = {
  numPages: 3,
  pdfInfo: {
    version: '1.7',
    isLinearized: false
  },
  getMetadata: vi.fn(),
  getPage: vi.fn(),
  getOutline: vi.fn()
};

// Mock PDF page
const mockPage = {
  getViewport: vi.fn(),
  getTextContent: vi.fn(),
  getAnnotations: vi.fn(),
  cleanup: vi.fn()
};

// Setup global mocks
vi.mock('pdfjs-dist', () => mockPDFJS);

describe('PDFMetadataExtractor', () => {
  let extractor;
  let mockFile;

  beforeEach(() => {
    extractor = new PDFMetadataExtractor({
      extractStructure: true,
      extractSecurity: true,
      extractFonts: true,
      extractImages: true,
      analyzeContent: true
    });

    mockFile = {
      name: 'test-invoice.pdf',
      size: 2 * 1024 * 1024, // 2MB
      type: 'application/pdf',
      lastModified: Date.now(),
      arrayBuffer: vi.fn().mockResolvedValue(new ArrayBuffer(2048))
    };

    // Setup default mock responses
    mockPDF.getMetadata.mockResolvedValue({
      info: {
        Title: 'Test Invoice',
        Author: 'Test Company',
        Subject: 'Invoice Document',
        Keywords: 'invoice, tax, payment',
        Creator: 'Test Creator',
        Producer: 'Test Producer',
        CreationDate: 'D:20240115100000+01\'00\'',
        ModDate: 'D:20240115120000+01\'00\''
      }
    });

    mockPage.getViewport.mockReturnValue({
      width: 595,
      height: 842,
      rotation: 0
    });

    mockPage.getTextContent.mockResolvedValue({
      items: [
        { str: 'FAKTURA' },
        { str: 'Nr:' },
        { str: 'FV/2024/001' },
        { str: 'Sprzedawca:' },
        { str: 'Test' },
        { str: 'Company' },
        { str: 'Kwota:' },
        { str: '1234.56' },
        { str: 'PLN' }
      ]
    });

    mockPage.getAnnotations.mockResolvedValue([]);

    mockPDF.getPage.mockResolvedValue(mockPage);
    mockPDF.getOutline.mockResolvedValue([]);

    mockPDFJS.getDocument.mockReturnValue({
      promise: Promise.resolve(mockPDF)
    });

    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('initialization', () => {
    it('should initialize successfully', async () => {
      await extractor.initialize();
      expect(extractor.initialized).toBe(true);
    });

    it('should handle initialization errors', async () => {
      const errorExtractor = new PDFMetadataExtractor();

      // Mock an initialization error
      const originalConsoleError = console.error;
      console.error = vi.fn();

      await expect(errorExtractor.initialize()).resolves.not.toThrow();

      console.error = originalConsoleError;
    });
  });

  describe('basic metadata extraction', () => {
    it('should extract basic PDF metadata successfully', async () => {
      const result = await extractor.extractMetadata(mockFile);

      expect(result.success).toBe(true);
      expect(result.metadata).toBeDefined();

      const metadata = result.metadata;
      expect(metadata.fileName).toBe('test-invoice.pdf');
      expect(metadata.fileSize).toBe(2 * 1024 * 1024);
      expect(metadata.numPages).toBe(3);
      expect(metadata.title).toBe('Test Invoice');
      expect(metadata.author).toBe('Test Company');
      expect(metadata.pdfVersion).toBe('1.7');
    });

    it('should handle missing metadata gracefully', async () => {
      mockPDF.getMetadata.mockResolvedValue({ info: {} });

      const result = await extractor.extractMetadata(mockFile);

      expect(result.success).toBe(true);
      expect(result.metadata.title).toBe('');
      expect(result.metadata.author).toBe('');
    });

    it('should parse PDF dates correctly', async () => {
      const result = await extractor.extractMetadata(mockFile);

      expect(result.metadata.creationDate).toBeDefined();
      expect(result.metadata.creationDate.parsed).toBeTruthy();
      expect(result.metadata.creationDate.timestamp).toBeTypeOf('number');
    });

    it('should handle invalid PDF dates', async () => {
      mockPDF.getMetadata.mockResolvedValue({
        info: {
          CreationDate: 'invalid-date'
        }
      });

      const result = await extractor.extractMetadata(mockFile);

      expect(result.metadata.creationDate.raw).toBe('invalid-date');
      expect(result.metadata.creationDate.parsed).toBeNull();
    });
  });

  describe('document metadata extraction', () => {
    it('should extract page dimensions and orientation', async () => {
      const result = await extractor.extractMetadata(mockFile);

      expect(result.metadata.pageInfo).toBeDefined();
      expect(result.metadata.pageInfo.width).toBe(595);
      expect(result.metadata.pageInfo.height).toBe(842);
      expect(result.metadata.pageInfo.orientation).toBe('portrait');
      expect(result.metadata.documentType).toBe('A4');
    });

    it('should detect landscape orientation', async () => {
      mockPage.getViewport.mockReturnValue({
        width: 842,
        height: 595,
        rotation: 0
      });

      const result = await extractor.extractMetadata(mockFile);

      expect(result.metadata.pageInfo.orientation).toBe('landscape');
    });

    it('should estimate document types correctly', async () => {
      // Test Letter size
      mockPage.getViewport.mockReturnValue({
        width: 612,
        height: 792,
        rotation: 0
      });

      const result = await extractor.extractMetadata(mockFile);
      expect(result.metadata.documentType).toBe('Letter');
    });
  });

  describe('structure metadata extraction', () => {
    it('should detect document structure features', async () => {
      mockPDF.getOutline.mockResolvedValue([
        { title: 'Chapter 1' },
        { title: 'Chapter 2' }
      ]);

      mockPage.getAnnotations.mockResolvedValue([
        { subtype: 'Link' },
        { subtype: 'Text' }
      ]);

      const result = await extractor.extractMetadata(mockFile, {
        includeStructure: true
      });

      expect(result.metadata.structure).toBeDefined();
      expect(result.metadata.structure.hasOutline).toBe(true);
      expect(result.metadata.structure.outlineItems).toBe(2);
      expect(result.metadata.structure.hasAnnotations).toBe(true);
    });

    it('should analyze page variations', async () => {
      // Mock different page sizes
      mockPDF.getPage.mockImplementation((pageNum) => {
        const page = { ...mockPage };
        page.getViewport.mockReturnValue({
          width: pageNum === 1 ? 595 : 612,
          height: pageNum === 1 ? 842 : 792,
          rotation: 0
        });
        return Promise.resolve(page);
      });

      const result = await extractor.extractMetadata(mockFile, {
        includeStructure: true
      });

      expect(result.metadata.structure.pageVariations).toBeDefined();
      expect(result.metadata.structure.hasConsistentDimensions).toBe(false);
    });

    it('should handle structure extraction errors gracefully', async () => {
      mockPDF.getOutline.mockRejectedValue(new Error('Outline error'));

      const result = await extractor.extractMetadata(mockFile, {
        includeStructure: true
      });

      expect(result.success).toBe(true);
      expect(result.metadata.structure).toBeDefined();
    });
  });

  describe('content analysis', () => {
    it('should analyze text content and detect invoice indicators', async () => {
      const result = await extractor.extractMetadata(mockFile, {
        analyzeContent: true
      });

      expect(result.metadata.content).toBeDefined();
      expect(result.metadata.content.averageWordsPerPage).toBeGreaterThan(0);
      expect(result.metadata.content.textDensity).toBeGreaterThan(0);
      expect(result.metadata.content.invoiceIndicators).toBeDefined();
      expect(result.metadata.content.invoiceIndicators.isLikelyInvoice).toBe(true);
    });

    it('should detect language hints', async () => {
      const result = await extractor.extractMetadata(mockFile, {
        analyzeContent: true
      });

      expect(result.metadata.content.languageHints).toBeDefined();
      expect(result.metadata.content.languageHints.length).toBeGreaterThan(0);
      expect(result.metadata.content.languageHints[0].language).toBe('Polish');
    });

    it('should handle content analysis with no text', async () => {
      mockPage.getTextContent.mockResolvedValue({ items: [] });

      const result = await extractor.extractMetadata(mockFile, {
        analyzeContent: true
      });

      expect(result.metadata.content.averageWordsPerPage).toBe(0);
      expect(result.metadata.content.textDensity).toBe(0);
    });

    it('should limit analysis to configured number of pages', async () => {
      const limitedExtractor = new PDFMetadataExtractor({
        maxAnalysisPages: 2
      });

      const getPageSpy = vi.spyOn(mockPDF, 'getPage');

      await limitedExtractor.extractMetadata(mockFile, {
        analyzeContent: true
      });

      // Should only call getPage for 2 pages (plus 1 for document metadata)
      expect(getPageSpy).toHaveBeenCalledTimes(3); // 1 for document + 2 for analysis
    });
  });

  describe('invoice content analysis', () => {
    it('should detect high-confidence invoice content', () => {
      const text = 'FAKTURA Nr: FV/2024/001 Sprzedawca: Test Company NIP: 123-456-78-90 Nabywca: Client Corp Kwota netto: 1000.00 PLN VAT: 230.00 PLN Razem: 1230.00 PLN';

      const analysis = extractor.analyzeInvoiceIndicators(text);

      expect(analysis.isLikelyInvoice).toBe(true);
      expect(analysis.confidence).toBeGreaterThan(70);
      expect(analysis.indicators.length).toBeGreaterThan(3);
    });

    it('should detect low-confidence non-invoice content', () => {
      const text = 'This is a regular document with some text content that does not contain invoice-related information.';

      const analysis = extractor.analyzeInvoiceIndicators(text);

      expect(analysis.isLikelyInvoice).toBe(false);
      expect(analysis.confidence).toBeLessThan(30);
    });

    it('should detect number patterns in invoice text', () => {
      const text = 'Invoice FV/2024/001 Amount: 1,234.56 EUR Date: 2024/01/15';

      const analysis = extractor.analyzeInvoiceIndicators(text);

      expect(analysis.indicators.some(ind => ind.category === 'Number Patterns')).toBe(true);
    });
  });

  describe('language detection', () => {
    it('should detect Polish language indicators', () => {
      const text = 'faktura sprzedawca nabywca kwota podatek nip regon';

      const hints = extractor.detectLanguageHints(text);

      expect(hints.length).toBeGreaterThan(0);
      expect(hints[0].language).toBe('Polish');
      expect(hints[0].confidence).toBeGreaterThan(0.5);
    });

    it('should detect English language indicators', () => {
      const text = 'invoice seller buyer amount tax total bill receipt';

      const hints = extractor.detectLanguageHints(text);

      expect(hints.length).toBeGreaterThan(0);
      expect(hints[0].language).toBe('English');
    });

    it('should detect multiple languages', () => {
      const text = 'invoice faktura seller sprzedawca amount kwota';

      const hints = extractor.detectLanguageHints(text);

      expect(hints.length).toBeGreaterThanOrEqual(2);
      expect(hints.map(h => h.language)).toContain('English');
      expect(hints.map(h => h.language)).toContain('Polish');
    });
  });

  describe('utility functions', () => {
    it('should format file sizes correctly', () => {
      expect(extractor.formatFileSize(0)).toBe('0 Bytes');
      expect(extractor.formatFileSize(1024)).toBe('1 KB');
      expect(extractor.formatFileSize(1024 * 1024)).toBe('1 MB');
      expect(extractor.formatFileSize(1536)).toBe('1.5 KB');
    });

    it('should estimate document types from dimensions', () => {
      expect(extractor.estimateDocumentType({ width: 595, height: 842, aspectRatio: 595 / 842 })).toBe('A4');
      expect(extractor.estimateDocumentType({ width: 612, height: 792, aspectRatio: 612 / 792 })).toBe('Letter');
      expect(extractor.estimateDocumentType({ width: 1000, height: 500, aspectRatio: 2 })).toBe('Banner/Wide');
    });
  });

  describe('error handling', () => {
    it('should handle PDF loading errors', async () => {
      mockPDFJS.getDocument.mockReturnValue({
        promise: Promise.reject(new Error('PDF loading failed'))
      });

      const result = await extractor.extractMetadata(mockFile);

      expect(result.success).toBe(false);
      expect(result.error).toContain('PDF loading failed');
      expect(result.metadata).toBeNull();
    });

    it('should handle page processing errors', async () => {
      mockPDF.getPage.mockRejectedValue(new Error('Page error'));

      const result = await extractor.extractMetadata(mockFile, {
        includeStructure: true
      });

      expect(result.success).toBe(true);
      // Should still succeed with partial data
    });

    it('should handle text content extraction errors', async () => {
      mockPage.getTextContent.mockRejectedValue(new Error('Text extraction error'));

      const result = await extractor.extractMetadata(mockFile, {
        analyzeContent: true
      });

      expect(result.success).toBe(true);
      expect(result.metadata.content.averageWordsPerPage).toBe(0);
    });
  });

  describe('progress tracking', () => {
    it('should report progress during extraction', async () => {
      const progressEvents = [];
      const onProgress = (event) => progressEvents.push(event);

      await extractor.extractMetadata(mockFile, {
        onProgress
      });

      expect(progressEvents.length).toBeGreaterThan(0);
      expect(progressEvents[0].stage).toBe('loading');
      expect(progressEvents[progressEvents.length - 1].stage).toBe('finalizing');
    });
  });

  describe('configuration management', () => {
    it('should respect configuration options', async () => {
      const limitedExtractor = new PDFMetadataExtractor({
        extractStructure: false,
        analyzeContent: false
      });

      const result = await limitedExtractor.extractMetadata(mockFile);

      expect(result.metadata.structure).toEqual({});
      expect(result.metadata.content).toEqual({});
    });

    it('should allow configuration updates', () => {
      const config = extractor.getConfig();
      expect(config.extractStructure).toBe(true);

      extractor.updateConfig({ extractStructure: false });
      const updatedConfig = extractor.getConfig();
      expect(updatedConfig.extractStructure).toBe(false);
    });
  });
});
