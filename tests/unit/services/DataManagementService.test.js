import { describe, it, expect, beforeEach, vi } from 'vitest';
import { DataManagementService } from '../../../src/services/DataManagementService.js';

// Mock Chrome APIs
global.chrome = {
  storage: {
    local: {
      get: vi.fn(),
      set: vi.fn(),
      remove: vi.fn()
    }
  }
};

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn()
};
global.localStorage = localStorageMock;

// Mock URL and Blob for file operations
global.URL = {
  createObjectURL: vi.fn(() => 'blob:mock-url'),
  revokeObjectURL: vi.fn()
};

global.Blob = vi.fn();

// Mock document for file download
global.document = {
  createElement: vi.fn(() => ({
    href: '',
    download: '',
    click: vi.fn()
  }))
};

describe('DataManagementService', () => {
  let service;

  beforeEach(() => {
    service = new DataManagementService();
    vi.clearAllMocks();
  });

  describe('Storage Statistics', () => {
    it('should calculate storage statistics correctly', async () => {
      const mockData = {
        settings: { company: { name: 'Test' } },
        invoices: [{ id: 1 }, { id: 2 }],
        cache: { key: 'value' }
      };

      chrome.storage.local.get.mockResolvedValue(mockData);

      const stats = await service.getStorageStats();

      expect(stats).toHaveProperty('totalSize');
      expect(stats).toHaveProperty('totalSizeFormatted');
      expect(stats).toHaveProperty('itemCount');
      expect(stats).toHaveProperty('items');
      expect(stats.itemCount).toBe(3);
      expect(stats.items).toHaveProperty('settings');
      expect(stats.items).toHaveProperty('invoices');
      expect(stats.items).toHaveProperty('cache');
    });

    it('should handle empty storage', async () => {
      chrome.storage.local.get.mockResolvedValue({});

      const stats = await service.getStorageStats();

      expect(stats.totalSize).toBe(2); // Empty object "{}" has 2 characters
      expect(stats.itemCount).toBe(0);
      expect(Object.values(stats.items).every(item => item.size === 0)).toBe(true);
    });

    it('should fallback to localStorage when Chrome storage unavailable', async () => {
      // Temporarily remove chrome.storage
      const originalChrome = global.chrome;
      global.chrome = undefined;

      localStorageMock.getItem.mockReturnValue('{"test": "data"}');

      const stats = await service.getStorageStats();

      expect(stats).toHaveProperty('totalSize');
      expect(stats).toHaveProperty('items');

      // Restore chrome
      global.chrome = originalChrome;
    });
  });

  describe('Clear Data Operations', () => {
    it('should clear all data by default', async () => {
      chrome.storage.local.remove.mockResolvedValue();

      const result = await service.clearAllData();

      expect(result.success).toBe(true);
      expect(chrome.storage.local.remove).toHaveBeenCalledWith([
        'invoices', 'documents', 'processingHistory', 'uploadHistory', 'cache'
      ]);
      expect(result.clearedKeys).toContain('cache');
    });

    it('should preserve settings when keepSettings is true', async () => {
      chrome.storage.local.remove.mockResolvedValue();

      const result = await service.clearAllData({ keepSettings: true });

      expect(result.success).toBe(true);
      expect(result.clearedKeys).not.toContain('settings');
      expect(result.clearedKeys).toContain('cache');
    });

    it('should preserve API keys when keepApiKeys is true', async () => {
      chrome.storage.local.remove.mockResolvedValue();

      const result = await service.clearAllData({ keepApiKeys: true });

      expect(result.success).toBe(true);
      expect(result.clearedKeys).not.toContain('encryptedApiKeys');
      expect(result.clearedKeys).toContain('cache');
    });

    it('should preserve documents when keepDocuments is true', async () => {
      chrome.storage.local.remove.mockResolvedValue();

      const result = await service.clearAllData({ keepDocuments: true });

      expect(result.success).toBe(true);
      expect(result.clearedKeys).not.toContain('invoices');
      expect(result.clearedKeys).not.toContain('documents');
      expect(result.clearedKeys).toContain('cache');
    });

    it('should handle Chrome storage errors', async () => {
      chrome.storage.local.remove.mockRejectedValue(new Error('Storage error'));

      await expect(service.clearAllData()).rejects.toThrow('Storage error');
    });
  });

  describe('Settings Export', () => {
    it('should export settings without API keys', async () => {
      const mockSettings = {
        company: { name: 'Test Company' },
        display: { currency: 'PLN' },
        processing: { ocrLanguage: 'pol' },
        apiKeys: { deepseek: 'secret-key' }
      };

      global.Blob.mockImplementation((content, options) => ({
        content,
        type: options.type
      }));

      const result = await service.exportSettings(mockSettings);

      expect(result.success).toBe(true);
      expect(result.filename).toMatch(/mvat-settings-\d{4}-\d{2}-\d{2}\.json/);
      expect(global.Blob).toHaveBeenCalledWith(
        expect.arrayContaining([expect.stringContaining('Test Company')]),
        { type: 'application/json' }
      );
      expect(global.URL.createObjectURL).toHaveBeenCalled();
      expect(global.document.createElement).toHaveBeenCalledWith('a');
    });

    it('should create proper export structure', async () => {
      const mockSettings = {
        company: { name: 'Test' },
        display: { currency: 'PLN' }
      };

      let exportedData;
      global.Blob.mockImplementation((content) => {
        exportedData = JSON.parse(content[0]);
        return { content, type: 'application/json' };
      });

      await service.exportSettings(mockSettings);

      expect(exportedData).toHaveProperty('version', '1.0');
      expect(exportedData).toHaveProperty('exportDate');
      expect(exportedData).toHaveProperty('application', 'MVAT Chrome Extension');
      expect(exportedData).toHaveProperty('data');
      expect(exportedData.data).toHaveProperty('company');
      expect(exportedData.data.apiKeys).toEqual({});
    });
  });

  describe('Settings Import', () => {
    it('should import valid settings file', async () => {
      const mockFile = {
        type: 'application/json',
        text: vi.fn().mockResolvedValue(JSON.stringify({
          version: '1.0',
          exportDate: '2025-01-28T00:00:00.000Z',
          application: 'MVAT Chrome Extension',
          data: {
            company: { name: 'Imported Company' },
            display: { currency: 'EUR' },
            processing: { ocrLanguage: 'eng' }
          }
        }))
      };

      const result = await service.importSettings(mockFile);

      expect(result.success).toBe(true);
      expect(result.settings).toHaveProperty('company');
      expect(result.settings.company.name).toBe('Imported Company');
      expect(result.version).toBe('1.0');
    });

    it('should reject non-JSON files', async () => {
      const mockFile = {
        type: 'text/plain',
        text: vi.fn()
      };

      await expect(service.importSettings(mockFile)).rejects.toThrow('Please select a valid JSON file');
    });

    it('should reject invalid file format', async () => {
      const mockFile = {
        type: 'application/json',
        text: vi.fn().mockResolvedValue(JSON.stringify({
          invalidFormat: true
        }))
      };

      await expect(service.importSettings(mockFile)).rejects.toThrow('Invalid settings file format');
    });

    it('should validate settings structure', async () => {
      const mockFile = {
        type: 'application/json',
        text: vi.fn().mockResolvedValue(JSON.stringify({
          version: '1.0',
          data: {
            // Missing required sections
            invalidSection: {}
          }
        }))
      };

      await expect(service.importSettings(mockFile)).rejects.toThrow();
    });
  });

  describe('Reset to Defaults', () => {
    it('should reset settings using Chrome storage', async () => {
      const defaultSettings = {
        company: { name: '' },
        display: { currency: 'PLN' },
        processing: { ocrLanguage: 'pol' }
      };

      chrome.storage.local.set.mockResolvedValue();

      const result = await service.resetToDefaults(defaultSettings);

      expect(result.success).toBe(true);
      expect(chrome.storage.local.set).toHaveBeenCalledWith({ settings: defaultSettings });
    });

    it('should fallback to localStorage when Chrome storage unavailable', async () => {
      const originalChrome = global.chrome;
      global.chrome = undefined;

      const defaultSettings = { test: 'data' };

      const result = await service.resetToDefaults(defaultSettings);

      expect(result.success).toBe(true);
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'mvat_settings',
        JSON.stringify(defaultSettings)
      );

      global.chrome = originalChrome;
    });
  });

  describe('Utility Functions', () => {
    it('should format bytes correctly', () => {
      expect(service.formatBytes(0)).toBe('0 B');
      expect(service.formatBytes(1024)).toBe('1 KB');
      expect(service.formatBytes(1048576)).toBe('1 MB');
      expect(service.formatBytes(1073741824)).toBe('1 GB');
      expect(service.formatBytes(1536)).toBe('1.5 KB');
    });

    it('should validate settings structure correctly', () => {
      const validSettings = {
        company: { name: 'Test' },
        display: { currency: 'PLN' },
        processing: { ocrLanguage: 'pol' }
      };

      expect(() => service.validateSettingsStructure(validSettings)).not.toThrow();

      const invalidSettings = {
        company: { name: 'Test' }
        // Missing display and processing
      };

      expect(() => service.validateSettingsStructure(invalidSettings)).toThrow();
    });

    it('should validate specific field requirements', () => {
      const settingsWithoutCurrency = {
        company: { name: 'Test' },
        display: {},
        processing: { ocrLanguage: 'pol' }
      };

      expect(() => service.validateSettingsStructure(settingsWithoutCurrency))
        .toThrow('Display settings missing currency');

      const settingsWithoutOCR = {
        company: { name: 'Test' },
        display: { currency: 'PLN' },
        processing: {}
      };

      expect(() => service.validateSettingsStructure(settingsWithoutOCR))
        .toThrow('Processing settings missing OCR language');
    });
  });

  describe('Error Handling', () => {
    it('should handle storage errors gracefully', async () => {
      chrome.storage.local.get.mockRejectedValue(new Error('Storage unavailable'));

      await expect(service.getStorageStats()).rejects.toThrow('Storage unavailable');
    });

    it('should handle file reading errors', async () => {
      const mockFile = {
        type: 'application/json',
        text: vi.fn().mockRejectedValue(new Error('File read error'))
      };

      await expect(service.importSettings(mockFile)).rejects.toThrow('File read error');
    });

    it('should handle JSON parsing errors', async () => {
      const mockFile = {
        type: 'application/json',
        text: vi.fn().mockResolvedValue('invalid json')
      };

      await expect(service.importSettings(mockFile)).rejects.toThrow();
    });
  });
});
