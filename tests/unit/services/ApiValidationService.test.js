import { describe, it, expect, beforeEach, vi } from 'vitest';
import { ApiValidationService } from '../../../src/services/ApiValidationService.js';

// Mock fetch
global.fetch = vi.fn();

describe('ApiValidationService', () => {
  let apiValidationService;

  beforeEach(() => {
    apiValidationService = new ApiValidationService();
    vi.clearAllMocks();
  });

  describe('Key Format Validation', () => {
    it('should validate DeepSeek API key format', () => {
      const validKey = 'sk-' + 'a'.repeat(32);
      const result = apiValidationService.validateKeyFormat('deepseek', validKey);

      expect(result.valid).toBe(true);
      expect(result.provider).toBe('DeepSeek');
      expect(result.keyLength).toBe(validKey.length);
    });

    it('should validate OpenAI API key format', () => {
      const validKey = 'sk-' + 'a'.repeat(32);
      const result = apiValidationService.validateKeyFormat('openai', validKey);

      expect(result.valid).toBe(true);
      expect(result.provider).toBe('OpenAI');
    });

    it('should validate Fakturownia API key format', () => {
      const validKey = 'a'.repeat(25);
      const result = apiValidationService.validateKeyFormat('fakturownia', validKey);

      expect(result.valid).toBe(true);
      expect(result.provider).toBe('Fakturownia');
    });

    it('should validate Infakt API key format', () => {
      const validKey = 'a'.repeat(25);
      const result = apiValidationService.validateKeyFormat('infakt', validKey);

      expect(result.valid).toBe(true);
      expect(result.provider).toBe('Infakt');
    });

    it('should reject invalid key formats', () => {
      const invalidKeys = [
        { provider: 'deepseek', key: 'invalid-key' },
        { provider: 'openai', key: 'sk-short' },
        { provider: 'fakturownia', key: 'short' },
        { provider: 'infakt', key: '' }
      ];

      invalidKeys.forEach(({ provider, key }) => {
        const result = apiValidationService.validateKeyFormat(provider, key);
        expect(result.valid).toBe(false);
        expect(result.error).toBeTruthy();
      });
    });

    it('should handle unknown providers', () => {
      const result = apiValidationService.validateKeyFormat('unknown', 'any-key');

      expect(result.valid).toBe(false);
      expect(result.error).toBe('Unknown provider');
    });

    it('should handle empty or null keys', () => {
      const emptyResults = [
        apiValidationService.validateKeyFormat('deepseek', ''),
        apiValidationService.validateKeyFormat('deepseek', null),
        apiValidationService.validateKeyFormat('deepseek', undefined),
        apiValidationService.validateKeyFormat('deepseek', '   ')
      ];

      emptyResults.forEach(result => {
        expect(result.valid).toBe(false);
        expect(result.error).toContain('required');
      });
    });
  });

  describe('Connection Testing', () => {
    it('should test successful API connection', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        text: vi.fn().mockResolvedValue('{"data": "success"}')
      };
      fetch.mockResolvedValue(mockResponse);

      const validKey = 'sk-' + 'a'.repeat(32);
      const result = await apiValidationService.testConnection('deepseek', validKey);

      expect(result.success).toBe(true);
      expect(result.provider).toBe('DeepSeek');
      expect(result.status).toBe(200);
      expect(result.responseTime).toBeGreaterThan(0);
      expect(result.message).toBe('Connection successful');
    });

    it('should handle API connection failures', async () => {
      const mockResponse = {
        ok: false,
        status: 401,
        text: vi.fn().mockResolvedValue('Unauthorized')
      };
      fetch.mockResolvedValue(mockResponse);

      const validKey = 'sk-' + 'a'.repeat(32);
      const result = await apiValidationService.testConnection('deepseek', validKey);

      expect(result.success).toBe(false);
      expect(result.provider).toBe('DeepSeek');
      expect(result.status).toBe(401);
      expect(result.error).toContain('HTTP 401');
    });

    it('should handle network errors', async () => {
      fetch.mockRejectedValue(new Error('Network error'));

      const validKey = 'sk-' + 'a'.repeat(32);
      const result = await apiValidationService.testConnection('deepseek', validKey);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Network error');
    });

    it('should handle connection timeout', async () => {
      // Mock a long-running request
      fetch.mockImplementation(() =>
        new Promise(resolve => setTimeout(resolve, 15000))
      );

      const validKey = 'sk-' + 'a'.repeat(32);
      const result = await apiValidationService.testConnection('deepseek', validKey);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Connection timeout');
    });

    it('should reject invalid key format before testing', async () => {
      const result = await apiValidationService.testConnection('deepseek', 'invalid-key');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid DeepSeek API key format');
      expect(fetch).not.toHaveBeenCalled();
    });
  });

  describe('Authentication Headers', () => {
    it('should generate correct headers for DeepSeek', () => {
      const headers = apiValidationService.getAuthHeaders('deepseek', 'test-key');

      expect(headers.Authorization).toBe('Bearer test-key');
      expect(headers['Content-Type']).toBe('application/json');
    });

    it('should generate correct headers for OpenAI', () => {
      const headers = apiValidationService.getAuthHeaders('openai', 'test-key');

      expect(headers.Authorization).toBe('Bearer test-key');
      expect(headers['Content-Type']).toBe('application/json');
    });

    it('should generate correct headers for Fakturownia', () => {
      const headers = apiValidationService.getAuthHeaders('fakturownia', 'test-key');

      expect(headers['X-API-TOKEN']).toBe('test-key');
      expect(headers.Accept).toBe('application/json');
    });

    it('should generate correct headers for Infakt', () => {
      const headers = apiValidationService.getAuthHeaders('infakt', 'test-key');

      expect(headers['X-inFakt-ApiKey']).toBe('test-key');
      expect(headers['Content-Type']).toBe('application/json');
    });

    it('should handle unknown providers', () => {
      const headers = apiValidationService.getAuthHeaders('unknown', 'test-key');

      expect(headers['Content-Type']).toBe('application/json');
      expect(headers.Authorization).toBeUndefined();
    });
  });

  describe('Batch Testing', () => {
    it('should test all provided API keys', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        text: vi.fn().mockResolvedValue('{"data": "success"}')
      };
      fetch.mockResolvedValue(mockResponse);

      const apiKeys = {
        deepseek: 'sk-' + 'a'.repeat(32),
        openai: 'sk-' + 'b'.repeat(32),
        fakturownia: 'c'.repeat(25),
        infakt: ''
      };

      const result = await apiValidationService.testAllConnections(apiKeys);

      expect(result.results.deepseek.success).toBe(true);
      expect(result.results.openai.success).toBe(true);
      expect(result.results.fakturownia.success).toBe(true);
      expect(result.results.infakt.skipped).toBe(true);

      expect(result.summary.total).toBe(4);
      expect(result.summary.successful).toBe(3);
      expect(result.summary.skipped).toBe(1);
    });

    it('should generate correct summary statistics', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        text: vi.fn().mockResolvedValue('{"data": "success"}')
      };
      fetch.mockResolvedValue(mockResponse);

      const apiKeys = {
        deepseek: 'sk-' + 'a'.repeat(32),
        openai: 'sk-' + 'b'.repeat(32)
      };

      const result = await apiValidationService.testAllConnections(apiKeys);

      expect(result.summary.total).toBe(2);
      expect(result.summary.successful).toBe(2);
      expect(result.summary.failed).toBe(0);
      expect(result.summary.skipped).toBe(0);
      expect(result.summary.successRate).toBe(100);
    });

    it('should handle mixed success/failure results', async () => {
      fetch
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          text: vi.fn().mockResolvedValue('success')
        })
        .mockResolvedValueOnce({
          ok: false,
          status: 401,
          text: vi.fn().mockResolvedValue('Unauthorized')
        });

      const apiKeys = {
        deepseek: 'sk-' + 'a'.repeat(32),
        openai: 'sk-' + 'b'.repeat(32)
      };

      const result = await apiValidationService.testAllConnections(apiKeys);

      expect(result.summary.successful).toBe(1);
      expect(result.summary.failed).toBe(1);
      expect(result.summary.successRate).toBe(50);
    });
  });

  describe('Provider Information', () => {
    it('should return supported providers list', () => {
      const providers = apiValidationService.getSupportedProviders();

      expect(providers).toHaveLength(4);
      expect(providers.map(p => p.id)).toEqual(['deepseek', 'openai', 'fakturownia', 'infakt']);
      expect(providers.map(p => p.name)).toEqual(['DeepSeek', 'OpenAI', 'Fakturownia', 'Infakt']);

      providers.forEach(provider => {
        expect(provider.keyFormat).toBeTruthy();
      });
    });
  });

  describe('Edge Cases', () => {
    it('should handle very long API keys', () => {
      const longKey = 'sk-' + 'a'.repeat(1000);
      const result = apiValidationService.validateKeyFormat('deepseek', longKey);

      expect(result.valid).toBe(true);
    });

    it('should handle keys with special characters', () => {
      const specialKey = 'sk-abc123_-+=';
      const result = apiValidationService.validateKeyFormat('deepseek', specialKey);

      // This should fail because the regex expects alphanumeric only
      expect(result.valid).toBe(false);
    });

    it('should trim whitespace from keys', () => {
      const keyWithSpaces = '  sk-' + 'a'.repeat(32) + '  ';
      const result = apiValidationService.validateKeyFormat('deepseek', keyWithSpaces);

      expect(result.valid).toBe(true);
    });
  });
});
