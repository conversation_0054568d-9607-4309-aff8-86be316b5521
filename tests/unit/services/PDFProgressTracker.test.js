/**
 * Unit Tests for PDFProgressTracker
 * Tests enhanced progress tracking functionality
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { PDFProgressTracker } from '../../../src/services/PDFProgressTracker.js';

// Mock Chrome storage
const mockChrome = {
  storage: {
    local: {
      set: vi.fn().mockResolvedValue(undefined),
      get: vi.fn().mockResolvedValue({})
    }
  }
};

// Mock performance API
const mockPerformance = {
  memory: {
    usedJSHeapSize: 50 * 1024 * 1024, // 50MB
    totalJSHeapSize: 100 * 1024 * 1024, // 100MB
    jsHeapSizeLimit: 2 * 1024 * 1024 * 1024 // 2GB
  }
};

// Setup global mocks
global.chrome = mockChrome;
global.performance = mockPerformance;

describe('PDFProgressTracker', () => {
  let tracker;
  let mockFile;

  beforeEach(() => {
    tracker = new PDFProgressTracker({
      persistProgress: false, // Disable persistence for tests
      trackMemory: true,
      trackPerformance: true
    });

    mockFile = {
      name: 'test-invoice.pdf',
      size: 2 * 1024 * 1024, // 2MB
      type: 'application/pdf'
    };

    vi.clearAllMocks();
  });

  afterEach(() => {
    // Clean up any active sessions
    for (const [trackingId] of tracker.sessions) {
      tracker.cancelSession(trackingId);
    }
  });

  describe('initialization', () => {
    it('should initialize successfully', async () => {
      await tracker.initialize();
      expect(tracker.initialized).toBe(true);
    });

    it('should handle initialization errors gracefully', async () => {
      // Mock storage error
      mockChrome.storage.local.get.mockRejectedValueOnce(new Error('Storage error'));

      await expect(tracker.initialize()).resolves.not.toThrow();
      expect(tracker.initialized).toBe(true);
    });
  });

  describe('session management', () => {
    it('should start tracking a new session', () => {
      const trackingId = 'test-session-1';
      const sessionTracker = tracker.startTracking(trackingId, {
        fileName: mockFile.name,
        fileSize: mockFile.size,
        totalPages: 5
      });

      expect(tracker.sessions.has(trackingId)).toBe(true);
      expect(sessionTracker).toHaveProperty('updateStage');
      expect(sessionTracker).toHaveProperty('updateProgress');
      expect(sessionTracker).toHaveProperty('complete');

      const session = tracker.sessions.get(trackingId);
      expect(session.fileName).toBe(mockFile.name);
      expect(session.fileSize).toBe(mockFile.size);
      expect(session.totalPages).toBe(5);
      expect(session.status).toBe('active');
    });

    it('should handle progress callbacks', () => {
      const onProgress = vi.fn();
      const trackingId = 'test-session-2';

      const sessionTracker = tracker.startTracking(trackingId, {
        fileName: mockFile.name,
        fileSize: mockFile.size,
        onProgress
      });

      sessionTracker.updateStage('loading', 50);

      expect(onProgress).toHaveBeenCalledWith(
        expect.objectContaining({
          trackingId,
          stage: 'loading',
          fileName: mockFile.name,
          fileSize: mockFile.size
        })
      );
    });

    it('should calculate overall progress correctly', () => {
      const trackingId = 'test-session-3';
      const sessionTracker = tracker.startTracking(trackingId, {
        fileName: mockFile.name,
        fileSize: mockFile.size
      });

      // Update to loading stage with 100% progress
      sessionTracker.updateStage('loading', 100);

      const status = sessionTracker.getStatus();
      expect(status.currentProgress).toBeGreaterThan(0);
      expect(status.currentProgress).toBeLessThan(100);

      // Move to extracting stage
      sessionTracker.updateStage('extracting', 50);

      const updatedStatus = sessionTracker.getStatus();
      expect(updatedStatus.currentProgress).toBeGreaterThan(status.currentProgress);
    });

    it('should complete a session successfully', () => {
      const onComplete = vi.fn();
      const trackingId = 'test-session-4';

      const sessionTracker = tracker.startTracking(trackingId, {
        fileName: mockFile.name,
        fileSize: mockFile.size,
        onComplete
      });

      const result = { success: true, text: 'extracted text' };
      sessionTracker.complete(result);

      const session = tracker.sessions.get(trackingId);
      expect(session.status).toBe('completed');
      expect(session.result).toEqual(result);
      expect(session.currentProgress).toBe(100);
      expect(onComplete).toHaveBeenCalledWith(result);
    });

    it('should cancel a session', () => {
      const trackingId = 'test-session-5';
      const sessionTracker = tracker.startTracking(trackingId, {
        fileName: mockFile.name,
        fileSize: mockFile.size
      });

      sessionTracker.cancel();

      const session = tracker.sessions.get(trackingId);
      expect(session.status).toBe('cancelled');
      expect(session.endTime).toBeTruthy();
    });
  });

  describe('error and warning handling', () => {
    it('should add errors to session', () => {
      const onError = vi.fn();
      const trackingId = 'test-session-6';

      const sessionTracker = tracker.startTracking(trackingId, {
        fileName: mockFile.name,
        fileSize: mockFile.size,
        onError
      });

      const error = new Error('Test error');
      sessionTracker.addError(error);

      const session = tracker.sessions.get(trackingId);
      expect(session.errors).toHaveLength(1);
      expect(session.errors[0].message).toBe('Test error');
      expect(onError).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Test error'
        })
      );
    });

    it('should add warnings to session', () => {
      const trackingId = 'test-session-7';
      const sessionTracker = tracker.startTracking(trackingId, {
        fileName: mockFile.name,
        fileSize: mockFile.size
      });

      sessionTracker.addWarning('Test warning');

      const session = tracker.sessions.get(trackingId);
      expect(session.warnings).toHaveLength(1);
      expect(session.warnings[0].message).toBe('Test warning');
    });
  });

  describe('metadata management', () => {
    it('should set and update metadata', () => {
      const trackingId = 'test-session-8';
      const sessionTracker = tracker.startTracking(trackingId, {
        fileName: mockFile.name,
        fileSize: mockFile.size
      });

      sessionTracker.setMetadata({ totalPages: 10, language: 'English' });
      sessionTracker.setMetadata({ author: 'Test Author' });

      const session = tracker.sessions.get(trackingId);
      expect(session.metadata).toEqual({
        totalPages: 10,
        language: 'English',
        author: 'Test Author'
      });
    });
  });

  describe('memory monitoring', () => {
    it('should track memory usage when enabled', (done) => {
      const trackingId = 'test-session-9';
      const sessionTracker = tracker.startTracking(trackingId, {
        fileName: mockFile.name,
        fileSize: mockFile.size
      });

      // Wait for memory monitoring to collect data
      setTimeout(() => {
        const session = tracker.sessions.get(trackingId);
        expect(session.memoryUsage.length).toBeGreaterThan(0);

        sessionTracker.cancel();
        done();
      }, 150); // Wait longer than update interval
    });

    it('should calculate memory peak correctly', () => {
      const trackingId = 'test-session-10';
      const sessionTracker = tracker.startTracking(trackingId, {
        fileName: mockFile.name,
        fileSize: mockFile.size
      });

      // Manually add memory usage data
      const session = tracker.sessions.get(trackingId);
      session.memoryUsage = [
        { timestamp: Date.now(), used: 40 * 1024 * 1024 },
        { timestamp: Date.now(), used: 60 * 1024 * 1024 },
        { timestamp: Date.now(), used: 45 * 1024 * 1024 }
      ];

      const memoryPeak = tracker.getMemoryPeak(trackingId);
      expect(memoryPeak).toBe(60 * 1024 * 1024);
    });
  });

  describe('performance metrics', () => {
    it('should calculate performance metrics on completion', () => {
      const trackingId = 'test-session-11';
      const sessionTracker = tracker.startTracking(trackingId, {
        fileName: mockFile.name,
        fileSize: mockFile.size,
        totalPages: 5
      });

      // Simulate some processing time
      const session = tracker.sessions.get(trackingId);
      session.startTime = Date.now() - 5000; // 5 seconds ago

      sessionTracker.complete({ success: true });

      expect(session.performanceMetrics).toBeDefined();
      expect(session.performanceMetrics.processingTime).toBeGreaterThan(4000);
      expect(session.performanceMetrics.pagesPerSecond).toBeGreaterThan(0);
    });
  });

  describe('global statistics', () => {
    it('should update global stats on session completion', () => {
      const initialStats = tracker.getGlobalStats();
      const initialTotal = initialStats.totalProcessed;

      const trackingId = 'test-session-12';
      const sessionTracker = tracker.startTracking(trackingId, {
        fileName: mockFile.name,
        fileSize: mockFile.size
      });

      sessionTracker.complete({ success: true });

      const updatedStats = tracker.getGlobalStats();
      expect(updatedStats.totalProcessed).toBe(initialTotal + 1);
    });

    it('should track error statistics', () => {
      const trackingId = 'test-session-13';
      const sessionTracker = tracker.startTracking(trackingId, {
        fileName: mockFile.name,
        fileSize: mockFile.size
      });

      sessionTracker.addError(new Error('Test error'));
      sessionTracker.complete({ success: false });

      const stats = tracker.getGlobalStats();
      expect(stats.totalErrors).toBeGreaterThan(0);
    });
  });

  describe('session queries', () => {
    it('should get active sessions', () => {
      const trackingId1 = 'active-session-1';
      const trackingId2 = 'active-session-2';
      const trackingId3 = 'completed-session';

      tracker.startTracking(trackingId1, { fileName: 'file1.pdf', fileSize: 1000 });
      tracker.startTracking(trackingId2, { fileName: 'file2.pdf', fileSize: 2000 });
      const sessionTracker3 = tracker.startTracking(trackingId3, { fileName: 'file3.pdf', fileSize: 3000 });

      // Complete one session
      sessionTracker3.complete({ success: true });

      const activeSessions = tracker.getActiveSessions();
      expect(activeSessions).toHaveLength(2);
      expect(activeSessions.map(s => s.trackingId)).toContain(trackingId1);
      expect(activeSessions.map(s => s.trackingId)).toContain(trackingId2);
      expect(activeSessions.map(s => s.trackingId)).not.toContain(trackingId3);
    });

    it('should get session status', () => {
      const trackingId = 'status-session';
      const sessionTracker = tracker.startTracking(trackingId, {
        fileName: mockFile.name,
        fileSize: mockFile.size,
        totalPages: 10
      });

      sessionTracker.updateStage('extracting', 50);

      const status = sessionTracker.getStatus();
      expect(status).toMatchObject({
        trackingId,
        fileName: mockFile.name,
        fileSize: mockFile.size,
        totalPages: 10,
        currentStage: 'extracting',
        status: 'active'
      });
      expect(status.currentProgress).toBeGreaterThan(0);
      expect(status.processingTime).toBeGreaterThan(0);
    });
  });

  describe('cleanup operations', () => {
    it('should clean up old sessions', () => {
      const oldTrackingId = 'old-session';
      const recentTrackingId = 'recent-session';

      const oldSessionTracker = tracker.startTracking(oldTrackingId, {
        fileName: 'old.pdf',
        fileSize: 1000
      });

      const recentSessionTracker = tracker.startTracking(recentTrackingId, {
        fileName: 'recent.pdf',
        fileSize: 2000
      });

      // Complete sessions with different end times
      const oldSession = tracker.sessions.get(oldTrackingId);
      oldSession.endTime = Date.now() - (25 * 60 * 60 * 1000); // 25 hours ago
      oldSession.status = 'completed';

      recentSessionTracker.complete({ success: true });

      // Clean up sessions older than 24 hours
      tracker.cleanupOldSessions(24 * 60 * 60 * 1000);

      expect(tracker.sessions.has(oldTrackingId)).toBe(false);
      expect(tracker.sessions.has(recentTrackingId)).toBe(true);
    });
  });

  describe('error handling', () => {
    it('should handle invalid session operations gracefully', () => {
      const invalidTracker = tracker.createSessionTracker('non-existent-session');

      expect(() => invalidTracker).toThrow('Session not found');
    });

    it('should handle operations on cancelled sessions', () => {
      const trackingId = 'cancelled-session';
      const sessionTracker = tracker.startTracking(trackingId, {
        fileName: mockFile.name,
        fileSize: mockFile.size
      });

      sessionTracker.cancel();

      // Operations on cancelled session should not throw
      expect(() => {
        sessionTracker.updateStage('extracting', 50);
        sessionTracker.updateProgress(75);
      }).not.toThrow();

      const session = tracker.sessions.get(trackingId);
      expect(session.status).toBe('cancelled');
    });
  });
});
