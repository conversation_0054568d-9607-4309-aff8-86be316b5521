/**
 * Unit Tests for EmbeddingGenerationService
 *
 * Tests for enhanced embedding generation with multiple models,
 * caching, error handling, and performance optimization.
 *
 * <AUTHOR> Agent
 * @version 1.0.0
 * @since 2025-01-28
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { EmbeddingGenerationService } from '../../../src/services/EmbeddingGenerationService.js';

// Mock dependencies
vi.mock('../../../src/utils/ProcessingLogger.js', () => ({
  ProcessingLogger: vi.fn().mockImplementation(() => ({
    log: vi.fn(),
    warn: vi.fn(),
    error: vi.fn()
  }))
}));

vi.mock('../../../src/utils/EmbeddingCache.js', () => ({
  EmbeddingCache: vi.fn().mockImplementation(() => ({
    get: vi.fn(),
    set: vi.fn(),
    clear: vi.fn()
  }))
}));

// Mock global fetch
global.fetch = vi.fn();

describe('EmbeddingGenerationService', () => {
  let service;
  let mockCache;

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup service with test configuration
    service = new EmbeddingGenerationService({
      maxRetries: 2,
      retryDelay: 100,
      timeout: 5000,
      embeddingDimension: 1536
    });

    mockCache = service.cache;
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Constructor', () => {
    it('should initialize with default configuration', () => {
      const defaultService = new EmbeddingGenerationService();

      expect(defaultService.config.defaultModel).toBe('deepseek-embedding');
      expect(defaultService.config.maxRetries).toBe(3);
      expect(defaultService.config.embeddingDimension).toBe(1536);
    });

    it('should accept custom configuration', () => {
      const customService = new EmbeddingGenerationService({
        defaultModel: 'openai-ada-002',
        maxRetries: 5,
        embeddingDimension: 768
      });

      expect(customService.config.defaultModel).toBe('openai-ada-002');
      expect(customService.config.maxRetries).toBe(5);
      expect(customService.config.embeddingDimension).toBe(768);
    });
  });

  describe('generateEmbedding', () => {
    const testText = 'This is a test document for embedding generation.';
    const testOptions = { documentId: 'test-doc-1', model: 'deepseek-embedding' };

    it('should generate embedding successfully with API', async () => {
      // Mock successful API response
      const mockEmbedding = new Array(1536).fill(0).map(() => Math.random());
      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          data: [{ embedding: mockEmbedding }],
          usage: { total_tokens: 10 }
        })
      });

      // Mock environment with API key
      global.window = { __MVAT_ENV__: { DEEPSEEK_API_KEY: 'test-key' } };

      const result = await service.generateEmbedding(testText, testOptions);

      expect(result).toHaveProperty('vector');
      expect(result).toHaveProperty('model', 'deepseek-embedding');
      expect(result).toHaveProperty('method', 'api');
      expect(result).toHaveProperty('confidence');
      expect(result.vector).toHaveLength(1536);
      expect(result.enhanced).toBe(true);
    });

    it('should use cached embedding when available', async () => {
      const cachedEmbedding = {
        vector: new Array(1536).fill(0.5),
        model: 'deepseek-embedding',
        method: 'api',
        confidence: 0.95
      };

      mockCache.get.mockResolvedValueOnce(cachedEmbedding);

      const result = await service.generateEmbedding(testText, testOptions);

      expect(mockCache.get).toHaveBeenCalledWith(testText, testOptions);
      expect(result.cacheHit).toBe(true);
      expect(result.vector).toEqual(cachedEmbedding.vector);
    });

    it('should fall back to local embedding when API fails', async () => {
      global.fetch.mockRejectedValueOnce(new Error('API Error'));
      mockCache.get.mockResolvedValueOnce(null);

      const result = await service.generateEmbedding(testText, testOptions);

      expect(result).toHaveProperty('vector');
      expect(result).toHaveProperty('method', 'fallback');
      expect(result.confidence).toBeLessThan(0.5);
    });

    it('should generate local embedding when specified', async () => {
      const localOptions = { ...testOptions, model: 'local-tfidf' };

      const result = await service.generateEmbedding(testText, localOptions);

      expect(result).toHaveProperty('vector');
      expect(result).toHaveProperty('model', 'local-tfidf');
      expect(result).toHaveProperty('method', 'local');
      expect(result.confidence).toBe(0.75);
    });

    it('should validate input text', async () => {
      await expect(service.generateEmbedding('')).rejects.toThrow('Text must be a non-empty string');
      await expect(service.generateEmbedding('short')).rejects.toThrow('Text too short');
      await expect(service.generateEmbedding(null)).rejects.toThrow('Text must be a non-empty string');
    });

    it('should handle unsupported models', async () => {
      const invalidOptions = { ...testOptions, model: 'unsupported-model' };

      const result = await service.generateEmbedding(testText, invalidOptions);

      expect(result.method).toBe('fallback');
      expect(result.confidence).toBeLessThan(0.5);
    });

    it('should retry on API failures', async () => {
      global.fetch
        .mockRejectedValueOnce(new Error('Network Error'))
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            data: [{ embedding: new Array(1536).fill(0.5) }]
          })
        });

      global.window = { __MVAT_ENV__: { DEEPSEEK_API_KEY: 'test-key' } };
      mockCache.get.mockResolvedValueOnce(null);

      const result = await service.generateEmbedding(testText, testOptions);

      expect(global.fetch).toHaveBeenCalledTimes(2);
      expect(result.method).toBe('api');
    });

    it('should handle API timeout', async () => {
      // Mock a delayed response that exceeds timeout
      global.fetch.mockImplementationOnce(() =>
        new Promise(resolve => setTimeout(resolve, 10000))
      );

      global.window = { __MVAT_ENV__: { DEEPSEEK_API_KEY: 'test-key' } };
      mockCache.get.mockResolvedValueOnce(null);

      const result = await service.generateEmbedding(testText, testOptions);

      expect(result.method).toBe('fallback');
    });

    it('should cache generated embeddings', async () => {
      const mockEmbedding = new Array(1536).fill(0.5);
      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          data: [{ embedding: mockEmbedding }]
        })
      });

      global.window = { __MVAT_ENV__: { DEEPSEEK_API_KEY: 'test-key' } };
      mockCache.get.mockResolvedValueOnce(null);

      await service.generateEmbedding(testText, testOptions);

      expect(mockCache.set).toHaveBeenCalledWith(
        testText,
        testOptions,
        expect.objectContaining({
          vector: expect.any(Array),
          model: 'deepseek-embedding',
          method: 'api'
        })
      );
    });

    it('should skip caching when disabled', async () => {
      const mockEmbedding = new Array(1536).fill(0.5);
      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          data: [{ embedding: mockEmbedding }]
        })
      });

      global.window = { __MVAT_ENV__: { DEEPSEEK_API_KEY: 'test-key' } };
      const noCacheOptions = { ...testOptions, useCache: false };

      await service.generateEmbedding(testText, noCacheOptions);

      expect(mockCache.get).not.toHaveBeenCalled();
      expect(mockCache.set).not.toHaveBeenCalled();
    });
  });

  describe('Text Processing', () => {
    it('should preprocess long text correctly', () => {
      const longText = 'word '.repeat(10000);
      const processed = service.preprocessText(longText, 1000);

      expect(processed.length).toBeLessThan(longText.length);
      expect(processed.endsWith('...')).toBe(true);
    });

    it('should extract keywords from text', () => {
      const text = 'This is a test document with important keywords and some common words.';
      const keywords = service.extractKeywords(text);

      expect(keywords).toContain('test');
      expect(keywords).toContain('document');
      expect(keywords).toContain('important');
      expect(keywords).toContain('keywords');
      expect(keywords).not.toContain('the');
      expect(keywords).not.toContain('is');
      expect(keywords).not.toContain('a');
    });

    it('should create TF-IDF vector from keywords', () => {
      const keywords = ['test', 'document', 'important'];
      const text = 'This is a test document with important test keywords.';
      const vector = service.createTFIDFVector(keywords, text);

      expect(vector).toHaveLength(service.config.embeddingDimension);
      expect(vector.some(val => val > 0)).toBe(true);
    });

    it('should create hash-based fallback vector', () => {
      const text = 'Test document for hash-based vector generation.';
      const vector = service.createHashBasedVector(text);

      expect(vector).toHaveLength(service.config.embeddingDimension);
      expect(vector.some(val => val !== 0)).toBe(true);
    });
  });

  describe('API Integration', () => {
    beforeEach(() => {
      global.window = { __MVAT_ENV__: { DEEPSEEK_API_KEY: 'test-key' } };
    });

    it('should make API request with correct headers', async () => {
      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          data: [{ embedding: new Array(1536).fill(0.5) }]
        })
      });

      const requestBody = {
        model: 'deepseek-embedding',
        input: 'test text',
        encoding_format: 'float'
      };

      await service.makeAPIRequest('https://api.test.com', 'test-key', requestBody, 'req-1');

      expect(global.fetch).toHaveBeenCalledWith(
        'https://api.test.com',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test-key',
            'User-Agent': 'MVAT-Chrome-Extension/1.0'
          }),
          body: JSON.stringify(requestBody)
        })
      );
    });

    it('should handle API error responses', async () => {
      global.fetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        text: async () => 'Invalid request'
      });

      await expect(
        service.makeAPIRequest('https://api.test.com', 'test-key', {}, 'req-1')
      ).rejects.toThrow('API error: 400 Bad Request - Invalid request');
    });

    it('should get API key from environment', () => {
      expect(service.getAPIKey('deepseek-embedding')).toBe('test-key');

      global.window.__MVAT_ENV__.OPENAI_API_KEY = 'openai-key';
      expect(service.getAPIKey('openai-ada-002')).toBe('openai-key');
    });

    it('should calculate API confidence correctly', () => {
      const vector = new Array(1536).fill(0.5);

      const deepseekConfidence = service.calculateAPIConfidence('deepseek-embedding', vector);
      expect(deepseekConfidence).toBeGreaterThan(0.9);

      const openaiConfidence = service.calculateAPIConfidence('openai-ada-002', vector);
      expect(openaiConfidence).toBeGreaterThan(0.9);
    });
  });

  describe('Performance and Metrics', () => {
    it('should track performance metrics', async () => {
      const initialMetrics = service.getMetrics();
      expect(initialMetrics.totalRequests).toBe(0);

      mockCache.get.mockResolvedValueOnce(null);
      await service.generateEmbedding('test text', { model: 'local-tfidf' });

      const updatedMetrics = service.getMetrics();
      expect(updatedMetrics.totalRequests).toBe(1);
      expect(updatedMetrics.successfulRequests).toBe(1);
      expect(updatedMetrics.averageProcessingTime).toBeGreaterThan(0);
    });

    it('should track cache hit rate', async () => {
      const cachedEmbedding = {
        vector: new Array(1536).fill(0.5),
        model: 'test',
        method: 'api'
      };

      mockCache.get.mockResolvedValueOnce(cachedEmbedding);
      await service.generateEmbedding('test text');

      const metrics = service.getMetrics();
      expect(metrics.cacheHitRate).toBe(1);
    });

    it('should return supported models', () => {
      const models = service.getSupportedModels();
      expect(models).toContain('deepseek-embedding');
      expect(models).toContain('openai-ada-002');
      expect(models).toContain('local-tfidf');
    });

    it('should allow configuration updates', () => {
      const newConfig = { maxRetries: 5, timeout: 10000 };
      service.updateConfiguration(newConfig);

      const config = service.getConfiguration();
      expect(config.maxRetries).toBe(5);
      expect(config.timeout).toBe(10000);
    });

    it('should clear cache', async () => {
      await service.clearCache();
      expect(mockCache.clear).toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors gracefully', async () => {
      global.fetch.mockRejectedValue(new Error('Network Error'));
      global.window = { __MVAT_ENV__: { DEEPSEEK_API_KEY: 'test-key' } };
      mockCache.get.mockResolvedValueOnce(null);

      const result = await service.generateEmbedding('test text');

      expect(result.method).toBe('fallback');
      expect(result.confidence).toBeLessThan(0.5);
    });

    it('should handle invalid API responses', async () => {
      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ invalid: 'response' })
      });

      global.window = { __MVAT_ENV__: { DEEPSEEK_API_KEY: 'test-key' } };
      mockCache.get.mockResolvedValueOnce(null);

      const result = await service.generateEmbedding('test text');

      expect(result.method).toBe('fallback');
    });

    it('should handle cache errors gracefully', async () => {
      mockCache.get.mockRejectedValueOnce(new Error('Cache Error'));
      mockCache.set.mockRejectedValueOnce(new Error('Cache Error'));

      const result = await service.generateEmbedding('test text', { model: 'local-tfidf' });

      expect(result).toHaveProperty('vector');
      expect(result.method).toBe('local');
    });
  });
});
