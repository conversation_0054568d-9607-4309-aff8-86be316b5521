import { describe, it, expect, beforeEach, vi } from 'vitest';
import { SettingsService } from '../../../src/services/SettingsService.js';

// Mock dependencies
vi.mock('../../../src/services/EncryptionService.js', () => ({
  encryptionService: {
    encryptApiKeys: vi.fn(),
    decryptApiKeys: vi.fn(),
    clearEncryptionKey: vi.fn()
  }
}));

vi.mock('../../../src/services/ApiValidationService.js', () => ({
  apiValidationService: {
    validateKeyFormat: vi.fn(),
    testAllConnections: vi.fn()
  }
}));

// Mock Chrome storage API
const mockChromeStorage = {
  local: {
    get: vi.fn(),
    set: vi.fn(),
    remove: vi.fn()
  }
};

global.chrome = {
  storage: mockChromeStorage
};

describe('SettingsService', () => {
  let settingsService;
  let encryptionService;
  let apiValidationService;

  beforeEach(async () => {
    settingsService = new SettingsService();

    // Get mocked services
    const { encryptionService: mockEncryption } = await import('../../../src/services/EncryptionService.js');
    const { apiValidationService: mockValidation } = await import('../../../src/services/ApiValidationService.js');

    encryptionService = mockEncryption;
    apiValidationService = mockValidation;

    vi.clearAllMocks();
  });

  describe('Settings Loading', () => {
    it('should load default settings when no stored settings exist', async () => {
      mockChromeStorage.local.get.mockResolvedValue({});

      const settings = await settingsService.loadSettings();

      expect(settings).toEqual(settingsService.defaultSettings);
    });

    it('should load and merge stored settings with defaults', async () => {
      const storedSettings = {
        company: { name: 'Test Company' },
        display: { currency: 'EUR' }
      };

      mockChromeStorage.local.get.mockResolvedValue({
        settings: storedSettings
      });

      const settings = await settingsService.loadSettings();

      expect(settings.company.name).toBe('Test Company');
      expect(settings.display.currency).toBe('EUR');
      expect(settings.display.language).toBe('pl'); // Default value
      expect(settings.processing).toEqual(settingsService.defaultSettings.processing);
    });

    it('should decrypt API keys when loading', async () => {
      const encryptedKeys = {
        deepseek: 'encrypted-deepseek-key',
        openai: 'encrypted-openai-key'
      };

      const decryptedKeys = {
        deepseek: 'sk-deepseek-real-key',
        openai: 'sk-openai-real-key',
        fakturownia: '',
        infakt: ''
      };

      mockChromeStorage.local.get.mockResolvedValue({
        settings: {},
        encryptedApiKeys: encryptedKeys
      });

      encryptionService.decryptApiKeys.mockResolvedValue(decryptedKeys);

      const settings = await settingsService.loadSettings();

      expect(encryptionService.decryptApiKeys).toHaveBeenCalledWith(encryptedKeys);
      expect(settings.apiKeys).toEqual(decryptedKeys);
    });

    it('should handle decryption failures gracefully', async () => {
      mockChromeStorage.local.get.mockResolvedValue({
        settings: {},
        encryptedApiKeys: { deepseek: 'corrupted-data' }
      });

      encryptionService.decryptApiKeys.mockRejectedValue(new Error('Decryption failed'));

      const settings = await settingsService.loadSettings();

      expect(settings.apiKeys).toEqual(settingsService.defaultSettings.apiKeys);
    });

    it('should fallback to localStorage in non-Chrome environment', async () => {
      // Temporarily remove chrome global
      const originalChrome = global.chrome;
      global.chrome = undefined;

      const testSettings = { company: { name: 'Local Test' } };
      global.localStorage = {
        getItem: vi.fn().mockReturnValue(JSON.stringify(testSettings))
      };

      const settings = await settingsService.loadSettings();

      expect(settings.company.name).toBe('Local Test');

      // Restore chrome global
      global.chrome = originalChrome;
    });
  });

  describe('Settings Saving', () => {
    it('should save regular settings and encrypt API keys separately', async () => {
      const settings = {
        company: { name: 'Test Company' },
        display: { currency: 'EUR' },
        processing: { aiProvider: 'openai' },
        apiKeys: {
          deepseek: 'sk-deepseek-key',
          openai: 'sk-openai-key',
          fakturownia: '',
          infakt: ''
        }
      };

      const encryptedKeys = {
        deepseek: 'encrypted-deepseek',
        openai: 'encrypted-openai',
        fakturownia: '',
        infakt: ''
      };

      encryptionService.encryptApiKeys.mockResolvedValue(encryptedKeys);
      mockChromeStorage.local.set.mockResolvedValue();

      await settingsService.saveSettings(settings);

      expect(encryptionService.encryptApiKeys).toHaveBeenCalledWith(settings.apiKeys);
      expect(mockChromeStorage.local.set).toHaveBeenCalledWith({
        settings: {
          company: settings.company,
          display: settings.display,
          processing: settings.processing
        }
      });
      expect(mockChromeStorage.local.set).toHaveBeenCalledWith({
        encryptedApiKeys: encryptedKeys
      });
    });

    it('should handle encryption failures', async () => {
      const settings = {
        company: { name: 'Test' },
        apiKeys: { deepseek: 'sk-test-key' }
      };

      encryptionService.encryptApiKeys.mockRejectedValue(new Error('Encryption failed'));

      await expect(settingsService.saveSettings(settings)).rejects.toThrow('Encryption failed');
    });
  });

  describe('Section Updates', () => {
    it('should update specific setting section', async () => {
      const currentSettings = {
        company: { name: 'Old Name', email: '<EMAIL>' },
        display: { currency: 'PLN' },
        processing: { aiProvider: 'deepseek' },
        apiKeys: {}
      };

      mockChromeStorage.local.get.mockResolvedValue({
        settings: currentSettings
      });
      encryptionService.decryptApiKeys.mockResolvedValue({});
      encryptionService.encryptApiKeys.mockResolvedValue({});
      mockChromeStorage.local.set.mockResolvedValue();

      const updates = { name: 'New Name', taxId: '*********' };
      const result = await settingsService.updateSection('company', updates);

      expect(result.company.name).toBe('New Name');
      expect(result.company.taxId).toBe('*********');
      expect(result.company.email).toBe('<EMAIL>'); // Preserved
    });
  });

  describe('API Key Management', () => {
    beforeEach(() => {
      mockChromeStorage.local.get.mockResolvedValue({ settings: {} });
      encryptionService.decryptApiKeys.mockResolvedValue({});
      encryptionService.encryptApiKeys.mockResolvedValue({});
      mockChromeStorage.local.set.mockResolvedValue();
    });

    it('should update API keys with validation', async () => {
      const apiKeys = {
        deepseek: 'sk-' + 'a'.repeat(32),
        openai: 'sk-' + 'b'.repeat(32)
      };

      apiValidationService.validateKeyFormat
        .mockReturnValueOnce({ valid: true })
        .mockReturnValueOnce({ valid: true });

      const result = await settingsService.updateApiKeys(apiKeys, false);

      expect(result.success).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(apiValidationService.validateKeyFormat).toHaveBeenCalledTimes(2);
    });

    it('should reject invalid API key formats', async () => {
      const apiKeys = {
        deepseek: 'invalid-key',
        openai: 'sk-' + 'b'.repeat(32)
      };

      apiValidationService.validateKeyFormat
        .mockReturnValueOnce({ valid: false, error: 'Invalid format' })
        .mockReturnValueOnce({ valid: true });

      const result = await settingsService.updateApiKeys(apiKeys, false);

      expect(result.success).toBe(false);
      expect(result.errors).toContain('deepseek: Invalid format');
    });

    it('should test connections when requested', async () => {
      const apiKeys = {
        deepseek: 'sk-' + 'a'.repeat(32)
      };

      const connectionResults = {
        results: { deepseek: { success: true } },
        summary: { successful: 1, failed: 0 }
      };

      apiValidationService.validateKeyFormat.mockReturnValue({ valid: true });
      apiValidationService.testAllConnections.mockResolvedValue(connectionResults);

      const result = await settingsService.updateApiKeys(apiKeys, true);

      expect(result.success).toBe(true);
      expect(result.validationResults).toEqual(connectionResults);
      expect(apiValidationService.testAllConnections).toHaveBeenCalledWith(apiKeys);
    });

    it('should clear all API keys', async () => {
      mockChromeStorage.local.remove.mockResolvedValue();

      await settingsService.clearApiKeys();

      expect(mockChromeStorage.local.remove).toHaveBeenCalledWith(['encryptedApiKeys']);
    });

    it('should get specific API key', async () => {
      const settings = {
        apiKeys: {
          deepseek: 'sk-test-key',
          openai: ''
        }
      };

      mockChromeStorage.local.get.mockResolvedValue({ settings });
      encryptionService.decryptApiKeys.mockResolvedValue(settings.apiKeys);

      const deepseekKey = await settingsService.getApiKey('deepseek');
      const openaiKey = await settingsService.getApiKey('openai');

      expect(deepseekKey).toBe('sk-test-key');
      expect(openaiKey).toBe('');
    });
  });

  describe('Settings Import/Export', () => {
    it('should export settings without API keys', async () => {
      const settings = {
        company: { name: 'Test Company' },
        display: { currency: 'EUR' },
        processing: { aiProvider: 'openai' },
        apiKeys: { deepseek: 'secret-key' }
      };

      mockChromeStorage.local.get.mockResolvedValue({ settings });
      encryptionService.decryptApiKeys.mockResolvedValue(settings.apiKeys);

      const exported = await settingsService.exportSettings();

      expect(exported.company).toEqual(settings.company);
      expect(exported.display).toEqual(settings.display);
      expect(exported.processing).toEqual(settings.processing);
      expect(exported.apiKeys).toBeUndefined();
      expect(exported.exportedAt).toBeTruthy();
      expect(exported.version).toBe('1.0');
    });

    it('should import settings while preserving API keys', async () => {
      const currentSettings = {
        company: { name: 'Old Company' },
        apiKeys: { deepseek: 'existing-key' }
      };

      const importedSettings = {
        company: { name: 'New Company' },
        display: { currency: 'USD' }
      };

      mockChromeStorage.local.get.mockResolvedValue({ settings: currentSettings });
      encryptionService.decryptApiKeys.mockResolvedValue(currentSettings.apiKeys);
      encryptionService.encryptApiKeys.mockResolvedValue({});
      mockChromeStorage.local.set.mockResolvedValue();

      const result = await settingsService.importSettings(importedSettings);

      expect(result.success).toBe(true);
      expect(result.message).toContain('API keys preserved');
    });
  });

  describe('Settings Validation', () => {
    it('should validate correct settings structure', () => {
      const validSettings = {
        company: { name: 'Test', email: '<EMAIL>' },
        display: { currency: 'PLN' },
        processing: { aiProvider: 'deepseek' },
        apiKeys: {}
      };

      const result = settingsService.validateSettings(validSettings);

      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect missing sections', () => {
      const invalidSettings = {
        company: { name: 'Test' }
        // Missing display, processing, apiKeys
      };

      const result = settingsService.validateSettings(invalidSettings);

      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Missing or invalid display section');
      expect(result.errors).toContain('Missing or invalid processing section');
      expect(result.errors).toContain('Missing or invalid apiKeys section');
    });

    it('should validate email format', () => {
      const settingsWithInvalidEmail = {
        company: { email: 'invalid-email' },
        display: {},
        processing: {},
        apiKeys: {}
      };

      const result = settingsService.validateSettings(settingsWithInvalidEmail);

      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Invalid email format');
    });

    it('should validate currency', () => {
      const settingsWithInvalidCurrency = {
        company: {},
        display: { currency: 'INVALID' },
        processing: {},
        apiKeys: {}
      };

      const result = settingsService.validateSettings(settingsWithInvalidCurrency);

      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Invalid currency');
    });
  });

  describe('Error Handling', () => {
    it('should handle storage errors gracefully', async () => {
      mockChromeStorage.local.get.mockRejectedValue(new Error('Storage error'));

      const settings = await settingsService.loadSettings();

      expect(settings).toEqual(settingsService.defaultSettings);
    });

    it('should handle save errors', async () => {
      mockChromeStorage.local.set.mockRejectedValue(new Error('Save error'));

      await expect(settingsService.saveSettings({})).rejects.toThrow('Save error');
    });
  });

  describe('Reset Functionality', () => {
    it('should reset all settings to defaults', async () => {
      encryptionService.clearEncryptionKey.mockResolvedValue();
      encryptionService.encryptApiKeys.mockResolvedValue({});
      mockChromeStorage.local.set.mockResolvedValue();

      await settingsService.resetToDefaults();

      expect(encryptionService.clearEncryptionKey).toHaveBeenCalled();
    });
  });
});
