/**
 * Unit Tests for VectorSearchService
 *
 * Tests for efficient vector similarity search with multiple algorithms,
 * indexing, and performance optimization.
 *
 * <AUTHOR> Agent
 * @version 1.0.0
 * @since 2025-01-28
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { VectorSearchService } from '../../../src/services/VectorSearchService.js';

// Mock dependencies
vi.mock('../../../src/utils/ProcessingLogger.js', () => ({
  ProcessingLogger: vi.fn().mockImplementation(() => ({
    log: vi.fn(),
    warn: vi.fn(),
    error: vi.fn()
  }))
}));

describe('VectorSearchService', () => {
  let service;
  let testVectors;

  beforeEach(() => {
    vi.clearAllMocks();

    service = new VectorSearchService({
      defaultAlgorithm: 'cosine',
      defaultK: 5,
      similarityThreshold: 0.7
    });

    // Create test vectors
    testVectors = [
      {
        id: 'doc1',
        vector: [1, 0, 0, 0, 0],
        metadata: { type: 'invoice', vendor: 'Company A' }
      },
      {
        id: 'doc2',
        vector: [0.9, 0.1, 0, 0, 0],
        metadata: { type: 'invoice', vendor: 'Company A' }
      },
      {
        id: 'doc3',
        vector: [0, 1, 0, 0, 0],
        metadata: { type: 'receipt', vendor: 'Company B' }
      },
      {
        id: 'doc4',
        vector: [0, 0, 1, 0, 0],
        metadata: { type: 'contract', vendor: 'Company C' }
      },
      {
        id: 'doc5',
        vector: [0, 0, 0, 1, 0],
        metadata: { type: 'invoice', vendor: 'Company D' }
      }
    ];
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Constructor', () => {
    it('should initialize with default configuration', () => {
      const defaultService = new VectorSearchService();

      expect(defaultService.config.defaultAlgorithm).toBe('cosine');
      expect(defaultService.config.defaultK).toBe(10);
      expect(defaultService.config.similarityThreshold).toBe(0.7);
    });

    it('should accept custom configuration', () => {
      const customService = new VectorSearchService({
        defaultAlgorithm: 'euclidean',
        defaultK: 15,
        similarityThreshold: 0.8
      });

      expect(customService.config.defaultAlgorithm).toBe('euclidean');
      expect(customService.config.defaultK).toBe(15);
      expect(customService.config.similarityThreshold).toBe(0.8);
    });
  });

  describe('addVectors', () => {
    it('should add vectors to index successfully', async () => {
      const result = await service.addVectors(testVectors);

      expect(result.success).toBe(true);
      expect(result.indexed).toBe(5);
      expect(result.total).toBe(5);
      expect(service.vectorIndex.size).toBe(5);
    });

    it('should validate vector data before adding', async () => {
      const invalidVectors = [
        { id: 'valid', vector: [1, 0, 0], metadata: {} },
        { id: 'invalid1', vector: null, metadata: {} },
        { vector: [1, 0, 0], metadata: {} }, // missing id
        { id: 'invalid2', vector: ['not', 'numbers'], metadata: {} }
      ];

      const result = await service.addVectors(invalidVectors);

      expect(result.indexed).toBe(1); // Only the valid one
      expect(service.vectorIndex.size).toBe(1);
    });

    it('should normalize vectors when adding', async () => {
      const unnormalizedVectors = [
        { id: 'test', vector: [3, 4, 0], metadata: {} }
      ];

      await service.addVectors(unnormalizedVectors);

      const stored = service.vectorIndex.get('test');
      const magnitude = Math.sqrt(stored.vector.reduce((sum, val) => sum + val * val, 0));
      expect(magnitude).toBeCloseTo(1, 5); // Should be normalized
    });

    it('should update index statistics', async () => {
      await service.addVectors(testVectors);

      expect(service.indexStats.totalVectors).toBe(5);
      expect(service.indexStats.indexedVectors).toBe(5);
      expect(service.indexStats.lastIndexUpdate).toBeTruthy();
    });
  });

  describe('search', () => {
    beforeEach(async () => {
      await service.addVectors(testVectors);
    });

    it('should perform cosine similarity search', async () => {
      const queryVector = [1, 0, 0, 0, 0];
      const results = await service.search(queryVector, { algorithm: 'cosine', k: 3 });

      expect(results).toHaveLength(2); // Only doc1 and doc2 should be above threshold
      expect(results[0].id).toBe('doc1');
      expect(results[0].similarity).toBeCloseTo(1, 5);
      expect(results[1].id).toBe('doc2');
      expect(results[1].similarity).toBeGreaterThan(0.7);
    });

    it('should perform euclidean distance search', async () => {
      const queryVector = [1, 0, 0, 0, 0];
      const results = await service.search(queryVector, { algorithm: 'euclidean', k: 3 });

      expect(results).toHaveLength(2);
      expect(results[0].id).toBe('doc1');
      expect(results[0].similarity).toBeGreaterThan(0.9);
    });

    it('should perform dot product search', async () => {
      const queryVector = [1, 0, 0, 0, 0];
      const results = await service.search(queryVector, { algorithm: 'dot', k: 3 });

      expect(results).toHaveLength(2);
      expect(results[0].id).toBe('doc1');
    });

    it('should perform hybrid search', async () => {
      const queryVector = [1, 0, 0, 0, 0];
      const results = await service.search(queryVector, { algorithm: 'hybrid', k: 3 });

      expect(results).toHaveLength(2);
      expect(results[0].algorithm).toBe('hybrid');
      expect(results[0].algorithms).toContain('cosine');
      expect(results[0].algorithms).toContain('euclidean');
    });

    it('should accept embedding object as query', async () => {
      const queryEmbedding = {
        vector: [1, 0, 0, 0, 0],
        metadata: { type: 'query' }
      };

      const results = await service.search(queryEmbedding);

      expect(results).toHaveLength(2);
      expect(results[0].id).toBe('doc1');
    });

    it('should exclude specified IDs', async () => {
      const queryVector = [1, 0, 0, 0, 0];
      const results = await service.search(queryVector, {
        excludeIds: ['doc1'],
        threshold: 0.5
      });

      expect(results).toHaveLength(1);
      expect(results[0].id).toBe('doc2');
    });

    it('should apply filter function', async () => {
      const queryVector = [1, 0, 0, 0, 0];
      const filterFn = (result) => result.metadata.vendor === 'Company A';

      const results = await service.search(queryVector, {
        filterFn,
        threshold: 0.5
      });

      expect(results).toHaveLength(2);
      expect(results.every(r => r.metadata.vendor === 'Company A')).toBe(true);
    });

    it('should respect similarity threshold', async () => {
      const queryVector = [1, 0, 0, 0, 0];
      const results = await service.search(queryVector, { threshold: 0.95 });

      expect(results).toHaveLength(1);
      expect(results[0].id).toBe('doc1');
    });

    it('should limit results by k parameter', async () => {
      const queryVector = [1, 0, 0, 0, 0];
      const results = await service.search(queryVector, {
        k: 1,
        threshold: 0.5
      });

      expect(results).toHaveLength(1);
      expect(results[0].id).toBe('doc1');
    });

    it('should include metadata when requested', async () => {
      const queryVector = [1, 0, 0, 0, 0];
      const results = await service.search(queryVector, { includeMetadata: true });

      expect(results[0]).toHaveProperty('metadata');
      expect(results[0].metadata).toHaveProperty('type');
      expect(results[0].metadata).toHaveProperty('vendor');
    });

    it('should exclude metadata when not requested', async () => {
      const queryVector = [1, 0, 0, 0, 0];
      const results = await service.search(queryVector, { includeMetadata: false });

      expect(results[0]).not.toHaveProperty('metadata');
    });

    it('should handle invalid query vectors', async () => {
      const invalidQuery = [1, 'invalid', null];
      const results = await service.search(invalidQuery);

      expect(results).toHaveLength(0);
    });

    it('should handle unsupported algorithms', async () => {
      const queryVector = [1, 0, 0, 0, 0];
      const results = await service.search(queryVector, { algorithm: 'unsupported' });

      expect(results).toHaveLength(0);
    });

    it('should cache search results', async () => {
      const queryVector = [1, 0, 0, 0, 0];

      // First search
      const results1 = await service.search(queryVector);

      // Second search with same parameters
      const results2 = await service.search(queryVector);

      expect(service.metrics.cacheHits).toBe(1);
      expect(results1).toEqual(results2);
    });
  });

  describe('batchSearch', () => {
    beforeEach(async () => {
      await service.addVectors(testVectors);
    });

    it('should perform batch search for multiple queries', async () => {
      const queries = [
        [1, 0, 0, 0, 0],
        [0, 1, 0, 0, 0],
        [0, 0, 1, 0, 0]
      ];

      const results = await service.batchSearch(queries, { threshold: 0.5 });

      expect(results).toHaveLength(3);
      expect(results[0]).toHaveLength(2); // First query should find doc1 and doc2
      expect(results[1]).toHaveLength(1); // Second query should find doc3
      expect(results[2]).toHaveLength(1); // Third query should find doc4
    });

    it('should handle empty query array', async () => {
      const results = await service.batchSearch([]);

      expect(results).toHaveLength(0);
    });

    it('should process queries in batches', async () => {
      const queries = new Array(10).fill([1, 0, 0, 0, 0]);

      const results = await service.batchSearch(queries, {
        batchSize: 3,
        threshold: 0.5
      });

      expect(results).toHaveLength(10);
      expect(results.every(r => r.length === 2)).toBe(true);
    });
  });

  describe('Index Management', () => {
    beforeEach(async () => {
      await service.addVectors(testVectors);
    });

    it('should remove vector from index', () => {
      const removed = service.removeVector('doc1');

      expect(removed).toBe(true);
      expect(service.vectorIndex.has('doc1')).toBe(false);
      expect(service.vectorIndex.size).toBe(4);
    });

    it('should return false when removing non-existent vector', () => {
      const removed = service.removeVector('non-existent');

      expect(removed).toBe(false);
      expect(service.vectorIndex.size).toBe(5);
    });

    it('should clear entire index', () => {
      service.clearIndex();

      expect(service.vectorIndex.size).toBe(0);
      expect(service.documentMetadata.size).toBe(0);
      expect(service.resultCache.size).toBe(0);
      expect(service.indexStats.totalVectors).toBe(0);
    });
  });

  describe('Performance and Metrics', () => {
    beforeEach(async () => {
      await service.addVectors(testVectors);
    });

    it('should track search metrics', async () => {
      const queryVector = [1, 0, 0, 0, 0];

      await service.search(queryVector);
      await service.search(queryVector); // This should be a cache hit

      const stats = service.getStats();
      expect(stats.totalSearches).toBe(2);
      expect(stats.cacheHits).toBe(1);
      expect(stats.cacheHitRate).toBe(0.5);
      expect(stats.averageSearchTime).toBeGreaterThan(0);
    });

    it('should return index statistics', () => {
      const stats = service.getStats();

      expect(stats.index.totalVectors).toBe(5);
      expect(stats.index.indexedVectors).toBe(5);
      expect(stats.index.lastIndexUpdate).toBeTruthy();
    });

    it('should allow configuration updates', () => {
      const newConfig = {
        defaultK: 20,
        similarityThreshold: 0.8
      };

      service.updateConfig(newConfig);

      const config = service.getConfig();
      expect(config.defaultK).toBe(20);
      expect(config.similarityThreshold).toBe(0.8);
    });

    it('should return supported algorithms', () => {
      const algorithms = service.getSupportedAlgorithms();

      expect(algorithms).toContain('cosine');
      expect(algorithms).toContain('euclidean');
      expect(algorithms).toContain('dot');
      expect(algorithms).toContain('hybrid');
    });
  });

  describe('Error Handling', () => {
    it('should handle errors during vector addition', async () => {
      const invalidVectors = [
        { id: 'test', vector: null, metadata: {} }
      ];

      const result = await service.addVectors(invalidVectors);

      expect(result.indexed).toBe(0);
      expect(result.total).toBe(0);
    });

    it('should handle search errors gracefully', async () => {
      // Search with empty index
      const queryVector = [1, 0, 0, 0, 0];
      const results = await service.search(queryVector);

      expect(results).toHaveLength(0);
    });

    it('should handle batch search errors', async () => {
      const invalidQueries = [null, undefined, 'invalid'];
      const results = await service.batchSearch(invalidQueries);

      expect(results).toHaveLength(3);
      expect(results.every(r => r.length === 0)).toBe(true);
    });
  });

  describe('Cache Management', () => {
    beforeEach(async () => {
      await service.addVectors(testVectors);
    });

    it('should manage cache size', async () => {
      service.maxCacheSize = 2;

      // Perform multiple searches to fill cache
      await service.search([1, 0, 0, 0, 0]);
      await service.search([0, 1, 0, 0, 0]);
      await service.search([0, 0, 1, 0, 0]); // This should evict the first entry

      expect(service.resultCache.size).toBeLessThanOrEqual(2);
    });

    it('should generate consistent cache keys', () => {
      const vector = [1, 0, 0, 0, 0];
      const options = { algorithm: 'cosine', k: 5 };

      const key1 = service.generateCacheKey(vector, options);
      const key2 = service.generateCacheKey(vector, options);

      expect(key1).toBe(key2);
    });

    it('should generate different cache keys for different inputs', () => {
      const vector1 = [1, 0, 0, 0, 0];
      const vector2 = [0, 1, 0, 0, 0];
      const options = { algorithm: 'cosine', k: 5 };

      const key1 = service.generateCacheKey(vector1, options);
      const key2 = service.generateCacheKey(vector2, options);

      expect(key1).not.toBe(key2);
    });
  });
});
