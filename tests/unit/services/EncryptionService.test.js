import { describe, it, expect, beforeEach, vi } from 'vitest';
import { EncryptionService } from '../../../src/services/EncryptionService.js';

// Mock Chrome storage API
const mockChromeStorage = {
  local: {
    get: vi.fn(),
    set: vi.fn(),
    remove: vi.fn()
  }
};

global.chrome = {
  storage: mockChromeStorage
};

describe('EncryptionService', () => {
  let encryptionService;

  beforeEach(() => {
    encryptionService = new EncryptionService();
    vi.clearAllMocks();
  });

  describe('Initialization', () => {
    it('should initialize with a new encryption key', async () => {
      mockChromeStorage.local.get.mockResolvedValue({});
      mockChromeStorage.local.set.mockResolvedValue();

      await encryptionService.initialize();

      expect(encryptionService.initialized).toBe(true);
      expect(encryptionService.encryptionKey).toBeTruthy();
      expect(mockChromeStorage.local.set).toHaveBeenCalledWith({
        encryptionKey: expect.any(String)
      });
    });

    it('should use existing encryption key if available', async () => {
      const existingKey = 'existing-key-123';
      mockChromeStorage.local.get.mockResolvedValue({
        encryptionKey: existingKey
      });

      await encryptionService.initialize();

      expect(encryptionService.initialized).toBe(true);
      expect(encryptionService.encryptionKey).toBe(existingKey);
      expect(mockChromeStorage.local.set).not.toHaveBeenCalled();
    });
  });

  describe('Basic Encryption/Decryption', () => {
    beforeEach(async () => {
      mockChromeStorage.local.get.mockResolvedValue({});
      mockChromeStorage.local.set.mockResolvedValue();
      await encryptionService.initialize();
    });

    it('should encrypt and decrypt text correctly', async () => {
      const plaintext = 'sk-test-api-key-12345';

      const encrypted = await encryptionService.encrypt(plaintext);
      expect(encrypted).toBeTruthy();
      expect(encrypted).not.toBe(plaintext);

      const decrypted = await encryptionService.decrypt(encrypted);
      expect(decrypted).toBe(plaintext);
    });

    it('should handle empty strings', async () => {
      await expect(encryptionService.encrypt('')).rejects.toThrow();
    });

    it('should handle null/undefined input', async () => {
      await expect(encryptionService.encrypt(null)).rejects.toThrow();
      await expect(encryptionService.encrypt(undefined)).rejects.toThrow();
    });

    it('should handle invalid ciphertext', async () => {
      await expect(encryptionService.decrypt('invalid-ciphertext')).rejects.toThrow();
    });
  });

  describe('API Keys Encryption', () => {
    beforeEach(async () => {
      mockChromeStorage.local.get.mockResolvedValue({});
      mockChromeStorage.local.set.mockResolvedValue();
      await encryptionService.initialize();
    });

    it('should encrypt API keys object', async () => {
      const apiKeys = {
        deepseek: 'sk-deepseek-test-key',
        openai: 'sk-openai-test-key',
        fakturownia: 'fakturownia-api-key',
        infakt: ''
      };

      const encrypted = await encryptionService.encryptApiKeys(apiKeys);

      expect(encrypted.deepseek).toBeTruthy();
      expect(encrypted.deepseek).not.toBe(apiKeys.deepseek);
      expect(encrypted.openai).toBeTruthy();
      expect(encrypted.openai).not.toBe(apiKeys.openai);
      expect(encrypted.fakturownia).toBeTruthy();
      expect(encrypted.fakturownia).not.toBe(apiKeys.fakturownia);
      expect(encrypted.infakt).toBe(''); // Empty strings should remain empty
    });

    it('should decrypt API keys object', async () => {
      const apiKeys = {
        deepseek: 'sk-deepseek-test-key',
        openai: 'sk-openai-test-key',
        fakturownia: 'fakturownia-api-key',
        infakt: ''
      };

      const encrypted = await encryptionService.encryptApiKeys(apiKeys);
      const decrypted = await encryptionService.decryptApiKeys(encrypted);

      expect(decrypted).toEqual(apiKeys);
    });

    it('should handle corrupted encrypted keys gracefully', async () => {
      const corruptedKeys = {
        deepseek: 'corrupted-data',
        openai: 'also-corrupted',
        fakturownia: '',
        infakt: ''
      };

      const decrypted = await encryptionService.decryptApiKeys(corruptedKeys);

      expect(decrypted.deepseek).toBe(''); // Should return empty string for corrupted data
      expect(decrypted.openai).toBe('');
      expect(decrypted.fakturownia).toBe('');
      expect(decrypted.infakt).toBe('');
    });
  });

  describe('Validation', () => {
    beforeEach(async () => {
      mockChromeStorage.local.get.mockResolvedValue({});
      mockChromeStorage.local.set.mockResolvedValue();
      await encryptionService.initialize();
    });

    it('should validate encryption is working', async () => {
      const isValid = await encryptionService.validateEncryption();
      expect(isValid).toBe(true);
    });

    it('should detect encryption failures', async () => {
      // Corrupt the encryption key to simulate failure
      encryptionService.encryptionKey = 'corrupted-key';

      const isValid = await encryptionService.validateEncryption();
      expect(isValid).toBe(false);
    });
  });

  describe('Key Management', () => {
    beforeEach(async () => {
      mockChromeStorage.local.get.mockResolvedValue({});
      mockChromeStorage.local.set.mockResolvedValue();
      mockChromeStorage.local.remove.mockResolvedValue();
      await encryptionService.initialize();
    });

    it('should clear encryption key', async () => {
      await encryptionService.clearEncryptionKey();

      expect(encryptionService.encryptionKey).toBeNull();
      expect(encryptionService.initialized).toBe(false);
      expect(mockChromeStorage.local.remove).toHaveBeenCalledWith(['encryptionKey']);
    });

    it('should generate unique encryption keys', () => {
      const key1 = encryptionService.generateEncryptionKey();
      const key2 = encryptionService.generateEncryptionKey();

      expect(key1).toBeTruthy();
      expect(key2).toBeTruthy();
      expect(key1).not.toBe(key2);
      expect(key1.length).toBeGreaterThan(20); // Base64 encoded 256-bit key should be longer
    });
  });

  describe('Error Handling', () => {
    it('should handle Chrome storage errors during initialization', async () => {
      mockChromeStorage.local.get.mockRejectedValue(new Error('Storage error'));

      await expect(encryptionService.initialize()).rejects.toThrow('Storage error');
    });

    it('should handle encryption errors gracefully', async () => {
      // Don't initialize to simulate error state
      await expect(encryptionService.encrypt('test')).rejects.toThrow();
    });

    it('should handle decryption errors gracefully', async () => {
      // Don't initialize to simulate error state
      await expect(encryptionService.decrypt('test')).rejects.toThrow();
    });
  });

  describe('Edge Cases', () => {
    beforeEach(async () => {
      mockChromeStorage.local.get.mockResolvedValue({});
      mockChromeStorage.local.set.mockResolvedValue();
      await encryptionService.initialize();
    });

    it('should handle very long strings', async () => {
      const longString = 'a'.repeat(10000);

      const encrypted = await encryptionService.encrypt(longString);
      const decrypted = await encryptionService.decrypt(encrypted);

      expect(decrypted).toBe(longString);
    });

    it('should handle special characters', async () => {
      const specialString = '!@#$%^&*()_+-=[]{}|;:,.<>?`~';

      const encrypted = await encryptionService.encrypt(specialString);
      const decrypted = await encryptionService.decrypt(encrypted);

      expect(decrypted).toBe(specialString);
    });

    it('should handle unicode characters', async () => {
      const unicodeString = '🔐🔑🛡️💾🌟';

      const encrypted = await encryptionService.encrypt(unicodeString);
      const decrypted = await encryptionService.decrypt(encrypted);

      expect(decrypted).toBe(unicodeString);
    });
  });
});
