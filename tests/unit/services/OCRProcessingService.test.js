/**
 * OCRProcessingService Unit Tests
 * Tests for EPIC-002 Story 2.3 Task 2.3.1 - Tesseract.js Integration
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { OCRProcessingService } from '../../../src/services/OCRProcessingService.js';

// Mock dependencies
vi.mock('../../../src/components/processors/OCRProcessor.js', () => ({
  OCRProcessor: vi.fn().mockImplementation(() => ({
    initialize: vi.fn().mockResolvedValue(undefined),
    performOCR: vi.fn().mockResolvedValue('Mocked OCR text'),
    terminate: vi.fn().mockResolvedValue(undefined)
  }))
}));

vi.mock('../../../src/utils/imageUtils.js', () => ({
  imageUtils: {
    loadImageFromFile: vi.fn().mockResolvedValue(new Image()),
    createCanvasFromImage: vi.fn().mockReturnValue(document.createElement('canvas')),
    enhanceContrast: vi.fn(),
    reduceNoise: vi.fn(),
    sharpenImage: vi.fn()
  }
}));

vi.mock('../../../src/utils/ocrUtils.js', () => ({
  ocrUtils: {
    cleanOCRText: vi.fn().mockImplementation(text => text),
    detectLanguage: vi.fn().mockReturnValue('pol+eng'),
    calculateConfidence: vi.fn().mockReturnValue(85),
    applyPolishCorrections: vi.fn().mockImplementation(text => text)
  }
}));

describe('OCRProcessingService', () => {
  let ocrService;
  let mockFile;

  beforeEach(() => {
    ocrService = new OCRProcessingService();

    // Create mock file
    mockFile = new File(['test content'], 'test-invoice.jpg', {
      type: 'image/jpeg',
      lastModified: Date.now()
    });

    // Mock canvas context
    global.document = {
      createElement: vi.fn().mockReturnValue({
        getContext: vi.fn().mockReturnValue({
          getImageData: vi.fn().mockReturnValue({
            data: new Uint8ClampedArray(400), // 10x10 image
            width: 10,
            height: 10
          }),
          putImageData: vi.fn()
        }),
        width: 100,
        height: 100
      })
    };
  });

  afterEach(async () => {
    if (ocrService) {
      await ocrService.cleanup();
    }
    vi.clearAllMocks();
  });

  describe('Initialization', () => {
    it('should initialize successfully', async () => {
      await expect(ocrService.initialize()).resolves.not.toThrow();
      expect(ocrService.initialized).toBe(true);
    });

    it('should not reinitialize if already initialized', async () => {
      await ocrService.initialize();
      const firstInit = ocrService.initialized;

      await ocrService.initialize();
      expect(ocrService.initialized).toBe(firstInit);
    });

    it('should handle initialization errors', async () => {
      const errorService = new OCRProcessingService();
      errorService.ocrProcessor.initialize = vi.fn().mockRejectedValue(new Error('Init failed'));

      await expect(errorService.initialize()).rejects.toThrow('OCR service initialization failed');
    });
  });

  describe('File Validation', () => {
    it('should validate supported image formats', () => {
      const jpegFile = new File([''], 'test.jpg', { type: 'image/jpeg' });
      const pngFile = new File([''], 'test.png', { type: 'image/png' });

      expect(() => ocrService.validateImageFile(jpegFile)).not.toThrow();
      expect(() => ocrService.validateImageFile(pngFile)).not.toThrow();
    });

    it('should reject unsupported file formats', () => {
      const txtFile = new File([''], 'test.txt', { type: 'text/plain' });

      expect(() => ocrService.validateImageFile(txtFile)).toThrow('Unsupported image format');
    });

    it('should reject files that are too large', () => {
      const largeFile = new File(['x'.repeat(11 * 1024 * 1024)], 'large.jpg', {
        type: 'image/jpeg'
      });

      expect(() => ocrService.validateImageFile(largeFile)).toThrow('Image file too large');
    });

    it('should reject files without names', () => {
      const noNameFile = new File([''], '', { type: 'image/jpeg' });

      expect(() => ocrService.validateImageFile(noNameFile)).toThrow('Image file must have a valid name');
    });
  });

  describe('Text Extraction', () => {
    it('should extract text from image successfully', async () => {
      const result = await ocrService.extractTextFromImage(mockFile);

      expect(result.success).toBe(true);
      expect(result.text).toBeDefined();
      expect(result.confidence).toBeGreaterThanOrEqual(0);
      expect(result.metadata).toBeDefined();
      expect(result.metadata.fileName).toBe('test-invoice.jpg');
      expect(result.metadata.extractionMethod).toBe('ocr');
    });

    it('should handle OCR processing errors gracefully', async () => {
      ocrService.ocrProcessor.performOCR = vi.fn().mockRejectedValue(new Error('OCR failed'));

      const result = await ocrService.extractTextFromImage(mockFile);

      expect(result.success).toBe(false);
      expect(result.error).toBe('OCR failed');
      expect(result.text).toBe('');
      expect(result.confidence).toBe(0);
    });

    it('should call progress callback during processing', async () => {
      const progressCallback = vi.fn();

      await ocrService.extractTextFromImage(mockFile, {
        onProgress: progressCallback
      });

      expect(progressCallback).toHaveBeenCalled();
      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          stage: expect.any(String),
          progress: expect.any(Number)
        })
      );
    });

    it('should apply preprocessing when enabled', async () => {
      const { imageUtils } = await import('../../../src/utils/imageUtils.js');

      await ocrService.extractTextFromImage(mockFile, {
        enablePreprocessing: true
      });

      expect(imageUtils.loadImageFromFile).toHaveBeenCalledWith(mockFile);
      expect(imageUtils.enhanceContrast).toHaveBeenCalled();
      expect(imageUtils.reduceNoise).toHaveBeenCalled();
      expect(imageUtils.sharpenImage).toHaveBeenCalled();
    });

    it('should skip preprocessing when disabled', async () => {
      const { imageUtils } = await import('../../../src/utils/imageUtils.js');

      await ocrService.extractTextFromImage(mockFile, {
        enablePreprocessing: false
      });

      expect(imageUtils.loadImageFromFile).not.toHaveBeenCalled();
    });
  });

  describe('Batch Processing', () => {
    it('should process multiple files', async () => {
      const files = [
        new File([''], 'file1.jpg', { type: 'image/jpeg' }),
        new File([''], 'file2.png', { type: 'image/png' })
      ];

      const results = await ocrService.processBatch(files);

      expect(results).toHaveLength(2);
      expect(results[0].file).toBe('file1.jpg');
      expect(results[1].file).toBe('file2.png');
      expect(results[0].index).toBe(0);
      expect(results[1].index).toBe(1);
    });

    it('should handle batch processing errors', async () => {
      ocrService.ocrProcessor.performOCR = vi.fn()
        .mockResolvedValueOnce('Success')
        .mockRejectedValueOnce(new Error('Failed'));

      const files = [
        new File([''], 'file1.jpg', { type: 'image/jpeg' }),
        new File([''], 'file2.jpg', { type: 'image/jpeg' })
      ];

      const results = await ocrService.processBatch(files);

      expect(results).toHaveLength(2);
      expect(results[0].success).toBe(true);
      expect(results[1].success).toBe(false);
      expect(results[1].error).toBe('Failed');
    });

    it('should call progress callback for batch processing', async () => {
      const progressCallback = vi.fn();
      const files = [
        new File([''], 'file1.jpg', { type: 'image/jpeg' }),
        new File([''], 'file2.jpg', { type: 'image/jpeg' })
      ];

      await ocrService.processBatch(files, {
        onProgress: progressCallback
      });

      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          stage: 'batch_processing',
          currentFile: expect.any(String),
          fileIndex: expect.any(Number),
          totalFiles: 2
        })
      );
    });
  });

  describe('Image Preprocessing', () => {
    it('should preprocess image successfully', async () => {
      const { imageUtils } = await import('../../../src/utils/imageUtils.js');

      const result = await ocrService.preprocessImage(mockFile);

      expect(imageUtils.loadImageFromFile).toHaveBeenCalledWith(mockFile);
      expect(result).toBeDefined();
    });

    it('should handle preprocessing errors gracefully', async () => {
      const { imageUtils } = await import('../../../src/utils/imageUtils.js');
      imageUtils.loadImageFromFile = vi.fn().mockRejectedValue(new Error('Load failed'));

      const result = await ocrService.preprocessImage(mockFile);

      expect(result).toBe(mockFile); // Should return original file on error
    });
  });

  describe('Post-processing', () => {
    it('should post-process OCR results', async () => {
      const { ocrUtils } = await import('../../../src/utils/ocrUtils.js');

      const result = await ocrService.postProcessOCRResult('test text', {
        language: 'pol+eng',
        minConfidence: 60
      });

      expect(ocrUtils.cleanOCRText).toHaveBeenCalledWith('test text');
      expect(ocrUtils.detectLanguage).toHaveBeenCalled();
      expect(ocrUtils.calculateConfidence).toHaveBeenCalled();
      expect(result.text).toBeDefined();
      expect(result.confidence).toBeDefined();
      expect(result.detectedLanguage).toBeDefined();
      expect(result.processingTime).toBeGreaterThanOrEqual(0);
    });

    it('should handle post-processing errors', async () => {
      const { ocrUtils } = await import('../../../src/utils/ocrUtils.js');
      ocrUtils.cleanOCRText = vi.fn().mockImplementation(() => {
        throw new Error('Cleaning failed');
      });

      const result = await ocrService.postProcessOCRResult('test text');

      expect(result.text).toBe('test text');
      expect(result.confidence).toBe(0);
      expect(result.detectedLanguage).toBe('unknown');
    });
  });

  describe('Cleanup', () => {
    it('should cleanup resources properly', async () => {
      await ocrService.initialize();
      await ocrService.cleanup();

      expect(ocrService.ocrProcessor.terminate).toHaveBeenCalled();
      expect(ocrService.initialized).toBe(false);
    });

    it('should handle cleanup errors gracefully', async () => {
      ocrService.ocrProcessor.terminate = vi.fn().mockRejectedValue(new Error('Cleanup failed'));

      await expect(ocrService.cleanup()).resolves.not.toThrow();
    });
  });

  describe('Configuration', () => {
    it('should have correct default configuration', () => {
      expect(ocrService.config.defaultLanguage).toBe('pol+eng');
      expect(ocrService.config.minConfidence).toBe(60);
      expect(ocrService.config.enablePreprocessing).toBe(true);
      expect(ocrService.config.maxImageSize).toBe(10 * 1024 * 1024);
      expect(ocrService.config.supportedFormats).toContain('image/jpeg');
      expect(ocrService.config.supportedFormats).toContain('image/png');
    });
  });
});
