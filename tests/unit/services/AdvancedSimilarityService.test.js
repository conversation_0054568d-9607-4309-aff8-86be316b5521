/**
 * Advanced Similarity Service Tests
 *
 * Comprehensive unit tests for the AdvancedSimilarityService class,
 * testing enhanced similarity calculations, context-aware scoring,
 * and performance optimization features.
 *
 * <AUTHOR> Agent
 * @version 1.0.0
 * @since 2025-06-14
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { AdvancedSimilarityService } from '../../../src/services/AdvancedSimilarityService.js';

// Mock dependencies
vi.mock('../../../src/utils/ProcessingLogger.js', () => ({
  ProcessingLogger: vi.fn().mockImplementation(() => ({
    log: vi.fn(),
    warn: vi.fn(),
    error: vi.fn()
  }))
}));

vi.mock('../../../src/services/VectorSimilarityService.js', () => ({
  VectorSimilarityService: vi.fn().mockImplementation(() => ({
    calculateSimilarity: vi.fn().mockResolvedValue({
      score: 0.75,
      confidence: 0.85,
      relationshipType: 'similar',
      isRelated: true
    })
  }))
}));

vi.mock('../../../src/services/DocumentRelationshipScorer.js', () => ({
  DocumentRelationshipScorer: vi.fn().mockImplementation(() => ({
    scoreRelationship: vi.fn().mockReturnValue({
      compositeScore: 0.80,
      confidence: 0.90,
      relationshipStrength: 'strong'
    })
  }))
}));

vi.mock('../../../src/services/VectorSearchService.js', () => ({
  VectorSearchService: vi.fn().mockImplementation(() => ({
    search: vi.fn().mockResolvedValue([
      {
        id: 'doc2',
        similarity: 0.85,
        distance: 0.15,
        algorithm: 'hybrid',
        metadata: { documentType: 'invoice' }
      }
    ])
  }))
}));

describe('AdvancedSimilarityService', () => {
  let service;
  let mockEmbedding1;
  let mockEmbedding2;

  beforeEach(() => {
    service = new AdvancedSimilarityService({
      strongSimilarityThreshold: 0.85,
      moderateSimilarityThreshold: 0.70,
      weakSimilarityThreshold: 0.55,
      cacheEnabled: true,
      useContextualEmbeddings: true,
      enableClusteringAnalysis: true
    });

    mockEmbedding1 = {
      vector: [0.1, 0.2, 0.3, 0.4, 0.5],
      method: 'deepseek-api',
      confidence: 0.95,
      metadata: {
        documentId: 'doc1',
        documentType: 'invoice',
        textLength: 1000,
        language: 'en'
      }
    };

    mockEmbedding2 = {
      vector: [0.15, 0.25, 0.35, 0.45, 0.55],
      method: 'deepseek-api',
      confidence: 0.90,
      metadata: {
        documentId: 'doc2',
        documentType: 'invoice',
        textLength: 1200,
        language: 'en'
      }
    };
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Constructor', () => {
    it('should initialize with default configuration', () => {
      const defaultService = new AdvancedSimilarityService();
      expect(defaultService.config.strongSimilarityThreshold).toBe(0.85);
      expect(defaultService.config.moderateSimilarityThreshold).toBe(0.70);
      expect(defaultService.config.weakSimilarityThreshold).toBe(0.55);
    });

    it('should initialize with custom configuration', () => {
      const customService = new AdvancedSimilarityService({
        strongSimilarityThreshold: 0.90,
        moderateSimilarityThreshold: 0.75,
        weakSimilarityThreshold: 0.60
      });

      expect(customService.config.strongSimilarityThreshold).toBe(0.90);
      expect(customService.config.moderateSimilarityThreshold).toBe(0.75);
      expect(customService.config.weakSimilarityThreshold).toBe(0.60);
    });

    it('should initialize performance metrics', () => {
      expect(service.metrics.totalComparisons).toBe(0);
      expect(service.metrics.averageProcessingTime).toBe(0);
      expect(service.metrics.enhancementSuccess).toBe(0);
    });
  });

  describe('calculateEnhancedSimilarity', () => {
    it('should calculate enhanced similarity successfully', async () => {
      const result = await service.calculateEnhancedSimilarity(mockEmbedding1, mockEmbedding2);

      expect(result).toHaveProperty('score');
      expect(result).toHaveProperty('confidence');
      expect(result).toHaveProperty('relationshipType');
      expect(result).toHaveProperty('isRelated');
      expect(result.enhanced).toBe(true);
      expect(result).toHaveProperty('components');
      expect(result).toHaveProperty('metadata');
    });

    it('should handle different document types', async () => {
      const embedding2Modified = {
        ...mockEmbedding2,
        metadata: {
          ...mockEmbedding2.metadata,
          documentType: 'receipt'
        }
      };

      const result = await service.calculateEnhancedSimilarity(mockEmbedding1, embedding2Modified);
      expect(result.enhanced).toBe(true);
      expect(result.score).toBeGreaterThanOrEqual(0);
      expect(result.score).toBeLessThanOrEqual(1);
    });

    it('should cache results when caching is enabled', async () => {
      const result1 = await service.calculateEnhancedSimilarity(mockEmbedding1, mockEmbedding2);
      const result2 = await service.calculateEnhancedSimilarity(mockEmbedding1, mockEmbedding2);

      expect(result1.score).toBe(result2.score);
      expect(service.similarityCache.size).toBeGreaterThan(0);
    });

    it('should handle errors gracefully', async () => {
      const invalidEmbedding = { vector: null, metadata: {} };

      const result = await service.calculateEnhancedSimilarity(invalidEmbedding, mockEmbedding2);
      expect(result).toBeDefined();
      // Should fallback to base similarity service
    });
  });

  describe('findSimilarDocumentsEnhanced', () => {
    it('should find similar documents with enhanced search', async () => {
      const documentEmbeddings = [mockEmbedding2];

      const results = await service.findSimilarDocumentsEnhanced(
        mockEmbedding1,
        documentEmbeddings,
        { maxResults: 5 }
      );

      expect(Array.isArray(results)).toBe(true);
      expect(results.length).toBeGreaterThanOrEqual(0);
    });

    it('should respect maxResults parameter', async () => {
      const documentEmbeddings = [mockEmbedding2, mockEmbedding2, mockEmbedding2];

      const results = await service.findSimilarDocumentsEnhanced(
        mockEmbedding1,
        documentEmbeddings,
        { maxResults: 2 }
      );

      expect(results.length).toBeLessThanOrEqual(2);
    });

    it('should handle empty document collection', async () => {
      const results = await service.findSimilarDocumentsEnhanced(
        mockEmbedding1,
        [],
        { maxResults: 5 }
      );

      expect(Array.isArray(results)).toBe(true);
      expect(results.length).toBe(0);
    });
  });

  describe('calculateStructuralSimilarity', () => {
    it('should calculate structural similarity based on metadata', () => {
      const score = service.calculateStructuralSimilarity(mockEmbedding1, mockEmbedding2);

      expect(typeof score).toBe('number');
      expect(score).toBeGreaterThanOrEqual(0);
      expect(score).toBeLessThanOrEqual(1);
    });

    it('should give higher scores for same document types', () => {
      const sameTypeScore = service.calculateStructuralSimilarity(mockEmbedding1, mockEmbedding2);

      const differentTypeEmbedding = {
        ...mockEmbedding2,
        metadata: { ...mockEmbedding2.metadata, documentType: 'contract' }
      };
      const differentTypeScore = service.calculateStructuralSimilarity(mockEmbedding1, differentTypeEmbedding);

      expect(sameTypeScore).toBeGreaterThanOrEqual(differentTypeScore);
    });

    it('should handle missing metadata gracefully', () => {
      const embeddingWithoutMeta = { vector: [0.1, 0.2, 0.3], metadata: {} };
      const score = service.calculateStructuralSimilarity(embeddingWithoutMeta, mockEmbedding2);

      expect(score).toBe(0);
    });
  });

  describe('determineEnhancedRelationshipType', () => {
    it('should classify strong relationships correctly', () => {
      const type = service.determineEnhancedRelationshipType(0.90);
      expect(type).toBe('strong');
    });

    it('should classify moderate relationships correctly', () => {
      const type = service.determineEnhancedRelationshipType(0.75);
      expect(type).toBe('moderate');
    });

    it('should classify weak relationships correctly', () => {
      const type = service.determineEnhancedRelationshipType(0.60);
      expect(type).toBe('weak');
    });

    it('should classify no relationship correctly', () => {
      const type = service.determineEnhancedRelationshipType(0.30);
      expect(type).toBe('none');
    });
  });

  describe('cosineSimilarity', () => {
    it('should calculate cosine similarity correctly', () => {
      const vector1 = [1, 0, 0];
      const vector2 = [0, 1, 0];
      const similarity = service.cosineSimilarity(vector1, vector2);
      expect(similarity).toBe(0);
    });

    it('should handle identical vectors', () => {
      const vector1 = [1, 2, 3];
      const vector2 = [1, 2, 3];
      const similarity = service.cosineSimilarity(vector1, vector2);
      expect(similarity).toBeCloseTo(1, 5);
    });

    it('should handle zero vectors', () => {
      const vector1 = [0, 0, 0];
      const vector2 = [1, 2, 3];
      const similarity = service.cosineSimilarity(vector1, vector2);
      expect(similarity).toBe(0);
    });

    it('should handle mismatched vector lengths', () => {
      const vector1 = [1, 2];
      const vector2 = [1, 2, 3];
      const similarity = service.cosineSimilarity(vector1, vector2);
      expect(similarity).toBe(0);
    });
  });

  describe('Performance and Metrics', () => {
    it('should update metrics after calculations', async () => {
      const initialComparisons = service.metrics.totalComparisons;

      await service.calculateEnhancedSimilarity(mockEmbedding1, mockEmbedding2);

      expect(service.metrics.totalComparisons).toBe(initialComparisons + 1);
      expect(service.metrics.averageProcessingTime).toBeGreaterThan(0);
    });

    it('should provide performance metrics', () => {
      const metrics = service.getMetrics();

      expect(metrics).toHaveProperty('totalComparisons');
      expect(metrics).toHaveProperty('averageProcessingTime');
      expect(metrics).toHaveProperty('cacheHitRate');
      expect(metrics).toHaveProperty('enhancementSuccessRate');
    });

    it('should reset metrics and cache', () => {
      service.metrics.totalComparisons = 10;
      service.similarityCache.set('test', {});

      service.reset();

      expect(service.metrics.totalComparisons).toBe(0);
      expect(service.similarityCache.size).toBe(0);
    });
  });

  describe('Caching', () => {
    it('should generate consistent cache keys', () => {
      const key1 = service.generateCacheKey(mockEmbedding1, mockEmbedding2, {});
      const key2 = service.generateCacheKey(mockEmbedding1, mockEmbedding2, {});

      expect(key1).toBe(key2);
    });

    it('should generate different cache keys for different inputs', () => {
      const key1 = service.generateCacheKey(mockEmbedding1, mockEmbedding2, {});
      const key2 = service.generateCacheKey(mockEmbedding2, mockEmbedding1, {});

      expect(key1).not.toBe(key2);
    });

    it('should respect cache size limits', () => {
      const smallCacheService = new AdvancedSimilarityService({ maxCacheSize: 2 });

      smallCacheService.cacheResult('key1', { score: 0.1 });
      smallCacheService.cacheResult('key2', { score: 0.2 });
      smallCacheService.cacheResult('key3', { score: 0.3 });

      expect(smallCacheService.similarityCache.size).toBe(2);
    });
  });
});
