/**
 * Unit Tests for UserSubscription Model
 * Tests all functionality of the UserSubscription class
 *
 * @fileoverview Comprehensive unit tests for UserSubscription model
 * <AUTHOR> Development Team
 * @since 1.0.0
 */

import {
  UserSubscription,
  SUBSCRIPTION_STATUS,
  BILLING_CYCLE
} from '../../../src/core/models/UserSubscription.js';
import { TIER_DEFINITIONS } from '../../../src/core/config/subscriptionTiers.js';

describe('UserSubscription Model', () => {
  let testSubscription, activeSubscription, trialSubscription, cancelledSubscription;

  beforeEach(() => {
    // Use current date to ensure subscriptions are not expired
    const now = new Date();
    const futureDate = new Date(now);
    futureDate.setMonth(futureDate.getMonth() + 1); // 1 month from now

    testSubscription = new UserSubscription({
      userId: 'user123',
      tierId: 'professional',
      status: SUBSCRIPTION_STATUS.ACTIVE,
      billingCycle: BILLING_CYCLE.MONTHLY,
      startDate: now.toISOString(),
      endDate: futureDate.toISOString(),
      price: 29
    });

    const annualFutureDate = new Date(now);
    annualFutureDate.setFullYear(annualFutureDate.getFullYear() + 1); // 1 year from now

    activeSubscription = new UserSubscription({
      userId: 'user456',
      tierId: 'business',
      status: SUBSCRIPTION_STATUS.ACTIVE,
      billingCycle: BILLING_CYCLE.ANNUAL,
      startDate: now.toISOString(),
      endDate: annualFutureDate.toISOString(),
      price: 990
    });

    const trialFutureDate = new Date(now);
    trialFutureDate.setDate(trialFutureDate.getDate() + 10); // 10 days from now

    trialSubscription = new UserSubscription({
      userId: 'user789',
      tierId: 'professional',
      status: SUBSCRIPTION_STATUS.TRIAL,
      startDate: now.toISOString(),
      trialEndDate: trialFutureDate.toISOString()
    });

    cancelledSubscription = new UserSubscription({
      userId: 'user999',
      tierId: 'starter',
      status: SUBSCRIPTION_STATUS.CANCELLED,
      cancellationDate: now.toISOString(),
      cancellationReason: 'User requested'
    });
  });

  describe('Constructor and Basic Properties', () => {
    test('should create subscription with default values', () => {
      const subscription = new UserSubscription();

      expect(subscription.userId).toBe('');
      expect(subscription.tierId).toBe('starter');
      expect(subscription.status).toBe(SUBSCRIPTION_STATUS.TRIAL);
      expect(subscription.billingCycle).toBe(BILLING_CYCLE.MONTHLY);
      expect(subscription.price).toBe(0);
      expect(subscription.currency).toBe('EUR');
      expect(subscription.autoRenew).toBe(true);
    });

    test('should create subscription with provided data', () => {
      const data = {
        userId: 'test123',
        tierId: 'business',
        status: SUBSCRIPTION_STATUS.ACTIVE,
        billingCycle: BILLING_CYCLE.ANNUAL,
        price: 990
      };

      const subscription = new UserSubscription(data);

      expect(subscription.userId).toBe('test123');
      expect(subscription.tierId).toBe('business');
      expect(subscription.status).toBe(SUBSCRIPTION_STATUS.ACTIVE);
      expect(subscription.billingCycle).toBe(BILLING_CYCLE.ANNUAL);
      expect(subscription.price).toBe(990);
    });

    test('should set timestamps on creation', () => {
      const subscription = new UserSubscription();

      expect(subscription.createdAt).toBeDefined();
      expect(subscription.updatedAt).toBeDefined();
      expect(new Date(subscription.createdAt)).toBeInstanceOf(Date);
      expect(new Date(subscription.updatedAt)).toBeInstanceOf(Date);
    });

    test('should calculate end date based on billing cycle', () => {
      const startDate = '2025-01-01T00:00:00.000Z';

      const monthlySubscription = new UserSubscription({
        startDate,
        billingCycle: BILLING_CYCLE.MONTHLY
      });

      const annualSubscription = new UserSubscription({
        startDate,
        billingCycle: BILLING_CYCLE.ANNUAL
      });

      expect(new Date(monthlySubscription.endDate).getMonth()).toBe(1); // February (month 1)
      expect(new Date(annualSubscription.endDate).getFullYear()).toBe(2026);
    });
  });

  describe('Tier Management', () => {
    test('should get subscription tier', () => {
      const tier = testSubscription.getTier();
      expect(tier).toBeDefined();
      expect(tier.id).toBe('professional');
      expect(tier.name).toBe('Professional');
    });

    test('should cache tier reference', () => {
      const tier1 = testSubscription.getTier();
      const tier2 = testSubscription.getTier();
      expect(tier1).toBe(tier2); // Same reference
    });

    test('should refresh tier cache when tier changes', () => {
      const originalTier = testSubscription.getTier();
      testSubscription.tierId = 'business';
      const newTier = testSubscription.getTier();

      expect(originalTier.id).toBe('professional');
      expect(newTier.id).toBe('business');
    });
  });

  describe('Status Checking', () => {
    test('should check if subscription is active', () => {
      expect(testSubscription.isActive()).toBe(true);
      expect(cancelledSubscription.isActive()).toBe(false);
    });

    test('should check if subscription is in trial', () => {
      expect(trialSubscription.isTrial()).toBe(true);
      expect(testSubscription.isTrial()).toBe(false);
    });

    test('should check if subscription is cancelled', () => {
      expect(cancelledSubscription.isCancelled()).toBe(true);
      expect(testSubscription.isCancelled()).toBe(false);
    });

    test('should check if subscription is expired', () => {
      const expiredSubscription = new UserSubscription({
        status: SUBSCRIPTION_STATUS.ACTIVE,
        endDate: '2024-01-01T00:00:00.000Z' // Past date
      });

      expect(expiredSubscription.isExpired()).toBe(true);
      expect(testSubscription.isExpired()).toBe(false);
    });
  });

  describe('Date Calculations', () => {
    test('should calculate days remaining correctly', () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 30);

      const subscription = new UserSubscription({
        endDate: futureDate.toISOString()
      });

      const daysRemaining = subscription.getDaysRemaining();
      expect(daysRemaining).toBeGreaterThan(29);
      expect(daysRemaining).toBeLessThanOrEqual(30);
    });

    test('should return 0 days remaining for expired subscription', () => {
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 10);

      const subscription = new UserSubscription({
        endDate: pastDate.toISOString()
      });

      expect(subscription.getDaysRemaining()).toBe(0);
    });

    test('should calculate trial days remaining', () => {
      const recentDate = new Date();
      recentDate.setDate(recentDate.getDate() - 5); // Started 5 days ago

      const subscription = new UserSubscription({
        status: SUBSCRIPTION_STATUS.TRIAL,
        startDate: recentDate.toISOString()
      });

      const trialDaysRemaining = subscription.getTrialDaysRemaining();
      expect(trialDaysRemaining).toBeGreaterThan(8);
      expect(trialDaysRemaining).toBeLessThanOrEqual(9);
    });
  });

  describe('Upgrade and Downgrade', () => {
    test('should allow upgrade to higher tier', () => {
      expect(testSubscription.canUpgradeTo('business')).toBe(true);
      expect(testSubscription.canUpgradeTo('enterprise')).toBe(true);
    });

    test('should not allow upgrade to same or lower tier', () => {
      expect(testSubscription.canUpgradeTo('professional')).toBe(false);
      expect(testSubscription.canUpgradeTo('starter')).toBe(false);
    });

    test('should allow downgrade to lower tier', () => {
      expect(activeSubscription.canDowngradeTo('professional')).toBe(true);
      expect(activeSubscription.canDowngradeTo('starter')).toBe(true);
    });

    test('should not allow downgrade to same or higher tier', () => {
      expect(activeSubscription.canDowngradeTo('business')).toBe(false);
      expect(activeSubscription.canDowngradeTo('enterprise')).toBe(false);
    });

    test('should perform successful upgrade', () => {
      const result = testSubscription.upgradeTo('business');

      expect(result.success).toBe(true);
      expect(result.previousTierId).toBe('professional');
      expect(result.newTierId).toBe('business');
      expect(testSubscription.tierId).toBe('business');
      expect(testSubscription.price).toBe(99); // Business monthly price
    });

    test('should perform successful downgrade', () => {
      const result = activeSubscription.downgradeTo('professional');

      expect(result.success).toBe(true);
      expect(result.previousTierId).toBe('business');
      expect(result.newTierId).toBe('professional');
      expect(activeSubscription.tierId).toBe('professional');
      expect(activeSubscription.price).toBe(290); // Professional annual price
    });

    test('should reject invalid upgrade', () => {
      const result = testSubscription.upgradeTo('starter');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Upgrade not allowed');
      expect(testSubscription.tierId).toBe('professional'); // Unchanged
    });

    test('should reject invalid downgrade', () => {
      const result = testSubscription.downgradeTo('enterprise');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Downgrade not allowed');
      expect(testSubscription.tierId).toBe('professional'); // Unchanged
    });
  });

  describe('Cancellation and Reactivation', () => {
    test('should cancel subscription immediately', () => {
      const result = testSubscription.cancel('Testing cancellation', true);

      expect(result.success).toBe(true);
      expect(result.immediate).toBe(true);
      expect(testSubscription.status).toBe(SUBSCRIPTION_STATUS.CANCELLED);
      expect(testSubscription.cancellationReason).toBe('Testing cancellation');
      expect(testSubscription.autoRenew).toBe(false);
    });

    test('should cancel subscription at period end', () => {
      const originalEndDate = testSubscription.endDate;
      const result = testSubscription.cancel('Testing cancellation', false);

      expect(result.success).toBe(true);
      expect(result.immediate).toBe(false);
      expect(testSubscription.status).toBe(SUBSCRIPTION_STATUS.CANCELLED);
      expect(testSubscription.endDate).toBe(originalEndDate); // Unchanged
      expect(testSubscription.autoRenew).toBe(false);
    });

    test('should not cancel already cancelled subscription', () => {
      const result = cancelledSubscription.cancel('Already cancelled');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Already cancelled');
    });

    test('should reactivate cancelled subscription', () => {
      const result = cancelledSubscription.reactivate();

      expect(result.success).toBe(true);
      expect(cancelledSubscription.status).toBe(SUBSCRIPTION_STATUS.ACTIVE);
      expect(cancelledSubscription.autoRenew).toBe(true);
      expect(cancelledSubscription.cancellationDate).toBeNull();
      expect(cancelledSubscription.cancellationReason).toBeNull();
    });

    test('should not reactivate active subscription', () => {
      const result = testSubscription.reactivate();

      expect(result.success).toBe(false);
      expect(result.error).toBe('Not cancelled');
    });
  });

  describe('Renewal', () => {
    test('should renew subscription successfully', () => {
      const originalEndDate = testSubscription.endDate;
      const result = testSubscription.renew();

      expect(result.success).toBe(true);
      expect(result.previousEndDate).toBe(originalEndDate);
      expect(testSubscription.endDate).not.toBe(originalEndDate);
      expect(testSubscription.status).toBe(SUBSCRIPTION_STATUS.ACTIVE);
    });

    test('should not renew subscription with auto-renewal disabled', () => {
      testSubscription.autoRenew = false;
      const result = testSubscription.renew();

      expect(result.success).toBe(false);
      expect(result.error).toBe('Auto-renewal disabled');
    });
  });

  describe('JSON Serialization', () => {
    test('should convert to JSON correctly', () => {
      const json = testSubscription.toJSON();

      expect(json.userId).toBe('user123');
      expect(json.tierId).toBe('professional');
      expect(json.status).toBe(SUBSCRIPTION_STATUS.ACTIVE);
      expect(json.price).toBe(29);
      expect(json.createdAt).toBeDefined();
    });

    test('should create from JSON correctly', () => {
      const json = testSubscription.toJSON();
      const newSubscription = UserSubscription.fromJSON(json);

      expect(newSubscription.userId).toBe(testSubscription.userId);
      expect(newSubscription.tierId).toBe(testSubscription.tierId);
      expect(newSubscription.status).toBe(testSubscription.status);
      expect(newSubscription.price).toBe(testSubscription.price);
    });
  });

  describe('Subscription Management', () => {
    test('should update subscription properties', () => {
      const originalUpdatedAt = testSubscription.updatedAt;

      // Wait a bit to ensure timestamp difference
      setTimeout(() => {
        testSubscription.update({ price: 35, billingCycle: BILLING_CYCLE.ANNUAL });

        expect(testSubscription.price).toBe(35);
        expect(testSubscription.billingCycle).toBe(BILLING_CYCLE.ANNUAL);
        expect(testSubscription.updatedAt).not.toBe(originalUpdatedAt);
        expect(testSubscription.userId).toBe('user123'); // Should not change
      }, 10);
    });

    test('should clone subscription with modifications', () => {
      const cloned = testSubscription.clone({
        userId: 'cloned123',
        price: 50
      });

      expect(cloned.userId).toBe('cloned123');
      expect(cloned.price).toBe(50);
      expect(cloned.tierId).toBe(testSubscription.tierId);
      expect(cloned).not.toBe(testSubscription); // Different instances
    });

    test('should get subscription identifier', () => {
      expect(testSubscription.getIdentifier()).toBe('user123-professional');

      const emptySubscription = new UserSubscription();
      expect(emptySubscription.getIdentifier()).toBe('-starter');
    });

    test('should get subscription summary', () => {
      const summary = testSubscription.getSummary();

      expect(summary.userId).toBe('user123');
      expect(summary.tier).toBeDefined();
      expect(summary.tier.id).toBe('professional');
      expect(summary.status).toBe(SUBSCRIPTION_STATUS.ACTIVE);
      expect(summary.isActive).toBe(true);
      expect(summary.isTrial).toBe(false);
      expect(summary.daysRemaining).toBeGreaterThan(0);
    });
  });

  describe('Constants', () => {
    test('should have correct subscription status constants', () => {
      expect(SUBSCRIPTION_STATUS.TRIAL).toBe('trial');
      expect(SUBSCRIPTION_STATUS.ACTIVE).toBe('active');
      expect(SUBSCRIPTION_STATUS.EXPIRED).toBe('expired');
      expect(SUBSCRIPTION_STATUS.CANCELLED).toBe('cancelled');
      expect(SUBSCRIPTION_STATUS.SUSPENDED).toBe('suspended');
      expect(SUBSCRIPTION_STATUS.PENDING).toBe('pending');
    });

    test('should have correct billing cycle constants', () => {
      expect(BILLING_CYCLE.MONTHLY).toBe('monthly');
      expect(BILLING_CYCLE.ANNUAL).toBe('annual');
    });
  });
});
