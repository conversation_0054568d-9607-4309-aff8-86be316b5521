/**
 * Unit Tests for UsageTracker Model
 * Tests all functionality of the UsageTracker class
 *
 * @fileoverview Comprehensive unit tests for UsageTracker model
 * <AUTHOR> Development Team
 * @since 1.0.0
 */

import {
  UsageTracker,
  RESOURCE_TYPE,
  USAGE_PERIOD
} from '../../../src/core/models/UsageTracker.js';

describe('UsageTracker Model', () => {
  let testTracker, starterTracker, professionalTracker;

  beforeEach(() => {
    // Create test trackers with different configurations
    testTracker = new UsageTracker({
      userId: 'user123',
      tierId: 'professional',
      period: USAGE_PERIOD.MONTHLY,
      limits: {
        monthlyInvoices: 500,
        aiCalls: 1000,
        storageGB: 5,
        apiCalls: 0,
        activeUsers: 1,
        integrations: 3
      }
    });

    starterTracker = new UsageTracker({
      userId: 'starter_user',
      tierId: 'starter',
      limits: {
        monthlyInvoices: 10,
        aiCalls: 0,
        storageGB: 0.1,
        apiCalls: 0,
        activeUsers: 1,
        integrations: 0
      }
    });

    professionalTracker = new UsageTracker({
      userId: 'pro_user',
      tierId: 'professional',
      usage: {
        monthlyInvoices: 250,
        aiCalls: 500,
        storageGB: 2.5,
        apiCalls: 0,
        activeUsers: 1,
        integrations: 2
      },
      limits: {
        monthlyInvoices: 500,
        aiCalls: 1000,
        storageGB: 5,
        apiCalls: 0,
        activeUsers: 1,
        integrations: 3
      }
    });
  });

  describe('Constructor and Basic Properties', () => {
    test('should create tracker with default values', () => {
      const tracker = new UsageTracker();

      expect(tracker.userId).toBe('');
      expect(tracker.tierId).toBe('starter');
      expect(tracker.period).toBe(USAGE_PERIOD.MONTHLY);
      expect(tracker.usage[RESOURCE_TYPE.MONTHLY_INVOICES]).toBe(0);
      expect(tracker.limits[RESOURCE_TYPE.STORAGE_GB]).toBe(0.1);
    });

    test('should create tracker with provided data', () => {
      const data = {
        userId: 'test123',
        tierId: 'business',
        period: USAGE_PERIOD.WEEKLY,
        usage: { monthlyInvoices: 50 },
        limits: { monthlyInvoices: 100 }
      };

      const tracker = new UsageTracker(data);

      expect(tracker.userId).toBe('test123');
      expect(tracker.tierId).toBe('business');
      expect(tracker.period).toBe(USAGE_PERIOD.WEEKLY);
      expect(tracker.usage.monthlyInvoices).toBe(50);
      expect(tracker.limits.monthlyInvoices).toBe(100);
    });

    test('should set timestamps on creation', () => {
      const tracker = new UsageTracker();

      expect(tracker.createdAt).toBeDefined();
      expect(tracker.updatedAt).toBeDefined();
      expect(tracker.lastResetAt).toBeDefined();
      expect(new Date(tracker.createdAt)).toBeInstanceOf(Date);
    });
  });

  describe('Usage Increment', () => {
    test('should increment usage successfully', () => {
      const result = testTracker.incrementUsage(RESOURCE_TYPE.MONTHLY_INVOICES, 5);

      expect(result.success).toBe(true);
      expect(result.newUsage).toBe(5);
      expect(testTracker.usage[RESOURCE_TYPE.MONTHLY_INVOICES]).toBe(5);
    });

    test('should reject invalid resource type', () => {
      const result = testTracker.incrementUsage('invalid_resource', 1);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Invalid resource type');
    });

    test('should reject negative amount', () => {
      const result = testTracker.incrementUsage(RESOURCE_TYPE.MONTHLY_INVOICES, -1);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Amount must be positive');
    });

    test('should reject zero amount', () => {
      const result = testTracker.incrementUsage(RESOURCE_TYPE.MONTHLY_INVOICES, 0);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Amount must be positive');
    });

    test('should prevent exceeding limits', () => {
      // Set usage close to limit
      starterTracker.usage[RESOURCE_TYPE.MONTHLY_INVOICES] = 9;

      const result = starterTracker.incrementUsage(RESOURCE_TYPE.MONTHLY_INVOICES, 2);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Usage limit exceeded');
      expect(result.excess).toBe(1);
      expect(starterTracker.usage[RESOURCE_TYPE.MONTHLY_INVOICES]).toBe(9); // Unchanged
    });

    test('should allow increment up to exact limit', () => {
      starterTracker.usage[RESOURCE_TYPE.MONTHLY_INVOICES] = 9;

      const result = starterTracker.incrementUsage(RESOURCE_TYPE.MONTHLY_INVOICES, 1);

      expect(result.success).toBe(true);
      expect(result.newUsage).toBe(10);
      expect(starterTracker.usage[RESOURCE_TYPE.MONTHLY_INVOICES]).toBe(10);
    });

    test('should allow unlimited resources (limit = 0)', () => {
      // Enterprise tier has unlimited limits (0)
      const enterpriseTracker = new UsageTracker({
        userId: 'enterprise_user',
        tierId: 'enterprise',
        limits: {
          monthlyInvoices: 0, // Unlimited
          aiCalls: 0,
          storageGB: 0,
          apiCalls: 0,
          activeUsers: 0,
          integrations: 0
        }
      });

      const result = enterpriseTracker.incrementUsage(RESOURCE_TYPE.MONTHLY_INVOICES, 10000);

      expect(result.success).toBe(true);
      expect(result.newUsage).toBe(10000);
    });

    test('should add history entry on increment', () => {
      const initialHistoryLength = testTracker.history.length;

      testTracker.incrementUsage(RESOURCE_TYPE.AI_CALLS, 10, { source: 'test' });

      expect(testTracker.history).toHaveLength(initialHistoryLength + 1);
      const lastEntry = testTracker.history[testTracker.history.length - 1];
      expect(lastEntry.action).toBe('increment');
      expect(lastEntry.resourceType).toBe(RESOURCE_TYPE.AI_CALLS);
      expect(lastEntry.amount).toBe(10);
      expect(lastEntry.metadata.source).toBe('test');
    });
  });

  describe('Limit Checking', () => {
    test('should check limit for valid resource', () => {
      const result = professionalTracker.checkLimit(RESOURCE_TYPE.MONTHLY_INVOICES);

      expect(result.isValid).toBe(true);
      expect(result.currentUsage).toBe(250);
      expect(result.limit).toBe(500);
      expect(result.remaining).toBe(250);
      expect(result.percentage).toBe(50);
    });

    test('should check limit with additional amount', () => {
      const result = professionalTracker.checkLimit(RESOURCE_TYPE.MONTHLY_INVOICES, 300);

      expect(result.isValid).toBe(false);
      expect(result.wouldExceed).toBe(true);
      expect(result.excess).toBe(50);
    });

    test('should handle unlimited resources', () => {
      const result = professionalTracker.checkLimit(RESOURCE_TYPE.API_CALLS);

      expect(result.isValid).toBe(true);
      expect(result.limit).toBe('unlimited');
      expect(result.remaining).toBe(Infinity);
      expect(result.percentage).toBe(0);
    });

    test('should reject invalid resource type', () => {
      const result = professionalTracker.checkLimit('invalid_resource');

      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Invalid resource type');
    });
  });

  describe('All Limits Status', () => {
    test('should get status for all resource types', () => {
      const status = professionalTracker.getAllLimitsStatus();

      expect(Object.keys(status)).toHaveLength(6);
      expect(status[RESOURCE_TYPE.MONTHLY_INVOICES].isValid).toBe(true);
      expect(status[RESOURCE_TYPE.MONTHLY_INVOICES].percentage).toBe(50);
    });

    test('should detect exceeded limits', () => {
      // Set usage over limit
      starterTracker.usage[RESOURCE_TYPE.MONTHLY_INVOICES] = 15;

      expect(starterTracker.hasExceededLimits()).toBe(true);
    });

    test('should detect no exceeded limits', () => {
      expect(professionalTracker.hasExceededLimits()).toBe(false);
    });
  });

  describe('Approaching Limits', () => {
    test('should detect resources approaching limits', () => {
      // Reset all usage first to avoid interference from initial values
      professionalTracker.usage[RESOURCE_TYPE.MONTHLY_INVOICES] = 0;
      professionalTracker.usage[RESOURCE_TYPE.AI_CALLS] = 0;
      professionalTracker.usage[RESOURCE_TYPE.STORAGE_GB] = 0;
      professionalTracker.usage[RESOURCE_TYPE.INTEGRATIONS] = 0;

      // Set usage to 85% of limit for monthly invoices only
      professionalTracker.usage[RESOURCE_TYPE.MONTHLY_INVOICES] = 425; // 85% of 500

      const approaching = professionalTracker.getApproachingLimits(80);

      expect(approaching).toHaveLength(1);
      expect(approaching[0].resourceType).toBe(RESOURCE_TYPE.MONTHLY_INVOICES);
      expect(approaching[0].percentage).toBe(85);
    });

    test('should use custom threshold', () => {
      // Reset all usage first
      professionalTracker.usage[RESOURCE_TYPE.MONTHLY_INVOICES] = 0;
      professionalTracker.usage[RESOURCE_TYPE.AI_CALLS] = 0;
      professionalTracker.usage[RESOURCE_TYPE.STORAGE_GB] = 0;
      professionalTracker.usage[RESOURCE_TYPE.INTEGRATIONS] = 0;

      professionalTracker.usage[RESOURCE_TYPE.MONTHLY_INVOICES] = 450; // 90% of 500

      const approaching = professionalTracker.getApproachingLimits(95);

      expect(approaching).toHaveLength(0); // Below 95% threshold
    });

    test('should ignore unlimited resources', () => {
      professionalTracker.usage[RESOURCE_TYPE.API_CALLS] = 10000; // High usage but unlimited

      const approaching = professionalTracker.getApproachingLimits(50);

      expect(approaching.find(r => r.resourceType === RESOURCE_TYPE.API_CALLS)).toBeUndefined();
    });
  });

  describe('Period Reset', () => {
    test('should reset usage for current period', () => {
      professionalTracker.usage[RESOURCE_TYPE.MONTHLY_INVOICES] = 250;
      professionalTracker.usage[RESOURCE_TYPE.AI_CALLS] = 500;

      const result = professionalTracker.resetPeriod();

      expect(result.success).toBe(true);
      expect(professionalTracker.usage[RESOURCE_TYPE.MONTHLY_INVOICES]).toBe(0);
      expect(professionalTracker.usage[RESOURCE_TYPE.AI_CALLS]).toBe(0);
    });

    test('should preserve history by default', () => {
      const initialHistoryLength = professionalTracker.history.length;
      professionalTracker.usage[RESOURCE_TYPE.MONTHLY_INVOICES] = 250;

      professionalTracker.resetPeriod();

      expect(professionalTracker.history).toHaveLength(initialHistoryLength + 1);
      const lastEntry = professionalTracker.history[professionalTracker.history.length - 1];
      expect(lastEntry.action).toBe('period_reset');
      expect(lastEntry.previousUsage.monthlyInvoices).toBe(250);
    });

    test('should not preserve history when disabled', () => {
      const initialHistoryLength = professionalTracker.history.length;

      professionalTracker.resetPeriod({ preserveHistory: false });

      expect(professionalTracker.history).toHaveLength(initialHistoryLength);
    });

    test('should update period dates', () => {
      const originalLastResetAt = professionalTracker.lastResetAt;

      professionalTracker.resetPeriod();

      // The lastResetAt should be updated to current time
      expect(professionalTracker.lastResetAt).not.toBe(originalLastResetAt);
      expect(professionalTracker.lastResetAt).toBeDefined();
      expect(new Date(professionalTracker.lastResetAt)).toBeInstanceOf(Date);

      // Period start and end should be valid dates
      expect(professionalTracker.periodStart).toBeDefined();
      expect(professionalTracker.periodEnd).toBeDefined();
      expect(new Date(professionalTracker.periodStart)).toBeInstanceOf(Date);
      expect(new Date(professionalTracker.periodEnd)).toBeInstanceOf(Date);
    });
  });

  describe('Period Management', () => {
    test('should check if period needs reset', () => {
      // Set period end to past date
      const pastDate = new Date();
      pastDate.setMonth(pastDate.getMonth() - 1);
      professionalTracker.periodEnd = pastDate.toISOString();

      expect(professionalTracker.needsPeriodReset()).toBe(true);
    });

    test('should not need reset for current period', () => {
      expect(professionalTracker.needsPeriodReset()).toBe(false);
    });

    test('should get days until reset', () => {
      const daysUntil = professionalTracker.getDaysUntilReset();

      expect(typeof daysUntil).toBe('number');
      expect(daysUntil).toBeGreaterThanOrEqual(0);
    });

    test('should get usage summary', () => {
      const summary = professionalTracker.getUsageSummary();

      expect(summary.userId).toBe('pro_user');
      expect(summary.tierId).toBe('professional');
      expect(summary.period).toBe(USAGE_PERIOD.MONTHLY);
      expect(summary.totalResources).toBe(6);
      expect(summary.resourcesWithUsage).toBeGreaterThan(0);
    });
  });

  describe('JSON Serialization', () => {
    test('should convert to JSON correctly', () => {
      const json = professionalTracker.toJSON();

      expect(json.userId).toBe('pro_user');
      expect(json.tierId).toBe('professional');
      expect(json.usage).toBeDefined();
      expect(json.limits).toBeDefined();
      expect(json.history).toBeDefined();
      expect(json.createdAt).toBeDefined();
    });

    test('should create from JSON correctly', () => {
      const json = professionalTracker.toJSON();
      const newTracker = UsageTracker.fromJSON(json);

      expect(newTracker.userId).toBe(professionalTracker.userId);
      expect(newTracker.tierId).toBe(professionalTracker.tierId);
      expect(newTracker.usage).toEqual(professionalTracker.usage);
      expect(newTracker.limits).toEqual(professionalTracker.limits);
    });
  });

  describe('Usage Management', () => {
    test('should update tracker properties', () => {
      const originalUpdatedAt = professionalTracker.updatedAt;

      setTimeout(() => {
        professionalTracker.update({ tierId: 'business' });

        expect(professionalTracker.tierId).toBe('business');
        expect(professionalTracker.updatedAt).not.toBe(originalUpdatedAt);
        expect(professionalTracker.userId).toBe('pro_user'); // Should not change
      }, 10);
    });

    test('should clone tracker with modifications', () => {
      const cloned = professionalTracker.clone({
        userId: 'cloned123',
        tierId: 'enterprise'
      });

      expect(cloned.userId).toBe('cloned123');
      expect(cloned.tierId).toBe('enterprise');
      expect(cloned.usage).toEqual(professionalTracker.usage);
      expect(cloned).not.toBe(professionalTracker); // Different instances
    });

    test('should get tracker identifier', () => {
      expect(professionalTracker.getIdentifier()).toBe('pro_user-professional');

      const emptyTracker = new UsageTracker();
      expect(emptyTracker.getIdentifier()).toBe('-starter');
    });
  });

  describe('Edge Cases and Error Handling', () => {
    test('should handle invalid period dates', () => {
      const tracker = new UsageTracker({
        periodStart: 'invalid-date',
        periodEnd: 'invalid-date'
      });

      expect(tracker.periodStart).toBeDefined();
      expect(tracker.periodEnd).toBeDefined();
    });

    test('should handle missing usage data', () => {
      const tracker = new UsageTracker({
        usage: null
      });

      expect(tracker.usage[RESOURCE_TYPE.MONTHLY_INVOICES]).toBe(0);
    });

    test('should handle missing limits data', () => {
      const tracker = new UsageTracker({
        limits: null
      });

      expect(tracker.limits[RESOURCE_TYPE.STORAGE_GB]).toBe(0.1);
    });

    test('should handle floating point precision in storage calculations', () => {
      const tracker = new UsageTracker({
        limits: { storageGB: 0.1 }
      });

      tracker.incrementUsage(RESOURCE_TYPE.STORAGE_GB, 0.05);
      tracker.incrementUsage(RESOURCE_TYPE.STORAGE_GB, 0.05);

      const result = tracker.checkLimit(RESOURCE_TYPE.STORAGE_GB);
      expect(result.isValid).toBe(true);
      expect(result.currentUsage).toBeCloseTo(0.1, 10);
    });
  });
});
