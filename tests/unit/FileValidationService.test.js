/**
 * Unit Tests for ConsolidatedFileValidationService
 * Tests comprehensive file validation and security features
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { ConsolidatedFileValidationService } from '../../src/services/ConsolidatedFileValidationService.js';

// Mock file-type library
vi.mock('file-type', () => ({
  fileTypeFromBuffer: vi.fn()
}));

// Mock crypto-js
vi.mock('crypto-js', () => ({
  lib: {
    WordArray: {
      create: vi.fn(() => 'mock-word-array')
    }
  },
  SHA256: vi.fn(() => ({
    toString: () => 'mock-hash-value'
  }))
}));

describe('ConsolidatedFileValidationService', () => {
  let validationService;
  let mockFile;

  beforeEach(() => {
    validationService = new ConsolidatedFileValidationService();

    // Create mock file
    mockFile = {
      name: 'test.pdf',
      size: 1024 * 1024, // 1MB
      type: 'application/pdf',
      slice: vi.fn(() => new Blob()),
      constructor: File
    };

    // Make mockFile an instance of File
    Object.setPrototypeOf(mockFile, File.prototype);

    // Reset all mocks
    vi.clearAllMocks();
  });

  describe('Constructor', () => {
    it('should initialize with default configuration', () => {
      const service = new ConsolidatedFileValidationService();

      expect(service.config.maxFileSize).toBe(15 * 1024 * 1024); // Updated default
      expect(service.config.maxTotalSize).toBe(50 * 1024 * 1024);
      expect(service.config.maxFiles).toBe(10);
      expect(service.config.allowedMimeTypes).toContain('application/pdf');
      expect(service.config.allowedExtensions).toContain('.pdf');
    });

    it('should accept custom configuration', () => {
      const customConfig = {
        maxFileSize: 5 * 1024 * 1024,
        maxFiles: 5,
        allowedMimeTypes: ['application/pdf']
      };

      const service = new ConsolidatedFileValidationService(customConfig);

      expect(service.config.maxFileSize).toBe(5 * 1024 * 1024);
      expect(service.config.maxFiles).toBe(5);
      expect(service.config.allowedMimeTypes).toEqual(['application/pdf']);
    });
  });

  describe('validateFileName', () => {
    it('should validate valid file names', () => {
      const result = validationService.validateFileName('document.pdf');

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject empty file names', () => {
      const result = validationService.validateFileName('');

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('File name is required');
    });

    it('should reject file names that are too long', () => {
      const longName = 'a'.repeat(256) + '.pdf';
      const result = validationService.validateFileName(longName);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('File name is too long (maximum 255 characters)');
    });

    it('should reject file names with invalid characters', () => {
      const result = validationService.validateFileName('file<name>.pdf');

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('File name contains invalid characters');
    });

    it('should reject file names with path traversal', () => {
      const result = validationService.validateFileName('../../../etc/passwd');

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('File name contains path traversal characters');
    });

    it('should warn about reserved system names', () => {
      const result = validationService.validateFileName('CON.pdf');

      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('File name uses a reserved system name');
    });
  });

  describe('validateFileSize', () => {
    it('should validate files within size limit', () => {
      const result = validationService.validateFileSize(5 * 1024 * 1024); // 5MB

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject empty files', () => {
      const result = validationService.validateFileSize(0);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('File is empty');
    });

    it('should reject files that are too large', () => {
      const result = validationService.validateFileSize(15 * 1024 * 1024); // 15MB

      expect(result.isValid).toBe(false);
      expect(result.errors[0]).toContain('exceeds maximum allowed size');
    });
  });

  describe('validateFileExtension', () => {
    it('should validate allowed extensions', () => {
      const result = validationService.validateFileExtension('document.pdf');

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject files without extensions', () => {
      const result = validationService.validateFileExtension('document');

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('File has no extension');
    });

    it('should reject disallowed extensions', () => {
      const result = validationService.validateFileExtension('document.exe');

      expect(result.isValid).toBe(false);
      expect(result.errors[0]).toContain('is not allowed');
    });

    it('should handle case-insensitive extensions', () => {
      const result = validationService.validateFileExtension('document.PDF');

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
  });

  describe('validateMimeType', () => {
    it('should validate allowed MIME types', () => {
      const result = validationService.validateMimeType('application/pdf');

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject missing MIME types', () => {
      const result = validationService.validateMimeType('');

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('MIME type is missing');
    });

    it('should reject disallowed MIME types', () => {
      const result = validationService.validateMimeType('application/x-executable');

      expect(result.isValid).toBe(false);
      expect(result.errors[0]).toContain('is not allowed');
    });
  });

  describe('validateFile', () => {
    beforeEach(() => {
      // Mock FileReader
      global.FileReader = vi.fn(() => ({
        readAsArrayBuffer: vi.fn(),
        onload: null,
        onerror: null,
        result: new ArrayBuffer(8)
      }));

      // Mock performance.now
      global.performance = {
        now: vi.fn(() => 1000)
      };
    });

    it('should validate a valid file', async () => {
      const result = await validationService.validateFile(mockFile);

      expect(result.isValid).toBe(true);
      expect(result.fileName).toBe('test.pdf');
      expect(result.fileSize).toBe(1024 * 1024);
      expect(result.fileType).toBe('application/pdf');
      expect(result.errors).toHaveLength(0);
    });

    it('should reject invalid file objects', async () => {
      const result = await validationService.validateFile(null);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid file object');
    });

    it('should include performance metrics', async () => {
      const result = await validationService.validateFile(mockFile);

      expect(result.performance).toBeDefined();
      expect(result.performance.validationTime).toBeDefined();
      expect(result.performance.fileSize).toBe(mockFile.size);
    });

    it('should handle validation errors gracefully', async () => {
      // Create a file that will fail validation
      const invalidFile = {
        ...mockFile,
        name: '',
        size: 0,
        type: ''
      };
      Object.setPrototypeOf(invalidFile, File.prototype);

      const result = await validationService.validateFile(invalidFile);

      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('validateFiles', () => {
    let mockFiles;

    beforeEach(() => {
      mockFiles = [
        { ...mockFile, name: 'file1.pdf' },
        { ...mockFile, name: 'file2.jpg', type: 'image/jpeg' },
        { ...mockFile, name: 'file3.png', type: 'image/png' }
      ];

      mockFiles.forEach(file => {
        Object.setPrototypeOf(file, File.prototype);
      });

      // Mock FileReader
      global.FileReader = vi.fn(() => ({
        readAsArrayBuffer: vi.fn(),
        onload: null,
        onerror: null,
        result: new ArrayBuffer(8)
      }));

      global.performance = {
        now: vi.fn(() => 1000)
      };
    });

    it('should validate multiple valid files', async () => {
      const result = await validationService.validateFiles(mockFiles);

      expect(result.isValid).toBe(true);
      expect(result.totalFiles).toBe(3);
      expect(result.totalSize).toBe(3 * 1024 * 1024);
      expect(result.results).toHaveLength(3);
    });

    it('should reject too many files', async () => {
      const tooManyFiles = Array(15).fill(mockFile);
      const result = await validationService.validateFiles(tooManyFiles);

      expect(result.isValid).toBe(false);
      expect(result.errors[0]).toContain('Too many files');
    });

    it('should reject files with total size too large', async () => {
      const largeFiles = Array(60).fill({
        ...mockFile,
        size: 1024 * 1024 // 1MB each, total 60MB
      });

      const result = await validationService.validateFiles(largeFiles);

      expect(result.isValid).toBe(false);
      expect(result.errors[0]).toContain('Total file size too large');
    });

    it('should detect duplicate files', async () => {
      const duplicateFiles = [
        mockFile,
        { ...mockFile } // Same name and size
      ];

      const result = await validationService.validateFiles(duplicateFiles);

      expect(result.warnings[0]).toContain('Duplicate files detected');
    });

    it('should include performance metrics for multiple files', async () => {
      const result = await validationService.validateFiles(mockFiles);

      expect(result.performance).toBeDefined();
      expect(result.performance.totalValidationTime).toBeDefined();
      expect(result.performance.averageTimePerFile).toBeDefined();
      expect(result.performance.totalThroughput).toBeDefined();
    });
  });

  describe('findDuplicateFiles', () => {
    it('should find duplicate files by name and size', () => {
      const files = [
        { name: 'file1.pdf', size: 1024 },
        { name: 'file2.pdf', size: 2048 },
        { name: 'file1.pdf', size: 1024 }, // Duplicate
        { name: 'file3.pdf', size: 1024 } // Different name, same size
      ];

      const duplicates = validationService.findDuplicateFiles(files);

      expect(duplicates).toContain('file1.pdf');
      expect(duplicates).not.toContain('file3.pdf');
    });

    it('should return empty array when no duplicates', () => {
      const files = [
        { name: 'file1.pdf', size: 1024 },
        { name: 'file2.pdf', size: 2048 },
        { name: 'file3.pdf', size: 4096 }
      ];

      const duplicates = validationService.findDuplicateFiles(files);

      expect(duplicates).toHaveLength(0);
    });
  });

  describe('getFileExtension', () => {
    it('should extract file extension correctly', () => {
      expect(validationService.getFileExtension('document.pdf')).toBe('.pdf');
      expect(validationService.getFileExtension('image.jpeg')).toBe('.jpeg');
      expect(validationService.getFileExtension('file.name.with.dots.png')).toBe('.png');
    });

    it('should return empty string for files without extension', () => {
      expect(validationService.getFileExtension('document')).toBe('');
      expect(validationService.getFileExtension('')).toBe('');
    });
  });

  describe('formatFileSize', () => {
    it('should format file sizes correctly', () => {
      expect(validationService.formatFileSize(0)).toBe('0 Bytes');
      expect(validationService.formatFileSize(1024)).toBe('1 KB');
      expect(validationService.formatFileSize(1024 * 1024)).toBe('1 MB');
      expect(validationService.formatFileSize(1024 * 1024 * 1024)).toBe('1 GB');
    });

    it('should handle decimal places', () => {
      expect(validationService.formatFileSize(1536)).toBe('1.5 KB');
      expect(validationService.formatFileSize(1024 * 1024 * 1.5)).toBe('1.5 MB');
    });
  });

  describe('Error Handling', () => {
    it('should handle FileReader errors gracefully', async () => {
      // Mock FileReader to throw error
      global.FileReader = vi.fn(() => ({
        readAsArrayBuffer: vi.fn(() => {
          throw new Error('FileReader error');
        }),
        onload: null,
        onerror: null
      }));

      const result = await validationService.validateFile(mockFile);

      // Should still return a result, but may have warnings
      expect(result).toBeDefined();
      expect(result.fileName).toBe(mockFile.name);
    });

    it('should handle missing file properties', async () => {
      const incompleteFile = {
        name: 'test.pdf'
        // Missing size, type, etc.
      };
      Object.setPrototypeOf(incompleteFile, File.prototype);

      const result = await validationService.validateFile(incompleteFile);

      expect(result).toBeDefined();
      expect(result.isValid).toBe(false);
    });
  });
});
