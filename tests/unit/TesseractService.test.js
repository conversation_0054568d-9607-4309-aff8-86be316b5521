import { describe, test, expect, beforeEach, afterEach } from 'vitest';
import TesseractService from '../../src/services/TesseractService';
import { mockOcrResult, setupTestMocks, clearTestMocks } from './testHelpers';

/**
 * @jest-environment jsdom
 */
describe('TesseractService', () => {
  let tesseractService;

  beforeEach(() => {
    setupTestMocks();
    tesseractService = new TesseractService();
  });

  afterEach(async () => {
    await tesseractService.terminate();
    clearTestMocks();
  });

  describe('initialize', () => {
    test('should initialize workers correctly', async () => {
      await tesseractService.initialize(2);
      expect(tesseractService.initialized).toBe(true);
      expect(tesseractService.workers.length).toBe(2);
    });

    test('should not reinitialize if already initialized', async () => {
      await tesseractService.initialize();
      const initialWorkers = tesseractService.workers.length;
      await tesseractService.initialize();
      expect(tesseractService.workers.length).toBe(initialWorkers);
    });

    test('should handle initialization errors', async () => {
      const mockError = new Error('Worker initialization failed');
      jest.spyOn(tesseractService, 'initialize').mockRejectedValue(mockError);
      await expect(tesseractService.initialize()).rejects.toThrow('Worker initialization failed');
    });
  });

  describe('extractText', () => {
    test('should extract text from image', async () => {
      const mockFile = new Blob(['test image content'], { type: 'image/png' });
      const result = await tesseractService.extractText(mockFile);

      expect(result).toHaveProperty('text');
      expect(result).toHaveProperty('words');
      expect(result).toHaveProperty('lines');
      expect(result).toHaveProperty('confidence');
      expect(result).toHaveProperty('language');
    });

    test('should initialize if not already initialized', async () => {
      const mockFile = new Blob(['test image content'], { type: 'image/png' });
      const initSpy = jest.spyOn(tesseractService, 'initialize');

      await tesseractService.extractText(mockFile);
      expect(initSpy).toHaveBeenCalled();
    });

    test('should handle processing errors', async () => {
      const mockFile = new Blob(['invalid image'], { type: 'image/png' });
      jest.spyOn(tesseractService.scheduler, 'addJob').mockRejectedValue(new Error('Processing failed'));

      await expect(tesseractService.extractText(mockFile)).rejects.toThrow('Processing failed');
    });
  });

  describe('processOcrResult', () => {
    test('should process OCR result correctly', () => {
      const result = tesseractService.processOcrResult({
        data: mockOcrResult
      });

      expect(result).toHaveProperty('text');
      expect(result).toHaveProperty('words');
      expect(result).toHaveProperty('lines');
      expect(result).toHaveProperty('confidence');
      expect(result).toHaveProperty('language');
      expect(result).toHaveProperty('orientation');
      expect(result).toHaveProperty('timestamp');
    });

    test('should handle empty OCR results', () => {
      const emptyResult = {
        data: {
          text: '',
          words: [],
          lines: [],
          confidence: 0
        }
      };

      const result = tesseractService.processOcrResult(emptyResult);
      expect(result.text).toBe('');
      expect(result.words).toHaveLength(0);
      expect(result.lines).toHaveLength(0);
      expect(result.confidence).toBe(0);
    });
  });

  describe('setLanguages', () => {
    test('should set single language', async () => {
      await tesseractService.setLanguages('eng');
      const supportedLangs = tesseractService.getSupportedLanguages();
      expect(supportedLangs).toContain('eng');
    });

    test('should set multiple languages', async () => {
      await tesseractService.setLanguages(['eng', 'pol']);
      const supportedLangs = tesseractService.getSupportedLanguages();
      expect(supportedLangs).toContain('eng');
      expect(supportedLangs).toContain('pol');
    });

    test('should initialize if not already initialized', async () => {
      const initSpy = jest.spyOn(tesseractService, 'initialize');
      await tesseractService.setLanguages('eng');
      expect(initSpy).toHaveBeenCalled();
    });
  });

  describe('terminate', () => {
    test('should terminate all workers', async () => {
      await tesseractService.initialize(2);
      await tesseractService.terminate();

      expect(tesseractService.workers).toHaveLength(0);
      expect(tesseractService.initialized).toBe(false);
    });

    test('should handle termination errors', async () => {
      await tesseractService.initialize();
      jest.spyOn(tesseractService.scheduler, 'terminate').mockRejectedValue(new Error('Termination failed'));

      await expect(tesseractService.terminate()).rejects.toThrow('Termination failed');
    });
  });
});
