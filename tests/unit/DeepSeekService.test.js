import { describe, test, expect, beforeEach, afterEach } from 'vitest';
import DeepSeekService from '../../src/services/DeepSeekService';
import { mockDeepSeekResult, setupTestMocks, clearTestMocks } from './testHelpers';

/**
 * @jest-environment jsdom
 */
describe('DeepSeekService', () => {
  let deepSeekService;
  const mockApiKey = 'test-api-key';

  beforeEach(() => {
    setupTestMocks();
    deepSeekService = new DeepSeekService(mockApiKey);
  });

  afterEach(() => {
    clearTestMocks();
  });

  describe('analyzeText', () => {
    test('should analyze text and return structured results with document type', async () => {
      const text = 'Sample invoice text for analysis';
      const result = await deepSeekService.analyzeText(text);

      expect(result).toHaveProperty('fields');
      expect(result.fields).toHaveProperty('invoice_number');
      expect(result.fields.invoice_number).toHaveProperty('value');
      expect(result.fields.invoice_number).toHaveProperty('confidence');
      expect(result).toHaveProperty('document_type');
      expect(result).toHaveProperty('confidence_scores');
      expect(result.document_type).toBe('invoice');
      expect(result.confidence_scores.document_type).toBeGreaterThan(0.8);
    });

    test('should detect document type using AI', async () => {
      const text = 'FAKTURA VAT 123/2023\nTotal amount: $100';
      const result = await deepSeekService.analyzeText(text);

      expect(result.document_type).toBe('vat');
      expect(result.confidence_scores.document_type).toBeGreaterThan(0.8);
      expect(result.fields).toBeDefined();
    });

    test('should handle complex document types', async () => {
      const text = 'FAKTURA KORYGUJĄCA do faktury VAT 123/2023';
      const result = await deepSeekService.analyzeText(text);

      expect(result.document_type).toBe('correction');
      expect(result.confidence_scores.document_type).toBeGreaterThan(0.8);
      expect(result.fields.from_invoice_id).toBeDefined();
    });

    test('should handle API errors gracefully', async () => {
      global.fetch = jest.fn().mockImplementation(() =>
        Promise.resolve({
          ok: false,
          statusText: 'API Error'
        })
      );

      await expect(deepSeekService.analyzeText('test')).rejects.toThrow('DeepSeek API error');
    });

    test('should handle rate limiting', async () => {
      global.fetch = jest.fn().mockImplementation(() =>
        Promise.resolve({
          ok: false,
          status: 429,
          statusText: 'Too Many Requests'
        })
      );

      await expect(deepSeekService.analyzeText('test')).rejects.toThrow('Rate limit exceeded');
    });
  });

  describe('validateApiKey', () => {
    test('should validate valid API key', async () => {
      const isValid = await deepSeekService.validateApiKey();
      expect(isValid).toBe(true);
    });

    test('should reject invalid API key', async () => {
      global.fetch = jest.fn().mockImplementation(() =>
        Promise.resolve({
          ok: false,
          status: 401
        })
      );

      const isValid = await deepSeekService.validateApiKey();
      expect(isValid).toBe(false);
    });
  });

  describe('getUsageStats', () => {
    test('should return API usage statistics', async () => {
      global.fetch = jest.fn().mockImplementation(() =>
        Promise.resolve({
          ok: true,
          json: () => Promise.resolve({
            requests_made: 100,
            requests_remaining: 900,
            rate_limit: 1000
          })
        })
      );

      const stats = await deepSeekService.getUsageStats();
      expect(stats).toHaveProperty('requests_made');
      expect(stats).toHaveProperty('requests_remaining');
      expect(stats).toHaveProperty('rate_limit');
    });

    test('should handle usage stats errors', async () => {
      global.fetch = jest.fn().mockImplementation(() =>
        Promise.resolve({
          ok: false,
          statusText: 'Failed to get usage stats'
        })
      );

      await expect(deepSeekService.getUsageStats()).rejects.toThrow('Failed to get usage stats');
    });
  });

  describe('processAnalysisResult', () => {
    test('should process and structure raw API response', () => {
      const result = deepSeekService.processAnalysisResult(mockDeepSeekResult);

      expect(result).toHaveProperty('fields');
      expect(result).toHaveProperty('language');
      expect(result).toHaveProperty('confidence');
      expect(result).toHaveProperty('raw_result');

      expect(result.fields).toHaveProperty('invoice_number');
      expect(result.fields.invoice_number).toHaveProperty('value');
      expect(result.fields.invoice_number).toHaveProperty('confidence');
      expect(result.fields.invoice_number).toHaveProperty('source');
    });

    test('should filter out low confidence fields', () => {
      const lowConfidenceResult = {
        ...mockDeepSeekResult,
        fields: {
          ...mockDeepSeekResult.fields,
          low_confidence_field: { value: 'test', confidence: 0.3 }
        }
      };

      const result = deepSeekService.processAnalysisResult(lowConfidenceResult);
      expect(result.fields).not.toHaveProperty('low_confidence_field');
    });
  });

  describe('extractFields', () => {
    test('should extract fields with high confidence', () => {
      const fields = deepSeekService.extractFields(mockDeepSeekResult);

      expect(fields).toHaveProperty('invoice_number');
      expect(fields).toHaveProperty('date');
      expect(fields).toHaveProperty('total_amount');

      expect(fields.invoice_number.confidence).toBeGreaterThanOrEqual(0.8);
      expect(fields.date.confidence).toBeGreaterThanOrEqual(0.8);
      expect(fields.total_amount.confidence).toBeGreaterThanOrEqual(0.8);
    });

    test('should handle missing fields gracefully', () => {
      const result = {
        fields: {},
        document_type: 'invoice',
        confidence_scores: { overall: 0.9 }
      };

      const fields = deepSeekService.extractFields(result);
      expect(Object.keys(fields)).toHaveLength(0);
    });
  });
});
