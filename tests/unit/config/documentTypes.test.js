import { describe, test, expect, vi, beforeEach } from 'vitest';
import {
  mapToFakturowniaDocumentType,
  documentTypeHasPositions,
  getRequiredFieldsForDocumentType,
  isValidDocumentType,
  getDocumentTypeCategory,
  getDocumentTypeDescription,
  guessDocumentTypeWithPatterns,
  guessDocumentTypeWithAI,
  guessDocumentType,
  VALID_DOCUMENT_TYPES,
  DOCUMENT_TYPE_CATEGORIES,
  DOCUMENT_TYPE_PATTERNS
} from '../../../src/core/config/documentTypes';

describe('Document Types Configuration', () => {
  describe('Document Type Mapping', () => {
    test('should map exact document types correctly', () => {
      expect(mapToFakturowniaDocumentType('vat')).toBe('vat');
      expect(mapToFakturowniaDocumentType('proforma')).toBe('proforma');
      expect(mapToFakturowniaDocumentType('bill')).toBe('bill');
    });

    test('should map invoice with content patterns', () => {
      expect(mapToFakturowniaDocumentType('invoice', 'proforma invoice')).toBe('proforma');
      expect(mapToFakturowniaDocumentType('invoice', 'faktura zaliczkowa')).toBe('advance');
      expect(mapToFakturowniaDocumentType('invoice', 'regular content')).toBe('vat');
    });

    test('should handle unknown types with fallback', () => {
      expect(mapToFakturowniaDocumentType('unknown')).toBe('vat');
      expect(mapToFakturowniaDocumentType('')).toBe('vat');
    });
  });

  describe('Document Type Validation', () => {
    test('should validate document types', () => {
      expect(isValidDocumentType('vat')).toBe(true);
      expect(isValidDocumentType('proforma')).toBe(true);
      expect(isValidDocumentType('unknown')).toBe(false);
    });

    test('should handle case-insensitive validation', () => {
      expect(isValidDocumentType('VAT')).toBe(true);
      expect(isValidDocumentType('Proforma')).toBe(true);
    });
  });

  describe('Document Type Features', () => {
    test('should identify documents with positions', () => {
      expect(documentTypeHasPositions('vat')).toBe(true);
      expect(documentTypeHasPositions('proforma')).toBe(true);
      expect(documentTypeHasPositions('correction_note')).toBe(false);
    });

    test('should return required fields for document types', () => {
      expect(getRequiredFieldsForDocumentType('correction')).toContain('invoice_id');
      expect(getRequiredFieldsForDocumentType('wnt')).toContain('reverse_charge');
      expect(getRequiredFieldsForDocumentType('vat')).toEqual([]);
    });

    test('should categorize document types', () => {
      expect(getDocumentTypeCategory('vat')).toBe('invoices');
      expect(getDocumentTypeCategory('receipt')).toBe('receipts');
      expect(getDocumentTypeCategory('correction_note')).toBe('notes');
    });

    test('should provide document type descriptions', () => {
      expect(getDocumentTypeDescription('vat')).toBe('faktura VAT');
      expect(getDocumentTypeDescription('proforma')).toBe('faktura Proforma');
      expect(getDocumentTypeDescription('unknown')).toBe('unknown');
    });
  });

  describe('Pattern-based Document Type Detection', () => {
    test('should detect invoice types from content', () => {
      expect(guessDocumentTypeWithPatterns('Invoice for services VAT')).toBe('invoice');
      expect(guessDocumentTypeWithPatterns('Contract between parties')).toBe('contract');
      expect(guessDocumentTypeWithPatterns('Financial Report 2023')).toBe('financial_report');
    });

    test('should require multiple patterns for complex documents', () => {
      expect(guessDocumentTypeWithPatterns('Payment Invoice VAT')).toBe('invoice');
      expect(guessDocumentTypeWithPatterns('Payment only')).toBe('business_document');
    });

    test('should handle empty or invalid content', () => {
      expect(guessDocumentTypeWithPatterns('')).toBe('business_document');
      expect(guessDocumentTypeWithPatterns('random text')).toBe('business_document');
    });
  });

  describe('AI-based Document Type Detection', () => {
    const mockDeepSeekAPI = {
      callAPI: vi.fn()
    };

    beforeEach(() => {
      vi.clearAllMocks();
    });

    test('should use AI for document type detection when available', async () => {
      mockDeepSeekAPI.callAPI.mockResolvedValue({
        success: true,
        content: 'invoice'
      });

      const result = await guessDocumentTypeWithAI(
        'Invoice content',
        'test-api-key',
        mockDeepSeekAPI
      );

      expect(result).toBe('invoice');
      expect(mockDeepSeekAPI.callAPI).toHaveBeenCalled();
    });

    test('should fall back to pattern matching when AI fails', async () => {
      mockDeepSeekAPI.callAPI.mockResolvedValue({
        success: false,
        error: 'API error'
      });

      const result = await guessDocumentTypeWithAI(
        'Invoice for services VAT',
        'test-api-key',
        mockDeepSeekAPI
      );

      expect(result).toBe('invoice');
    });

    test('should fall back to pattern matching without API key', async () => {
      const result = await guessDocumentTypeWithAI(
        'Invoice content',
        null,
        mockDeepSeekAPI
      );

      expect(result).toBe('business_document');
      expect(mockDeepSeekAPI.callAPI).not.toHaveBeenCalled();
    });
  });

  describe('Main Document Type Detection', () => {
    const mockDeepSeekAPI = {
      callAPI: vi.fn()
    };

    beforeEach(() => {
      vi.clearAllMocks();
    });

    test('should use AI detection when enabled and available', async () => {
      mockDeepSeekAPI.callAPI.mockResolvedValue({
        success: true,
        content: 'invoice'
      });

      const result = await guessDocumentType('Invoice content', {
        apiKey: 'test-api-key',
        deepSeekAPI: mockDeepSeekAPI,
        useAI: true
      });

      expect(result).toBe('invoice');
      expect(mockDeepSeekAPI.callAPI).toHaveBeenCalled();
    });

    test('should use pattern matching when AI is disabled', async () => {
      const result = await guessDocumentType('Invoice for services VAT', {
        apiKey: 'test-api-key',
        deepSeekAPI: mockDeepSeekAPI,
        useAI: false
      });

      expect(result).toBe('invoice');
      expect(mockDeepSeekAPI.callAPI).not.toHaveBeenCalled();
    });

    test('should handle errors gracefully', async () => {
      mockDeepSeekAPI.callAPI.mockRejectedValue(new Error('API error'));

      const result = await guessDocumentType('Invoice content', {
        apiKey: 'test-api-key',
        deepSeekAPI: mockDeepSeekAPI,
        useAI: true
      });

      expect(result).toBe('business_document');
    });
  });
});
