import { describe, test, expect, beforeEach, afterEach } from 'vitest';
import PDFService from '../../src/services/PDFService';
import {
  createMockPdfFile,
  mockPdfResult,
  setupTestMocks,
  clearTestMocks
} from './testHelpers';

/**
 * @jest-environment jsdom
 */
describe('PDFService', () => {
  let pdfService;
  let mockPdfFile;

  beforeEach(() => {
    setupTestMocks();
    pdfService = new PDFService();
    mockPdfFile = createMockPdfFile();
  });

  afterEach(() => {
    clearTestMocks();
  });

  describe('extractText', () => {
    test('should extract text from PDF file', async () => {
      const result = await pdfService.extractText(mockPdfFile);
      expect(result).toHaveProperty('text');
      expect(result).toHaveProperty('pages');
      expect(result).toHaveProperty('metadata');
    });

    test('should handle empty PDF files', async () => {
      const emptyPdf = new Blob(['%PDF-1.4'], { type: 'application/pdf' });
      const result = await pdfService.extractText(emptyPdf);
      expect(result.text).toBe('');
      expect(result.pages).toBe(0);
    });

    test('should throw error for invalid PDF files', async () => {
      const invalidPdf = new Blob(['not a pdf'], { type: 'application/pdf' });
      await expect(pdfService.extractText(invalidPdf)).rejects.toThrow();
    });
  });

  describe('validatePDF', () => {
    test('should validate valid PDF files', async () => {
      const isValid = await pdfService.validatePDF(mockPdfFile);
      expect(isValid).toBe(true);
    });

    test('should reject non-PDF files', async () => {
      const textFile = new Blob(['text content'], { type: 'text/plain' });
      const isValid = await pdfService.validatePDF(textFile);
      expect(isValid).toBe(false);
    });

    test('should reject corrupted PDF files', async () => {
      const corruptedPdf = new Blob(['corrupted content'], { type: 'application/pdf' });
      const isValid = await pdfService.validatePDF(corruptedPdf);
      expect(isValid).toBe(false);
    });
  });

  describe('getMetadata', () => {
    test('should extract PDF metadata', async () => {
      const metadata = await pdfService.getMetadata(mockPdfFile);
      expect(metadata).toBeDefined();
      expect(metadata.info).toBeDefined();
    });

    test('should handle PDFs without metadata', async () => {
      const noMetadataPdf = new Blob(['%PDF-1.4 minimal'], { type: 'application/pdf' });
      const metadata = await pdfService.getMetadata(noMetadataPdf);
      expect(metadata.info).toEqual({});
    });
  });

  describe('extractPageText', () => {
    test('should extract text from specific page', async () => {
      const text = await pdfService.extractPageText(mockPdfFile, 1);
      expect(typeof text).toBe('string');
    });

    test('should throw error for invalid page number', async () => {
      await expect(pdfService.extractPageText(mockPdfFile, 0)).rejects.toThrow('Invalid page number');
      await expect(pdfService.extractPageText(mockPdfFile, 999)).rejects.toThrow('Invalid page number');
    });
  });

  describe('getStructureInfo', () => {
    test('should return document structure information', async () => {
      const info = await pdfService.getStructureInfo(mockPdfFile);
      expect(info).toHaveProperty('pages');
      expect(info).toHaveProperty('hasOutline');
      expect(info).toHaveProperty('metadata');
      expect(info).toHaveProperty('isTagged');
      expect(info).toHaveProperty('isEncrypted');
    });

    test('should handle PDFs without structure information', async () => {
      const basicPdf = new Blob(['%PDF-1.4 basic'], { type: 'application/pdf' });
      const info = await pdfService.getStructureInfo(basicPdf);
      expect(info.hasOutline).toBe(false);
      expect(info.isTagged).toBe(false);
      expect(info.isEncrypted).toBe(false);
    });
  });
});
