/**
 * App Component Test Suite
 * Tests for the main React App component
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import App from '@/popup/App.jsx';

describe('App Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render loading state initially', () => {
    render(
      <MemoryRouter>
        <App />
      </MemoryRouter>
    );

    // The app should show loading state initially
    expect(screen.getByText('Loading MVAT...')).toBeInTheDocument();
  });

  it('should have proper loading spinner', () => {
    const { container } = render(
      <MemoryRouter>
        <App />
      </MemoryRouter>
    );

    // Check for loading spinner
    const spinner = container.querySelector('.animate-spin');
    expect(spinner).toBeInTheDocument();
  });

  it('should have popup container class', () => {
    const { container } = render(
      <MemoryRouter>
        <App />
      </MemoryRouter>
    );

    // Check for popup container
    const popupContainer = container.querySelector('.popup-container');
    expect(popupContainer).toBeInTheDocument();
  });

  it('should render in memory router without errors', () => {
    expect(() => {
      render(
        <MemoryRouter>
          <App />
        </MemoryRouter>
      );
    }).not.toThrow();
  });

  it('should handle different initial routes', () => {
    // Test with different initial routes - all should show loading
    const routes = ['/', '/upload', '/table', '/settings'];

    routes.forEach(route => {
      const { unmount } = render(
        <MemoryRouter initialEntries={[route]}>
          <App />
        </MemoryRouter>
      );

      expect(screen.getByText('Loading MVAT...')).toBeInTheDocument();
      unmount();
    });
  });
});
