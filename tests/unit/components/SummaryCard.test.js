/**
 * SummaryCard Component Unit Tests
 * Tests the enhanced summary card components and trend indicators
 *
 * ASSIGNMENT-030: Summary Cards and Visual Indicators
 * Epic: EPIC-003 - Data Display & Visualization
 * Story: STORY-3.2 - Grouping & Aggregation
 * Task: TASK-3.2.2 - Summary Views
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';

// Mock the components since we're testing in Node.js environment
const mockSummaryCard = {
  SummaryCard: ({ group, isExpanded, onToggleExpand, showTrends }) => {
    return (
      <div data-testid="summary-card">
        <div
          data-testid="card-header"
          onClick={onToggleExpand}
          role="button"
        >
          <h3>{group.metadata.displayName}</h3>
          <span>{isExpanded ? '▼' : '▶'}</span>
        </div>
        {isExpanded && (
          <div data-testid="card-content">
            {showTrends && <div data-testid="trend-indicators">Trends</div>}
            <div data-testid="financial-metrics">Metrics</div>
          </div>
        )}
      </div>
    );
  },
  TrendIndicator: ({ trend, percentage, comparison }) => {
    if (!trend) { return null; }
    return (
      <div data-testid="trend-indicator">
        <span data-testid="trend-arrow">{trend === 'up' ? '↗' : '↘'}</span>
        <span data-testid="trend-percentage">{percentage}%</span>
        <span data-testid="trend-comparison">{comparison}</span>
      </div>
    );
  },
  TrendCard: ({ title, value, trend, percentage }) => {
    return (
      <div data-testid="trend-card">
        <h4>{title}</h4>
        <div data-testid="card-value">{value}</div>
        {trend && (
          <div data-testid="card-trend">
            {trend} {percentage}%
          </div>
        )}
      </div>
    );
  }
};

// Mock data for testing
const mockGroup = {
  key: '2024-Q4',
  metadata: {
    groupKey: '2024-Q4',
    groupBy: 'quarter',
    displayName: 'Q4 2024',
    sortOrder: 202404
  },
  aggregations: {
    PLN: {
      total_gross: { sum: 15000, average: 750, min: 200, max: 2000, median: 650 },
      total_net: { sum: 12195, average: 609.75 },
      total_vat: { sum: 2805, average: 140.25 },
      count: 20
    }
  },
  statistics: {
    invoiceCount: 20,
    daySpan: 92,
    currencies: ['PLN']
  },
  invoices: [
    {
      id: 'inv-1',
      number: 'INV/2024/001',
      total_gross: 750,
      currency: 'PLN',
      seller_name: 'Test Seller'
    }
  ]
};

const mockAllGroups = [
  mockGroup,
  {
    key: '2024-Q3',
    metadata: {
      groupKey: '2024-Q3',
      groupBy: 'quarter',
      displayName: 'Q3 2024',
      sortOrder: 202403
    },
    aggregations: {
      PLN: {
        total_gross: { sum: 12500, average: 625 },
        count: 20
      }
    }
  }
];

describe('SummaryCard Component', () => {
  let mockOnToggleExpand;
  let mockOnInvoiceSelect;

  beforeEach(() => {
    mockOnToggleExpand = vi.fn();
    mockOnInvoiceSelect = vi.fn();
  });

  describe('Basic Rendering', () => {
    it('should render summary card with group data', () => {
      const { SummaryCard } = mockSummaryCard;

      render(
        <SummaryCard
          group={mockGroup}
          isExpanded={false}
          onToggleExpand={mockOnToggleExpand}
        />
      );

      expect(screen.getByTestId('summary-card')).toBeInTheDocument();
      expect(screen.getByTestId('card-header')).toBeInTheDocument();
      expect(screen.getByText('Q4 2024')).toBeInTheDocument();
      expect(screen.getByText('▶')).toBeInTheDocument();
    });

    it('should handle null group gracefully', () => {
      const { SummaryCard } = mockSummaryCard;

      const { container } = render(
        <SummaryCard
          group={null}
          isExpanded={false}
          onToggleExpand={mockOnToggleExpand}
        />
      );

      expect(container.firstChild).toBeNull();
    });

    it('should show expanded content when isExpanded is true', () => {
      const { SummaryCard } = mockSummaryCard;

      render(
        <SummaryCard
          group={mockGroup}
          isExpanded={true}
          onToggleExpand={mockOnToggleExpand}
          showTrends={true}
        />
      );

      expect(screen.getByTestId('card-content')).toBeInTheDocument();
      expect(screen.getByTestId('trend-indicators')).toBeInTheDocument();
      expect(screen.getByTestId('financial-metrics')).toBeInTheDocument();
      expect(screen.getByText('▼')).toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    it('should call onToggleExpand when header is clicked', () => {
      const { SummaryCard } = mockSummaryCard;

      render(
        <SummaryCard
          group={mockGroup}
          isExpanded={false}
          onToggleExpand={mockOnToggleExpand}
        />
      );

      fireEvent.click(screen.getByTestId('card-header'));
      expect(mockOnToggleExpand).toHaveBeenCalledTimes(1);
    });

    it('should handle keyboard navigation', () => {
      const { SummaryCard } = mockSummaryCard;

      render(
        <SummaryCard
          group={mockGroup}
          isExpanded={false}
          onToggleExpand={mockOnToggleExpand}
        />
      );

      const header = screen.getByTestId('card-header');
      fireEvent.keyDown(header, { key: 'Enter' });
      expect(mockOnToggleExpand).toHaveBeenCalledTimes(1);
    });
  });

  describe('Trend Display', () => {
    it('should show trends when showTrends is true and previous data exists', () => {
      const { SummaryCard } = mockSummaryCard;

      render(
        <SummaryCard
          group={mockGroup}
          allGroups={mockAllGroups}
          isExpanded={true}
          onToggleExpand={mockOnToggleExpand}
          showTrends={true}
        />
      );

      expect(screen.getByTestId('trend-indicators')).toBeInTheDocument();
    });

    it('should hide trends when showTrends is false', () => {
      const { SummaryCard } = mockSummaryCard;

      render(
        <SummaryCard
          group={mockGroup}
          allGroups={mockAllGroups}
          isExpanded={true}
          onToggleExpand={mockOnToggleExpand}
          showTrends={false}
        />
      );

      expect(screen.queryByTestId('trend-indicators')).not.toBeInTheDocument();
    });
  });
});

describe('TrendIndicator Component', () => {
  describe('Trend Direction', () => {
    it('should show up arrow for positive trends', () => {
      const { TrendIndicator } = mockSummaryCard;

      render(
        <TrendIndicator
          trend="up"
          percentage={15.5}
          comparison="vs Q3 2024"
        />
      );

      expect(screen.getByTestId('trend-arrow')).toHaveTextContent('↗');
      expect(screen.getByTestId('trend-percentage')).toHaveTextContent('15.5%');
      expect(screen.getByTestId('trend-comparison')).toHaveTextContent('vs Q3 2024');
    });

    it('should show down arrow for negative trends', () => {
      const { TrendIndicator } = mockSummaryCard;

      render(
        <TrendIndicator
          trend="down"
          percentage={8.2}
          comparison="vs Q3 2024"
        />
      );

      expect(screen.getByTestId('trend-arrow')).toHaveTextContent('↘');
      expect(screen.getByTestId('trend-percentage')).toHaveTextContent('8.2%');
    });

    it('should not render when trend is null', () => {
      const { TrendIndicator } = mockSummaryCard;

      const { container } = render(
        <TrendIndicator
          trend={null}
          percentage={0}
          comparison=""
        />
      );

      expect(container.firstChild).toBeNull();
    });
  });
});

describe('TrendCard Component', () => {
  describe('Card Content', () => {
    it('should display title and value', () => {
      const { TrendCard } = mockSummaryCard;

      render(
        <TrendCard
          title="Total Gross"
          value={15000}
          trend="up"
          percentage={20}
        />
      );

      expect(screen.getByText('Total Gross')).toBeInTheDocument();
      expect(screen.getByTestId('card-value')).toHaveTextContent('15000');
      expect(screen.getByTestId('card-trend')).toHaveTextContent('up 20%');
    });

    it('should handle missing trend data', () => {
      const { TrendCard } = mockSummaryCard;

      render(
        <TrendCard
          title="Total Net"
          value={12000}
          trend={null}
          percentage={0}
        />
      );

      expect(screen.getByText('Total Net')).toBeInTheDocument();
      expect(screen.getByTestId('card-value')).toHaveTextContent('12000');
      expect(screen.queryByTestId('card-trend')).not.toBeInTheDocument();
    });
  });
});

describe('Accessibility', () => {
  it('should have proper ARIA attributes', () => {
    const { SummaryCard } = mockSummaryCard;

    render(
      <SummaryCard
        group={mockGroup}
        isExpanded={false}
        onToggleExpand={mockOnToggleExpand}
      />
    );

    const header = screen.getByTestId('card-header');
    expect(header).toHaveAttribute('role', 'button');
  });

  it('should support keyboard navigation', () => {
    const { SummaryCard } = mockSummaryCard;

    render(
      <SummaryCard
        group={mockGroup}
        isExpanded={false}
        onToggleExpand={mockOnToggleExpand}
      />
    );

    const header = screen.getByTestId('card-header');

    // Test Enter key
    fireEvent.keyDown(header, { key: 'Enter' });
    expect(mockOnToggleExpand).toHaveBeenCalledTimes(1);

    // Test Space key
    fireEvent.keyDown(header, { key: ' ' });
    expect(mockOnToggleExpand).toHaveBeenCalledTimes(2);
  });
});

describe('Performance', () => {
  it('should handle large datasets efficiently', () => {
    const largeGroup = {
      ...mockGroup,
      invoices: Array.from({ length: 1000 }, (_, i) => ({
        id: `inv-${i}`,
        number: `INV/2024/${i}`,
        total_gross: 750 + i,
        currency: 'PLN'
      }))
    };

    const { SummaryCard } = mockSummaryCard;

    const startTime = performance.now();

    render(
      <SummaryCard
        group={largeGroup}
        isExpanded={true}
        onToggleExpand={mockOnToggleExpand}
      />
    );

    const endTime = performance.now();
    const renderTime = endTime - startTime;

    // Should render within 100ms even with large datasets
    expect(renderTime).toBeLessThan(100);
  });
});

describe('Error Handling', () => {
  it('should handle malformed group data', () => {
    const malformedGroup = {
      key: '2024-Q4',
      metadata: null,
      aggregations: undefined,
      statistics: {},
      invoices: null
    };

    const { SummaryCard } = mockSummaryCard;

    expect(() => {
      render(
        <SummaryCard
          group={malformedGroup}
          isExpanded={false}
          onToggleExpand={mockOnToggleExpand}
        />
      );
    }).not.toThrow();
  });

  it('should handle missing callback functions', () => {
    const { SummaryCard } = mockSummaryCard;

    expect(() => {
      render(
        <SummaryCard
          group={mockGroup}
          isExpanded={false}
          onToggleExpand={null}
          onInvoiceSelect={undefined}
        />
      );
    }).not.toThrow();
  });
});
