import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import TableHeader, { defaultInvoiceColumns, compactInvoiceColumns } from '../../../../src/popup/components/tables/TableHeader.jsx';

describe('TableHeader Component', () => {
  const mockColumns = [
    {
      key: 'filename',
      label: 'File Name',
      icon: '📄',
      sortable: true,
      width: '200px'
    },
    {
      key: 'date',
      label: 'Date',
      icon: '📅',
      sortable: true,
      width: '100px'
    },
    {
      key: 'actions',
      label: 'Actions',
      icon: '⚙️',
      sortable: false,
      width: '100px'
    }
  ];

  const defaultProps = {
    columns: mockColumns,
    sortField: 'filename',
    sortDirection: 'asc',
    onSort: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render all column headers', () => {
      render(
        <table>
          <TableHeader {...defaultProps} />
        </table>
      );

      expect(screen.getByText('File Name')).toBeInTheDocument();
      expect(screen.getByText('Date')).toBeInTheDocument();
      expect(screen.getByText('Actions')).toBeInTheDocument();
    });

    it('should render column icons', () => {
      render(
        <table>
          <TableHeader {...defaultProps} />
        </table>
      );

      expect(screen.getByText('📄')).toBeInTheDocument();
      expect(screen.getByText('📅')).toBeInTheDocument();
      expect(screen.getByText('⚙️')).toBeInTheDocument();
    });

    it('should apply custom column widths', () => {
      render(
        <table>
          <TableHeader {...defaultProps} />
        </table>
      );

      const filenameHeader = screen.getByText('File Name').closest('th');
      expect(filenameHeader).toHaveStyle({ width: '200px' });
    });

    it('should apply custom CSS classes', () => {
      const columnsWithClass = [
        {
          ...mockColumns[0],
          className: 'custom-class'
        }
      ];

      render(
        <table>
          <TableHeader {...defaultProps} columns={columnsWithClass} />
        </table>
      );

      const header = screen.getByText('File Name').closest('th');
      expect(header).toHaveClass('custom-class');
    });
  });

  describe('Sorting functionality', () => {
    it('should show sort indicator for current sort field', () => {
      render(
        <table>
          <TableHeader {...defaultProps} />
        </table>
      );

      const filenameHeader = screen.getByText('File Name').closest('th');
      expect(filenameHeader).toHaveTextContent('↑'); // ascending indicator
    });

    it('should show descending sort indicator', () => {
      render(
        <table>
          <TableHeader {...defaultProps} sortDirection="desc" />
        </table>
      );

      const filenameHeader = screen.getByText('File Name').closest('th');
      expect(filenameHeader).toHaveTextContent('↓'); // descending indicator
    });

    it('should show neutral sort indicator for non-active columns', () => {
      render(
        <table>
          <TableHeader {...defaultProps} />
        </table>
      );

      const dateHeader = screen.getByText('Date').closest('th');
      expect(dateHeader).toHaveTextContent('↕️'); // neutral indicator
    });

    it('should call onSort when sortable column is clicked', () => {
      render(
        <table>
          <TableHeader {...defaultProps} />
        </table>
      );

      const dateHeader = screen.getByText('Date').closest('th');
      fireEvent.click(dateHeader);

      expect(defaultProps.onSort).toHaveBeenCalledWith('date');
    });

    it('should not call onSort when non-sortable column is clicked', () => {
      render(
        <table>
          <TableHeader {...defaultProps} />
        </table>
      );

      const actionsHeader = screen.getByText('Actions').closest('th');
      fireEvent.click(actionsHeader);

      expect(defaultProps.onSort).not.toHaveBeenCalled();
    });

    it('should have cursor pointer for sortable columns', () => {
      render(
        <table>
          <TableHeader {...defaultProps} />
        </table>
      );

      const filenameHeader = screen.getByText('File Name').closest('th');
      expect(filenameHeader).toHaveClass('cursor-pointer');
    });

    it('should not have cursor pointer for non-sortable columns', () => {
      render(
        <table>
          <TableHeader {...defaultProps} />
        </table>
      );

      const actionsHeader = screen.getByText('Actions').closest('th');
      expect(actionsHeader).not.toHaveClass('cursor-pointer');
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA attributes for sortable columns', () => {
      render(
        <table>
          <TableHeader {...defaultProps} />
        </table>
      );

      const filenameHeader = screen.getByText('File Name').closest('th');
      expect(filenameHeader).toHaveAttribute('aria-sort', 'ascending');
      expect(filenameHeader).toHaveAttribute('role', 'columnheader button');
      expect(filenameHeader).toHaveAttribute('tabIndex', '0');
    });

    it('should have proper ARIA attributes for non-sortable columns', () => {
      render(
        <table>
          <TableHeader {...defaultProps} />
        </table>
      );

      const actionsHeader = screen.getByText('Actions').closest('th');
      expect(actionsHeader).toHaveAttribute('aria-sort', 'none');
      expect(actionsHeader).toHaveAttribute('role', 'columnheader');
      expect(actionsHeader).not.toHaveAttribute('tabIndex');
    });

    it('should have proper aria-label for sorting', () => {
      render(
        <table>
          <TableHeader {...defaultProps} />
        </table>
      );

      const dateHeader = screen.getByText('Date').closest('th');
      expect(dateHeader).toHaveAttribute('aria-label', 'Sort by Date');
    });

    it('should update aria-label based on current sort', () => {
      render(
        <table>
          <TableHeader {...defaultProps} />
        </table>
      );

      const filenameHeader = screen.getByText('File Name').closest('th');
      expect(filenameHeader).toHaveAttribute('aria-label', 'Sort by File Name descending');
    });
  });

  describe('Keyboard navigation', () => {
    it('should handle Enter key for sorting', () => {
      render(
        <table>
          <TableHeader {...defaultProps} />
        </table>
      );

      const dateHeader = screen.getByText('Date').closest('th');
      fireEvent.keyDown(dateHeader, { key: 'Enter' });

      expect(defaultProps.onSort).toHaveBeenCalledWith('date');
    });

    it('should handle Space key for sorting', () => {
      render(
        <table>
          <TableHeader {...defaultProps} />
        </table>
      );

      const dateHeader = screen.getByText('Date').closest('th');
      fireEvent.keyDown(dateHeader, { key: ' ' });

      expect(defaultProps.onSort).toHaveBeenCalledWith('date');
    });

    it('should not handle other keys', () => {
      render(
        <table>
          <TableHeader {...defaultProps} />
        </table>
      );

      const dateHeader = screen.getByText('Date').closest('th');
      fireEvent.keyDown(dateHeader, { key: 'Tab' });

      expect(defaultProps.onSort).not.toHaveBeenCalled();
    });
  });

  describe('Column configurations', () => {
    it('should export default invoice columns', () => {
      expect(defaultInvoiceColumns).toBeDefined();
      expect(defaultInvoiceColumns).toHaveLength(8);
      expect(defaultInvoiceColumns[0].key).toBe('filename');
      expect(defaultInvoiceColumns[0].label).toBe('File');
    });

    it('should export compact invoice columns', () => {
      expect(compactInvoiceColumns).toBeDefined();
      expect(compactInvoiceColumns).toHaveLength(4);
      expect(compactInvoiceColumns[0].key).toBe('filename');
    });

    it('should have proper sortable configuration in default columns', () => {
      const sortableColumns = defaultInvoiceColumns.filter(col => col.sortable !== false);
      const nonSortableColumns = defaultInvoiceColumns.filter(col => col.sortable === false);

      expect(sortableColumns).toHaveLength(7);
      expect(nonSortableColumns).toHaveLength(1);
      expect(nonSortableColumns[0].key).toBe('actions');
    });
  });

  describe('Edge cases', () => {
    it('should handle empty columns array', () => {
      render(
        <table>
          <TableHeader {...defaultProps} columns={[]} />
        </table>
      );

      const thead = screen.getByRole('rowgroup');
      expect(thead.querySelector('tr')).toBeEmptyDOMElement();
    });

    it('should handle columns without icons', () => {
      const columnsWithoutIcons = [
        {
          key: 'test',
          label: 'Test Column',
          sortable: true
        }
      ];

      render(
        <table>
          <TableHeader {...defaultProps} columns={columnsWithoutIcons} />
        </table>
      );

      expect(screen.getByText('Test Column')).toBeInTheDocument();
    });

    it('should handle missing sort field', () => {
      render(
        <table>
          <TableHeader {...defaultProps} sortField="" />
        </table>
      );

      const filenameHeader = screen.getByText('File Name').closest('th');
      expect(filenameHeader).toHaveAttribute('aria-sort', 'none');
    });
  });
});
