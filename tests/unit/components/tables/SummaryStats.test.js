import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import SummaryStats from '../../../../src/popup/components/tables/SummaryStats';

// Mock Intl.NumberFormat
global.Intl = {
  NumberFormat: vi.fn().mockImplementation(() => ({
    format: vi.fn().mockImplementation((value) => `${value.toFixed(2)} PLN`)
  }))
};

describe('SummaryStats Component', () => {
  it('can be imported without errors', () => {
    expect(SummaryStats).toBeDefined();
    expect(typeof SummaryStats).toBe('function');
  });

});
