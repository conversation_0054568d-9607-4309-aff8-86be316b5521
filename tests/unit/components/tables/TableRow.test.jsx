import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import TableRow, { TableRowSkeleton, EmptyTableRow } from '../../../../src/popup/components/tables/TableRow.jsx';

describe('TableRow Component', () => {
  const mockInvoice = {
    id: '1',
    filename: 'test-invoice.pdf',
    fileSize: 1024000, // 1MB
    extractionMethod: 'pdf_text',
    number: 'INV-001',
    date: '2024-01-15',
    seller_name: 'Test Seller',
    buyer_name: 'Test Buyer',
    total_gross: 123.45,
    currency: 'PLN'
  };

  const mockColumns = [
    { key: 'filename', label: 'File' },
    { key: 'extractionMethod', label: 'Method' },
    { key: 'number', label: 'Number' },
    { key: 'date', label: 'Date' },
    { key: 'seller_name', label: 'Seller' },
    { key: 'buyer_name', label: 'Buyer' },
    { key: 'total_gross', label: 'Total' },
    { key: 'actions', label: 'Actions' }
  ];

  const defaultProps = {
    invoice: mockInvoice,
    columns: mockColumns,
    onSelect: vi.fn(),
    onDelete: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render all column data', () => {
      render(
        <table>
          <tbody>
            <TableRow {...defaultProps} />
          </tbody>
        </table>
      );

      expect(screen.getByText('test-invoice.pdf')).toBeInTheDocument();
      expect(screen.getByText('INV-001')).toBeInTheDocument();
      expect(screen.getByText('Test Seller')).toBeInTheDocument();
      expect(screen.getByText('Test Buyer')).toBeInTheDocument();
    });

    it('should format file size correctly', () => {
      render(
        <table>
          <tbody>
            <TableRow {...defaultProps} />
          </tbody>
        </table>
      );

      expect(screen.getByText('1000.0 KB')).toBeInTheDocument();
    });

    it('should format date correctly', () => {
      render(
        <table>
          <tbody>
            <TableRow {...defaultProps} />
          </tbody>
        </table>
      );

      expect(screen.getByText('Jan 15, 2024')).toBeInTheDocument();
    });

    it('should format currency correctly', () => {
      render(
        <table>
          <tbody>
            <TableRow {...defaultProps} />
          </tbody>
        </table>
      );

      expect(screen.getByText('123.45 PLN')).toBeInTheDocument();
    });
  });

  describe('Extraction method display', () => {
    it('should display PDF text method correctly', () => {
      render(
        <table>
          <tbody>
            <TableRow {...defaultProps} />
          </tbody>
        </table>
      );

      const methodBadge = screen.getByText('📄 PDF Text');
      expect(methodBadge).toBeInTheDocument();
      expect(methodBadge).toHaveClass('bg-green-100', 'text-green-800');
    });

    it('should display PDF OCR method correctly', () => {
      const invoiceWithOCR = {
        ...mockInvoice,
        extractionMethod: 'pdf_ocr'
      };

      render(
        <table>
          <tbody>
            <TableRow {...defaultProps} invoice={invoiceWithOCR} />
          </tbody>
        </table>
      );

      const methodBadge = screen.getByText('🔍 PDF OCR');
      expect(methodBadge).toHaveClass('bg-yellow-100', 'text-yellow-800');
    });

    it('should display OCR method correctly', () => {
      const invoiceWithOCR = {
        ...mockInvoice,
        extractionMethod: 'ocr'
      };

      render(
        <table>
          <tbody>
            <TableRow {...defaultProps} invoice={invoiceWithOCR} />
          </tbody>
        </table>
      );

      const methodBadge = screen.getByText('👁️ OCR');
      expect(methodBadge).toHaveClass('bg-blue-100', 'text-blue-800');
    });

    it('should display unknown method correctly', () => {
      const invoiceWithUnknown = {
        ...mockInvoice,
        extractionMethod: 'unknown'
      };

      render(
        <table>
          <tbody>
            <TableRow {...defaultProps} invoice={invoiceWithUnknown} />
          </tbody>
        </table>
      );

      const methodBadge = screen.getByText('❓ Unknown');
      expect(methodBadge).toHaveClass('bg-gray-100', 'text-gray-800');
    });
  });

  describe('Action buttons', () => {
    it('should render view and delete buttons', () => {
      render(
        <table>
          <tbody>
            <TableRow {...defaultProps} />
          </tbody>
        </table>
      );

      expect(screen.getByLabelText('View details for test-invoice.pdf')).toBeInTheDocument();
      expect(screen.getByLabelText('Delete test-invoice.pdf')).toBeInTheDocument();
    });

    it('should call onSelect when view button is clicked', () => {
      render(
        <table>
          <tbody>
            <TableRow {...defaultProps} />
          </tbody>
        </table>
      );

      const viewButton = screen.getByLabelText('View details for test-invoice.pdf');
      fireEvent.click(viewButton);

      expect(defaultProps.onSelect).toHaveBeenCalledWith(mockInvoice);
    });

    it('should call onDelete when delete button is clicked', () => {
      render(
        <table>
          <tbody>
            <TableRow {...defaultProps} />
          </tbody>
        </table>
      );

      const deleteButton = screen.getByLabelText('Delete test-invoice.pdf');
      fireEvent.click(deleteButton);

      expect(defaultProps.onDelete).toHaveBeenCalledWith('1');
    });

    it('should stop propagation on action button clicks', () => {
      const rowClickHandler = vi.fn();

      render(
        <table>
          <tbody>
            <TableRow {...defaultProps} onSelect={rowClickHandler} />
          </tbody>
        </table>
      );

      const viewButton = screen.getByLabelText('View details for test-invoice.pdf');
      fireEvent.click(viewButton);

      // onSelect should be called from button click, not row click
      expect(defaultProps.onSelect).toHaveBeenCalledTimes(1);
    });
  });

  describe('Row interaction', () => {
    it('should call onSelect when row is clicked', () => {
      render(
        <table>
          <tbody>
            <TableRow {...defaultProps} />
          </tbody>
        </table>
      );

      const row = screen.getByRole('row');
      fireEvent.click(row);

      expect(defaultProps.onSelect).toHaveBeenCalledWith(mockInvoice);
    });

    it('should handle keyboard navigation', () => {
      render(
        <table>
          <tbody>
            <TableRow {...defaultProps} />
          </tbody>
        </table>
      );

      const row = screen.getByRole('row');

      fireEvent.keyDown(row, { key: 'Enter' });
      expect(defaultProps.onSelect).toHaveBeenCalledWith(mockInvoice);

      fireEvent.keyDown(row, { key: ' ' });
      expect(defaultProps.onSelect).toHaveBeenCalledTimes(2);
    });

    it('should show selected state', () => {
      render(
        <table>
          <tbody>
            <TableRow {...defaultProps} isSelected={true} />
          </tbody>
        </table>
      );

      const row = screen.getByRole('row');
      expect(row).toHaveClass('bg-blue-50', 'border-blue-200');
      expect(row).toHaveAttribute('aria-selected', 'true');
    });
  });

  describe('Data formatting edge cases', () => {
    it('should handle missing data gracefully', () => {
      const incompleteInvoice = {
        id: '2',
        filename: 'incomplete.pdf'
        // Missing other fields
      };

      render(
        <table>
          <tbody>
            <TableRow {...defaultProps} invoice={incompleteInvoice} />
          </tbody>
        </table>
      );

      // Should show dashes for missing data
      expect(screen.getAllByText('-')).toHaveLength(5); // number, seller, buyer, total, date
    });

    it('should handle zero values correctly', () => {
      const zeroInvoice = {
        ...mockInvoice,
        total_gross: 0
      };

      render(
        <table>
          <tbody>
            <TableRow {...defaultProps} invoice={zeroInvoice} />
          </tbody>
        </table>
      );

      expect(screen.getByText('0.00 PLN')).toBeInTheDocument();
    });

    it('should handle invalid dates', () => {
      const invalidDateInvoice = {
        ...mockInvoice,
        date: 'invalid-date'
      };

      render(
        <table>
          <tbody>
            <TableRow {...defaultProps} invoice={invalidDateInvoice} />
          </tbody>
        </table>
      );

      expect(screen.getByText('invalid-date')).toBeInTheDocument();
    });
  });

  describe('Custom formatters', () => {
    it('should use custom formatter when provided', () => {
      const customColumns = [
        {
          key: 'custom',
          label: 'Custom',
          formatter: (value, invoice) => `Custom: ${value}`
        }
      ];

      const customInvoice = {
        ...mockInvoice,
        custom: 'test-value'
      };

      render(
        <table>
          <tbody>
            <TableRow {...defaultProps} columns={customColumns} invoice={customInvoice} />
          </tbody>
        </table>
      );

      expect(screen.getByText('Custom: test-value')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', () => {
      render(
        <table>
          <tbody>
            <TableRow {...defaultProps} />
          </tbody>
        </table>
      );

      const row = screen.getByRole('row');
      expect(row).toHaveAttribute('tabIndex', '0');
      expect(row).toHaveAttribute('aria-label', 'Invoice INV-001');
    });

    it('should have proper cell roles', () => {
      render(
        <table>
          <tbody>
            <TableRow {...defaultProps} />
          </tbody>
        </table>
      );

      const cells = screen.getAllByRole('gridcell');
      expect(cells).toHaveLength(mockColumns.length);
    });
  });
});

describe('TableRowSkeleton Component', () => {
  const mockColumns = [
    { key: 'col1', label: 'Column 1' },
    { key: 'col2', label: 'Column 2' },
    { key: 'col3', label: 'Column 3' }
  ];

  it('should render skeleton with correct number of cells', () => {
    render(
      <table>
        <tbody>
          <TableRowSkeleton columns={mockColumns} />
        </tbody>
      </table>
    );

    const skeletonCells = screen.getAllByRole('cell');
    expect(skeletonCells).toHaveLength(3);
  });

  it('should have loading animation class', () => {
    render(
      <table>
        <tbody>
          <TableRowSkeleton columns={mockColumns} />
        </tbody>
      </table>
    );

    const row = screen.getByRole('row');
    expect(row).toHaveClass('animate-pulse');
  });
});

describe('EmptyTableRow Component', () => {
  const mockColumns = [
    { key: 'col1', label: 'Column 1' },
    { key: 'col2', label: 'Column 2' }
  ];

  it('should render empty state with default message', () => {
    render(
      <table>
        <tbody>
          <EmptyTableRow columns={mockColumns} />
        </tbody>
      </table>
    );

    expect(screen.getByText('No data available')).toBeInTheDocument();
  });

  it('should render custom message', () => {
    render(
      <table>
        <tbody>
          <EmptyTableRow columns={mockColumns} message="Custom empty message" />
        </tbody>
      </table>
    );

    expect(screen.getByText('Custom empty message')).toBeInTheDocument();
  });

  it('should span all columns', () => {
    render(
      <table>
        <tbody>
          <EmptyTableRow columns={mockColumns} />
        </tbody>
      </table>
    );

    const cell = screen.getByRole('cell');
    expect(cell).toHaveAttribute('colSpan', '2');
  });
});
