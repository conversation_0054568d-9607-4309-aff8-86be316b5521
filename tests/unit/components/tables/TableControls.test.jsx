import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import TableControls from '../../../../src/popup/components/tables/TableControls.jsx';

describe('TableControls Component', () => {
  const mockPagination = {
    totalItems: 100,
    totalPages: 10,
    currentPage: 1,
    pageSize: 10,
    startItem: 1,
    endItem: 10,
    hasNextPage: true,
    hasPrevPage: false
  };

  const defaultProps = {
    filterText: '',
    onFilterChange: vi.fn(),
    pagination: mockPagination,
    onPageChange: vi.fn(),
    onPageSizeChange: vi.fn(),
    onReset: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Search functionality', () => {
    it('should render search input with placeholder', () => {
      render(<TableControls {...defaultProps} />);

      const searchInput = screen.getByPlaceholderText('Search invoices...');
      expect(searchInput).toBeInTheDocument();
    });

    it('should display current filter text', () => {
      render(<TableControls {...defaultProps} filterText="test search" />);

      const searchInput = screen.getByDisplayValue('test search');
      expect(searchInput).toBeInTheDocument();
    });

    it('should call onFilterChange when typing in search', () => {
      render(<TableControls {...defaultProps} />);

      const searchInput = screen.getByPlaceholderText('Search invoices...');
      fireEvent.change(searchInput, { target: { value: 'new search' } });

      expect(defaultProps.onFilterChange).toHaveBeenCalledWith('new search');
    });

    it('should show clear button when filter text exists', () => {
      render(<TableControls {...defaultProps} filterText="test" />);

      const clearButton = screen.getByLabelText('Clear search');
      expect(clearButton).toBeInTheDocument();
    });

    it('should clear filter when clear button is clicked', () => {
      render(<TableControls {...defaultProps} filterText="test" />);

      const clearButton = screen.getByLabelText('Clear search');
      fireEvent.click(clearButton);

      expect(defaultProps.onFilterChange).toHaveBeenCalledWith('');
    });
  });

  describe('Reset functionality', () => {
    it('should render reset button by default', () => {
      render(<TableControls {...defaultProps} />);

      const resetButton = screen.getByText('🔄 Reset');
      expect(resetButton).toBeInTheDocument();
    });

    it('should call onReset when reset button is clicked', () => {
      render(<TableControls {...defaultProps} />);

      const resetButton = screen.getByText('🔄 Reset');
      fireEvent.click(resetButton);

      expect(defaultProps.onReset).toHaveBeenCalled();
    });

    it('should not render reset button when showReset is false', () => {
      render(<TableControls {...defaultProps} showReset={false} />);

      const resetButton = screen.queryByText('🔄 Reset');
      expect(resetButton).not.toBeInTheDocument();
    });
  });

  describe('Pagination display', () => {
    it('should show results info', () => {
      render(<TableControls {...defaultProps} />);

      expect(screen.getByText(/Showing 1 to 10 of 100 results/)).toBeInTheDocument();
    });

    it('should show page size selector', () => {
      render(<TableControls {...defaultProps} />);

      const pageSelect = screen.getByLabelText('Items per page');
      expect(pageSelect).toBeInTheDocument();
      expect(pageSelect.value).toBe('10');
    });

    it('should call onPageSizeChange when page size is changed', () => {
      render(<TableControls {...defaultProps} />);

      const pageSelect = screen.getByLabelText('Items per page');
      fireEvent.change(pageSelect, { target: { value: '25' } });

      expect(defaultProps.onPageSizeChange).toHaveBeenCalledWith(25);
    });

    it('should not show pagination when showPagination is false', () => {
      render(<TableControls {...defaultProps} showPagination={false} />);

      expect(screen.queryByText(/Showing/)).not.toBeInTheDocument();
    });
  });

  describe('Pagination navigation', () => {
    it('should render pagination buttons', () => {
      render(<TableControls {...defaultProps} />);

      expect(screen.getByLabelText('Go to first page')).toBeInTheDocument();
      expect(screen.getByLabelText('Go to previous page')).toBeInTheDocument();
      expect(screen.getByLabelText('Go to next page')).toBeInTheDocument();
      expect(screen.getByLabelText('Go to last page')).toBeInTheDocument();
    });

    it('should disable previous/first buttons on first page', () => {
      render(<TableControls {...defaultProps} />);

      expect(screen.getByLabelText('Go to first page')).toBeDisabled();
      expect(screen.getByLabelText('Go to previous page')).toBeDisabled();
    });

    it('should enable next/last buttons when not on last page', () => {
      render(<TableControls {...defaultProps} />);

      expect(screen.getByLabelText('Go to next page')).not.toBeDisabled();
      expect(screen.getByLabelText('Go to last page')).not.toBeDisabled();
    });

    it('should disable next/last buttons on last page', () => {
      const lastPagePagination = {
        ...mockPagination,
        currentPage: 10,
        hasNextPage: false,
        hasPrevPage: true
      };

      render(<TableControls {...defaultProps} pagination={lastPagePagination} />);

      expect(screen.getByLabelText('Go to next page')).toBeDisabled();
      expect(screen.getByLabelText('Go to last page')).toBeDisabled();
    });

    it('should call onPageChange when navigation buttons are clicked', () => {
      const midPagePagination = {
        ...mockPagination,
        currentPage: 5,
        hasPrevPage: true,
        hasNextPage: true
      };

      render(<TableControls {...defaultProps} pagination={midPagePagination} />);

      fireEvent.click(screen.getByLabelText('Go to first page'));
      expect(defaultProps.onPageChange).toHaveBeenCalledWith(1);

      fireEvent.click(screen.getByLabelText('Go to previous page'));
      expect(defaultProps.onPageChange).toHaveBeenCalledWith(4);

      fireEvent.click(screen.getByLabelText('Go to next page'));
      expect(defaultProps.onPageChange).toHaveBeenCalledWith(6);

      fireEvent.click(screen.getByLabelText('Go to last page'));
      expect(defaultProps.onPageChange).toHaveBeenCalledWith(10);
    });
  });

  describe('Page number buttons', () => {
    it('should render current page as active', () => {
      render(<TableControls {...defaultProps} />);

      const currentPageButton = screen.getByLabelText('Go to page 1');
      expect(currentPageButton).toHaveAttribute('aria-current', 'page');
    });

    it('should call onPageChange when page number is clicked', () => {
      render(<TableControls {...defaultProps} />);

      const pageButton = screen.getByLabelText('Go to page 2');
      fireEvent.click(pageButton);

      expect(defaultProps.onPageChange).toHaveBeenCalledWith(2);
    });
  });

  describe('Empty state', () => {
    it('should show no results message when filtered and no items', () => {
      const emptyPagination = {
        ...mockPagination,
        totalItems: 0,
        totalPages: 0,
        startItem: 0,
        endItem: 0
      };

      render(<TableControls {...defaultProps} pagination={emptyPagination} filterText="test" />);

      expect(screen.getByText('No results found for "test"')).toBeInTheDocument();
      expect(screen.getByText('Clear search')).toBeInTheDocument();
    });

    it('should clear search when clear button in empty state is clicked', () => {
      const emptyPagination = {
        ...mockPagination,
        totalItems: 0,
        totalPages: 0,
        startItem: 0,
        endItem: 0
      };

      render(<TableControls {...defaultProps} pagination={emptyPagination} filterText="test" />);

      const clearButton = screen.getByText('Clear search');
      fireEvent.click(clearButton);

      expect(defaultProps.onFilterChange).toHaveBeenCalledWith('');
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      render(<TableControls {...defaultProps} />);

      expect(screen.getByLabelText('Search invoices')).toBeInTheDocument();
      expect(screen.getByLabelText('Items per page')).toBeInTheDocument();
    });

    it('should have proper ARIA attributes for pagination', () => {
      render(<TableControls {...defaultProps} />);

      const currentPage = screen.getByLabelText('Go to page 1');
      expect(currentPage).toHaveAttribute('aria-current', 'page');
    });
  });

  describe('Conditional rendering', () => {
    it('should not show page size selector when showPageSize is false', () => {
      render(<TableControls {...defaultProps} showPageSize={false} />);

      expect(screen.queryByLabelText('Items per page')).not.toBeInTheDocument();
    });

    it('should not show pagination when totalPages is 1', () => {
      const singlePagePagination = {
        ...mockPagination,
        totalPages: 1,
        hasNextPage: false
      };

      render(<TableControls {...defaultProps} pagination={singlePagePagination} />);

      expect(screen.queryByLabelText('Go to next page')).not.toBeInTheDocument();
    });
  });
});
