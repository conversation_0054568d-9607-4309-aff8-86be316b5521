import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import FileProgressItem from '../../../../src/components/upload/FileProgressItem.jsx';

// Mock prettyBytes
vi.mock('pretty-bytes', () => ({
  default: (bytes) => `${Math.round(bytes / 1024)}KB`
}));

// Mock ProgressBar component
vi.mock('../../../../src/components/upload/ProgressBar.jsx', () => ({
  default: ({ progress, variant, size, showPercentage }) => (
    <div data-testid="progress-bar">
      <span data-testid="progress-value">{progress}%</span>
      <span data-testid="progress-variant">{variant}</span>
      <span data-testid="progress-size">{size}</span>
      <span data-testid="progress-percentage">{showPercentage ? 'shown' : 'hidden'}</span>
    </div>
  )
}));

describe('FileProgressItem Component', () => {
  const mockOnToggleExpansion = vi.fn();
  const mockOnRetry = vi.fn();

  const baseFile = {
    id: 'file1',
    name: 'document.pdf',
    size: 1024000,
    type: 'application/pdf',
    lastModified: Date.now(),
    progress: 45,
    status: 'uploading',
    stage: 'validating',
    startTime: Date.now() - 5000,
    estimatedTime: 10
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('returns null when no file provided', () => {
      const { container } = render(<FileProgressItem file={null} />);
      expect(container.firstChild).toBeNull();
    });

    it('renders basic file information', () => {
      render(<FileProgressItem file={baseFile} />);

      expect(screen.getByText('document.pdf')).toBeInTheDocument();
      expect(screen.getByText('1000KB')).toBeInTheDocument();
      expect(screen.getByText('45%')).toBeInTheDocument();
    });

    it('renders file status correctly', () => {
      render(<FileProgressItem file={baseFile} />);

      expect(screen.getByText('Checking file format and security...')).toBeInTheDocument();
    });
  });

  describe('Status Configurations', () => {
    it('renders pending status', () => {
      const pendingFile = { ...baseFile, status: 'pending', progress: 0 };
      render(<FileProgressItem file={pendingFile} />);

      expect(screen.getByText('Waiting to start...')).toBeInTheDocument();
      expect(screen.getByLabelText('Pending')).toBeInTheDocument();
    });

    it('renders uploading status with spinner', () => {
      const uploadingFile = { ...baseFile, status: 'uploading' };
      render(<FileProgressItem file={uploadingFile} />);

      expect(screen.getByText('Transferring file...')).toBeInTheDocument();
      expect(screen.getByTestId('progress-bar')).toBeInTheDocument();
    });

    it('renders validating status with spinner', () => {
      const validatingFile = { ...baseFile, status: 'validating' };
      render(<FileProgressItem file={validatingFile} />);

      expect(screen.getByText('Checking file format and security...')).toBeInTheDocument();
      expect(screen.getByTestId('progress-bar')).toBeInTheDocument();
    });

    it('renders processing status', () => {
      const processingFile = { ...baseFile, status: 'processing' };
      render(<FileProgressItem file={processingFile} />);

      expect(screen.getByText('Preparing for analysis...')).toBeInTheDocument();
    });

    it('renders complete status', () => {
      const completeFile = { ...baseFile, status: 'complete', progress: 100 };
      render(<FileProgressItem file={completeFile} />);

      expect(screen.getByText('Successfully processed')).toBeInTheDocument();
      expect(screen.getByLabelText('Complete')).toBeInTheDocument();
    });

    it('renders error status with retry button', () => {
      const errorFile = {
        ...baseFile,
        status: 'error',
        error: 'Processing failed',
        progress: 0
      };

      render(
        <FileProgressItem
          file={errorFile}
          onRetry={mockOnRetry}
        />
      );

      expect(screen.getByText('Processing failed')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /retry document.pdf/i })).toBeInTheDocument();
    });
  });

  describe('Progress Bar', () => {
    it('shows progress bar for active statuses', () => {
      render(<FileProgressItem file={baseFile} />);

      const progressBar = screen.getByTestId('progress-bar');
      expect(progressBar).toBeInTheDocument();
      expect(screen.getByTestId('progress-value')).toHaveTextContent('45%');
      expect(screen.getByTestId('progress-variant')).toHaveTextContent('active');
      expect(screen.getByTestId('progress-size')).toHaveTextContent('sm');
      expect(screen.getByTestId('progress-percentage')).toHaveTextContent('hidden');
    });

    it('does not show progress bar for pending status', () => {
      const pendingFile = { ...baseFile, status: 'pending' };
      render(<FileProgressItem file={pendingFile} />);

      expect(screen.queryByTestId('progress-bar')).not.toBeInTheDocument();
    });

    it('does not show progress bar for complete status', () => {
      const completeFile = { ...baseFile, status: 'complete' };
      render(<FileProgressItem file={completeFile} />);

      expect(screen.queryByTestId('progress-bar')).not.toBeInTheDocument();
    });

    it('shows error variant for error status', () => {
      const errorFile = { ...baseFile, status: 'error' };
      render(<FileProgressItem file={errorFile} />);

      expect(screen.getByTestId('progress-variant')).toHaveTextContent('error');
    });
  });

  describe('Time Display', () => {
    it('shows processing time when available', () => {
      const fileWithTime = {
        ...baseFile,
        startTime: Date.now() - 30000, // 30 seconds ago
        endTime: Date.now()
      };

      render(<FileProgressItem file={fileWithTime} />);
      expect(screen.getByText('30s')).toBeInTheDocument();
    });

    it('shows estimated time remaining', () => {
      render(<FileProgressItem file={baseFile} />);
      expect(screen.getByText('10s remaining')).toBeInTheDocument();
    });

    it('formats time correctly for minutes', () => {
      const fileWithLongTime = {
        ...baseFile,
        startTime: Date.now() - 90000, // 90 seconds ago
        endTime: Date.now()
      };

      render(<FileProgressItem file={fileWithLongTime} />);
      expect(screen.getByText('1m 30s')).toBeInTheDocument();
    });
  });

  describe('Action Buttons', () => {
    it('shows retry button for error status when onRetry provided', () => {
      const errorFile = { ...baseFile, status: 'error' };

      render(
        <FileProgressItem
          file={errorFile}
          onRetry={mockOnRetry}
        />
      );

      const retryButton = screen.getByRole('button', { name: /retry document.pdf/i });
      fireEvent.click(retryButton);
      expect(mockOnRetry).toHaveBeenCalledTimes(1);
    });

    it('does not show retry button when onRetry not provided', () => {
      const errorFile = { ...baseFile, status: 'error' };

      render(<FileProgressItem file={errorFile} />);
      expect(screen.queryByRole('button', { name: /retry/i })).not.toBeInTheDocument();
    });

    it('shows expansion toggle when details available', () => {
      const fileWithDetails = {
        ...baseFile,
        validationResult: { isValid: true },
        error: 'Some error'
      };

      render(
        <FileProgressItem
          file={fileWithDetails}
          onToggleExpansion={mockOnToggleExpansion}
        />
      );

      const toggleButton = screen.getByRole('button', { name: /show details for document.pdf/i });
      expect(toggleButton).toBeInTheDocument();

      fireEvent.click(toggleButton);
      expect(mockOnToggleExpansion).toHaveBeenCalledTimes(1);
    });

    it('updates toggle button text when expanded', () => {
      const fileWithDetails = {
        ...baseFile,
        validationResult: { isValid: true }
      };

      render(
        <FileProgressItem
          file={fileWithDetails}
          isExpanded={true}
          onToggleExpansion={mockOnToggleExpansion}
        />
      );

      expect(screen.getByRole('button', { name: /hide details for document.pdf/i })).toBeInTheDocument();
    });
  });

  describe('Expanded Details', () => {
    const fileWithAllDetails = {
      ...baseFile,
      error: 'Processing failed',
      validationResult: {
        isValid: false,
        errors: ['Invalid format', 'Too large'],
        warnings: ['Quality warning']
      },
      securityResult: {
        riskLevel: 'medium',
        riskScore: 0.3,
        threats: ['Suspicious pattern']
      }
    };

    it('shows error details when expanded', () => {
      render(
        <FileProgressItem
          file={fileWithAllDetails}
          isExpanded={true}
        />
      );

      expect(screen.getByText('Error Details')).toBeInTheDocument();
      expect(screen.getByText('Processing failed')).toBeInTheDocument();
    });

    it('shows validation results when expanded', () => {
      render(
        <FileProgressItem
          file={fileWithAllDetails}
          isExpanded={true}
        />
      );

      expect(screen.getByText('Validation Results')).toBeInTheDocument();
      expect(screen.getByText('❌ Invalid')).toBeInTheDocument();
      expect(screen.getByText('Invalid format')).toBeInTheDocument();
      expect(screen.getByText('Too large')).toBeInTheDocument();
      expect(screen.getByText('Quality warning')).toBeInTheDocument();
    });

    it('shows security scan results when expanded', () => {
      render(
        <FileProgressItem
          file={fileWithAllDetails}
          isExpanded={true}
        />
      );

      expect(screen.getByText('Security Scan')).toBeInTheDocument();
      expect(screen.getByText('Risk Level: medium')).toBeInTheDocument();
      expect(screen.getByText('Score: 30%')).toBeInTheDocument();
      expect(screen.getByText('Suspicious pattern')).toBeInTheDocument();
    });

    it('shows file metadata when expanded', () => {
      render(
        <FileProgressItem
          file={fileWithAllDetails}
          isExpanded={true}
        />
      );

      expect(screen.getByText('File Information')).toBeInTheDocument();
      expect(screen.getByText('Size: 1000KB')).toBeInTheDocument();
      expect(screen.getByText('Type: application/pdf')).toBeInTheDocument();
    });

    it('does not show expanded details when not expanded', () => {
      render(
        <FileProgressItem
          file={fileWithAllDetails}
          isExpanded={false}
        />
      );

      expect(screen.queryByText('Error Details')).not.toBeInTheDocument();
      expect(screen.queryByText('Validation Results')).not.toBeInTheDocument();
      expect(screen.queryByText('Security Scan')).not.toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('provides screen reader announcements for active processing', () => {
      render(<FileProgressItem file={baseFile} />);

      const announcement = screen.getByText(/document.pdf.*validating.*45% complete/i);
      expect(announcement).toBeInTheDocument();
      expect(announcement).toHaveAttribute('aria-live', 'polite');
    });

    it('provides screen reader announcements for completion', () => {
      const completeFile = { ...baseFile, status: 'complete' };
      render(<FileProgressItem file={completeFile} />);

      const announcement = screen.getByText(/document.pdf.*processing completed successfully/i);
      expect(announcement).toBeInTheDocument();
    });

    it('provides screen reader announcements for errors', () => {
      const errorFile = { ...baseFile, status: 'error', error: 'Failed to process' };
      render(<FileProgressItem file={errorFile} />);

      const announcement = screen.getByText(/document.pdf.*processing failed.*failed to process/i);
      expect(announcement).toBeInTheDocument();
    });
  });
});
