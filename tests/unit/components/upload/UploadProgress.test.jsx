import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import UploadProgress from '../../../../src/components/upload/UploadProgress.jsx';

// Mock child components
vi.mock('../../../../src/components/upload/FileProgressItem.jsx', () => ({
  default: ({ file, isExpanded, onToggleExpansion, onRetry }) => (
    <div data-testid={`file-progress-${file.id}`}>
      <span>{file.name}</span>
      <span>{file.status}</span>
      <span>{file.progress}%</span>
      {onToggleExpansion && (
        <button onClick={onToggleExpansion} data-testid={`toggle-${file.id}`}>
          {isExpanded ? 'Collapse' : 'Expand'}
        </button>
      )}
      {onRetry && file.status === 'error' && (
        <button onClick={onRetry} data-testid={`retry-${file.id}`}>
          Retry
        </button>
      )}
    </div>
  )
}));

vi.mock('../../../../src/components/upload/ProgressBar.jsx', () => ({
  default: ({ progress, variant, showPercentage }) => (
    <div data-testid="progress-bar">
      <span data-testid="progress-value">{progress}%</span>
      <span data-testid="progress-variant">{variant}</span>
      {showPercentage && <span data-testid="progress-percentage">Percentage shown</span>}
    </div>
  )
}));

describe('UploadProgress Component', () => {
  const mockOnCancel = vi.fn();
  const mockOnRetry = vi.fn();
  const mockOnClear = vi.fn();

  const sampleFiles = [
    {
      id: 'file1',
      name: 'document1.pdf',
      size: 1024000,
      status: 'uploading',
      progress: 45,
      stage: 'validating'
    },
    {
      id: 'file2',
      name: 'image1.jpg',
      size: 512000,
      status: 'complete',
      progress: 100,
      stage: 'complete'
    },
    {
      id: 'file3',
      name: 'document2.pdf',
      size: 2048000,
      status: 'error',
      progress: 0,
      stage: 'error',
      error: 'Processing failed'
    }
  ];

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders nothing when not active and no files', () => {
      const { container } = render(
        <UploadProgress
          files={[]}
          overallProgress={0}
          isActive={false}
        />
      );
      expect(container.firstChild).toBeNull();
    });

    it('renders overall progress header when active', () => {
      render(
        <UploadProgress
          files={sampleFiles}
          overallProgress={65}
          isActive={true}
          onCancel={mockOnCancel}
          onRetry={mockOnRetry}
          onClear={mockOnClear}
        />
      );

      expect(screen.getByText('Processing Files...')).toBeInTheDocument();
      expect(screen.getByText('1/3 files completed • 1 failed • 1 processing')).toBeInTheDocument();
      expect(screen.getByTestId('progress-bar')).toBeInTheDocument();
      expect(screen.getByTestId('progress-value')).toHaveTextContent('65%');
    });

    it('renders completed state correctly', () => {
      const completedFiles = sampleFiles.map(f => ({ ...f, status: 'complete', progress: 100 }));

      render(
        <UploadProgress
          files={completedFiles}
          overallProgress={100}
          isActive={false}
        />
      );

      expect(screen.getByText('All Files Processed')).toBeInTheDocument();
      expect(screen.getByText('3/3 files completed')).toBeInTheDocument();
    });

    it('renders error state correctly', () => {
      render(
        <UploadProgress
          files={sampleFiles}
          overallProgress={50}
          isActive={false}
        />
      );

      expect(screen.getByText('Processing Completed with Errors')).toBeInTheDocument();
      expect(screen.getByText('1/3 files completed • 1 failed')).toBeInTheDocument();
    });
  });

  describe('Action Buttons', () => {
    it('shows cancel button when active', () => {
      render(
        <UploadProgress
          files={sampleFiles}
          overallProgress={50}
          isActive={true}
          onCancel={mockOnCancel}
        />
      );

      const cancelButton = screen.getByRole('button', { name: /cancel upload/i });
      expect(cancelButton).toBeInTheDocument();

      fireEvent.click(cancelButton);
      expect(mockOnCancel).toHaveBeenCalledTimes(1);
    });

    it('shows retry button when not active and has failed files', () => {
      render(
        <UploadProgress
          files={sampleFiles}
          overallProgress={50}
          isActive={false}
          onRetry={mockOnRetry}
        />
      );

      const retryButton = screen.getByRole('button', { name: /retry failed uploads/i });
      expect(retryButton).toBeInTheDocument();

      fireEvent.click(retryButton);
      expect(mockOnRetry).toHaveBeenCalledTimes(1);
    });

    it('shows clear button when not active', () => {
      render(
        <UploadProgress
          files={sampleFiles}
          overallProgress={50}
          isActive={false}
          onClear={mockOnClear}
        />
      );

      const clearButton = screen.getByRole('button', { name: /clear progress/i });
      expect(clearButton).toBeInTheDocument();

      fireEvent.click(clearButton);
      expect(mockOnClear).toHaveBeenCalledTimes(1);
    });

    it('does not show action buttons when callbacks not provided', () => {
      render(
        <UploadProgress
          files={sampleFiles}
          overallProgress={50}
          isActive={false}
        />
      );

      expect(screen.queryByRole('button', { name: /retry/i })).not.toBeInTheDocument();
      expect(screen.queryByRole('button', { name: /clear/i })).not.toBeInTheDocument();
    });
  });

  describe('File Details', () => {
    it('renders file details when showDetails is true', () => {
      render(
        <UploadProgress
          files={sampleFiles}
          overallProgress={50}
          isActive={false}
          showDetails={true}
        />
      );

      expect(screen.getByText('File Details (3)')).toBeInTheDocument();
      expect(screen.getByTestId('file-progress-file1')).toBeInTheDocument();
      expect(screen.getByTestId('file-progress-file2')).toBeInTheDocument();
      expect(screen.getByTestId('file-progress-file3')).toBeInTheDocument();
    });

    it('does not render file details when showDetails is false', () => {
      render(
        <UploadProgress
          files={sampleFiles}
          overallProgress={50}
          isActive={false}
          showDetails={false}
        />
      );

      expect(screen.queryByText('File Details (3)')).not.toBeInTheDocument();
      expect(screen.queryByTestId('file-progress-file1')).not.toBeInTheDocument();
    });

    it('shows expand/collapse all button when more than 3 files', () => {
      const manyFiles = Array.from({ length: 5 }, (_, i) => ({
        id: `file${i}`,
        name: `file${i}.pdf`,
        status: 'complete',
        progress: 100
      }));

      render(
        <UploadProgress
          files={manyFiles}
          overallProgress={100}
          isActive={false}
          showDetails={true}
        />
      );

      expect(screen.getByText('Expand All')).toBeInTheDocument();
    });
  });

  describe('Progress Bar Variants', () => {
    it('uses error variant when there are failed files', () => {
      render(
        <UploadProgress
          files={sampleFiles}
          overallProgress={50}
          isActive={false}
        />
      );

      expect(screen.getByTestId('progress-variant')).toHaveTextContent('error');
    });

    it('uses active variant when processing', () => {
      render(
        <UploadProgress
          files={sampleFiles}
          overallProgress={50}
          isActive={true}
        />
      );

      expect(screen.getByTestId('progress-variant')).toHaveTextContent('active');
    });

    it('uses success variant when completed without errors', () => {
      const successFiles = sampleFiles.map(f => ({ ...f, status: 'complete' }));

      render(
        <UploadProgress
          files={successFiles}
          overallProgress={100}
          isActive={false}
        />
      );

      expect(screen.getByTestId('progress-variant')).toHaveTextContent('success');
    });
  });

  describe('Accessibility', () => {
    it('provides screen reader announcements for active processing', () => {
      render(
        <UploadProgress
          files={sampleFiles}
          overallProgress={65}
          isActive={true}
        />
      );

      const announcement = screen.getByText(/processing 1 of 3 files/i);
      expect(announcement).toBeInTheDocument();
      expect(announcement).toHaveAttribute('aria-live', 'polite');
    });

    it('provides screen reader announcements for completion', () => {
      const completedFiles = sampleFiles.map(f => ({ ...f, status: 'complete' }));

      render(
        <UploadProgress
          files={completedFiles}
          overallProgress={100}
          isActive={false}
        />
      );

      const announcement = screen.getByText(/all 3 files processed successfully/i);
      expect(announcement).toBeInTheDocument();
    });

    it('provides screen reader announcements for errors', () => {
      render(
        <UploadProgress
          files={sampleFiles}
          overallProgress={50}
          isActive={false}
        />
      );

      const announcement = screen.getByText(/processing completed.*1 successful.*1 failed/i);
      expect(announcement).toBeInTheDocument();
    });
  });

  describe('File Expansion', () => {
    it('auto-expands failed files', async () => {
      render(
        <UploadProgress
          files={sampleFiles}
          overallProgress={50}
          isActive={false}
          showDetails={true}
        />
      );

      // Failed file should be auto-expanded
      await waitFor(() => {
        expect(screen.getByTestId('toggle-file3')).toHaveTextContent('Collapse');
      });
    });

    it('handles file expansion toggle', () => {
      render(
        <UploadProgress
          files={sampleFiles}
          overallProgress={50}
          isActive={false}
          showDetails={true}
        />
      );

      const toggleButton = screen.getByTestId('toggle-file1');
      expect(toggleButton).toHaveTextContent('Expand');

      fireEvent.click(toggleButton);
      expect(toggleButton).toHaveTextContent('Collapse');
    });
  });
});
