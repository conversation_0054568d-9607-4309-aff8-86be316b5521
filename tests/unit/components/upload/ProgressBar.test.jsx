import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect, beforeEach } from 'vitest';
import ProgressBar from '../../../../src/components/upload/ProgressBar.jsx';

describe('ProgressBar Component', () => {
  beforeEach(() => {
    // Clear any previous renders
  });

  describe('Basic Rendering', () => {
    it('renders with default props', () => {
      render(<ProgressBar />);

      const progressBar = screen.getByRole('progressbar');
      expect(progressBar).toBeInTheDocument();
      expect(progressBar).toHaveAttribute('aria-valuenow', '0');
      expect(progressBar).toHaveAttribute('aria-valuemin', '0');
      expect(progressBar).toHaveAttribute('aria-valuemax', '100');
    });

    it('renders with custom progress value', () => {
      render(<ProgressBar progress={75} />);

      const progressBar = screen.getByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-valuenow', '75');
    });

    it('clamps progress value to valid range', () => {
      const { rerender } = render(<ProgressBar progress={-10} />);
      let progressBar = screen.getByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-valuenow', '0');

      rerender(<ProgressBar progress={150} />);
      progressBar = screen.getByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-valuenow', '100');
    });
  });

  describe('Variants', () => {
    it('applies default variant classes', () => {
      render(<ProgressBar progress={50} />);

      const container = screen.getByRole('progressbar').parentElement;
      expect(container).toHaveClass('bg-gray-200');

      const fill = container.querySelector('div[style*="width: 50%"]');
      expect(fill).toHaveClass('bg-blue-600');
    });

    it('applies success variant classes', () => {
      render(<ProgressBar progress={100} variant="success" />);

      const container = screen.getByRole('progressbar').parentElement;
      expect(container).toHaveClass('bg-green-100');

      const fill = container.querySelector('div[style*="width: 100%"]');
      expect(fill).toHaveClass('bg-green-600');
    });

    it('applies error variant classes', () => {
      render(<ProgressBar progress={0} variant="error" />);

      const container = screen.getByRole('progressbar').parentElement;
      expect(container).toHaveClass('bg-red-100');
    });

    it('applies warning variant classes', () => {
      render(<ProgressBar progress={75} variant="warning" />);

      const container = screen.getByRole('progressbar').parentElement;
      expect(container).toHaveClass('bg-yellow-100');

      const fill = container.querySelector('div[style*="width: 75%"]');
      expect(fill).toHaveClass('bg-yellow-500');
    });

    it('applies active variant classes', () => {
      render(<ProgressBar progress={50} variant="active" />);

      const container = screen.getByRole('progressbar').parentElement;
      expect(container).toHaveClass('bg-blue-100');

      const fill = container.querySelector('div[style*="width: 50%"]');
      expect(fill).toHaveClass('bg-blue-600');
    });

    it('applies gradient variant classes', () => {
      render(<ProgressBar progress={60} variant="gradient" />);

      const fill = screen.getByRole('progressbar').parentElement.querySelector('div[style*="width: 60%"]');
      expect(fill).toHaveClass('bg-gradient-to-r', 'from-blue-500', 'to-purple-600');
    });
  });

  describe('Sizes', () => {
    it('applies default medium size', () => {
      render(<ProgressBar />);

      const container = screen.getByRole('progressbar').parentElement;
      expect(container).toHaveClass('h-3');
    });

    it('applies extra small size', () => {
      render(<ProgressBar size="xs" />);

      const container = screen.getByRole('progressbar').parentElement;
      expect(container).toHaveClass('h-1');
    });

    it('applies small size', () => {
      render(<ProgressBar size="sm" />);

      const container = screen.getByRole('progressbar').parentElement;
      expect(container).toHaveClass('h-2');
    });

    it('applies large size', () => {
      render(<ProgressBar size="lg" />);

      const container = screen.getByRole('progressbar').parentElement;
      expect(container).toHaveClass('h-4');
    });

    it('applies extra large size', () => {
      render(<ProgressBar size="xl" />);

      const container = screen.getByRole('progressbar').parentElement;
      expect(container).toHaveClass('h-6');
    });
  });

  describe('Percentage Display', () => {
    it('shows percentage when showPercentage is true', () => {
      render(<ProgressBar progress={75} showPercentage={true} />);

      expect(screen.getByText('75%')).toBeInTheDocument();
    });

    it('does not show percentage by default', () => {
      render(<ProgressBar progress={75} />);

      expect(screen.queryByText('75%')).not.toBeInTheDocument();
    });

    it('rounds percentage to nearest integer', () => {
      render(<ProgressBar progress={75.7} showPercentage={true} />);

      expect(screen.getByText('76%')).toBeInTheDocument();
    });
  });

  describe('Label', () => {
    it('shows label when provided', () => {
      render(<ProgressBar progress={50} label="Upload Progress" />);

      expect(screen.getByText('Upload Progress')).toBeInTheDocument();

      const progressBar = screen.getByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-labelledby');
    });

    it('shows both label and percentage', () => {
      render(
        <ProgressBar
          progress={60}
          label="Processing Files"
          showPercentage={true}
        />
      );

      expect(screen.getByText('Processing Files')).toBeInTheDocument();
      expect(screen.getByText('60%')).toBeInTheDocument();
    });

    it('uses aria-label when no label provided', () => {
      render(<ProgressBar progress={40} />);

      const progressBar = screen.getByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-label', 'Progress: 40%');
    });
  });

  describe('Animation', () => {
    it('applies animation classes by default', () => {
      render(<ProgressBar progress={50} />);

      const fill = screen.getByRole('progressbar').parentElement.querySelector('div[style*="width: 50%"]');
      expect(fill).toHaveClass('transition-all', 'duration-300', 'ease-out');
    });

    it('does not apply animation classes when animated is false', () => {
      render(<ProgressBar progress={50} animated={false} />);

      const fill = screen.getByRole('progressbar').parentElement.querySelector('div[style*="width: 50%"]');
      expect(fill).not.toHaveClass('transition-all');
    });
  });

  describe('Striped Pattern', () => {
    it('applies striped classes when striped is true', () => {
      render(<ProgressBar progress={50} striped={true} />);

      const fill = screen.getByRole('progressbar').parentElement.querySelector('div[style*="width: 50%"]');
      expect(fill).toHaveClass('bg-stripes');
    });

    it('does not apply striped classes by default', () => {
      render(<ProgressBar progress={50} />);

      const fill = screen.getByRole('progressbar').parentElement.querySelector('div[style*="width: 50%"]');
      expect(fill).not.toHaveClass('bg-stripes');
    });
  });

  describe('Status Messages', () => {
    it('shows error message for error variant with 0 progress', () => {
      render(<ProgressBar progress={0} variant="error" />);

      expect(screen.getByText('Processing failed')).toBeInTheDocument();
    });

    it('shows success message for success variant with 100% progress', () => {
      render(<ProgressBar progress={100} variant="success" />);

      expect(screen.getByText('Completed successfully')).toBeInTheDocument();
    });

    it('does not show status messages for other variants', () => {
      render(<ProgressBar progress={50} variant="default" />);

      expect(screen.queryByText('Processing failed')).not.toBeInTheDocument();
      expect(screen.queryByText('Completed successfully')).not.toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('provides screen reader announcements for different states', () => {
      const { rerender } = render(<ProgressBar progress={0} variant="active" />);

      let announcement = screen.getByText('Processing started');
      expect(announcement).toBeInTheDocument();
      expect(announcement).toHaveAttribute('aria-live', 'polite');

      rerender(<ProgressBar progress={50} variant="active" />);
      announcement = screen.getByText('50% complete');
      expect(announcement).toBeInTheDocument();

      rerender(<ProgressBar progress={100} variant="success" />);
      announcement = screen.getByText('Processing completed');
      expect(announcement).toBeInTheDocument();

      rerender(<ProgressBar progress={0} variant="error" />);
      announcement = screen.getByText('Processing failed');
      expect(announcement).toBeInTheDocument();
    });

    it('has proper ARIA attributes', () => {
      render(<ProgressBar progress={75} label="File Upload" />);

      const progressBar = screen.getByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-valuenow', '75');
      expect(progressBar).toHaveAttribute('aria-valuemin', '0');
      expect(progressBar).toHaveAttribute('aria-valuemax', '100');
      expect(progressBar).toHaveAttribute('aria-labelledby');
    });
  });

  describe('Custom Props', () => {
    it('passes through additional props', () => {
      render(<ProgressBar progress={50} data-testid="custom-progress" />);

      expect(screen.getByTestId('custom-progress')).toBeInTheDocument();
    });

    it('applies custom className', () => {
      render(<ProgressBar progress={50} className="custom-class" />);

      const container = screen.getByRole('progressbar').closest('.custom-class');
      expect(container).toBeInTheDocument();
    });
  });

  describe('Progress Fill Styles', () => {
    it('sets correct width style for progress fill', () => {
      render(<ProgressBar progress={75} />);

      const fill = screen.getByRole('progressbar').parentElement.querySelector('div[style*="width: 75%"]');
      expect(fill).toBeInTheDocument();
      expect(fill).toHaveStyle('width: 75%');
    });

    it('applies translateX(-100%) for 0% progress', () => {
      render(<ProgressBar progress={0} />);

      const fill = screen.getByRole('progressbar').parentElement.querySelector('div[style*="translateX(-100%)"]');
      expect(fill).toBeInTheDocument();
    });

    it('applies translateX(0) for non-zero progress', () => {
      render(<ProgressBar progress={50} />);

      const fill = screen.getByRole('progressbar').parentElement.querySelector('div[style*="translateX(0)"]');
      expect(fill).toBeInTheDocument();
    });
  });
});
