/**
 * Unit Tests for DragDropUpload Component
 * Comprehensive testing of drag & drop functionality, validation, and accessibility
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import DragDropUpload from '../../../../src/popup/components/upload/DragDropUpload.jsx';

// Mock dependencies
vi.mock('react-dropzone', () => ({
  useDropzone: vi.fn()
}));

vi.mock('pretty-bytes', () => ({
  default: vi.fn((bytes) => `${bytes} B`)
}));

vi.mock('../../../../src/popup/utils/fileValidation.js', () => ({
  validateFile: vi.fn()
}));

// Import mocked modules
import { useDropzone } from 'react-dropzone';
import prettyBytes from 'pretty-bytes';
import { validateFile } from '../../../../src/popup/utils/fileValidation.js';

describe('DragDropUpload Component', () => {
  const mockOnFilesSelected = vi.fn();
  const mockOnUploadProgress = vi.fn();
  const mockOnError = vi.fn();

  const defaultProps = {
    onFilesSelected: mockOnFilesSelected,
    onUploadProgress: mockOnUploadProgress,
    onError: mockOnError,
    maxFiles: 5,
    maxSize: 5 * 1024 * 1024, // 5MB
    acceptedTypes: {
      'application/pdf': ['.pdf'],
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png']
    }
  };

  const mockDropzoneProps = {
    getRootProps: vi.fn(() => ({ 'data-testid': 'dropzone' })),
    getInputProps: vi.fn(() => ({ 'data-testid': 'file-input' })),
    isDragActive: false,
    isDragAccept: false,
    isDragReject: false,
    open: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
    useDropzone.mockReturnValue(mockDropzoneProps);
    prettyBytes.mockImplementation((bytes) => `${(bytes / 1024 / 1024).toFixed(1)} MB`);
    validateFile.mockReturnValue({ isValid: true, errors: [], warnings: [] });
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('Rendering', () => {
    it('renders the upload component with default state', () => {
      render(<DragDropUpload {...defaultProps} />);

      expect(screen.getByText('Drag & drop files here')).toBeInTheDocument();
      expect(screen.getByText('click to select files')).toBeInTheDocument();
      expect(screen.getByText('Supported formats: .PDF, .JPG, .JPEG, .PNG')).toBeInTheDocument();
      expect(screen.getByText('Maximum file size: 5.0 MB')).toBeInTheDocument();
      expect(screen.getByText('Maximum files: 5')).toBeInTheDocument();
    });

    it('renders with custom className', () => {
      const customClass = 'custom-upload-class';
      render(<DragDropUpload {...defaultProps} className={customClass} />);

      const dropzone = screen.getByTestId('dropzone');
      expect(dropzone).toHaveClass(customClass);
    });

    it('renders disabled state correctly', () => {
      render(<DragDropUpload {...defaultProps} disabled={true} />);

      const dropzone = screen.getByTestId('dropzone');
      expect(dropzone).toHaveClass('cursor-not-allowed');
    });
  });

  describe('Drag and Drop States', () => {
    it('shows active drag state', () => {
      useDropzone.mockReturnValue({
        ...mockDropzoneProps,
        isDragActive: true
      });

      render(<DragDropUpload {...defaultProps} />);

      expect(screen.getByText('Drop files here')).toBeInTheDocument();
      expect(screen.getByText('📥')).toBeInTheDocument();
    });

    it('shows accept drag state', () => {
      useDropzone.mockReturnValue({
        ...mockDropzoneProps,
        isDragAccept: true
      });

      render(<DragDropUpload {...defaultProps} />);

      const dropzone = screen.getByTestId('dropzone');
      expect(dropzone).toHaveClass('border-green-300', 'bg-green-50', 'text-green-700');
    });

    it('shows reject drag state', () => {
      useDropzone.mockReturnValue({
        ...mockDropzoneProps,
        isDragReject: true
      });

      render(<DragDropUpload {...defaultProps} />);

      const dropzone = screen.getByTestId('dropzone');
      expect(dropzone).toHaveClass('border-red-300', 'bg-red-50', 'text-red-700');
    });
  });

  describe('File Selection', () => {
    it('calls open function when click to select is clicked', async () => {
      const user = userEvent.setup();
      render(<DragDropUpload {...defaultProps} />);

      const selectButton = screen.getByText('click to select files');
      await user.click(selectButton);

      expect(mockDropzoneProps.open).toHaveBeenCalledTimes(1);
    });

    it('does not call open when disabled', async () => {
      const user = userEvent.setup();
      render(<DragDropUpload {...defaultProps} disabled={true} />);

      const selectButton = screen.getByText('click to select files');
      expect(selectButton).toBeDisabled();
    });
  });

  describe('File Validation', () => {
    it('handles valid files correctly', async () => {
      const mockFiles = [
        new File(['content'], 'test.pdf', { type: 'application/pdf' })
      ];

      validateFile.mockReturnValue({
        isValid: true,
        fileName: 'test.pdf',
        errors: [],
        warnings: []
      });

      const mockOnDrop = vi.fn();
      useDropzone.mockReturnValue({
        ...mockDropzoneProps,
        onDrop: mockOnDrop
      });

      render(<DragDropUpload {...defaultProps} />);

      // Simulate the onDrop callback that would be called by react-dropzone
      const onDropCallback = useDropzone.mock.calls[0][0].onDrop;
      await onDropCallback(mockFiles, []);

      expect(validateFile).toHaveBeenCalledWith(mockFiles[0], {
        maxSize: defaultProps.maxSize,
        acceptedTypes: Object.keys(defaultProps.acceptedTypes)
      });

      expect(mockOnFilesSelected).toHaveBeenCalledWith(mockFiles);
    });

    it('handles rejected files correctly', async () => {
      const mockRejectedFiles = [
        {
          file: new File(['content'], 'test.txt', { type: 'text/plain' }),
          errors: [{ message: 'File type not accepted' }]
        }
      ];

      render(<DragDropUpload {...defaultProps} />);

      const onDropCallback = useDropzone.mock.calls[0][0].onDrop;
      await onDropCallback([], mockRejectedFiles);

      await waitFor(() => {
        expect(screen.getByText('Upload Error')).toBeInTheDocument();
        expect(screen.getByText(/File validation failed/)).toBeInTheDocument();
      });

      expect(mockOnError).toHaveBeenCalledWith(
        expect.stringContaining('File validation failed')
      );
    });

    it('handles validation errors for accepted files', async () => {
      const mockFiles = [
        new File(['content'], 'test.pdf', { type: 'application/pdf' })
      ];

      validateFile.mockReturnValue({
        isValid: false,
        fileName: 'test.pdf',
        errors: ['File is too large'],
        warnings: []
      });

      render(<DragDropUpload {...defaultProps} />);

      const onDropCallback = useDropzone.mock.calls[0][0].onDrop;
      await onDropCallback(mockFiles, []);

      await waitFor(() => {
        expect(screen.getByText('Upload Error')).toBeInTheDocument();
        expect(screen.getByText(/File validation failed/)).toBeInTheDocument();
      });

      expect(mockOnError).toHaveBeenCalled();
      expect(mockOnFilesSelected).not.toHaveBeenCalled();
    });
  });

  describe('Upload Progress', () => {
    it('shows upload progress during file processing', async () => {
      const mockFiles = [
        new File(['content'], 'test.pdf', { type: 'application/pdf' })
      ];

      validateFile.mockReturnValue({
        isValid: true,
        fileName: 'test.pdf',
        errors: [],
        warnings: []
      });

      // Mock onFilesSelected to simulate async processing
      mockOnFilesSelected.mockImplementation(async () => {
        // Simulate processing time
        await new Promise(resolve => setTimeout(resolve, 100));
      });

      render(<DragDropUpload {...defaultProps} />);

      const onDropCallback = useDropzone.mock.calls[0][0].onDrop;

      // Start the upload
      const uploadPromise = onDropCallback(mockFiles, []);

      // Check that progress is shown
      await waitFor(() => {
        expect(screen.getByText('Uploading')).toBeInTheDocument();
      });

      // Wait for upload to complete
      await uploadPromise;

      // Check success state
      await waitFor(() => {
        expect(screen.getByText('Files uploaded successfully!')).toBeInTheDocument();
      });
    });

    it('handles upload errors correctly', async () => {
      const mockFiles = [
        new File(['content'], 'test.pdf', { type: 'application/pdf' })
      ];

      validateFile.mockReturnValue({
        isValid: true,
        fileName: 'test.pdf',
        errors: [],
        warnings: []
      });

      mockOnFilesSelected.mockRejectedValue(new Error('Upload failed'));

      render(<DragDropUpload {...defaultProps} />);

      const onDropCallback = useDropzone.mock.calls[0][0].onDrop;
      await onDropCallback(mockFiles, []);

      await waitFor(() => {
        expect(screen.getByText('Upload Error')).toBeInTheDocument();
        expect(screen.getByText('Upload failed')).toBeInTheDocument();
      });

      expect(mockOnError).toHaveBeenCalledWith('Upload failed');
    });
  });

  describe('Error Handling', () => {
    it('displays error messages correctly', async () => {
      const errorMessage = 'Test error message';

      render(<DragDropUpload {...defaultProps} />);

      // Simulate an error by calling onDrop with rejected files
      const mockRejectedFiles = [
        {
          file: new File(['content'], 'test.txt', { type: 'text/plain' }),
          errors: [{ message: errorMessage }]
        }
      ];

      const onDropCallback = useDropzone.mock.calls[0][0].onDrop;
      await onDropCallback([], mockRejectedFiles);

      await waitFor(() => {
        expect(screen.getByText('Upload Error')).toBeInTheDocument();
        expect(screen.getByText(/File validation failed/)).toBeInTheDocument();
      });
    });

    it('allows dismissing error messages', async () => {
      const user = userEvent.setup();

      render(<DragDropUpload {...defaultProps} />);

      // Trigger an error
      const mockRejectedFiles = [
        {
          file: new File(['content'], 'test.txt', { type: 'text/plain' }),
          errors: [{ message: 'Test error' }]
        }
      ];

      const onDropCallback = useDropzone.mock.calls[0][0].onDrop;
      await onDropCallback([], mockRejectedFiles);

      await waitFor(() => {
        expect(screen.getByText('Upload Error')).toBeInTheDocument();
      });

      // Dismiss the error
      const dismissButton = screen.getByText('Dismiss');
      await user.click(dismissButton);

      await waitFor(() => {
        expect(screen.queryByText('Upload Error')).not.toBeInTheDocument();
      });
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels and roles', () => {
      render(<DragDropUpload {...defaultProps} />);

      const dropzone = screen.getByTestId('dropzone');
      expect(dropzone).toHaveAttribute('aria-label',
        expect.stringContaining('Upload files. Accepted formats:')
      );
      expect(dropzone).toHaveAttribute('tabIndex', '0');
    });

    it('has proper aria-describedby attributes', () => {
      render(<DragDropUpload {...defaultProps} />);

      const dropzone = screen.getByTestId('dropzone');
      expect(dropzone).toHaveAttribute('aria-describedby',
        expect.stringContaining('upload-instructions')
      );
    });

    it('announces errors with proper ARIA live regions', async () => {
      render(<DragDropUpload {...defaultProps} />);

      // Trigger an error
      const mockRejectedFiles = [
        {
          file: new File(['content'], 'test.txt', { type: 'text/plain' }),
          errors: [{ message: 'Test error' }]
        }
      ];

      const onDropCallback = useDropzone.mock.calls[0][0].onDrop;
      await onDropCallback([], mockRejectedFiles);

      await waitFor(() => {
        const errorRegion = screen.getByRole('alert');
        expect(errorRegion).toHaveAttribute('aria-live', 'polite');
      });
    });
  });

  describe('Configuration', () => {
    it('respects maxFiles configuration', () => {
      const customMaxFiles = 3;
      render(<DragDropUpload {...defaultProps} maxFiles={customMaxFiles} />);

      expect(screen.getByText(`Maximum files: ${customMaxFiles}`)).toBeInTheDocument();

      // Check that useDropzone was called with correct maxFiles
      expect(useDropzone).toHaveBeenCalledWith(
        expect.objectContaining({ maxFiles: customMaxFiles })
      );
    });

    it('respects maxSize configuration', () => {
      const customMaxSize = 2 * 1024 * 1024; // 2MB
      prettyBytes.mockReturnValue('2.0 MB');

      render(<DragDropUpload {...defaultProps} maxSize={customMaxSize} />);

      expect(screen.getByText('Maximum file size: 2.0 MB')).toBeInTheDocument();

      // Check that useDropzone was called with correct maxSize
      expect(useDropzone).toHaveBeenCalledWith(
        expect.objectContaining({ maxSize: customMaxSize })
      );
    });

    it('respects acceptedTypes configuration', () => {
      const customAcceptedTypes = {
        'application/pdf': ['.pdf']
      };

      render(<DragDropUpload {...defaultProps} acceptedTypes={customAcceptedTypes} />);

      expect(screen.getByText('Supported formats: .PDF')).toBeInTheDocument();

      // Check that useDropzone was called with correct accept types
      expect(useDropzone).toHaveBeenCalledWith(
        expect.objectContaining({ accept: customAcceptedTypes })
      );
    });
  });
});
