import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ProcessingPreferences } from '../../../../src/components/settings/ProcessingPreferences.jsx';

describe('ProcessingPreferences', () => {
  const defaultProcessingData = {
    ocrLanguage: 'pol',
    ocrQuality: 'balanced',
    aiProvider: 'deepseek',
    analysisDepth: 'standard',
    autoProcess: true,
    cacheEnabled: true,
    batchSize: 5,
    timeoutSeconds: 30,
    retryAttempts: 3
  };

  const mockOnChange = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render main component structure', () => {
      render(
        <ProcessingPreferences
          processingData={defaultProcessingData}
          onChange={mockOnChange}
        />
      );

      expect(screen.getByText('Processing Preferences')).toBeInTheDocument();
      expect(screen.getByText('OCR (Text Recognition)')).toBeInTheDocument();
      expect(screen.getByText('AI Analysis')).toBeInTheDocument();
      expect(screen.getByText('Processing Options')).toBeInTheDocument();
    });

    it('should render OCR language options', () => {
      render(
        <ProcessingPreferences
          processingData={defaultProcessingData}
          onChange={mockOnChange}
        />
      );

      expect(screen.getByText('OCR Language')).toBeInTheDocument();
      expect(screen.getByLabelText('Polish')).toBeInTheDocument();
      expect(screen.getByLabelText('English')).toBeInTheDocument();
      expect(screen.getByLabelText('Polish + English')).toBeInTheDocument();
    });

    it('should render OCR quality options', () => {
      render(
        <ProcessingPreferences
          processingData={defaultProcessingData}
          onChange={mockOnChange}
        />
      );

      expect(screen.getByText('OCR Quality Level')).toBeInTheDocument();
      expect(screen.getByLabelText('Fast')).toBeInTheDocument();
      expect(screen.getByLabelText('Balanced')).toBeInTheDocument();
      expect(screen.getByLabelText('High Quality')).toBeInTheDocument();
    });

    it('should render AI provider options', () => {
      render(
        <ProcessingPreferences
          processingData={defaultProcessingData}
          onChange={mockOnChange}
        />
      );

      expect(screen.getByText('AI Provider')).toBeInTheDocument();
      expect(screen.getByLabelText('DeepSeek')).toBeInTheDocument();
      expect(screen.getByLabelText('OpenAI')).toBeInTheDocument();
    });

    it('should render analysis depth options', () => {
      render(
        <ProcessingPreferences
          processingData={defaultProcessingData}
          onChange={mockOnChange}
        />
      );

      expect(screen.getByText('Analysis Depth')).toBeInTheDocument();
      expect(screen.getByLabelText('Basic')).toBeInTheDocument();
      expect(screen.getByLabelText('Standard')).toBeInTheDocument();
      expect(screen.getByLabelText('Comprehensive')).toBeInTheDocument();
    });

    it('should render processing options', () => {
      render(
        <ProcessingPreferences
          processingData={defaultProcessingData}
          onChange={mockOnChange}
        />
      );

      expect(screen.getByLabelText('Auto-process uploaded documents')).toBeInTheDocument();
      expect(screen.getByLabelText(/Enable result caching/)).toBeInTheDocument();
      expect(screen.getByLabelText('Batch Size')).toBeInTheDocument();
      expect(screen.getByLabelText('Timeout (seconds)')).toBeInTheDocument();
      expect(screen.getByLabelText('Retry Attempts')).toBeInTheDocument();
    });
  });

  describe('Data Management', () => {
    it('should load initial data correctly', () => {
      render(
        <ProcessingPreferences
          processingData={defaultProcessingData}
          onChange={mockOnChange}
        />
      );

      expect(screen.getByLabelText('Polish')).toBeChecked();
      expect(screen.getByLabelText('Balanced')).toBeChecked();
      expect(screen.getByLabelText('DeepSeek')).toBeChecked();
      expect(screen.getByLabelText('Standard')).toBeChecked();
      expect(screen.getByLabelText('Auto-process uploaded documents')).toBeChecked();
      expect(screen.getByLabelText(/Enable result caching/)).toBeChecked();
    });

    it('should handle OCR language changes', () => {
      render(
        <ProcessingPreferences
          processingData={defaultProcessingData}
          onChange={mockOnChange}
        />
      );

      const englishRadio = screen.getByLabelText('English');
      fireEvent.click(englishRadio);

      expect(mockOnChange).toHaveBeenCalledWith(
        expect.objectContaining({ ocrLanguage: 'eng' })
      );
    });

    it('should handle OCR quality changes', () => {
      render(
        <ProcessingPreferences
          processingData={defaultProcessingData}
          onChange={mockOnChange}
        />
      );

      const highQualityRadio = screen.getByLabelText('High Quality');
      fireEvent.click(highQualityRadio);

      expect(mockOnChange).toHaveBeenCalledWith(
        expect.objectContaining({ ocrQuality: 'high' })
      );
    });

    it('should handle AI provider changes', () => {
      render(
        <ProcessingPreferences
          processingData={defaultProcessingData}
          onChange={mockOnChange}
        />
      );

      const openaiRadio = screen.getByLabelText('OpenAI');
      fireEvent.click(openaiRadio);

      expect(mockOnChange).toHaveBeenCalledWith(
        expect.objectContaining({ aiProvider: 'openai' })
      );
    });

    it('should handle analysis depth changes', () => {
      render(
        <ProcessingPreferences
          processingData={defaultProcessingData}
          onChange={mockOnChange}
        />
      );

      const comprehensiveRadio = screen.getByLabelText('Comprehensive');
      fireEvent.click(comprehensiveRadio);

      expect(mockOnChange).toHaveBeenCalledWith(
        expect.objectContaining({ analysisDepth: 'comprehensive' })
      );
    });

    it('should handle auto-process toggle', () => {
      render(
        <ProcessingPreferences
          processingData={defaultProcessingData}
          onChange={mockOnChange}
        />
      );

      const autoProcessCheckbox = screen.getByLabelText('Auto-process uploaded documents');
      fireEvent.click(autoProcessCheckbox);

      expect(mockOnChange).toHaveBeenCalledWith(
        expect.objectContaining({ autoProcess: false })
      );
    });

    it('should handle cache toggle', () => {
      render(
        <ProcessingPreferences
          processingData={defaultProcessingData}
          onChange={mockOnChange}
        />
      );

      const cacheCheckbox = screen.getByLabelText(/Enable result caching/);
      fireEvent.click(cacheCheckbox);

      expect(mockOnChange).toHaveBeenCalledWith(
        expect.objectContaining({ cacheEnabled: false })
      );
    });

    it('should handle batch size changes', () => {
      render(
        <ProcessingPreferences
          processingData={defaultProcessingData}
          onChange={mockOnChange}
        />
      );

      const batchSizeSelect = screen.getByLabelText('Batch Size');
      fireEvent.change(batchSizeSelect, { target: { value: '10' } });

      expect(mockOnChange).toHaveBeenCalledWith(
        expect.objectContaining({ batchSize: 10 })
      );
    });

    it('should handle timeout changes', () => {
      render(
        <ProcessingPreferences
          processingData={defaultProcessingData}
          onChange={mockOnChange}
        />
      );

      const timeoutSelect = screen.getByLabelText('Timeout (seconds)');
      fireEvent.change(timeoutSelect, { target: { value: '60' } });

      expect(mockOnChange).toHaveBeenCalledWith(
        expect.objectContaining({ timeoutSeconds: 60 })
      );
    });

    it('should handle retry attempts changes', () => {
      render(
        <ProcessingPreferences
          processingData={defaultProcessingData}
          onChange={mockOnChange}
        />
      );

      const retrySelect = screen.getByLabelText('Retry Attempts');
      fireEvent.change(retrySelect, { target: { value: '5' } });

      expect(mockOnChange).toHaveBeenCalledWith(
        expect.objectContaining({ retryAttempts: 5 })
      );
    });
  });

  describe('Configuration Summary', () => {
    it('should display configuration summary correctly', () => {
      render(
        <ProcessingPreferences
          processingData={defaultProcessingData}
          onChange={mockOnChange}
        />
      );

      expect(screen.getByText('Processing Configuration')).toBeInTheDocument();
      expect(screen.getByText('pol / balanced')).toBeInTheDocument();
      expect(screen.getByText('deepseek / standard')).toBeInTheDocument();
      expect(screen.getByText('Enabled')).toBeInTheDocument(); // Auto-process
    });

    it('should update summary when settings change', () => {
      const { rerender } = render(
        <ProcessingPreferences
          processingData={defaultProcessingData}
          onChange={mockOnChange}
        />
      );

      // Change settings and rerender
      const updatedData = {
        ...defaultProcessingData,
        ocrLanguage: 'eng',
        ocrQuality: 'high',
        aiProvider: 'openai',
        analysisDepth: 'comprehensive',
        autoProcess: false
      };

      rerender(
        <ProcessingPreferences
          processingData={updatedData}
          onChange={mockOnChange}
        />
      );

      expect(screen.getByText('eng / high')).toBeInTheDocument();
      expect(screen.getByText('openai / comprehensive')).toBeInTheDocument();
      expect(screen.getByText('Disabled')).toBeInTheDocument(); // Auto-process
    });
  });

  describe('Disabled State', () => {
    it('should disable all inputs when disabled prop is true', () => {
      render(
        <ProcessingPreferences
          processingData={defaultProcessingData}
          onChange={mockOnChange}
          disabled={true}
        />
      );

      expect(screen.getByLabelText('Polish')).toBeDisabled();
      expect(screen.getByLabelText('Balanced')).toBeDisabled();
      expect(screen.getByLabelText('DeepSeek')).toBeDisabled();
      expect(screen.getByLabelText('Standard')).toBeDisabled();
      expect(screen.getByLabelText('Auto-process uploaded documents')).toBeDisabled();
      expect(screen.getByLabelText(/Enable result caching/)).toBeDisabled();
      expect(screen.getByLabelText('Batch Size')).toBeDisabled();
      expect(screen.getByLabelText('Timeout (seconds)')).toBeDisabled();
      expect(screen.getByLabelText('Retry Attempts')).toBeDisabled();
    });

    it('should not call onChange when disabled', () => {
      render(
        <ProcessingPreferences
          processingData={defaultProcessingData}
          onChange={mockOnChange}
          disabled={true}
        />
      );

      const englishRadio = screen.getByLabelText('English');
      fireEvent.click(englishRadio);

      // Should not be called because component is disabled
      expect(mockOnChange).not.toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('should display error messages when provided', () => {
      const errors = {
        ocrLanguage: 'OCR language is required',
        aiProvider: 'AI provider is required'
      };

      render(
        <ProcessingPreferences
          processingData={defaultProcessingData}
          onChange={mockOnChange}
          errors={errors}
        />
      );

      expect(screen.getByText('OCR language is required')).toBeInTheDocument();
      expect(screen.getByText('AI provider is required')).toBeInTheDocument();
    });
  });

  describe('Default Values', () => {
    it('should handle empty processingData gracefully', () => {
      render(
        <ProcessingPreferences
          processingData={{}}
          onChange={mockOnChange}
        />
      );

      // Should render with default values
      expect(screen.getByLabelText('Polish')).toBeChecked();
      expect(screen.getByLabelText('Balanced')).toBeChecked();
      expect(screen.getByLabelText('DeepSeek')).toBeChecked();
      expect(screen.getByLabelText('Standard')).toBeChecked();
    });

    it('should merge provided data with defaults', () => {
      const partialData = {
        ocrLanguage: 'eng',
        aiProvider: 'openai',
        autoProcess: false
      };

      render(
        <ProcessingPreferences
          processingData={partialData}
          onChange={mockOnChange}
        />
      );

      expect(screen.getByLabelText('English')).toBeChecked();
      expect(screen.getByLabelText('OpenAI')).toBeChecked();
      expect(screen.getByLabelText('Auto-process uploaded documents')).not.toBeChecked();
      expect(screen.getByLabelText('Balanced')).toBeChecked(); // Default
      expect(screen.getByLabelText('Standard')).toBeChecked(); // Default
    });
  });

  describe('Feature Information', () => {
    it('should display OCR quality information', () => {
      render(
        <ProcessingPreferences
          processingData={defaultProcessingData}
          onChange={mockOnChange}
        />
      );

      expect(screen.getByText('~2-5 seconds')).toBeInTheDocument(); // Fast processing time
      expect(screen.getByText('~5-10 seconds')).toBeInTheDocument(); // Balanced processing time
      expect(screen.getByText('~10-20 seconds')).toBeInTheDocument(); // High quality processing time

      expect(screen.getByText('85-90%')).toBeInTheDocument(); // Fast accuracy
      expect(screen.getByText('90-95%')).toBeInTheDocument(); // Balanced accuracy
      expect(screen.getByText('95-98%')).toBeInTheDocument(); // High quality accuracy
    });

    it('should display AI provider features', () => {
      render(
        <ProcessingPreferences
          processingData={defaultProcessingData}
          onChange={mockOnChange}
        />
      );

      expect(screen.getByText('VAT extraction')).toBeInTheDocument();
      expect(screen.getByText('Invoice parsing')).toBeInTheDocument();
      expect(screen.getByText('Data validation')).toBeInTheDocument();
      expect(screen.getByText('Natural language processing')).toBeInTheDocument();
      expect(screen.getByText('Complex document analysis')).toBeInTheDocument();
    });

    it('should display analysis depth features', () => {
      render(
        <ProcessingPreferences
          processingData={defaultProcessingData}
          onChange={mockOnChange}
        />
      );

      expect(screen.getByText('VAT numbers')).toBeInTheDocument();
      expect(screen.getByText('Amounts')).toBeInTheDocument();
      expect(screen.getByText('Dates')).toBeInTheDocument();
      expect(screen.getByText('All basic fields')).toBeInTheDocument();
      expect(screen.getByText('Company details')).toBeInTheDocument();
      expect(screen.getByText('Line items')).toBeInTheDocument();
      expect(screen.getByText('All standard fields')).toBeInTheDocument();
      expect(screen.getByText('Anomaly detection')).toBeInTheDocument();
    });
  });
});
