import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { CompanyProfileSettings } from '../../../../src/components/settings/CompanyProfileSettings.jsx';

// Mock the useSettings hook
const mockUpdateSection = vi.fn();
const mockSettings = {
  company: {
    name: 'Test Company',
    taxId: '123-456-78-90',
    address: 'Test Address',
    email: '<EMAIL>',
    phone: '+48 ***********',
    logo: ''
  },
  display: {
    currency: 'PLN'
  }
};

vi.mock('../../../../src/popup/hooks/useSettings.js', () => ({
  useSettings: () => ({
    settings: mockSettings,
    updateSection: mockUpdateSection,
    isLoading: false
  })
}));

// Mock the sub-components
vi.mock('../../../../src/components/settings/CompanyInformation.jsx', () => ({
  CompanyInformation: ({ companyData, onChange, errors, disabled }) => (
    <div data-testid="company-information">
      <input
        data-testid="company-name"
        value={companyData.name || ''}
        onChange={(e) => onChange({ ...companyData, name: e.target.value })}
        disabled={disabled}
      />
      {errors.name && <span data-testid="name-error">{errors.name}</span>}
    </div>
  )
}));

vi.mock('../../../../src/components/settings/BusinessConfiguration.jsx', () => ({
  BusinessConfiguration: ({ businessData, onChange, errors, disabled }) => (
    <div data-testid="business-configuration">
      <select
        data-testid="currency-select"
        value={businessData.currency || 'PLN'}
        onChange={(e) => onChange({ ...businessData, currency: e.target.value })}
        disabled={disabled}
      >
        <option value="PLN">PLN</option>
        <option value="EUR">EUR</option>
        <option value="USD">USD</option>
      </select>
      {errors.currency && <span data-testid="currency-error">{errors.currency}</span>}
    </div>
  )
}));

vi.mock('../../../../src/components/settings/LogoUpload.jsx', () => ({
  LogoUpload: ({ logoData, onChange, errors, disabled }) => (
    <div data-testid="logo-upload">
      <button
        data-testid="upload-logo"
        onClick={() => onChange('data:image/jpeg;base64,test')}
        disabled={disabled}
      >
        Upload Logo
      </button>
      {logoData && <img data-testid="logo-preview" src={logoData} alt="Logo" />}
      {errors.logo && <span data-testid="logo-error">{errors.logo}</span>}
    </div>
  )
}));

describe('CompanyProfileSettings', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render main component structure', () => {
      render(<CompanyProfileSettings />);

      expect(screen.getByText('Company Profile')).toBeInTheDocument();
      expect(screen.getByText('Configure your company information and business settings for accurate document processing.')).toBeInTheDocument();
    });

    it('should render tab navigation', () => {
      render(<CompanyProfileSettings />);

      expect(screen.getByText('Company Info')).toBeInTheDocument();
      expect(screen.getByText('Business Config')).toBeInTheDocument();
      expect(screen.getByText('Logo & Branding')).toBeInTheDocument();
    });

    it('should render action buttons', () => {
      render(<CompanyProfileSettings />);

      expect(screen.getByText('Reset to Defaults')).toBeInTheDocument();
      expect(screen.getByText('Save Changes')).toBeInTheDocument();
    });

    it('should show loading state', () => {
      vi.mocked(require('../../../../src/popup/hooks/useSettings.js').useSettings).mockReturnValue({
        settings: {},
        updateSection: mockUpdateSection,
        isLoading: true
      });

      render(<CompanyProfileSettings />);

      expect(screen.getByText('Loading company settings...')).toBeInTheDocument();
    });
  });

  describe('Tab Navigation', () => {
    it('should show company information tab by default', () => {
      render(<CompanyProfileSettings />);

      expect(screen.getByTestId('company-information')).toBeInTheDocument();
      expect(screen.queryByTestId('business-configuration')).not.toBeInTheDocument();
      expect(screen.queryByTestId('logo-upload')).not.toBeInTheDocument();
    });

    it('should switch to business configuration tab', () => {
      render(<CompanyProfileSettings />);

      fireEvent.click(screen.getByText('Business Config'));

      expect(screen.queryByTestId('company-information')).not.toBeInTheDocument();
      expect(screen.getByTestId('business-configuration')).toBeInTheDocument();
      expect(screen.queryByTestId('logo-upload')).not.toBeInTheDocument();
    });

    it('should switch to logo upload tab', () => {
      render(<CompanyProfileSettings />);

      fireEvent.click(screen.getByText('Logo & Branding'));

      expect(screen.queryByTestId('company-information')).not.toBeInTheDocument();
      expect(screen.queryByTestId('business-configuration')).not.toBeInTheDocument();
      expect(screen.getByTestId('logo-upload')).toBeInTheDocument();
    });
  });

  describe('Data Management', () => {
    it('should load initial data from settings', () => {
      render(<CompanyProfileSettings />);

      expect(screen.getByTestId('company-name')).toHaveValue('Test Company');
    });

    it('should handle company data changes', () => {
      render(<CompanyProfileSettings />);

      const nameInput = screen.getByTestId('company-name');
      fireEvent.change(nameInput, { target: { value: 'New Company Name' } });

      expect(nameInput).toHaveValue('New Company Name');
    });

    it('should handle business data changes', () => {
      render(<CompanyProfileSettings />);

      fireEvent.click(screen.getByText('Business Config'));

      const currencySelect = screen.getByTestId('currency-select');
      fireEvent.change(currencySelect, { target: { value: 'EUR' } });

      expect(currencySelect).toHaveValue('EUR');
    });

    it('should handle logo changes', () => {
      render(<CompanyProfileSettings />);

      fireEvent.click(screen.getByText('Logo & Branding'));
      fireEvent.click(screen.getByTestId('upload-logo'));

      expect(screen.getByTestId('logo-preview')).toBeInTheDocument();
    });
  });

  describe('Validation', () => {
    it('should show validation errors for empty company name', async () => {
      render(<CompanyProfileSettings />);

      // Clear company name
      const nameInput = screen.getByTestId('company-name');
      fireEvent.change(nameInput, { target: { value: '' } });

      // Try to save
      fireEvent.click(screen.getByText('Save Changes'));

      await waitFor(() => {
        expect(screen.getByText('❌ Please fix the errors above')).toBeInTheDocument();
      });
    });

    it('should clear errors when data is corrected', () => {
      render(<CompanyProfileSettings />);

      // Clear company name to trigger error
      const nameInput = screen.getByTestId('company-name');
      fireEvent.change(nameInput, { target: { value: '' } });

      // Try to save to trigger validation
      fireEvent.click(screen.getByText('Save Changes'));

      // Fix the error
      fireEvent.change(nameInput, { target: { value: 'Valid Company' } });

      // Error should be cleared (we can't easily test this without more complex state management)
      expect(nameInput).toHaveValue('Valid Company');
    });
  });

  describe('Save Functionality', () => {
    it('should save company data successfully', async () => {
      mockUpdateSection.mockResolvedValue(true);

      render(<CompanyProfileSettings />);

      fireEvent.click(screen.getByText('Save Changes'));

      await waitFor(() => {
        expect(mockUpdateSection).toHaveBeenCalledWith('company', expect.objectContaining({
          name: 'Test Company'
        }));
      });

      await waitFor(() => {
        expect(screen.getByText('✅ Company profile saved successfully')).toBeInTheDocument();
      });
    });

    it('should handle save errors', async () => {
      mockUpdateSection.mockRejectedValue(new Error('Save failed'));

      render(<CompanyProfileSettings />);

      fireEvent.click(screen.getByText('Save Changes'));

      await waitFor(() => {
        expect(screen.getByText('❌ Failed to save: Save failed')).toBeInTheDocument();
      });
    });

    it('should show saving state', async () => {
      mockUpdateSection.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));

      render(<CompanyProfileSettings />);

      fireEvent.click(screen.getByText('Save Changes'));

      expect(screen.getByText('Saving...')).toBeInTheDocument();

      await waitFor(() => {
        expect(screen.getByText('Save Changes')).toBeInTheDocument();
      });
    });
  });

  describe('Reset Functionality', () => {
    it('should reset data to defaults when confirmed', () => {
      // Mock window.confirm
      const originalConfirm = window.confirm;
      window.confirm = vi.fn(() => true);

      render(<CompanyProfileSettings />);

      // Change some data
      const nameInput = screen.getByTestId('company-name');
      fireEvent.change(nameInput, { target: { value: 'Changed Name' } });

      // Reset
      fireEvent.click(screen.getByText('Reset to Defaults'));

      expect(nameInput).toHaveValue('');

      // Restore original confirm
      window.confirm = originalConfirm;
    });

    it('should not reset data when cancelled', () => {
      // Mock window.confirm to return false
      const originalConfirm = window.confirm;
      window.confirm = vi.fn(() => false);

      render(<CompanyProfileSettings />);

      const nameInput = screen.getByTestId('company-name');
      const originalValue = nameInput.value;

      fireEvent.click(screen.getByText('Reset to Defaults'));

      expect(nameInput).toHaveValue(originalValue);

      // Restore original confirm
      window.confirm = originalConfirm;
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels and roles', () => {
      render(<CompanyProfileSettings />);

      // Check for proper heading structure
      expect(screen.getByRole('heading', { level: 2 })).toHaveTextContent('Company Profile');

      // Check for tab navigation
      const tabs = screen.getAllByRole('button');
      const tabButtons = tabs.filter(button =>
        button.textContent.includes('Company Info') ||
        button.textContent.includes('Business Config') ||
        button.textContent.includes('Logo & Branding')
      );
      expect(tabButtons).toHaveLength(3);
    });

    it('should support keyboard navigation', () => {
      render(<CompanyProfileSettings />);

      const firstTab = screen.getByText('Company Info');
      const secondTab = screen.getByText('Business Config');

      // Focus first tab
      firstTab.focus();
      expect(document.activeElement).toBe(firstTab);

      // Navigate to second tab
      fireEvent.keyDown(firstTab, { key: 'Tab' });
      // Note: Full keyboard navigation testing would require more complex setup
    });
  });

  describe('Error Handling', () => {
    it('should handle missing settings gracefully', () => {
      vi.mocked(require('../../../../src/popup/hooks/useSettings.js').useSettings).mockReturnValue({
        settings: {},
        updateSection: mockUpdateSection,
        isLoading: false
      });

      render(<CompanyProfileSettings />);

      // Should render without crashing
      expect(screen.getByText('Company Profile')).toBeInTheDocument();
    });

    it('should handle partial settings data', () => {
      vi.mocked(require('../../../../src/popup/hooks/useSettings.js').useSettings).mockReturnValue({
        settings: {
          company: { name: 'Partial Company' }
          // Missing other fields
        },
        updateSection: mockUpdateSection,
        isLoading: false
      });

      render(<CompanyProfileSettings />);

      expect(screen.getByTestId('company-name')).toHaveValue('Partial Company');
    });
  });
});
