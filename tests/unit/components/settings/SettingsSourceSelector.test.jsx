/**
 * Unit Tests for SettingsSourceSelector Component
 *
 * Tests multi-source settings loading UI component
 *
 * ASSIGNMENT-048: Settings Loading Enhancement
 * Epic: EPIC-005 - Enhanced AI Analysis & RAG Integration
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { SettingsSourceSelector } from '../../../../src/components/settings/SettingsSourceSelector.jsx';

// Mock the ConfigurationSourceManager
const mockConfigurationSourceManager = {
  getCurrentSource: vi.fn().mockReturnValue('environment_service'),
  getAvailableSources: vi.fn().mockReturnValue([
    {
      id: 'environment_service',
      name: 'Environment Service',
      description: 'Default environment configuration service',
      icon: '⚙️',
      priority: 1
    },
    {
      id: 'chrome_storage',
      name: 'Chrome Storage',
      description: 'Configuration stored in Chrome extension storage',
      icon: '💾',
      priority: 2
    },
    {
      id: 'env_file',
      name: '.env File',
      description: 'Environment variables from .env file',
      icon: '📄',
      priority: 3
    },
    {
      id: 'json_config',
      name: 'JSON Config',
      description: 'Configuration from JSON file',
      icon: '📋',
      priority: 4
    }
  ]),
  setCurrentSource: vi.fn(),
  loadConfiguration: vi.fn().mockResolvedValue({
    company: { name: 'Test Company' },
    apiKeys: { deepseek: { key: 'test-key' } }
  }),
  getLoadingStatus: vi.fn().mockReturnValue({
    isLoading: false,
    currentSource: 'environment_service',
    lastLoadTime: new Date('2025-01-28T12:00:00Z'),
    hasConfig: true,
    errors: {}
  }),
  testAllSources: vi.fn().mockResolvedValue({
    environment_service: { success: true, loadTime: 100, configSize: 5 },
    chrome_storage: { success: true, loadTime: 50, configSize: 3 },
    env_file: { success: false, error: 'File not found' },
    json_config: { success: true, loadTime: 75, configSize: 4 }
  })
};

vi.mock('../../../../src/services/ConfigurationSourceManager.js', () => ({
  configurationSourceManager: mockConfigurationSourceManager,
  CONFIG_SOURCES: {
    ENVIRONMENT_SERVICE: 'environment_service',
    CHROME_STORAGE: 'chrome_storage',
    ENV_FILE: 'env_file',
    JSON_CONFIG: 'json_config'
  }
}));

describe('SettingsSourceSelector', () => {
  const mockOnConfigurationLoaded = vi.fn();
  const mockOnError = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('🧪 Component Rendering', () => {
    it('should render with default props', () => {
      render(<SettingsSourceSelector />);

      expect(screen.getByText('Configuration Source')).toBeInTheDocument();
      expect(screen.getByText('Select Configuration Source')).toBeInTheDocument();
      expect(screen.getByText('🔄 Load Settings')).toBeInTheDocument();
      expect(screen.getByText('🧪 Test All')).toBeInTheDocument();
    });

    it('should display current source information', () => {
      render(<SettingsSourceSelector />);

      expect(screen.getByText('Environment Service')).toBeInTheDocument();
      expect(screen.getByText('Default environment configuration service')).toBeInTheDocument();
      expect(screen.getByText('⚙️')).toBeInTheDocument();
    });

    it('should show loading status when available', () => {
      render(<SettingsSourceSelector />);

      expect(screen.getByText(/Last loaded from:/)).toBeInTheDocument();
      expect(screen.getByText('environment_service')).toBeInTheDocument();
      expect(screen.getByText('✅ Configuration loaded successfully')).toBeInTheDocument();
    });
  });

  describe('🔧 Source Selection', () => {
    it('should open dropdown when clicked', async () => {
      render(<SettingsSourceSelector />);

      const dropdownButton = screen.getByRole('button', { name: /Environment Service/ });
      fireEvent.click(dropdownButton);

      await waitFor(() => {
        expect(screen.getByText('Chrome Storage')).toBeInTheDocument();
        expect(screen.getByText('.env File')).toBeInTheDocument();
        expect(screen.getByText('JSON Config')).toBeInTheDocument();
      });
    });

    it('should select new source from dropdown', async () => {
      render(<SettingsSourceSelector />);

      // Open dropdown
      const dropdownButton = screen.getByRole('button', { name: /Environment Service/ });
      fireEvent.click(dropdownButton);

      // Select Chrome Storage
      await waitFor(() => {
        const chromeStorageOption = screen.getByText('Chrome Storage');
        fireEvent.click(chromeStorageOption);
      });

      expect(mockConfigurationSourceManager.setCurrentSource).toHaveBeenCalledWith('chrome_storage');
    });

    it('should close dropdown after selection', async () => {
      render(<SettingsSourceSelector />);

      // Open dropdown
      const dropdownButton = screen.getByRole('button', { name: /Environment Service/ });
      fireEvent.click(dropdownButton);

      // Select option
      await waitFor(() => {
        const option = screen.getByText('Chrome Storage');
        fireEvent.click(option);
      });

      // Dropdown should be closed
      await waitFor(() => {
        expect(screen.queryByText('Configuration stored in Chrome extension storage')).not.toBeInTheDocument();
      });
    });
  });

  describe('🔄 Configuration Loading', () => {
    it('should load configuration when button clicked', async () => {
      render(
        <SettingsSourceSelector
          onConfigurationLoaded={mockOnConfigurationLoaded}
          onError={mockOnError}
        />
      );

      const loadButton = screen.getByText('🔄 Load Settings');
      fireEvent.click(loadButton);

      await waitFor(() => {
        expect(mockConfigurationSourceManager.loadConfiguration).toHaveBeenCalled();
        expect(mockOnConfigurationLoaded).toHaveBeenCalledWith(
          { company: { name: 'Test Company' }, apiKeys: { deepseek: { key: 'test-key' } } },
          'environment_service'
        );
      });
    });

    it('should show loading state during configuration load', async () => {
      // Mock a delayed response
      mockConfigurationSourceManager.loadConfiguration.mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve({}), 100))
      );

      render(<SettingsSourceSelector />);

      const loadButton = screen.getByText('🔄 Load Settings');
      fireEvent.click(loadButton);

      expect(screen.getByText('⏳ Loading...')).toBeInTheDocument();
      expect(loadButton).toBeDisabled();
    });

    it('should handle loading errors', async () => {
      const error = new Error('Loading failed');
      mockConfigurationSourceManager.loadConfiguration.mockRejectedValue(error);

      render(
        <SettingsSourceSelector
          onConfigurationLoaded={mockOnConfigurationLoaded}
          onError={mockOnError}
        />
      );

      const loadButton = screen.getByText('🔄 Load Settings');
      fireEvent.click(loadButton);

      await waitFor(() => {
        expect(mockOnError).toHaveBeenCalledWith(error, 'environment_service');
      });
    });
  });

  describe('🧪 Source Testing', () => {
    it('should test all sources when button clicked', async () => {
      render(<SettingsSourceSelector />);

      const testButton = screen.getByText('🧪 Test All');
      fireEvent.click(testButton);

      await waitFor(() => {
        expect(mockConfigurationSourceManager.testAllSources).toHaveBeenCalled();
      });
    });

    it('should display test results', async () => {
      render(<SettingsSourceSelector />);

      const testButton = screen.getByText('🧪 Test All');
      fireEvent.click(testButton);

      await waitFor(() => {
        expect(screen.getByText('Source Test Results:')).toBeInTheDocument();
        expect(screen.getByText('✅ Success')).toBeInTheDocument();
        expect(screen.getByText('❌ File not found')).toBeInTheDocument();
        expect(screen.getByText('(100ms, 5 items)')).toBeInTheDocument();
      });
    });

    it('should show testing state during test execution', async () => {
      // Mock a delayed response
      mockConfigurationSourceManager.testAllSources.mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve({}), 100))
      );

      render(<SettingsSourceSelector />);

      const testButton = screen.getByText('🧪 Test All');
      fireEvent.click(testButton);

      expect(screen.getByText('🧪 Testing...')).toBeInTheDocument();
      expect(testButton).toBeDisabled();
    });
  });

  describe('📊 Status Display', () => {
    it('should display loading errors', () => {
      mockConfigurationSourceManager.getLoadingStatus.mockReturnValue({
        isLoading: false,
        currentSource: 'environment_service',
        lastLoadTime: null,
        hasConfig: false,
        errors: {
          chrome_storage: {
            error: 'Storage not available',
            timestamp: new Date('2025-01-28T11:00:00Z')
          },
          env_file: {
            error: 'File not found',
            timestamp: new Date('2025-01-28T10:00:00Z')
          }
        }
      });

      render(<SettingsSourceSelector />);

      expect(screen.getByText('Loading Errors:')).toBeInTheDocument();
      expect(screen.getByText(/chrome_storage.*Storage not available/)).toBeInTheDocument();
      expect(screen.getByText(/env_file.*File not found/)).toBeInTheDocument();
    });

    it('should display help text', () => {
      render(<SettingsSourceSelector />);

      expect(screen.getByText('Configuration Sources:')).toBeInTheDocument();
      expect(screen.getByText(/Environment Service.*Default configuration/)).toBeInTheDocument();
      expect(screen.getByText(/Chrome Storage.*Settings stored in browser/)).toBeInTheDocument();
      expect(screen.getByText(/\.env File.*Direct environment variable/)).toBeInTheDocument();
      expect(screen.getByText(/JSON Config.*Configuration from config\.json/)).toBeInTheDocument();
    });
  });

  describe('🔄 Component Updates', () => {
    it('should update when configuration source manager state changes', () => {
      const { rerender } = render(<SettingsSourceSelector />);

      // Change the mock return value
      mockConfigurationSourceManager.getCurrentSource.mockReturnValue('chrome_storage');
      mockConfigurationSourceManager.getLoadingStatus.mockReturnValue({
        isLoading: false,
        currentSource: 'chrome_storage',
        lastLoadTime: new Date(),
        hasConfig: true,
        errors: {}
      });

      rerender(<SettingsSourceSelector />);

      expect(mockConfigurationSourceManager.getLoadingStatus).toHaveBeenCalled();
    });
  });
});

console.log('✅ SettingsSourceSelector component tests completed');
