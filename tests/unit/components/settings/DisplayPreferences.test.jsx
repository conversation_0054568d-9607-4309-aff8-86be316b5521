import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { DisplayPreferences } from '../../../../src/components/settings/DisplayPreferences.jsx';

// Mock the sub-components
vi.mock('../../../../src/components/settings/ThemeSelector.jsx', () => ({
  ThemeSelector: ({ currentTheme, onChange, disabled }) => (
    <div data-testid="theme-selector">
      <select
        data-testid="theme-select"
        value={currentTheme}
        onChange={(e) => onChange(e.target.value)}
        disabled={disabled}
      >
        <option value="light">Light</option>
        <option value="dark">Dark</option>
        <option value="auto">Auto</option>
      </select>
    </div>
  )
}));

vi.mock('../../../../src/components/settings/LanguageSelector.jsx', () => ({
  LanguageSelector: ({ currentLanguage, onChange, disabled }) => (
    <div data-testid="language-selector">
      <select
        data-testid="language-select"
        value={currentLanguage}
        onChange={(e) => onChange(e.target.value, { code: e.target.value })}
        disabled={disabled}
      >
        <option value="pl">Polish</option>
        <option value="en">English</option>
      </select>
    </div>
  )
}));

describe('DisplayPreferences', () => {
  const defaultDisplayData = {
    theme: 'light',
    language: 'pl',
    dateFormat: 'DD/MM/YYYY',
    currency: 'PLN',
    groupBy: 'month',
    tablePageSize: 10,
    showNotifications: true,
    notificationDuration: 3000
  };

  const mockOnChange = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render main component structure', () => {
      render(
        <DisplayPreferences
          displayData={defaultDisplayData}
          onChange={mockOnChange}
        />
      );

      expect(screen.getByText('Display Preferences')).toBeInTheDocument();
      expect(screen.getByText('Theme & Appearance')).toBeInTheDocument();
      expect(screen.getByText('Language & Locale')).toBeInTheDocument();
      expect(screen.getByText('Formatting Options')).toBeInTheDocument();
      expect(screen.getByText('Table & Data Display')).toBeInTheDocument();
      expect(screen.getByText('Notifications')).toBeInTheDocument();
    });

    it('should render theme selector', () => {
      render(
        <DisplayPreferences
          displayData={defaultDisplayData}
          onChange={mockOnChange}
        />
      );

      expect(screen.getByTestId('theme-selector')).toBeInTheDocument();
      expect(screen.getByTestId('theme-select')).toHaveValue('light');
    });

    it('should render language selector', () => {
      render(
        <DisplayPreferences
          displayData={defaultDisplayData}
          onChange={mockOnChange}
        />
      );

      expect(screen.getByTestId('language-selector')).toBeInTheDocument();
      expect(screen.getByTestId('language-select')).toHaveValue('pl');
    });

    it('should render formatting options', () => {
      render(
        <DisplayPreferences
          displayData={defaultDisplayData}
          onChange={mockOnChange}
        />
      );

      expect(screen.getByLabelText('Date Format')).toBeInTheDocument();
      expect(screen.getByLabelText('Default Currency')).toBeInTheDocument();
    });

    it('should render table preferences', () => {
      render(
        <DisplayPreferences
          displayData={defaultDisplayData}
          onChange={mockOnChange}
        />
      );

      expect(screen.getByText('Default Grouping')).toBeInTheDocument();
      expect(screen.getByLabelText('Table Page Size')).toBeInTheDocument();
    });

    it('should render notification settings', () => {
      render(
        <DisplayPreferences
          displayData={defaultDisplayData}
          onChange={mockOnChange}
        />
      );

      expect(screen.getByLabelText(/Show notifications/)).toBeInTheDocument();
    });
  });

  describe('Data Management', () => {
    it('should load initial data correctly', () => {
      render(
        <DisplayPreferences
          displayData={defaultDisplayData}
          onChange={mockOnChange}
        />
      );

      expect(screen.getByTestId('theme-select')).toHaveValue('light');
      expect(screen.getByTestId('language-select')).toHaveValue('pl');
      expect(screen.getByDisplayValue('DD/MM/YYYY (e.g., 27/01/2025)')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Polish Złoty (zł)')).toBeInTheDocument();
    });

    it('should handle theme changes', () => {
      render(
        <DisplayPreferences
          displayData={defaultDisplayData}
          onChange={mockOnChange}
        />
      );

      const themeSelect = screen.getByTestId('theme-select');
      fireEvent.change(themeSelect, { target: { value: 'dark' } });

      expect(mockOnChange).toHaveBeenCalledWith(
        expect.objectContaining({ theme: 'dark' })
      );
    });

    it('should handle language changes with format updates', () => {
      render(
        <DisplayPreferences
          displayData={defaultDisplayData}
          onChange={mockOnChange}
        />
      );

      const languageSelect = screen.getByTestId('language-select');
      fireEvent.change(languageSelect, { target: { value: 'en' } });

      expect(mockOnChange).toHaveBeenCalledWith(
        expect.objectContaining({
          language: 'en',
          dateFormat: 'MM/DD/YYYY',
          currency: 'USD'
        })
      );
    });

    it('should handle date format changes', () => {
      render(
        <DisplayPreferences
          displayData={defaultDisplayData}
          onChange={mockOnChange}
        />
      );

      const dateFormatSelect = screen.getByLabelText('Date Format');
      fireEvent.change(dateFormatSelect, { target: { value: 'YYYY-MM-DD' } });

      expect(mockOnChange).toHaveBeenCalledWith(
        expect.objectContaining({ dateFormat: 'YYYY-MM-DD' })
      );
    });

    it('should handle currency changes', () => {
      render(
        <DisplayPreferences
          displayData={defaultDisplayData}
          onChange={mockOnChange}
        />
      );

      const currencySelect = screen.getByLabelText('Default Currency');
      fireEvent.change(currencySelect, { target: { value: 'EUR' } });

      expect(mockOnChange).toHaveBeenCalledWith(
        expect.objectContaining({ currency: 'EUR' })
      );
    });

    it('should handle grouping changes', () => {
      render(
        <DisplayPreferences
          displayData={defaultDisplayData}
          onChange={mockOnChange}
        />
      );

      const quarterRadio = screen.getByLabelText('By Quarter');
      fireEvent.click(quarterRadio);

      expect(mockOnChange).toHaveBeenCalledWith(
        expect.objectContaining({ groupBy: 'quarter' })
      );
    });

    it('should handle page size changes', () => {
      render(
        <DisplayPreferences
          displayData={defaultDisplayData}
          onChange={mockOnChange}
        />
      );

      const pageSizeSelect = screen.getByLabelText('Table Page Size');
      fireEvent.change(pageSizeSelect, { target: { value: '25' } });

      expect(mockOnChange).toHaveBeenCalledWith(
        expect.objectContaining({ tablePageSize: 25 })
      );
    });

    it('should handle notification settings changes', () => {
      render(
        <DisplayPreferences
          displayData={defaultDisplayData}
          onChange={mockOnChange}
        />
      );

      const notificationCheckbox = screen.getByLabelText(/Show notifications/);
      fireEvent.click(notificationCheckbox);

      expect(mockOnChange).toHaveBeenCalledWith(
        expect.objectContaining({ showNotifications: false })
      );
    });
  });

  describe('Conditional Rendering', () => {
    it('should show notification duration when notifications are enabled', () => {
      render(
        <DisplayPreferences
          displayData={{ ...defaultDisplayData, showNotifications: true }}
          onChange={mockOnChange}
        />
      );

      expect(screen.getByLabelText('Notification Duration')).toBeInTheDocument();
    });

    it('should hide notification duration when notifications are disabled', () => {
      render(
        <DisplayPreferences
          displayData={{ ...defaultDisplayData, showNotifications: false }}
          onChange={mockOnChange}
        />
      );

      expect(screen.queryByLabelText('Notification Duration')).not.toBeInTheDocument();
    });
  });

  describe('Settings Preview', () => {
    it('should display settings preview correctly', () => {
      render(
        <DisplayPreferences
          displayData={defaultDisplayData}
          onChange={mockOnChange}
        />
      );

      expect(screen.getByText('Settings Preview')).toBeInTheDocument();
      expect(screen.getByText('light')).toBeInTheDocument();
      expect(screen.getByText('PL')).toBeInTheDocument();
      expect(screen.getByText('27/01/2025')).toBeInTheDocument();
      expect(screen.getByText('zł PLN')).toBeInTheDocument();
    });

    it('should update preview when settings change', () => {
      const { rerender } = render(
        <DisplayPreferences
          displayData={defaultDisplayData}
          onChange={mockOnChange}
        />
      );

      // Change theme and rerender
      const updatedData = { ...defaultDisplayData, theme: 'dark', currency: 'EUR' };
      rerender(
        <DisplayPreferences
          displayData={updatedData}
          onChange={mockOnChange}
        />
      );

      expect(screen.getByText('dark')).toBeInTheDocument();
      expect(screen.getByText('€ EUR')).toBeInTheDocument();
    });
  });

  describe('Disabled State', () => {
    it('should disable all inputs when disabled prop is true', () => {
      render(
        <DisplayPreferences
          displayData={defaultDisplayData}
          onChange={mockOnChange}
          disabled={true}
        />
      );

      expect(screen.getByTestId('theme-select')).toBeDisabled();
      expect(screen.getByTestId('language-select')).toBeDisabled();
      expect(screen.getByLabelText('Date Format')).toBeDisabled();
      expect(screen.getByLabelText('Default Currency')).toBeDisabled();
      expect(screen.getByLabelText('Table Page Size')).toBeDisabled();
      expect(screen.getByLabelText(/Show notifications/)).toBeDisabled();
    });

    it('should not call onChange when disabled', () => {
      render(
        <DisplayPreferences
          displayData={defaultDisplayData}
          onChange={mockOnChange}
          disabled={true}
        />
      );

      const dateFormatSelect = screen.getByLabelText('Date Format');
      fireEvent.change(dateFormatSelect, { target: { value: 'YYYY-MM-DD' } });

      // Should not be called because component is disabled
      expect(mockOnChange).not.toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('should display error messages when provided', () => {
      const errors = {
        dateFormat: 'Invalid date format',
        currency: 'Currency is required'
      };

      render(
        <DisplayPreferences
          displayData={defaultDisplayData}
          onChange={mockOnChange}
          errors={errors}
        />
      );

      expect(screen.getByText('Invalid date format')).toBeInTheDocument();
      expect(screen.getByText('Currency is required')).toBeInTheDocument();
    });

    it('should apply error styling to inputs with errors', () => {
      const errors = {
        dateFormat: 'Invalid date format'
      };

      render(
        <DisplayPreferences
          displayData={defaultDisplayData}
          onChange={mockOnChange}
          errors={errors}
        />
      );

      const dateFormatSelect = screen.getByLabelText('Date Format');
      expect(dateFormatSelect).toHaveClass('border-red-300');
    });
  });

  describe('Default Values', () => {
    it('should handle empty displayData gracefully', () => {
      render(
        <DisplayPreferences
          displayData={{}}
          onChange={mockOnChange}
        />
      );

      // Should render with default values
      expect(screen.getByTestId('theme-select')).toHaveValue('light');
      expect(screen.getByTestId('language-select')).toHaveValue('pl');
    });

    it('should merge provided data with defaults', () => {
      const partialData = { theme: 'dark', currency: 'EUR' };

      render(
        <DisplayPreferences
          displayData={partialData}
          onChange={mockOnChange}
        />
      );

      expect(screen.getByTestId('theme-select')).toHaveValue('dark');
      expect(screen.getByDisplayValue('Euro (€)')).toBeInTheDocument();
      expect(screen.getByTestId('language-select')).toHaveValue('pl'); // Default
    });
  });
});
