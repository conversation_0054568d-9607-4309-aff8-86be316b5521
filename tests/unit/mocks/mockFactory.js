/**
 * Mock Factory for creating test data
 * Provides consistent mock data for testing across the application
 */

class MockFactory {
  /**
   * Create mock invoice data
   */
  static createMockInvoice(overrides = {}) {
    return {
      id: 'INV-2024-001',
      number: 'FV/2024/001',
      kind: 'vat',
      issue_date: '2024-01-15',
      sell_date: '2024-01-15',
      payment_to: '2024-01-29',

      // Seller information
      seller_name: 'Test Company Sp. z o.o.',
      seller_tax_no: '**********',
      seller_address: 'ul. Testowa 123, 00-001 Warszawa',
      seller_bank_account: 'PL12 3456 7890 1234 5678 9012 3456',

      // Buyer information
      buyer_name: 'Client Company Ltd.',
      buyer_tax_no: '**********',
      buyer_address: 'ul. Kliencka 456, 00-002 Kraków',
      buyer_email: '<EMAIL>',

      // Financial data
      total_net: 1000.00,
      total_vat: 230.00,
      total_gross: 1230.00,
      currency: 'PLN',

      // Line items
      positions: [
        {
          name: 'Test Product 1',
          quantity: 2,
          unit: 'szt.',
          price_net: 300.00,
          vat_rate: 23,
          total_net: 600.00,
          total_vat: 138.00,
          total_gross: 738.00
        },
        {
          name: 'Test Service 1',
          quantity: 1,
          unit: 'usł.',
          price_net: 400.00,
          vat_rate: 23,
          total_net: 400.00,
          total_vat: 92.00,
          total_gross: 492.00
        }
      ],

      // Processing metadata
      processed_at: new Date().toISOString(),
      processing_method: 'ai',
      confidence_score: 0.95,

      ...overrides
    };
  }

  /**
   * Create mock company settings
   */
  static createMockCompanySettings(overrides = {}) {
    return {
      name: 'My Test Company Sp. z o.o.',
      taxId: '1111111111',
      address: 'ul. Firmowa 789, 00-003 Gdańsk',
      email: '<EMAIL>',
      phone: '+48 ***********',
      ...overrides
    };
  }

  /**
   * Create mock display settings
   */
  static createMockDisplaySettings(overrides = {}) {
    return {
      groupBy: 'month',
      dateFormat: 'DD/MM/YYYY',
      currency: 'PLN',
      language: 'pl',
      ...overrides
    };
  }

  /**
   * Create mock processing settings
   */
  static createMockProcessingSettings(overrides = {}) {
    return {
      ocrLanguage: 'pol',
      aiProvider: 'deepseek',
      autoProcess: true,
      cacheEnabled: true,
      ...overrides
    };
  }

  /**
   * Create mock user settings
   */
  static createMockUserSettings(overrides = {}) {
    return {
      company: this.createMockCompanySettings(),
      display: this.createMockDisplaySettings(),
      processing: this.createMockProcessingSettings(),
      ...overrides
    };
  }

  /**
   * Create mock PDF file data
   */
  static createMockPDFData() {
    return {
      filename: 'test-invoice.pdf',
      size: 245760,
      type: 'application/pdf',
      content: '%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n...',
      pages: 1,
      text: 'FAKTURA VAT\nNumer: FV/2024/001\nData wystawienia: 15.01.2024\n...'
    };
  }

  /**
   * Create mock OCR result
   */
  static createMockOCRResult(overrides = {}) {
    return {
      text: 'FAKTURA VAT\nFV/2024/001\nData: 15.01.2024\nSprzedawca: Test Company\nNabywca: Client Company\nRazem: 1230,00 PLN',
      confidence: 0.92,
      words: [
        { text: 'FAKTURA', confidence: 0.98, bbox: { x0: 100, y0: 50, x1: 200, y1: 80 } },
        { text: 'VAT', confidence: 0.95, bbox: { x0: 210, y0: 50, x1: 250, y1: 80 } }
        // ... more words
      ],
      processing_time: 2.5,
      ...overrides
    };
  }

  /**
   * Create mock AI extraction result
   */
  static createMockAIResult(overrides = {}) {
    return {
      extracted_fields: this.createMockInvoice(),
      confidence: 0.95,
      processing_time: 1.2,
      model_used: 'deepseek-chat',
      tokens_used: 1250,
      ...overrides
    };
  }

  /**
   * Create mock file upload event
   */
  static createMockFileUploadEvent(files = []) {
    const mockFiles = files.length > 0 ? files : [
      new File(['mock pdf content'], 'test.pdf', { type: 'application/pdf' })
    ];

    return {
      target: {
        files: mockFiles
      },
      dataTransfer: {
        files: mockFiles
      },
      preventDefault: jest.fn(),
      stopPropagation: jest.fn()
    };
  }

  /**
   * Create mock Chrome storage data
   */
  static createMockStorageData(overrides = {}) {
    return {
      invoices: [
        this.createMockInvoice({ id: 'INV-001' }),
        this.createMockInvoice({ id: 'INV-002', number: 'FV/2024/002' })
      ],
      settings: this.createMockUserSettings(),
      cache: {
        'test.pdf': {
          processed_at: new Date().toISOString(),
          result: this.createMockInvoice()
        }
      },
      ...overrides
    };
  }

  /**
   * Create mock API response
   */
  static createMockAPIResponse(data = {}, success = true) {
    return {
      ok: success,
      status: success ? 200 : 400,
      statusText: success ? 'OK' : 'Bad Request',
      json: () => Promise.resolve({
        success,
        data: success ? data : null,
        error: success ? null : 'Mock API error',
        timestamp: new Date().toISOString()
      }),
      text: () => Promise.resolve(JSON.stringify(data))
    };
  }

  /**
   * Create mock error
   */
  static createMockError(message = 'Mock error', code = 'MOCK_ERROR') {
    const error = new Error(message);
    error.code = code;
    error.timestamp = new Date().toISOString();
    return error;
  }

  /**
   * Create mock React component props
   */
  static createMockComponentProps(overrides = {}) {
    return {
      className: 'test-component',
      'data-testid': 'mock-component',
      ...overrides
    };
  }

  /**
   * Create mock table data
   */
  static createMockTableData(count = 5) {
    return Array.from({ length: count }, (_, index) =>
      this.createMockInvoice({
        id: `INV-${String(index + 1).padStart(3, '0')}`,
        number: `FV/2024/${String(index + 1).padStart(3, '0')}`,
        issue_date: new Date(2024, 0, index + 1).toISOString().split('T')[0]
      })
    );
  }

  /**
   * Create mock processing progress
   */
  static createMockProcessingProgress(stage = 'uploading', progress = 0) {
    return {
      stage, // 'uploading', 'extracting', 'processing', 'complete', 'error'
      progress, // 0-100
      message: `Processing ${stage}...`,
      timestamp: new Date().toISOString(),
      details: {
        filename: 'test.pdf',
        fileSize: 245760,
        currentStep: stage,
        totalSteps: 4
      }
    };
  }
}

// Export for CommonJS
module.exports = {
  MockFactory,
  createMockInvoice: MockFactory.createMockInvoice,
  createMockCompanySettings: MockFactory.createMockCompanySettings,
  createMockDisplaySettings: MockFactory.createMockDisplaySettings,
  createMockProcessingSettings: MockFactory.createMockProcessingSettings,
  createMockUserSettings: MockFactory.createMockUserSettings,
  createMockPDFData: MockFactory.createMockPDFData,
  createMockOCRResult: MockFactory.createMockOCRResult,
  createMockAIResult: MockFactory.createMockAIResult,
  createMockFileUploadEvent: MockFactory.createMockFileUploadEvent,
  createMockStorageData: MockFactory.createMockStorageData,
  createMockAPIResponse: MockFactory.createMockAPIResponse,
  createMockError: MockFactory.createMockError,
  createMockComponentProps: MockFactory.createMockComponentProps,
  createMockTableData: MockFactory.createMockTableData,
  createMockProcessingProgress: MockFactory.createMockProcessingProgress
};
