import { describe, test, expect, beforeEach, afterEach } from 'vitest';
import DocumentAnalysisPipeline from '../../src/services/DocumentAnalysisPipeline';
import {
  createMockPdfFile,
  mockPdfResult,
  mockOcrResult,
  mockDeepSeekResult,
  setupTestMocks,
  clearTestMocks
} from '../unit/testHelpers';

/**
 * @jest-environment jsdom
 */
describe('DocumentAnalysisPipeline Integration', () => {
  let pipeline;
  const mockApiKey = 'test-api-key';
  let mockFile;

  beforeEach(() => {
    setupTestMocks();
    pipeline = new DocumentAnalysisPipeline(mockApiKey);
    mockFile = createMockPdfFile();
  });

  afterEach(() => {
    clearTestMocks();
  });

  describe('Full Document Processing', () => {
    test('should process document through all steps successfully with AI document detection', async () => {
      const result = await pipeline.processDocument(mockFile);

      // Verify overall result structure
      expect(result).toHaveProperty('document_type');
      expect(result).toHaveProperty('language');
      expect(result).toHaveProperty('confidence');
      expect(result).toHaveProperty('metadata');
      expect(result).toHaveProperty('fields');
      expect(result).toHaveProperty('raw_results');

      // Verify PDF processing results
      expect(result.raw_results.pdf_text).toBeDefined();
      expect(result.metadata.pdf).toBeDefined();
      expect(result.metadata.pages).toBeGreaterThan(0);

      // Verify DeepSeek analysis results
      expect(result.fields.invoice_number).toBeDefined();
      expect(result.fields.invoice_number.confidence).toBeGreaterThan(0.8);
      expect(result.document_type).toBe('vat');
      expect(result.confidence_scores.document_type).toBeGreaterThan(0.8);

      // Verify OCR results
      expect(result.raw_results.ocr).toBeDefined();
      expect(result.raw_results.ocr.confidence).toBeGreaterThan(0);
    });

    test('should detect complex document types using AI and patterns', async () => {
      // Mock a correction invoice PDF
      const correctionPdfContent = 'FAKTURA KORYGUJĄCA do faktury VAT 123/2023';
      const mockCorrectionFile = new Blob([correctionPdfContent], { type: 'application/pdf' });

      const result = await pipeline.processDocument(mockCorrectionFile);

      expect(result.document_type).toBe('correction');
      expect(result.confidence_scores.document_type).toBeGreaterThan(0.8);
      expect(result.fields.from_invoice_id).toBeDefined();
      expect(result.fields.from_invoice_id.value).toBe('123/2023');
    });

    test('should fallback to pattern matching when AI detection fails', async () => {
      // Simulate AI failure but successful PDF extraction
      jest.spyOn(pipeline.deepSeekService, 'analyzeText').mockRejectedValue(new Error('AI analysis failed'));

      const proformaPdfContent = 'FAKTURA PRO FORMA 456/2023';
      const mockProformaFile = new Blob([proformaPdfContent], { type: 'application/pdf' });

      const result = await pipeline.processDocument(mockProformaFile);

      expect(result.document_type).toBe('proforma');
      expect(result.confidence_scores.document_type).toBeDefined();
      expect(result.fields).toBeDefined();
    });

    test('should handle processing errors gracefully', async () => {
      // Simulate PDF extraction error
      jest.spyOn(pipeline.pdfService, 'extractText').mockRejectedValue(new Error('PDF extraction failed'));

      await expect(pipeline.processDocument(mockFile)).rejects.toThrow('PDF extraction failed');
    });

    test('should maintain data consistency across processing steps', async () => {
      const result = await pipeline.processDocument(mockFile);

      // Check data consistency between steps
      expect(result.raw_results.pdf_text).toBeDefined();
      expect(result.raw_results.deepseek.fields).toMatchObject(result.fields);
      expect(result.confidence.overall).toBeLessThanOrEqual(1);
      expect(result.confidence.overall).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Cross-Service Validation', () => {
    test('should validate fields across different sources', async () => {
      const result = await pipeline.processDocument(mockFile);

      // Check field validation between DeepSeek and OCR
      for (const [fieldName, field] of Object.entries(result.fields)) {
        if (field.source === 'deepseek') {
          expect(field.confidence).toBeGreaterThan(0.8);
        }
      }
    });

    test('should handle conflicting field values', async () => {
      // Simulate conflicting values between DeepSeek and OCR
      jest.spyOn(pipeline.deepSeekService, 'analyzeText').mockResolvedValue({
        fields: {
          invoice_number: { value: 'INV-001', confidence: 0.9 }
        }
      });

      jest.spyOn(pipeline.tesseractService, 'extractText').mockResolvedValue({
        lines: [{ text: 'INV-002', confidence: 0.95 }]
      });

      const result = await pipeline.processDocument(mockFile);
      expect(result.fields.invoice_number.value).toBe('INV-001');
      expect(result.fields.invoice_number.source).toBe('deepseek');
    });
  });

  describe('Performance Monitoring', () => {
    test('should track processing time for each step', async () => {
      const startTime = Date.now();
      const result = await pipeline.processDocument(mockFile);
      const endTime = Date.now();

      expect(result.metadata.processing_time).toBeDefined();
      expect(new Date(result.metadata.processing_time).getTime()).toBeGreaterThan(startTime);
      expect(new Date(result.metadata.processing_time).getTime()).toBeLessThanOrEqual(endTime);
    });
  });

  describe('Error Handling and Recovery', () => {
    test('should recover from OCR failures using DeepSeek results', async () => {
      // Simulate OCR failure
      jest.spyOn(pipeline.tesseractService, 'extractText').mockRejectedValue(new Error('OCR failed'));

      const result = await pipeline.processDocument(mockFile);

      // Should still have results from DeepSeek
      expect(result.fields).toBeDefined();
      expect(Object.keys(result.fields).length).toBeGreaterThan(0);
      expect(result.raw_results.deepseek).toBeDefined();
    });

    test('should handle complete analysis failure', async () => {
      // Simulate both OCR and DeepSeek failures
      jest.spyOn(pipeline.tesseractService, 'extractText').mockRejectedValue(new Error('OCR failed'));
      jest.spyOn(pipeline.deepSeekService, 'analyzeText').mockRejectedValue(new Error('DeepSeek failed'));

      await expect(pipeline.processDocument(mockFile)).rejects.toThrow('Error in document analysis pipeline');
    });
  });
});
