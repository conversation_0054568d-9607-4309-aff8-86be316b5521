#!/usr/bin/env node

/**
 * CSP Validation Test
 * Validates that the manifest.json CSP policies are correctly formatted
 * and will not cause Chrome extension loading errors.
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 CSP Validation Test');
console.log('='.repeat(50));

// Test 1: Validate manifest.json exists and is valid JSON
function testManifestExists() {
  console.log('\n🔍 Test 1: Manifest JSON Validation');

  const manifestPath = path.join(__dirname, '..', 'dist', 'manifest.json');

  if (!fs.existsSync(manifestPath)) {
    console.log('❌ FAILED: dist/manifest.json not found');
    return false;
  }

  try {
    const manifestContent = fs.readFileSync(manifestPath, 'utf-8');
    const manifest = JSON.parse(manifestContent);
    console.log('✅ PASSED: manifest.json is valid JSON');
    return manifest;
  } catch (error) {
    console.log('❌ FAILED: manifest.json is invalid JSON:', error.message);
    return false;
  }
}

// Test 2: Validate CSP sandbox policy syntax
function testCSPSandboxPolicy(manifest) {
  console.log('\n🔍 Test 2: CSP Sandbox Policy Validation');

  if (!manifest.content_security_policy) {
    console.log('❌ FAILED: content_security_policy not found');
    return false;
  }

  const csp = manifest.content_security_policy;

  if (!csp.sandbox) {
    console.log('❌ FAILED: sandbox policy not found');
    return false;
  }

  const sandboxPolicy = csp.sandbox;
  console.log('📋 Sandbox policy:', sandboxPolicy);

  // Check for invalid "sandbox allow-scripts;" prefix
  if (sandboxPolicy.includes('sandbox allow-scripts;')) {
    console.log('❌ FAILED: Invalid "sandbox allow-scripts;" prefix found');
    return false;
  }

  // Check for proper script-src directive
  if (!sandboxPolicy.includes('script-src')) {
    console.log('❌ FAILED: script-src directive missing');
    return false;
  }

  // Check for proper 'self' directive (should be quoted)
  if (!sandboxPolicy.includes("'self'")) {
    console.log('❌ FAILED: \'self\' directive missing or improperly quoted');
    return false;
  }

  // Check for required unsafe directives for Tesseract.js
  if (!sandboxPolicy.includes("'unsafe-inline'") || !sandboxPolicy.includes("'unsafe-eval'")) {
    console.log('❌ FAILED: Required unsafe directives missing for Tesseract.js');
    return false;
  }

  console.log('✅ PASSED: CSP sandbox policy syntax is valid');
  return true;
}

// Test 3: Validate extension pages CSP
function testExtensionPagesCSP(manifest) {
  console.log('\n🔍 Test 3: Extension Pages CSP Validation');

  const csp = manifest.content_security_policy;

  if (!csp.extension_pages) {
    console.log('❌ FAILED: extension_pages policy not found');
    return false;
  }

  const extensionPolicy = csp.extension_pages;
  console.log('📋 Extension pages policy:', extensionPolicy);

  // Check for required directives
  const requiredDirectives = ['script-src', 'object-src', 'style-src', 'connect-src'];

  for (const directive of requiredDirectives) {
    if (!extensionPolicy.includes(directive)) {
      console.log(`❌ FAILED: ${directive} directive missing`);
      return false;
    }
  }

  // Check for WASM support
  if (!extensionPolicy.includes("'wasm-unsafe-eval'")) {
    console.log('❌ FAILED: wasm-unsafe-eval missing (required for PDF.js)');
    return false;
  }

  console.log('✅ PASSED: Extension pages CSP is valid');
  return true;
}

// Test 4: Validate sandbox configuration
function testSandboxConfiguration(manifest) {
  console.log('\n🔍 Test 4: Sandbox Configuration Validation');

  if (!manifest.sandbox) {
    console.log('❌ FAILED: sandbox configuration not found');
    return false;
  }

  if (!manifest.sandbox.pages || !Array.isArray(manifest.sandbox.pages)) {
    console.log('❌ FAILED: sandbox.pages not found or not an array');
    return false;
  }

  if (!manifest.sandbox.pages.includes('sandbox/sandbox.html')) {
    console.log('❌ FAILED: sandbox/sandbox.html not in sandbox.pages');
    return false;
  }

  console.log('✅ PASSED: Sandbox configuration is valid');
  return true;
}

// Test 5: Check for duplicate CSP entries
function testNoDuplicateCSPEntries(manifest) {
  console.log('\n🔍 Test 5: Duplicate CSP Entries Check');

  const manifestString = JSON.stringify(manifest, null, 2);
  const sandboxMatches = (manifestString.match(/"sandbox":/g) || []).length;

  // Should have exactly 2 "sandbox": entries - one in CSP and one for sandbox config
  if (sandboxMatches !== 2) {
    console.log(`❌ FAILED: Expected 2 "sandbox": entries, found ${sandboxMatches}`);
    return false;
  }

  console.log('✅ PASSED: No duplicate CSP entries found');
  return true;
}

// Main test runner
function runTests() {
  console.log('🎯 Running CSP validation tests...\n');

  const manifest = testManifestExists();
  if (!manifest) {
    console.log('\n❌ TESTS FAILED: Cannot proceed without valid manifest');
    process.exit(1);
  }

  const tests = [
    () => testCSPSandboxPolicy(manifest),
    () => testExtensionPagesCSP(manifest),
    () => testSandboxConfiguration(manifest),
    () => testNoDuplicateCSPEntries(manifest)
  ];

  let passed = 0;
  let total = tests.length + 1; // +1 for manifest test

  if (manifest) { passed++; } // Manifest test passed

  for (const test of tests) {
    if (test()) {
      passed++;
    }
  }

  console.log('\n📊 TEST SUMMARY');
  console.log('='.repeat(50));
  console.log(`✅ Tests passed: ${passed}/${total}`);
  console.log(`📈 Success rate: ${((passed / total) * 100).toFixed(1)}%`);

  if (passed === total) {
    console.log('\n🎉 ALL CSP VALIDATION TESTS PASSED!');
    console.log('✅ Chrome extension should load without CSP errors');
    process.exit(0);
  } else {
    console.log('\n❌ SOME TESTS FAILED');
    console.log('⚠️  Chrome extension may have CSP loading issues');
    process.exit(1);
  }
}

// Run the tests
runTests();
