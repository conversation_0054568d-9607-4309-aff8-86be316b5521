/**
 * Pipeline UI Performance Tests
 * Tests for responsive UI, drag-and-drop performance, and window management
 */

import { test, expect } from '@playwright/test';
import { performance } from 'perf_hooks';

test.describe('Pipeline UI Performance Tests', () => {
  let page;
  let extensionId;

  test.beforeAll(async ({ browser }) => {
    // Load extension
    const context = await browser.newContext();
    page = await context.newPage();
    
    // Navigate to extension popup
    await page.goto('chrome-extension://test/popup.html');
    
    // Wait for extension to load
    await page.waitForSelector('[data-testid="mvat-app"]', { timeout: 10000 });
  });

  test('Popup resize performance', async () => {
    const startTime = performance.now();
    
    // Test multiple resize operations
    const resizeOperations = [
      { width: 400, height: 600 },
      { width: 600, height: 800 },
      { width: 800, height: 600 },
      { width: 400, height: 600 }
    ];

    for (const { width, height } of resizeOperations) {
      const resizeStart = performance.now();
      
      // Resize viewport
      await page.setViewportSize({ width, height });
      
      // Wait for layout to stabilize
      await page.waitForTimeout(100);
      
      // Check that UI elements are still responsive
      await expect(page.locator('[data-testid="upload-area"]')).toBeVisible();
      
      const resizeEnd = performance.now();
      const resizeTime = resizeEnd - resizeStart;
      
      // Resize should complete within 200ms
      expect(resizeTime).toBeLessThan(200);
    }

    const totalTime = performance.now() - startTime;
    console.log(`Total resize test time: ${totalTime}ms`);
    
    // Total test should complete within 2 seconds
    expect(totalTime).toBeLessThan(2000);
  });

  test('Drag and drop event performance', async () => {
    // Navigate to upload tab
    await page.click('[data-testid="upload-tab"]');
    await page.waitForSelector('[data-testid="drag-drop-upload"]');

    const dragDropArea = page.locator('[data-testid="drag-drop-upload"]');
    
    // Test multiple drag events
    const dragEvents = 10;
    const eventTimes = [];

    for (let i = 0; i < dragEvents; i++) {
      const startTime = performance.now();
      
      // Simulate drag enter
      await dragDropArea.dispatchEvent('dragenter', {
        dataTransfer: {
          types: ['Files'],
          files: []
        }
      });
      
      // Wait for visual feedback
      await page.waitForTimeout(50);
      
      // Simulate drag leave
      await dragDropArea.dispatchEvent('dragleave');
      
      const endTime = performance.now();
      eventTimes.push(endTime - startTime);
    }

    // Calculate average event time
    const avgEventTime = eventTimes.reduce((a, b) => a + b, 0) / eventTimes.length;
    
    console.log(`Average drag event time: ${avgEventTime}ms`);
    console.log(`Event times: ${eventTimes.join(', ')}ms`);
    
    // Each drag event should complete within 100ms
    expect(avgEventTime).toBeLessThan(100);
    
    // No single event should take longer than 150ms
    expect(Math.max(...eventTimes)).toBeLessThan(150);
  });

  test('Pipeline window opening performance', async () => {
    // Navigate to upload tab
    await page.click('[data-testid="upload-tab"]');
    
    // Mock a file upload to create recent uploads
    await page.evaluate(() => {
      // Add mock invoice to context
      window.mockInvoice = {
        id: 'test-invoice-1',
        filename: 'test-invoice.pdf',
        processedAt: new Date().toISOString(),
        number: 'INV-001',
        total_gross: '1000.00',
        currency: 'PLN'
      };
      
      // Trigger context update
      if (window.updateContext) {
        window.updateContext({
          invoices: [window.mockInvoice]
        });
      }
    });

    // Wait for recent uploads to appear
    await page.waitForSelector('text=Recent Uploads', { timeout: 5000 });
    
    // Test pipeline window opening
    const startTime = performance.now();
    
    // Click pipeline button
    await page.click('button:has-text("Pipeline")');
    
    // Wait for window to open (check for window.open call)
    await page.waitForTimeout(500);
    
    const endTime = performance.now();
    const openTime = endTime - startTime;
    
    console.log(`Pipeline window open time: ${openTime}ms`);
    
    // Window should open within 500ms
    expect(openTime).toBeLessThan(500);
  });

  test('Recent uploads layout performance', async () => {
    // Navigate to upload tab
    await page.click('[data-testid="upload-tab"]');
    
    // Add multiple mock invoices to test layout performance
    await page.evaluate(() => {
      const mockInvoices = Array.from({ length: 20 }, (_, i) => ({
        id: `test-invoice-${i}`,
        filename: `test-invoice-${i}.pdf`,
        processedAt: new Date(Date.now() - i * 60000).toISOString(),
        number: `INV-${String(i).padStart(3, '0')}`,
        total_gross: `${(Math.random() * 1000).toFixed(2)}`,
        currency: 'PLN'
      }));
      
      if (window.updateContext) {
        window.updateContext({ invoices: mockInvoices });
      }
    });

    const startTime = performance.now();
    
    // Wait for all uploads to render
    await page.waitForSelector('text=Recent Uploads');
    await page.waitForTimeout(200); // Allow for rendering
    
    // Check that scrolling is smooth
    const uploadsContainer = page.locator('.extension-scroll').first();
    
    // Test scrolling performance
    for (let i = 0; i < 5; i++) {
      await uploadsContainer.evaluate(el => {
        el.scrollTop = el.scrollHeight * (i / 4);
      });
      await page.waitForTimeout(50);
    }
    
    const endTime = performance.now();
    const renderTime = endTime - startTime;
    
    console.log(`Recent uploads render time: ${renderTime}ms`);
    
    // Rendering should complete within 1 second
    expect(renderTime).toBeLessThan(1000);
  });

  test('Pipeline styling consistency', async () => {
    // Navigate to upload tab
    await page.click('[data-testid="upload-tab"]');
    
    // Check that pipeline elements use consistent styling
    const pipelineElements = await page.locator('[class*="pipeline"], [class*="Pipeline"]').all();
    
    for (const element of pipelineElements) {
      // Check for consistent color scheme
      const styles = await element.evaluate(el => {
        const computed = window.getComputedStyle(el);
        return {
          fontFamily: computed.fontFamily,
          borderRadius: computed.borderRadius,
          transition: computed.transition
        };
      });
      
      // Should use consistent font family
      expect(styles.fontFamily).toContain('Inter');
      
      // Should have consistent border radius (if any)
      if (styles.borderRadius && styles.borderRadius !== '0px') {
        expect(['0.375rem', '0.25rem', '0.5rem', '6px', '4px', '8px']).toContain(styles.borderRadius);
      }
    }
  });

  test('Memory usage during file processing', async () => {
    // Navigate to upload tab
    await page.click('[data-testid="upload-tab"]');
    
    // Get initial memory usage
    const initialMemory = await page.evaluate(() => {
      if (performance.memory) {
        return {
          used: performance.memory.usedJSHeapSize,
          total: performance.memory.totalJSHeapSize,
          limit: performance.memory.jsHeapSizeLimit
        };
      }
      return null;
    });

    if (initialMemory) {
      console.log('Initial memory usage:', initialMemory);
      
      // Simulate file processing
      await page.evaluate(() => {
        // Create mock file processing scenario
        const mockFiles = Array.from({ length: 10 }, (_, i) => ({
          name: `test-file-${i}.pdf`,
          size: 1024 * 1024, // 1MB
          type: 'application/pdf'
        }));
        
        // Simulate processing
        mockFiles.forEach(file => {
          const mockResult = {
            id: Math.random().toString(36),
            filename: file.name,
            processedAt: new Date().toISOString(),
            data: new Array(1000).fill('mock data') // Simulate processed data
          };
        });
      });
      
      // Wait for processing simulation
      await page.waitForTimeout(1000);
      
      // Get memory usage after processing
      const finalMemory = await page.evaluate(() => {
        if (performance.memory) {
          return {
            used: performance.memory.usedJSHeapSize,
            total: performance.memory.totalJSHeapSize,
            limit: performance.memory.jsHeapSizeLimit
          };
        }
        return null;
      });

      if (finalMemory) {
        console.log('Final memory usage:', finalMemory);
        
        const memoryIncrease = finalMemory.used - initialMemory.used;
        const memoryIncreasePercent = (memoryIncrease / initialMemory.used) * 100;
        
        console.log(`Memory increase: ${memoryIncrease} bytes (${memoryIncreasePercent.toFixed(2)}%)`);
        
        // Memory increase should be reasonable (less than 50% for this test)
        expect(memoryIncreasePercent).toBeLessThan(50);
      }
    }
  });
});

// Lighthouse performance test
test.describe('Lighthouse Performance Tests', () => {
  test('Extension popup Lighthouse score', async ({ page }) => {
    // Navigate to extension popup
    await page.goto('chrome-extension://test/popup.html');
    await page.waitForSelector('[data-testid="mvat-app"]');

    // Run basic performance checks
    const performanceMetrics = await page.evaluate(() => {
      return {
        loadTime: performance.timing.loadEventEnd - performance.timing.navigationStart,
        domContentLoaded: performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart,
        firstPaint: performance.getEntriesByType('paint').find(entry => entry.name === 'first-paint')?.startTime,
        firstContentfulPaint: performance.getEntriesByType('paint').find(entry => entry.name === 'first-contentful-paint')?.startTime
      };
    });

    console.log('Performance metrics:', performanceMetrics);

    // Check performance thresholds
    expect(performanceMetrics.loadTime).toBeLessThan(2000); // Load within 2 seconds
    expect(performanceMetrics.domContentLoaded).toBeLessThan(1000); // DOM ready within 1 second
    
    if (performanceMetrics.firstContentfulPaint) {
      expect(performanceMetrics.firstContentfulPaint).toBeLessThan(1500); // FCP within 1.5 seconds
    }
  });
});
