# Python dependencies for Selenium visual testing
# Updated for Python 3.12 compatibility
# Chrome WebDriver version pinned to < 136 to support --load-extension flag
# See: https://github.com/SeleniumHQ/selenium/issues/15788
selenium>=4.16.0
Pillow>=10.1.0
pytest>=7.4.0
pytest-html>=4.1.0
webdriver-manager>=4.0.0
opencv-python>=4.8.0
numpy>=1.26.0
matplotlib>=3.8.0
requests>=2.31.0
beautifulsoup4>=4.12.0
setuptools>=69.0.0
