#!/usr/bin/env python3

"""
Enhanced Visual Testing Runner for MVAT Chrome Extension
Comprehensive visual regression testing with screenshot comparison
"""

import os
import sys
import time
import json
import hashlib
from PIL import Image, ImageDraw, ImageFont
import numpy as np
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

class VisualTestRunner:
    def __init__(self):
        self.driver = None
        self.extension_id = None
        self.test_results = []
        self.screenshots_dir = os.path.join(os.path.dirname(__file__), 'screenshots')
        self.baselines_dir = os.path.join(os.path.dirname(__file__), 'baselines')
        self.reports_dir = os.path.join(os.path.dirname(__file__), 'reports')
        
        # Create directories if they don't exist
        for directory in [self.screenshots_dir, self.baselines_dir, self.reports_dir]:
            os.makedirs(directory, exist_ok=True)
        
    def setup_driver(self):
        """Setup Chrome driver with extension loaded"""
        print("🔧 Setting up Chrome driver for visual testing...")
        
        # Chrome options for consistent visual testing
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1200,800")
        chrome_options.add_argument("--force-device-scale-factor=1")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--disable-features=VizDisplayCompositor")
        
        # Load extension from dist directory
        extension_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../dist'))
        if os.path.exists(extension_path):
            chrome_options.add_argument(f"--load-extension={extension_path}")
            print(f"✅ Loading extension from: {extension_path}")
        else:
            print(f"❌ Extension not found at: {extension_path}")
            print("   Please run 'npm run build' first")
            return False
            
        # Setup driver
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.implicitly_wait(10)
            
            # Set consistent viewport
            self.driver.set_window_size(1200, 800)
            print("✅ Chrome driver setup complete")
            return True
        except Exception as e:
            print(f"❌ Failed to setup Chrome driver: {e}")
            return False
    
    def get_extension_id(self):
        """Get the extension ID from chrome://extensions page"""
        try:
            self.driver.get("chrome://extensions/")
            time.sleep(3)
            
            # Enable developer mode if not already enabled
            try:
                dev_mode_toggle = self.driver.find_element(By.CSS_SELECTOR, "#devMode")
                if not dev_mode_toggle.is_selected():
                    dev_mode_toggle.click()
                    time.sleep(1)
            except:
                pass
            
            # Look for MVAT extension
            extension_cards = self.driver.find_elements(By.CSS_SELECTOR, "extensions-item")
            for card in extension_cards:
                try:
                    name_element = card.find_element(By.CSS_SELECTOR, "#name")
                    if "MVAT" in name_element.text or "Invoice" in name_element.text:
                        # Get extension ID from the card's ID attribute
                        extension_id = card.get_attribute("id")
                        if extension_id:
                            self.extension_id = extension_id
                            print(f"✅ Found extension ID: {extension_id}")
                            return True
                except:
                    continue
            
            # Fallback: try to find extension ID in page source
            page_source = self.driver.page_source
            if "chrome-extension://" in page_source:
                import re
                matches = re.findall(r'chrome-extension://([a-z]+)/', page_source)
                if matches:
                    self.extension_id = matches[0]
                    print(f"✅ Found extension ID from page source: {self.extension_id}")
                    return True
            
            # Final fallback: use test ID
            self.extension_id = "test-extension-id"
            print("⚠️  Using fallback extension ID")
            return True
            
        except Exception as e:
            print(f"❌ Failed to get extension ID: {e}")
            return False
    
    def take_screenshot(self, name, element=None, wait_time=2):
        """Take screenshot for visual testing"""
        try:
            # Wait for page to stabilize
            time.sleep(wait_time)
            
            timestamp = int(time.time())
            filename = f"{name}_{timestamp}.png"
            filepath = os.path.join(self.screenshots_dir, filename)
            
            if element:
                element.screenshot(filepath)
            else:
                self.driver.save_screenshot(filepath)
            
            print(f"📸 Screenshot saved: {filename}")
            return filepath
        except Exception as e:
            print(f"❌ Failed to take screenshot: {e}")
            return None
    
    def compare_screenshots(self, current_path, baseline_name, threshold=5.0):
        """Compare current screenshot with baseline"""
        baseline_path = os.path.join(self.baselines_dir, f"{baseline_name}.png")
        
        if not os.path.exists(baseline_path):
            # Create baseline if it doesn't exist
            if os.path.exists(current_path):
                import shutil
                shutil.copy2(current_path, baseline_path)
                print(f"📋 Created baseline: {baseline_name}.png")
                return True, "Baseline created"
        
        try:
            # Load images
            current_img = Image.open(current_path)
            baseline_img = Image.open(baseline_path)
            
            # Resize to same dimensions if needed
            if current_img.size != baseline_img.size:
                baseline_img = baseline_img.resize(current_img.size)
            
            # Convert to numpy arrays
            current_array = np.array(current_img)
            baseline_array = np.array(baseline_img)
            
            # Calculate difference
            diff = np.abs(current_array.astype(float) - baseline_array.astype(float))
            diff_percentage = np.mean(diff) / 255.0 * 100
            
            # Create diff image
            diff_img = Image.fromarray(np.uint8(diff))
            diff_path = os.path.join(self.reports_dir, f"{baseline_name}_diff.png")
            diff_img.save(diff_path)
            
            # Create comparison report image
            self.create_comparison_report(current_img, baseline_img, diff_img, baseline_name, diff_percentage)
            
            is_similar = diff_percentage < threshold
            result_msg = f"Difference: {diff_percentage:.2f}% (threshold: {threshold}%)"
            print(f"🔍 Visual comparison: {result_msg}")
            
            return is_similar, result_msg
            
        except Exception as e:
            print(f"❌ Failed to compare screenshots: {e}")
            return False, str(e)
    
    def create_comparison_report(self, current_img, baseline_img, diff_img, name, diff_percentage):
        """Create a visual comparison report"""
        try:
            # Create a side-by-side comparison image
            width = max(current_img.width, baseline_img.width)
            height = max(current_img.height, baseline_img.height)
            
            # Create report image (3 images side by side)
            report_width = width * 3 + 40  # 20px padding between images
            report_height = height + 60  # 60px for text
            
            report_img = Image.new('RGB', (report_width, report_height), 'white')
            
            # Paste images
            report_img.paste(baseline_img, (0, 30))
            report_img.paste(current_img, (width + 20, 30))
            report_img.paste(diff_img, (width * 2 + 40, 30))
            
            # Add labels
            draw = ImageDraw.Draw(report_img)
            try:
                font = ImageFont.truetype("arial.ttf", 16)
            except:
                font = ImageFont.load_default()
            
            draw.text((10, 5), "Baseline", fill='black', font=font)
            draw.text((width + 30, 5), "Current", fill='black', font=font)
            draw.text((width * 2 + 50, 5), f"Diff ({diff_percentage:.2f}%)", fill='black', font=font)
            
            # Save report
            report_path = os.path.join(self.reports_dir, f"{name}_comparison.png")
            report_img.save(report_path)
            print(f"📊 Comparison report saved: {name}_comparison.png")
            
        except Exception as e:
            print(f"⚠️  Failed to create comparison report: {e}")
    
    def test_popup_visual(self):
        """Test popup visual appearance"""
        print("\n🎨 Testing popup visual appearance...")
        
        try:
            # Navigate to extension popup
            popup_url = f"chrome-extension://{self.extension_id}/popup.html"
            self.driver.get(popup_url)
            
            # Wait for popup to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Take screenshot
            screenshot_path = self.take_screenshot("popup_main")
            
            if screenshot_path:
                # Compare with baseline
                is_similar, message = self.compare_screenshots(screenshot_path, "popup_main_baseline")
                
                self.test_results.append({
                    "test": "popup_visual",
                    "status": "PASS" if is_similar else "FAIL",
                    "message": f"Popup visual test: {message}",
                    "screenshot": screenshot_path
                })
                
                return is_similar
            else:
                self.test_results.append({
                    "test": "popup_visual",
                    "status": "ERROR",
                    "message": "Failed to take screenshot"
                })
                return False
                
        except Exception as e:
            self.test_results.append({
                "test": "popup_visual",
                "status": "ERROR",
                "message": str(e)
            })
            print(f"❌ Error in popup visual test: {e}")
            return False
    
    def test_responsive_layouts(self):
        """Test responsive layouts at different sizes"""
        print("\n📱 Testing responsive layouts...")
        
        sizes = [
            (400, 600, "popup_small"),
            (600, 800, "popup_medium"),
            (800, 600, "popup_large")
        ]
        
        all_passed = True
        
        for width, height, name in sizes:
            try:
                # Set window size
                self.driver.set_window_size(width + 100, height + 100)  # Add padding for browser chrome
                time.sleep(2)  # Allow layout to adjust
                
                # Take screenshot
                screenshot_path = self.take_screenshot(name)
                
                if screenshot_path:
                    # Compare with baseline
                    is_similar, message = self.compare_screenshots(screenshot_path, f"{name}_baseline")
                    
                    self.test_results.append({
                        "test": f"responsive_{name}",
                        "status": "PASS" if is_similar else "FAIL",
                        "message": f"Responsive {name}: {message}",
                        "screenshot": screenshot_path
                    })
                    
                    if not is_similar:
                        all_passed = False
                        print(f"❌ Responsive test failed for {name}")
                    else:
                        print(f"✅ Responsive test passed for {name}")
                else:
                    all_passed = False
                    
            except Exception as e:
                print(f"❌ Error testing {name}: {e}")
                all_passed = False
        
        return all_passed
    
    def run_all_visual_tests(self):
        """Run all visual tests"""
        print("🚀 Starting visual regression tests...")
        
        if not self.setup_driver():
            print("❌ Failed to setup driver")
            return False
        
        try:
            # Get extension ID
            if not self.get_extension_id():
                print("❌ Failed to get extension ID")
                return False
            
            # Run visual tests
            tests = [
                self.test_popup_visual,
                self.test_responsive_layouts
            ]
            
            passed = 0
            failed = 0
            
            for test in tests:
                if test():
                    passed += 1
                else:
                    failed += 1
            
            # Generate HTML report
            self.generate_html_report()
            
            # Print summary
            print(f"\n📊 Visual Test Summary:")
            print(f"✅ Passed: {passed}")
            print(f"❌ Failed: {failed}")
            print(f"📈 Success Rate: {(passed/(passed+failed)*100):.1f}%")
            
            return failed == 0
            
        finally:
            if self.driver:
                self.driver.quit()
    
    def generate_html_report(self):
        """Generate HTML report with visual comparisons"""
        try:
            html_content = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>MVAT Visual Test Report</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .test-result { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
                    .pass { border-color: #4CAF50; background-color: #f9fff9; }
                    .fail { border-color: #f44336; background-color: #fff9f9; }
                    .error { border-color: #ff9800; background-color: #fffaf0; }
                    .screenshot { max-width: 300px; margin: 10px; }
                    .comparison { display: flex; gap: 10px; }
                </style>
            </head>
            <body>
                <h1>MVAT Visual Test Report</h1>
                <p>Generated: {timestamp}</p>
            """.format(timestamp=time.strftime("%Y-%m-%d %H:%M:%S"))
            
            for result in self.test_results:
                status_class = result['status'].lower()
                html_content += f"""
                <div class="test-result {status_class}">
                    <h3>{result['test']} - {result['status']}</h3>
                    <p>{result['message']}</p>
                """
                
                if 'screenshot' in result:
                    screenshot_name = os.path.basename(result['screenshot'])
                    html_content += f'<img src="screenshots/{screenshot_name}" class="screenshot" alt="Screenshot">'
                
                html_content += "</div>"
            
            html_content += """
            </body>
            </html>
            """
            
            report_path = os.path.join(self.reports_dir, 'visual_test_report.html')
            with open(report_path, 'w') as f:
                f.write(html_content)
            
            print(f"📄 HTML report generated: {report_path}")
            
        except Exception as e:
            print(f"⚠️  Failed to generate HTML report: {e}")

def main():
    """Main test runner"""
    runner = VisualTestRunner()
    success = runner.run_all_visual_tests()
    
    if success:
        print("\n🎉 All visual tests passed!")
        sys.exit(0)
    else:
        print("\n💥 Some visual tests failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
