import { vi } from 'vitest';
import 'vitest-canvas-mock';

/** @type {Object.<string, Function>} */
const canvasContext = {
  fillRect: vi.fn(),
  clearRect: vi.fn(),
  getImageData: vi.fn((x, y, w, h) => ({
    data: new Uint8ClampedArray(w * h * 4),
    width: w,
    height: h,
    colorSpace: 'srgb'
  })),
  putImageData: vi.fn(),
  createImageData: vi.fn(),
  setTransform: vi.fn(),
  drawImage: vi.fn(),
  save: vi.fn(),
  restore: vi.fn(),
  scale: vi.fn(),
  rotate: vi.fn(),
  translate: vi.fn(),
  transform: vi.fn(),
  beginPath: vi.fn(),
  closePath: vi.fn(),
  stroke: vi.fn(),
  fill: vi.fn()
};

// Mock canvas getContext
if (typeof HTMLCanvasElement !== 'undefined') {
  HTMLCanvasElement.prototype.getContext = function () {
    return canvasContext;
  };
}

// Mock window.URL methods if window exists
if (typeof window !== 'undefined') {
  if (!window.URL) {
    window.URL = {};
  }
  if (!window.URL.createObjectURL) {
    window.URL.createObjectURL = vi.fn(() => 'mock-object-url');
  }
  if (!window.URL.revokeObjectURL) {
    window.URL.revokeObjectURL = vi.fn();
  }
}

// Provide minimal window mock if needed
if (typeof window === 'undefined') {
  global.window = {
    URL: {
      createObjectURL: vi.fn(() => 'mock-object-url'),
      revokeObjectURL: vi.fn()
    }
  };
}

// Mock requestAnimationFrame and cancelAnimationFrame
global.requestAnimationFrame = global.requestAnimationFrame ||
  ((cb) => setTimeout(cb, 0));
global.cancelAnimationFrame = global.cancelAnimationFrame ||
  ((id) => clearTimeout(id));

// Mock Image constructor
if (typeof Image === 'undefined') {
  /** @constructor */
  function MockImage() {
    this.width = 0;
    this.height = 0;
    this.onload = null;
    this.onerror = null;
    this.src = '';
    this.complete = false;
  }
  global.Image = MockImage;
}

// Mock OffscreenCanvas
if (typeof OffscreenCanvas === 'undefined') {
  /** @constructor */
  function MockOffscreenCanvas(width, height) {
    this.width = width;
    this.height = height;
    this.getContext = () => canvasContext;
    this.transferToImageBitmap = vi.fn();
    this.convertToBlob = vi.fn(() => Promise.resolve(new Blob()));
  }
  global.OffscreenCanvas = MockOffscreenCanvas;
}

// Export mocked context for test usage
export const mockContext = canvasContext;
