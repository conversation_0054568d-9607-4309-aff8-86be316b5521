/**
 * OCR Processing Functional Tests
 * Tests for EPIC-002 Story 2.3 - OCR Processing with Tesseract.js
 *
 * These tests verify the complete OCR processing workflow including:
 * - Image file upload and validation
 * - OCR text extraction
 * - Integration with document processing pipeline
 * - Error handling and recovery
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { OCRProcessingService } from '../../src/services/OCRProcessingService.js';
import { DocumentProcessingService } from '../../src/popup/services/DocumentProcessingService.js';

// Test utilities
function createMockImageFile(name = 'test-invoice.jpg', type = 'image/jpeg', size = 1024) {
  const content = new Array(size).fill('x').join('');
  return new File([content], name, { type, lastModified: Date.now() });
}

function createCanvasWithText(text = 'Test Invoice', width = 200, height = 100) {
  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;

  const ctx = canvas.getContext('2d');
  ctx.fillStyle = 'white';
  ctx.fillRect(0, 0, width, height);
  ctx.fillStyle = 'black';
  ctx.font = '16px Arial';
  ctx.fillText(text, 10, 50);

  return canvas;
}

describe('OCR Processing Functional Tests', () => {
  let ocrService;
  let documentService;

  beforeEach(() => {
    ocrService = new OCRProcessingService();
    documentService = new DocumentProcessingService();
  });

  afterEach(async () => {
    if (ocrService) {
      await ocrService.cleanup();
    }
    if (documentService) {
      await documentService.cleanup();
    }
  });

  describe('End-to-End OCR Workflow', () => {
    it('should process image file through complete workflow', async () => {
      const imageFile = createMockImageFile('invoice-001.jpg', 'image/jpeg', 2048);
      const progressEvents = [];

      const progressCallback = (event) => {
        progressEvents.push(event);
      };

      // Process through document service (which uses OCR service)
      const result = await documentService.processDocument(imageFile, progressCallback);

      // Verify result structure
      expect(result).toBeDefined();
      expect(result.success).toBeDefined();
      expect(result.data || result.error).toBeDefined();

      // Verify progress events were fired
      expect(progressEvents.length).toBeGreaterThan(0);
      expect(progressEvents[0]).toHaveProperty('stage');
      expect(progressEvents[0]).toHaveProperty('progress');

      // If successful, verify data structure
      if (result.success) {
        expect(result.data).toHaveProperty('id');
        expect(result.data).toHaveProperty('filename', 'invoice-001.jpg');
        expect(result.data).toHaveProperty('fileType', 'image/jpeg');
        expect(result.data).toHaveProperty('extractionMethod');
        expect(result.data).toHaveProperty('extractedText');
      }
    }, 30000); // 30 second timeout for OCR processing

    it('should handle multiple image formats', async () => {
      const testFiles = [
        createMockImageFile('test1.jpg', 'image/jpeg'),
        createMockImageFile('test2.png', 'image/png')
      ];

      for (const file of testFiles) {
        const result = await documentService.processDocument(file);

        expect(result).toBeDefined();
        expect(result.success).toBeDefined();

        if (result.success) {
          expect(result.data.fileType).toBe(file.type);
          expect(result.data.filename).toBe(file.name);
        }
      }
    }, 60000);

    it('should provide meaningful progress updates', async () => {
      const imageFile = createMockImageFile('progress-test.jpg');
      const progressEvents = [];

      const progressCallback = (event) => {
        progressEvents.push(event);
      };

      await documentService.processDocument(imageFile, progressCallback);

      // Verify progress sequence
      expect(progressEvents.length).toBeGreaterThan(2);

      // Check for expected stages
      const stages = progressEvents.map(e => e.stage);
      expect(stages).toContain('starting');
      expect(stages).toContain('extracting');

      // Verify progress values are increasing
      const progressValues = progressEvents.map(e => e.progress);
      expect(progressValues[0]).toBeLessThanOrEqual(progressValues[progressValues.length - 1]);
    });
  });

  describe('OCR Service Integration', () => {
    it('should extract text from image with proper metadata', async () => {
      const imageFile = createMockImageFile('metadata-test.jpg', 'image/jpeg', 5120);

      const result = await ocrService.extractTextFromImage(imageFile, {
        language: 'pol+eng',
        enablePreprocessing: true,
        minConfidence: 60
      });

      expect(result).toHaveProperty('success');
      expect(result).toHaveProperty('text');
      expect(result).toHaveProperty('confidence');
      expect(result).toHaveProperty('metadata');

      if (result.success) {
        expect(result.metadata).toHaveProperty('fileName', 'metadata-test.jpg');
        expect(result.metadata).toHaveProperty('fileSize', 5120);
        expect(result.metadata).toHaveProperty('extractionMethod', 'ocr');
        expect(result.metadata).toHaveProperty('ocrEngine', 'tesseract');
        expect(result.metadata).toHaveProperty('trackingId');
        expect(result.metadata).toHaveProperty('processingTime');
      }
    }, 20000);

    it('should handle batch processing correctly', async () => {
      const files = [
        createMockImageFile('batch1.jpg', 'image/jpeg'),
        createMockImageFile('batch2.png', 'image/png'),
        createMockImageFile('batch3.jpg', 'image/jpeg')
      ];

      const results = await ocrService.processBatch(files, {
        language: 'pol+eng',
        enablePreprocessing: false // Faster for testing
      });

      expect(results).toHaveLength(3);

      results.forEach((result, index) => {
        expect(result).toHaveProperty('file', files[index].name);
        expect(result).toHaveProperty('index', index);
        expect(result).toHaveProperty('success');
      });
    }, 45000);
  });

  describe('Error Handling and Recovery', () => {
    it('should handle invalid file types gracefully', async () => {
      const invalidFile = new File(['test'], 'test.txt', { type: 'text/plain' });

      const result = await documentService.processDocument(invalidFile);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Unsupported file type');
    });

    it('should handle oversized files', async () => {
      // Create a file larger than 10MB
      const largeFile = createMockImageFile('large.jpg', 'image/jpeg', 11 * 1024 * 1024);

      const result = await documentService.processDocument(largeFile);

      expect(result.success).toBe(false);
      expect(result.error).toContain('File too large');
    });

    it('should provide fallback when OCR fails', async () => {
      const imageFile = createMockImageFile('fallback-test.jpg');

      // Mock OCR service to fail
      const originalExtractText = ocrService.extractTextFromImage;
      ocrService.extractTextFromImage = async () => {
        throw new Error('OCR service unavailable');
      };

      const result = await documentService.processDocument(imageFile);

      // Should still attempt processing with fallback
      expect(result).toBeDefined();

      // Restore original method
      ocrService.extractTextFromImage = originalExtractText;
    });
  });

  describe('Performance and Memory', () => {
    it('should process images within reasonable time limits', async () => {
      const imageFile = createMockImageFile('performance-test.jpg', 'image/jpeg', 2048);

      const startTime = Date.now();
      const result = await ocrService.extractTextFromImage(imageFile);
      const processingTime = Date.now() - startTime;

      // Should complete within 30 seconds
      expect(processingTime).toBeLessThan(30000);

      if (result.success) {
        expect(result.metadata.processingTime).toBeGreaterThan(0);
        expect(result.metadata.processingTime).toBeLessThan(processingTime);
      }
    }, 35000);

    it('should handle multiple concurrent processing requests', async () => {
      const files = Array.from({ length: 3 }, (_, i) =>
        createMockImageFile(`concurrent-${i}.jpg`, 'image/jpeg', 1024)
      );

      const startTime = Date.now();

      // Process files concurrently
      const promises = files.map(file =>
        ocrService.extractTextFromImage(file, { enablePreprocessing: false })
      );

      const results = await Promise.all(promises);
      const totalTime = Date.now() - startTime;

      expect(results).toHaveLength(3);

      // Concurrent processing should be faster than sequential
      expect(totalTime).toBeLessThan(60000); // Should complete within 1 minute

      results.forEach(result => {
        expect(result).toHaveProperty('success');
        expect(result).toHaveProperty('metadata');
      });
    }, 65000);
  });

  describe('Language Detection and Processing', () => {
    it('should detect and process Polish text correctly', async () => {
      const polishFile = createMockImageFile('faktura-pl.jpg', 'image/jpeg');

      const result = await ocrService.extractTextFromImage(polishFile, {
        language: 'pol+eng',
        enablePreprocessing: true
      });

      if (result.success) {
        expect(result.language).toBeDefined();
        expect(result.metadata.extractionMethod).toBe('ocr');
      }
    }, 20000);

    it('should handle mixed language content', async () => {
      const mixedFile = createMockImageFile('invoice-mixed.jpg', 'image/jpeg');

      const result = await ocrService.extractTextFromImage(mixedFile, {
        language: 'pol+eng',
        enablePreprocessing: true
      });

      if (result.success) {
        expect(result.language).toBeDefined();
        expect(['pol', 'eng', 'pol+eng']).toContain(result.language);
      }
    }, 20000);
  });

  describe('Integration with Document Processing Pipeline', () => {
    it('should integrate seamlessly with PDF processing fallback', async () => {
      // This test verifies that image processing works as a fallback
      // when PDF text extraction fails

      const imageFile = createMockImageFile('pdf-fallback.jpg', 'image/jpeg');

      const result = await documentService.processDocument(imageFile);

      expect(result).toBeDefined();

      if (result.success) {
        expect(result.data.extractionMethod).toMatch(/ocr/);
        expect(result.data.extractedText).toBeDefined();
      }
    });

    it('should maintain consistent data structure across processing methods', async () => {
      const imageFile = createMockImageFile('structure-test.jpg', 'image/jpeg');

      const result = await documentService.processDocument(imageFile);

      if (result.success) {
        // Verify standard document processing structure
        expect(result.data).toHaveProperty('id');
        expect(result.data).toHaveProperty('filename');
        expect(result.data).toHaveProperty('fileSize');
        expect(result.data).toHaveProperty('fileType');
        expect(result.data).toHaveProperty('processedAt');
        expect(result.data).toHaveProperty('extractionMethod');
        expect(result.data).toHaveProperty('extractedText');

        // Verify invoice data extraction structure
        expect(result.data).toHaveProperty('number');
        expect(result.data).toHaveProperty('date');
        expect(result.data).toHaveProperty('seller_name');
        expect(result.data).toHaveProperty('buyer_name');
        expect(result.data).toHaveProperty('total_net');
        expect(result.data).toHaveProperty('total_vat');
        expect(result.data).toHaveProperty('total_gross');
        expect(result.data).toHaveProperty('currency');
        expect(result.data).toHaveProperty('status');
      }
    });
  });
});
