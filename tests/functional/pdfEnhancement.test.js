/**
 * Functional Tests for Enhanced PDF Processing
 * Tests the complete enhanced PDF processing workflow
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { PDFProcessingService } from '../../src/services/PDFProcessingService.js';
import { PDFProgressTracker } from '../../src/services/PDFProgressTracker.js';
import { PDFMetadataExtractor } from '../../src/services/PDFMetadataExtractor.js';
import { performanceMonitor, memoryOptimizer, createBatchProcessor } from '../../src/utils/pdfPerformanceUtils.js';

// Mock Chrome environment
global.chrome = {
  runtime: {
    getURL: vi.fn((path) => `chrome-extension://test-id/${path}`)
  }
};

// Mock performance API
global.performance = {
  memory: {
    usedJSHeapSize: 50 * 1024 * 1024,
    totalJSHeapSize: 100 * 1024 * 1024,
    jsHeapSizeLimit: 2 * 1024 * 1024 * 1024
  }
};

// Mock PDF.js
vi.mock('pdfjs-dist', () => ({
  GlobalWorkerOptions: {
    workerSrc: ''
  },
  getDocument: vi.fn(() => ({
    promise: Promise.resolve({
      numPages: 3,
      pdfInfo: { version: '1.7', isLinearized: false },
      getPage: vi.fn((pageNum) => Promise.resolve({
        getTextContent: vi.fn(() => Promise.resolve({
          items: [
            { str: 'FAKTURA' },
            { str: 'Nr:' },
            { str: `FV/2024/00${pageNum}` },
            { str: 'Kwota:' },
            { str: '1234.56' },
            { str: 'PLN' },
            { str: 'Sprzedawca:' },
            { str: 'Test' },
            { str: 'Company' }
          ]
        })),
        getViewport: vi.fn(() => ({
          width: 595,
          height: 842,
          rotation: 0
        })),
        getAnnotations: vi.fn(() => Promise.resolve([])),
        cleanup: vi.fn()
      })),
      getMetadata: vi.fn(() => Promise.resolve({
        info: {
          Title: 'Enhanced Test Invoice',
          Author: 'Test Company Enhanced',
          CreationDate: 'D:20240115100000+01\'00\''
        }
      })),
      getOutline: vi.fn(() => Promise.resolve([]))
    })
  }))
}));

describe('Enhanced PDF Processing Integration Tests', () => {
  let pdfService;
  let progressTracker;
  let metadataExtractor;
  let mockInvoicePDF;
  let mockLargePDF;

  beforeEach(() => {
    pdfService = new PDFProcessingService();
    progressTracker = new PDFProgressTracker({ persistProgress: false });
    metadataExtractor = new PDFMetadataExtractor();

    // Mock enhanced invoice PDF file
    mockInvoicePDF = {
      name: 'enhanced-invoice-fv-2024-001.pdf',
      type: 'application/pdf',
      size: 3 * 1024 * 1024, // 3MB
      lastModified: Date.now(),
      arrayBuffer: vi.fn().mockResolvedValue(new ArrayBuffer(3072))
    };

    // Mock large PDF file
    mockLargePDF = {
      name: 'large-enhanced-document.pdf',
      type: 'application/pdf',
      size: 25 * 1024 * 1024, // 25MB
      lastModified: Date.now(),
      arrayBuffer: vi.fn().mockResolvedValue(new ArrayBuffer(25 * 1024 * 1024))
    };

    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Enhanced PDF Processing Workflow', () => {
    it('should process PDF with comprehensive metadata extraction', async () => {
      const progressEvents = [];
      const progressCallback = (event) => progressEvents.push(event);

      const result = await pdfService.extractText(mockInvoicePDF, {
        onProgress: progressCallback,
        extractMetadata: true,
        optimizePerformance: true
      });

      // Verify successful processing
      expect(result.success).toBe(true);
      expect(result.text).toBeTruthy();
      expect(result.metadata).toBeDefined();

      // Verify enhanced metadata
      expect(result.metadata.extractionMethod).toBe('pdf_text_enhanced');
      expect(result.metadata.comprehensive).toBeDefined();
      expect(result.metadata.performance).toBeDefined();
      expect(result.metadata.trackingId).toBeTruthy();

      // Verify extracted content
      expect(result.text).toContain('FAKTURA');
      expect(result.text).toContain('FV/2024/001');
      expect(result.text).toContain('Test Company');

      // Verify progress tracking
      expect(progressEvents.length).toBeGreaterThan(0);
      const stages = progressEvents.map(e => e.stage);
      expect(stages).toContain('initializing');
      expect(stages).toContain('loading');
      expect(stages).toContain('extracting');
      expect(stages).toContain('complete');

      // Verify performance metrics
      expect(result.metadata.performance.totalTime).toBeGreaterThan(0);
      expect(result.metadata.performance.performance.score).toBeGreaterThan(0);
    });

    it('should handle large PDF files with performance optimization', async () => {
      const result = await pdfService.extractText(mockLargePDF, {
        optimizePerformance: true,
        extractMetadata: true
      });

      expect(result.success).toBe(true);
      expect(result.metadata.performance).toBeDefined();
      expect(result.metadata.performance.peakMemory).toBeGreaterThan(0);
    });

    it('should track detailed progress with memory monitoring', async () => {
      const progressEvents = [];
      const progressCallback = (event) => {
        progressEvents.push({
          ...event,
          timestamp: Date.now()
        });
      };

      await pdfService.extractText(mockInvoicePDF, {
        onProgress: progressCallback,
        trackingId: 'test-tracking-enhanced'
      });

      // Verify detailed progress information
      const extractingEvents = progressEvents.filter(e => e.stage === 'extracting');
      expect(extractingEvents.length).toBeGreaterThan(0);

      // Check for memory usage information
      const eventsWithMemory = progressEvents.filter(e => e.memoryUsage);
      expect(eventsWithMemory.length).toBeGreaterThan(0);
    });

    it('should provide comprehensive error handling and recovery', async () => {
      // Mock a PDF that fails during processing
      const mockFailingPDF = {
        ...mockInvoicePDF,
        arrayBuffer: vi.fn().mockRejectedValue(new Error('File read error'))
      };

      const result = await pdfService.extractText(mockFailingPDF);

      expect(result.success).toBe(false);
      expect(result.error).toContain('File read error');
      expect(result.metadata.trackingId).toBeTruthy();
      expect(result.metadata.error).toBeTruthy();
    });
  });

  describe('Progress Tracking Integration', () => {
    it('should track session lifecycle completely', async () => {
      const trackingId = 'lifecycle-test';
      const sessionTracker = progressTracker.startTracking(trackingId, {
        fileName: mockInvoicePDF.name,
        fileSize: mockInvoicePDF.size,
        totalPages: 3
      });

      // Simulate processing stages
      sessionTracker.updateStage('initializing', 0);
      sessionTracker.updateStage('loading', 20);
      sessionTracker.updateStage('extracting', 50);
      sessionTracker.updateStage('analyzing', 80);
      sessionTracker.updateStage('finalizing', 95);

      const result = { success: true, text: 'extracted text' };
      sessionTracker.complete(result);

      const finalStatus = sessionTracker.getStatus();
      expect(finalStatus.status).toBe('completed');
      expect(finalStatus.currentProgress).toBe(100);
      expect(finalStatus.processingTime).toBeGreaterThan(0);
    });

    it('should handle concurrent session tracking', async () => {
      const session1 = progressTracker.startTracking('concurrent-1', {
        fileName: 'file1.pdf',
        fileSize: 1000
      });

      const session2 = progressTracker.startTracking('concurrent-2', {
        fileName: 'file2.pdf',
        fileSize: 2000
      });

      session1.updateStage('extracting', 50);
      session2.updateStage('loading', 30);

      const activeSessions = progressTracker.getActiveSessions();
      expect(activeSessions).toHaveLength(2);

      session1.complete({ success: true });
      session2.cancel();

      const remainingActive = progressTracker.getActiveSessions();
      expect(remainingActive).toHaveLength(0);
    });
  });

  describe('Metadata Extraction Integration', () => {
    it('should extract comprehensive metadata with progress tracking', async () => {
      const progressEvents = [];
      const onProgress = (event) => progressEvents.push(event);

      const result = await metadataExtractor.extractMetadata(mockInvoicePDF, {
        onProgress,
        includeStructure: true,
        includeSecurity: true,
        analyzeContent: true
      });

      expect(result.success).toBe(true);
      expect(result.metadata).toBeDefined();

      // Verify basic metadata
      expect(result.metadata.fileName).toBe(mockInvoicePDF.name);
      expect(result.metadata.fileSize).toBe(mockInvoicePDF.size);
      expect(result.metadata.numPages).toBe(3);

      // Verify document analysis
      expect(result.metadata.pageInfo).toBeDefined();
      expect(result.metadata.documentType).toBeTruthy();

      // Verify content analysis
      expect(result.metadata.content).toBeDefined();
      expect(result.metadata.content.invoiceIndicators).toBeDefined();
      expect(result.metadata.content.invoiceIndicators.isLikelyInvoice).toBe(true);

      // Verify progress tracking
      expect(progressEvents.length).toBeGreaterThan(0);
    });

    it('should detect invoice content with high confidence', async () => {
      const result = await metadataExtractor.extractMetadata(mockInvoicePDF, {
        analyzeContent: true
      });

      const invoiceAnalysis = result.metadata.content.invoiceIndicators;
      expect(invoiceAnalysis.isLikelyInvoice).toBe(true);
      expect(invoiceAnalysis.confidence).toBeGreaterThan(50);
      expect(invoiceAnalysis.indicators.length).toBeGreaterThan(0);
    });
  });

  describe('Performance Monitoring Integration', () => {
    it('should monitor performance metrics during processing', async () => {
      const operationId = 'perf-test-1';
      const monitor = performanceMonitor.startMonitoring(operationId, {
        trackMemory: true,
        trackTime: true
      });

      // Simulate processing work
      monitor.checkpoint('start');
      await new Promise(resolve => setTimeout(resolve, 100));
      monitor.checkpoint('middle');
      await new Promise(resolve => setTimeout(resolve, 100));

      const finalMetrics = monitor.stop();

      expect(finalMetrics).toBeDefined();
      expect(finalMetrics.totalTime).toBeGreaterThan(100);
      expect(finalMetrics.checkpoints).toBeGreaterThan(0);
      expect(finalMetrics.performance.score).toBeGreaterThan(0);
    });

    it('should detect performance issues and warnings', async () => {
      const operationId = 'perf-test-2';
      const monitor = performanceMonitor.startMonitoring(operationId);

      // Simulate slow processing
      monitor.addWarning('Slow processing detected');

      const metrics = monitor.getMetrics(operationId);
      expect(metrics.warnings).toBe(1);

      monitor.stop();
    });
  });

  describe('Memory Optimization Integration', () => {
    it('should optimize memory usage during processing', async () => {
      let memoryOptimized = false;

      const result = await memoryOptimizer.withOptimization(async () => {
        memoryOptimized = true;
        return { success: true, data: 'test' };
      }, {
        forceGC: true,
        monitorMemory: true
      });

      expect(memoryOptimized).toBe(true);
      expect(result.success).toBe(true);
    });

    it('should handle cleanup callbacks', () => {
      let cleanupCalled = false;
      const cleanupCallback = () => { cleanupCalled = true; };

      memoryOptimizer.registerCleanup(cleanupCallback);
      memoryOptimizer.performCleanup();

      expect(cleanupCalled).toBe(true);

      memoryOptimizer.unregisterCleanup(cleanupCallback);
    });
  });

  describe('Batch Processing Integration', () => {
    it('should process multiple PDFs in batch', async () => {
      const batchProcessor = createBatchProcessor({
        maxConcurrent: 2,
        retryAttempts: 1
      });

      const files = [
        { ...mockInvoicePDF, name: 'batch-file-1.pdf' },
        { ...mockInvoicePDF, name: 'batch-file-2.pdf' },
        { ...mockInvoicePDF, name: 'batch-file-3.pdf' }
      ];

      const mockProcessor = vi.fn().mockResolvedValue({ success: true, text: 'extracted' });

      batchProcessor.addFiles(files, mockProcessor);

      const progressEvents = [];
      const onProgress = (progress) => progressEvents.push(progress);

      const result = await batchProcessor.startProcessing({
        onProgress
      });

      expect(result.completed.length).toBe(3);
      expect(result.failed.length).toBe(0);
      expect(mockProcessor).toHaveBeenCalledTimes(3);
      expect(progressEvents.length).toBeGreaterThan(0);
    });

    it('should handle batch processing with retries', async () => {
      const batchProcessor = createBatchProcessor({
        maxConcurrent: 1,
        retryAttempts: 2,
        retryDelay: 10
      });

      const files = [mockInvoicePDF];
      let attemptCount = 0;

      const mockProcessor = vi.fn().mockImplementation(() => {
        attemptCount++;
        if (attemptCount < 3) {
          return Promise.reject(new Error('Processing failed'));
        }
        return Promise.resolve({ success: true, text: 'extracted' });
      });

      batchProcessor.addFiles(files, mockProcessor);

      const result = await batchProcessor.startProcessing();

      expect(result.completed.length).toBe(1);
      expect(result.failed.length).toBe(0);
      expect(mockProcessor).toHaveBeenCalledTimes(3); // Initial + 2 retries
    });
  });

  describe('Error Recovery and Resilience', () => {
    it('should recover from partial processing failures', async () => {
      // Mock a PDF where some pages fail to process
      const mockPDFJS = await import('pdfjs-dist');
      const originalGetDocument = mockPDFJS.getDocument;

      mockPDFJS.getDocument.mockReturnValueOnce({
        promise: Promise.resolve({
          numPages: 3,
          getPage: vi.fn((pageNum) => {
            if (pageNum === 2) {
              return Promise.reject(new Error('Page 2 error'));
            }
            return Promise.resolve({
              getTextContent: vi.fn(() => Promise.resolve({
                items: [{ str: `Page ${pageNum} content` }]
              })),
              cleanup: vi.fn()
            });
          }),
          getMetadata: vi.fn(() => Promise.resolve({ info: {} }))
        })
      });

      const result = await pdfService.extractText(mockInvoicePDF);

      expect(result.success).toBe(true);
      expect(result.text).toContain('Page 1 content');
      expect(result.text).toContain('Page 3 content');
      expect(result.text).not.toContain('Page 2 content');

      // Restore original mock
      mockPDFJS.getDocument.mockImplementation(originalGetDocument);
    });

    it('should handle memory pressure gracefully', async () => {
      // Mock high memory usage
      global.performance.memory.usedJSHeapSize = 150 * 1024 * 1024; // 150MB

      const result = await pdfService.extractText(mockLargePDF, {
        optimizePerformance: true
      });

      expect(result.success).toBe(true);
      expect(result.metadata.performance.warnings).toBeDefined();
    });
  });

  describe('Integration with Existing Systems', () => {
    it('should maintain backward compatibility with existing API', async () => {
      // Test that the enhanced service still works with the old API
      const result = await pdfService.extractText(mockInvoicePDF, {
        onProgress: null,
        enableOCRFallback: true,
        maxPages: 10
      });

      expect(result.success).toBe(true);
      expect(result.text).toBeTruthy();
      expect(result.metadata).toBeDefined();
      expect(result.metadata.numPages).toBeLessThanOrEqual(10);
    });

    it('should work with existing progress callback format', async () => {
      const progressEvents = [];
      const legacyProgressCallback = (event) => {
        // Legacy format expects stage and progress
        expect(event).toHaveProperty('stage');
        expect(event).toHaveProperty('progress');
        progressEvents.push(event);
      };

      await pdfService.extractText(mockInvoicePDF, {
        onProgress: legacyProgressCallback
      });

      expect(progressEvents.length).toBeGreaterThan(0);
    });
  });
});
