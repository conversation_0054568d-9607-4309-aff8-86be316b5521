/**
 * Functional Tests for PDF Processing Integration
 * Tests the complete PDF processing workflow
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { PDFProcessingService } from '../../src/services/PDFProcessingService.js';
import { validatePDFFile, analyzeInvoiceContent, cleanExtractedText } from '../../src/utils/pdfUtils.js';

// Mock Chrome environment
global.chrome = {
  runtime: {
    getURL: vi.fn((path) => `chrome-extension://test-id/${path}`)
  }
};

// Mock PDF.js
vi.mock('pdfjs-dist', () => ({
  GlobalWorkerOptions: {
    workerSrc: ''
  },
  getDocument: vi.fn(() => ({
    promise: Promise.resolve({
      numPages: 2,
      getPage: vi.fn((pageNum) => Promise.resolve({
        getTextContent: vi.fn(() => Promise.resolve({
          items: pageNum === 1 ? [
            { str: 'FAKTURA' },
            { str: 'Nr:' },
            { str: 'FV/2024/001' },
            { str: 'Data:' },
            { str: '2024-01-15' },
            { str: 'Sprzedawca:' },
            { str: 'Test' },
            { str: 'Company' },
            { str: 'Sp.' },
            { str: 'z' },
            { str: 'o.o.' }
          ] : [
            { str: 'Nabywca:' },
            { str: 'Client' },
            { str: 'Corporation' },
            { str: 'Kwota' },
            { str: 'netto:' },
            { str: '1000.00' },
            { str: 'PLN' },
            { str: 'VAT:' },
            { str: '230.00' },
            { str: 'PLN' },
            { str: 'Razem:' },
            { str: '1230.00' },
            { str: 'PLN' }
          ]
        })),
        cleanup: vi.fn()
      })),
      getMetadata: vi.fn(() => Promise.resolve({
        info: {
          Title: 'Invoice FV/2024/001',
          Author: 'Test Company',
          CreationDate: '2024-01-15T10:00:00Z'
        }
      }))
    })
  }))
}));

describe('PDF Processing Integration Tests', () => {
  let pdfService;
  let mockInvoicePDF;
  let mockLargePDF;
  let mockCorruptedPDF;

  beforeEach(() => {
    pdfService = new PDFProcessingService();

    // Mock invoice PDF file
    mockInvoicePDF = {
      name: 'invoice-fv-2024-001.pdf',
      type: 'application/pdf',
      size: 2 * 1024 * 1024, // 2MB
      lastModified: Date.now(),
      arrayBuffer: vi.fn().mockResolvedValue(new ArrayBuffer(2048))
    };

    // Mock large PDF file
    mockLargePDF = {
      name: 'large-document.pdf',
      type: 'application/pdf',
      size: 15 * 1024 * 1024, // 15MB
      lastModified: Date.now(),
      arrayBuffer: vi.fn().mockResolvedValue(new ArrayBuffer(15 * 1024 * 1024))
    };

    // Mock corrupted PDF file
    mockCorruptedPDF = {
      name: 'corrupted.pdf',
      type: 'application/pdf',
      size: 1024,
      lastModified: Date.now(),
      arrayBuffer: vi.fn().mockRejectedValue(new Error('File corrupted'))
    };

    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('End-to-End PDF Processing Workflow', () => {
    it('should process a complete invoice PDF successfully', async () => {
      const progressEvents = [];
      const progressCallback = (event) => progressEvents.push(event);

      const result = await pdfService.extractText(mockInvoicePDF, {
        onProgress: progressCallback
      });

      // Verify successful processing
      expect(result.success).toBe(true);
      expect(result.text).toBeTruthy();
      expect(result.metadata).toBeDefined();

      // Verify extracted content
      expect(result.text).toContain('FAKTURA');
      expect(result.text).toContain('FV/2024/001');
      expect(result.text).toContain('Test Company');
      expect(result.text).toContain('1230.00');

      // Verify metadata
      expect(result.metadata.numPages).toBe(2);
      expect(result.metadata.extractionMethod).toBe('pdf_text');
      expect(result.metadata.fileName).toBe('invoice-fv-2024-001.pdf');

      // Verify progress tracking
      expect(progressEvents.length).toBeGreaterThan(0);
      expect(progressEvents[0]).toMatchObject({
        stage: 'loading',
        progress: 0
      });
      expect(progressEvents[progressEvents.length - 1]).toMatchObject({
        stage: 'complete',
        progress: 100
      });
    });

    it('should handle large PDF files within limits', async () => {
      // Configure service for larger files
      pdfService.updateConfig({ maxFileSize: 20 * 1024 * 1024 }); // 20MB

      const result = await pdfService.extractText(mockLargePDF);

      expect(result.success).toBe(true);
      expect(result.metadata.fileSize).toBe(15 * 1024 * 1024);
    });

    it('should reject oversized PDF files', async () => {
      const result = await pdfService.extractText(mockLargePDF);

      expect(result.success).toBe(false);
      expect(result.error).toContain('File too large');
    });

    it('should handle corrupted PDF files gracefully', async () => {
      const result = await pdfService.extractText(mockCorruptedPDF);

      expect(result.success).toBe(false);
      expect(result.error).toContain('File corrupted');
    });
  });

  describe('File Validation Integration', () => {
    it('should validate correct PDF files', () => {
      const validation = validatePDFFile(mockInvoicePDF);

      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
      expect(validation.fileInfo.name).toBe('invoice-fv-2024-001.pdf');
    });

    it('should reject invalid file types', () => {
      const invalidFile = {
        ...mockInvoicePDF,
        type: 'image/jpeg',
        name: 'image.jpg'
      };

      const validation = validatePDFFile(invalidFile);

      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain(
        expect.stringContaining('Invalid file type')
      );
    });

    it('should provide warnings for suspicious files', () => {
      const suspiciousFile = {
        ...mockInvoicePDF,
        name: 'invoice.exe.pdf'
      };

      const validation = validatePDFFile(suspiciousFile);

      expect(validation.warnings.length).toBeGreaterThan(0);
    });
  });

  describe('Text Quality Analysis Integration', () => {
    it('should analyze high-quality invoice text', async () => {
      const result = await pdfService.extractText(mockInvoicePDF);
      const analysis = analyzeInvoiceContent(result.text);

      expect(analysis.hasInvoiceContent).toBe(true);
      expect(analysis.confidence).toBeGreaterThan(50);
      expect(analysis.foundKeywords).toContain('faktura');
    });

    it('should clean extracted text properly', async () => {
      const result = await pdfService.extractText(mockInvoicePDF);
      const cleanedText = cleanExtractedText(result.text);

      expect(cleanedText).toBeTruthy();
      expect(cleanedText.trim()).toBe(cleanedText);
      expect(cleanedText).not.toMatch(/\s{2,}/); // No multiple spaces
    });
  });

  describe('Progress Tracking Integration', () => {
    it('should provide detailed progress updates', async () => {
      const progressEvents = [];
      const progressCallback = (event) => {
        progressEvents.push({
          ...event,
          timestamp: Date.now()
        });
      };

      await pdfService.extractText(mockInvoicePDF, {
        onProgress: progressCallback
      });

      // Verify progress sequence
      const stages = progressEvents.map(e => e.stage);
      expect(stages).toContain('loading');
      expect(stages).toContain('parsing');
      expect(stages).toContain('extracting');
      expect(stages).toContain('complete');

      // Verify progress values are increasing
      const progressValues = progressEvents.map(e => e.progress);
      for (let i = 1; i < progressValues.length; i++) {
        expect(progressValues[i]).toBeGreaterThanOrEqual(progressValues[i - 1]);
      }
    });

    it('should include page-level progress for multi-page PDFs', async () => {
      const progressEvents = [];
      const progressCallback = (event) => progressEvents.push(event);

      await pdfService.extractText(mockInvoicePDF, {
        onProgress: progressCallback
      });

      // Check for page-specific progress
      const extractingEvents = progressEvents.filter(e => e.stage === 'extracting');
      expect(extractingEvents.length).toBeGreaterThan(1); // Multiple progress updates during extraction
    });
  });

  describe('Error Recovery Integration', () => {
    it('should continue processing when individual pages fail', async () => {
      // Mock one page to fail
      const mockGetDocument = vi.mocked(await import('pdfjs-dist')).getDocument;
      mockGetDocument.mockReturnValueOnce({
        promise: Promise.resolve({
          numPages: 2,
          getPage: vi.fn((pageNum) => {
            if (pageNum === 1) {
              return Promise.reject(new Error('Page 1 error'));
            }
            return Promise.resolve({
              getTextContent: vi.fn(() => Promise.resolve({
                items: [{ str: 'Page 2 content' }]
              })),
              cleanup: vi.fn()
            });
          }),
          getMetadata: vi.fn(() => Promise.resolve({ info: {} }))
        })
      });

      const result = await pdfService.extractText(mockInvoicePDF);

      expect(result.success).toBe(true);
      expect(result.text).toContain('Page 2 content');
      expect(result.text).not.toContain('Page 1');
    });

    it('should handle metadata extraction failures gracefully', async () => {
      const metadataResult = await pdfService.getMetadata(mockCorruptedPDF);

      expect(metadataResult.success).toBe(false);
      expect(metadataResult.error).toBeTruthy();
      expect(metadataResult.metadata).toBeNull();
    });
  });

  describe('Memory Management Integration', () => {
    it('should clean up resources after processing', async () => {
      const mockCleanup = vi.fn();

      // Mock page cleanup
      const mockGetDocument = vi.mocked(await import('pdfjs-dist')).getDocument;
      mockGetDocument.mockReturnValueOnce({
        promise: Promise.resolve({
          numPages: 1,
          getPage: vi.fn(() => Promise.resolve({
            getTextContent: vi.fn(() => Promise.resolve({
              items: [{ str: 'test' }]
            })),
            cleanup: mockCleanup
          })),
          getMetadata: vi.fn(() => Promise.resolve({ info: {} }))
        })
      });

      await pdfService.extractText(mockInvoicePDF);

      expect(mockCleanup).toHaveBeenCalled();
    });
  });

  describe('Configuration Integration', () => {
    it('should respect custom configuration settings', async () => {
      // Configure for single page processing
      pdfService.updateConfig({ maxPages: 1 });

      const result = await pdfService.extractText(mockInvoicePDF);

      expect(result.success).toBe(true);
      expect(result.metadata.numPages).toBe(1);
      expect(result.metadata.totalPages).toBe(2); // Original PDF has 2 pages
    });

    it('should use default configuration when not specified', () => {
      const config = pdfService.getConfig();

      expect(config.maxFileSize).toBe(10 * 1024 * 1024); // 10MB default
      expect(config.maxPages).toBe(100); // 100 pages default
      expect(config.textExtractionTimeout).toBe(30000); // 30 seconds default
    });
  });
});
