/**
 * Functional Tests for File Validation Consolidation
 * Tests end-to-end file validation workflows using the consolidated service
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { consolidatedFileValidationService } from '../../src/services/ConsolidatedFileValidationService.js';

// Mock dependencies for functional testing
vi.mock('../../src/utils/securityChecks.js', () => ({
  performSecurityChecks: vi.fn().mockResolvedValue({
    isValid: true,
    errors: [],
    warnings: []
  })
}));

vi.mock('../../src/utils/pdfUtils.js', () => ({
  isPDFFile: vi.fn((file) => file.type === 'application/pdf'),
  validatePDFFile: vi.fn(() => ({ isValid: true, errors: [], warnings: [] })),
  analyzeInvoiceContent: vi.fn()
}));

describe('File Validation Consolidation - Functional Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    consolidatedFileValidationService.clearCache();
  });

  afterEach(() => {
    // Clean up any test files or resources
  });

  describe('PDF File Validation Workflow', () => {
    it('should validate a legitimate PDF file', async () => {
      // Create a mock PDF file with proper structure
      const pdfContent = new Uint8Array([
        0x25, 0x50, 0x44, 0x46, 0x2D, // %PDF- header
        0x31, 0x2E, 0x34, 0x0A, // 1.4\n
        ...Array(1000).fill(0x20) // Padding to make it substantial
      ]);

      const pdfFile = new File([pdfContent], 'invoice.pdf', {
        type: 'application/pdf',
        lastModified: Date.now()
      });

      const result = await consolidatedFileValidationService.validateFile(pdfFile);

      expect(result.isValid).toBe(true);
      expect(result.fileName).toBe('invoice.pdf');
      expect(result.fileType).toBe('application/pdf');
      expect(result.errors).toHaveLength(0);
      expect(result.performance.validationTime).toBeGreaterThan(0);
    });

    it('should reject PDF files that are too large', async () => {
      // Create a large PDF file (20MB)
      const largeContent = new Uint8Array(20 * 1024 * 1024);
      largeContent.set([0x25, 0x50, 0x44, 0x46, 0x2D]); // PDF header

      const largePdfFile = new File([largeContent], 'large.pdf', {
        type: 'application/pdf'
      });

      const result = await consolidatedFileValidationService.validateFile(largePdfFile);

      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('too large'))).toBe(true);
    });

    it('should detect PDF MIME type spoofing', async () => {
      // Create a file with PDF extension but different content
      const fakeContent = new Uint8Array([
        0x89, 0x50, 0x4E, 0x47, // PNG signature
        0x0D, 0x0A, 0x1A, 0x0A
      ]);

      const spoofedFile = new File([fakeContent], 'fake.pdf', {
        type: 'application/pdf'
      });

      const result = await consolidatedFileValidationService.validateFile(spoofedFile);

      // Should detect the mismatch in content validation
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('Invalid PDF'))).toBe(true);
    });
  });

  describe('Image File Validation Workflow', () => {
    it('should validate a legitimate JPEG file', async () => {
      // Create a mock JPEG file with proper header
      const jpegContent = new Uint8Array([
        0xFF, 0xD8, 0xFF, 0xE0, // JPEG header
        0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, // JFIF
        ...Array(1000).fill(0x00) // Padding
      ]);

      const jpegFile = new File([jpegContent], 'receipt.jpg', {
        type: 'image/jpeg'
      });

      const result = await validationService.validateFile(jpegFile);

      expect(result.isValid).toBe(true);
      expect(result.fileName).toBe('receipt.jpg');
      expect(result.fileType).toBe('image/jpeg');
      expect(result.errors).toHaveLength(0);
    });

    it('should validate a legitimate PNG file', async () => {
      // Create a mock PNG file with proper signature
      const pngContent = new Uint8Array([
        0x89, 0x50, 0x4E, 0x47, // PNG signature
        0x0D, 0x0A, 0x1A, 0x0A,
        ...Array(1000).fill(0x00) // Padding
      ]);

      const pngFile = new File([pngContent], 'invoice.png', {
        type: 'image/png'
      });

      const result = await validationService.validateFile(pngFile);

      expect(result.isValid).toBe(true);
      expect(result.fileName).toBe('invoice.png');
      expect(result.fileType).toBe('image/png');
      expect(result.errors).toHaveLength(0);
    });
  });

  describe('Security Validation Workflow', () => {
    it('should reject executable files', async () => {
      // Create a mock executable file
      const exeContent = new Uint8Array([
        0x4D, 0x5A, // MZ header (Windows executable)
        ...Array(1000).fill(0x00)
      ]);

      const exeFile = new File([exeContent], 'malware.exe', {
        type: 'application/octet-stream'
      });

      const result = await validationService.validateFile(exeFile);

      expect(result.isValid).toBe(false);
      expect(result.errors.some(error =>
        error.includes('not allowed') ||
        error.includes('dangerous')
      )).toBe(true);
    });

    it('should detect suspicious file names', async () => {
      const suspiciousFile = new File(['content'], 'autorun.inf', {
        type: 'text/plain'
      });

      const result = await validationService.validateFile(suspiciousFile);

      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('not allowed'))).toBe(true);
    });

    it('should detect path traversal attempts', async () => {
      const maliciousFile = new File(['content'], '../../../etc/passwd', {
        type: 'text/plain'
      });

      const result = await validationService.validateFile(maliciousFile);

      expect(result.isValid).toBe(false);
      expect(result.errors.some(error =>
        error.includes('path traversal') ||
        error.includes('invalid characters')
      )).toBe(true);
    });
  });

  describe('Multiple Files Validation Workflow', () => {
    it('should validate multiple valid files', async () => {
      const files = [
        new File(['PDF content'], 'doc1.pdf', { type: 'application/pdf' }),
        new File(['JPEG content'], 'img1.jpg', { type: 'image/jpeg' }),
        new File(['PNG content'], 'img2.png', { type: 'image/png' })
      ];

      const result = await validationService.validateFiles(files);

      expect(result.isValid).toBe(true);
      expect(result.totalFiles).toBe(3);
      expect(result.results).toHaveLength(3);
      expect(result.results.every(r => r.isValid)).toBe(true);
    });

    it('should reject batch with invalid files', async () => {
      const files = [
        new File(['PDF content'], 'valid.pdf', { type: 'application/pdf' }),
        new File(['EXE content'], 'malware.exe', { type: 'application/octet-stream' }),
        new File([''], 'empty.pdf', { type: 'application/pdf' }) // Empty file
      ];

      const result = await validationService.validateFiles(files);

      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.results.some(r => !r.isValid)).toBe(true);
    });

    it('should detect duplicate files in batch', async () => {
      const files = [
        new File(['content'], 'doc.pdf', { type: 'application/pdf' }),
        new File(['content'], 'doc.pdf', { type: 'application/pdf' }) // Duplicate
      ];

      const result = await validationService.validateFiles(files);

      expect(result.warnings.some(warning =>
        warning.includes('Duplicate files detected')
      )).toBe(true);
    });

    it('should reject too many files', async () => {
      const files = Array(15).fill(null).map((_, i) =>
        new File(['content'], `file${i}.pdf`, { type: 'application/pdf' })
      );

      const result = await validationService.validateFiles(files);

      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('Too many files'))).toBe(true);
    });
  });

  describe('Performance and Edge Cases', () => {
    it('should handle empty files gracefully', async () => {
      const emptyFile = new File([], 'empty.pdf', { type: 'application/pdf' });

      const result = await validationService.validateFile(emptyFile);

      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('empty'))).toBe(true);
      expect(result.performance.validationTime).toBeGreaterThan(0);
    });

    it('should handle files with no extension', async () => {
      const noExtFile = new File(['content'], 'document', { type: 'application/pdf' });

      const result = await validationService.validateFile(noExtFile);

      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('no extension'))).toBe(true);
    });

    it('should handle files with very long names', async () => {
      const longName = 'a'.repeat(300) + '.pdf';
      const longNameFile = new File(['content'], longName, { type: 'application/pdf' });

      const result = await validationService.validateFile(longNameFile);

      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('too long'))).toBe(true);
    });

    it('should complete validation within reasonable time', async () => {
      const normalFile = new File(['content'], 'test.pdf', { type: 'application/pdf' });

      const startTime = performance.now();
      const result = await validationService.validateFile(normalFile);
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second
      expect(result.performance.validationTime).toBeLessThan(1000);
    });
  });

  describe('Integration with Security Scanner', () => {
    it('should integrate security scanning with file validation', async () => {
      const testFile = new File(['content'], 'test.pdf', { type: 'application/pdf' });

      // First validate the file
      const validationResult = await validationService.validateFile(testFile);

      // Then perform security scan
      const securityResult = await securityScanner.scanFile(testFile);

      expect(validationResult).toBeDefined();
      expect(securityResult).toBeDefined();
      expect(securityResult.isSecure).toBeDefined();
      expect(securityResult.riskScore).toBeGreaterThanOrEqual(0);
      expect(securityResult.riskScore).toBeLessThanOrEqual(1);
    });

    it('should detect high-risk files through security scanning', async () => {
      // Create a file that should trigger security warnings
      const suspiciousFile = new File(['content'], 'suspicious.exe', {
        type: 'application/octet-stream'
      });

      const securityResult = await securityScanner.scanFile(suspiciousFile);

      expect(securityResult.riskScore).toBeGreaterThan(0.5);
      expect(securityResult.threats.length).toBeGreaterThan(0);
    });
  });

  describe('Error Recovery and Resilience', () => {
    it('should handle corrupted file objects gracefully', async () => {
      const corruptedFile = {
        name: 'test.pdf',
        size: 'invalid', // Invalid size
        type: null // Invalid type
      };

      const result = await validationService.validateFile(corruptedFile);

      expect(result).toBeDefined();
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('should continue validation even if some checks fail', async () => {
      // Create a file that might cause some validation steps to fail
      const problematicFile = new File(['content'], 'test.pdf', {
        type: 'application/pdf'
      });

      // Mock one of the validation methods to throw an error
      const originalMethod = validationService.performAdvancedValidation;
      validationService.performAdvancedValidation = async () => {
        throw new Error('Simulated validation error');
      };

      const result = await validationService.validateFile(problematicFile);

      // Should still return a result
      expect(result).toBeDefined();
      expect(result.fileName).toBe('test.pdf');

      // Restore original method
      validationService.performAdvancedValidation = originalMethod;
    });
  });
});
