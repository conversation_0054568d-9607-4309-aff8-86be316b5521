/**
 * Functional Tests for Environment Configuration
 *
 * Tests the complete environment configuration workflow including
 * loading, validation, storage, and integration with settings.
 *
 * ASSIGNMENT-039: Environment Configuration Setup
 * Epic: EPIC-B01 - Subscription & Monetization System
 */

import { environmentConfig } from '../../src/services/EnvironmentConfigService.js';
import { settingsService } from '../../src/services/SettingsService.js';

/**
 * Test environment configuration loading and validation
 */
async function testEnvironmentConfigLoading() {
  console.log('🧪 ENVIRONMENT CONFIGURATION LOADING TEST');
  console.log('============================================================');
  console.log('🎯 Purpose: Test environment configuration loading and validation');
  console.log('📅 Timestamp:', new Date().toISOString());
  console.log('============================================================\n');

  try {
    // Test 1: Initialize environment configuration
    console.log('📋 TEST 1: Environment Configuration Initialization');

    const initResult = await environmentConfig.initialize();
    console.log(`✅ Initialization result: ${initResult}`);
    console.log(`✅ Configuration loaded: ${environmentConfig.isLoaded}`);

    if (!initResult || !environmentConfig.isLoaded) {
      throw new Error('Environment configuration initialization failed');
    }

    // Test 2: Validate configuration structure
    console.log('\n📋 TEST 2: Configuration Structure Validation');

    const config = environmentConfig.getAll();
    const requiredSections = ['company', 'app', 'features', 'subscription', 'localization'];

    requiredSections.forEach(section => {
      if (!config[section]) {
        throw new Error(`Missing required configuration section: ${section}`);
      }
      console.log(`✅ Section '${section}' present`);
    });

    // Test 3: API key access
    console.log('\n📋 TEST 3: API Key Access');

    const deepseekKey = environmentConfig.getApiKey('deepseek');
    console.log(`✅ DeepSeek API key accessible: ${!!deepseekKey}`);
    console.log(`✅ API key format valid: ${deepseekKey ? deepseekKey.startsWith('sk-') : 'N/A'}`);

    // Test 4: Company information access
    console.log('\n📋 TEST 4: Company Information Access');

    const companyInfo = environmentConfig.getCompanyInfo();
    console.log(`✅ Company name: ${companyInfo.name || 'Not configured'}`);
    console.log(`✅ Company NIP: ${companyInfo.nip || 'Not configured'}`);
    console.log(`✅ Company email: ${companyInfo.contact?.email || 'Not configured'}`);

    // Test 5: Feature flags
    console.log('\n📋 TEST 5: Feature Flags');

    const subscriptionEnabled = environmentConfig.isFeatureEnabled('subscriptionSystem');
    const paymentEnabled = environmentConfig.isFeatureEnabled('paymentProcessing');

    console.log(`✅ Subscription system: ${subscriptionEnabled ? 'Enabled' : 'Disabled'}`);
    console.log(`✅ Payment processing: ${paymentEnabled ? 'Enabled' : 'Disabled'}`);

    // Test 6: Subscription tiers
    console.log('\n📋 TEST 6: Subscription Tiers');

    const tiers = ['starter', 'professional', 'business', 'enterprise'];
    tiers.forEach(tier => {
      const tierConfig = environmentConfig.getSubscriptionTier(tier);
      console.log(`✅ ${tier} tier: ${tierConfig.invoiceLimit !== undefined ? 'Configured' : 'Missing'}`);
      if (tierConfig.invoiceLimit !== undefined) {
        console.log(`   📊 Invoice limit: ${tierConfig.invoiceLimit === -1 ? 'Unlimited' : tierConfig.invoiceLimit}`);
      }
    });

    // Test 7: Configuration reload
    console.log('\n📋 TEST 7: Configuration Reload');

    const reloadResult = await environmentConfig.reload();
    console.log(`✅ Reload result: ${reloadResult}`);
    console.log(`✅ Still loaded after reload: ${environmentConfig.isLoaded}`);

    console.log('\n🎉 ENVIRONMENT CONFIGURATION LOADING TEST COMPLETED SUCCESSFULLY!');
    console.log('============================================================');

    return true;

  } catch (error) {
    console.error('❌ Environment configuration loading test failed:', error);
    console.error('Stack trace:', error.stack);
    return false;
  }
}

/**
 * Test settings integration with environment configuration
 */
async function testSettingsIntegration() {
  console.log('\n🧪 SETTINGS INTEGRATION TEST');
  console.log('============================================================');
  console.log('🎯 Purpose: Test integration between environment config and settings');
  console.log('📅 Timestamp:', new Date().toISOString());
  console.log('============================================================\n');

  try {
    // Test 1: Load settings with environment integration
    console.log('📋 TEST 1: Settings Loading with Environment Integration');

    const settings = await settingsService.loadSettings();
    console.log('✅ Settings loaded successfully');
    console.log(`✅ Company name from env: ${settings.company?.name || 'Not set'}`);
    console.log(`✅ API key from env: ${settings.apiKeys?.deepseek ? 'Present' : 'Not set'}`);

    // Test 2: Environment configuration override
    console.log('\n📋 TEST 2: Environment Configuration Override');

    // Check if environment values override default settings
    const defaultSettings = settingsService.getDefaultSettings();

    if (settings.company.name && settings.company.name !== defaultSettings.company.name) {
      console.log(`✅ Company name overridden by environment: ${settings.company.name}`);
    }

    if (settings.display.currency && settings.display.currency !== defaultSettings.display.currency) {
      console.log(`✅ Currency overridden by environment: ${settings.display.currency}`);
    }

    // Test 3: API key integration
    console.log('\n📋 TEST 3: API Key Integration');

    const deepseekKeyFromSettings = await settingsService.getApiKey('deepseek');
    const deepseekKeyFromEnv = environmentConfig.getApiKey('deepseek');

    console.log(`✅ API key from settings: ${deepseekKeyFromSettings ? 'Present' : 'Not set'}`);
    console.log(`✅ API key from environment: ${deepseekKeyFromEnv ? 'Present' : 'Not set'}`);

    if (deepseekKeyFromEnv && deepseekKeyFromSettings) {
      console.log(`✅ API keys match: ${deepseekKeyFromSettings === deepseekKeyFromEnv}`);
    }

    // Test 4: Settings validation
    console.log('\n📋 TEST 4: Settings Validation');

    const validation = settingsService.validateSettings(settings);
    console.log(`✅ Settings validation: ${validation.valid ? 'Passed' : 'Failed'}`);

    if (!validation.valid) {
      console.log(`⚠️ Validation errors: ${validation.errors.join(', ')}`);
    }

    console.log('\n🎉 SETTINGS INTEGRATION TEST COMPLETED SUCCESSFULLY!');
    console.log('============================================================');

    return true;

  } catch (error) {
    console.error('❌ Settings integration test failed:', error);
    console.error('Stack trace:', error.stack);
    return false;
  }
}

/**
 * Test configuration validation and error handling
 */
async function testConfigurationValidation() {
  console.log('\n🧪 CONFIGURATION VALIDATION TEST');
  console.log('============================================================');
  console.log('🎯 Purpose: Test configuration validation and error handling');
  console.log('📅 Timestamp:', new Date().toISOString());
  console.log('============================================================\n');

  try {
    // Test 1: Valid configuration access
    console.log('📋 TEST 1: Valid Configuration Access');

    const validPath = 'company.name';
    const validValue = environmentConfig.get(validPath);
    console.log(`✅ Valid path '${validPath}': ${validValue || 'Not set'}`);

    // Test 2: Invalid configuration access
    console.log('\n📋 TEST 2: Invalid Configuration Access');

    const invalidPath = 'invalid.path.that.does.not.exist';
    const invalidValue = environmentConfig.get(invalidPath, 'default');
    console.log(`✅ Invalid path '${invalidPath}': ${invalidValue}`);

    if (invalidValue === 'default') {
      console.log('✅ Default value returned for invalid path');
    }

    // Test 3: Nested object access
    console.log('\n📋 TEST 3: Nested Object Access');

    const nestedPath = 'company.address.city';
    const nestedValue = environmentConfig.get(nestedPath);
    console.log(`✅ Nested path '${nestedPath}': ${nestedValue || 'Not set'}`);

    // Test 4: Feature flag validation
    console.log('\n📋 TEST 4: Feature Flag Validation');

    const validFeature = 'subscriptionSystem';
    const invalidFeature = 'nonExistentFeature';

    const validFeatureResult = environmentConfig.isFeatureEnabled(validFeature);
    const invalidFeatureResult = environmentConfig.isFeatureEnabled(invalidFeature);

    console.log(`✅ Valid feature '${validFeature}': ${validFeatureResult}`);
    console.log(`✅ Invalid feature '${invalidFeature}': ${invalidFeatureResult}`);

    // Test 5: Subscription tier validation
    console.log('\n📋 TEST 5: Subscription Tier Validation');

    const validTier = 'professional';
    const invalidTier = 'nonExistentTier';

    const validTierConfig = environmentConfig.getSubscriptionTier(validTier);
    const invalidTierConfig = environmentConfig.getSubscriptionTier(invalidTier);

    console.log(`✅ Valid tier '${validTier}': ${Object.keys(validTierConfig).length > 0 ? 'Configured' : 'Empty'}`);
    console.log(`✅ Invalid tier '${invalidTier}': ${Object.keys(invalidTierConfig).length === 0 ? 'Empty (expected)' : 'Unexpected data'}`);

    console.log('\n🎉 CONFIGURATION VALIDATION TEST COMPLETED SUCCESSFULLY!');
    console.log('============================================================');

    return true;

  } catch (error) {
    console.error('❌ Configuration validation test failed:', error);
    console.error('Stack trace:', error.stack);
    return false;
  }
}

/**
 * Run all environment configuration functional tests
 */
async function runEnvironmentConfigTests() {
  console.log('🚀 STARTING ENVIRONMENT CONFIGURATION FUNCTIONAL TESTS');
  console.log('================================================================');
  console.log('📋 Assignment: ASSIGNMENT-039 - Environment Configuration Setup');
  console.log('🎯 Epic: EPIC-B01 - Subscription & Monetization System');
  console.log('📖 Story: STORY-B1.1 - Subscription Tier Management');
  console.log('📝 Task: TASK-B1.1.0 - Environment Configuration (Prerequisite)');
  console.log('================================================================\n');

  const results = [];

  // Run all tests
  results.push(await testEnvironmentConfigLoading());
  results.push(await testSettingsIntegration());
  results.push(await testConfigurationValidation());

  // Final summary
  const passedTests = results.filter(result => result).length;
  const totalTests = results.length;

  console.log('\n🏁 FINAL FUNCTIONAL TEST SUMMARY');
  console.log('================================================================');
  console.log(`📊 Tests Passed: ${passedTests}/${totalTests} (${(passedTests / totalTests * 100).toFixed(1)}%)`);

  if (passedTests === totalTests) {
    console.log('🎉 ALL FUNCTIONAL TESTS PASSED! Environment configuration is working correctly.');
    console.log('✅ Environment configuration loading working');
    console.log('✅ Settings integration working');
    console.log('✅ Configuration validation working');
    console.log('✅ Ready for subscription tier implementation');
  } else {
    console.log('❌ Some functional tests failed. Please review the implementation.');
  }

  console.log('================================================================');

  return passedTests === totalTests;
}

// Run tests if executed directly
if (typeof process !== 'undefined' && process.argv && process.argv[1] && process.argv[1].includes('environment-config-test.js')) {
  runEnvironmentConfigTests()
    .then(success => {
      if (success) {
        console.log('\n✅ All environment configuration functional tests passed!');
        console.log('🎯 ASSIGNMENT-039: Environment Configuration Setup - FUNCTIONAL TESTS COMPLETE');
        process.exit(0);
      } else {
        console.log('\n❌ Environment configuration functional tests failed!');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('\n❌ Test execution failed:', error);
      process.exit(1);
    });
}

// Export for module usage
export { testEnvironmentConfigLoading, testSettingsIntegration, testConfigurationValidation, runEnvironmentConfigTests };
export default runEnvironmentConfigTests;
