/**
 * Functional tests for multi-step pipeline with real PDF files
 * Tests the complete pipeline functionality using sample invoices
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { documentProcessingPipeline } from '../../../src/services/DocumentProcessingPipeline.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Test configuration
const TEST_CONFIG = {
  sampleDataPath: path.join(__dirname, '../../../data/samples/invoices/input'),
  outputPath: path.join(__dirname, '../../../data/samples/invoices/output'),
  testFiles: [
    '2738_WE_05_25.pdf',
    '3590_RU_09_24.pdf',
    '10026_M_11_24.pdf'
  ],
  timeout: 30000 // 30 seconds per test
};

/**
 * Functional test suite for multi-step pipeline
 */
class MultiStepPipelineFunctionalTest {
  constructor() {
    this.testResults = [];
    this.totalTests = 0;
    this.passedTests = 0;
    this.failedTests = 0;
  }

  /**
   * Run all functional tests
   */
  async runAllTests() {
    console.log('🧪 Starting Multi-Step Pipeline Functional Tests');
    console.log('=' .repeat(60));

    try {
      await this.testPipelineInitialization();
      await this.testIndividualSteps();
      await this.testCompleteWorkflow();
      await this.testErrorHandling();
      await this.testDataStorage();

      this.printSummary();
      return this.passedTests === this.totalTests;
    } catch (error) {
      console.error('❌ Test suite failed:', error);
      return false;
    }
  }

  /**
   * Test pipeline initialization
   */
  async testPipelineInitialization() {
    console.log('\n🔧 Testing Pipeline Initialization...');
    
    try {
      await documentProcessingPipeline.initialize();
      this.recordTest('Pipeline Initialization', true, 'Pipeline initialized successfully');
    } catch (error) {
      this.recordTest('Pipeline Initialization', false, `Initialization failed: ${error.message}`);
    }
  }

  /**
   * Test individual step execution
   */
  async testIndividualSteps() {
    console.log('\n🔍 Testing Individual Step Execution...');

    const testFile = await this.loadTestFile(TEST_CONFIG.testFiles[0]);
    if (!testFile) {
      this.recordTest('Individual Steps', false, 'Could not load test file');
      return;
    }

    try {
      // Test PDF extraction
      const pdfResult = await documentProcessingPipeline.runPdfExtraction(testFile);
      this.recordTest('PDF Extraction Step', pdfResult.success, 
        pdfResult.success ? `Extracted ${pdfResult.data.text.length} characters` : pdfResult.error);

      if (pdfResult.success) {
        // Test DeepSeek analysis (skip if no API key)
        try {
          const deepSeekResult = await documentProcessingPipeline.runDeepSeekAnalysis(
            pdfResult.data.text, 
            { apiKey: process.env.DEEPSEEK_API_KEY || 'test-key' }
          );
          this.recordTest('DeepSeek Analysis Step', deepSeekResult.success, 
            deepSeekResult.success ? 'Analysis completed' : deepSeekResult.error);
        } catch (error) {
          this.recordTest('DeepSeek Analysis Step', false, `API error: ${error.message}`);
        }

        // Test Tesseract OCR
        const ocrResult = await documentProcessingPipeline.runTesseractReference(testFile);
        this.recordTest('Tesseract OCR Step', ocrResult.success, 
          ocrResult.success ? 'OCR processing completed' : ocrResult.error);

        // Test field mapping
        const mappingResult = await documentProcessingPipeline.runFieldMapping(
          pdfResult.data, 
          { success: true, extractedFields: {} }, 
          ocrResult.data
        );
        this.recordTest('Field Mapping Step', mappingResult.success, 
          mappingResult.success ? 'Field mapping completed' : mappingResult.error);

        // Test data validation
        if (mappingResult.success) {
          const validationResult = await documentProcessingPipeline.runDataValidation(mappingResult.data);
          this.recordTest('Data Validation Step', validationResult.success, 
            validationResult.success ? `Accuracy: ${validationResult.data.accuracyScore}%` : validationResult.error);
        }
      }
    } catch (error) {
      this.recordTest('Individual Steps', false, `Step execution failed: ${error.message}`);
    }
  }

  /**
   * Test complete workflow
   */
  async testCompleteWorkflow() {
    console.log('\n🔄 Testing Complete Workflow...');

    for (const fileName of TEST_CONFIG.testFiles.slice(0, 2)) { // Test first 2 files
      const testFile = await this.loadTestFile(fileName);
      if (!testFile) continue;

      try {
        console.log(`  📄 Processing: ${fileName}`);
        const startTime = Date.now();

        const result = await documentProcessingPipeline.processDocument(testFile, {
          apiKey: process.env.DEEPSEEK_API_KEY || null,
          language: 'pol',
          progressCallback: (progress) => {
            console.log(`    📊 ${progress.stage}: ${progress.progress}%`);
          }
        });

        const processingTime = Date.now() - startTime;
        
        this.recordTest(`Complete Workflow - ${fileName}`, result.success, 
          result.success ? 
            `Processed in ${processingTime}ms, Accuracy: ${result.finalResult?.accuracyScore || 0}%` : 
            result.error);

        // Save results for analysis
        if (result.success) {
          await this.saveTestResults(fileName, result);
        }

      } catch (error) {
        this.recordTest(`Complete Workflow - ${fileName}`, false, `Workflow failed: ${error.message}`);
      }
    }
  }

  /**
   * Test error handling
   */
  async testErrorHandling() {
    console.log('\n⚠️ Testing Error Handling...');

    try {
      // Test with invalid file
      const invalidFile = new File(['invalid content'], 'invalid.txt', { type: 'text/plain' });
      const result = await documentProcessingPipeline.runPdfExtraction(invalidFile);
      
      this.recordTest('Invalid File Handling', !result.success, 
        !result.success ? 'Correctly rejected invalid file' : 'Should have failed with invalid file');

      // Test with missing API key
      const deepSeekResult = await documentProcessingPipeline.runDeepSeekAnalysis('test text', {});
      this.recordTest('Missing API Key Handling', !deepSeekResult.success, 
        !deepSeekResult.success ? 'Correctly handled missing API key' : 'Should have failed without API key');

    } catch (error) {
      this.recordTest('Error Handling', false, `Error handling test failed: ${error.message}`);
    }
  }

  /**
   * Test data storage functionality
   */
  async testDataStorage() {
    console.log('\n💾 Testing Data Storage...');

    try {
      const { pipelineDataStorage } = await import('../../../src/services/PipelineDataStorageService.js');
      
      // Test storage stats
      const stats = await pipelineDataStorage.getStorageStats();
      this.recordTest('Storage Stats', true, `Found ${stats.totalDocuments} stored documents`);

      // Test document retrieval
      const documents = await pipelineDataStorage.getAllDocuments();
      this.recordTest('Document Retrieval', Array.isArray(documents), 
        `Retrieved ${documents.length} documents`);

    } catch (error) {
      this.recordTest('Data Storage', false, `Storage test failed: ${error.message}`);
    }
  }

  /**
   * Load test file from samples directory
   */
  async loadTestFile(fileName) {
    try {
      const filePath = path.join(TEST_CONFIG.sampleDataPath, fileName);
      
      if (!fs.existsSync(filePath)) {
        console.warn(`⚠️ Test file not found: ${fileName}`);
        return null;
      }

      const buffer = fs.readFileSync(filePath);
      return new File([buffer], fileName, { type: 'application/pdf' });
    } catch (error) {
      console.error(`❌ Failed to load test file ${fileName}:`, error);
      return null;
    }
  }

  /**
   * Save test results to output directory
   */
  async saveTestResults(fileName, result) {
    try {
      if (!fs.existsSync(TEST_CONFIG.outputPath)) {
        fs.mkdirSync(TEST_CONFIG.outputPath, { recursive: true });
      }

      const outputFileName = fileName.replace('.pdf', '_functional_test_result.json');
      const outputPath = path.join(TEST_CONFIG.outputPath, outputFileName);

      const testResult = {
        fileName,
        timestamp: new Date().toISOString(),
        processingTime: result.processingTimeMs,
        success: result.success,
        accuracy: result.finalResult?.accuracyScore || 0,
        stepResults: result.stepResults,
        stepTimings: result.stepTimings,
        extractedFields: result.finalResult || {}
      };

      fs.writeFileSync(outputPath, JSON.stringify(testResult, null, 2));
      console.log(`  💾 Results saved: ${outputFileName}`);
    } catch (error) {
      console.warn(`⚠️ Failed to save test results: ${error.message}`);
    }
  }

  /**
   * Record test result
   */
  recordTest(testName, passed, details) {
    this.totalTests++;
    if (passed) {
      this.passedTests++;
      console.log(`  ✅ ${testName}: ${details}`);
    } else {
      this.failedTests++;
      console.log(`  ❌ ${testName}: ${details}`);
    }

    this.testResults.push({
      name: testName,
      passed,
      details,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Print test summary
   */
  printSummary() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 FUNCTIONAL TEST SUMMARY');
    console.log('='.repeat(60));
    console.log(`Total Tests: ${this.totalTests}`);
    console.log(`✅ Passed: ${this.passedTests}`);
    console.log(`❌ Failed: ${this.failedTests}`);
    console.log(`📈 Success Rate: ${((this.passedTests / this.totalTests) * 100).toFixed(1)}%`);
    
    if (this.failedTests > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults
        .filter(test => !test.passed)
        .forEach(test => console.log(`  - ${test.name}: ${test.details}`));
    }
    
    console.log('\n🎉 Functional testing completed!');
  }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const tester = new MultiStepPipelineFunctionalTest();
  tester.runAllTests().then(success => {
    process.exit(success ? 0 : 1);
  });
}

// Export for use in other test files
export { MultiStepPipelineFunctionalTest };

// 🧪 Self-test execution
console.log('🧪 Multi-Step Pipeline Functional Tests loaded');
console.log('📁 Sample data path:', TEST_CONFIG.sampleDataPath);
console.log('📄 Test files:', TEST_CONFIG.testFiles.join(', '));
